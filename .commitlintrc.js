// Commit message 规范配置
// 使用 Conventional Commits 标准
// https://conventionalcommits.org/

module.exports = {
  extends: ['@commitlint/config-conventional'],
  
  // 自定义规则
  rules: {
    // type 类型定义，表示 git 提交的 type 必须在以下类型范围内
    'type-enum': [
      2,
      'always',
      [
        'feat',     // 新功能 feature
        'fix',      // 修复 bug
        'docs',     // 文档注释
        'style',    // 代码格式(不影响代码运行的变动)
        'refactor', // 重构(既不增加新功能，也不是修复bug)
        'perf',     // 性能优化
        'test',     // 增加测试
        'chore',    // 构建过程或辅助工具的变动
        'revert',   // 回退
        'build',    // 打包
        'ci'        // CI/CD相关
      ]
    ],
    // subject 大小写不做校验
    'subject-case': [0],
    
    // subject 不允许为空
    'subject-empty': [2, 'never'],
    
    // subject 长度限制
    'subject-max-length': [2, 'always', 50],
    
    // body 换行长度限制
    'body-max-line-length': [2, 'always', 100],
    
    // footer 换行长度限制  
    'footer-max-line-length': [2, 'always', 100]
  },
  
  // 忽略规则
  ignores: [
    (commit) => commit.includes('init'),
    (commit) => commit.includes('WIP'),
    (commit) => commit.includes('wip')
  ],
  
  // 自定义提示信息
  prompt: {
    messages: {
      type: '选择你要提交的类型 :',
      scope: '选择一个提交范围（可选）:',
      customScope: '请输入自定义的提交范围 :',
      subject: '填写简短精炼的变更描述 :',
      body: '填写更加详细的变更描述（可选）。使用 "|" 换行 :',
      breaking: '列举非兼容性重大的变更（可选）。使用 "|" 换行 :',
      footerPrefixSelect: '选择关联issue前缀（可选）:',
      customFooterPrefix: '输入自定义issue前缀 :',
      footer: '列举关联issue (可选) 例如: #31, #I3244 :',
      generatingByAI: '正在通过 AI 生成你的提交简短描述...',
      generatedSelectByAI: '选择一个 AI 生成的简短描述:',
      confirmCommit: '是否提交或修改commit ?'
    },
    
    // 类型定义
    types: [
      { value: 'feat', name: '✨ feat:     新增功能', emoji: ':sparkles:' },
      { value: 'fix', name: '🐛 fix:      修复缺陷', emoji: ':bug:' },
      { value: 'docs', name: '📝 docs:     文档变更', emoji: ':memo:' },
      { value: 'style', name: '💄 style:    代码格式（不影响功能，例如空格、分号等格式修正）', emoji: ':lipstick:' },
      { value: 'refactor', name: '♻️  refactor: 代码重构（不包括 bug 修复、功能新增）', emoji: ':recycle:' },
      { value: 'perf', name: '⚡️ perf:     性能优化', emoji: ':zap:' },
      { value: 'test', name: '✅ test:     添加疏漏测试或已有测试改动', emoji: ':white_check_mark:' },
      { value: 'build', name: '📦️ build:    构建流程、外部依赖变更（如升级 npm 包、修改 webpack 配置等）', emoji: ':package:' },
      { value: 'ci', name: '🎡 ci:       修改 CI 配置、脚本', emoji: ':ferris_wheel:' },
      { value: 'chore', name: '🔨 chore:    对构建过程或辅助工具和库的更改（不影响源文件、测试用例）', emoji: ':hammer:' },
      { value: 'revert', name: '⏪️ revert:   回滚 commit', emoji: ':rewind:' }
    ],
    
    // 范围定义
    scopes: [
      { name: 'auth', description: '用户认证模块' },
      { name: 'api', description: 'API接口相关' },
      { name: 'ui', description: '用户界面' },
      { name: 'config', description: '配置文件' },
      { name: 'deps', description: '依赖相关' },
      { name: 'workflow', description: '工作流程' },
      { name: 'types', description: '类型定义' }
    ]
  }
};