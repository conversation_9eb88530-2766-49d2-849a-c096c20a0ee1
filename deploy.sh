#!/bin/bash
set -e

# LookAh电商平台简化部署脚本
# 功能：编译前后端代码，复制编译产物到deploy目录，然后打包整个deploy目录上传到服务器解压

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 确保脚本在项目根目录下运行
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
if [[ "$(basename "$SCRIPT_DIR")" == "scripts" ]]; then
  PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
  cd "$PROJECT_ROOT" || { echo "无法切换到项目根目录"; exit 1; }
else
  PROJECT_ROOT="$SCRIPT_DIR"
fi

echo "当前工作目录: $(pwd)"

# 基本配置
BUILD_DIR="./build"
DEPLOY_DIR="./deploy"

# 解析命令行参数
SKIP_BUILD=false
SKIP_SERVER_BUILD=false
SKIP_ADMIN_BUILD=false
SKIP_WHOLESALE_BUILD=false
SKIP_DEPLOY=false

for arg in "$@"; do
  case $arg in
    --prod)
      DEPLOY_TYPE_DEFAULT="server"
      ;;
    --dev)
      DEPLOY_TYPE_DEFAULT="local"
      ;;
    --skip-build)
      SKIP_BUILD=true
      SKIP_SERVER_BUILD=true
      SKIP_ADMIN_BUILD=true
      SKIP_WHOLESALE_BUILD=true
      ;;
    --skip-server-build)
      SKIP_SERVER_BUILD=true
      ;;
    --skip-admin-build)
      SKIP_ADMIN_BUILD=true
      ;;
    --skip-wholesale-build)
      SKIP_WHOLESALE_BUILD=true
      ;;
    --skip-deploy)
      SKIP_DEPLOY=true
      ;;
    *)
      # 忽略未知参数
      ;;
  esac
done

# 询问编译选项
if [ "$SKIP_BUILD" == false ]; then
  echo -e "\n${BLUE}请选择需要编译的模块:${NC}"
  
  # 询问服务端编译
  read -p "是否编译服务端? (y/n) [y]: " BUILD_SERVER_INPUT
  if [[ "$BUILD_SERVER_INPUT" == "n" || "$BUILD_SERVER_INPUT" == "N" ]]; then
    SKIP_SERVER_BUILD=true
  fi
  
  # 询问后台前端编译
  read -p "是否编译后台前端? (y/n) [y]: " BUILD_ADMIN_INPUT
  if [[ "$BUILD_ADMIN_INPUT" == "n" || "$BUILD_ADMIN_INPUT" == "N" ]]; then
    SKIP_ADMIN_BUILD=true
  else
    SKIP_ADMIN_BUILD=false
  fi
  
  # 询问批发前端编译
  read -p "是否编译批发前端? (y/n) [y]: " BUILD_WHOLESALE_INPUT
  if [[ "$BUILD_WHOLESALE_INPUT" == "n" || "$BUILD_WHOLESALE_INPUT" == "N" ]]; then
    SKIP_WHOLESALE_BUILD=true
  fi

  # 如果全部跳过，则设置SKIP_BUILD为true
  if [ "$SKIP_SERVER_BUILD" == true ] && [ "$SKIP_ADMIN_BUILD" == true ] && [ "$SKIP_WHOLESALE_BUILD" == true ]; then
    SKIP_BUILD=true
  fi
fi

# 创建必要的目录
mkdir -p "$BUILD_DIR"
mkdir -p "$DEPLOY_DIR"

# 如果不跳过构建，进行编译
if [ "$SKIP_BUILD" == false ]; then
  echo -e "\n${GREEN}开始编译项目...${NC}"
  
  # 1. 编译后端
  if [ "$SKIP_SERVER_BUILD" == false ]; then
    echo -e "\n${BLUE}开始编译后端服务...${NC}"
    if [ -d "server" ]; then
      cd server
      
      if command -v mvn &> /dev/null; then
        echo "使用Maven构建后端项目..."
        mvn clean package -DskipTests
        
        if [ $? -ne 0 ]; then
          echo -e "${RED}❌ 后端编译失败，请检查错误${NC}"
          cd ..
          exit 1
        fi
        
        # 查找服务器JAR包
        SERVER_JAR=""
        if [ -f "lookah-app/target/lookah-app.jar" ]; then
          SERVER_JAR="lookah-app/target/lookah-app.jar"
        elif [ -f "lookah-app/target/lookah-server.jar" ]; then
          SERVER_JAR="lookah-app/target/lookah-server.jar"
        elif [ -f "target/lookah-app.jar" ]; then
          SERVER_JAR="target/lookah-app.jar"
        elif [ -f "target/lookah-server.jar" ]; then
          SERVER_JAR="target/lookah-server.jar"
        else
          SERVER_JAR=$(find . -name "lookah-*.jar" | grep -v "snailjob" | head -n 1)
        fi
        
        if [ -n "$SERVER_JAR" ]; then
          mkdir -p "../$BUILD_DIR"
          cp "$SERVER_JAR" "../$BUILD_DIR/lookah-server.jar"
          echo -e "${GREEN}✅ 已编译并复制主服务JAR: $SERVER_JAR${NC}"
        else
          echo -e "${YELLOW}⚠️ 未找到主服务JAR包${NC}"
        fi
        
        # 查找定时任务JAR包
        SNAILJOB_JAR=""
        if [ -f "lookah-extend/lookah-snailjob-server/target/lookah-snailjob-server.jar" ]; then
          SNAILJOB_JAR="lookah-extend/lookah-snailjob-server/target/lookah-snailjob-server.jar"
        elif [ -f "lookah-extend/lookah-snailjob/target/lookah-snailjob.jar" ]; then
          SNAILJOB_JAR="lookah-extend/lookah-snailjob/target/lookah-snailjob.jar"
        elif [ -f "lookah-snailjob-server/target/lookah-snailjob-server.jar" ]; then
          SNAILJOB_JAR="lookah-snailjob-server/target/lookah-snailjob-server.jar"
        elif [ -f "lookah-snailjob/target/lookah-snailjob.jar" ]; then
          SNAILJOB_JAR="lookah-snailjob/target/lookah-snailjob.jar"
        else
          SNAILJOB_JAR=$(find . -name "*snailjob*.jar" | head -n 1)
        fi
        
        if [ -n "$SNAILJOB_JAR" ]; then
          mkdir -p "../$BUILD_DIR"
          cp "$SNAILJOB_JAR" "../$BUILD_DIR/lookah-snailjob.jar"
          echo -e "${GREEN}✅ 已编译并复制定时任务JAR: $SNAILJOB_JAR${NC}"
        else
          echo -e "${YELLOW}⚠️ 未找到定时任务JAR包${NC}"
        fi
      else
        echo -e "${YELLOW}⚠️ 未找到Maven命令，跳过后端编译${NC}"
      fi
      
      cd ..
    else
      echo -e "${YELLOW}⚠️ 未找到server目录，跳过后端编译${NC}"
    fi
  else
    echo -e "${YELLOW}跳过服务端编译${NC}"
  fi
  
  # 2. 编译前端应用
  if [ "$SKIP_ADMIN_BUILD" == false ] || [ "$SKIP_WHOLESALE_BUILD" == false ]; then
    echo -e "\n${BLUE}开始编译前端应用...${NC}"
    if [ -d "web" ]; then
      cd web
      
      if command -v pnpm &> /dev/null; then
        echo "使用PNPM安装依赖..."
        pnpm install
        
        if [ $? -ne 0 ]; then
          echo -e "${YELLOW}⚠️ 前端依赖安装失败，尝试使用--force参数${NC}"
          pnpm install --force
        fi
        
        echo "构建内部依赖包..."
        pnpm -r run stub --if-present
        
        # 使用 turbo 统一编译前端应用
        echo "正在使用 turbo 编译前端应用..."

        # 构建需要编译的应用列表
        BUILD_FILTER=""
        if [ "$SKIP_ADMIN_BUILD" == false ] && [ "$SKIP_WHOLESALE_BUILD" == false ]; then
          BUILD_FILTER="--filter=@lookah/admin --filter=@lookah/wholesale"
        elif [ "$SKIP_ADMIN_BUILD" == false ]; then
          BUILD_FILTER="--filter=@lookah/admin"
        elif [ "$SKIP_WHOLESALE_BUILD" == false ]; then
          BUILD_FILTER="--filter=@lookah/wholesale"
        fi

        if [ -n "$BUILD_FILTER" ]; then
          NODE_OPTIONS=--max-old-space-size=8192 npx turbo build $BUILD_FILTER

          if [ $? -ne 0 ]; then
            echo -e "${RED}❌ 前端编译失败，请检查错误${NC}"
            cd ..
            exit 1
          fi

          # 复制管理后台构建产物
          if [ "$SKIP_ADMIN_BUILD" == false ]; then
            ADMIN_DIST="apps/lookah-admin/dist"
            if [ -d "$ADMIN_DIST" ]; then
              # 清理旧的构建产物
              if [ -d "../$BUILD_DIR/admin" ]; then
                echo "清理旧的Admin构建产物..."
                rm -rf "../$BUILD_DIR/admin"
              fi
              mkdir -p "../$BUILD_DIR/admin/dist"
              cp -r "$ADMIN_DIST"/* "../$BUILD_DIR/admin/dist/"
              echo -e "${GREEN}✅ 已编译并复制管理后台前端: $ADMIN_DIST${NC}"
            else
              echo -e "${RED}❌ 未找到管理后台构建产物: $ADMIN_DIST${NC}"
              cd ..
              exit 1
            fi
          fi

          # 复制批发前端构建产物
          if [ "$SKIP_WHOLESALE_BUILD" == false ]; then
            WHOLESALE_DIST="apps/lookah-wholesale/dist"
            if [ -d "$WHOLESALE_DIST" ]; then
              # 清理旧的构建产物
              if [ -d "../$BUILD_DIR/wholesale" ]; then
                echo "清理旧的Wholesale构建产物..."
                rm -rf "../$BUILD_DIR/wholesale"
              fi
              mkdir -p "../$BUILD_DIR/wholesale/dist"
              cp -r "$WHOLESALE_DIST"/* "../$BUILD_DIR/wholesale/dist/"
              echo -e "${GREEN}✅ 已编译并复制批发前端: $WHOLESALE_DIST${NC}"
            else
              echo -e "${RED}❌ 未找到批发前端构建产物: $WHOLESALE_DIST${NC}"
              cd ..
              exit 1
            fi
          fi
        fi

      else
        echo -e "${YELLOW}⚠️ 未找到pnpm命令，跳过前端编译${NC}"
      fi
      
      cd ..
    else
      echo -e "${YELLOW}⚠️ 未找到web目录，跳过前端编译${NC}"
    fi
  else
    echo -e "${YELLOW}跳过前端编译${NC}"
  fi
  
  
  echo -e "\n${GREEN}所有选定项目编译完成✅${NC}"
fi

# 如果指定跳过部署，则直接退出
if [ "$SKIP_DEPLOY" == true ]; then
  echo -e "\n${YELLOW}跳过部署操作${NC}"
  echo "编译完成! 如需部署，请运行 ./deploy.sh 脚本"
  exit 0
fi

# 部署类型交互式询问
echo "========== LookAh 电商平台部署 =========="
echo -e "\n请选择部署目标:"
echo "1) 本地开发环境"
echo "2) 生产环境服务器"
echo "q) 退出，不部署"

# 设置默认选项
DEFAULT_CHOICE="1"
if [ "$DEPLOY_TYPE_DEFAULT" == "server" ]; then
  DEFAULT_CHOICE="2"
fi

read -p "请输入选项 [1-2/q]，默认 $DEFAULT_CHOICE: " DEPLOY_CHOICE

# 如果用户未输入，使用默认选项
DEPLOY_CHOICE=${DEPLOY_CHOICE:-$DEFAULT_CHOICE}

case $DEPLOY_CHOICE in
  1)
    DEPLOY_TYPE="local"
    echo "已选择部署到本地开发环境"
    ;;
  2)
    DEPLOY_TYPE="server"
    echo "已选择部署到生产环境服务器"
    
    # 默认服务器信息
    DEFAULT_SERVER_IP="************"
    DEFAULT_SERVER_USER="root"
    DEFAULT_SERVER_PORT="520"
    DEFAULT_REMOTE_DEPLOY_DIR="/www/server/lookah-wholesale"
    
    # 询问是否使用默认配置
    read -p "是否使用默认服务器配置? (y/n) [y]: " USE_DEFAULT_SERVER
    USE_DEFAULT_SERVER=${USE_DEFAULT_SERVER:-y}
    
    if [[ "$USE_DEFAULT_SERVER" == "n" || "$USE_DEFAULT_SERVER" == "N" ]]; then
      # 询问自定义服务器信息
      read -p "请输入服务器IP地址: " SERVER_IP
      read -p "请输入服务器用户名 [root]: " SERVER_USER
      SERVER_USER=${SERVER_USER:-root}
      read -p "请输入服务器端口 [22]: " SERVER_PORT
      SERVER_PORT=${SERVER_PORT:-22}
      read -p "请输入远程部署目录 [/www/server/lookah-wholesale]: " REMOTE_DEPLOY_DIR
      REMOTE_DEPLOY_DIR=${REMOTE_DEPLOY_DIR:-/www/server/lookah-wholesale}
    else
      # 使用默认值
      SERVER_IP="$DEFAULT_SERVER_IP"
      SERVER_USER="$DEFAULT_SERVER_USER"
      SERVER_PORT="$DEFAULT_SERVER_PORT"
      REMOTE_DEPLOY_DIR="$DEFAULT_REMOTE_DEPLOY_DIR"
    fi
    
    echo -e "\n您的服务器配置如下:"
    echo "- 服务器IP: $SERVER_IP"
    echo "- 服务器用户名: $SERVER_USER"
    echo "- 服务器端口: $SERVER_PORT"
    echo "- 部署目录: $REMOTE_DEPLOY_DIR"
    ;;
  q|Q)
    echo "已取消部署"
    exit 0
    ;;
  *)
    echo "无效的选项，已取消部署"
    exit 1
    ;;
esac

# 检查构建目录和部署目录是否存在
# 只有在需要编译时才要求构建目录存在
if [ "$SKIP_BUILD" == false ] && [ ! -d "$BUILD_DIR" ]; then
  echo -e "${RED}错误: 构建目录 ${BUILD_DIR} 不存在${NC}"
  echo "请先编译前端和后端项目，确保构建目录中包含所有编译文件"
  exit 1
fi

if [ ! -d "$DEPLOY_DIR" ]; then
  echo -e "${RED}错误: 部署目录 ${DEPLOY_DIR} 不存在${NC}"
  echo "请确保部署目录存在"
  exit 1
fi

# 第1步: 复制编译产物到deploy目录
echo -e "\n${GREEN}正在复制编译产物到deploy目录...${NC}"

# 复制前端文件
# 只有在编译了对应模块时才要求构建产物存在
if [ "$SKIP_ADMIN_BUILD" == false ]; then
  if [ -d "$BUILD_DIR/admin/dist" ]; then
    # 清理旧的前端文件
    if [ -d "$DEPLOY_DIR/admin/dist" ]; then
      echo "正在清理旧的Admin前端文件..."
      rm -rf "$DEPLOY_DIR/admin/dist"
    fi
    mkdir -p "$DEPLOY_DIR/admin/dist"
    echo "正在复制Admin前端文件..."
    cp -r "$BUILD_DIR/admin/dist"/* "$DEPLOY_DIR/admin/dist/"
    echo "✅ 已复制Admin前端文件到deploy目录"
  else
    echo "${RED}❌ 错误: 选择编译Admin前端但未找到构建产物${NC}"
    exit 1
  fi
else
  echo "${YELLOW}⚠️ 跳过Admin前端文件复制（未编译）${NC}"
fi

if [ "$SKIP_WHOLESALE_BUILD" == false ]; then
  if [ -d "$BUILD_DIR/wholesale/dist" ]; then
    # 清理旧的前端文件
    if [ -d "$DEPLOY_DIR/wholesale/dist" ]; then
      echo "正在清理旧的Wholesale前端文件..."
      rm -rf "$DEPLOY_DIR/wholesale/dist"
    fi
    mkdir -p "$DEPLOY_DIR/wholesale/dist"
    echo "正在复制Wholesale前端文件..."
    cp -r "$BUILD_DIR/wholesale/dist"/* "$DEPLOY_DIR/wholesale/dist/"
    echo "✅ 已复制Wholesale前端文件到deploy目录"
  else
    echo "${RED}❌ 错误: 选择编译Wholesale前端但未找到构建产物${NC}"
    exit 1
  fi
else
  echo "${YELLOW}⚠️ 跳过Wholesale前端文件复制（未编译）${NC}"
fi


# 复制后端文件
# 检查是否有已编译的JAR包，无论是否跳过编译都尝试复制
if [ -f "$BUILD_DIR/lookah-server.jar" ]; then
  # 清理旧的JAR文件
  if [ -f "$DEPLOY_DIR/server/lookah-server.jar" ]; then
    echo "正在清理旧的服务器JAR文件..."
    rm -f "$DEPLOY_DIR/server/lookah-server.jar"
  fi
  mkdir -p "$DEPLOY_DIR/server"
  echo "正在复制服务器JAR文件..."
  cp "$BUILD_DIR/lookah-server.jar" "$DEPLOY_DIR/server/"
  echo "✅ 已复制服务器JAR包到deploy目录"
elif [ "$SKIP_SERVER_BUILD" == false ]; then
  echo "${RED}❌ 错误: 选择编译服务端但未找到JAR包${NC}"
  exit 1
else
  echo "${YELLOW}⚠️ 未找到服务器JAR包，跳过复制${NC}"
fi

if [ -f "$BUILD_DIR/lookah-snailjob.jar" ]; then
  # 清理旧的JAR文件
  if [ -f "$DEPLOY_DIR/snailjob/lookah-snailjob.jar" ]; then
    echo "正在清理旧的定时任务JAR文件..."
    rm -f "$DEPLOY_DIR/snailjob/lookah-snailjob.jar"
  fi
  mkdir -p "$DEPLOY_DIR/snailjob"
  echo "正在复制定时任务JAR文件..."
  cp "$BUILD_DIR/lookah-snailjob.jar" "$DEPLOY_DIR/snailjob/"
  echo "✅ 已复制定时任务JAR包到deploy目录"
else
  echo "${YELLOW}⚠️ 未找到定时任务JAR包，跳过复制${NC}"
fi

# 如果选择本地部署
if [ "$DEPLOY_TYPE" == "local" ]; then
  echo -e "\n${GREEN}开始本地部署...${NC}"
  
  # 启动本地服务
  (cd $DEPLOY_DIR && docker-compose down || true && docker-compose up -d)
  
  if [ $? -eq 0 ]; then
    echo -e "\n${GREEN}✅ 本地部署成功！${NC}"
    echo "服务可通过以下地址访问:"
    echo "管理后台: https://distro-admin.lookah.com"
    echo "批发前端: https://distro.lookah.com"
    echo "API服务: https://distro-api.lookah.com"

    echo -e "\n注意: 请确保已在hosts文件中添加以下映射:"
    echo "127.0.0.1 distro-api.lookah.com"
    echo "127.0.0.1 distro-admin.lookah.com"
    echo "127.0.0.1 distro.lookah.com"
    
    # 显示运行状态
    echo -e "\n容器运行状态:"
    (cd $DEPLOY_DIR && docker-compose ps)
  else
    echo -e "\n${RED}❌ 本地部署失败，请检查错误信息${NC}"
    exit 1
  fi
  
  exit 0
fi

# 如果选择服务器部署
if [ "$DEPLOY_TYPE" == "server" ]; then
  # 第2步: 打包整个deploy目录
  echo -e "\n${GREEN}正在打包deploy目录...${NC}"
  
  # 获取版本号
  VERSION=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0-dev")
  DEPLOY_PACKAGE="lookah-$VERSION.tar.gz"
  
  # 创建版本标记文件
  echo "$VERSION" > "$DEPLOY_DIR/version.txt"
  
  # 打包deploy目录，排除不必要文件
  tar -czf "$DEPLOY_PACKAGE" \
    --exclude="init-scripts" \
    --exclude="*.map" \
    --exclude="*.log" \
    --exclude=".DS_Store" \
    --exclude="node_modules" \
    --exclude=".git" \
    -C "$(dirname "$DEPLOY_DIR")" "$(basename "$DEPLOY_DIR")"
  
  echo -e "${GREEN}✅ 已创建部署包: $DEPLOY_PACKAGE ${NC}"
  
  # 第3步: 上传部署包到服务器
  echo -e "\n${GREEN}正在将部署包上传到服务器...${NC}"
  scp -P "$SERVER_PORT" "$DEPLOY_PACKAGE" "$SERVER_USER@$SERVER_IP:/tmp/"
  
  if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 上传部署包失败，请检查服务器连接${NC}"
    rm -f "$DEPLOY_PACKAGE"
    exit 1
  fi
  
  # 第4步: 在服务器上解压并部署
  echo -e "\n${GREEN}正在解压部署包到目标目录...${NC}"
  
  ssh -p "$SERVER_PORT" "$SERVER_USER@$SERVER_IP" "
    # 检查远程目录是否存在，不存在则创建
    mkdir -p \"$REMOTE_DEPLOY_DIR\"
    
    # 备份配置文件和SSL证书
    if [ -f \"$REMOTE_DEPLOY_DIR/.env\" ]; then
      cp \"$REMOTE_DEPLOY_DIR/.env\" \"/tmp/.env.backup\"
      echo '已备份.env配置文件'
    fi
    
    if [ -d \"$REMOTE_DEPLOY_DIR/nginx/ssl\" ]; then
      mkdir -p /tmp/ssl.backup
      cp -r \"$REMOTE_DEPLOY_DIR/nginx/ssl\"/* \"/tmp/ssl.backup/\" 2>/dev/null || true
      echo '已备份SSL证书'
    fi
    
    # 创建临时目录用于解压新文件
    TMP_EXTRACT_DIR=\$(mktemp -d)
    echo '正在解压新文件到临时目录...'
    tar -xzf /tmp/$DEPLOY_PACKAGE -C \$TMP_EXTRACT_DIR
    
    # 使用rsync更新文件，排除.data目录
    echo '正在更新文件(保留数据目录)...'
    rsync -av --exclude='.data' \$TMP_EXTRACT_DIR/deploy/ \"$REMOTE_DEPLOY_DIR/\"
    
    # 恢复配置文件
    if [ -f \"/tmp/.env.backup\" ]; then
      mv \"/tmp/.env.backup\" \"$REMOTE_DEPLOY_DIR/.env\"
      echo '✅ 已恢复.env配置文件'
    elif [ -f \"$REMOTE_DEPLOY_DIR/.env.example\" ]; then
      cp \"$REMOTE_DEPLOY_DIR/.env.example\" \"$REMOTE_DEPLOY_DIR/.env\"
      echo '✅ 已从示例创建.env配置文件'
    fi
    
    # 恢复SSL证书
    if [ -d \"/tmp/ssl.backup\" ]; then
      mkdir -p \"$REMOTE_DEPLOY_DIR/nginx/ssl\"
      cp -r /tmp/ssl.backup/* \"$REMOTE_DEPLOY_DIR/nginx/ssl/\" 2>/dev/null || true
      rm -rf \"/tmp/ssl.backup\"
      echo '✅ 已恢复SSL证书'
    fi
    
    # 确保数据目录结构完整
    mkdir -p \"$REMOTE_DEPLOY_DIR/.data/mysql\"
    mkdir -p \"$REMOTE_DEPLOY_DIR/.data/redis\"
    mkdir -p \"$REMOTE_DEPLOY_DIR/.data/minio\"
    mkdir -p \"$REMOTE_DEPLOY_DIR/.data/nginx-logs\"
    mkdir -p \"$REMOTE_DEPLOY_DIR/.data/server-logs\"
    mkdir -p \"$REMOTE_DEPLOY_DIR/.data/snailjob-logs\"
    
    # 在服务器上编译容器
    cd \"$REMOTE_DEPLOY_DIR\"
    echo '正在重新构建容器...'
    
    # 询问是否使用缓存构建
    read -p '是否使用缓存构建容器? (y/n) [y]: ' USE_CACHE
    USE_CACHE=\${USE_CACHE:-y}
    
    if [[ \"\$USE_CACHE\" == \"n\" || \"\$USE_CACHE\" == \"N\" ]]; then
      echo '不使用缓存构建容器...'
      docker-compose build --no-cache
    else
      echo '使用缓存构建容器...'
      docker-compose build
    fi
    
    # 检查构建是否成功
    if [ \$? -ne 0 ]; then
      echo '❌ 容器构建失败，请检查错误'
      exit 1
    fi
    
    # 清理临时文件
    rm -rf \$TMP_EXTRACT_DIR
    rm -f /tmp/$DEPLOY_PACKAGE
    
    # 自动执行服务
    echo '停止现有服务...'
    docker-compose down || true
    echo '启动服务...'
    docker-compose up -d
    echo '服务已启动'
    
    # 显示运行状态
    echo '容器运行状态:'
    docker-compose ps
  "
  
  # 清理本地临时文件
  rm -f "$DEPLOY_PACKAGE"
  
  if [ $? -eq 0 ]; then
    echo -e "\n${GREEN}✅ 部署成功! 版本 $VERSION 已成功部署到服务器${NC}"
    echo "部署目录: $REMOTE_DEPLOY_DIR"
    echo "服务已自动启动并运行"
  else
    echo -e "\n${RED}❌ 部署失败! 请检查服务器连接或部署脚本${NC}"
    exit 1
  fi
  
  echo -e "\n${GREEN}部署完成!${NC}"
fi 
