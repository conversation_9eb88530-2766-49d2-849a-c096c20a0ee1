/**
 * 表单错误提示动画组合式函数
 * 提供统一的错误提示显示动画和样式
 */

export interface ErrorTransitionConfig {
  enterDuration?: string
  leaveDuration?: string
  maxHeight?: string
}

/**
 * 获取错误提示的Transition配置
 */
export function useErrorTransition(config: ErrorTransitionConfig = {}) {
  const {
    enterDuration = 'duration-200',
    leaveDuration = 'duration-150',
    maxHeight = 'max-h-6',
  } = config

  return {
    enterActiveClass: `transition-all ${enterDuration} ease-out`,
    enterFromClass: `opacity-0 max-h-0`,
    enterToClass: `opacity-100 ${maxHeight}`,
    leaveActiveClass: `transition-all ${leaveDuration} ease-in`,
    leaveFromClass: `opacity-100 ${maxHeight}`,
    leaveToClass: `opacity-0 max-h-0`,
  }
}

/**
 * 获取错误提示容器的样式类
 */
export function useErrorContainerClasses() {
  return {
    container: 'overflow-hidden',
    content: 'flex items-center gap-1 text-red-600 text-xs mt-1 py-0.5',
    icon: 'pi pi-exclamation-circle text-[10px]',
    text: 'font-medium leading-tight',
  }
}
