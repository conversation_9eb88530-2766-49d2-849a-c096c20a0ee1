<script setup lang="ts">
/**
 * 通用表单字段错误提示组件
 * 提供统一的错误显示动画和样式
 */
interface Props {
  /** 是否显示错误 */
  show?: boolean
  /** 错误消息 */
  error?: string | { message?: string }
  /** 自定义动画配置 */
  animationConfig?: {
    enterDuration?: string
    leaveDuration?: string
    maxHeight?: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  error: undefined,
  animationConfig: () => ({}),
})

// 使用组合式函数获取动画配置和样式
const transitionConfig = useErrorTransition(props.animationConfig)
const classes = useErrorContainerClasses()

// 计算错误消息文本
const errorMessage = computed(() => {
  if (!props.error)
    return ''
  return typeof props.error === 'string' ? props.error : props.error.message || ''
})
</script>

<template>
  <Transition v-bind="transitionConfig">
    <div v-if="show && errorMessage" :class="classes.container" role="alert" aria-live="assertive">
      <div :class="classes.content">
        <i :class="classes.icon" />
        <span :class="classes.text">{{ errorMessage }}</span>
      </div>
    </div>
  </Transition>
</template>
