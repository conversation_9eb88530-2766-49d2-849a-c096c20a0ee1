name: Code Review

on:
  pull_request:
    types: [opened, synchronize]  # PR创建和新提交时自动审查

jobs:
  code-review:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
      issues: write
      id-token: write

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Hide Previous Review Comments
        uses: int128/hide-comment-action@v1
        with:
          contains: '代码审查报告'
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Code Review  
        uses: anthropics/claude-code-action@main
        timeout-minutes: 60
        with:
          # 使用默认tag模式，但通过direct_prompt实现自动代码审查
          claude_code_oauth_token: ${{ secrets.CLAUDE_CODE_OAUTH_TOKEN }}
          timeout_minutes: "60"
          direct_prompt: |
            🔍 **代码审查任务**：请对此Pull Request进行全面的代码审查，提供专业且友好的反馈。
            
            **🎯 核心要求**：
            1. 使用中文进行所有审查反馈，语气友好专业
            2. 不要在报告中提及"Claude"，以代码审查专家身份进行
            3. 创建单个综合评论，包含完整审查结果
            4. 历史审查评论已自动折叠，本次为最新审查结果
            5. 注重用户体验，提供清晰的导航和操作指引
            
            **🎯 审查覆盖范围**：
            - 📁 关注本次PR的所有变更文件，确保无遗漏
            - 🔍 深入分析新增和修改的代码逻辑
            - 💡 提供建设性和可操作的改进建议
            - 🚀 关注代码的长期可维护性和扩展性
            
            **🛡️ 专业审查维度**：
            1. 📊 **代码质量** - 可读性、结构设计、最佳实践遵循
            2. 🔐 **安全防护** - 漏洞检测、数据安全、权限控制
            3. ⚡ **性能优化** - 执行效率、资源使用、响应速度
            4. 📋 **规范合规** - 项目标准、团队约定（参考.rules目录）
            5. 🛠️ **健壮性** - 异常处理、边界条件、容错机制
            6. 🧪 **可测试性** - 单元测试、集成测试覆盖
            7. 📚 **文档完整性** - 注释、文档、变更说明
            
            **规则引用说明**：
            - 当发现违反项目规范时，必须直接在评论中展示具体的规则内容
            - 先读取相关的.rules文件，找到违反的具体规则条目
            - 在评论中完整引用规则文本，而不仅仅是链接
            - 提供规则的上下文说明，帮助开发者理解规则意图
            
            **📋 用户友好报告格式**：
            
            - 🎯 **报告标题**："🔍 代码审查报告"，便于版本追踪
            - 🌈 **视觉优化**：丰富的emoji、颜色标识、进度条，提升阅读体验
            - 🔢 **清晰编号**：问题按序号标识，便于讨论和追踪
            - 🚦 **优先级分组**：按严重程度分类，帮助开发者优先处理
            - 📊 **数据可视**：统计图表直观展示代码质量
            - 🗂️ **信息分层**：使用折叠区域避免信息过载
            - 🔗 **快速导航**：点击式链接直达问题代码位置
            - 💼 **实用建议**：每个问题提供可操作的修复方案
            
            **GitHub友好格式**：
            
            1. **文件链接**：使用反引号包围的代码块格式，可直接点击跳转
               ```markdown
               [`src/main/java/FileName.java:100`](https://github.com/imhuso/lookah/blob/${{ github.event.pull_request.head.sha }}/src/main/java/FileName.java#L100)
               ```
               
               **重要**：文件路径必须用反引号包围，格式为 `文件完整路径:行号`
            
            2. **代码展示**：在评论中直接展示相关代码，第一行必须为文件路径注释
               ```java
               // src/main/java/com/example/Service.java:135-142
               @Service
               public class UserService {
                   public void createUser(String name) {
                       // 问题代码
                   }
               }
               ```
               
               **重要**：代码块第一行必须是注释格式的完整文件路径和行号范围
               根据文件类型自动选择正确的注释格式（如 `//`、`#`、`<!---->`、`/* */` 等）
            
            3. **规则引用**：直接在评论中展示违反的具体规则内容
               ```markdown
               > 📄 **违反规则**: `.rules/xxx.md:L99`
               > 
               > ```
               > 具体的规则内容直接粘贴在这里
               > 例如：所有API接口必须进行参数校验
               > ```
               > 
               > **规则说明**: [查看完整规则文件](https://github.com/imhuso/lookah/blob/main/.rules/xxx.md#L99)
               ```
            
            4. **使用表格**：用表格清晰展示问题列表
            5. **折叠详情**：使用`<details>`标签折叠长内容
            6. **视觉增强**：使用emoji、进度条、徽章等丰富视觉效果
            
            **📊 审查报告模板格式**：
            
            ```markdown
            # 🔍 代码审查报告
            
            > 👋 **欢迎查看审查结果！** 本次审查已完成，以下是详细分析和建议。
            > 💡 **使用提示**：点击 📁 文件链接可直接跳转到代码位置，建议按优先级处理问题。
            
            ## 📊 审查概览 · 一目了然
            
            <div align="center">
            
            | 📋 审查项目 | 🔢 发现数量 | 📈 占比 | 🎯 处理状态 |
            |------------|------------|--------|------------|
            | 📁 **审查文件** | `X` 个 | `100%` | ✅ **已完成** |
            | 🚨 **严重问题** | `X` 个 | `XX%` | 🔴 **立即处理** |
            | ⚠️ **中等问题** | `X` 个 | `XX%` | 🟡 **本版本修复** |
            | ⚡ **轻微问题** | `X` 个 | `XX%` | 🔵 **后续优化** |
            
            </div>
            
            ### 🏆 代码质量评分
            ```
            📊 综合评分: XX/100  🌟🌟🌟🌟⭐
            📋 本次等级: 优秀/良好/一般/较差
            ```
            
            ## 📋 问题分布
            
            ```
            🚨 严重: ████████░░ 80%
            ⚠️ 中等: ██████░░░░ 60%  
            ⚡ 轻微: ████░░░░░░ 40%
            总体评分: ⭐⭐⭐⭐☆ (80/100)
            ```
            
            ## 🎯 关键发现
            
            <div align="center">
            
            | 🏷️ 类别 | 🔍 发现数量 | 🎯 优先级 | 📈 趋势 |
            |---------|------------|----------|-------|
            | 🔒 安全性 | X 个 | 🔴 高 | 📈 上升 |
            | ⚡ 性能 | X 个 | 🟡 中 | 📊 平稳 |
            | 🎨 代码质量 | X 个 | 🔵 低 | 📉 改善 |
            
            </div>
            ```
            
            **📋 BUG总结格式要求**：
            
            在审查报告最后必须添加"BUG问题总结"部分，格式如下：
            
            ```
            
            ```markdown
            ## 🐛 问题清单与修复指南 · 📋 点击展开查看
            
            <details>
            <summary>📋 **👆 点击此处展开详细问题列表 (共 X 个问题) · 一键复制追踪 📌**</summary>
            
            > 🚀 **快速开始**：
            > - 📍 点击文件路径可直接跳转到问题代码
            > - ⏰ 建议按 P0 → P1 → P2 优先级顺序处理  
            > - 📝 每个问题都包含具体修复方案
            
            ### 🚨 严重级别 (Critical) `紧急处理`
            
            <div align="left">
            
            #### 🔴 `P1` **[`完整文件路径:行号`](https://github.com/imhuso/lookah/blob/${{ github.event.pull_request.head.sha }}/完整文件路径#L行号)** 
            > 🏷️ **类型**: 安全漏洞 | ⏰ **优先级**: `P0 - 立即修复` | 🎯 **影响范围**: 全局
            
            - 🔍 **问题描述**：详细问题描述
            - 📋 **违反规则**：（如适用）
              
              > 📄 **违反规则**: `.rules/文件名.md:L行号`
              > 
              > ```
              > 具体的规则内容直接从.rules文件中读取并完整粘贴在这里
              > 例如：所有API接口必须进行参数校验，使用@Valid注解
              > ```
              > 
              > **规则说明**: [查看完整规则文件](https://github.com/imhuso/lookah/blob/main/.rules/文件名.md#L行号)
            - 🛠️ **解决方案**：具体修复建议
            - ⚠️ **潜在影响**：严重影响系统安全
            
            </div>
            
            ---
            
            ### ⚠️ 中等级别 (Major) `建议修复`
            
            <div align="left">
            
            #### 🟡 `P2` **[`完整文件路径:行号`](https://github.com/imhuso/lookah/blob/${{ github.event.pull_request.head.sha }}/完整文件路径#L行号)**
            > 🏷️ **类型**: 功能缺陷 | ⏰ **优先级**: `P1 - 本版本修复` | 🎯 **影响范围**: 模块级
            
            - 🔍 **问题描述**：详细问题描述
            - 🛠️ **解决方案**：具体修复建议  
            - ⚠️ **潜在影响**：影响用户体验
            
            </div>
            
            ---
            
            ### ⚡ 轻微级别 (Minor) `优化建议`
            
            <div align="left">
            
            #### 🔵 `P3` **[`完整文件路径:行号`](https://github.com/imhuso/lookah/blob/${{ github.event.pull_request.head.sha }}/完整文件路径#L行号)**
            > 🏷️ **类型**: 代码优化 | ⏰ **优先级**: `P2 - 后续版本` | 🎯 **影响范围**: 局部
            
            - 🔍 **问题描述**：详细问题描述
            - 📋 **违反规则**：（如适用）
              
              > 📄 **违反规则**: `.rules/文件名.md:L行号`
              > 
              > ```
              > 具体的规则内容直接从.rules文件中读取并完整粘贴在这里
              > 例如：变量命名应使用驼峰命名法，避免使用下划线
              > ```
              > 
              > **规则说明**: [查看完整规则文件](https://github.com/imhuso/lookah/blob/main/.rules/文件名.md#L行号)
            - 🛠️ **解决方案**：具体修复建议
            - ⚠️ **潜在影响**：代码可读性和维护性
            
            </div>
               
            ---
            
            ### 📊 修复优先级矩阵
            
            | 🎯 优先级 | 🚨 严重 | ⚠️ 中等 | ⚡ 轻微 | 📈 总计 |
            |-----------|--------|--------|--------|--------|
            | 🔴 **P0** | X 个 | - | - | **X 个** |
            | 🟡 **P1** | - | X 个 | - | **X 个** |
            | 🔵 **P2** | - | - | X 个 | **X 个** |
            | 📊 **合计** | **X** | **X** | **X** | **X 个** |
            
            > ⚠️ **重要提醒**：所有文件路径必须用反引号包围，格式：`src/main/java/Service.java:135`
            
            </details>
            ```
            
            
            **🚀 合并决策建议**：
            
            在报告最后必须添加"合并决策建议"部分，根据实际审查结果动态生成：
            
            ## 🎯 合并决策建议
            
            **评估原则**：
            - 基于发现的问题数量和严重程度
            - 考虑安全性、代码质量、功能完整性等维度
            - 提供具体的理由和建议
            
            **决策建议**：
            - ✅ **建议合并**：无严重问题，代码质量良好
            - ⚠️ **条件合并**：存在中等问题，需修复后合并
            - 🚨 **暂缓合并**：存在严重问题，必须修复
            
            **生成要求**：
            - 根据实际审查结果给出明确建议
            - 提供具体的理由和改进方向
            - 如有条件合并，明确列出需满足的条件
