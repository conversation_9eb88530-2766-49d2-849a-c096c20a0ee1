#!/usr/bin/env node

/**
 * AI 代码审查脚本 - Node.js 版本
 * 作者: imhuso
 * 用途: 使用 Claude Code CLI 进行智能代码审查
 * 兼容: Windows/macOS/Linux
 * 调用方式: node scripts/ai-code-review.js
 */

// =============================================================================
// AI 审查提示词配置  
// =============================================================================
const AI_REVIEW_PROMPT = `请立即使用 code-reviewer-with-standards agent 来审查当前分支的未提交代码变更。

你需要调用 Task 工具，使用以下参数：
- subagent_type: "code-reviewer-with-standards"
- description: "审查未提交代码变更"
- prompt: "当前分支包括未提交的代码是一个菜鸟写的代码，有可能存在 BUG 或代码不符合规范，规范在 .rules 下，请进行仔细的 Review。

审查要求：
1. 严格按照项目规范：
   - 仔细读取并遵守 .rules/ 目录下的所有规范文件
   - 包括代码规范、架构规范、安全规范、业务规则等
   - 如果是规范问题，则要表达关联的具体规范，方便定位

2. 全面审查范围：
   - 审查当前分支的所有未提交代码变更
   - 检查逻辑错误、潜在BUG、安全漏洞
   - 验证代码规范符合性
   - 检查性能问题和最佳实践

3. 菜鸟开发者视角：
   - 以菜鸟开发者角度审视代码，假设可能存在常见错误
   - 对规范问题零容忍，必须严格检查
   - 重点关注可能导致运行时错误的问题

4. BUG清单创建要求：
   - 如果发现了问题，请在 temp 中创建 BUG 清单
   - 清单文件命名最好和当前的功能相关，如果没有则使用时间格式
   - 都要以 bug_ 开头
   - 清单必须包含：问题类型、文件位置、问题描述、关联的具体规范文件和条款、修复建议、严重程度

请现在开始仔细审查，发现问题请创建详细的BUG清单文件。"

重要提醒：
- 请确保使用 .claude/agents/code-reviewer-with-standards.md 中定义的 agent
- 请严格按照 .rules 目录下的所有规范执行
- 禁止主动提交代码，只能审查和创建BUG清单`;

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 颜色定义 (支持 Windows)
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m', 
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// 日志函数
const log = {
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`)
};

// 创建 readline 接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 询问用户是否继续
function askContinue(message) {
  return new Promise((resolve) => {
    rl.question(`${colors.cyan}🤔 ${message} [y/N]: ${colors.reset}`, (answer) => {
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

// 检查命令是否存在
function commandExists(command) {
  return new Promise((resolve) => {
    exec(`${process.platform === 'win32' ? 'where' : 'which'} ${command}`, (error) => {
      resolve(!error);
    });
  });
}

// 检查 git diff --cached 是否有变更
function hasGitChanges() {
  return new Promise((resolve) => {
    exec('git diff --cached --quiet', (error) => {
      resolve(!!error); // 有变更时 git diff --cached --quiet 会返回非零状态
    });
  });
}

// 检查是否存在新生成的 BUG 清单文件（最近5分钟内创建的）
function hasNewBugList() {
  const tempDir = path.join(process.cwd(), 'temp');
  
  if (!fs.existsSync(tempDir)) {
    return false;
  }
  
  try {
    const files = fs.readdirSync(tempDir);
    const now = Date.now();
    const fiveMinutesAgo = now - (5 * 60 * 1000); // 5分钟前
    
    return files.some(file => {
      if (file.match(/^bug_.*_\d{14}\.md$/)) {
        const filePath = path.join(tempDir, file);
        const stats = fs.statSync(filePath);
        return stats.mtime.getTime() > fiveMinutesAgo;
      }
      return false;
    });
  } catch (error) {
    log.error(`检查BUG清单文件时出错: ${error.message}`);
    return false;
  }
}

// 获取最新的 BUG 清单文件名
function getLatestBugList() {
  const tempDir = path.join(process.cwd(), 'temp');
  
  if (!fs.existsSync(tempDir)) {
    return null;
  }
  
  try {
    const files = fs.readdirSync(tempDir)
      .filter(file => file.match(/^bug_.*_\d{14}\.md$/))
      .map(file => ({
        name: file,
        path: path.join(tempDir, file),
        mtime: fs.statSync(path.join(tempDir, file)).mtime.getTime()
      }))
      .sort((a, b) => b.mtime - a.mtime);
    
    return files.length > 0 ? files[0].name : null;
  } catch (error) {
    return null;
  }
}

// 执行 Claude Code CLI 审查
function runClaudeReview() {
  return new Promise((resolve) => {
    log.info('正在调用 code-reviewer-with-standards Agent...');
    log.info('严格按照 .rules 规范执行代码审查');
    console.log(); // 添加空行分隔
    
    // 使用 Claude CLI headless 模式进行代码审查
    const claudeArgs = [
      '-p', // print mode (headless)  
      '--output-format', 'stream-json', // 流式 JSON 输出，便于处理
      AI_REVIEW_PROMPT
    ];
    
    const claude = spawn('claude', claudeArgs, {
      stdio: ['pipe', 'pipe', 'pipe'], // 使用管道捕获所有输出
      shell: false // 不使用 shell，避免参数被错误解释
    });
    
    let hasOutput = false;
    
    // 处理标准输出（Claude 的响应）
    claude.stdout.on('data', (data) => {
      hasOutput = true;
      const lines = data.toString().split('\n');
      
      lines.forEach(line => {
        if (line.trim()) {
          try {
            // 尝试解析 JSON，如果成功就提取文本内容
            const jsonData = JSON.parse(line);
            if (jsonData.content) {
              process.stdout.write(jsonData.content);
            } else if (jsonData.text) {
              process.stdout.write(jsonData.text);
            }
          } catch {
            // 如果不是 JSON，直接输出原文本
            process.stdout.write(line + '\n');
          }
        }
      });
    });
    
    // 处理错误输出
    claude.stderr.on('data', (data) => {
      process.stderr.write(data);
    });
    
    claude.on('close', (code) => {
      if (!hasOutput) {
        log.warning('Claude CLI 没有产生输出，可能是配置或网络问题');
      }
      console.log(); // 添加空行分隔
      resolve(code === 0);
    });
    
    claude.on('error', (error) => {
      log.error(`执行 Claude CLI 时出错: ${error.message}`);
      log.info('请确认 Claude CLI 已正确安装并配置');
      resolve(false);
    });
  });
}

// 主函数
async function main() {
  try {
    log.info('启动AI代码审查... (Node.js 跨平台版本)');
    
    // 检查是否安装 Claude Code CLI
    if (!(await commandExists('claude'))) {
      log.warning('Claude CLI 未安装，跳过AI审查');
      log.info('安装方法: 请参考 Claude Code 文档');
      rl.close();
      return 0;
    }
    
    // 检查是否有未提交的变更
    if (await hasGitChanges()) {
      log.info('检测到代码变更，开始AI审查...');
      log.info('使用 code-reviewer-with-standards Agent 进行审查');
      log.info('审查范围: 所有暂存的代码变更');
      console.log();
      
      // 调用 Claude Code CLI 进行审查
      const reviewSuccess = await runClaudeReview();
      
      if (reviewSuccess) {
        log.success('AI代码审查完成');
        
        // 检查是否生成了新的BUG清单
        if (hasNewBugList()) {
          const latestBugFile = getLatestBugList();
          log.warning('发现代码问题，已生成BUG清单');
          log.info(`BUG清单文件: temp/${latestBugFile}`);
          log.info('请查看具体问题并修复后重新提交');
          console.log();
          
          const shouldContinue = await askContinue('是否继续提交？');
          if (!shouldContinue) {
            log.error('用户选择取消提交');
            rl.close();
            process.exit(1);
          }
          log.warning('用户选择跳过问题继续提交');
        } else {
          log.success('未发现明显问题，代码符合规范');
        }
      } else {
        log.warning('AI审查过程中出现问题');
        console.log();
        
        const shouldContinue = await askContinue('是否继续提交？');
        if (!shouldContinue) {
          log.error('用户选择取消提交');
          rl.close();
          process.exit(1);
        }
        log.warning('用户选择跳过AI审查继续提交');
      }
    } else {
      log.info('没有检测到代码变更，跳过AI审查');
    }
    
    rl.close();
    return 0;
    
  } catch (error) {
    log.error(`AI审查脚本执行出错: ${error.message}`);
    rl.close();
    process.exit(1);
  }
}

// 当文件直接运行时执行主函数
if (require.main === module) {
  main();
}

module.exports = { main };