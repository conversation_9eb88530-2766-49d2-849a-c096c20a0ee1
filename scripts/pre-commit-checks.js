#!/usr/bin/env node

/**
 * 预提交检查统一入口 - 跨平台兼容版本
 * 作者: imhuso
 * 兼容: Windows/macOS/Linux
 * 调用方式: node scripts/pre-commit-checks.js
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 颜色定义 (支持 Windows)
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m', 
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// 日志函数
const log = {
  info: (msg) => console.log(`${colors.blue}${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`)
};

// 询问用户是否继续
function askContinue(message) {
  return new Promise((resolve) => {
    // 严格的非交互式环境检测，只在确定的CI环境中跳过用户输入
    const isNonInteractive = 
      process.env.CI === 'true' || 
      process.env.CONTINUOUS_INTEGRATION === 'true' || 
      process.env.GITHUB_ACTIONS === 'true' ||
      process.env.TERM === 'dumb' ||
      process.env.NODE_ENV === 'test' ||
      process.argv.includes('--no-interactive');
    
    // 如果在非交互式环境，直接返回false
    if (isNonInteractive) {
      log.warning('检测到非交互式环境，默认选择N');
      resolve(false);
      return;
    }
    
    try {
      const fs = require('fs');
      let inputStream;
      
      // 简化输入流处理，统一使用process.stdin避免tty问题
      inputStream = process.stdin;
      
      const rl = readline.createInterface({
        input: inputStream,
        output: process.stdout
      });
      
      rl.question(`${colors.cyan}🤔 ${message} [y/N]: ${colors.reset}`, (answer) => {
        rl.close();
        // inputStream现在总是process.stdin，无需销毁
        const result = answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes';
        resolve(result);
      });
      
      // 处理 Ctrl+C
      rl.on('SIGINT', () => {
        rl.close();
        if (inputStream !== process.stdin && process.platform !== 'win32') {
          inputStream.destroy();
        }
        resolve(false);
      });
      
      // 设置超时（避免在某些环境中无限等待）
      const timeout = setTimeout(() => {
        rl.close();
        if (inputStream !== process.stdin && process.platform !== 'win32') {
          inputStream.destroy();
        }
        log.warning('输入超时，默认选择N');
        resolve(false);
      }, 30000); // 30秒超时
      
      // 当用户输入时清除超时
      rl.on('line', () => {
        clearTimeout(timeout);
      });
      
    } catch (error) {
      // 如果发生任何错误，默认返回false
      log.warning('获取用户输入时出错，默认选择N');
      log.info(`错误详情: ${error.message}`);
      resolve(false);
    }
  });
}

// 询问用户是否继续（支持默认值）
function askContinueWithDefault(message, defaultValue = false) {
  return new Promise((resolve) => {
    // 严格的非交互式环境检测，只在确定的CI环境中跳过用户输入
    const isNonInteractive = 
      process.env.CI === 'true' || 
      process.env.CONTINUOUS_INTEGRATION === 'true' || 
      process.env.GITHUB_ACTIONS === 'true' ||
      process.env.TERM === 'dumb' ||
      process.env.NODE_ENV === 'test' ||
      process.argv.includes('--no-interactive');
    
    // 如果在非交互式环境，直接使用默认值
    if (isNonInteractive) {
      log.warning(`检测到非交互式环境，使用默认值: ${defaultValue ? 'Y' : 'N'}`);
      resolve(defaultValue);
      return;
    }
    
    try {
      const fs = require('fs');
      let inputStream;
      
      // 简化输入流处理，统一使用process.stdin避免tty问题
      inputStream = process.stdin;
      
      const rl = readline.createInterface({
        input: inputStream,
        output: process.stdout
      });
      
      const prompt = defaultValue ? '[Y/n]' : '[y/N]';
      
      rl.question(`${colors.cyan}🤔 ${message} ${prompt}: ${colors.reset}`, (answer) => {
        rl.close();
        // inputStream现在总是process.stdin，无需销毁
        
        // 如果用户直接按回车，使用默认值
        if (answer.trim() === '') {
          resolve(defaultValue);
          return;
        }
        
        const result = answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes';
        resolve(result);
      });
      
      // 处理 Ctrl+C
      rl.on('SIGINT', () => {
        rl.close();
        if (inputStream !== process.stdin && process.platform !== 'win32') {
          inputStream.destroy();
        }
        resolve(false);
      });
      
      // 设置超时（避免在某些环境中无限等待）
      const timeout = setTimeout(() => {
        rl.close();
        if (inputStream !== process.stdin && process.platform !== 'win32') {
          inputStream.destroy();
        }
        log.warning(`输入超时，使用默认值: ${defaultValue ? 'Y' : 'N'}`);
        resolve(defaultValue);
      }, 30000); // 30秒超时
      
      // 当用户输入时清除超时
      rl.on('line', () => {
        clearTimeout(timeout);
      });
      
    } catch (error) {
      // 如果发生任何错误，使用默认值
      log.warning(`获取用户输入时出错，使用默认值: ${defaultValue ? 'Y' : 'N'}`);
      log.info(`错误详情: ${error.message}`);
      resolve(defaultValue);
    }
  });
}

// 检查目录是否存在
function directoryExists(dirPath) {
  try {
    return fs.statSync(dirPath).isDirectory();
  } catch (error) {
    return false;
  }
}

// 检查命令是否存在
function commandExists(command) {
  return new Promise((resolve) => {
    const checkCommand = process.platform === 'win32' ? 'where' : 'which';
    exec(`${checkCommand} ${command}`, (error) => {
      resolve(!error);
    });
  });
}

// 执行命令并返回结果
function runCommand(command, options = {}) {
  return new Promise((resolve) => {
    const child = spawn(command, options.args || [], {
      cwd: options.cwd || process.cwd(),
      stdio: options.silent ? 'pipe' : 'inherit',
      shell: true
    });
    
    child.on('close', (code) => {
      resolve(code === 0);
    });
    
    child.on('error', (error) => {
      if (!options.silent) {
        log.error(`执行命令失败: ${error.message}`);
      }
      resolve(false);
    });
  });
}

// 1. 前端ESLint检查 (强制)
async function checkFrontend() {
  log.info('🌐 前端ESLint检查 (强制)...');
  
  if (!directoryExists('web')) {
    log.info('⏭️ 前端项目目录不存在，跳过前端检查');
    return true;
  }
  
  const success = await runCommand('pnpm', {
    args: ['lint'],
    cwd: path.join(process.cwd(), 'web')
  });
  
  if (!success) {
    log.error('前端代码检查失败 - 此检查为强制性，必须修复');
    log.info('💡 问题已尝试自动修复，请检查并重新提交');
    return false;
  }
  
  log.success('前端代码检查通过');
  return true;
}

// 2. Java编译检查 (强制)
async function checkJava() {
  if (!directoryExists('server')) {
    return true;
  }
  
  log.info('☕ Java编译检查 (强制)...');
  
  const success = await runCommand('mvn', {
    args: ['compile', '-q'],
    cwd: path.join(process.cwd(), 'server')
  });
  
  if (!success) {
    log.error('Java编译失败 - 此检查为强制性，必须修复');
    log.info('💡 请修复编译错误后重新提交');
    return false;
  }
  
  log.success('Java编译检查通过');
  return true;
}

// 3. 安全检查 (可选)
async function checkSecurity() {
  log.info('🔐 安全检查 (可选)...');
  
  return new Promise((resolve) => {
    // 检查是否包含敏感信息
    exec('git diff --cached --name-only', async (error, stdout) => {
      if (error) {
        resolve(true);
        return;
      }
      
      const files = stdout.trim().split('\n').filter(f => f);
      if (files.length === 0) {
        resolve(true);
        return;
      }
      
      // 检查文件内容中的真正敏感词，避免误报
      exec(`git diff --cached | grep -i -E "(password|secret|token|api[_-]?key|private[_-]?key|access[_-]?key)\\s*[=:]\\s*['\"][^'\"]{8,}"`, async (grepError, grepOutput) => {
        if (!grepError && grepOutput.trim()) { // 找到了敏感信息
          log.warning('发现可能的敏感信息:');
          console.log(`${colors.yellow}${grepOutput.trim()}${colors.reset}`);
          const shouldContinue = await askContinue('是否继续提交？');
          if (!shouldContinue) {
            log.error('用户选择取消提交');
            resolve(false);
          } else {
            log.warning('用户选择继续提交，请注意安全风险');
            resolve(true);
          }
        } else {
          resolve(true);
        }
      });
    });
  });
}


// 检查是否是release提交
async function isReleaseCommit() {
  return new Promise((resolve) => {
    // 检查环境变量
    if (process.env.SKIP_HOOKS === 'true' || 
        process.env.SKIP_PRE_COMMIT === 'true' || 
        process.env.RELEASE_MODE === 'true') {
      resolve(true);
      return;
    }
    
    // 检查是否有release相关的staged文件或变更
    exec('git diff --cached --name-only', (error, stdout) => {
      if (error) {
        resolve(false);
        return;
      }
      
      const files = stdout.trim().split('\n').filter(f => f);
      // 如果有版本文件变更，可能是release提交
      const hasVersionChanges = files.some(file => 
        file.includes('pom.xml') || 
        file.includes('package.json') ||
        file.includes('version')
      );
      
      if (hasVersionChanges) {
        // 进一步检查提交内容是否包含版本号更新
        exec('git diff --cached | grep -E "(version|<revision>.*</revision>)"', (grepError, grepOutput) => {
          if (!grepError && grepOutput.trim()) {
            log.info('🎯 检测到版本号更新，可能是release提交');
            resolve(true);
          } else {
            resolve(false);
          }
        });
      } else {
        resolve(false);
      }
    });
  });
}

// 主函数
async function main() {
  try {
    log.info('🚀 开始预提交检查... (跨平台兼容版本)');
    
    // 检查是否是release提交
    if (await isReleaseCommit()) {
      log.info('🚀 检测到release提交，跳过代码检查');
      log.success('Release模式 - 跳过所有预提交检查');
      process.exit(0);
    }
    
    // 1. 前端检查 (强制)
    if (!(await checkFrontend())) {
      process.exit(1);
    }
    
    // 2. Java编译检查 (强制)
    if (!(await checkJava())) {
      process.exit(1);
    }
    
    // 3. 安全检查 (可选)
    if (!(await checkSecurity())) {
      process.exit(1);
    }
    
    
    log.success('所有检查通过');
    process.exit(0);
    
  } catch (error) {
    log.error(`预提交检查执行出错: ${error.message}`);
    process.exit(1);
  }
}

// 当文件直接运行时执行主函数
if (require.main === module) {
  main();
}

module.exports = { main };