#!/bin/bash
set -e

# 分支清理脚本
# 删除除 dev、main 和 jf 以外的所有本地和远程分支

# 基本配置
MAIN_BRANCH="main"
DEV_BRANCH="dev"
JF_BRANCH="jf"
REMOTE="origin"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧹 Git 分支清理工具${NC}"
echo "=================================================="

# 检查当前目录是否为Git仓库
if ! git rev-parse --is-inside-work-tree > /dev/null 2>&1; then
  echo -e "${RED}❌ 错误: 当前目录不是Git仓库${NC}"
  exit 1
fi

# 确保脚本在项目根目录下运行
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT" || { echo -e "${RED}❌ 无法切换到项目根目录${NC}"; exit 1; }

echo -e "${BLUE}📍 当前工作目录: $(pwd)${NC}"

# 检查当前分支
CURRENT_BRANCH=$(git branch --show-current)
echo -e "${BLUE}📍 当前分支: $CURRENT_BRANCH${NC}"

# 如果当前分支不是 dev、main 或 jf，切换到 dev 分支
if [ "$CURRENT_BRANCH" != "$DEV_BRANCH" ] && [ "$CURRENT_BRANCH" != "$MAIN_BRANCH" ] && [ "$CURRENT_BRANCH" != "$JF_BRANCH" ]; then
  echo -e "${YELLOW}⚠️  当前分支不是 $DEV_BRANCH、$MAIN_BRANCH 或 $JF_BRANCH，切换到 $DEV_BRANCH 分支${NC}"
  git checkout $DEV_BRANCH
  CURRENT_BRANCH=$DEV_BRANCH
fi

# 检查工作区是否干净
if [ -n "$(git status --porcelain)" ]; then
  echo -e "${RED}❌ 错误: 工作区有未提交的更改，请先提交或暂存更改${NC}"
  exit 1
fi

# 更新远程分支信息
echo -e "${BLUE}🔄 更新远程分支信息...${NC}"
git fetch $REMOTE --prune

# 获取所有本地分支（除了 dev、main 和 jf）
echo -e "${BLUE}📋 扫描本地分支...${NC}"
LOCAL_BRANCHES_TO_DELETE=$(git branch | grep -v -E "^\*|$DEV_BRANCH|$MAIN_BRANCH|$JF_BRANCH" | sed 's/^[ \t]*//')

# 获取所有远程分支（除了 dev、main 和 jf）
echo -e "${BLUE}📋 扫描远程分支...${NC}"
REMOTE_BRANCHES_TO_DELETE=$(git branch -r | grep -v -E "HEAD|$DEV_BRANCH|$MAIN_BRANCH|$JF_BRANCH" | sed 's/^[ \t]*origin\///' | sed 's/^[ \t]*//')

# 显示将要删除的分支
echo ""
echo -e "${YELLOW}📋 将要删除的本地分支:${NC}"
if [ -n "$LOCAL_BRANCHES_TO_DELETE" ]; then
  echo "$LOCAL_BRANCHES_TO_DELETE" | while read -r branch; do
    if [ -n "$branch" ]; then
      echo -e "  ${RED}🗑️  $branch${NC}"
    fi
  done
else
  echo -e "  ${GREEN}✅ 没有需要删除的本地分支${NC}"
fi

echo ""
echo -e "${YELLOW}📋 将要删除的远程分支:${NC}"
if [ -n "$REMOTE_BRANCHES_TO_DELETE" ]; then
  echo "$REMOTE_BRANCHES_TO_DELETE" | while read -r branch; do
    if [ -n "$branch" ]; then
      echo -e "  ${RED}🗑️  origin/$branch${NC}"
    fi
  done
else
  echo -e "  ${GREEN}✅ 没有需要删除的远程分支${NC}"
fi

# 如果没有分支需要删除，退出
if [ -z "$LOCAL_BRANCHES_TO_DELETE" ] && [ -z "$REMOTE_BRANCHES_TO_DELETE" ]; then
  echo ""
  echo -e "${GREEN}✅ 没有需要清理的分支，仓库已经很干净了！${NC}"
  exit 0
fi

echo ""
echo -e "${YELLOW}⚠️  警告: 此操作将永久删除上述分支！${NC}"
echo -e "${YELLOW}⚠️  请确保这些分支的代码已经合并或不再需要！${NC}"
echo ""

# 询问确认
read -p "确认删除这些分支? (y/n) [n]: " CONFIRM
CONFIRM=${CONFIRM:-n}

if [ "$CONFIRM" != "y" ]; then
  echo -e "${BLUE}ℹ️  已取消操作${NC}"
  exit 0
fi

# 删除本地分支
if [ -n "$LOCAL_BRANCHES_TO_DELETE" ]; then
  echo ""
  echo -e "${BLUE}🗑️  删除本地分支...${NC}"
  echo "$LOCAL_BRANCHES_TO_DELETE" | while read -r branch; do
    if [ -n "$branch" ]; then
      echo -e "  删除本地分支: ${RED}$branch${NC}"
      git branch -D "$branch" || echo -e "    ${YELLOW}⚠️  删除失败，可能分支不存在${NC}"
    fi
  done
fi

# 删除远程分支
if [ -n "$REMOTE_BRANCHES_TO_DELETE" ]; then
  echo ""
  echo -e "${BLUE}🗑️  删除远程分支...${NC}"
  echo "$REMOTE_BRANCHES_TO_DELETE" | while read -r branch; do
    if [ -n "$branch" ]; then
      echo -e "  删除远程分支: ${RED}origin/$branch${NC}"
      git push $REMOTE --delete "$branch" || echo -e "    ${YELLOW}⚠️  删除失败，可能分支不存在${NC}"
    fi
  done
fi

# 清理远程跟踪分支
echo ""
echo -e "${BLUE}🧹 清理远程跟踪分支...${NC}"
git remote prune $REMOTE

echo ""
echo -e "${GREEN}✅ 分支清理完成！${NC}"
echo ""
echo -e "${BLUE}📋 当前剩余分支:${NC}"
git branch -a

echo ""
echo -e "${GREEN}🎉 分支清理操作已完成！${NC}"
