{"enabled": true, "name": "智能代码质量检查", "description": "根据文件类型自动执行全面的代码质量检查，集成编码规范、最佳实践和提交准备评估", "version": "2", "when": {"type": "userTriggered", "patterns": ["**/*.java", "**/*.vue", "**/*.js", "**/*.ts", "**/*.jsx", "**/*.tsx", "**/*.md", "**/*.yml", "**/*.yaml", "**/*.json"]}, "then": {"type": "askAgent", "prompt": "请对最近修改的代码文件进行全面的质量检查，遵循项目的编码规范和最佳实践。\n\n**检查范围：**\n\n1. **代码规范检查**\n   - Java文件：需遵循 `.rules/java-backend-standards.md` 等规范\n   - Vue文件：需遵循 `.rules/vue-admin-standards.md` 和 `.rules/nuxt-mall-standards.md`  等规范\n   - JavaScript/TypeScript：检查ESLint规则和编码标准\n   - 配置文件：检查格式和结构正确性\n\n2. **代码质量评估**\n   - 命名规范（变量、函数、类、文件名）\n   - 代码结构和组织\n   - 注释和文档完整性\n   - 错误处理和边界情况\n   - 性能和安全考虑\n\n3. **项目规范符合性**\n   - 目录结构是否符合 `.rules/structure.md`\n   - API设计是否符合 `.rules/api-standards.md`\n   - 技术栈使用是否符合 `.rules/tech.md`\n\n4. **提交准备评估**\n   - 代码是否达到可提交状态\n   - 是否需要额外的测试或文档\n   - 是否符合 `.rules` 目录下相关规范的提交标准\n\n**输出格式：**\n- 🟢 通过的检查项\n- 🟡 需要注意的问题\n- 🔴 必须修复的问题\n- 💡 改进建议\n- 📋 提交准备状态评估\n\n请基于实际修改的文件内容进行具体分析，提供可操作的修复建议。"}}