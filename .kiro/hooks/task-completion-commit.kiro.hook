{"enabled": false, "name": "任务完成提交评估", "description": "当 Specs 任务完成后，自动评估是否需要提交代码，遵循小步向前的开发规范", "version": "1", "when": {"type": "userTriggered", "description": "手动触发，在完成 Specs 任务后使用"}, "then": {"type": "askAgent", "prompt": "我刚刚完成了一个 Specs 任务，请根据 `.rules` 目录下的规范帮我评估是否需要提交当前代码。\n\n请执行以下步骤：\n\n0. **分支检查**\n   - 检查当前所在的 Git 分支\n   - 如果不在 PR 分支（feature 分支），询问是否需要切换到 PR 分支\n   - 确保不在 main 或 dev 分支上直接提交\n\n1. **任务完成检查**\n   - 检查最近修改的文件是否符合编码规范\n   - 验证代码是否能正常编译（如果适用）\n   - 确认没有明显的代码质量问题\n\n2. **提交评估**\n   - 评估当前变更的完整性和独立性\n   - 检查是否满足\"必须提交\"的条件\n   - 确认提交不会破坏现有功能\n\n3. **提交建议**\n   - 如果建议提交，请提供规范的提交信息模板\n   - 如果建议延迟提交，请说明原因和建议\n   - 列出需要检查或完善的项目\n\n4. **后续行动**\n   - 如果确认提交，询问是否需要创建 PR\n   - 提供下一步的开发建议\n\n请基于项目的实际情况和最近的代码变更进行评估。"}}