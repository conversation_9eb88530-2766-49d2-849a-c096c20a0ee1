{"enabled": true, "name": "Hook 状态检查", "description": "检查所有 Agent Hooks 的状态和配置，提供使用建议", "version": "1", "when": {"type": "manual", "description": "手动触发，用于检查和管理所有 Hooks 的状态"}, "then": {"type": "askAgent", "prompt": "请检查 `.kiro/hooks/` 目录下所有 Agent Hooks 的状态和配置。\n\n**检查内容：**\n\n1. **Hook 清单**\n   - 列出所有可用的 .kiro.hook 文件\n   - 显示每个 Hook 的启用状态\n   - 检查 Hook 配置的完整性\n\n2. **功能概览**\n   - 总结每个 Hook 的主要功能\n   - 说明触发方式（自动/手动）\n   - 列出支持的文件类型或触发条件\n\n3. **使用建议**\n   - 根据当前项目状态推荐使用哪些 Hooks\n   - 提供最佳的使用顺序和组合\n   - 建议可能需要的配置调整\n\n4. **问题诊断**\n   - 检查是否有配置错误或冲突\n   - 识别可能的性能影响\n   - 提供故障排除建议\n\n5. **改进建议**\n   - 基于项目需求建议新的 Hook 功能\n   - 推荐配置优化\n   - 提供使用技巧和最佳实践\n\n请基于 `.kiro/hooks/README.md` 的内容进行全面分析，并提供实用的管理建议。"}}