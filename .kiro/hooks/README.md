# Agent Hooks 使用指南

## 📋 可用的 Hooks

### 1. 🔍 智能代码质量检查 (`code-quality-check.kiro.hook`)
**触发方式**: 手动触发
**功能**: 全面的代码质量检查和提交准备评估

**检查内容**:
- ✅ 代码规范符合性
- ✅ 命名和结构规范
- ✅ 项目规范符合性
- ✅ 提交准备状态评估

**支持文件类型**:
- Java (`.java`)
- Vue (`.vue`)
- JavaScript/TypeScript (`.js`, `.ts`, `.jsx`, `.tsx`)
- 配置文件 (`.json`, `.yml`, `.yaml`)
- 文档 (`.md`)

### 2. ⚠️ 任务完成提交评估 (`task-completion-commit.kiro.hook`) - 已禁用
**触发方式**: 手动触发（当前已禁用）
**功能**: 评估 Specs 任务完成后是否需要提交代码（为避免自动提交干扰已禁用）

**评估流程**:
1. 🌿 分支检查 - 确保在正确的 PR 分支
2. 🔍 任务完成检查 - 验证代码质量和功能完整性
3. 📊 提交评估 - 判断是否满足提交条件
4. 📝 提交建议 - 提供规范的提交信息模板
5. 🚀 后续行动 - 指导 PR 创建和下一步开发

### 3. 🌿 分支切换助手 (`branch-switcher.kiro.hook`)
**触发方式**: 手动触发
**功能**: 检查和管理 Git 分支状态

**主要功能**:
- 🔍 当前分支状态检查
- ⚠️ 分支安全评估
- 💡 分支命名建议
- 🔄 安全的分支切换指导

## 🚀 使用场景和工作流程

### 场景1: 开始新任务开发
```
1. 触发 branch-switcher.kiro.hook
   └── 检查当前分支，切换到合适的 feature 分支

2. 开始编码
   └── code-quality-check.kiro.hook 自动检查代码质量

3. 完成任务后
   └── 触发 task-completion-commit.kiro.hook 评估提交
```

### 场景2: 代码质量保障
```
1. 手动触发代码质量检查
   └── 运行 code-quality-check.kiro.hook 检查代码质量

2. 根据检查结果修复问题
   └── 🟢 通过 🟡 注意 🔴 必修

3. 开发者自主判断提交时机
   └── 基于实际需求决定何时提交
```

### 场景3: 任务完成流程
```
1. 完成功能开发
   └── 开发者自主判断是否完整

2. 可选的质量检查:
   ├── 手动触发代码质量检查
   ├── 功能完整性自我评估
   └── 兼容性验证

3. 开发者主导的提交决策:
   ├── 功能完整时手动提交
   ├── 需要继续开发时延迟提交
   └── 根据实际情况创建 PR
```

## 📊 Hook 状态和配置

### 当前启用状态
- ✅ `code-quality-check.kiro.hook` - 已启用（手动触发）
- ❌ `task-completion-commit.kiro.hook` - 已禁用（避免自动提交干扰）
- ✅ `branch-switcher.kiro.hook` - 已启用（手动触发）

### 配置说明
所有 Hooks 都已集成项目的 Steering 规范：
- 📋 `task-commit-policy.md` - 提交规范
- 🏗️ `structure.md` - 项目结构规范
- ⚙️ `tech.md` - 技术栈规范
- 🎯 `api-standards.md` - API 设计规范
- ☕ `java-backend-standards.md` - Java 后端规范
- 🖼️ `vue-admin-standards.md` - Vue 管理后台规范
- 🛍️ `nuxt-mall-standards.md` - Nuxt 商城规范

## 🔧 自定义和扩展

### 修改 Hook 配置
1. 编辑对应的 `.kiro.hook` 文件
2. 修改 `enabled` 字段启用/禁用
3. 调整 `patterns` 字段修改触发文件类型
4. 更新 `prompt` 字段自定义检查内容

### 添加新的 Hook
1. 在 `.kiro/hooks/` 目录创建新的 `.kiro.hook` 文件
2. 参考现有 Hook 的结构
3. 定义触发条件和执行逻辑
4. 更新本文档

## 💡 最佳实践

### 开发流程建议
1. **开始开发前**: 使用 `branch-switcher.kiro.hook` 确保分支正确
2. **开发过程中**: 专注功能实现，避免过度干扰
3. **质量检查时**: 手动触发 `code-quality-check.kiro.hook` 进行检查
4. **提交时机**: 开发者根据功能完整性自主决定

### 团队协作
- 所有团队成员都应启用这些 Hooks
- 定期更新 Steering 规范以保持一致性
- 分享 Hook 使用经验和改进建议
- 根据项目发展调整 Hook 配置

### 问题排查
- 如果 Hook 没有触发，检查 `enabled` 状态
- 如果检查结果不准确，更新相关的 Steering 规范
- 如果需要跳过某次检查，可以临时禁用 Hook
- 遇到问题时查看 Hook 的 `description` 了解预期行为

## 📈 效果监控

### 预期改进
- 🎯 **代码质量**: 自动化检查减少人工审查工作量
- 🚀 **开发效率**: 规范化流程减少返工和错误
- 🔄 **提交质量**: 小步向前确保每次提交都有价值
- 🌿 **分支管理**: 避免在错误分支开发的问题
- 📋 **规范遵循**: 自动化确保团队规范一致性

### 持续改进
- 根据使用反馈调整 Hook 逻辑
- 定期更新检查标准和规范
- 收集团队使用数据优化流程
- 扩展 Hook 功能覆盖更多场景
