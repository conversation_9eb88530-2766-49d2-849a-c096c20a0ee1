# Wholesale模块代码结构分析

## 📋 模块整体架构

### 1. 模块划分
```
lookah-wholesale/
├── lookah-wholesale-core/          # 核心业务模块
├── lookah-wholesale-api/           # API接口模块  
├── lookah-wholesale-starter/       # 启动器模块
└── pom.xml                        # 聚合POM
```

### 2. 核心模块包结构
```
com.imhuso.wholesale.core/
├── adapter/                       # 海外仓适配器
├── callback/                      # 回调处理
├── config/                        # 配置类
├── constant/                      # 常量定义
├── domain/                        # 实体类
│   ├── bo/                       # 业务对象
│   └── vo/                       # 视图对象
├── enums/                        # 枚举类
├── event/                        # 事件定义
├── exception/                    # 异常类
├── job/                          # 定时任务
├── listener/                     # 事件监听器
├── mapper/                       # 数据访问层
├── model/                        # 模型类
├── mybatis/                      # MyBatis扩展
├── satoken/                      # 认证相关
├── security/                     # 安全相关
├── service/                      # 服务接口
│   └── impl/                     # 服务实现
├── strategy/                     # 策略模式实现
├── translation/                  # 翻译相关
├── utils/                        # 工具类
└── validate/                     # 验证相关
```

### 3. API模块包结构
```
com.imhuso.wholesale.controller/
├── admin/                        # 后台管理接口
└── front/                        # 前台用户接口
```

## 🗄️ 核心数据表结构

### 1. 产品相关表
```sql
-- 产品主表 (SPU)
whs_product
├── id (产品ID)
├── category_id (分类ID)
├── series_id (系列ID)
├── item_name (产品名称)
├── default_variant_id (默认变体ID)
├── status (状态)
├── sort (排序)
└── del_flag (删除标志)

-- 产品变体表 (SKU)
whs_product_variant
├── id (变体ID)
├── product_id (产品ID)
├── sku_code (SKU编码)
├── upc (UPC编码)
├── wholesale_price (批发价)
├── msrp (建议零售价)
├── main_image (主图)
├── specs (规格属性JSON)
├── status (状态)
├── packaging_type (包装类型)
├── alert_stock (预警库存)
└── del_flag (删除标志)

-- 产品分类表
whs_product_category
-- 产品系列表
whs_product_series
-- 产品属性表
whs_product_attribute
-- 产品属性值表
whs_product_attribute_value
-- 产品属性关联表
whs_product_attribute_relation
-- 产品变体属性表
whs_product_variant_attribute
-- 包装项目表
whs_package_item
```

### 2. 库存相关表
```sql
-- 库存主表
whs_stock
├── id (库存ID)
├── warehouse_id (仓库ID)
├── variant_id (变体ID)
├── total_stock (总库存)
├── available_stock (可用库存)
├── locked_stock (锁定库存)
└── version (乐观锁版本)

-- 仓库表
whs_warehouse
├── id (仓库ID)
├── name (仓库名称)
├── code (仓库编码)
├── address (地址)
├── contact (联系人)
├── phone (电话)
├── status (状态)
├── is_default (是否默认)
├── provider_id (提供商ID)
├── priority (发货优先级)
├── is_overseas (是否海外仓)
├── shipping_rules (发货规则JSON)
├── warehouse_config (仓库配置JSON)
└── alert_stock (预警库存阈值)

-- 库存操作日志表
whs_stock_log
-- 库存分配表
whs_stock_allocation
-- 库存通知表
whs_stock_notify
-- 在途库存表
whs_in_transit_stock
```

### 3. 订单相关表
```sql
-- 订单主表
whs_order
├── id (订单ID)
├── invoice_id (发票编号)
├── internal_order_no (内部订单号)
├── customer_order_no (客户订单号)
├── member_id (会员ID)
├── is_manual_order (是否手动下单)
├── shipping_* (收货信息)
├── total_amount (总金额)
├── total_items (总项数)
├── total_pcs (总PCS数)
├── payment_status (支付状态)
├── order_status (订单状态)
├── shipment_status (发货状态)
├── invoice_status (发票状态)
└── remark (备注)

-- 订单项表
whs_order_item
├── id (订单项ID)
├── order_id (订单ID)
├── product_id (产品ID)
├── variant_id (变体ID)
├── product_name (产品名称)
├── quantity (数量)
├── price (单价)
├── amount (小计)
├── packaging_type (包装类型)
├── packaging_quantity (包装数量)
├── sku_code (SKU编码)
└── shipped_quantity (已发货数量)
```

### 4. 海外仓相关表
```sql
-- 入库单表
whs_inbound_order
├── id (入库单ID)
├── warehouse_id (仓库ID)
├── external_order_no (外部单号)
├── order_no (我方单号)
├── status (状态)
├── status_text (状态文本)
├── status_code (状态编码)
├── expected_arrival (预计到达)
├── actual_arrival (实际到达)
├── remark (备注)
├── external_data (外部数据JSON)
└── last_sync_time (最后同步时间)

-- 入库单明细表
whs_inbound_order_item
├── id (明细ID)
├── inbound_order_id (入库单ID)
├── variant_id (变体ID)
├── sku_code (SKU编码)
├── quantity (数量)
└── received_quantity (已接收数量)

-- 海外仓提供商表
whs_overseas_warehouse_provider
-- 海外仓账号表
whs_overseas_warehouse_account
```

## 🔧 核心服务架构

### 1. 产品管理服务
```java
// 产品核心服务
IWhsProductService                 // 产品管理
IWhsProductVariantService          // 产品变体管理
IWhsPackageVariantService          // 包装变体管理
IWhsProductCategoryService         // 产品分类管理
IWhsProductSeriesService           // 产品系列管理
IWhsProductAttributeService        // 产品属性管理
IWhsProductAttributeValueService   // 属性值管理
IWhsProductAttributeRelationService // 属性关联管理
IWhsProductFeatureService          // 产品特征服务
IProductHierarchyService           // 产品层级服务
```

### 2. 库存管理服务
```java
// 库存核心服务
IWhsStockService                   // 库存查询服务
IWhsStockOperationService          // 库存操作服务
IWhsStockSyncService               // 库存同步服务
IWhsStockAllocationService         // 库存分配服务
IWhsStockCacheService              // 库存缓存服务
IWhsStockLogService                // 库存日志服务
IWhsStockNotifyService             // 库存通知服务
IWhsReplenishmentService           // 补货管理服务
IWhsInTransitStockService          // 在途库存服务
```

### 3. 海外仓集成服务
```java
// 海外仓服务
IWhsOverseasWarehouseService       // 海外仓服务
IWhsInboundOrderService            // 入库单服务
IWhsInboundOrderSyncService        // 入库单同步服务
StockSyncProcessor                 // 库存同步处理器
IOverseasWarehouseProvider         // 海外仓提供商接口
OverseasWarehouseProviderFactory   // 提供商工厂
```

### 4. 通知服务
```java
// 通知相关服务
IReplenishmentNotificationService  // 补货通知服务
IStockNotificationService          // 库存通知服务
IStockNotificationSenderService    // 通知发送服务
IWecomService                      // 企业微信服务
IWhsMailService                    // 邮件服务
```

## 📊 关键设计模式

### 1. 策略模式
```java
// 产品映射策略
strategy/productMapping/
├── IProductMappingStrategy        // 映射策略接口
├── DefaultProductMappingStrategy  // 默认映射策略
└── CustomProductMappingStrategy   // 自定义映射策略

// 库存通知策略
strategy/stockNotification/
├── IStockNotificationStrategy     // 通知策略接口
├── EmailStockNotificationStrategy // 邮件通知策略
└── WecomStockNotificationStrategy // 企业微信通知策略
```

### 2. 适配器模式
```java
// 海外仓适配器
adapter/
├── IOverseasWarehouseProvider     // 海外仓提供商接口
├── AbstractOverseasWarehouseProvider // 抽象适配器
└── impl/                          // 具体适配器实现
```

### 3. 事件驱动模式
```java
// 事件定义
event/
├── StockChangeEvent               // 库存变更事件
├── WhsOrderEvent                  // 订单事件
└── WhsStockNotifyEvent            // 库存通知事件

// 事件监听器
listener/
├── StockChangeCacheListener       // 库存变更缓存监听器
├── StockRestoreNotifyListener     // 库存恢复通知监听器
└── WhsStockNotifyEventListener    // 库存通知事件监听器
```

## 🔄 数据流转架构

### 1. 库存同步流程
```
海外仓API → StockSyncProcessor → 库存操作服务 → 数据库更新 → 缓存刷新 → 事件发布
```

### 2. 补货通知流程
```
定时任务 → 库存检查 → 补货计算 → 通知生成 → 邮件/企微发送
```

### 3. 产品管理流程
```
产品CRUD → 属性关联 → 变体管理 → 缓存更新 → 事件通知
```

## 🎯 抽取重点分析

### 1. 高优先级抽取组件

#### 1.1 产品核心组件
```java
// 必须完整抽取的核心服务
- WhsProductServiceImpl           // 产品管理核心逻辑
- WhsProductVariantServiceImpl    // 变体管理核心逻辑
- WhsPackageVariantServiceImpl    // 包装变体管理
- ProductHierarchyServiceImpl     // 产品层级结构
- WhsProductFeatureServiceImpl    // 产品特征管理

// 必须完整抽取的数据访问层
- WhsProductMapper + XML          // 产品数据访问
- WhsProductVariantMapper + XML   // 变体数据访问
- WhsProductCategoryMapper + XML  // 分类数据访问
- WhsProductSeriesMapper + XML    // 系列数据访问
- WhsProductAttributeMapper + XML // 属性数据访问

// 必须完整抽取的实体类
- WhsProduct                      // 产品实体
- WhsProductVariant              // 变体实体
- WhsProductCategory             // 分类实体
- WhsProductSeries               // 系列实体
- WhsProductAttribute            // 属性实体
- WhsProductAttributeValue       // 属性值实体
- WhsPackageItem                 // 包装项实体
```

#### 1.2 库存管理组件
```java
// 必须完整抽取的库存服务
- WhsStockServiceImpl            // 库存查询服务
- WhsStockOperationServiceImpl   // 库存操作服务
- WhsStockSyncServiceImpl        // 库存同步服务
- WhsStockAllocationServiceImpl  // 库存分配服务
- WhsStockCacheServiceImpl       // 库存缓存服务
- WhsReplenishmentServiceImpl    // 补货管理服务

// 必须完整抽取的数据访问层
- WhsStockMapper + XML           // 库存数据访问
- WhsWarehouseMapper + XML       // 仓库数据访问
- WhsStockLogMapper + XML        // 库存日志访问
- WhsStockAllocationMapper + XML // 库存分配访问

// 必须完整抽取的实体类
- WhsStock                       // 库存实体
- WhsWarehouse                   // 仓库实体
- WhsStockLog                    // 库存日志实体
- WhsStockAllocation             // 库存分配实体
- WhsStockNotify                 // 库存通知实体
```

#### 1.3 海外仓集成组件
```java
// 必须完整抽取的海外仓服务
- WhsOverseasWarehouseServiceImpl    // 海外仓服务
- WhsInboundOrderServiceImpl         // 入库单服务
- WhsInboundOrderSyncServiceImpl     // 入库单同步服务
- StockSyncProcessor                 // 库存同步处理器

// 必须完整抽取的适配器组件
- IOverseasWarehouseProvider         // 海外仓提供商接口
- AbstractOverseasWarehouseProvider  // 抽象适配器
- OverseasWarehouseProviderFactory   // 提供商工厂
- 所有具体的海外仓适配器实现

// 必须完整抽取的策略组件
- IProductMappingStrategy            // 产品映射策略接口
- 所有产品映射策略实现
```

### 2. 中优先级抽取组件

#### 2.1 通知系统组件
```java
// 通知相关服务
- ReplenishmentNotificationServiceImpl // 补货通知服务
- StockNotificationServiceImpl         // 库存通知服务
- WecomServiceImpl                     // 企业微信服务
- WhsMailServiceImpl                   // 邮件服务

// 通知策略实现
- EmailStockNotificationStrategy       // 邮件通知策略
- WecomStockNotificationStrategy       // 企微通知策略
```

#### 2.2 定时任务组件
```java
// 定时任务
- ReplenishmentNotificationJob         // 补货通知任务
- StockSyncJob                         // 库存同步任务
- InboundOrderSyncJob                  // 入库单同步任务
- StockCacheRepairJob                  // 库存缓存修复任务
```

#### 2.3 事件监听组件
```java
// 事件监听器
- StockChangeCacheListener             // 库存变更缓存监听
- StockRestoreNotifyListener           // 库存恢复通知监听
- WhsStockNotifyEventListener          // 库存通知事件监听
```

### 3. 低优先级抽取组件

#### 3.1 工具类和配置
```java
// 工具类
- WhsEnumTranslationUtils              // 枚举翻译工具
- WarehouseNameUtils                   // 仓库名称工具
- NotificationConfigUtils              // 通知配置工具

// 配置类
- WholesaleBusinessConfig              // 业务配置
- WhsCacheConstants                    // 缓存常量
```

#### 3.2 验证和安全
```java
// 验证相关
- PostalCodeValidator                  // 邮编验证器
- ValidPostalCode                      // 邮编验证注解

// 安全相关
- WholesaleClientValidator             // 客户端验证器
```

### 4. 依赖关系分析

#### 4.1 核心依赖链
```
产品服务 → 变体服务 → 属性服务 → 分类服务
库存服务 → 仓库服务 → 海外仓服务 → 同步处理器
通知服务 → 邮件服务 → 企微服务 → 策略实现
```

#### 4.2 数据依赖关系
```
产品 → 变体 → 库存 → 仓库
产品 → 属性关联 → 属性 → 属性值
变体 → 包装项 → 包装关系
库存 → 库存日志 → 库存分配
```

#### 4.3 外部依赖
```java
// 平台依赖
- lookah-common-*                      // 通用组件
- lookah-system                        // 系统服务

// 第三方依赖
- MyBatis Plus                         // ORM框架
- Redis                                // 缓存
- Lock4j                               // 分布式锁
- EasyExcel                            // Excel处理
- RestTemplate                         // HTTP客户端
```

## 📋 抽取实施建议

### 1. 抽取顺序建议
1. **第一阶段**: 抽取产品核心实体和基础服务
2. **第二阶段**: 抽取库存管理完整体系
3. **第三阶段**: 抽取海外仓集成和同步组件
4. **第四阶段**: 抽取通知系统和定时任务
5. **第五阶段**: 抽取工具类和配置组件

### 2. 抽取注意事项
- **保持完整性**: 相关组件必须整体抽取，避免功能缺失
- **依赖处理**: 仔细处理组件间的依赖关系
- **配置迁移**: 相关配置文件和常量定义需要一并迁移
- **测试覆盖**: 抽取后需要完整的测试覆盖
- **文档同步**: 相关文档和注释需要同步更新

### 3. 风险控制
- **数据一致性**: 确保抽取过程中数据的一致性
- **功能完整性**: 确保抽取后功能的完整性
- **性能影响**: 评估抽取对系统性能的影响
- **回滚机制**: 建立完善的回滚机制

---

**文档版本**: v1.0
**分析日期**: 2025年1月17日
**分析范围**: Wholesale模块完整代码结构
**用途**: 产品目录统一项目实施参考
