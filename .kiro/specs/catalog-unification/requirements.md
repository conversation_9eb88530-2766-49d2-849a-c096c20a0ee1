# 产品目录统一 - 需求规格说明书

## 1. 项目目标

将Wholesale模块的产品功能抽取为统一的产品目录模块。

**核心要求**：
- 创建 `lookah-business-common-catalog` 统一产品目录模块
- Wholesale功能完全不变
- 采用catalog模块统一表名（cat_前缀）
- 为未来的业务扩展预留接口

## 2. 核心功能需求

### 2.1 产品管理
- 产品CRUD、状态管理、分类管理
- 产品变体管理（SKU、包装类型、价格）
- 产品属性管理
- 产品搜索和过滤

### 2.2 库存管理
- 库存操作（增减、锁定、释放、调整）
- 海外仓库存同步
- 库存预警和通知
- 库存统计和报表

### 2.3 价格管理
- 多价格类型支持（批发价、零售价、建议零售价、成本价）
- 价格计算和策略

## 3. 技术要求

### 3.1 架构要求
- 采用catalog模块统一表名（cat_前缀）
- 两层架构：catalog-core + catalog-business
- 保持现有事件驱动架构
- 为未来业务扩展预留适配器接口

### 3.2 性能要求
- 产品查询响应时间 < 200ms
- 支持1000+并发用户
- 缓存命中率 > 90%

### 3.3 数据迁移要求
- 一次性重构到catalog模块统一表名
- 确保数据完整性
- 支持回滚机制

## 4. 关键约束条件

### 4.1 Wholesale模块重构要求
**重要性**：🚨 **极高优先级 - 业务安全**

**重构策略**：
- **禁止删除Wholesale模块中的任何文件**
- **从Wholesale模块复制文件到catalog模块**，而不是重新创建
- 复制后调整为catalog模块的规范（表名、包名等）
- Wholesale模块保持原有文件，仅修改依赖关系

**重构范围**：
- 从Wholesale复制所有产品相关的Entity、Service、Mapper到catalog模块
- 更新Wholesale模块的依赖配置，使用catalog模块的服务
- 保持Wholesale模块的Controller和API接口不变

**安全要求**：
- **100%业务功能不变**：任何现有功能都不能受影响
- **100%API兼容**：所有现有API接口保持完全一致
- **100%数据一致**：数据迁移后必须完全一致
- **零业务中断**：重构过程中业务不能中断
- **文件安全**：不删除Wholesale模块中的任何现有文件

**验证标准**：
- 所有现有的单元测试必须通过
- 所有现有的集成测试必须通过
- 所有现有的API响应格式必须一致
- 所有现有的业务流程必须正常工作

## 5. 数据库设计

### 5.1 新表结构（catalog模块统一）
- **cat_product**：产品主表
- **cat_product_variant**：产品变体表（SKU、价格、包装类型）
- **cat_category**：产品分类表
- **cat_collection**：产品集合表
- **cat_inventory**：库存水平表
- **cat_location**：仓库位置表

### 5.2 数据迁移映射
| 原表名 | 新表名 |
|--------|--------|
| whs_product | cat_product |
| whs_product_variant | cat_product_variant |
| whs_product_category | cat_category |
| whs_product_series | cat_collection |
| whs_stock | cat_inventory |
| whs_warehouse | cat_location |

## 6. 验收标准

### 6.1 Wholesale重构验收（🚨 关键验收）
**功能验收**：
- [ ] 所有现有产品管理功能正常工作
- [ ] 所有现有库存管理功能正常工作
- [ ] 所有现有海外仓同步功能正常工作
- [ ] 所有现有价格管理功能正常工作
- [ ] 所有现有产品搜索和过滤功能正常工作

**API验收**：
- [ ] 所有现有API接口响应格式完全一致
- [ ] 所有现有API接口性能不降低
- [ ] 所有现有API接口错误处理一致

**数据验收**：
- [ ] 数据迁移后所有产品数据完整
- [ ] 数据迁移后所有库存数据准确
- [ ] 数据迁移后所有价格数据正确
- [ ] 数据迁移后所有关联关系正确

**测试验收**：
- [ ] 所有现有单元测试通过
- [ ] 所有现有集成测试通过
- [ ] 新增的catalog模块测试通过
- [ ] 端到端业务流程测试通过

### 6.2 Catalog模块验收
- [ ] catalog-core模块功能正常
- [ ] catalog-business模块功能正常
- [ ] 数据库表结构符合catalog模块规范
- [ ] 为未来业务扩展预留的接口设计合理

### 6.3 性能验收
- [ ] 产品查询响应时间 < 200ms
- [ ] 并发处理能力 > 1000用户
- [ ] 缓存命中率 > 90%
- [ ] 重构后性能不低于重构前
