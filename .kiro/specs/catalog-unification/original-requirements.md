# 产品目录统一 - 原始需求整理

## 需求背景

### 业务现状
- **Wholesale模块**：已先上线，实现了完整的产品功能
- **Mall模块**：零售业务也需要产品功能
- **数据重复问题**：两个模块都需要产品数据，存在重复维护的问题

### 核心业务需求

#### 1. 产品数据统一管理
> "现在零售的mall中也会有产品。有很多通用的地方。但是我希望最终的产品是以mall中的产品作为基准"

- 希望最终以Mall产品为基准
- 库存同步（从海外仓）以Mall产品为准, 现Wholesale 中已经存在完善库存同步代码
- Wholesale中的产品实际上是Mall产品的来源

#### 2. 业务差异化处理
> "区别是零售不出售箱装、展示盒的产品。并且零售不会有批发价"

- **零售（Mall）限制**：
  - 不出售箱装（Case）产品
  - 不出售展示盒（Display）产品
  - 只销售单品（Individual）包装
  - 不显示批发价，显示建议零售价、售价、成本价等

- **批发（Wholesale）完整功能**：
  - 显示所有包装类型（单品/展示盒/箱装）
  - 显示批发价格体系
  - 显示建议零售价 MSRP

#### 3. 价格管理统一
> "价格可以统一放到产品里维护"

- **统一价格存储**: 所有价格信息（批发价、建议零售价、售价、成本价等）统一存储在产品数据中
- **差异化展示**: 通过业务逻辑控制不同模块显示不同的价格字段
- **简化管理**: 避免价格数据分散维护，降低管理复杂度

#### 4. 技术架构要求
> "按照最佳实践"、"按照行业规范做"

- 遵循行业最佳实践
- 最大化代码复用
- 建立统一的数据模型和服务层
- 支持业务差异化配置

### 详细功能需求

#### 1. 统一产品数据模型
- 建立包含所有必要字段的统一产品表
- 支持多种包装类型（Individual/Display/Case）
- 统一价格管理（批发价/零售价/成本价/建议零售价）
- 库存信息统一管理
- 业务配置字段（可见性控制）

#### 2. 业务差异化实现
- **Mall模块**：
  - 产品过滤：仅显示Individual包装
  - 价格过滤：显示建议零售价、售价、成本价
  - 隐藏批发相关信息

- **Wholesale模块**：
  - 产品完整：显示所有包装类型
  - 价格完整：显示批发价和建议零售价
  - 保持现有功能完整性

#### 3. 完整产品功能抽取
> "Wholesale 中还有一个库存补货的逻辑。所有和产品有关的都要抽取出来"

- **产品核心功能抽取**: 将Wholesale中所有产品相关功能完整抽取
- **产品管理功能包括**:
  - 产品基础管理（CRUD、状态管理、分类管理）
  - 产品变体管理（规格变体、包装变体）
  - 产品属性管理（属性定义、属性值、属性关联）
  - 产品系列和分类管理
  - 产品层级结构管理
  - 产品搜索和推荐
  - 产品特征和标签管理
  - 产品图片和媒体管理
  - 产品导入导出功能
  - 产品缓存和性能优化
  - 产品事件处理和通知

- **库存管理功能包括**:
  - 库存操作服务（增减、锁定、释放、调整）
  - 库存同步服务（海外仓同步、多仓库管理）
  - 补货通知服务（自动补货提醒、邮件通知）
  - 库存预警服务（低库存告警、阈值管理）
  - 到货通知服务（用户订阅通知、库存恢复通知）
  - 库存监控和报表（库存统计、库存分析）
  - 库存缓存管理（高性能库存查询）
  - 库存分配服务（订单库存分配、锁定管理）

- **海外仓集成功能**:
  - 海外仓库存同步
  - 入库单同步管理
  - 产品映射策略
  - 多仓库协调管理

- **业务差异化**: Mall模块简化展示，Wholesale保持完整功能

#### 4. 代码复用策略
- 复用Wholesale现有的完整库存管理代码
- 抽象通用的产品管理服务
- 建立统一的数据访问层
- 提供业务适配器接口
- 保持现有的事件驱动架构
