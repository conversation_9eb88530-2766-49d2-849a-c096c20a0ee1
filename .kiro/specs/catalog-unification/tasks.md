# 产品目录统一 - 任务清单

## 项目概述
将Wholesale模块的产品功能抽取为统一的产品目录模块，采用catalog模块统一的数据库设计。**重点确保Wholesale模块重构后100%业务功能不变。**

---

- [ ] 1. 基础架构搭建
    - [ ] 1.1 创建catalog模块结构
        - 创建lookah-business-common-catalog父模块
        - 创建catalog-core子模块（核心数据层）
        - 创建catalog-business子模块（业务逻辑层）
        - 配置模块间的依赖关系
        - 配置Maven构建和打包
        - _需求: 4.1_

    - [ ] 1.2 使用usql查询并创建数据库表结构
        - 使用usql连接数据库，查询whs_product表的实际结构，创建cat_product表（参考Shopify products表）
        - 使用usql查询whs_product_variant表结构，创建cat_product_variant表（参考Shopify product_variants表）
        - 使用usql查询whs_product_category表结构，创建cat_category表（参考Shopify collections表）
        - 使用usql查询whs_product_series表结构，创建cat_collection表（参考Shopify custom_collections表）
        - 使用usql查询whs_stock表结构，创建cat_inventory表（参考Shopify inventory_levels表）
        - 使用usql查询whs_warehouse表结构，创建cat_location表（参考Shopify locations表）
        - 根据最新规范调整字段类型（枚举改为TINYINT等）
        - 保持现有索引和约束关系
        - 禁止依赖项目中的SQL文件，必须基于实际数据库表结构
        - _需求: 5.1,5.2_

- [ ] 2. 核心实体和数据访问层
    - [ ] 2.1 从Wholesale模块复制核心实体类到catalog模块
        - 从Wholesale复制WhsProduct实体，调整为CatProduct实体（对应cat_product表）
        - 从Wholesale复制WhsProductVariant实体，调整为CatProductVariant实体（对应cat_product_variant表）
        - 从Wholesale复制WhsProductCategory实体，调整为CatCategory实体（对应cat_category表）
        - 从Wholesale复制WhsProductSeries实体，调整为CatCollection实体（对应cat_collection表）
        - 从Wholesale复制WhsStock实体，调整为CatInventory实体（对应cat_inventory表，添加version乐观锁字段）
        - 从Wholesale复制WhsWarehouse实体，调整为CatLocation实体（对应cat_location表）
        - **重要**：保留Wholesale模块中的原文件，不删除任何文件
        - 仅调整包名、类名、@TableName注解，保持字段定义和业务逻辑完全一致
        - _需求: 3.1_

    - [ ] 2.2 创建枚举类
        - PackagingType枚举（0单品/1展示盒/2整箱）
        - BusinessType枚举（0批发/1零售）
        - LocationType枚举（0仓库/1门店/2供应商）
        - 注意：禁止创建status相关枚举，使用BusinessConstants
        - _需求: 3.1_

    - [ ] 2.3 从Wholesale模块复制数据访问层到catalog模块
        - 从Wholesale复制IWhsProductMapper及XML，调整为CatProductMapper接口
        - 从Wholesale复制IWhsProductVariantMapper及XML，调整为CatProductVariantMapper接口
        - 从Wholesale复制IWhsProductCategoryMapper及XML，调整为CatCategoryMapper接口
        - 从Wholesale复制IWhsProductSeriesMapper及XML，调整为CatCollectionMapper接口
        - 从Wholesale复制IWhsStockMapper及XML，调整为CatInventoryMapper接口
        - 从Wholesale复制IWhsWarehouseMapper及XML，调整为CatLocationMapper接口
        - **重要**：保留Wholesale模块中的原Mapper文件和XML文件
        - 仅调整包名、接口名、表名引用，保持查询方法和业务逻辑完全一致
        - _需求: 3.1_

    - [ ] 2.4 创建核心服务接口
        - ICatProductCoreService（产品基础CRUD）
        - ICatProductVariantCoreService（变体基础CRUD）
        - ICatCategoryCoreService（分类基础CRUD）
        - ICatCollectionCoreService（集合基础CRUD）
        - ICatInventoryCoreService（库存基础CRUD）
        - ICatLocationCoreService（位置基础CRUD）
        - _需求: 3.1_

- [ ] 3. 业务适配器设计
    - [ ] 3.1 设计适配器接口
        - IBusinessAdapter接口定义
        - 产品过滤方法定义
        - 价格过滤方法定义
        - 包装类型控制方法定义
        - 功能权限控制方法定义
        - _需求: 2.1,2.2_

    - [ ] 3.2 实现WholesaleAdapter
        - 显示所有包装类型的产品
        - 显示完整价格信息（批发价、零售价、建议零售价、成本价）
        - 提供完整的产品管理功能
        - 显示详细的库存信息
        - _需求: 2.1_

    - [ ] 3.3 实现RetailAdapter
        - 仅显示Individual包装类型的产品
        - 仅显示零售相关价格（零售价、建议零售价）
        - 提供简化的产品浏览功能
        - 仅显示库存可用性信息
        - _需求: 2.2_

    - [ ] 3.4 创建适配器工厂
        - BusinessAdapterFactory实现
        - 根据BusinessType返回对应适配器
        - 支持适配器的扩展和配置
        - _需求: 2.1,2.2_

- [ ] 4. 业务服务层实现
    - [ ] 4.1 实现产品目录服务
        - ICatProductCatalogService实现
        - 产品详情查询（集成适配器）
        - 产品列表查询（支持搜索、过滤、分页）
        - 产品管理功能（创建、更新、删除）
        - 产品层级结构查询
        - _需求: 3.1_

    - [ ] 4.2 实现库存管理服务
        - ICatInventoryService实现
        - 库存信息查询（集成适配器）
        - 库存操作（调整、锁定、释放）
        - 海外仓库存同步
        - 库存预警和通知
        - _需求: 3.2_

    - [ ] 4.3 实现定价服务
        - ICatPricingService实现
        - 价格信息查询（集成适配器）
        - 价格计算和策略
        - 批量价格获取
        - _需求: 3.3_

    - [ ] 4.4 实现海外仓服务
        - ICatOverseasWarehouseService实现
        - 海外仓库存同步逻辑
        - 入库单处理
        - 多仓库协调管理
        - _需求: 3.2_

- [ ] 5. 数据迁移实施
    - [ ] 5.0 准备usql数据库连接
        - 从项目配置中获取数据库连接信息
        - 使用usql连接到数据库
        - 验证可以正常查询whs_*表
        - 准备常用的查询命令（DESCRIBE、SHOW CREATE TABLE等）
        - _需求: 数据库操作规范_
    - [ ] 5.1 使用usql查询数据并编写迁移脚本
        - 使用usql查询whs_product表数据，编写迁移脚本到cat_product表
        - 使用usql查询whs_product_variant表数据，编写迁移脚本到cat_product_variant表
        - 使用usql查询whs_product_category表数据，编写迁移脚本到cat_category表
        - 使用usql查询whs_product_series表数据，编写迁移脚本到cat_collection表
        - 使用usql查询whs_stock表数据，编写迁移脚本到cat_inventory表
        - 使用usql查询whs_warehouse表数据，编写迁移脚本到cat_location表
        - 基于实际数据结构和内容编写迁移脚本，不依赖项目中的SQL文件
        - _需求: 4.3_

    - [ ] 5.2 数据迁移验证
        - 数据完整性验证脚本
        - 数据一致性对比脚本
        - 关联关系验证脚本
        - 业务数据验证脚本
        - _需求: 7.1_

    - [ ] 5.3 执行数据迁移
        - 备份现有数据库
        - 执行表结构创建
        - 执行数据迁移脚本
        - 验证迁移结果
        - 准备回滚方案
        - _需求: 4.3,7.1_

- [ ] 6. Wholesale模块重构（🚨 关键任务）
    - [ ] 6.1 更新依赖配置
        - 在Wholesale模块中添加catalog模块依赖
        - 更新Spring配置和组件扫描，支持catalog模块的Bean
        - 保持原有依赖配置不变
        - _需求: 5.1_

    - [ ] 6.2 重构Service层实现
        - **保留**原有的IWhsProductService等接口定义
        - **保留**原有的Service实现类文件
        - **修改**Service实现类的内部逻辑，调用catalog模块的服务
        - 在Service调用中指定BusinessType.WHOLESALE
        - 保持所有方法签名和返回格式不变
        - _需求: 5.1_

    - [ ] 6.3 验证Controller层
        - **不修改**任何Controller代码
        - 确保Controller继续使用原有的Service接口
        - 确保API请求参数格式不变
        - 确保API响应格式不变
        - 确保错误处理逻辑不变
        - _需求: 5.1_

    - [ ] 6.4 文件保留策略
        - **禁止删除**任何原有的产品实体类
        - **禁止删除**任何原有的产品Mapper
        - **禁止删除**任何原有的产品Service文件
        - **仅在数据迁移完成后**删除旧的数据库表
        - _需求: 5.1_

- [ ] 7. 测试和验证（🚨 关键任务）
    - [ ] 7.1 单元测试
        - catalog-core模块单元测试
        - catalog-business模块单元测试
        - 适配器单元测试
        - 业务服务单元测试
        - _需求: 7.2_

    - [ ] 7.2 集成测试
        - catalog模块集成测试
        - Wholesale模块重构后集成测试
        - 数据库操作集成测试
        - 适配器集成测试
        - _需求: 7.1_

    - [ ] 7.3 API兼容性测试
        - 所有Wholesale API响应格式对比测试
        - API性能对比测试
        - 错误处理对比测试
        - HTTP状态码对比测试
        - _需求: 7.1_

    - [ ] 7.4 业务功能验证
        - 产品管理功能完整性测试
        - 库存管理功能完整性测试
        - 海外仓同步功能测试
        - 价格管理功能测试
        - 端到端业务流程测试
        - _需求: 7.1_

    - [ ] 7.5 性能测试
        - 产品查询性能测试（< 200ms）
        - 并发处理能力测试（> 1000用户）
        - 缓存命中率测试（> 90%）
        - 重构前后性能对比测试
        - _需求: 7.3_

- [ ] 8. Mall模块预留接口（低优先级）
    - [ ] 8.1 验证RetailAdapter
        - 确保RetailAdapter正确过滤产品
        - 确保RetailAdapter正确过滤价格
        - 确保RetailAdapter功能权限控制正确
        - _需求: 5.2_

    - [ ] 8.2 为Mall模块预留服务接口
        - 设计Mall模块的产品服务接口
        - 确保catalog模块支持Mall的业务需求
        - 为后续Mall集成做好准备
        - _需求: 5.2_

---

## 风险控制措施

### 高风险任务标识
- 🚨 **任务6（Wholesale模块重构）**：直接影响现有业务，必须确保100%功能不变
- 🚨 **任务7（测试和验证）**：确保重构质量，防止业务事故

### 回滚准备
- 每个阶段完成后都要有完整的回滚方案
- 数据迁移前必须完整备份
- 代码重构前必须创建回滚分支

### 验证标准
- 所有现有单元测试必须通过
- 所有现有集成测试必须通过
- API响应格式必须完全一致
- 业务功能必须完全正常
