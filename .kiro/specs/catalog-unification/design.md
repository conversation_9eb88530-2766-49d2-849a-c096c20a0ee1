# 产品目录统一 - 技术设计文档

## 1. 设计目标

将Wholesale模块的产品功能抽取为统一模块，采用catalog模块统一的数据库设计，为未来业务扩展预留接口。

## 2. 架构设计

### 2.1 模块结构

```
lookah-business-common-catalog/
├── catalog-core/        # 核心数据层
└── catalog-business/    # 业务逻辑层 + 对外接口
```

### 2.2 层次职责

#### catalog-core（核心数据层）
- 实体类：CatProduct、CatProductVariant、CatCategory、CatCollection、CatInventory、CatLocation
- 数据访问：Mapper接口和基础CRUD
- 核心服务：纯数据操作，无业务逻辑

#### catalog-business（业务逻辑层）
- 业务服务：ICatProductCatalogService、ICatInventoryService、ICatPricingService
- 扩展接口：为未来业务差异化预留适配器接口
- VO/BO类：CatProductVo、CatProductBo等（不分前后台目录）
- 对外接口：直接为业务模块提供服务

## 3. 数据模型设计

### 3.1 实体映射（参考Shopify设计，符合项目规范）

| 原实体 | 新实体 | 新表名 | Shopify对应 | 说明 |
|--------|--------|--------|-------------|------|
| WhsProduct | CatProduct | cat_product | products | 产品主表（SPU） |
| WhsProductVariant | CatProductVariant | cat_product_variant | product_variants | 产品变体（SKU、价格、包装） |
| WhsProductCategory | CatCategory | cat_category | collections | 产品分类 |
| WhsProductSeries | CatCollection | cat_collection | custom_collections | 产品集合/系列 |
| WhsStock | CatInventory | cat_inventory | inventory_levels | 库存水平 |
| WhsWarehouse | CatLocation | cat_location | locations | 仓库/门店位置 |

### 3.2 关键字段

**价格字段**（cat_product_variant表）：
- wholesale_price：批发价
- retail_price：零售价
- msrp：建议零售价
- cost_price：成本价

**包装类型**（packaging_type字段）：
- Individual：单品包装
- Display：展示盒包装
- Case：箱装包装

## 4. 扩展接口设计

### 4.1 业务适配器接口预留

**为未来业务差异化预留接口**：
- 产品过滤接口：支持根据业务类型过滤产品
- 价格过滤接口：支持根据业务类型过滤价格信息
- 包装类型控制接口：支持定义允许的包装类型
- 功能权限控制接口：支持控制业务功能的可用性

### 4.2 当前实现策略

**当前阶段**：
- 保持Wholesale模块的完整功能
- 预留适配器接口但暂不实现具体的业务差异化
- 确保catalog模块的通用性和可扩展性

## 5. 数据迁移策略

### 5.1 迁移步骤

1. **创建新表**：按catalog模块规范创建新数据库表
2. **数据迁移**：一次性迁移所有数据到新表
3. **代码重构**：更新所有相关代码使用新实体
4. **删除旧表**：完成后删除旧表
5. **功能测试**：确保所有功能正常

### 5.2 服务接口映射

| 原服务 | 新服务 |
|--------|--------|
| IWhsProductService | ICatProductCatalogService |
| IWhsStockService | ICatInventoryService |
| IWhsProductVariantService | ICatProductVariantService |

## 6. 核心服务接口设计

### 6.1 产品目录服务

**服务职责**：
- 产品详情查询：获取完整的产品详情信息
- 产品列表查询：支持搜索、过滤、分页的产品列表
- 产品管理：产品的创建、更新、删除操作
- 产品层级查询：获取产品的分类和集合层级结构

**关键方法**：
- getProductDetail：获取单个产品详情
- getProductList：获取产品列表，支持关键词搜索和分类过滤
- saveProduct：保存产品信息，包括基本信息和变体信息
- deleteProduct：删除产品，同时处理关联的变体和库存

### 6.2 库存管理服务

**服务职责**：
- 库存信息查询：获取完整的库存信息
- 库存操作：库存调整、锁定、释放等操作
- 海外仓同步：从海外仓系统同步库存数据
- 库存预警：低库存告警和补货提醒

**关键方法**：
- getInventoryInfo：获取库存信息
- adjustInventory：库存调整，支持增减、锁定、释放等操作
- syncInventoryFromOverseas：海外仓库存同步，支持增量和全量同步
- getBatchInventoryInfo：批量获取库存信息，用于产品列表展示

### 6.3 扩展机制预留

**扩展策略**：
- 预留适配器工厂接口
- 预留业务类型参数传递机制
- 预留数据过滤和转换接口
- 确保未来可以轻松添加业务差异化功能

## 7. 技术设计细节

### 7.1 模块依赖关系

```
lookah-wholesale ──┐
                   ├──> lookah-business-common-catalog
lookah-mall ───────┘         │
                             ├──> catalog-business
                             └──> catalog-core ──> lookah-common
```

**依赖说明**：
- catalog-core：依赖lookah-common，提供基础数据访问
- catalog-business：依赖catalog-core，提供业务逻辑和适配器
- wholesale/mall模块：依赖catalog-business，获取产品服务

### 7.2 数据库表关系设计

```
cat_product (产品主表)
    │
    ├── cat_product_variant (产品变体表)
    │       │
    │       └── cat_inventory (库存水平表)
    │               │
    │               └── cat_location (位置表)
    │
    ├── cat_category (分类表)
    └── cat_collection (集合表)
```

**关键表字段设计**：

**cat_product表核心字段**：
- id：主键
- item_name：产品名称
- category_id：分类ID（外键）
- collection_id：集合ID（外键）
- default_variant_id：默认变体ID
- status：状态（0停用 1启用）
- sort_order：排序权重

**cat_product_variant表核心字段**：
- id：主键
- product_id：产品ID（外键）
- sku：SKU编码（唯一）
- packaging_type：包装类型（0单品 1展示盒 2整箱）
- wholesale_price：批发价
- retail_price：零售价
- msrp：建议零售价
- cost_price：成本价
- weight：重量
- dimensions：尺寸

**cat_inventory表核心字段**：
- variant_id + location_id：联合唯一键
- available_quantity：可用库存
- reserved_quantity：预留库存
- incoming_quantity：在途库存
- version：乐观锁版本号（库存并发控制必需）

### 7.3 业务适配器设计细节

**适配器调用流程**：
```
Business Module Request
        │
        ▼
Business Service
        │
        ├── 获取原始数据
        ├── 根据BusinessType获取适配器
        ├── 应用适配器过滤规则
        └── 返回适配后的数据
        │
        ▼
Filtered Response
```

**WholesaleAdapter过滤规则**：
- 产品过滤：显示所有状态为ACTIVE的产品
- 变体过滤：显示所有包装类型（Individual/Display/Case）
- 价格过滤：显示完整价格信息（批发价、零售价、建议零售价、成本价）
- 库存过滤：显示详细库存信息（各仓库分布、预留库存、在途库存）

**RetailAdapter过滤规则**：
- 产品过滤：仅显示包含Individual包装的产品
- 变体过滤：仅显示Individual包装类型的变体
- 价格过滤：仅显示零售相关价格（零售价、建议零售价）
- 库存过滤：仅显示总可用库存和是否有货状态

### 7.4 服务层交互设计

**ICatProductCatalogService调用链**：
```
Controller
    │
    ▼
ICatProductCatalogService
    │
    ├── ProductCoreService (获取基础数据)
    ├── BusinessAdapterFactory (获取适配器)
    ├── CategoryService (获取分类信息)
    ├── CatCollectionService (获取集合信息)
    └── ICatInventoryService (获取库存信息)
    │
    ▼
Aggregated Response
```

**ICatInventoryService调用链**：
```
Controller
    │
    ▼
ICatInventoryService
    │
    ├── InventoryCoreService (获取库存数据)
    ├── CatLocationService (获取位置信息)
    ├── BusinessAdapter (应用业务过滤)
    └── EventPublisher (发布库存变更事件)
    │
    ▼
Filtered Inventory Info
```

### 7.5 数据迁移设计

**迁移策略**：
1. **使用usql查询现有表结构**：通过usql连接数据库，查询whs_*表的实际结构
2. **基于实际表结构创建新表**：根据usql查询结果创建cat_*表，确保字段完全一致
3. **复制现有代码**：复制Wholesale模块的实体类、Service等，确保业务逻辑一致
4. **规范化调整**：根据最新规范调整表名、字段类型、枚举存储等
5. **数据迁移脚本**：编写SQL脚本将数据从旧表迁移到新表
6. **逐步替换**：逐步替换Wholesale模块中的引用

**实施细节**：
- **usql查询表结构**：使用usql连接数据库，执行DESCRIBE或SHOW CREATE TABLE查询实际表结构
- **基于实际结构创建表**：根据usql查询结果创建cat_*表，不依赖项目中的SQL文件
- **代码复制策略**：**从Wholesale模块复制文件到catalog模块，禁止删除Wholesale中的原文件**
- **复制范围**：复制Entity、Mapper、Service、XML配置文件等所有相关代码
- **规范调整**：仅调整表名、包名、类名等，保持业务逻辑完全一致
- **业务逻辑保持**：确保所有业务逻辑、计算规则、验证规则完全一致
- **数据直接迁移**：使用INSERT INTO ... SELECT语句直接迁移数据

### 7.6 Wholesale模块重构设计

**重构策略**：
- **禁止删除文件**：不删除Wholesale模块中的任何Entity、Service、Mapper文件
- **复制到catalog**：将所有产品相关文件复制到catalog模块
- **调整依赖关系**：Wholesale模块改为依赖catalog模块的服务
- **保持接口不变**：Wholesale的Controller和API接口完全不变

**重构范围**：
- **依赖配置**：在Wholesale模块中添加catalog模块依赖
- **Service层**：保留原有Service接口，内部调用catalog模块服务
- **Controller层**：保持不变，继续使用原有的Service接口
- **文件保留**：所有原有的Entity、Mapper、Service实现文件都保留

**重构步骤**：
- **第一步**：复制文件到catalog模块并调整规范
- **第二步**：在Wholesale中注入catalog服务
- **第三步**：修改Wholesale Service实现，内部调用catalog服务
- **第四步**：保持所有原有文件，仅修改实现逻辑

**API兼容性保证**：
- **请求参数**：保持所有现有API的请求参数格式不变
- **响应格式**：保持所有现有API的响应格式不变
- **错误码**：保持所有现有的错误码和错误信息不变
- **HTTP状态码**：保持所有现有的HTTP状态码不变

**重构验证机制**：
- **对比测试**：重构前后API响应进行逐一对比
- **性能测试**：确保重构后性能不低于重构前
- **压力测试**：验证重构后系统稳定性
- **回归测试**：运行所有现有的测试用例

### 7.7 异常处理设计

**异常层次结构**：
- CatalogException（基础异常）
  - ProductNotFoundException（产品未找到）
  - InventoryInsufficientException（库存不足）
  - PriceCalculationException（价格计算异常）
  - BusinessAdapterException（适配器异常）
  - DataMigrationException（数据迁移异常）

**异常处理策略**：
- 业务异常：返回明确的错误码和错误信息
- 系统异常：记录详细日志，返回通用错误信息
- 数据异常：进行数据修复或降级处理
- 迁移异常：立即停止迁移，保护现有数据

**Wholesale重构异常处理**：
- **兼容性异常**：如果API响应格式不一致，立即回滚
- **性能异常**：如果响应时间超过阈值，进行性能优化
- **数据异常**：如果数据不一致，立即停止并修复
- **功能异常**：如果任何功能不工作，立即回滚到原有实现
