# 批发端新功能需求文档

## 1. 需求概述

为批发端增加国外销售团队录入客户资料和订单的功能，支持英语界面，提供完整的客户管理和订单管理流程。

## 2. 功能需求分析

### 2.1 统一后台登录
- **现状**: 已存在批发端后台登录系统
- **需求**: 国外销售团队使用相同的后台系统
- **实现**: 无需额外开发，使用现有权限系统

### 2.2 客户信息录入功能扩展

#### 2.2.1 现有字段（已实现）
- 客户编码（根据系统格式自动生成）
- 公司名称（firstName + lastName组合）
- 发货地址（shipping相关字段）
- 销售代表（salespersonId）
- 所在国（shippingCountry）
- 所在州（shippingState）

#### 2.2.2 需要新增的字段
基于现有`whs_member`表结构，需要新增以下字段：

```sql
-- 公司类型枚举：批发、零售店、连锁店、cash&carry
ALTER TABLE `whs_member` ADD COLUMN `company_type` varchar(50) DEFAULT NULL COMMENT '公司类型（wholesale,retail,chain,cash_carry）' AFTER `lastName`;

-- 店面数量
ALTER TABLE `whs_member` ADD COLUMN `store_count` int DEFAULT 1 COMMENT '店面数量' AFTER `company_type`;

-- 客户来源
ALTER TABLE `whs_member` ADD COLUMN `customer_source` varchar(200) DEFAULT NULL COMMENT '客户来源' AFTER `store_count`;

-- 公司名称（独立字段）
ALTER TABLE `whs_member` ADD COLUMN `company_name` varchar(200) DEFAULT NULL COMMENT '公司名称' AFTER `customer_source`;
```

### 2.3 订单明细录入功能扩展

#### 2.3.1 现有功能（已实现）
- 通过选择SKU下单
- 订单导入功能
- 订单号、产品、SKU、数量、总价、总金额
- 订单流转到发货环节

#### 2.3.2 需要新增的字段
基于现有`whs_order_item`表结构，需要新增以下字段：

```sql
-- 采购单价
ALTER TABLE `whs_order_item` ADD COLUMN `purchase_price` decimal(10,2) DEFAULT NULL COMMENT '采购单价' AFTER `price`;

-- 销售单价（可手动填入，区别于现有的price字段）
ALTER TABLE `whs_order_item` ADD COLUMN `sales_price` decimal(10,2) DEFAULT NULL COMMENT '销售单价（手动填入）' AFTER `purchase_price`;
```

### 2.4 运费管理功能

#### 2.4.1 现状分析
- 已有昊通系统集成（HaotongOverseasWarehouseProvider）
- 已有物流方式管理（WhsWarehouseLogisticsMethod）
- 已有发货功能

#### 2.4.2 需要新增的功能
- 订单列表显示运费信息
- 发货后同步昊通系统实际运费金额
- 运费数据存储和展示

```sql
-- 订单运费信息表
CREATE TABLE `whs_order_freight` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `estimated_freight` decimal(10,2) DEFAULT NULL COMMENT '预估运费',
  `actual_freight` decimal(10,2) DEFAULT NULL COMMENT '实际运费',
  `freight_currency` varchar(10) DEFAULT 'USD' COMMENT '运费币种',
  `sync_time` datetime DEFAULT NULL COMMENT '运费同步时间',
  `sync_status` char(1) DEFAULT '0' COMMENT '同步状态（0未同步 1已同步）',
  `haotong_tracking_no` varchar(100) DEFAULT NULL COMMENT '昊通跟踪号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) NOT NULL DEFAULT '0' COMMENT '删除标志',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_sync_status` (`sync_status`),
  KEY `idx_sync_time` (`sync_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单运费信息表';
```

### 2.5 国际化支持

#### 2.5.1 需要支持的页面
- 会员管理 → 会员列表
- 订单管理 → 订单列表
- 相关的增删改查页面

#### 2.5.2 现有国际化基础
- 后端已有国际化支持（MessageUtils、I18nLocaleResolver）
- 前端已有国际化配置（vue-i18n）
- 支持中英文切换

#### 2.5.3 需要扩展的国际化内容
- 新增字段的多语言标签
- 表单验证消息
- 操作提示信息

## 3. 技术架构分析

### 3.1 后端架构
- **框架**: Spring Boot + MyBatis Plus
- **权限**: Sa-Token
- **国际化**: Spring MessageSource
- **数据库**: MySQL

### 3.2 前端架构
- **框架**: Nuxt.js + Vue 3
- **UI组件**: PrimeVue
- **国际化**: vue-i18n
- **状态管理**: Pinia

### 3.3 第三方集成
- **昊通系统**: 已有完整的API集成
- **物流管理**: 已有物流渠道同步功能

## 4. 数据库设计

### 4.1 现有表结构
- `whs_member`: 客户信息表
- `whs_order`: 订单主表
- `whs_order_item`: 订单明细表
- `whs_warehouse_logistics_method`: 物流方式表

### 4.2 需要修改的表
1. **whs_member表扩展**
   - company_type: 公司类型
   - store_count: 店面数量
   - customer_source: 客户来源
   - company_name: 公司名称

2. **whs_order_item表扩展**
   - purchase_price: 采购单价
   - sales_price: 销售单价

3. **新增whs_order_freight表**
   - 运费信息管理

## 5. 权限设计

### 5.1 现有权限体系
- 基于Sa-Token的权限管理
- 支持角色和权限分离
- 已有批发端相关权限

### 5.2 需要新增的权限
- 国外销售团队角色
- 客户信息扩展字段的编辑权限
- 运费信息查看权限

## 6. 接口设计

### 6.1 客户管理接口扩展
- 扩展现有的客户CRUD接口
- 支持新增字段的验证和处理

### 6.2 订单管理接口扩展
- 扩展订单明细接口
- 新增运费信息接口

### 6.3 昊通系统集成
- 扩展现有的昊通API调用
- 新增运费同步接口

## 7. 国际化实现

### 7.1 后端国际化
- 扩展现有的messages.properties
- 新增英文资源文件

### 7.2 前端国际化
- 扩展现有的i18n配置
- 新增相关页面的多语言支持

## 8. 实施计划

详细的任务拆分将在后续的任务管理中进行规划，主要包括：
1. 数据库结构调整
2. 后端接口开发
3. 前端页面开发
4. 国际化实现
5. 测试和部署

## 9. 风险评估

### 9.1 技术风险
- 数据库结构变更需要谨慎处理
- 昊通系统API调用的稳定性

### 9.2 业务风险
- 新增字段对现有业务流程的影响
- 国际化实现的完整性

## 10. 验收标准

1. 客户信息录入功能完整实现
2. 订单明细录入功能扩展完成
3. 运费管理功能正常工作
4. 国际化支持完整
5. 现有功能不受影响
