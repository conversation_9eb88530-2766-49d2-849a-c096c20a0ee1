#!/bin/bash
set -e

# 检查当前目录是否为Git仓库
if ! git rev-parse --is-inside-work-tree > /dev/null 2>&1; then
  echo "错误: 当前目录不是Git仓库"
  echo "请确保您在项目的根目录下运行此脚本，并且该目录已初始化为Git仓库"
  echo "您可以通过运行以下命令初始化Git仓库:"
  echo "  git init"
  echo "  git remote add origin <您的远程仓库URL>"
  exit 1
fi

# 确保脚本在项目根目录下运行
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# 检查脚本是否在根目录还是在scripts目录下
if [[ "$(basename "$SCRIPT_DIR")" == "scripts" ]]; then
  PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
  cd "$PROJECT_ROOT" || { echo "无法切换到项目根目录"; exit 1; }
else
  PROJECT_ROOT="$SCRIPT_DIR"
fi

echo "当前工作目录: $(pwd)"

# 基本配置
MAIN_BRANCH="main"
DEV_BRANCH="dev"
REMOTE="origin"

# 检查当前分支
CURRENT_BRANCH=$(git branch --show-current)

# 检查必要的分支是否存在
check_branch_exists() {
  git show-ref --verify --quiet refs/heads/$1
  return $?
}

# 如果必要的分支不存在，询问是否创建
if ! check_branch_exists $MAIN_BRANCH; then
  echo "警告: $MAIN_BRANCH 分支不存在"
  read -p "是否创建 $MAIN_BRANCH 分支? (y/n): " CREATE_MAIN
  if [ "$CREATE_MAIN" == "y" ]; then
    # 检查当前是否有任何分支
    if [ -z "$(git branch)" ]; then
      # 没有分支，创建主分支并提交初始化文件
      echo "创建初始提交..."
      touch README.md
      echo "# Lookah 电商平台" > README.md
      git add README.md
      git commit -m "初始化项目"
    fi
    
    CURRENT_BRANCH=$(git branch --show-current)
    git branch $MAIN_BRANCH
    echo "已创建 $MAIN_BRANCH 分支"
  else
    echo "错误: 没有 $MAIN_BRANCH 分支，无法继续"
    exit 1
  fi
fi

if ! check_branch_exists $DEV_BRANCH; then
  echo "警告: $DEV_BRANCH 分支不存在"
  read -p "是否基于 $MAIN_BRANCH 创建 $DEV_BRANCH 分支? (y/n): " CREATE_DEV
  if [ "$CREATE_DEV" == "y" ]; then
    git checkout $MAIN_BRANCH
    git branch $DEV_BRANCH
    git checkout $DEV_BRANCH
    echo "已创建并切换到 $DEV_BRANCH 分支"
  else
    echo "错误: 没有 $DEV_BRANCH 分支，无法继续"
    exit 1
  fi
fi

# 重新获取当前分支（可能在创建分支过程中切换了）
CURRENT_BRANCH=$(git branch --show-current)
if [ "$CURRENT_BRANCH" != "$DEV_BRANCH" ] && [ "$CURRENT_BRANCH" != "$MAIN_BRANCH" ]; then
  echo "错误: 必须在 $DEV_BRANCH 或 $MAIN_BRANCH 分支上执行此脚本"
  exit 1
fi

# 检查是否已配置远程仓库
check_remote_exists() {
  git remote | grep -q "$1"
  return $?
}

if ! check_remote_exists "$REMOTE"; then
  echo "警告: 远程仓库 '$REMOTE' 不存在"
  read -p "是否配置远程仓库? (y/n): " SETUP_REMOTE
  if [ "$SETUP_REMOTE" == "y" ]; then
    read -p "请输入远程仓库URL: " REMOTE_URL
    git remote add $REMOTE $REMOTE_URL
    echo "已配置远程仓库 $REMOTE: $REMOTE_URL"
  else
    echo "警告: 未配置远程仓库，将仅在本地创建版本标签"
    echo "注意: 推送操作将被跳过"
    SKIP_PUSH=true
  fi
else
  SKIP_PUSH=false
fi

# 确保本地代码是最新的
echo "正在更新本地仓库..."
git fetch $REMOTE --prune || true

# 检查工作区是否干净
if [ -n "$(git status --porcelain)" ]; then
  echo "错误: 工作区有未提交的更改，请先提交或暂存更改"
  exit 1
fi

# 如果在dev分支，确保它是最新的
if [ "$CURRENT_BRANCH" == "$DEV_BRANCH" ]; then
  echo "正在拉取 $DEV_BRANCH 最新代码..."
  git pull $REMOTE $DEV_BRANCH || true
fi

# 如果在main分支，确保它是最新的
if [ "$CURRENT_BRANCH" == "$MAIN_BRANCH" ]; then
  echo "正在拉取 $MAIN_BRANCH 最新代码..."
  git pull $REMOTE $MAIN_BRANCH || true
fi

# 获取最新标签版本
LATEST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0")
echo "最新标签版本: $LATEST_TAG"

# 解析最新版本号
if [[ $LATEST_TAG =~ ^v([0-9]+)\.([0-9]+)\.([0-9]+) ]]; then
  MAJOR="${BASH_REMATCH[1]}"
  MINOR="${BASH_REMATCH[2]}"
  PATCH="${BASH_REMATCH[3]}"
else
  # 如果标签格式不标准，默认使用0.0.0
  MAJOR=0
  MINOR=0
  PATCH=0
  echo "警告: 标签格式不标准，使用默认版本 v0.0.0"
fi

# 提示选择版本更新类型
echo "选择要增加的版本类型:"
echo "1) 主版本 (Major) - 当前 v$MAJOR.$MINOR.$PATCH → v$((MAJOR+1)).0.0"
echo "2) 次版本 (Minor) - 当前 v$MAJOR.$MINOR.$PATCH → v$MAJOR.$((MINOR+1)).0"
echo "3) 补丁版本 (Patch) - 当前 v$MAJOR.$MINOR.$PATCH → v$MAJOR.$MINOR.$((PATCH+1))"
echo "4) 自定义版本号"
echo "5) 使用日期格式版本号 (v年.月.日-时分-哈希)"
echo "6) 默认使用下一个补丁版本 (v$MAJOR.$MINOR.$((PATCH+1)))"

read -p "请选择 [1-6] (默认6): " VERSION_TYPE
VERSION_TYPE=${VERSION_TYPE:-6}  # 如果未输入，默认为选项6

case $VERSION_TYPE in
  1)
    NEW_VERSION="v$((MAJOR+1)).0.0"
    ;;
  2)
    NEW_VERSION="v$MAJOR.$((MINOR+1)).0"
    ;;
  3)
    NEW_VERSION="v$MAJOR.$MINOR.$((PATCH+1))"
    ;;
  4)
    read -p "请输入自定义版本号 (格式: vX.Y.Z): " NEW_VERSION
    if ! [[ $NEW_VERSION =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
      echo "错误: 版本号格式不正确，应为 vX.Y.Z 格式"
      exit 1
    fi
    ;;
  5)
    NEW_VERSION="v$(date '+%Y.%m.%d')-$(date '+%H%M')-$(git rev-parse --short HEAD)"
    ;;
  6)
    NEW_VERSION="v$MAJOR.$MINOR.$((PATCH+1))"
    ;;
  *)
    echo "错误: 无效的选择"
    exit 1
    ;;
esac

echo "将使用版本号: $NEW_VERSION"
# 将默认选择设置为 "y"
CONFIRM="y"
read -p "确认使用此版本号? (y/n) [y]: " USER_CONFIRM
CONFIRM=${USER_CONFIRM:-$CONFIRM}  # 如果用户没有输入，则使用默认值

if [ "$CONFIRM" != "y" ]; then 
  echo "已取消操作"
  exit 0
fi

VERSION=$NEW_VERSION
VERSION_NO_V="${VERSION#v}" # 去掉v前缀的版本号

# 生成简洁的中文变更摘要（用于commit消息）
generate_simple_summary() {
  local last_tag="$1"

  # 获取提交数量
  local commit_count
  if [ -n "$last_tag" ]; then
    commit_count=$(git rev-list --count "$last_tag"..HEAD)
  else
    commit_count=$(git rev-list --count HEAD)
  fi

  # 获取变更文件数量
  local changed_files_count
  if [ -n "$last_tag" ]; then
    changed_files_count=$(git diff --name-only "$last_tag"..HEAD | wc -l | xargs)
  else
    changed_files_count=$(git ls-files | wc -l | xargs)
  fi

  echo "包含 ${commit_count} 个提交，变更 ${changed_files_count} 个文件"
}

# 使用 Fast-forward 合并策略确保分支同步
if [ "$CURRENT_BRANCH" == "$DEV_BRANCH" ]; then
  echo "正在切换到 $MAIN_BRANCH 分支..."
  git checkout $MAIN_BRANCH
  git pull $REMOTE $MAIN_BRANCH || true

  # 获取上一个版本标签用于生成变更摘要
  LAST_TAG=""
  if [ -n "$(git tag)" ]; then
    LAST_TAG=$(git describe --tags --abbrev=0)
  fi

  # 生成简洁的变更摘要
  SIMPLE_SUMMARY=$(generate_simple_summary "$LAST_TAG")

  # 使用 Fast-forward 合并确保分支完全同步
  echo "正在进行 Fast-forward 合并 $DEV_BRANCH 到 $MAIN_BRANCH..."
  echo "变更摘要: $SIMPLE_SUMMARY"

  # 执行 Fast-forward 合并
  git merge $DEV_BRANCH --ff-only
  
  if [ $? -ne 0 ]; then
    echo "❌ 错误: Fast-forward 合并失败"
    echo "这通常表示 $MAIN_BRANCH 分支有独立于 $DEV_BRANCH 的提交"
    echo "请确保所有提交都在 $DEV_BRANCH 分支上进行"
    exit 1
  fi
  
  echo "✅ Fast-forward 合并成功，$MAIN_BRANCH 和 $DEV_BRANCH 现已完全同步"
else
  # 如果在main分支，生成变更摘要
  LAST_TAG=""
  if [ -n "$(git tag)" ]; then
    LAST_TAG=$(git describe --tags --abbrev=0)
  fi
  SIMPLE_SUMMARY=$(generate_simple_summary "$LAST_TAG")
fi

echo "正在创建版本标签: $VERSION"

# 检查是否有代码变更
echo "正在检查代码变更..."

# 检查变更的逻辑需要根据当前分支和是否有暂存变更来判断
check_code_changes() {
  local actual_changes=0

  if [ "$CURRENT_BRANCH" == "$DEV_BRANCH" ]; then
    # 从dev分支发版，检查dev分支相对于main分支的变更
    if [ -n "$(git tag)" ]; then
      # 检查dev分支相对于最新标签的变更
      CHANGED_FILES=$(git diff --name-only "$(git describe --tags --abbrev=0)" "$DEV_BRANCH")
      actual_changes=$(echo "$CHANGED_FILES" | grep -v -E 'package.json|pom.xml|.version|^$' | wc -l | xargs)
    else
      # 首次发布，检查dev分支的所有文件
      actual_changes=$(git ls-files | grep -v -E 'package.json|pom.xml|.version' | wc -l | xargs)
    fi
  else
    # 在main分支发版，检查当前工作区是否有变更或暂存的变更
    if [ -n "$(git status --porcelain)" ]; then
      # 有未提交的变更
      actual_changes=1
    elif [ -n "$(git tag)" ]; then
      # 检查相对于最新标签的变更
      CHANGED_FILES=$(git diff --name-only "$(git describe --tags --abbrev=0)" HEAD)
      actual_changes=$(echo "$CHANGED_FILES" | grep -v -E 'package.json|pom.xml|.version|^$' | wc -l | xargs)
    else
      # 首次发布
      actual_changes=$(git ls-files | grep -v -E 'package.json|pom.xml|.version' | wc -l | xargs)
    fi
  fi

  echo $actual_changes
}

ACTUAL_CHANGES=$(check_code_changes)

if [ "$ACTUAL_CHANGES" -eq 0 ]; then
  echo "⚠️  警告: 自上一个版本以来没有检测到实质性代码变更"
  echo "这可能是因为："
  echo "1. 确实没有代码变更"
  echo "2. 变更已经在之前的版本中发布"
  echo "3. 只有文档或配置文件的变更"
  echo ""
  read -p "是否仍要继续发布? (y/n) [n]: " FORCE_RELEASE
  FORCE_RELEASE=${FORCE_RELEASE:-n}

  if [ "$FORCE_RELEASE" != "y" ]; then
    echo "已取消发布操作"
    exit 0
  else
    echo "强制继续发布..."
  fi
else
  echo "✅ 检测到 $ACTUAL_CHANGES 个文件变更，可以继续发布"
fi




# 创建带注释的标签，使用简洁的中文描述
git tag -a "$VERSION" -m "版本 $VERSION 发布

$SIMPLE_SUMMARY

发布时间: $(date '+%Y年%m月%d日 %H:%M')
发布分支: $DEV_BRANCH → $MAIN_BRANCH"

# 更新版本号
echo "正在更新项目版本号..."

# 更新Java项目版本号
if [ -f "server/pom.xml" ]; then
  echo "正在更新server模块版本号..."
  cd server
  if command -v mvn &> /dev/null; then
    sed -i.bak "s/<revision>.*<\/revision>/<revision>$VERSION_NO_V<\/revision>/g" pom.xml && rm -f pom.xml.bak

    # 更新common-bom模块版本号
    if [ -f "lookah-common/lookah-common-bom/pom.xml" ]; then
      echo "正在更新lookah-common-bom模块版本号..."
      sed -i.bak "s/<revision>.*<\/revision>/<revision>$VERSION_NO_V<\/revision>/g" lookah-common/lookah-common-bom/pom.xml && rm -f lookah-common/lookah-common-bom/pom.xml.bak
      echo "已将lookah-common-bom模块版本更新到 $VERSION_NO_V"
    fi

    echo "已将server模块版本更新到 $VERSION_NO_V"
  else
    echo "警告: 未找到mvn命令，无法更新Java项目版本号"
  fi
  cd ..
fi

# 更新前端项目版本号
if [ -f "admin/package.json" ]; then
  echo "正在更新admin模块版本号..."
  sed -i.bak "s/\"version\": \".*\"/\"version\": \"$VERSION_NO_V\"/g" admin/package.json && rm -f admin/package.json.bak
  echo "已将admin模块版本更新到 $VERSION_NO_V"
fi

if [ -f "store/package.json" ]; then
  echo "正在更新store模块版本号..."
  sed -i.bak "s/\"version\": \".*\"/\"version\": \"$VERSION_NO_V\"/g" store/package.json && rm -f store/package.json.bak
  echo "已将store模块版本更新到 $VERSION_NO_V"
fi

# 构建简洁的commit message
if [ "$CURRENT_BRANCH" == "$DEV_BRANCH" ]; then
  # 从dev分支发版的情况
  COMMIT_MESSAGE="Release $VERSION

$SIMPLE_SUMMARY"
else
  # 直接在main分支发版的情况
  COMMIT_MESSAGE="Release $VERSION

$SIMPLE_SUMMARY"
fi

# 一次性提交发版相关变更（版本号更新）
git add .
git commit -m "$COMMIT_MESSAGE"

# 推送主分支和标签到远程
if [ "$SKIP_PUSH" == false ]; then
  echo "正在推送 $MAIN_BRANCH 分支和标签到远程..."
  # 普通推送
  git push $REMOTE $MAIN_BRANCH
  git push $REMOTE "$VERSION"
fi

# 切换回开发分支并同步发版提交
if [ "$CURRENT_BRANCH" == "$DEV_BRANCH" ]; then
  echo "正在切换回 $DEV_BRANCH 分支..."
  git checkout $DEV_BRANCH
  
  # 将 main 分支的发版提交同步到 dev 分支
  echo "正在同步发版提交到 $DEV_BRANCH 分支..."
  git merge $MAIN_BRANCH --ff-only
  
  if [ $? -eq 0 ]; then
    echo "✅ 发版完成！$DEV_BRANCH 和 $MAIN_BRANCH 分支保持完全同步"
    echo "📝 提示：可以直接在 $DEV_BRANCH 分支上继续开发"
    
    # 推送同步后的 dev 分支到远程
    if [ "$SKIP_PUSH" == false ]; then
      echo "正在推送 $DEV_BRANCH 分支到远程..."
      git push $REMOTE $DEV_BRANCH
    fi
  else
    echo "⚠️  同步发版提交失败，请手动检查分支状态"
  fi
else
  echo "继续在 $MAIN_BRANCH 分支上工作..."
fi

echo "发布完成! 版本: $VERSION 已经推送到远程仓库"

# 询问是否要构建此版本
BUILD_VERSION="y"
read -p "是否要构建此版本? (y/n) [y]: " USER_BUILD_VERSION
BUILD_VERSION=${USER_BUILD_VERSION:-$BUILD_VERSION}  # 如果用户没有输入，则使用默认值
if [ "$BUILD_VERSION" == "y" ]; then
  echo "正在启动构建脚本..."
  
  # 询问是否需要部署
  DEPLOY_VERSION="y"
  read -p "是否同时部署此版本? (y/n) [y]: " USER_DEPLOY_VERSION
  DEPLOY_VERSION=${USER_DEPLOY_VERSION:-$DEPLOY_VERSION}  # 如果用户没有输入，则使用默认值
  if [ "$DEPLOY_VERSION" == "y" ]; then
    echo "正在启动部署脚本..."
    # 执行完整构建和部署
    ./deploy.sh --prod
  else
    # 仅构建不部署
    ./deploy.sh --skip-deploy
    echo "构建完成! 如需部署此版本，请执行 ./deploy.sh 脚本"
  fi
else
  echo "跳过构建操作。如需构建和部署，请运行 ./deploy.sh 脚本"
fi

echo "如需单独构建项目，请运行 ./deploy.sh --skip-deploy 脚本" 
