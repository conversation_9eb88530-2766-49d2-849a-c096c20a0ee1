---
inclusion: fileMatch
fileMatchPattern: "web/apps/lookah-admin/**/*"
---
# Vue3管理系统开发标准

## 技术栈要求
- **框架**: Vue3 + Composition API
- **UI组件**: Ant Design Vue (通过Vben5框架)
- **样式**: Tailwind CSS
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **表单验证**: Zod

## 组件开发规范
- **单一职责**: 每个组件只负责一个功能
- **Composition API**: 优先使用Composition API
- **Props验证**: 使用TypeScript接口定义Props
- **响应式数据**: 合理使用`ref`和`reactive`

## 文件命名规范【管理系统例外】
- **组件文件**: 使用kebab-case命名（如`inbound-order-modal.vue`）
- **页面文件**: 使用kebab-case命名（如`product-detail.vue`）
- **说明**: 与前台商城系统不同，管理系统使用kebab-case命名以保持一致性

## 状态管理规范
- **命名约定**: 以`use`开头(`useUserStore`, `useProductStore`)
- **状态响应式**: 使用`ref`和`reactive`包装状态
- **计算属性**: 复杂逻辑使用`computed`
- **Actions**: 异步操作和状态修改
- **模块化**: 按功能域拆分store

## 路由管理规范
- **懒加载**: 使用动态导入进行路由懒加载
- **路由守卫**: 实现权限控制和认证检查
- **元数据**: 设置页面标题、权限等元信息
- **权限控制**: 路由级、组件级、按钮级权限控制

## 表单处理规范
- **表单Hook**: 使用`useVbenForm`创建表单实例
- **Schema配置**: 通过schema数组定义表单结构和验证规则
- **Zod验证**: 使用Zod库进行表单验证，支持复杂验证逻辑
- **组件类型**: 支持Input、Select、RadioGroup、ApiSelect等多种组件
- **表单API**: 通过formApi进行表单操作（setValues、validate、resetForm等）
- **依赖关系**: 支持字段间的依赖关系和动态验证
- **国际化**: 验证规则支持国际化配置

## API交互规范
- **请求拦截器**: 添加认证token、请求ID等
- **响应拦截器**: 统一错误处理、数据格式化
- **模块化**: 按功能模块组织API
- **类型安全**: 使用TypeScript定义请求和响应类型

### API路径规范【强制要求】
- **路径定义**: 前端API路径定义不需要手动添加`/api/admin`前缀
- **代理处理**: 代理配置会自动处理路径前缀转换
- **相对路径**: 使用相对路径如`/mall/brand`而不是`/api/admin/mall/brand`
- **避免重复**: 路径前缀重复会导致请求失败

### API响应处理规范【强制要求】
- **拦截器处理**: 响应数据已在全局拦截器中处理，无需二次处理
- **直接使用**: Store中直接使用`response`而不是`response.data`
- **分页数据**: 分页数据使用`response.records`和`response.total`
- **成功判断**: 成功判断使用`if (response)`而不是`if (response.data)`
- **避免二次解析**: 避免对响应数据进行二次解析

**❌ 错误示例** - 不要进行二次解构：
```typescript
// 错误：不要这样做，会导致data为undefined
const { data } = await searchAddressList(params)
const { rows: records = [], total = 0 } = data || {}
return { records, total }
```

**✅ 正确示例** - 直接返回响应：
```typescript
// 正确：直接返回API响应，拦截器已处理数据结构
return await searchAddressList(params)
```

**说明**: 由于全局响应拦截器已统一处理了API响应格式，前端代码无需再对响应数据进行解构或格式转换。错误的二次处理会导致数据结构异常，表格等组件无法正常渲染数据。

### API消息处理规范【强制要求】
- **WithMsg方法**: `postWithMsg`, `putWithMsg`, `deleteWithMsg` 会自动显示成功消息
- **避免重复**: 使用WithMsg方法的API调用后，**禁止**再手动调用 `message.success()` 
- **统一处理**: 全局拦截器已处理成功/失败消息显示，前端组件无需重复处理
- **错误处理**: 异常情况下拦截器会自动显示错误消息，catch块中无需再次显示

**❌ 错误示例** - 会导致重复消息：
```typescript
// 错误：使用WithMsg后又手动显示消息
await addAddress(memberId, data)
message.success('添加成功') // 与API自动消息重复
```

**✅ 正确示例** - 避免重复消息：
```typescript
// 正确：使用WithMsg方法，拦截器自动处理消息
await addAddress(memberId, data)
// 不需要手动调用 message.success()
```

**说明**: WithMsg方法设置了 `successMessageMode: 'message'`，全局拦截器会检测此配置并自动显示成功消息。前端组件中的手动消息调用会导致用户看到两次相同的成功提示。

### ID字段处理规范【强制要求】
- **雪花ID类型**: 所有ID字段必须声明为string类型，避免数值精度丢失
- **TypeScript接口**: 在定义接口时使用`id: string`
- **表单处理**: 表单中的ID字段使用字符串类型进行传输和存储
- **API调用**: 发送请求时确保ID参数为字符串格式

## 样式开发规范 
- **工具类优先**: 使用Tailwind工具类进行样式开发，避免自定义CSS，使用Tailwind工具类
- **组件样式**: Ant Design Vue组件的自定义样式
- **主题定制**: 通过CSS变量定制Ant Design Vue主题
- **SCSS使用**: 统一管理颜色、字体等设计token

## UI组件渲染规范

### Status字段统一渲染【强制要求】
针对业务实体的启用/禁用状态字段，必须使用统一的渲染方式以保持界面一致性。

#### 适用范围
以下实体的`status`字段必须使用`renderDict`渲染：
- 会员管理、产品管理、仓库管理、分类管理、属性管理、系列管理
- 部门管理、用户管理等系统管理模块
- 所有表示"启用/禁用"状态的`status`字段

#### 强制渲染方式
```typescript
{
  field: 'status',
  title: '状态', // 或使用国际化文本
  width: 100,
  slots: {
    default: ({ row }) => {
      return renderDict(row.status, DictEnum.SYS_NORMAL_DISABLE)
    },
  },
}
```

#### 必须导入的依赖
```typescript
import { renderDict } from '#/utils/render'
import { DictEnum } from '@vben/constants'
```

#### 禁止使用的方式
- ❌ 手动状态映射：`statusMap[row.status]`
- ❌ 条件判断：`row.status === '1' ? '启用' : '禁用'`
- ❌ 硬编码文本：直接写入"启用"、"禁用"等字符串

#### 例外情况
以下状态字段不适用此规则，使用专门的渲染函数：
- `orderStatus` - 订单状态
- `paymentStatus` - 支付状态  
- `shipmentStatus` - 发货状态
- `approvalStatus` - 审批状态

## 性能优化规范
- **路由懒加载**: 按路由进行代码分割
- **组件懒加载**: 大型组件的懒加载
- **虚拟滚动**: 大列表使用虚拟滚动
- **防抖节流**: 搜索、滚动等事件的优化

## 错误处理规范
- **全局异常**: 统一的全局异常处理
- **用户提示**: 友好的错误提示信息
- **错误上报**: 生产环境的错误上报

## 国际化规范【按需使用】
- **默认豁免**: lookah-admin应用默认不强制使用国际化，可直接使用中文文本
- **按需支持**: 特定模块或功能如有国际化需求，可选择性使用i18n
- **混合使用**: 允许同一组件中国际化文本与中文文本混用，但建议保持一致性
- **推荐场景**: 面向多地区用户的功能、用户界面文本、错误提示等建议使用国际化
- **豁免场景**: 纯内部管理功能、调试信息、开发者注释等可使用中文
