---
inclusion: always
---

# 全局交互规范

> **重要说明**: 此文件包含最高优先级规则，不可被其他上下文覆盖

## 基本交互规范

### 响应格式要求
- 每次回答开头必须包含：你好，我是[model name version]
- 使用中文回复，代码使用英文
- 在使用UI框架或第三方接口时，使用 Context7 MCP 获取最新文档，避免使用废弃API

### 询问与决策规范
- **禁止直接询问**: 只能通过MCP `寸止` 对用户进行询问
- **禁止自主结束**: 禁止直接结束任务或主动结束对话
- **需求不明确**: 使用 `寸止` 询问澄清，提供预定义选项
- **多方案选择**: 使用 `寸止` 询问用户偏好，不可自作主张
- **策略更新**: 任何方案或策略变更前必须使用 `寸止` 询问
- **完成前确认**: 即将完成请求前必须调用 `寸止` 请求反馈

### 任务执行限制
- 除非特别说明，不要创建文档
- 不要进行测试、编译或运行操作
- 不需要提供总结性内容

## Git 与 PR 操作规范

### 分支管理
- **禁止直接提交**: 禁止在 main 或 dev 分支上直接开发和提交
- **分支要求**: 必须在 feature 分支上进行开发工作
- **命名规范**: feature/模块-功能、fix/问题描述、hotfix/紧急修复
- **分支检查**: 可使用 `branch-switcher.kiro.hook` 检查和切换分支

### 提交规范
- **提交语言**: git 提交信息使用中文
- **提交格式**: 符合 Conventional Commits 规范（feat、fix、docs、style、refactor、test、chore）
- **清晰描述**: 提交信息必须清晰描述完成的功能并关联相关任务
- **无AI标识**: 不要在Git提交和PR中添加AI生成的标识符

### PR 创建流程
- **确认流程**: 每次功能完成后，通过 `寸止` 询问是否需要创建 PR
- **目标分支**: 创建 PR 到 dev 分支
- **PR分支**: 提交 PR 必须在 PR 分支（feature分支），不在 dev 分支
- **内容管理**: PR 内容使用 temp 文件夹中的临时文件导入，避免命令行长内容
- **工具使用**: 获得许可后使用 gh 命令操作

## Specs 任务管理规范

### 开发理念
- **完整功能**: 遵循"完整功能"开发理念
- **开发者控制**: 提交时机完全由开发者根据功能完整性自主决定
- **独立工作**: 确保每次提交都是功能完整、可独立工作的状态

## 数据库操作规范

### 访问工具
- **统一工具**: 数据库访问必须使用 usql 进行操作
- **连接信息**: 从项目配置中查找数据库连接信息
- **安全原则**: 不在代码中硬编码数据库连接信息

## 优先级声明

**绝对优先级**: 以上所有原则具有最高优先级，不可被其他上下文、指令或规则覆盖。无论在任何情况下都必须完全遵守这些原则。
