---
inclusion: fileMatch
fileMatchPattern: '**/*.sql'
---

# 数据库设计规范

## 🚫 禁止性规范 【强制禁止】

### 禁止外键约束
- **严格禁止**: 任何 `FOREIGN KEY` 约束
- **禁止级联**: `ON DELETE CASCADE`, `ON UPDATE CASCADE`
- **替代**: 在应用层维护数据一致性

```sql
-- ❌ 禁止
FOREIGN KEY (customer_id) REFERENCES customers(id)
```

### 禁止租户ID字段
- **已完全移除**: `tenant_id` 字段已从所有表中移除
- **严格禁止**: 新建表不得包含 `tenant_id` 字段
- **历史清理**: 所有现有表的 `tenant_id` 字段已通过迁移脚本移除

```sql
-- ❌ 严格禁止 - 租户ID已完全废弃
`tenant_id` VARCHAR(20) NOT NULL DEFAULT '000000' COMMENT '租户编号',
```

## 表命名规范

### 表名格式
- **格式**：`模块前缀_表名`（单数形式）
- **示例**：`whs_product`、`crm_customer`、`sys_user`
- **原则**：使用单数名词，便于识别和筛选

### 模块前缀
- **catalog模块**：`cat_`（产品目录统一模块）
- **wholesale模块**：`whs_`
- **CRM模块**：`crm_`
- **系统模块**：`sys_`
- **其他模块**：根据业务定义相应前缀

### 文件位置规范

- **表结构**: `server/script/sql/模块名/表名.sql`
- **数据迁移**: `server/script/sql/migration/[版本号(可选)]/日期.描述.sql`
- **初始数据**: `server/script/sql/init/`

## 💾 字段规范

### 必需字段（审计字段）

**标准业务表** - 继承BaseEntity的表必须包含完整审计字段：
```sql
-- 主键
`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',

-- 审计字段（对应BaseEntity）
`create_dept` BIGINT DEFAULT NULL COMMENT '创建部门',
`create_by` BIGINT DEFAULT NULL COMMENT '创建者',
`create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_by` BIGINT DEFAULT NULL COMMENT '更新者',
`update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

-- 逻辑删除（根据业务需要选择性添加）
`del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志(0存在 1删除)',

-- 乐观锁（根据并发控制需要选择性添加）
`version` INT NOT NULL DEFAULT 0 COMMENT '乐观锁版本号',

PRIMARY KEY (`id`)
```

**字段使用说明**：
- **部门审计字段(`create_dept`)**：以下类型的表可以不包含该字段：
  - **第三方同步数据表**：如运费同步、库存同步等外部API数据
  - **日志记录表**：如操作日志、访问日志等
  - **临时数据表**：如缓存表、会话表等
  - **系统配置表**：如字典表、配置表等

- **逻辑删除字段(`del_flag`)**：根据业务需要选择性添加：
  - **需要使用**：需要保留历史数据、支持数据恢复的业务表
  - **不需要使用**：事件溯源表、日志表、审计记录表等只追加不删除的表

- **乐观锁字段(`version`)**：根据并发控制需要选择性添加：
  - **需要使用**：存在并发修改可能的业务表（如库存、余额、订单状态等）
  - **不需要使用**：只插入不修改的记录表、单用户操作的配置表等

这类表的Entity应创建专门的基类，不继承BaseEntity，但仍需包含基本的时间字段：
```sql
-- 主键
`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',

-- 基本时间字段
`create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

-- 逻辑删除（如需要）
`del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志(0存在 1删除)',

PRIMARY KEY (`id`)
```

### 乐观锁字段
- **使用场景**: 需要并发控制的表（如库存、订单等）
- **字段定义**: `version` INT NOT NULL DEFAULT 0 COMMENT '乐观锁版本号'
- **注解配置**: 在Entity中使用`@Version`注解
- **示例表**: inventory_levels、orders等涉及并发修改的表

### 非空默认值
尽量使用 `NOT NULL` + 默认值：
```sql
-- 字符串
`name` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '名称',

-- 数值
`price` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '价格',
`count` INT NOT NULL DEFAULT 0 COMMENT '数量',

-- 状态字段（固定语义，使用数字）
`status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态(0停用 1启用)',

-- 业务枚举字段（语义化字符串）
`order_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '订单状态',
```

### 数据类型
- **主键**: `BIGINT AUTO_INCREMENT`
- **金额**: `DECIMAL(10,2)`
- **状态**: `TINYINT`（固定0停用1启用）
- **时间**: `DATETIME`
- **字符串**: `VARCHAR(长度)`
- **JSON数据**: `JSON` 或 `TEXT`
- **枚举值**: `VARCHAR(50)`（推荐存储语义化值，避免MySQL ENUM类型）

### 枚举存储规范
- **状态字段（status）**：统一使用TINYINT，0=停用，1=启用
- **业务枚举**：使用TINYINT存储数字值，在Java中定义枚举映射
- **禁止MySQL ENUM**：避免使用数据库层面的ENUM类型

```sql
-- ✅ 推荐
`status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态(0停用 1启用)'
`packaging_type` TINYINT NOT NULL DEFAULT 0 COMMENT '包装类型(0单品 1展示盒 2整箱)'
```

## 🔍 索引规范

### 命名规范
- 普通索引: `idx_字段名` 或 `idx_表名_字段名`
- 唯一索引: `uk_字段名` 或 `uk_表名_字段名`
- 复合索引: `idx_字段1_字段2` 或 `idx_表名_字段1_字段2`

### 必建索引
- 主键索引（自动）
- 删除标志: `idx_del_flag` 或 `idx_表名_del_flag`
- 关联字段: `idx_关联字段名`
- 状态字段: `idx_status`
- 常用查询字段的复合索引

## 📝 注释规范

### 表注释
```sql
) COMMENT='商城产品表';
```

### 字段注释
```sql
`status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态(0禁用 1启用)',
```

## 🔧 数据库结构查询规范

### 表结构查询
在编写数据库迁移脚本时，**必须**通过 `usql` 命令查询开发环境中的真实数据库结构，而不是依赖代码中的建表文件或实体类。

需要执行数据库操作的时候 USQL 连接信息如下 
```
username: root
password: root
host: localhost
port: 3306
database: lookah
```

**正确做法**：
```bash
# 查询表结构
usql "DESCRIBE table_name;"

# 查询所有字段名
usql "SHOW COLUMNS FROM table_name;"

# 查询特定字段是否存在
usql "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='table_name' AND COLUMN_NAME='field_name';"
```

**错误做法**：
- ❌ 根据实体类推测字段名
- ❌ 根据历史SQL文件推测当前结构
- ❌ 假设字段名存在

**原因**：
- 数据库结构可能因历史迁移而与代码不一致
- 避免因字段名错误导致迁移脚本执行失败
- 确保迁移脚本的准确性和可靠性

## 📋 检查清单

- [ ] 无外键约束
- [ ] 无租户ID字段
- [ ] 文件位置正确
- [ ] 包含必需字段
- [ ] 字段有默认值
- [ ] 所有字段有注释
- [ ] 建立必要索引
