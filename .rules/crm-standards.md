---
inclusion: fileMatch
fileMatchPattern: "server/lookah-business/lookah-crm/**/*,web/apps/lookah-admin/src/views/crm/**/*,web/apps/lookah-admin/src/api/crm/**/*"
---
# CRM模块开发标准

## 核心开发原则
- 所有CRM模块生成的文件及代码作者都是 **jf**
- 严格遵循现有CRM模块的代码风格和架构模式
- 每次生成前端文件必须进行语法校验，确保语法正确才可使用
- 修改文件、修复bug时不可改全局文件，只能修改该模块下的文件

## 后端开发规范

### Mapper文件规范【强制要求】
- **位置要求**：CRM的mapper.xml文件必须放在 `resources/mapper` 目录下
- **禁止行为**：不可在mapper目录下新建子文件夹
- **文件命名**：使用实体名+Mapper.xml格式，如 `CrmCustomerMapper.xml`

### 字典管理规范【强制要求】
- **后端字典存放**：创建或修改字典时，生成的后端字典必须存放在以下位置之一：
  - `constant/CrmConstants.java` 中定义常量
  - `enums` 目录下创建对应的枚举类
- **命名规范**：枚举类使用大驼峰命名，如 `CustomerStatusEnum`
- **常量命名**：使用全大写+下划线格式，如 `CRM_CUSTOMER_STATUS`

### 文档和SQL文件管理【强制要求】
- **存放位置**：每次生成的文档及SQL文件必须放在 `server/script/sql/crm/migration/{YYYY-MM-DD}` 文件夹下
- **日期格式**：使用当前日期，格式为 `YYYY-MM-DD`
- **SQL文件命名**：使用序号排序，格式为 `01.xxx.sql`、`02.xxx.sql`
- **示例**：
  ```
  server/script/sql/crm/migration/2025-08-04/
  ├── 01.create_customer_table.sql
  ├── 02.insert_customer_dict.sql
  └── 03.create_customer_menu.sql
  ```

### 雪花算法ID规范【强制要求】
- **适用范围**：生成字典及菜单SQL的ID必须为雪花算法ID
- **生成方式**：使用系统提供的雪花算法生成器
- **示例**：`1820123456789012345`

## 前端开发规范

### API文件组织规范【强制要求】
- **API位置**：CRM模块API必须放在 `api/crm` 文件夹下
- **新功能组织**：新菜单功能需要创建新的子文件夹
- **目录结构示例**：
  ```
  src/api/crm/
  ├── customer/
  │   ├── index.ts
  │   ├── model.d.ts
  │   └── follow-record.ts
  ├── order/
  │   ├── index.ts
  │   └── model.ts
  └── new-module/
      ├── index.ts
      └── model.d.ts
  ```

### API定义规范【强制要求】
- **枚举定义**：创建API时必须有 `enum Api` 用于定义API地址
- **导出规范**：导出API必须使用 `commonExport`
- **示例**：
  ```typescript
  enum Api {
    CUSTOMER_LIST = '/crm/customer/list',
    CUSTOMER_DETAIL = '/crm/customer/detail',
  }
  
  export const customerApi = commonExport(Api);
  ```

### 数据字典规范【强制要求】
- **统一位置**：CRM下创建的前端数据字典定义统一放在 `constants/index.ts` 下
- **禁止行为**：不可以在其他文件另建字典定义
- **命名规范**：使用 `CrmDictEnum` 枚举，如：
  ```typescript
  export enum CrmDictEnum {
    CRM_CUSTOMER_STATUS = 'crm_customer_status',
    CRM_CUSTOMER_TYPE = 'crm_customer_type',
  }
  ```

### 模块文件结构规范【强制要求】
- **参考标准**：生成新模块的前端文件目录结构参考 `crm/customer`
- **新模块组织**：当创建新模块时，前端要在 `crm` 下创建新的文件夹存放新模块文件
- **标准结构**：
  ```
  src/views/crm/new-module/
  ├── components/           # 组件目录（如需要）
  ├── data.tsx             # 表格、表单配置
  ├── index.vue            # 主页面
  ├── new-module-drawer.vue # 抽屉组件
  └── constants.ts         # 模块常量（如需要）
  ```

### 组件使用规范【强制要求】
- **表格组件**：生成新模块使用的表格组件必须为 `BasicTable`，不可使用其他table组件
- **配置位置**：表格、表单配置必须放在 `data.tsx` 下面，不可在vue文件下创建配置
- **UI组件规范**：生成的抽屉、对话框、列表、按钮必须严格参考 `crm/customer` 下的使用方式、样式
- **简约原则**：非必要不可使用多余的UI组件

### 代码风格规范【强制要求】
- **CRUD一致性**：新生成的增、删、改、查代码风格必须和 `crm/customer` 一致
- **Modal使用**：当需要在抽屉里再打开Modal时，必须按照以下方式生成：
  ```typescript
  const [Modal, modalApi] = useVbenModal({})
  ```
- **下拉框组件**：当使用需要可以实时新增字典的下拉框组件时，必须使用 `createDictAddDropdown`
- **参考示例**：可以参考 `crm/customer/data.tsx` 中的使用方式

### 代码质量规范【强制要求】
- **语法校验**：生成新文件时要校验浏览器警告，有警告需处理掉，不可忽略
- **错误处理**：所有异步操作必须有适当的错误处理
- **类型安全**：使用TypeScript严格模式，确保类型安全

## 开发流程规范

### 新模块开发流程
1. **后端开发**：
   - 创建实体类（放在domain目录下）
   - 创建Mapper接口和XML文件
   - 创建Service接口和实现
   - 创建Controller
   - 生成相关字典和枚举

2. **前端开发**：
   - 在 `api/crm` 下创建新模块API文件夹
   - 在 `views/crm` 下创建新模块页面文件夹
   - 参考 `customer` 模块创建标准文件结构
   - 更新 `constants/index.ts` 中的字典定义

3. **文档和SQL**：
   - 在当前日期的migration文件夹下创建相关SQL文件
   - 使用雪花算法ID生成字典和菜单数据
   - 按序号命名SQL文件

### 修改和维护流程
1. **Bug修复**：只修改对应模块下的文件，不可修改全局文件
2. **功能增强**：遵循现有代码风格和架构模式
3. **代码审查**：确保符合所有规范要求

## 详细技术规范

### 后端技术细节

#### 实体类规范
- **位置**：实体类必须直接放在 `domain` 目录下，不可放在 `domain/entity` 子目录
- **命名**：使用 `Crm` 前缀 + 业务名称，如 `CrmCustomer`、`CrmOrder`
- **注解**：使用标准的JPA注解和MyBatis-Plus注解
- **作者标注**：所有类文件的 `@author` 必须为 `jf`

#### Service层规范
- **接口命名**：使用 `I` 前缀，如 `ICrmCustomerService`
- **实现类命名**：使用 `Impl` 后缀，如 `CrmCustomerServiceImpl`
- **事务管理**：涉及数据修改的方法必须添加 `@Transactional` 注解

#### Controller层规范
- **路径映射**：使用 `/crm/{module}` 格式，如 `/crm/customer`
- **权限控制**：所有接口必须添加适当的权限注解
- **参数验证**：使用标准的Bean Validation注解

### 前端技术细节

#### Vue组件规范
- **组件命名**：使用kebab-case命名，如 `customer-drawer.vue`
- **Composition API**：必须使用Vue3的Composition API
- **Props定义**：使用TypeScript接口定义Props类型
- **响应式数据**：合理使用 `ref` 和 `reactive`

#### 表格配置规范
- **BasicTable配置**：必须在 `data.tsx` 中定义表格列配置
- **操作列**：统一使用 `ActionColumn` 组件
- **分页配置**：使用标准的分页配置
- **抽屉中的表格tooltip问题**：当抽屉中的表格 tooltip 被遮挡时，正确的解决方案是在 `useVbenDrawer` 配置中设置 `zIndex: 999` 来降低抽屉层级，而不是修改表格的 tooltipConfig
- **示例结构**：
  ```typescript
  export const columns: BasicColumn[] = [
    {
      title: '客户名称',
      dataIndex: 'customerName',
      width: 150,
      showOverflow: 'tooltip', // 启用 tooltip
    },
    // ... 其他列配置
  ];

  // 表格配置示例
  const gridOptions: VxeGridProps = {
    columns: followRecordColumns,
    height: '500',
    // 标准的 tooltip 配置
    tooltipConfig: {
      showAll: true,
      enterable: true,
      theme: 'dark',
      leaveDelay: 100,
    },
    // ... 其他配置
  };

  // 正确的抽屉配置（解决 tooltip 遮挡问题）
  const [BasicDrawer, drawerApi] = useVbenDrawer({
    zIndex: 999, // 关键：降低抽屉层级
    // ... 其他配置
  });
  ```

#### 表单配置规范
- **表单Schema**：在 `data.tsx` 中定义表单配置
- **验证规则**：使用Zod进行表单验证
- **字典组件**：使用 `createDictAddDropdown` 创建可新增字典的下拉框
- **示例结构**：
  ```typescript
  export const formSchema: FormSchema[] = [
    {
      field: 'customerName',
      label: '客户名称',
      component: 'Input',
      required: true,
    },
    // ... 其他表单项
  ];
  ```

#### 抽屉和Modal规范
- **抽屉命名**：使用 `{module}-drawer.vue` 格式
- **Modal使用**：在抽屉中使用Modal时，必须使用 `useVbenModal`
- **事件处理**：统一使用 `emit` 进行父子组件通信
- **z-index层级问题**：当抽屉中的表格 tooltip 被遮挡时，必须在 `useVbenDrawer` 配置中设置 `zIndex: 999` 来降低抽屉层级
- **示例**：
  ```vue
  <script setup lang="ts">
  // Modal 使用示例
  const [Modal, modalApi] = useVbenModal({
    title: '确认操作',
    content: '是否确认执行此操作？',
  });

  // 抽屉 z-index 配置示例（解决 tooltip 遮挡问题）
  const [BasicDrawer, drawerApi] = useVbenDrawer({
    zIndex: 999, // 降低抽屉层级，让 VXE Table tooltip 显示在上面
    async onClosed() {
      // 关闭时的处理逻辑
    },
    async onOpenChange(isOpen) {
      // 打开/关闭状态变化处理
    }
  });
  </script>
  ```

### 数据库设计规范

#### 表命名规范
- **前缀**：所有CRM相关表必须以 `crm_` 开头
- **命名风格**：使用下划线分隔的小写字母
- **示例**：`crm_customer`、`crm_order`、`crm_follow_record`

#### 字段规范
- **主键**：使用 `id` 作为主键，类型为 `bigint`，使用雪花算法生成
- **通用字段**：包含 `create_time`、`update_time`、`create_by`、`update_by`、`deleted`
- **状态字段**：使用 `status` 字段表示状态，类型为 `char(1)`
- **备注字段**：使用 `remark` 字段，类型为 `varchar(500)`

#### 索引规范
- **主键索引**：自动创建
- **业务索引**：根据查询需求创建合适的索引
- **外键索引**：关联字段必须创建索引

### 权限和安全规范

#### 接口权限
- **权限标识**：使用 `crm:{module}:{action}` 格式，如 `crm:customer:list`
- **角色控制**：根据用户角色控制数据访问范围
- **数据权限**：实现行级数据权限控制

#### 数据安全
- **敏感数据**：客户联系方式等敏感数据需要加密存储
- **操作日志**：重要操作必须记录操作日志
- **数据备份**：定期备份重要数据

## 测试规范

### 单元测试
- **覆盖率要求**：Service层代码覆盖率不低于80%
- **测试命名**：使用 `{ClassName}Test` 格式
- **Mock使用**：合理使用Mock对象进行单元测试

### 集成测试
- **API测试**：所有Controller接口必须有集成测试
- **数据库测试**：使用H2内存数据库进行测试
- **事务测试**：验证事务的正确性

### 前端测试
- **组件测试**：重要组件必须有单元测试
- **E2E测试**：关键业务流程必须有端到端测试
- **语法检查**：使用ESLint和TypeScript进行语法检查

## 部署和运维规范

### 环境配置
- **配置文件**：使用不同环境的配置文件
- **数据库连接**：使用连接池管理数据库连接
- **缓存配置**：合理使用Redis缓存

### 监控和日志
- **应用监控**：使用APM工具监控应用性能
- **日志规范**：使用统一的日志格式和级别
- **错误处理**：统一的异常处理和错误码

## 注意事项
- 所有规范都是【强制要求】，不可违反
- 开发过程中如有疑问，参考现有 `crm/customer` 模块的实现方式
- 保持代码的一致性和可维护性
- 定期检查和更新规范文档
- 新功能开发前必须先阅读本规范文档
- 代码提交前必须通过所有检查和测试

## 常见问题解决方案

### 抽屉中表格 tooltip 被遮挡问题
**问题描述**：在抽屉中使用 VXE Table 时，鼠标悬停在超长列内容上，tooltip 气泡不显示或被抽屉遮挡。

**错误做法**：
- ❌ 修改表格的 tooltipConfig 中的 zIndex
- ❌ 使用 CSS 强制修改 tooltip 的 z-index
- ❌ 使用 JavaScript 监听和修改 tooltip 样式

**正确做法**：
```typescript
// 在 useVbenDrawer 配置中设置 zIndex: 999
const [BasicDrawer, drawerApi] = useVbenDrawer({
  zIndex: 999, // 降低抽屉层级，让 tooltip 显示在上面
  // ... 其他配置
});
```

**原理说明**：
- VXE Table 的 tooltip 默认 z-index 为 1000
- 抽屉默认 z-index 为 2000
- 通过设置抽屉 zIndex: 999，使 tooltip 能够显示在抽屉之上
