---
inclusion: always
---

# API开发标准

## RESTful API设计原则
- 遵循REST API设计原则
- 使用HTTP动词表示操作：GET(查询)、POST(创建)、PUT(更新)、DELETE(删除)
- 统一响应格式：使用`R<T>`包装响应数据，比如 R.ok、R.field
- 分页查询返回`TableDataInfo<T>`对象

## 接口文档规范
- **SpringDoc**: 使用 javadoc 注释生成文档
- **简洁注释**: 如`商城产品`而不是`商城产品控制器`
- **禁用Swagger**: 不要使用Swagger注解
- **参数说明**: 重要参数添加javadoc注释

## 权限控制
- **后台接口**: 必须添加`@SaCheckPermission`注解
- **权限格式**: `模块:功能:操作`（如`mall:product:list`）
- **前台接口**: 根据业务需要添加登录检查

## 数据传输对象规范
### VO (View Object) 设计
- 用于API响应的数据传输
- 使用MapStructPlus的工具类进行字段映射
- 敏感字段使用`@Sensitive`注解脱敏
- 命名规范：以业务含义命名，如`MallProductVo`

### ID字段类型规范【强制要求】
- **后端统一使用Long**: 所有ID字段在Entity和VO中都使用Long类型，与数据库保持一致
- **自动序列化处理**: 项目配置了`BigNumberSerializer`，自动将Long类型ID序列化为字符串返回给前端
- **前端字符串处理**: 前端接收到ID后统一作为字符串使用，避免JavaScript精度丢失
- **示例**: `private Long id;` 在后端，前端接收为字符串格式

### BO (Business Object) 设计
- 用于业务逻辑处理的数据对象
- 使用Bean Validation注解进行参数验证
- 命名规范：以业务操作命名，如`MallProductBo`
- **前台BO验证消息必须国际化**，后台管理BO可使用中文



## API路由约定
- **配置驱动**: API路由前缀通过 `config/modules.yml` 配置文件统一管理
- **自动前缀**: 各模块的路径前缀由配置自动分配，不在代码中硬编码
- **模块化管理**: 每个业务模块在配置中定义独立的prefix路径
- **一致性**: 确保前后台模块的路由前缀与配置文件保持一致
- **版本控制**: 支持通过Header或路径参数进行版本控制
