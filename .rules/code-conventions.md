---
inclusion: always
---

# 代码规范与最佳实践

## 通用代码规范
- 使用一致的命名约定
- 保持代码简洁和可读性
- 添加必要的注释，但避免过度注释
- 遵循单一职责原则
- 优先使用组合而非继承

## 语言规范
- **注释语言**: 所有代码注释（包括JavaDoc、行内注释）使用中文
- **日志语言**: 所有日志消息使用中文
- **变量命名**: 使用英文命名，遵循驼峰命名法
- **方法命名**: 使用英文命名，遵循驼峰命名法

## 翻译文本质量规范【强制要求】
- **专业性**: 使用准确的行业术语和专业词汇，避免生硬直译
- **简洁性**: 文本表达简洁明了，避免冗余和重复，符合目标语言习惯
- **地道性**: 翻译结果应符合目标语言的表达习惯和文化背景
- **一致性**: 同一概念在整个系统中保持统一的翻译，建立术语库
- **用户友好**: 面向用户的文本使用通俗易懂的语言，避免技术行话
- **错误处理**: 错误消息和提示文本要清晰指明问题和解决方案
- **上下文适配**: 根据使用场景调整语气和措辞（正式/非正式）
- **符合规范**: 遵循各语言地区的标点符号、数字格式等本地化规范

### 翻译质量检查要点
- **术语一致**: 核心业务术语在多语言间保持准确对应
- **语法正确**: 确保翻译文本语法正确，无拼写错误
- **文化适应**: 考虑目标市场的文化差异和使用习惯
- **长度控制**: 注意UI界面中文本长度限制，避免布局问题

## 命名规范
### 控制器命名规范
- **前后台区分**: 后台控制器使用通用名称，前台控制器加`Front`后缀
- **模块前缀**: 根据需要使用模块前缀避免跨模块冲突
- **Bean名称冲突**: 优先通过类名解决，避免使用注解指定Bean名称
- **一致性原则**: 同一模块内的命名风格保持一致

### 文件命名规范
- **Java类**: 使用PascalCase（大驼峰）
- **接口**: 以I开头，如IUserService
- **实现类**: 以Impl结尾，如UserServiceImpl
- **枚举**: 使用PascalCase，如PackagingType（注意：禁止创建status相关枚举）
- **常量**: 使用UPPER_SNAKE_CASE，如MAX_RETRY_COUNT

## 版本控制规范
- **提交信息**: 使用中文，符合Conventional Commits规范
- **提交前缀**: feat(新功能)、fix(修复)、docs(文档)、style(格式)、refactor(重构)、test(测试)、chore(构建)
- **分支策略**: 功能分支合并到dev分支，再合并到main分支
- **PR要求**: 代码审查通过后才能合并

## 安全规范
- 不在代码中硬编码敏感信息
- 使用环境变量管理配置
- 输入验证和输出编码
- 定期更新依赖包

## 性能优化原则
- 避免不必要的数据库查询
- 合理使用缓存
- 优化前端资源加载
- 监控关键性能指标

## 测试策略
- 单元测试覆盖核心业务逻辑
- 集成测试验证模块间交互
- E2E测试覆盖关键用户流程
- 定期进行性能测试

## 文档维护
- 保持API文档与代码同步
- 更新README和技术文档
- 记录重要的架构决策
- 维护变更日志
