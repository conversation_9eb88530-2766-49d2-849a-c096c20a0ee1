---
inclusion: always
---

# Lookah技术栈

## 后端技术栈
- **框架**: Spring Boot 3.4.7
- **Java版本**: Java 17
- **数据库**: MySQL + MyBatis Plus 3.5.11
- **缓存**: Redis + Redisson 3.50.0
- **认证授权**: Sa-Token 1.44.0
- **对象映射**: MapStruct Plus 1.4.6
- **API文档**: SpringDoc OpenAPI 2.8.3
- **任务调度**: SnailJob 1.3.0
- **分布式锁**: Lock4j 2.2.7
- **多数据源**: Dynamic DataSource 4.3.1

## 前端技术栈
### Mall商城 (C端)
- **框架**: Nuxt3 (SSR/ISR)
- **UI组件**: PrimeVue
- **样式**: Tailwind CSS
- **状态管理**: Pinia
- **国际化**: @nuxtjs/i18n
- **构建工具**: Vite

### Admin管理后台 & Wholesale批发系统
- **框架**: Vue3 + Composition API
- **UI组件**: Ant Design Vue (通过Vben5框架)
- **样式**: Tailwind CSS
- **状态管理**: Pinia
- **表单验证**: Zod
- **HTTP客户端**: Axios
- **构建工具**: Vite

## 开发工具链
- **包管理**: TurboRepo (前端) + Maven (后端)
- **代码规范**: ESLint + Prettier + Stylelint
- **版本控制**: Git + GitHub
- **构建部署**: Docker + Docker Compose
- **API测试**: SpringDoc + OpenAPI

## 数据存储
- **关系数据库**: MySQL 8.0+
- **缓存数据库**: Redis 7.0+
- **文件存储**: 本地文件系统 / 云存储

## 运维监控
- **容器化**: Docker
- **编排部署**: Docker Compose
- **反向代理**: Nginx
- **任务调度**: SnailJob
- **日志管理**: 基于Spring Boot日志框架
