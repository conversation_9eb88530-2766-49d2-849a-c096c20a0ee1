---
inclusion: always
---

# Lookah项目结构

## 整体架构
```
lookah/
├── web/                    # 前端工作空间 (Turborepo)
│   ├── apps/
│   │   ├── lookah-mall/    # 商城前端 (Nuxt3)
│   │   ├── lookah-admin/   # 管理后台 (Vue3)
│   │   └── lookah-wholesale/ # 批发系统 (Vue3)
│   ├── packages/           # 共享包
│   └── internal/           # 内部工具包
├── server/                 # 后端服务 (Maven多模块)
│   ├── lookah-app/         # 应用启动模块
│   ├── lookah-common/      # 通用组件模块
│   ├── lookah-business/    # 业务域模块
│   ├── lookah-platform/    # 平台功能模块
│   └── lookah-extend/      # 扩展模块
└── deploy/                 # 部署配置
```

## 后端模块结构
### 包命名规范
- 基础包名: `com.imhuso.lookah`
- 模块包结构: `{module}.{layer}.{domain}`
- 示例: `com.imhuso.lookah.mall.controller.product`

### 分层架构
```
src/main/java/com/imhuso/lookah/{module}/
├── controller/             # 控制器层
│   ├── admin/             # 后台管理接口
│   └── front/             # 前台用户接口
├── service/               # 服务层
│   └── impl/              # 服务实现
├── mapper/                # 数据访问层
├── domain/                # 领域对象
│   ├── entity/            # 实体类
│   ├── vo/                # 视图对象
│   └── bo/                # 业务对象
├── config/                # 配置类
├── enums/                 # 枚举类
├── constants/             # 常量类
└── utils/                 # 工具类
```

## 前端项目结构
### Mall商城 (Nuxt3)
```
apps/lookah-mall/
├── components/            # 组件
│   ├── product/          # 商品相关
│   ├── cart/             # 购物车
│   ├── order/            # 订单
│   ├── auth/             # 认证
│   └── common/           # 通用组件
├── pages/                # 页面路由
├── layouts/              # 布局
├── stores/               # 状态管理
├── composables/          # 组合式API
├── utils/                # 工具函数
├── types/                # 类型定义
└── i18n/                 # 国际化
```

### Admin/Wholesale (Vue3)
```
apps/lookah-{admin|wholesale}/
├── src/
│   ├── components/       # 组件
│   ├── pages/            # 页面
│   ├── layouts/          # 布局
│   ├── stores/           # 状态管理
│   ├── api/              # API接口
│   ├── utils/            # 工具函数
│   ├── types/            # 类型定义
│   └── styles/           # 样式文件
```

## 命名约定
### 后端命名
- **实体类**: 数据库表名，如 `MallProduct`
- **VO类**: 业务含义，如 `MallProductVo`
- **BO类**: 业务操作，如 `MallProductBo`
- **Service**: `I{Domain}Service` / `{Domain}ServiceImpl`
- **Controller**: 后台使用通用名称，前台加`Front`后缀
  - 后台: `ProductController`
  - 前台: `ProductFrontController`
- **Mapper**: `{Domain}Mapper`

### 前端命名
- **组件**: PascalCase (`UserProfile.vue`)
- **页面**: kebab-case (`user-profile.vue`)
- **工具函数**: camelCase (`formatPrice.ts`)
- **类型定义**: PascalCase (`UserProfile.d.ts`)
- **Store**: `use{Domain}Store`

## 导入规范
### 后端导入顺序
1. Java标准库
2. 第三方库
3. Spring框架
4. 项目内部包

### 前端导入顺序
1. Vue/Nuxt相关
2. 第三方库
3. 项目内部模块
4. 类型定义

## 文件组织原则
- 按功能域组织，而非技术层
- 相关文件就近放置
- 避免过深的目录嵌套
- 使用一致的命名约定

## API路由约定
- 后台管理: `/api/admin/{module}/{resource}`
- 前台接口: `/api/{module}/{resource}`
- 版本控制: 通过Header或路径参数

## 配置文件组织
- 环境配置: `application-{env}.yml`
- 功能配置: 按模块拆分配置文件
- 敏感配置: 使用环境变量或配置中心
