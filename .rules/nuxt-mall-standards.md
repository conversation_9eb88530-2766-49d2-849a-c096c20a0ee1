---
inclusion: fileMatch
fileMatchPattern: "web/apps/lookah-mall/**/*"
---
# Mall商城前端开发标准

## 技术栈要求
- **框架**: Nuxt3 (SSR/ISR优先)
- **UI组件**: PrimeVue
- **样式**: Tailwind CSS
- **状态管理**: Pinia
- **国际化**: @nuxtjs/i18n

## 国际化规范【强制要求】
- **必须使用**: 所有用户界面文本必须使用i18n翻译函数
- **禁止硬编码**: 禁止在组件中直接写入文本内容
- **模板中**: 使用`$t('key')`获取文本
- **script setup**: 使用`const { t } = useI18n()`然后`t('key')`
- **动态文本**: 使用插值`t('key', { param: value })`

## 组件开发规范
- **单一职责**: 每个组件只负责一个功能
- **Props验证**: 使用TypeScript接口定义Props
- **事件命名**: 使用动词前缀(`@update:value`, `@submit`)
- **自动导入**: 组件路径`components/auth/LoginForm.vue`自动导入为`AuthLoginForm`

## 表单处理规范
- **Form组件**: 统一使用PrimeVue的Form组件
- **Zod验证**: 使用zod库定义验证规则和schema
- **类型安全**: 基于zod schema自动推导TypeScript类型
- **验证消息**: 所有验证错误消息必须使用i18n
- **组件要求**: InputText配合FloatLabel，Button用于提交，Message显示错误

## 样式开发规范
- **工具类优先**: 避免自定义CSS，使用Tailwind工具类
- **组件样式**: 复杂组件使用`@apply`指令
- **响应式设计**: 移动端优先(`sm:`, `md:`, `lg:`)

## SEO优化规范
- **标题格式**: `${商品名} | ${分类} | Lookah`
- **首页标题**: `Lookah - Premium Quality Products`
- **描述优化**: 包含商品名称、价格、特性等关键信息
- **Open Graph**: 设置社交媒体分享信息
- **结构化数据**: 使用Schema.org标准

## 性能优化
- **SSR优先**: 商品列表、详情页使用SSR
- **SPA模式**: 用户交互频繁的页面使用SPA
- **懒加载**: 使用`defineAsyncComponent`懒加载组件
- **图片优化**: WebP格式，懒加载，响应式尺寸

## API交互
- 使用`useFetch`进行数据获取
- 设置合适的key用于缓存
- 提供默认值和错误处理
- 错误消息使用i18n，用户可理解

### ID字段处理规范【强制要求】
- **雪花ID类型**: 所有ID字段必须使用string类型声明和处理
- **路由参数**: 路由中的ID参数接收和传递时保持字符串格式
- **API请求**: 发送API请求时确保ID参数为字符串类型
- **类型定义**: TypeScript接口中ID字段使用string类型
