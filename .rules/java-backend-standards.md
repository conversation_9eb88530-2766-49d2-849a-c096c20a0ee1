---
inclusion: fileMatch
fileMatchPattern: "server/**/*.java"
---
# Java后端开发标准

## 核心开发原则
- 直接更新代码，不留TODO，完成任务
- 理解代码后再修改，保持原有结构和逻辑
- 代码拆分、封装、模块化，避免单文件过大
- 除登录接口外，所有接口需身份认证和权限控制
- 后台管理系统可按需使用国际化；若无跨语言需求，可豁免 i18n 校验

## 包结构规范
### 实体类组织规范【强制要求】
- **实体类位置**：实体类必须直接放在模块的 `domain` 目录下，**禁止**放在 `domain/entity` 子目录中
- **包命名**：`com.imhuso.{module}.core.domain.{EntityName}`
- **文件命名**：实体类文件名与类名一致，使用大驼峰命名
- **示例结构**：
```
src/main/java/com/imhuso/mall/core/domain/
├── MallMember.java          # 正确：直接放在domain下
├── MallProduct.java         # 正确：直接放在domain下
└── vo/                      # VO对象子目录
    ├── front/               # 前台VO（有前台功能的模块）
    └── admin/               # 后台VO
```

- **仅后台模块示例**：
```
src/main/java/com/imhuso/wholesale/core/domain/
├── WhsProduct.java          # 正确：直接放在domain下
├── WhsOrder.java            # 正确：直接放在domain下
└── vo/                      # VO对象子目录
    └── admin/               # 仅后台VO（无前台功能）
```

- **错误示例**：
```
src/main/java/com/imhuso/mall/core/domain/
├── entity/                  # 错误：不应该有entity子目录
│   ├── MallMember.java
│   └── MallProduct.java
```

### 模块包结构规范
- **业务模块**：`com.imhuso.{module}.core.domain`
  - mall模块：`com.imhuso.mall.core.domain`（有前台+后台）
  - wholesale模块：`com.imhuso.wholesale.core.domain`（仅后台）
  - crm模块：`com.imhuso.crm.core.domain`（仅后台）
  - catalog模块：`com.imhuso.catalog.core.domain`（产品目录共享）
- **平台模块**：`com.imhuso.{module}.domain`
  - system模块：`com.imhuso.system.domain`（仅后台）
  - admin模块：`com.imhuso.admin.domain`（仅后台）

### 模块功能分类
- **双端模块**：同时提供前台和后台功能
  - mall模块：商城前台 + 后台管理
  - 包结构：`vo/front/` + `vo/admin/`
- **单端模块**：仅提供后台管理功能
  - wholesale模块：批发后台管理
  - crm模块：客户关系管理后台
  - system模块：系统管理后台
  - admin模块：管理员后台
  - 包结构：仅 `vo/admin/`

## 代码质量规范
- 遵循阿里巴巴Java开发规范，使用lombok注解
- 使用MapStructPlus的`@AutoMapper`注解进行对象转换
- 控制器采用SpringDoc + javadoc注释，简洁明了
- **前台接口必须使用i18n国际化**: `MessageUtils.message()`，后台管理系统可豁免
- 分页查询返回`TableDataInfo`对象
- 禁止硬编码，善用hutool工具类

## 业务常量规范
- 状态统一使用`BusinessConstants`中的常量

### 状态字段规范【强制要求】
- **通用status字段统一规范**：数据库表中的通用`status`字段（表示启用/禁用状态）统一使用`BusinessConstants.NORMAL`(1)和`BusinessConstants.DISABLE`(0)
- **适用范围限制**：此规范仅适用于纯粹的`status`字段，不适用于其他语义的字段（如`isDefault`、`isActive`、`flagType`等）
- **业务特定状态允许自定义**：对于业务特定的状态字段（如订单状态、审批状态等），可以创建对应的枚举类
- **实体类映射**：Entity中通用status字段使用String类型，值为"0"或"1"
- **业务逻辑**：通用状态判断使用BusinessConstants常量
- **其他标识字段**：对于其他标识字段（如`isDefault`使用"Y"/"N"），可按业务需求自定义，不强制使用BusinessConstants

```java
// ✅ 正确做法
if (BusinessConstants.NORMAL.equals(entity.getStatus())) {
    // 启用状态的业务逻辑
}

// ❌ 错误做法 - 禁止自定义状态枚举
public enum ProductStatus {
    ACTIVE, INACTIVE  // 禁止这样做
}
```

## 安全规范
- 敏感字段使用`@Sensitive`注解脱敏
- 实体类必须继承BaseEntity获得审计字段

## MyBatis Plus使用规范
- **优先使用Wrapper**: 简单查询使用MyBatis Plus的LambdaQueryWrapper，复杂连表查询使用XML映射
- **BaseMapperPlus**: Mapper接口继承BaseMapperPlus<Entity, Vo>，获得更强大的映射功能
- **避免废弃API**: 如`deleteBatchIds`改用`deleteByIds`
- **分页查询**: 使用`Page<T>`对象
- **条件构造**: 使用`LambdaQueryWrapper`提高类型安全
- **XML映射**: 复杂JOIN查询、聚合查询等使用XML映射文件
- **查询规则**: 单表查询用Wrapper，连表查询用XML

### 废弃方法处理规范【强制要求】
- **外部API**: 第三方库中`@Deprecated`方法必须立即替换为新API
- **业务代码**: 项目内部`@Deprecated`方法必须重构并删除旧代码
- **禁止提交**: 任何包含废弃方法调用的代码不得提交
- **常见示例**:
```java
// ❌ 废弃 → ✅ 新方法
mapper.deleteBatchIds() → mapper.deleteByIds()
wrapper.eq("name", value) → wrapper.eq(Entity::getName, value)
```



## 异常处理规范
- **统一异常**: 使用`ServiceException`抛出业务异常
- **国际化消息**: 异常消息使用`MessageUtils.message()`
- **模块异常**: 优先使用模块自定义异常类

## 缓存使用规范
- **缓存注解**: 使用`@Cacheable`、`@CacheEvict`等注解
- **缓存常量**: 格式为`cacheNames#ttl#maxIdleTime#maxSize`
- **模块化管理**: 如`MallCacheNames`类管理商城缓存常量

## 日志记录规范
- **操作日志**: admin模块的重要操作添加`@Log`注解
- **异常日志**: 使用合适的日志级别记录异常
- **性能日志**: 记录关键操作的执行时间

## 枚举规范
### 基本规范
- 枚举在模块的`enums`包下
- **国际化接口**：根据实际使用场景选择性实现`EnumTranslatableInterface`接口
  - **需要实现**：面向用户展示的枚举值（如订单状态、商品状态等）
  - **不需要实现**：内部状态管理的枚举值（如记录状态、操作类型等）
- **统一设计模式**: 参考Wholesale模块的标准做法，保持代码简洁一致

### 标准枚举模式【推荐】
```java
@Getter
public enum PackagingType implements EnumTranslatableInterface {
    /**
     * 单品
     */
    INDIVIDUAL(0),

    /**
     * 展示盒
     */
    DISPLAY(1),

    /**
     * 整箱
     */
    CASE(2);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 构造函数
     */
    PackagingType(int value) {
        this.value = value;
    }
}
```

### 设计要点
- **数据库存储**: 使用TINYINT存储数字值（0,1,2），性能最优
- **Java枚举**: 实体类中使用枚举类型，类型安全
- **自动转换**: MyBatis Plus自动处理枚举与数字的转换
- **国际化支持**: 实现`EnumTranslatableInterface`接口提供翻译功能
- **简洁设计**: 只需value字段和构造函数，保持代码简化

## 类命名规范
### Controller命名
- **后台管理**: 直接使用功能名称（如`ProductController`）
- **前台接口**: 添加`Front`后缀（如`ProductFrontController`）
- **避免冲突**: 跨模块时使用模块前缀

### Service命名
- **接口**: 以`I`开头（如`IProductService`）
- **实现类**: 以`Impl`结尾（如`ProductServiceImpl`）
- **核心服务**: 添加`Core`后缀（如`ProductCoreService`）

### Entity命名
- **实体类**: 使用模块前缀+业务名称（如`WhsProduct`、`WhsCategory`）
- **VO类**: 使用模块前缀+业务名称+`Vo`后缀（如`WhsProductVo`）
- **BO类**: 使用模块前缀+业务名称+`Bo`后缀（如`WhsProductBo`）
- **目录结构**: 有前后台的模块按admin/front分目录，纯服务模块不分目录
- **Bean名称冲突**: 优先通过类名区分，避免使用注解指定Bean名称

## 跨模块查询规范【新增】
### 用户查询服务
- **公共服务**: 各业务模块需要查询系统用户时，使用`lookah-business-common-core`中的`IBusinessUserQueryService`
- **避免模块依赖**: 业务模块不应直接依赖system模块，通过公共服务间接查询
- **统一接口**: 所有业务模块使用相同的用户查询接口，确保数据一致性

### 实现要点
- **BaseMapperPlus**: 继承BaseMapperPlus<BusinessSysUser, BusinessUserOptionVo>
- **LambdaQueryWrapper**: 使用`Wrappers.lambdaQuery()`或`new LambdaQueryWrapper<>()`，禁用`lambdaQueryWrapper()`
- **自动映射**: 使用@AutoMapper注解实现Entity到VO的自动转换
- **命名规范**: 公共实体使用Business前缀，如BusinessSysUser

## ID字段处理规范【强制要求】
- **VO中的ID字段**: 使用Long类型，与数据库保持一致
- **自动处理**: 项目配置了`BigNumberSerializer`，自动处理JavaScript精度丢失问题
- **前端处理**: 前端接收到ID后统一转换为字符串使用
- **示例**:
```java
// VO类中的正确定义
public class MallProductVo {
    private Long id;  // 使用Long类型
    private Long categoryId;  // 所有ID字段都使用Long
}

// Entity中同样使用Long
public class MallProduct extends BaseEntity {
    private Long id;  // 数据库层使用Long
}
