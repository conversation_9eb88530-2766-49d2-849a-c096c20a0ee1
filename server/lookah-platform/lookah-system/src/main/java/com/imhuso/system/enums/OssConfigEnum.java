package com.imhuso.system.enums;

import lombok.Getter;

/**
 * OSS配置相关枚举
 *
 * <AUTHOR>
 */
public class OssConfigEnum {

    /**
     * 桶权限类型
     */
    @Getter
    public enum AccessPolicy {
        PRIVATE("0", "私有"),
        PUBLIC("1", "公有"),
        CUSTOM("2", "自定义");

        private final String code;
        private final String desc;

        AccessPolicy(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

    }

    /**
     * 默认配置类型
     */
    @Getter
    public enum DefaultConfigType {
        PUBLIC_DEFAULT("isPublicDefault", "公有默认配置"),
        PRIVATE_DEFAULT("isPrivateDefault", "私有默认配置");

        private final String fieldName;
        private final String description;

        DefaultConfigType(String fieldName, String description) {
            this.fieldName = fieldName;
            this.description = description;
        }

    }
}
