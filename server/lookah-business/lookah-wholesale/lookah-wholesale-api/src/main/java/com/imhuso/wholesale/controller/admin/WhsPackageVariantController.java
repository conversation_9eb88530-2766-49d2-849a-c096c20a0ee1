package com.imhuso.wholesale.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.idempotent.annotation.RepeatSubmit;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.bo.admin.WhsPackageVariantBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVariantVo;
import com.imhuso.wholesale.core.service.IWhsPackageVariantService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 包装变体
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/product/package")
@RequiredArgsConstructor
public class WhsPackageVariantController extends BaseController {
    private final IWhsPackageVariantService packageVariantService;

    /**
     * 创建包装变体
     */
    @SaCheckPermission("wholesale:product:add")
    @Log(title = "包装变体", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public R<WhsProductVariantVo> createPackageVariant(@Validated(AddGroup.class) @RequestBody WhsPackageVariantBo createPackageBo) {
        return R.ok(packageVariantService.createPackageVariant(createPackageBo));
    }

    /**
     * 更新包装变体
     */
    @SaCheckPermission("wholesale:product:edit")
    @Log(title = "包装变体", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public R<WhsProductVariantVo> updatePackageVariant(@Validated(EditGroup.class) @RequestBody WhsPackageVariantBo packageVariantBo) {
        return R.ok(packageVariantService.updatePackageVariant(packageVariantBo));
    }

    /**
     * 删除包装变体
     */
    @SaCheckPermission("wholesale:product:remove")
    @Log(title = "包装变体", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Void> deletePackageVariant(@NotNull(message = "变体ID不能为空") @PathVariable Long id) {
        return toAjax(packageVariantService.deletePackageVariant(id));
    }
}
