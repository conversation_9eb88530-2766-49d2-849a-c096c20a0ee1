package com.imhuso.wholesale.controller.front;

import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.wholesale.core.domain.vo.front.CountryVo;
import com.imhuso.wholesale.core.domain.vo.front.StateVo;
import com.imhuso.wholesale.core.service.IWhsCountryService;
import com.imhuso.wholesale.core.service.IWhsStateService;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 区域信息
 *
 * <AUTHOR>
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/region")
public class WhsRegionFrontController {

    private final IWhsCountryService countryService;
    private final IWhsStateService stateService;

    /**
     * 获取所有启用的国家列表
     */
    @GetMapping("/countries")
    public R<List<CountryVo>> getAllCountries() {
        List<CountryVo> countries = countryService.getAllEnabledCountries(CountryVo.class);
        return R.ok(countries);
    }

    /**
     * 获取州/省列表
     *
     * @param countryCode 国家代码
     * @return 州/省列表
     */
    @GetMapping("/states/{countryCode}")
    public R<List<StateVo>> getStatesByCountry(
        @NotBlank(message = "wholesale.region.country.code.not.blank") @PathVariable("countryCode") String countryCode) {
        CountryVo country = countryService.getCountryByCode(countryCode, CountryVo.class);
        if (country == null) {
            return R.failed(MessageUtils.message("wholesale.region.country.not.found"));
        }
        List<StateVo> states = stateService.getStatesByCountryId(country.getId(), StateVo.class);
        return R.ok(states);
    }
}
