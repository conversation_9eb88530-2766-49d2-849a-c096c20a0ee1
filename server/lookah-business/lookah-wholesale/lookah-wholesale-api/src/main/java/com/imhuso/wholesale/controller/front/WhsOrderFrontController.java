package com.imhuso.wholesale.controller.front;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.idempotent.annotation.RepeatSubmit;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.constant.MemberPermissionConstants;
import com.imhuso.wholesale.core.domain.bo.front.OrderBo;
import com.imhuso.wholesale.core.domain.bo.front.OrderListBo;
import com.imhuso.wholesale.core.domain.vo.front.OrderVo;
import com.imhuso.wholesale.core.service.IWhsOrderService;
import com.imhuso.wholesale.core.utils.MemberPermissionHelper;
import com.imhuso.wholesale.core.satoken.WholesaleLoginHelper;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order")
public class WhsOrderFrontController {

    private final IWhsOrderService orderService;
    private final MemberPermissionHelper permissionHelper;

    /**
     * 创建订单
     */
    @PostMapping
    @RepeatSubmit
    public R<Long> createOrder(@Valid @RequestBody OrderBo bo) {
        // 检查客户是否有下单权限
        Long memberId = WholesaleLoginHelper.getUserId();
        if (!permissionHelper.hasPermission(memberId, MemberPermissionConstants.ALLOW_ORDER)) {
            throw new ServiceException(MessageUtils.message("wholesale.member.permission.no.order"));
        }

        Long orderId = orderService.createOrder(bo);
        return R.ok(orderId);
    }

    /**
     * 订单列表
     */
    @GetMapping("/list")
    public TableDataInfo<OrderVo> list(OrderListBo bo, PageQuery pageQuery) {
        return orderService.queryMemberOrderPageList(bo, pageQuery);
    }

    /**
     * 订单详情
     */
    @GetMapping("/{orderId}")
    public R<OrderVo> getInfo(@PathVariable Long orderId) {
        OrderVo order = orderService.selectOrderById(orderId);
        return R.ok(order);
    }
}
