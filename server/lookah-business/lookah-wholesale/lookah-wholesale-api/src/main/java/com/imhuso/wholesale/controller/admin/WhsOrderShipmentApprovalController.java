package com.imhuso.wholesale.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderShipmentApprovalBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsSubmitApprovalRequestBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsApproveShipmentRequestBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentApprovalVo;
import com.imhuso.wholesale.core.service.IWhsOrderShipmentApprovalService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 发货审批
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/shipment-approval")
public class WhsOrderShipmentApprovalController extends BaseController {

    private final IWhsOrderShipmentApprovalService approvalService;

    /**
     * 发货审批列表
     */
    @SaCheckPermission("wholesale:shipment:approve")
    @GetMapping
    public TableDataInfo<WhsOrderShipmentApprovalVo> list(WhsOrderShipmentApprovalBo bo, PageQuery pageQuery) {
        return approvalService.queryPageList(bo, pageQuery);
    }

    /**
     * 发货审批详情
     */
    @SaCheckPermission("wholesale:shipment:approve")
    @GetMapping("/{id}")
    public R<WhsOrderShipmentApprovalVo> getInfo(@PathVariable @NotNull(message = "审核ID不能为空") Long id) {
        WhsOrderShipmentApprovalVo approval = approvalService.queryById(id);
        return R.ok(approval);
    }

    /**
     * 订单最新审核记录
     */
    @SaCheckPermission("wholesale:shipment:request")
    @GetMapping("/order/{orderId}")
    public R<WhsOrderShipmentApprovalVo> getLatestByOrderId(@PathVariable @NotNull(message = "订单ID不能为空") Long orderId) {
        WhsOrderShipmentApprovalVo approval = approvalService.queryLatestByOrderId(orderId);
        return R.ok(approval);
    }

    /**
     * 查询订单所有审批历史记录
     */
    @SaCheckPermission("wholesale:shipment:request")
    @GetMapping("/order/{orderId}/history")
    public R<List<WhsOrderShipmentApprovalVo>> getApprovalHistoryByOrderId(@PathVariable @NotNull(message = "订单ID不能为空") Long orderId) {
        List<WhsOrderShipmentApprovalVo> approvals = approvalService.queryHistoryByOrderId(orderId);
        return R.ok(approvals);
    }

    /**
     * 提交审核申请
     */
    @SaCheckPermission("wholesale:shipment:request")
    @Log(title = "发货审批", businessType = BusinessType.INSERT)
    @PostMapping("/request")
    public R<Long> submitApprovalRequest(@RequestBody @Valid WhsSubmitApprovalRequestBo bo) {
        Long approvalId = approvalService.submitApprovalRequest(bo.getOrderId(), bo.getApplicationReason());
        return R.ok(approvalId);
    }

    /**
     * 审核发货申请
     */
    @SaCheckPermission("wholesale:shipment:approve")
    @Log(title = "发货审批", businessType = BusinessType.UPDATE)
    @PostMapping("/approve/{id}")
    public R<Void> approveShipmentRequest(@PathVariable @NotNull(message = "审核ID不能为空") Long id,
                                          @RequestBody @Valid WhsApproveShipmentRequestBo bo) {
        try {
            boolean success = approvalService.approveShipmentRequest(id, bo.getApproved(), bo.getApprovalComment());
            return success ? R.ok() : R.failed("审核处理失败，请稍后重试");
        } catch (ServiceException e) {
            return R.failed(e.getMessage());
        }
    }

    /**
     * 撤销发货审批申请
     */
    @SaCheckPermission("wholesale:shipment:request")
    @Log(title = "发货审批", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{id}")
    public R<Void> cancelApprovalRequest(@PathVariable @NotNull(message = "审核ID不能为空") Long id) {
        try {
            boolean success = approvalService.cancelApprovalRequest(id);
            return success ? R.ok() : R.failed("撤销申请失败，请稍后重试");
        } catch (ServiceException e) {
            return R.failed(e.getMessage());
        }
    }

    /**
     * 检查订单是否需要发货审批
     */
    @SaCheckPermission("wholesale:shipment:request")
    @GetMapping("/check-required/{orderId}")
    public R<Boolean> checkApprovalRequired(@PathVariable @NotNull(message = "订单ID不能为空") Long orderId) {
        boolean required = approvalService.isShipmentApprovalRequired(orderId);
        return R.ok(required);
    }

    /**
     * 检查订单是否已通过发货审批
     */
    @SaCheckPermission("wholesale:shipment:request")
    @GetMapping("/check-approved/{orderId}")
    public R<Boolean> checkApprovalStatus(@PathVariable @NotNull(message = "订单ID不能为空") Long orderId) {
        boolean approved = approvalService.isShipmentApproved(orderId);
        return R.ok(approved);
    }

}
