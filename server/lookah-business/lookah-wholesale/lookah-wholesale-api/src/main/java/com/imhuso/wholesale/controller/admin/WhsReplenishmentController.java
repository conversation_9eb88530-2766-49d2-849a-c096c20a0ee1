package com.imhuso.wholesale.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.imhuso.common.excel.utils.ExcelUtil;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.bo.admin.WhsReplenishmentBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsReplenishmentVo;
import com.imhuso.wholesale.core.service.IWhsReplenishmentService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 补货单管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/replenishment")
@Slf4j
public class WhsReplenishmentController extends BaseController {

    private final IWhsReplenishmentService replenishmentService;

    /**
     * 导出补货单Excel
     */
    @Log(title = "补货单管理", businessType = BusinessType.EXPORT)
    @SaCheckPermission("wholesale:replenishment:export")
    @PostMapping("/export")
    public void export(WhsReplenishmentBo bo, HttpServletResponse response) {
        List<WhsReplenishmentVo> list = replenishmentService.exportReplenishmentList(bo);
        ExcelUtil.exportExcel(list, "补货单", WhsReplenishmentVo.class, response);
    }


}
