package com.imhuso.wholesale.controller.front;

import com.imhuso.common.core.domain.R;
import com.imhuso.wholesale.core.domain.vo.front.CheckoutSummaryVo;
import com.imhuso.wholesale.core.service.IWhsCheckoutService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 批发结账
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/checkout")
public class WhsCheckoutFrontController {

    private final IWhsCheckoutService checkoutService;

    /**
     * 获取结账摘要信息
     */
    @GetMapping("/summary")
    public R<CheckoutSummaryVo> getSummary() {
        CheckoutSummaryVo summary = checkoutService.getCheckoutSummary();
        log.info("获取结账摘要信息成功");
        return R.ok(summary);
    }
}
