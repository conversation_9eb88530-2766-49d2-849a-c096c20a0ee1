package com.imhuso.wholesale.controller.admin;

import java.util.List;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.imhuso.common.core.domain.R;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.WhsProductCategory;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductCategoryBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductCategoryVo;
import com.imhuso.wholesale.core.service.IWhsProductCategoryService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 产品分类
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/category")
public class WhsProductCategoryController extends BaseController {

    private final IWhsProductCategoryService categoryService;

    /**
     * 分类列表
     * 支持OR权限：基础查看权限 OR 需要选择产品分类的业务权限
     */
    @SaCheckPermission(value = {
        "wholesale:category:list",          // 基础产品分类列表权限
        "wholesale:order:manual:create"     // 手动创建订单需要选择产品分类
    }, mode = SaMode.OR)
    @GetMapping
    public R<List<WhsProductCategoryVo>> list(WhsProductCategoryBo bo) {
        List<WhsProductCategoryVo> list = categoryService.selectCategoryList(bo);
        return R.ok(list);
    }

    /**
     * 分类信息
     */
    @SaCheckPermission("wholesale:category:query")
    @GetMapping("/{categoryId}")
    public R<WhsProductCategoryVo> getInfo(@PathVariable Long categoryId) {
        return R.ok(categoryService.selectCategoryById(categoryId));
    }

    /**
     * 新增分类
     */
    @SaCheckPermission("wholesale:category:add")
    @Log(title = "产品分类", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@RequestBody WhsProductCategory category) {
        return toAjax(categoryService.insertCategory(category));
    }

    /**
     * 修改分类
     */
    @SaCheckPermission("wholesale:category:edit")
    @Log(title = "产品分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@RequestBody WhsProductCategoryBo bo) {
        return toAjax(categoryService.updateCategory(bo));
    }

    /**
     * 删除分类
     */
    @SaCheckPermission("wholesale:category:remove")
    @Log(title = "产品分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{categoryId}")
    public R<Void> remove(@PathVariable Long categoryId) {
        return toAjax(categoryService.deleteCategory(categoryId));
    }
}
