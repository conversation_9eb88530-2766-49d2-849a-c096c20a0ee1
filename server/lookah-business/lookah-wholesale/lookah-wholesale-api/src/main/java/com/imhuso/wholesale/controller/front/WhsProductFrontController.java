package com.imhuso.wholesale.controller.front;

import com.imhuso.common.core.domain.R;
import com.imhuso.wholesale.core.domain.vo.front.ProductSeriesHierarchyVo;
import com.imhuso.wholesale.core.service.IProductHierarchyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 批发产品前端接口
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RequestMapping("/product")
@RestController
public class WhsProductFrontController {

    private final IProductHierarchyService productHierarchyService;

    /**
     * 获取完整的产品层级结构
     * 产品系列 > 产品 > 变体 > 包装变体
     */
    @GetMapping("/list")
    public R<List<ProductSeriesHierarchyVo>> getProductList() {
        return R.ok(productHierarchyService.queryProductHierarchy());
    }
}
