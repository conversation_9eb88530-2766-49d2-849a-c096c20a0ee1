package com.imhuso.wholesale.controller.admin;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.bo.admin.WhsWarehouseBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsWarehouseVo;
import com.imhuso.wholesale.core.service.IWhsWarehouseService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 仓库管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/warehouse")
@Slf4j
public class WhsWarehouseController extends BaseController {

    private final IWhsWarehouseService warehouseService;

    /**
     * 仓库列表
     * 支持OR权限：基础查看权限 OR 需要选择仓库的业务权限
     */
    @SaCheckPermission(value = {
        "wholesale:warehouse:list",         // 基础仓库列表权限
        "wholesale:order:manual:create",    // 手动创建订单需要选择发货仓库
        "wholesale:stock:adjust"            // 库存调整需要选择仓库
    }, mode = SaMode.OR)
    @GetMapping
    public TableDataInfo<WhsWarehouseVo> list(WhsWarehouseBo bo, PageQuery pageQuery) {
        return warehouseService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取所有仓库列表（不分页）
     * 支持OR权限：基础查看权限 OR 需要选择仓库的业务权限
     */
    @SaCheckPermission(value = {
        "wholesale:warehouse:list",         // 基础仓库列表权限
        "wholesale:order:manual:create",    // 手动创建订单需要选择发货仓库
        "wholesale:stock:adjust"            // 库存调整需要选择仓库
    }, mode = SaMode.OR)
    @GetMapping("/all")
    public R<List<WhsWarehouseVo>> listAll(WhsWarehouseBo bo) {
        return R.ok(warehouseService.queryList(bo));
    }

    /**
     * 仓库详情
     */
    @SaCheckPermission("wholesale:warehouse:query")
    @GetMapping("/{id}")
    public R<WhsWarehouseVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(warehouseService.getById(id));
    }

    /**
     * 新增仓库
     */
    @SaCheckPermission("wholesale:warehouse:add")
    @Log(title = "仓库管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WhsWarehouseBo bo) {
        return toAjax(warehouseService.insertWarehouse(bo));
    }

    /**
     * 修改仓库
     */
    @SaCheckPermission("wholesale:warehouse:edit")
    @Log(title = "仓库管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WhsWarehouseBo bo) {
        return toAjax(warehouseService.updateWarehouse(bo));
    }

    /**
     * 删除仓库
     */
    @SaCheckPermission("wholesale:warehouse:remove")
    @Log(title = "仓库管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Void> remove(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(warehouseService.deleteWarehouseById(id));
    }

    /**
     * 设置默认仓库
     */
    @SaCheckPermission("wholesale:warehouse:edit")
    @Log(title = "仓库管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/default")
    public R<Void> setDefault(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(warehouseService.setDefaultWarehouse(id));
    }
}
