package com.imhuso.wholesale.config;

import cn.dev33.satoken.dao.SaTokenDao;
import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import com.imhuso.common.core.config.ModuleConfigProperties;
import com.imhuso.common.core.utils.ServletUtils;
import com.imhuso.common.security.config.ModuleSecurityAutoConfiguration;
import com.imhuso.wholesale.core.constant.WholesaleConstants;
import com.imhuso.wholesale.core.satoken.WholesaleLoginHelper;
import com.imhuso.wholesale.core.satoken.utils.StpWholesaleUtil;
import com.imhuso.wholesale.core.security.WholesaleClientValidator;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;

/**
 * 批发门户安全配置
 * 继承通用模块安全配置，获得智能模块排除能力
 * <p>
 * 处理批发前端相关的认证:
 * 1. 自动从模块配置中获取拦截路径
 * 2. 自动排除其他模块（如admin）的所有路径
 * 3. 排除Wholesale模块自身的公开接口
 * 4. 使用 Wholesale 专用的 StpLogic 进行会员认证
 *
 * <AUTHOR>
 */

@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(ModuleConfigProperties.class)
public class WholesaleSecurityConfig extends ModuleSecurityAutoConfiguration.BaseModuleSecurityConfig
        implements WebMvcConfigurer {

    public WholesaleSecurityConfig(ModuleConfigProperties moduleConfigProperties) {
        super(moduleConfigProperties);
    }

    @Override
    public void addInterceptors(@NonNull InterceptorRegistry registry) {
        log.info("初始化批发门户安全拦截器");

        // 自动获取拦截路径模式
        String[] interceptPatterns = getInterceptPatterns(getCurrentSecurityModule());
        if (interceptPatterns.length == 0) {
            log.warn("Wholesale模块未配置拦截路径，跳过安全拦截器注册");
            return;
        }

        // 自动获取排除路径（包括其他模块的路径和本模块的公开路径）
        String[] excludePatterns = getModuleExcludes(getCurrentSecurityModule());
        log.info("Wholesale模块拦截路径: {}", Arrays.toString(interceptPatterns));
        log.info("Wholesale模块排除路径: {}", Arrays.toString(excludePatterns));

        // 注册路由拦截器，自定义验证规则
        registry.addInterceptor(new SaInterceptor(handler -> {
            // 登录验证 -- 排除多个路径
            SaRouter
                    // 需要验证配置的路径
                    .match(interceptPatterns)
                    // 对未排除的路径进行检查
                    .check(() -> {
                        HttpServletRequest request = ServletUtils.getRequest();
                        if (request == null) {
                            // This case should ideally not happen in a web interceptor context.
                            log.error("HttpServletRequest is null within SaInterceptor. Skipping security validation.");
                            throw new IllegalStateException(
                                    "HttpServletRequest cannot be null in SaInterceptor context");
                        }
                        // 执行统一的安全验证
                        performSecurityValidation(request);
                    });
        })).addPathPatterns("/**")
                // 使用智能排除路径（自动排除其他模块）
                .excludePathPatterns(excludePatterns);
    }

    /**
     * 执行Wholesale模块的统一安全验证
     * 包括：登录验证、Token验证、客户端ID验证
     *
     * @param request HTTP请求对象
     * @throws NotLoginException 验证失败时抛出
     */
    private void performSecurityValidation(@NonNull HttpServletRequest request) {
        // 1. 检查用户是否已登录（Token验证）
        try {
            StpWholesaleUtil.checkLogin();
            log.debug("Wholesale用户登录验证通过，请求路径: {}", request.getRequestURI());
        } catch (NotLoginException e) {
            log.warn("Wholesale用户未登录，请求路径: {}, 错误信息: {}, 错误代码: {}", request.getRequestURI(), e.getMessage(), e.getCode());
            throw e;
        }

        // 2. 验证客户端ID与Token的一致性
        try {
            WholesaleClientValidator.validateConsistency(request);
            log.debug("Wholesale客户端ID验证通过，请求路径: {}", request.getRequestURI());
        } catch (NotLoginException e) {
            log.warn("Wholesale客户端ID验证失败，请求路径: {}, 错误信息: {}", request.getRequestURI(), e.getMessage());
            throw e;
        }

        // 3. 验证Token有效性（可选的额外验证）
        validateTokenIntegrity(request);

        log.debug("Wholesale安全验证全部通过，用户ID: {}, 请求路径: {}", WholesaleLoginHelper.getUserId(), request.getRequestURI());
    }

    /**
     * 验证Token完整性
     * 检查Token是否有效、是否过期等
     *
     * @param request HTTP请求对象
     */
    private void validateTokenIntegrity(@NonNull HttpServletRequest request) {
        try {
            // 检查Token是否有效
            String token = StpWholesaleUtil.getTokenValue();
            if (token == null || token.trim().isEmpty()) {
                log.warn("Wholesale Token为空，请求路径: {}", request.getRequestURI());
                throw NotLoginException.newInstance(StpWholesaleUtil.getLoginType(), NotLoginException.NOT_TOKEN,
                        "未提供有效Token", token);
            }

            // 检查Token是否过期
            long timeout = StpWholesaleUtil.getTokenTimeout();
            // SaTokenDao.NOT_VALUE_EXPIRE (-2L) 表示Token不存在或无效/已过期
            if (timeout == SaTokenDao.NOT_VALUE_EXPIRE) {
                log.warn("Wholesale Token已过期，请求路径: {}", request.getRequestURI());
                throw NotLoginException.newInstance(StpWholesaleUtil.getLoginType(), NotLoginException.INVALID_TOKEN,
                        "Token无效或已过期", token);
            }

            log.debug("Wholesale Token验证通过，剩余时间: {}秒", timeout);
        } catch (Exception e) {
            if (e instanceof NotLoginException) {
                throw e;
            }
            log.warn("Wholesale Token验证时发生异常，请求路径: {}", request.getRequestURI(), e);
            // 对于其他异常，不抛出，允许继续执行
        }
    }

    @Override
    protected String getCurrentSecurityModule() {
        return WholesaleConstants.Module.NAME;
    }
}
