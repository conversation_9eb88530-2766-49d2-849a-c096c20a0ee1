package com.imhuso.wholesale.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.bo.admin.WhsStockSummaryBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsInboundOrderVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsReplenishmentVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsStockSummaryVo;
import com.imhuso.wholesale.core.service.IWhsInboundOrderService;
import com.imhuso.wholesale.core.service.IWhsReplenishmentService;
import com.imhuso.wholesale.core.service.IWhsStockService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 库存汇总管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/stock/summary")
@Slf4j
public class WhsStockSummaryController extends BaseController {

    private final IWhsStockService stockService;
    private final IWhsInboundOrderService inboundOrderService;
    private final IWhsReplenishmentService replenishmentService;

    /**
     * 库存汇总列表
     */
    @SaCheckPermission("wholesale:stock:summary")
    @GetMapping
    public R<WhsStockSummaryVo.StockSummaryResponse> list(WhsStockSummaryBo bo) {
        WhsStockSummaryVo.StockSummaryResponse response = stockService.queryStockSummaryWithStatistics(bo);
        return R.ok(response);
    }

    /**
     * 根据产品ID查询相关的在途入库单
     */
    @SaCheckPermission("wholesale:stock:summary")
    @GetMapping("/inbound-orders/{productId}")
    public TableDataInfo<WhsInboundOrderVo> getInboundOrdersByProductId(
        @NotNull(message = "产品ID不能为空") @PathVariable Long productId,
        PageQuery pageQuery) {
        return inboundOrderService.queryInboundOrdersByProductId(productId, pageQuery);
    }

    /**
     * 根据变体ID查询相关的在途入库单
     */
    @SaCheckPermission("wholesale:stock:summary")
    @GetMapping("/inbound-orders/variant/{variantId}")
    public TableDataInfo<WhsInboundOrderVo> getInboundOrdersByVariantId(
        @NotNull(message = "变体ID不能为空") @PathVariable Long variantId,
        PageQuery pageQuery) {
        return inboundOrderService.queryInboundOrdersByVariantId(variantId, pageQuery);
    }

    /**
     * 查找补货候选商品
     * 查询洛杉矶仓缺货预警的单品，并检查芝加哥仓是否有对应箱装库存
     */
    @SaCheckPermission("wholesale:stock:summary")
    @GetMapping("/replenishment-candidates")
    public R<List<WhsReplenishmentVo>> getReplenishmentCandidates() {
        List<WhsReplenishmentVo> candidates = replenishmentService.findReplenishmentCandidates();
        return R.ok(candidates);
    }

    /**
     * 一键生成补货订单
     * 根据洛杉矶仓缺货预警的单品，从芝加哥仓选择对应箱装生成补货订单
     */
    @SaCheckPermission("wholesale:order:replenishment:generate")
    @Log(title = "生成补货订单", businessType = BusinessType.INSERT)
    @PostMapping("/generate-replenishment-order")
    public R<Long> generateReplenishmentOrder() {
        try {
            Long orderId = replenishmentService.generateReplenishmentOrder();
            if (orderId != null) {
                // 返回订单ID，前端可以显示具体的订单ID
                return R.ok("补货订单生成成功", orderId);
            } else {
                // 返回null表示没有需要补货的商品
                return R.ok("当前没有需要补货的商品", null);
            }
        } catch (Exception e) {
            log.error("生成补货订单失败", e);
            return R.failed("生成补货订单失败：" + e.getMessage());
        }
    }
}
