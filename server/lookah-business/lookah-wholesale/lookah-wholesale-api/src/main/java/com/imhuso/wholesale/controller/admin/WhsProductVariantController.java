package com.imhuso.wholesale.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.idempotent.annotation.RepeatSubmit;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductVariantBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductVariantListBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsGetSkusInfoBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsGetSkusInfoVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVariantListVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVariantVo;
import com.imhuso.wholesale.core.service.IWhsProductVariantService;
import com.imhuso.wholesale.core.service.IWhsReplenishmentService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商品变体
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/product/variant")
@RequiredArgsConstructor
public class WhsProductVariantController extends BaseController {
    private final IWhsProductVariantService variantService;
    private final IWhsReplenishmentService replenishmentService;

    /**
     * 变体列表
     * 支持OR权限：基础查看权限 OR 需要选择产品变体的业务权限
     */
    @SaCheckPermission(value = {
        "wholesale:product:query",          // 基础产品查询权限
        "wholesale:order:manual:create"     // 手动创建订单需要选择产品变体
    }, mode = SaMode.OR)
    @GetMapping
    public TableDataInfo<WhsProductVariantListVo> variantList(WhsProductVariantListBo bo, PageQuery pageQuery) {
        return variantService.queryVariantList(bo, pageQuery);
    }

    /**
     * 获取变体详情
     * 支持OR权限：基础查看权限 OR 需要查看变体详情的业务权限
     */
    @SaCheckPermission(value = {
        "wholesale:product:query",          // 基础产品查询权限
        "wholesale:order:manual:create",    // 手动创建订单需要查看变体详情
        "wholesale:order:shipment"          // 发货时需要查看变体详情
    }, mode = SaMode.OR)
    @GetMapping("/{id}")
    public R<WhsProductVariantVo> getInfo(@NotNull(message = "变体ID不能为空") @PathVariable Long id) {
        return R.ok(variantService.getVariant(id, true));
    }

    /**
     * 添加商品变体
     */
    @SaCheckPermission("wholesale:product:add")
    @Log(title = "商品变体", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public R<Void> addVariant(@Validated(AddGroup.class) @RequestBody WhsProductVariantBo variantBo) {
        return toAjax(variantService.insertVariant(variantBo));
    }

    /**
     * 修改商品变体
     */
    @SaCheckPermission("wholesale:product:edit")
    @Log(title = "商品变体", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> editVariant(@Validated(EditGroup.class) @RequestBody WhsProductVariantBo variantBo) {
        return toAjax(variantService.updateVariant(variantBo));
    }

    /**
     * 删除商品变体
     */
    @SaCheckPermission("wholesale:product:remove")
    @Log(title = "商品变体", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Void> removeVariant(@NotNull(message = "变体ID不能为空") @PathVariable Long id) {
        return toAjax(variantService.deleteById(id));
    }

    /**
     * 设置默认变体
     */
    @SaCheckPermission("wholesale:product:edit")
    @Log(title = "商品变体", businessType = BusinessType.UPDATE)
    @PutMapping("/default/{id}")
    public R<Void> setDefaultVariant(@NotNull(message = "变体ID不能为空") @PathVariable Long id) {
        return toAjax(variantService.setDefaultVariant(id));
    }

    /**
     * 批量获取SKU信息
     * 支持OR权限：基础查看权限 OR 需要获取SKU信息的业务权限
     */
    @SaCheckPermission(value = {
        "wholesale:product:query",          // 基础产品查询权限
        "wholesale:order:manual:create",    // 手动创建订单需要获取SKU信息
        "wholesale:order:shipment"          // 发货时需要获取SKU信息
    }, mode = SaMode.OR)
    @PostMapping("/skus")
    public R<List<WhsGetSkusInfoVo>> getSkuInfoBySkuCodes(@Validated @RequestBody WhsGetSkusInfoBo bo) {
        return R.ok(variantService.getSkuInfoBySkuCodes(bo.getSkuCodes()));
    }

    /**
     * 获取变体的有效预警库存
     */
    @SaCheckPermission("wholesale:product:query")
    @GetMapping("/{id}/effective-alert-stock")
    public R<Integer> getVariantEffectiveAlertStock(@NotNull(message = "变体ID不能为空") @PathVariable Long id) {
        // 先获取变体信息
        WhsProductVariantVo variant = variantService.getVariant(id, false);
        if (variant == null) {
            return R.failed("变体不存在");
        }

        // 获取有效预警库存
        Integer effectiveAlertStock = replenishmentService.getEffectiveAlertStockForVariant(id, variant.getAlertStock());
        return R.ok(effectiveAlertStock);
    }

    /**
     * 批量获取变体的有效预警库存
     */
    @SaCheckPermission("wholesale:product:query")
    @PostMapping("/batch-effective-alert-stock")
    public R<Map<String, Integer>> getBatchVariantEffectiveAlertStock(@Validated @RequestBody Map<String, List<Long>> request) {
        List<Long> variantIds = request.get("variantIds");
        if (variantIds == null || variantIds.isEmpty()) {
            return R.ok(new HashMap<>());
        }

        Map<String, Integer> result = new HashMap<>();

        // 批量获取变体信息
        List<WhsProductVariantVo> variants = variantService.getVariantsByIds(variantIds, true);

        for (WhsProductVariantVo variant : variants) {
            Integer effectiveAlertStock = replenishmentService.getEffectiveAlertStockForVariant(
                variant.getId(), variant.getAlertStock());
            result.put(String.valueOf(variant.getId()), effectiveAlertStock);
        }

        return R.ok(result);
    }
}
