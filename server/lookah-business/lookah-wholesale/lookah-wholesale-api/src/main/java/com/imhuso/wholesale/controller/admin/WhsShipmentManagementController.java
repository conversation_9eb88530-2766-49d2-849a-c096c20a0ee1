package com.imhuso.wholesale.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.bo.admin.WhsShipmentManagementBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentManagementVo;
import com.imhuso.wholesale.core.service.IWhsShipmentManagementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发货管理
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/shipment-management")
public class WhsShipmentManagementController extends BaseController {

    private final IWhsShipmentManagementService shipmentManagementService;

    /**
     * 待发货订单列表
     * 查询条件：
     * 1. 订单状态为处理中(1)
     * 2. 发货状态为待发货(0)或部分发货(2)
     */
    @SaCheckPermission("wholesale:shipment:list")
    @GetMapping("/orders")
    public TableDataInfo<WhsShipmentManagementVo> listPendingShipmentOrders(WhsShipmentManagementBo bo, PageQuery pageQuery) {
        return shipmentManagementService.queryPendingShipmentOrders(bo, pageQuery);
    }
}
