package com.imhuso.wholesale.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductAttributeBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductAttributeVo;
import com.imhuso.wholesale.core.service.IWhsProductAttributeService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品属性
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/productAttribute")
@Slf4j
public class WhsProductAttributeController extends BaseController {

    private final IWhsProductAttributeService attributeService;

    /**
     * 获取产品属性列表
     * 支持OR权限：基础查看权限 OR 需要选择产品属性的业务权限
     */
    @SaCheckPermission(value = {
        "wholesale:productAttribute:list",  // 基础产品属性列表权限
        "wholesale:order:manual:create"     // 手动创建订单需要选择产品属性
    }, mode = SaMode.OR)
    @GetMapping("/all")
    public R<List<WhsProductAttributeVo>> all() {
        return R.ok(attributeService.queryList(new WhsProductAttributeBo()));
    }

    /**
     * 属性列表
     */
    @SaCheckPermission("wholesale:productAttribute:list")
    @GetMapping
    public TableDataInfo<WhsProductAttributeVo> list(WhsProductAttributeBo bo, PageQuery pageQuery) {
        return attributeService.queryPage(bo, pageQuery);
    }

    /**
     * 属性详情
     */
    @SaCheckPermission("wholesale:productAttribute:query")
    @GetMapping("/{id}")
    public R<WhsProductAttributeVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(attributeService.getById(id));
    }

    /**
     * 新增属性
     */
    @SaCheckPermission("wholesale:productAttribute:add")
    @Log(title = "产品属性", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WhsProductAttributeBo bo) {
        return toAjax(attributeService.insertAttribute(bo));
    }

    /**
     * 修改属性
     */
    @SaCheckPermission("wholesale:productAttribute:edit")
    @Log(title = "产品属性", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WhsProductAttributeBo bo) {
        return toAjax(attributeService.updateAttribute(bo));
    }

    /**
     * 删除属性
     */
    @SaCheckPermission("wholesale:productAttribute:remove")
    @Log(title = "产品属性", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Void> remove(@NotNull(message = "请选择需要删除的属性") @PathVariable Long id) {
        return toAjax(attributeService.deleteAttributeById(id));
    }
}
