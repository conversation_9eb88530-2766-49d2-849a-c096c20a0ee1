package com.imhuso.wholesale.controller.front;

import com.imhuso.common.core.domain.R;
import com.imhuso.wholesale.core.domain.bo.front.StockNotifyCreateBo;
import com.imhuso.wholesale.core.service.IWhsStockNotifyService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 到货通知
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/notify")
public class WhsNotifyFrontController {

    private final IWhsStockNotifyService notifyService;

    /**
     * 创建到货通知
     */
    @PostMapping("/stock")
    public R<Void> createNotify(@Valid @RequestBody StockNotifyCreateBo bo) {
        notifyService.createNotify(bo);
        return R.ok();
    }
}
