package com.imhuso.wholesale.controller.front;

import com.imhuso.common.core.domain.R;
import com.imhuso.wholesale.core.domain.bo.front.MemberPasswordBo;
import com.imhuso.wholesale.core.domain.bo.front.MemberUpdateBo;
import com.imhuso.wholesale.core.domain.vo.front.MemberVo;
import com.imhuso.wholesale.core.service.IWhsMemberService;
import com.imhuso.wholesale.core.satoken.WholesaleLoginHelper;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 批发会员
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/member")
public class WhsMemberFrontController {

    private final IWhsMemberService memberService;

    /**
     * 获取个人资料
     */
    @GetMapping("/info")
    public R<MemberVo> profile() {
        return R.ok(memberService.getMemberInfo(WholesaleLoginHelper.getUserId()));
    }

    /**
     * 更新个人资料
     */
    @PutMapping("/profile")
    public R<Void> updateProfile(@Valid @RequestBody MemberUpdateBo bo) {
        memberService.updateProfile(bo);
        return R.ok();
    }

    /**
     * 修改密码
     * 密码至少6个字符，最多50个字符
     */
    @PutMapping("/password")
    public R<Void> updatePassword(@Valid @RequestBody MemberPasswordBo bo) {
        memberService.updatePassword(bo);
        return R.ok();
    }
}
