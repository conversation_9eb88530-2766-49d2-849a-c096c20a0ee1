package com.imhuso.wholesale.controller.admin;

import java.util.Map;
import java.util.List;

import com.imhuso.wholesale.core.service.IWhsMemberPermissionService;
import com.imhuso.business.core.service.IBusinessUserQueryService;
import com.imhuso.business.core.domain.vo.BusinessUserOptionVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.bo.admin.MemberPasswordResetBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsMemberBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsMemberVo;
import com.imhuso.wholesale.core.service.IWhsMemberAdminService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 会员管理
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/member")
public class WhsMemberController extends BaseController {

    private final IWhsMemberAdminService memberService;
    private final IWhsMemberPermissionService memberPermissionService;
    private final IBusinessUserQueryService businessUserQueryService;

    /**
     * 查询会员列表
     * 支持OR权限：基础查看权限 OR 需要选择客户的业务权限
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return 会员列表数据
     */
    @SaCheckPermission(value = {
        "wholesale:member:list",            // 基础会员列表权限
        "wholesale:order:manual:create"     // 手动创建订单需要选择客户
    }, mode = SaMode.OR)
    @GetMapping
    public TableDataInfo<WhsMemberVo> list(WhsMemberBo bo, PageQuery pageQuery) {
        return memberService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取会员详细信息
     * 支持OR权限：基础查看权限 OR 需要查看会员详情的业务权限
     *
     * @param memberId 会员ID
     * @return 会员详细信息
     */
    @SaCheckPermission(value = {
        "wholesale:member:query",           // 基础会员详情权限
        "wholesale:order:manual:create"     // 手动创建订单需要查看会员详情
    }, mode = SaMode.OR)
    @GetMapping("/{memberId}")
    public R<WhsMemberVo> getInfo(@PathVariable Long memberId) {
        return R.ok(memberService.getMemberDetail(memberId));
    }

    /**
     * 新增会员
     *
     * @param bo 会员信息
     * @return 操作结果
     */
    @SaCheckPermission("wholesale:member:add")
    @Log(title = "会员管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WhsMemberBo bo) {
        return toAjax(memberService.insertMember(bo));
    }

    /**
     * 修改会员
     *
     * @param bo 会员信息
     * @return 操作结果
     */
    @SaCheckPermission("wholesale:member:edit")
    @Log(title = "会员管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WhsMemberBo bo) {
        return toAjax(memberService.updateMember(bo));
    }

    /**
     * 删除会员
     *
     * @param memberId 会员ID
     * @return 操作结果
     */
    @SaCheckPermission("wholesale:member:remove")
    @Log(title = "会员管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{memberId}")
    public R<Void> remove(@PathVariable Long memberId) {
        return toAjax(memberService.deleteMember(memberId));
    }

    /**
     * 重置会员密码
     *
     * @param bo 密码重置信息
     * @return 操作结果
     */
    @SaCheckPermission("wholesale:member:edit")
    @Log(title = "会员管理", businessType = BusinessType.UPDATE)
    @PutMapping("/password")
    public R<Void> resetPassword(@Validated(EditGroup.class) @RequestBody MemberPasswordResetBo bo) {
        return toAjax(memberService.resetPassword(bo));
    }

    /**
     * 修改会员状态
     *
     * @param memberId 会员ID
     * @param status 状态值
     * @return 操作结果
     */
    @SaCheckPermission("wholesale:member:edit")
    @Log(title = "会员管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{memberId}/status/{status}")
    public R<Void> changeStatus(@PathVariable Long memberId, @PathVariable String status) {
        return toAjax(memberService.changeStatus(memberId, status));
    }

    /**
     * 获取会员权限
     *
     * @param memberId 会员ID
     * @return 会员权限映射
     */
    @SaCheckPermission("wholesale:member:permission:view")
    @GetMapping("/{memberId}/permissions")
    public R<Map<String, String>> getMemberPermissions(@PathVariable Long memberId) {
        return R.ok(memberService.getMemberPermissionMap(memberId));
    }

    /**
     * 设置会员权限
     *
     * @param memberId 会员ID
     * @param permissions 权限映射
     * @return 操作结果
     */
    @SaCheckPermission("wholesale:member:permission:edit")
    @Log(title = "会员管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{memberId}/permissions")
    public R<Void> setMemberPermissions(@PathVariable Long memberId, @RequestBody Map<String, String> permissions) {
        return toAjax(memberService.setMemberPermissions(memberId, permissions));
    }

    /**
     * 获取默认权限配置
     *
     * @return 默认权限映射
     */
    @SaCheckPermission("wholesale:member:permission:view")
    @GetMapping("/permission/defaults")
    public R<Map<String, String>> getDefaultPermissions() {
        return R.ok(memberPermissionService.getDefaultPermissions());
    }

    /**
     * 生成客户编码
     *
     * @return 生成的客户编码
     */
    @SaCheckPermission("wholesale:member:add")
    @GetMapping("/generate-code")
    public R<String> generateCustomerCode() {
        String customerCode = memberService.generateCustomerCode();
        return R.ok("客户编码生成成功", customerCode);
    }

    /**
     * 获取销售代表候选列表
     *
     * @return 销售代表列表
     */
    @SaCheckPermission(value = {
        "wholesale:member:add",
        "wholesale:member:edit"
    }, mode = SaMode.OR)
    @GetMapping("/salesperson/options")
    public R<List<BusinessUserOptionVo>> getSalespersonOptions() {
        List<BusinessUserOptionVo> salespersonList = businessUserQueryService.getSalespersonOptions();
        return R.ok(salespersonList);
    }

}
