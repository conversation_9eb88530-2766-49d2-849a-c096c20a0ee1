package com.imhuso.wholesale.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.idempotent.annotation.RepeatSubmit;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.bo.admin.WarehouseSelectionBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderShipmentBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsShipmentPlanGenerationBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanGenerationVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentStockResponseVo;
import com.imhuso.wholesale.core.service.IWhsOrderShipmentService;
import com.imhuso.wholesale.core.service.IWhsOrderShipmentStockService;
import com.imhuso.wholesale.core.service.IWhsShipmentPlanGenerationService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 订单发货管理
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order-shipment")
public class WhsOrderShipmentController extends BaseController {

    private final IWhsOrderShipmentService orderShipmentService;
    private final IWhsOrderShipmentStockService shipmentStockService;
    private final IWhsShipmentPlanGenerationService shipmentPlanGenerationService;

    /**
     * 获取/生成发货方案
     */
    @SaCheckPermission("wholesale:order:shipment")
    @Log(title = "订单发货管理", businessType = BusinessType.OTHER)
    @GetMapping("/plans/{orderId}")
    public R<List<WhsShipmentPlanVo>> getOrGenerateShipmentPlans(
            @PathVariable @NotNull(message = "订单ID不能为空") Long orderId,
            @RequestParam(required = false) Boolean forceRenew) {
        List<WhsShipmentPlanVo> plans = orderShipmentService.getOrGenerateShipmentPlans(orderId, forceRenew);
        return R.ok(plans);
    }

    /**
     * 查询发货库存信息
     * 获取实际发货变体在各仓库的有效可用库存，用于发货仓库分配决策
     * 注意：提供orderId将返回订单相关的所有产品库存，提供planId将返回方案相关的变体库存
     * 支持OR权限：基础查看权限 OR 发货权限
     */
    @SaCheckPermission(value = {
            "wholesale:order:view", // 基础订单查看权限
            "wholesale:order:shipment" // 发货权限（包含查看和执行）
    }, mode = SaMode.OR)
    @Log(title = "发货库存查询", businessType = BusinessType.OTHER)
    @GetMapping("/stocks")
    public R<WhsShipmentStockResponseVo> getShipmentStocks(@RequestParam(required = false) Long orderId,
            @RequestParam(required = false) Long planId) {

        // 参数校验，确保至少有一个不为空
        if (orderId == null && planId == null) {
            return R.failed("订单ID和发货计划ID不能同时为空");
        }

        WhsShipmentStockResponseVo stockResponse;

        // 根据提供的参数类型调用对应的服务方法
        if (planId != null) {
            // 如果提供了planId，优先使用planId查询
            stockResponse = shipmentStockService.getStocksByPlanId(planId);
        } else {
            return R.failed("暂时不支持根据订单ID查询库存");
        }

        return R.ok(stockResponse);
    }

    /**
     * 选择发货方案
     */
    @SaCheckPermission("wholesale:order:shipment")
    @Log(title = "订单发货管理", businessType = BusinessType.UPDATE)
    @PostMapping("/select-plan/{planId}")
    public R<Boolean> selectShipmentPlan(@PathVariable @NotNull(message = "方案ID不能为空") Long planId) {
        boolean result = orderShipmentService.selectShipmentPlan(planId);
        return R.ok(result);
    }

    /**
     * 准备自定义包装方案数据
     */
    @SaCheckPermission("wholesale:order:shipment")
    @Log(title = "自定义包装方案", businessType = BusinessType.OTHER)
    @GetMapping("/custom-plan/{orderId}")
    public R<WhsShipmentPlanGenerationVo> prepareCustomPackagePlan(
            @PathVariable @NotNull(message = "订单ID不能为空") Long orderId) {
        WhsShipmentPlanGenerationVo plan = shipmentPlanGenerationService.prepareOrderDataForPlan(orderId);
        return R.ok(plan);
    }

    /**
     * 提交自定义发货方案, 调用生成服务，保存方案方法
     */
    @SaCheckPermission("wholesale:order:shipment")
    @Log(title = "订单发货管理", businessType = BusinessType.INSERT)
    @PostMapping("/custom-plan")
    public R<Boolean> submitCustomShipmentPlan(@RequestBody @Valid WhsShipmentPlanGenerationBo planBo) {
        shipmentPlanGenerationService.submitCustomShipmentPlan(planBo);
        return R.ok(true);
    }

    /**
     * 预览发货仓库分配
     */
    @SaCheckPermission("wholesale:order:shipment")
    @Log(title = "订单发货管理", businessType = BusinessType.OTHER)
    @PostMapping("/preview-warehouse-assignment")
    public R<Boolean> previewWarehouseAssignment(@RequestBody @Valid WarehouseSelectionBo selectionBo) {
        Boolean result = orderShipmentService.previewWarehouseAssignment(selectionBo);
        return R.ok(result);
    }

    /**
     * 确认发货
     */
    @SaCheckPermission("wholesale:order:shipment")
    @Log(title = "订单发货管理", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm-shipment")
    @RepeatSubmit(interval = 30 * 1000, message = "请勿重复提交")
    public R<Boolean> confirmShipment(@RequestBody @Valid WhsOrderShipmentBo shipmentBo) {
        boolean result = orderShipmentService.confirmShipment(shipmentBo);
        return R.ok(result);
    }

    /**
     * 获取仓库签名服务支持状态
     */
    @SaCheckPermission("wholesale:order:shipment")
    @Log(title = "获取仓库签名服务支持状态", businessType = BusinessType.OTHER)
    @GetMapping("/warehouse-signature-support")
    public R<Map<String, Boolean>> getWarehouseSignatureSupport(@RequestParam String warehouseIds) {
        // 解析逗号分隔的ID字符串
        List<Long> warehouseIdList = new ArrayList<>();
        if (warehouseIds != null && !warehouseIds.trim().isEmpty()) {
            String[] idArray = warehouseIds.split(",");
            for (String idStr : idArray) {
                try {
                    warehouseIdList.add(Long.parseLong(idStr.trim()));
                } catch (NumberFormatException e) {
                    log.warn("无效的仓库ID格式: {}", idStr);
                }
            }
        }

        Map<String, Boolean> supportMap = orderShipmentService.getWarehouseSignatureSupport(warehouseIdList);
        return R.ok(supportMap);
    }
}
