package com.imhuso.wholesale.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.StrUtil;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.system.domain.vo.SysOssUploadVo;
import com.imhuso.system.domain.vo.SysOssVo;
import com.imhuso.system.service.ISysOssService;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderFileCreateBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderFileVo;
import com.imhuso.wholesale.core.service.IWhsOrderFileService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 批发文件管理控制器
 * 提供INVOICE文件的下载和删除功能，上传统一使用系统OSS接口
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/file")
public class WhsFileUploadController extends BaseController {

    private final ISysOssService ossService;
    private final IWhsOrderFileService orderFileService;


    /**
     * 上传INVOICE文件到私有存储桶
     * 支持单个或多个文件上传，统一使用此接口
     *
     * @param files 上传的文件数组
     * @return 上传结果列表
     */
    @SaCheckPermission("wholesale:order:manual:create")
    @PostMapping(value = "/invoice/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<List<SysOssUploadVo>> uploadInvoiceFiles(@RequestPart("files") MultipartFile[] files) {
        try {
            if (files == null || files.length == 0) {
                return R.failed("上传文件不能为空");
            }

            List<SysOssUploadVo> uploadResults = new ArrayList<>();

            for (MultipartFile file : files) {
                if (file.isEmpty()) {
                    continue;
                }

                // 验证文件类型（三重验证：MIME类型 + 文件扩展名 + 文件魔数）
                String contentType = file.getContentType();
                String originalFilename = file.getOriginalFilename();
                if (!isAllowedInvoiceFileType(contentType, originalFilename, file)) {
                    log.warn("跳过不支持的文件类型或文件名不匹配: contentType={}, fileName={}", 
                        contentType, maskFileName(originalFilename));
                    continue;
                }

                // 验证文件大小（最大50MB）
                long maxFileSize = 50 * 1024 * 1024; // 50MB
                if (file.getSize() > maxFileSize) {
                    log.warn("跳过超大文件，文件大小: {}MB, 最大允许: 50MB, fileName: {}",
                        file.getSize() / (1024 * 1024), maskFileName(originalFilename));
                    continue;
                }

                try {
                    // 使用私有存储桶配置上传文件
                    SysOssVo oss = ossService.upload(file, "private");

                    // 构建返回结果
                    SysOssUploadVo uploadVo = new SysOssUploadVo();
                    uploadVo.setUrl(oss.getUrl());
                    uploadVo.setFileName(oss.getOriginalName());
                    uploadVo.setOssId(oss.getOssId().toString());

                    uploadResults.add(uploadVo);
                    log.info("INVOICE文件上传成功: fileName={}, ossId={}, private=true", 
                        maskFileName(file.getOriginalFilename()), maskOssId(oss.getOssId()));
                } catch (Exception e) {
                    log.error("单个INVOICE文件上传失败: fileName={}", 
                        maskFileName(file.getOriginalFilename()), e);
                    // 继续处理其他文件，不因单个文件失败而中断
                }
            }

            if (uploadResults.isEmpty()) {
                return R.failed("没有文件上传成功，请检查文件格式是否正确");
            }

            log.info("批量INVOICE文件上传完成: 成功={}, 总计={}", uploadResults.size(), files.length);
            return R.ok("批量上传完成，成功 " + uploadResults.size() + " 个文件", uploadResults);

        } catch (Exception e) {
            log.error("批量INVOICE文件上传失败", e);
            return R.failed("批量INVOICE文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 验证INVOICE文件类型（双重验证：MIME类型 + 文件扩展名）
     * 提供给前端调用，用于在上传前验证文件类型
     * 注意：此接口仅进行基础验证，完整验证需要在文件上传时进行文件内容检查
     *
     * @param contentType 文件MIME类型
     * @param filename    文件名（含扩展名）
     * @return 验证结果
     */
    @SaCheckPermission("wholesale:order:manual:create")
    @PostMapping("/invoice/validate")
    public R<Boolean> validateInvoiceFile(@RequestParam("contentType") String contentType,
                                          @RequestParam("filename") String filename) {
        try {
            boolean isValid = isAllowedInvoiceFileType(contentType, filename);
            if (isValid) {
                return R.ok("文件类型有效", Boolean.TRUE);
            } else {
                return R.ok("不支持的文件类型或文件扩展名不匹配，仅支持PDF、图片、Excel、Word文件，且文件扩展名必须与内容类型匹配", Boolean.FALSE);
            }
        } catch (Exception e) {
            log.error("验证文件类型失败: {}", contentType, e);
            return R.failed("验证文件类型失败: " + e.getMessage());
        }
    }


    /**
     * 下载INVOICE文件（权限控制）
     *
     * @param ossId    文件OSS ID
     * @param response HTTP响应
     */
    @SaCheckPermission("wholesale:order:view")
    @GetMapping("/invoice/download/{ossId}")
    public void downloadInvoice(@PathVariable Long ossId, HttpServletResponse response) throws IOException {
        try {
            log.info("下载INVOICE文件: ossId={}", maskOssId(ossId));
            ossService.download(ossId, response);
        } catch (Exception e) {
            log.error("下载INVOICE文件失败: ossId={}", maskOssId(ossId), e);
            // 重置响应状态，避免部分内容已写入的问题
            if (!response.isCommitted()) {
                response.reset();
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.setContentType("application/json; charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"文件下载失败: " + e.getMessage() + "\"}");
            }
        }
    }

    /**
     * 删除INVOICE文件
     *
     * @param ossId 文件OSS ID
     */
    @SaCheckPermission("wholesale:order:edit")
    @DeleteMapping("/invoice/{ossId}")
    public R<Void> deleteInvoice(@PathVariable Long ossId) {
        try {
            boolean success = ossService.deleteWithValidByIds(List.of(ossId), true);
            log.info("删除INVOICE文件: ossId={}, success={}", ossId, success);
            return success ? R.ok("文件删除成功") : R.failed("删除文件失败");
        } catch (Exception e) {
            log.error("删除INVOICE文件失败: ossId={}", ossId, e);
            return R.failed("删除文件失败: " + e.getMessage());
        }
    }

    /**
     * 关联文件到订单
     *
     * @param orderId 订单ID
     * @param fileBo  文件信息
     */
    @SaCheckPermission("wholesale:order:edit")
    @PostMapping("/order/{orderId}/files")
    public R<Void> addOrderFile(@PathVariable Long orderId, @RequestBody WhsOrderFileCreateBo fileBo) {
        try {
            boolean success = orderFileService.addOrderFile(orderId, fileBo);
            log.info("关联文件到订单: orderId={}, ossId={}, success={}", orderId, fileBo.getOssId(), success);
            return success ? R.ok("文件关联成功") : R.failed("文件关联失败");
        } catch (Exception e) {
            log.error("关联文件到订单失败: orderId={}, ossId={}", orderId, fileBo.getOssId(), e);
            return R.failed("文件关联失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单的所有文件
     *
     * @param orderId 订单ID
     */
    @SaCheckPermission("wholesale:order:view")
    @GetMapping("/order/{orderId}/files")
    public R<List<WhsOrderFileVo>> getOrderFiles(@PathVariable Long orderId) {
        try {
            List<WhsOrderFileVo> files = orderFileService.getOrderFiles(orderId);
            // 填充文件URL
            enrichFileUrls(files);
            log.debug("获取订单文件: orderId={}, fileCount={}", orderId, files.size());
            return R.ok(files);
        } catch (Exception e) {
            log.error("获取订单文件失败: orderId={}", orderId, e);
            return R.failed("获取订单文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单的INVOICE文件
     *
     * @param orderId 订单ID
     */
    @SaCheckPermission("wholesale:order:view")
    @GetMapping("/order/{orderId}/invoice-files")
    public R<List<WhsOrderFileVo>> getOrderInvoiceFiles(@PathVariable Long orderId) {
        try {
            List<WhsOrderFileVo> files = orderFileService.getOrderInvoiceFiles(orderId);
            // 填充文件URL
            enrichFileUrls(files);
            log.debug("获取订单INVOICE文件: orderId={}, fileCount={}", orderId, files.size());
            return R.ok(files);
        } catch (Exception e) {
            log.error("获取订单INVOICE文件失败: orderId={}", orderId, e);
            return R.failed("获取订单INVOICE文件失败: " + e.getMessage());
        }
    }

    /**
     * 删除订单文件关联
     *
     * @param orderId 订单ID
     * @param fileId  文件关联ID
     */
    @SaCheckPermission("wholesale:order:edit")
    @DeleteMapping("/order/{orderId}/files/{fileId}")
    public R<Void> removeOrderFile(@PathVariable Long orderId, @PathVariable Long fileId) {
        try {
            boolean success = orderFileService.removeOrderFile(orderId, fileId);
            log.info("删除订单文件关联: orderId={}, fileId={}, success={}", orderId, fileId, success);
            return success ? R.ok("文件关联删除成功") : R.failed("文件关联删除失败");
        } catch (Exception e) {
            log.error("删除订单文件关联失败: orderId={}, fileId={}", orderId, fileId, e);
            return R.failed("文件关联删除失败: " + e.getMessage());
        }
    }


    /**
     * 检查是否为允许的INVOICE文件类型
     * 使用双重验证：MIME类型 + 文件扩展名，提高安全性
     */
    private boolean isAllowedInvoiceFileType(String contentType, String originalFilename) {
        if (StrUtil.isBlank(contentType) || StrUtil.isBlank(originalFilename)) {
            return false;
        }

        // 获取文件扩展名（转为小写）
        String extension = "";
        int dotIndex = originalFilename.lastIndexOf(".");
        if (dotIndex > 0 && dotIndex < originalFilename.length() - 1) {
            extension = originalFilename.substring(dotIndex + 1).toLowerCase();
        }

        // 双重验证：MIME类型和扩展名必须都匹配
        return isValidContentTypeAndExtension(contentType, extension);
    }
    
    /**
     * 检查是否为允许的INVOICE文件类型（包含文件内容验证）
     * 使用三重验证：MIME类型 + 文件扩展名 + 文件魔数，提高安全性
     */
    private boolean isAllowedInvoiceFileType(String contentType, String originalFilename, MultipartFile file) {
        // 先进行基础验证
        if (!isAllowedInvoiceFileType(contentType, originalFilename)) {
            return false;
        }
        
        // 进行文件魔数验证
        try {
            return isValidFileMagicNumber(file, contentType);
        } catch (Exception e) {
            log.warn("文件魔数验证失败: fileName={}, error={}", 
                maskFileName(originalFilename), e.getMessage());
            return false;
        }
    }

    /**
     * 验证MIME类型和文件扩展名的匹配性
     */
    private boolean isValidContentTypeAndExtension(String contentType, String extension) {
        // PDF文件
        if ("application/pdf".equals(contentType) && "pdf".equals(extension)) {
            return true;
        }

        // 图片文件
        if (contentType.startsWith("image/")) {
            return "jpg".equals(extension) || "jpeg".equals(extension) ||
                "png".equals(extension) || "gif".equals(extension) ||
                "bmp".equals(extension) || "webp".equals(extension);
        }

        // Excel文件
        if ("application/vnd.ms-excel".equals(contentType) && "xls".equals(extension)) {
            return true;
        }
        if ("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".equals(contentType) && "xlsx".equals(extension)) {
            return true;
        }

        // Word文件
        if ("application/msword".equals(contentType) && "doc".equals(extension)) {
            return true;
        }
        return "application/vnd.openxmlformats-officedocument.wordprocessingml.document".equals(contentType) && "docx".equals(extension);
    }

    /**
     * 填充文件URL
     */
    private void enrichFileUrls(List<WhsOrderFileVo> files) {
        if (files == null || files.isEmpty()) {
            return;
        }

        for (WhsOrderFileVo file : files) {
            try {
                if (file.getOssId() != null) {
                    SysOssVo ossVo = ossService.getById(file.getOssId());
                    if (ossVo != null && ossVo.getUrl() != null) {
                        file.setFileUrl(ossVo.getUrl());
                    }
                }
            } catch (Exception e) {
                log.warn("获取文件URL失败: ossId={}, error={}", file.getOssId(), e.getMessage());
            }
        }
    }

    /**
     * 脱敏文件名，防止敏感信息泄露
     * 保留文件扩展名，中间部分用*替换
     */
    private String maskFileName(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return "unknown";
        }
        
        // 如果文件名很短，只显示扩展名
        if (fileName.length() <= 8) {
            int dotIndex = fileName.lastIndexOf('.');
            if (dotIndex > 0) {
                return "***" + fileName.substring(dotIndex);
            }
            return "***";
        }
        
        // 保留前2个字符和扩展名，中间用*替换
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 2) {
            String prefix = fileName.substring(0, 2);
            String suffix = fileName.substring(dotIndex);
            return prefix + "***" + suffix;
        }
        
        return fileName.substring(0, 2) + "***";
    }
    
    /**
     * 脱敏OSS ID，防止敏感信息泄露
     * 只显示前4位和后4位，中间用*替换
     */
    private String maskOssId(Long ossId) {
        if (ossId == null) {
            return "unknown";
        }
        
        String idStr = ossId.toString();
        if (idStr.length() <= 8) {
            return "***" + idStr.substring(Math.max(0, idStr.length() - 2));
        }
        
        String prefix = idStr.substring(0, 4);
        String suffix = idStr.substring(idStr.length() - 4);
        return prefix + "***" + suffix;
    }
    
    /**
     * 验证文件魔数（Magic Number），确保文件内容与声明的MIME类型匹配
     */
    private boolean isValidFileMagicNumber(MultipartFile file, String contentType) throws IOException {
        if (file.isEmpty()) {
            return false;
        }
        
        // 读取文件前16个字节用于魔数检查
        byte[] header = new byte[16];
        try (var inputStream = file.getInputStream()) {
            int bytesRead = inputStream.read(header);
            if (bytesRead < 4) {
                log.warn("文件太小，无法进行魔数验证");
                return false;
            }
        }
        
        // 根据MIME类型验证对应的魔数
        switch (contentType) {
            case "application/pdf":
                return isPdfMagicNumber(header);
            case "image/png":
                return isPngMagicNumber(header);
            case "image/jpeg":
                return isJpegMagicNumber(header);
            case "image/gif":
                return isGifMagicNumber(header);
            case "application/vnd.ms-excel":
            case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
            case "application/msword":
            case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                return isOfficeFileMagicNumber(header, contentType);
            default:
                if (contentType.startsWith("image/")) {
                    return isImageMagicNumber(header, contentType);
                }
                // 对于其他类型，暂时通过验证（保守策略）
                log.info("未实现魔数验证的文件类型: {}", contentType);
                return true;
        }
    }
    
    /**
     * 验证PDF文件魔数 %PDF
     */
    private boolean isPdfMagicNumber(byte[] header) {
        return header.length >= 4 && 
               header[0] == 0x25 && header[1] == 0x50 && header[2] == 0x44 && header[3] == 0x46;
    }
    
    /**
     * 验证PNG文件魔数 89 50 4E 47
     */
    private boolean isPngMagicNumber(byte[] header) {
        return header.length >= 8 &&
               header[0] == (byte) 0x89 && header[1] == 0x50 && header[2] == 0x4E && header[3] == 0x47 &&
               header[4] == 0x0D && header[5] == 0x0A && header[6] == 0x1A && header[7] == 0x0A;
    }
    
    /**
     * 验证JPEG文件魔数 FF D8 FF
     */
    private boolean isJpegMagicNumber(byte[] header) {
        return header.length >= 3 &&
               header[0] == (byte) 0xFF && header[1] == (byte) 0xD8 && header[2] == (byte) 0xFF;
    }
    
    /**
     * 验证GIF文件魔数 GIF87a 或 GIF89a
     */
    private boolean isGifMagicNumber(byte[] header) {
        if (header.length < 6) return false;
        
        String headerStr = new String(header, 0, 6);
        return "GIF87a".equals(headerStr) || "GIF89a".equals(headerStr);
    }
    
    /**
     * 验证Office文件魔数
     */
    private boolean isOfficeFileMagicNumber(byte[] header, String contentType) {
        if (header.length < 4) return false;
        
        // 新版Office文件 (2007+) - ZIP格式魔数 PK 03 04
        if (header[0] == 0x50 && header[1] == 0x4B && header[2] == 0x03 && header[3] == 0x04) {
            return contentType.contains("openxmlformats");
        }
        
        // 旧版Office文件 (97-2003) - OLE2格式魔数 D0 CF 11 E0
        if (header.length >= 8 &&
            header[0] == (byte) 0xD0 && header[1] == (byte) 0xCF && 
            header[2] == 0x11 && header[3] == (byte) 0xE0) {
            return contentType.equals("application/vnd.ms-excel") || 
                   contentType.equals("application/msword");
        }
        
        return false;
    }
    
    /**
     * 验证其他图片格式魔数
     */
    private boolean isImageMagicNumber(byte[] header, String contentType) {
        if (header.length < 4) return false;
        
        // BMP: 42 4D
        if (contentType.equals("image/bmp") || contentType.equals("image/x-ms-bmp")) {
            return header[0] == 0x42 && header[1] == 0x4D;
        }
        
        // WebP: RIFF + WEBP
        if (contentType.equals("image/webp")) {
            return header.length >= 8 &&
                   header[0] == 0x52 && header[1] == 0x49 && header[2] == 0x46 && header[3] == 0x46;
        }
        
        return true; // 其他图片类型暂时通过
    }
}
