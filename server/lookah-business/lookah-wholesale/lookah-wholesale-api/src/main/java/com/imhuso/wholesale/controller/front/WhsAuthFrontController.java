package com.imhuso.wholesale.controller.front;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.ObjectUtil;
import com.imhuso.common.client.domain.ClientVo;
import com.imhuso.common.client.service.IClientService;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.core.utils.ServletUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.core.utils.ValidatorUtils;
import com.imhuso.common.json.utils.JsonUtils;
import com.imhuso.wholesale.core.domain.bo.front.WhsLoginBody;
import com.imhuso.wholesale.core.domain.vo.front.WhsLoginVo;
import com.imhuso.wholesale.core.satoken.WholesaleLoginHelper;
import com.imhuso.wholesale.core.service.IWhsAuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



/**
 * 批发商认证控制器
 *
 * <AUTHOR>
 */
@Slf4j
@SaIgnore
@RequiredArgsConstructor
@RestController
@RequestMapping("/auth")
public class WhsAuthFrontController {

    private final IWhsAuthService authService;
    private final IClientService clientService;

    /**
     * 批发商登录
     *
     * @param body 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public R<WhsLoginVo> login(@RequestBody String body) {
        WhsLoginBody loginBody = JsonUtils.parseObject(body, WhsLoginBody.class);
        ValidatorUtils.validate(loginBody);

        // 从请求头中提取clientId（如果请求体中没有）
        if (StringUtils.isBlank(loginBody.getClientId())) {
            String headerClientId = ServletUtils.getRequest().getHeader(WholesaleLoginHelper.CLIENT_ID);
            if (StringUtils.isNotBlank(headerClientId)) {
                loginBody.setClientId(headerClientId);
            }
        }

        // 设置默认的grantType为password（批发商登录）
        if (StringUtils.isBlank(loginBody.getGrantType())) {
            loginBody.setGrantType("password");
        }

        // 参考后台管理模块的校验方式
        // 授权类型和客户端id
        String clientId = loginBody.getClientId();
        String grantType = loginBody.getGrantType();
        ClientVo client = clientService.getClientConfig(clientId);

        // 查询不到 client 或 client 内不包含 grantType
        if (ObjectUtil.isNull(client) || !StringUtils.contains(client.getGrantType(), grantType)) {
            log.info("批发模块客户端id: {} 认证类型：{} 异常!.", clientId, grantType);
            return R.failed(MessageUtils.message("auth.grant.type.error"));
        } else if (!BusinessConstants.NORMAL.equals(client.getStatus())) {
            return R.failed(MessageUtils.message("auth.grant.type.blocked"));
        }

        // 登录
        WhsLoginVo loginVo = authService.login(loginBody, client);
        return R.ok(loginVo);
    }

    /**
     * 批发商退出登录
     */
    @PostMapping("/logout")
    public R<Void> logout() {
        authService.logout();
        return R.ok();
    }
}
