package com.imhuso.wholesale.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.WhsStock;
import com.imhuso.wholesale.core.domain.bo.admin.WhsStockAdjustBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsStockBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsStockLogVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsStockVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsVariantStockByWarehouseVo;
import com.imhuso.wholesale.core.enums.StockOperationType;
import com.imhuso.wholesale.core.mapper.WhsStockMapper;
import com.imhuso.wholesale.core.service.IWhsStockLogService;
import com.imhuso.wholesale.core.service.IWhsStockOperationService;
import com.imhuso.wholesale.core.service.IWhsStockService;
import com.imhuso.wholesale.core.service.IWhsStockSyncService;
import com.imhuso.wholesale.core.domain.vo.warehouse.StockSyncSummary;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 库存管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/stock")
@Slf4j
public class WhsStockController extends BaseController {

    private final IWhsStockOperationService stockOperationService;
    private final IWhsStockLogService stockLogService;
    private final WhsStockMapper stockMapper;
    private final IWhsStockService stockService;
    private final IWhsStockSyncService stockSyncService;

    /**
     * 库存列表
     * 支持OR权限：基础查看权限 OR 需要查看库存的业务权限
     */
    @SaCheckPermission(value = {
        "wholesale:stock:list",             // 基础库存列表权限
        "wholesale:stock:adjust",           // 库存调整需要查看当前库存状态
        "wholesale:order:manual:create"     // 手动创建订单时查看库存状态
    }, mode = SaMode.OR)
    @GetMapping
    public TableDataInfo<WhsStockVo> list(WhsStockBo bo, PageQuery pageQuery) {
        return stockService.queryPageList(bo, pageQuery);
    }

    /**
     * 调整库存数量
     */
    @Log(title = "库存管理", businessType = BusinessType.UPDATE)
    @SaCheckPermission("wholesale:stock:adjust")
    @PutMapping("/adjust")
    public R<Void> adjustStock(@Validated({EditGroup.class}) @RequestBody WhsStockAdjustBo bo) {
        // 获取并校验库存记录
        WhsStock stock = stockMapper.selectById(bo.getStockId());
        if (stock == null) {
            return R.failed("库存记录不存在");
        }

        // 直接调用updateStock方法进行库存调整
        int result = stockOperationService.updateStock(stock, StockOperationType.ADJUST, bo.getQuantity(),
            null, null, "后台库存调整");

        return result != 0 ? R.ok() : R.failed("库存调整失败");
    }

    /**
     * 库存操作日志列表
     */
    @SaCheckPermission("wholesale:stock:query")
    @GetMapping("{stockId}/logs")
    public TableDataInfo<WhsStockLogVo> getStockLogs(
        @NotNull(message = "库存ID不能为空") @PathVariable Long stockId,
        PageQuery pageQuery) {
        return stockLogService.getStockLogsPageByStockId(stockId, pageQuery);
    }

    /**
     * 同步所有库存（海外仓 + ERP）
     */
    @SaCheckPermission("wholesale:stock:batch:sync")
    @Log(title = "库存同步", businessType = BusinessType.OTHER)
    @PostMapping("/sync")
    public R<StockSyncSummary> syncAll() {
        try {
            log.info("管理员手动触发库存同步（海外仓 + ERP）");
            StockSyncSummary summary = stockSyncService.syncAllStocks();
            log.info("库存同步完成，海外仓成功: {}, ERP成功: {}, 总耗时: {}ms",
                    summary.getWarehouseSuccessCount(), summary.getErpSuccessCount(), summary.getTotalDuration());
            return R.ok(summary);
        } catch (Exception e) {
            String errorMessage = "库存同步失败: " + e.getMessage();
            log.error(errorMessage, e);
            return R.failed(errorMessage);
        }
    }



    /**
     * 获取变体在各仓库的库存分布
     * 支持OR权限：基础查看权限 OR 发货相关权限
     */
    @SaCheckPermission(value = {
        "wholesale:stock:query",            // 基础库存查询权限
        "wholesale:order:shipment"          // 发货权限（包含库存查看）
    }, mode = SaMode.OR)
    @GetMapping("/variant/{variantId}")
    public R<List<WhsVariantStockByWarehouseVo>> getVariantStockByWarehouses(
        @NotNull(message = "变体ID不能为空") @PathVariable Long variantId) {
        List<WhsVariantStockByWarehouseVo> result = stockService.getVariantStockByWarehouses(variantId);
        return R.ok(result);
    }
}
