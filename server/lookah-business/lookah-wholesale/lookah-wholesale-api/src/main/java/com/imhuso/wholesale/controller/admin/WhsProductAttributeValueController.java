package com.imhuso.wholesale.controller.admin;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductAttributeValueBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductAttributeValueVo;
import com.imhuso.wholesale.core.service.IWhsProductAttributeValueService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;

/**
 * 产品属性值
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/productAttributeValue")
public class WhsProductAttributeValueController extends BaseController {

    private final IWhsProductAttributeValueService attributeValueService;

    /**
     * 根据属性ID查询属性值列表
     */
    @SaCheckPermission("wholesale:productAttributeValue:list")
    @GetMapping("/list/{attributeId}")
    public TableDataInfo<WhsProductAttributeValueVo> list(@NotNull(message = "属性ID不能为空") @PathVariable Long attributeId, PageQuery pageQuery) {
        WhsProductAttributeValueBo bo = new WhsProductAttributeValueBo();
        bo.setAttributeId(attributeId);
        return attributeValueService.queryPage(bo, pageQuery);
    }

    /**
     * 属性值详细信息
     */
    @SaCheckPermission("wholesale:productAttributeValue:query")
    @GetMapping("/{id}")
    public R<WhsProductAttributeValueVo> getInfo(@NotNull(message = "ID不能为空") @PathVariable Long id) {
        return R.ok(attributeValueService.getById(id));
    }

    /**
     * 新增属性值
     */
    @SaCheckPermission("wholesale:productAttributeValue:add")
    @Log(title = "属性值", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WhsProductAttributeValueBo bo) {
        return toAjax(attributeValueService.insertAttributeValue(bo));
    }

    /**
     * 修改属性值
     */
    @SaCheckPermission("wholesale:productAttributeValue:edit")
    @Log(title = "属性值", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WhsProductAttributeValueBo bo) {
        return toAjax(attributeValueService.updateAttributeValue(bo));
    }

    /**
     * 删除属性值
     */
    @SaCheckPermission("wholesale:productAttributeValue:remove")
    @Log(title = "属性值", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(attributeValueService.deleteAttributeValueByIds(ids));
    }
}
