package com.imhuso.wholesale.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderStatusLogVo;
import com.imhuso.wholesale.core.service.IWhsOrderStatusLogService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单状态日志管理
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/log")
public class WhsOrderStatusLogController extends BaseController {

    private final IWhsOrderStatusLogService statusLogService;

    /**
     * 获取订单状态日志列表
     */
    @SaCheckPermission("wholesale:order:list")
    @GetMapping("/{orderId}")
    public R<List<WhsOrderStatusLogVo>> list(@PathVariable @NotNull(message = "订单ID不能为空") Long orderId) {
        return R.ok(statusLogService.getOrderStatusLogs(orderId));
    }
}
