package com.imhuso.wholesale.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.bo.admin.WhsShippingAddressBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShippingAddressVo;
import com.imhuso.wholesale.core.service.IWhsShippingAddressService;
import com.imhuso.wholesale.core.service.IWhsShippingAddressAdminService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 会员地址管理
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/address")
public class WhsShippingAddressController extends BaseController {

    private final IWhsShippingAddressService addressService;
    private final IWhsShippingAddressAdminService adminAddressService;

    /**
     * 搜索会员地址分页列表
     * 支持多字段模糊搜索：姓名、电话、邮箱、公司名称、地址等
     */
    @SaCheckPermission("wholesale:address:query")
    @GetMapping("/list")
    public TableDataInfo<WhsShippingAddressVo> list(WhsShippingAddressBo bo, PageQuery pageQuery) {
        return adminAddressService.queryPageList(bo, pageQuery);
    }

    /**
     * 会员地址详细信息
     * 支持OR权限：基础查看权限 OR 需要查看地址详情的业务权限
     */
    @SaCheckPermission(value = {
        "wholesale:address:query",          // 基础地址详情权限
        "wholesale:order:manual:create"     // 手动创建订单需要查看地址详情
    }, mode = SaMode.OR)
    @GetMapping("/{addressId}")
    public R<WhsShippingAddressVo> getInfo(@PathVariable Long addressId) {
        return R.ok(addressService.selectAddressDetailById(addressId));
    }

    /**
     * 会员地址分页列表
     * 支持OR权限：基础查看权限 OR 需要查看地址的业务权限
     */
    @SaCheckPermission(value = {
        "wholesale:address:query",          // 基础地址查询权限
        "wholesale:order:manual:create"     // 手动创建订单需要查看地址
    }, mode = SaMode.OR)
    @GetMapping("/member/{memberId}")
    public TableDataInfo<WhsShippingAddressVo> getMemberAddresses(@PathVariable Long memberId, PageQuery pageQuery) {
        return addressService.getAddressesByMemberIdPage(memberId, pageQuery);
    }

    /**
     * 会员地址列表 不分页
     * 支持OR权限：基础查看权限 OR 需要选择地址的业务权限
     */
    @SaCheckPermission(value = {
        "wholesale:address:list",           // 基础地址列表权限
        "wholesale:order:manual:create"     // 手动创建订单需要选择收货地址
    }, mode = SaMode.OR)
    @GetMapping("/member/{memberId}/list")
    public R<List<WhsShippingAddressVo>> getMemberAddressList(@PathVariable Long memberId) {
        return R.ok(addressService.getAddressesByMemberId(memberId));
    }

    /**
     * 新增会员地址
     */
    @SaCheckPermission("wholesale:address:add")
    @Log(title = "会员地址管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WhsShippingAddressBo bo) {
        return toAjax(adminAddressService.insertAddress(bo));
    }

    /**
     * 修改会员地址
     */
    @SaCheckPermission("wholesale:address:edit")
    @Log(title = "会员地址管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WhsShippingAddressBo bo) {
        return toAjax(adminAddressService.updateAddress(bo));
    }

    /**
     * 删除会员地址
     */
    @SaCheckPermission("wholesale:address:remove")
    @Log(title = "会员地址管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{addressId}")
    public R<Void> remove(@PathVariable Long addressId) {
        return toAjax(adminAddressService.deleteAddress(addressId));
    }
}
