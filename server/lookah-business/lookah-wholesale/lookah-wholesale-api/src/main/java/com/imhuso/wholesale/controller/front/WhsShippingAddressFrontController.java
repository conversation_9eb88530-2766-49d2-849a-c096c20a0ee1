package com.imhuso.wholesale.controller.front;

import java.util.List;

import com.imhuso.wholesale.core.domain.vo.front.ShippingAddressVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.wholesale.core.domain.bo.front.ShippingAddressBo;
import com.imhuso.wholesale.core.service.IWhsShippingAddressService;

import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 收货地址管理
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/address")
public class WhsShippingAddressFrontController {

    private final IWhsShippingAddressService addressService;

    /**
     * 获取收货地址列表
     */
    @GetMapping("/list")
    public R<List<ShippingAddressVo>> list() {
        List<ShippingAddressVo> list = addressService.selectAddressList();
        log.info("查询收货地址列表成功");
        return R.ok(list);
    }

    /**
     * 获取收货地址详细信息
     *
     * @param addressId 地址ID
     */
    @GetMapping("/{addressId}")
    public R<ShippingAddressVo> getInfo(@NotNull(message = "wholesale.address.id.not.null") @PathVariable Long addressId) {
        ShippingAddressVo vo = addressService.selectAddressById(addressId);
        log.info("查询收货地址详情成功");
        return R.ok(vo);
    }

    /**
     * 新增收货地址
     *
     * @param bo 地址信息
     */
    @PostMapping
    public R<Long> add(@Validated(AddGroup.class) @RequestBody ShippingAddressBo bo) {
        Long addressId = addressService.createAddress(bo);
        log.info("新增收货地址成功");
        return R.ok(addressId);
    }

    /**
     * 修改收货地址
     *
     * @param bo 地址信息
     */
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ShippingAddressBo bo) {
        addressService.updateAddress(bo);
        log.info("修改收货地址成功");
        return R.ok();
    }

    /**
     * 删除收货地址
     *
     * @param addressId 地址ID
     */
    @DeleteMapping("/{addressId}")
    public R<Void> remove(@NotNull(message = "wholesale.address.id.not.null") @PathVariable Long addressId) {
        addressService.deleteAddress(addressId);
        log.info("删除收货地址成功");
        return R.ok();
    }

    /**
     * 设置默认地址
     *
     * @param addressId 地址ID
     */
    @PutMapping("/{addressId}/default")
    public R<Void> setDefault(@NotNull(message = "wholesale.address.id.not.null") @PathVariable Long addressId) {
        addressService.setDefaultAddress(addressId);
        log.info("设置默认收货地址成功");
        return R.ok();
    }
}
