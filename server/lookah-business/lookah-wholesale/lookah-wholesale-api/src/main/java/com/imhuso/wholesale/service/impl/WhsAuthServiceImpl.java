package com.imhuso.wholesale.service.impl;

import com.imhuso.common.client.domain.ClientVo;
import com.imhuso.common.json.utils.JsonUtils;
import com.imhuso.wholesale.core.domain.bo.front.WhsLoginBody;
import com.imhuso.wholesale.core.domain.vo.front.WhsLoginVo;
import com.imhuso.wholesale.core.satoken.utils.StpWholesaleUtil;
import com.imhuso.wholesale.core.service.IWhsAuthService;
import com.imhuso.wholesale.core.service.IWhsAuthStrategy;
import com.imhuso.wholesale.core.service.IWhsMemberService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 批发商认证服务实现
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WhsAuthServiceImpl implements IWhsAuthService {

    private final IWhsMemberService memberService;

    @Override
    public WhsLoginVo login(WhsLoginBody loginBody, ClientVo client) {
        // 客户端校验和参数处理已在控制器层完成，这里直接使用传入的参数

        // 使用策略模式登录
        String body = JsonUtils.toJsonString(loginBody);
        return IWhsAuthStrategy.login(body, client, loginBody.getGrantType());
    }

    @Override
    public void logout() {
        StpWholesaleUtil.logout();
    }
}
