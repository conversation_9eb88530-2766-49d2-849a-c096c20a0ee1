package com.imhuso.wholesale.controller.front;

import com.imhuso.common.core.domain.R;
import com.imhuso.wholesale.core.domain.bo.front.CartBo;
import com.imhuso.wholesale.core.domain.vo.front.CartVo;
import com.imhuso.wholesale.core.service.IWhsCartService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 购物车
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/cart")
public class WhsCartFrontController {

    private final IWhsCartService cartService;

    /**
     * 添加到购物车
     */
    @PostMapping
    public R<Void> add(@Valid @RequestBody CartBo bo) {
        cartService.add(bo);
        return R.ok();
    }

    /**
     * 修改购物车数量
     */
    @PutMapping("/{cartId}/quantity/{quantity}")
    public R<Void> updateQuantity(@PathVariable Long cartId,
                                  @PathVariable @NotNull(message = "wholesale.cart.quantity.not.null")
                                  @Min(value = 1, message = "wholesale.cart.quantity.min") Integer quantity) {
        cartService.updateQuantity(cartId, quantity);
        return R.ok();
    }

    /**
     * 删除购物车
     */
    @DeleteMapping("/{cartIds}")
    public R<Void> delete(@PathVariable List<Long> cartIds) {
        cartService.delete(cartIds);
        return R.ok();
    }

    /**
     * 清空购物车
     */
    @DeleteMapping("/clear")
    public R<Void> clear() {
        cartService.clear();
        return R.ok();
    }

    /**
     * 购物车列表
     */
    @GetMapping("/list")
    public R<List<CartVo>> list() {
        List<CartVo> list = cartService.list();
        return R.ok(list);
    }
}
