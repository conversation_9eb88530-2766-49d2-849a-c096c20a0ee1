package com.imhuso.wholesale.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.idempotent.annotation.RepeatSubmit;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.bo.admin.*;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderPendingShipmentVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderProgressVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderVo;
import com.imhuso.wholesale.core.enums.OrderStatus;
import com.imhuso.wholesale.core.service.IWhsOrderPendingShipmentService;
import com.imhuso.wholesale.core.service.IWhsOrderService;
import com.imhuso.wholesale.core.service.IWhsOrderShipmentApprovalService;
import com.imhuso.wholesale.core.service.IWhsOverseasFreightSyncService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 订单管理
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order")
public class WhsOrderController extends BaseController {

    private final IWhsOrderService orderService;
    private final IWhsOrderPendingShipmentService orderPendingShipmentService;
    private final IWhsOverseasFreightSyncService freightSyncService;
    private final IWhsOrderShipmentApprovalService shipmentApprovalService;

    /**
     * 订单列表
     * 支持OR权限：基础查看权限 OR 需要查看订单的业务权限
     */
    @SaCheckPermission(value = {
        "wholesale:order:list",             // 基础订单列表权限
        "wholesale:order:cancel",           // 取消订单需要查看订单列表
        "wholesale:order:status:edit"       // 订单状态修改需要查看订单
    }, mode = SaMode.OR)
    @GetMapping
    public TableDataInfo<WhsOrderVo> list(WhsOrderBo bo, PageQuery pageQuery) {
        return orderService.queryPageList(bo, pageQuery);
    }

    /**
     * 手动下单
     */
    @SaCheckPermission("wholesale:order:manual:create")
    @RepeatSubmit
    @Log(title = "订单管理", businessType = BusinessType.INSERT)
    @PostMapping("/manual")
    public R<Void> manualOrder(@RequestBody @Validated(EditGroup.class) WhsOrderCreateBo bo) {
        return toAjax(orderService.manualOrder(bo));
    }

    /**
     * 订单详情
     * 支持OR权限：基础查看权限 OR 需要查看订单详情的业务权限
     */
    @SaCheckPermission(value = {
        "wholesale:order:view",             // 基础订单查看权限
        "wholesale:order:cancel",           // 取消订单需要查看订单详情
        "wholesale:order:status:edit",       // 订单状态修改需要查看订单详情
        "wholesale:order:shipment"         // 发货需要查看订单详情
    }, mode = SaMode.OR)
    @GetMapping("/{id}")
    public R<WhsOrderVo> getInfo(@PathVariable @NotNull(message = "主键不能为空") Long id) {
        return R.ok(orderService.getOrderById(id));
    }

    /**
     * 订单进度
     */
    @SaCheckPermission("wholesale:order:view")
    @GetMapping("/{id}/progress")
    public R<WhsOrderProgressVo> getProgress(@PathVariable @NotNull(message = "主键不能为空") Long id) {
        return R.ok(orderService.getOrderProgress(id));
    }

    /**
     * 订单待发货明细
     */
    @SaCheckPermission("wholesale:order:view")
    @GetMapping("/{id}/pending-shipment")
    public R<WhsOrderPendingShipmentVo> getPendingShipment(@PathVariable @NotNull(message = "主键不能为空") Long id) {
        return R.ok(orderPendingShipmentService.getOrderPendingShipment(id));
    }

    /**
     * 更新订单支付状态
     */
    @SaCheckPermission("wholesale:order:payment:edit")
    @Log(title = "订单管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/payment-status")
    public R<Void> updatePaymentStatus(@PathVariable @NotNull(message = "订单ID不能为空") Long id,
                                       @RequestBody @Validated(EditGroup.class) OrderPaymentStatusUpdateBo bo) {
        return toAjax(orderService.updatePaymentStatus(id, bo.getPaymentStatus(), bo.getRemark()));
    }

    /**
     * 更新订单发票状态
     */
    @SaCheckPermission("wholesale:order:status:edit")
    @Log(title = "订单管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/invoice-status")
    public R<Void> updateInvoiceStatus(@PathVariable @NotNull(message = "订单ID不能为空") Long id,
                                       @RequestBody @Validated(EditGroup.class) OrderInvoiceStatusUpdateBo bo) {
        return toAjax(orderService.updateInvoiceStatus(id, bo.getInvoiceStatus(), bo.getRemark()));
    }

    /**
     * 取消订单
     */
    @SaCheckPermission("wholesale:order:cancel")
    @Log(title = "订单管理", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/cancel")
    public R<Void> cancel(@PathVariable @NotNull(message = "主键不能为空") Long id,
                          @RequestBody @Validated(EditGroup.class) WhsOrderCancelBo bo) {
        orderService.cancelOrder(id, bo.getCancelReason());
        return R.ok();
    }

    /**
     * 下载发票
     */
    @SaCheckPermission("wholesale:order:view")
    @GetMapping("/{id}/invoice")
    public void downloadInvoice(@PathVariable @NotNull(message = "主键不能为空") Long id, HttpServletResponse response)
        throws Exception {
        orderService.downloadOrderInvoice(id, response);
    }

    /**
     * 下载装箱单
     */
    @SaCheckPermission("wholesale:order:view")
    @GetMapping("/{id}/packing-slip")
    public void downloadPackingSlip(@PathVariable @NotNull(message = "主键不能为空") Long id, HttpServletResponse response)
        throws Exception {
        orderService.downloadOrderPackingSlip(id, response);
    }

    /**
     * 订单发货记录
     */
    @SaCheckPermission("wholesale:order:view")
    @GetMapping("/{id}/shipments")
    public R<List<WhsOrderShipmentVo>> getShipmentRecords(@PathVariable @NotNull(message = "主键不能为空") Long id) {
        return R.ok(orderService.getOrderShipmentRecords(id));
    }

    /**
     * 更新订单号
     */
    @SaCheckPermission("wholesale:order:status:edit")
    @Log(title = "订单管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/order-no")
    public R<Void> updateOrderNo(@PathVariable @NotNull(message = "订单ID不能为空") Long id,
                                 @RequestBody @Validated(EditGroup.class) WhsOrderNoEditBo bo) {
        return toAjax(orderService.updateOrderNo(id, bo));
    }

    /**
     * 标记订单为已完成
     */
    @SaCheckPermission("wholesale:order:status:edit")
    @Log(title = "订单管理", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/complete")
    public R<Void> completeOrder(@PathVariable @NotNull(message = "订单ID不能为空") Long id,
                                 @RequestParam(required = false) String remark) {
        return toAjax(orderService.updateOrderStatus(id, OrderStatus.COMPLETED.getValue(), remark));
    }

    /**
     * 强制完成订单
     * 用于处理因订单中途变化导致无法正常完成的订单
     * 限制条件：只有处理中且已经发过货的订单才能强制完成
     */
    @SaCheckPermission("wholesale:order:forceComplete")
    @Log(title = "订单强制完成", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/force-complete")
    public R<Void> forceCompleteOrder(@PathVariable @NotNull(message = "订单ID不能为空") Long id,
                                      @RequestBody @Valid WhsOrderForceCompleteBo bo) {
        return toAjax(orderService.forceCompleteOrder(id, bo.getReason()));
    }

    /**
     * 撤回订单到草稿状态
     */
    @SaCheckPermission("wholesale:order:status:edit")
    @Log(title = "订单管理", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/revert-to-draft")
    public R<Void> revertOrderToDraft(@PathVariable @NotNull(message = "订单ID不能为空") Long id,
                                      @RequestBody(required = false) Map<String, String> requestBody) {
        String remark = requestBody != null ? requestBody.get("remark") : null;
        return toAjax(orderService.revertOrderToDraft(id, remark));
    }

    /**
     * 编辑草稿状态订单
     */
    @SaCheckPermission("wholesale:order:status:edit")
    @RepeatSubmit
    @Log(title = "订单管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/edit")
    public R<Void> editDraftOrder(@PathVariable @NotNull(message = "订单ID不能为空") Long id,
                                  @RequestBody @Validated(EditGroup.class) WhsOrderCreateBo bo) {
        return toAjax(orderService.editDraftOrder(id, bo));
    }

    /**
     * 同步所有海外仓运费信息
     * 后台手动触发的运费同步功能，异步执行避免前端请求超时
     *
     * @return 同步请求提交结果
     */
    @SaCheckPermission("wholesale:order:freight:sync")
    @Log(title = "运费同步", businessType = BusinessType.OTHER)
    @PostMapping("/freight/sync")
    public R<Void> syncAllFreight() {
        try {
            freightSyncService.asyncSyncAllWarehousesFreight();
            return R.ok("运费同步请求已提交，正在后台处理");
        } catch (Exception e) {
            log.error("提交运费同步任务失败: {}", e.getMessage(), e);
            return R.failed("运费同步请求提交失败：" + e.getMessage());
        }
    }

    /**
     * 检查订单是否需要发货审批
     */
    @SaCheckPermission("wholesale:shipment:request")
    @GetMapping("/{id}/shipment-approval/required")
    public R<Boolean> checkShipmentApprovalRequired(@PathVariable @NotNull(message = "订单ID不能为空") Long id) {
        boolean required = shipmentApprovalService.isShipmentApprovalRequired(id);
        return R.ok(required);
    }

    /**
     * 检查订单发货审批状态
     */
    @SaCheckPermission("wholesale:shipment:request")
    @GetMapping("/{id}/shipment-approval/status")
    public R<Boolean> checkShipmentApprovalStatus(@PathVariable @NotNull(message = "订单ID不能为空") Long id) {
        boolean approved = shipmentApprovalService.isShipmentApproved(id);
        return R.ok(approved);
    }
}
