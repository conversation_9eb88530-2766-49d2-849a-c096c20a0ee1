package com.imhuso.wholesale.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.vo.admin.WhsWarehouseLogisticsMethodVo;
import com.imhuso.wholesale.core.service.IWhsWarehouseLogisticsMethodService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 物流方式管理
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/logistics")
public class WhsLogisticsController extends BaseController {

    private final IWhsWarehouseLogisticsMethodService logisticsMethodService;

    /**
     * 同步物流渠道
     */
    @SaCheckPermission("wholesale:logistics:sync")
    @Log(title = "物流方式", businessType = BusinessType.OTHER)
    @PostMapping("/sync/{warehouseId}")
    public R<Boolean> syncLogisticsMethods(@PathVariable @NotNull(message = "仓库ID不能为空") Long warehouseId) {
        try {
            Boolean result = logisticsMethodService.syncLogisticsChannels(warehouseId);
            return R.ok(result);
        } catch (ServiceException e) {
            log.error("同步物流渠道失败", e);
            return R.failed(e.getMessage());
        } catch (Exception e) {
            log.error("同步物流渠道发生未知异常", e);
            return R.failed("系统错误，同步物流渠道失败");
        }
    }

    /**
     * 获取物流方式列表
     * 支持OR权限：基础查看权限 OR 发货权限
     */
    @SaCheckPermission(value = {
        "wholesale:logistics:list",         // 基础物流列表权限
        "wholesale:order:shipment"          // 发货需要选择物流方式
    }, mode = SaMode.OR)
    @Log(title = "物流方式", businessType = BusinessType.OTHER)
    @GetMapping("/list/{warehouseId}")
    public R<List<WhsWarehouseLogisticsMethodVo>> getLogisticsList(@PathVariable @NotNull(message = "仓库ID不能为空") Long warehouseId) {
        List<WhsWarehouseLogisticsMethodVo> list = logisticsMethodService.queryByWarehouseId(warehouseId);
        return R.ok(list);
    }
}
