package com.imhuso.wholesale.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.bo.admin.WhsInboundOrderListBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsInboundOrderVo;
import com.imhuso.wholesale.core.service.IWhsInboundOrderAsyncService;
import com.imhuso.wholesale.core.service.IWhsInboundOrderService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 入库单管理
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/inbound")
@RequiredArgsConstructor
public class WhsInboundOrderController extends BaseController {

    private final IWhsInboundOrderService inboundOrderService;
    private final IWhsInboundOrderAsyncService inboundOrderAsyncService;

    /**
     * 入库单列表
     */
    @SaCheckPermission("wholesale:inbound:list")
    @GetMapping("/list")
    public TableDataInfo<WhsInboundOrderVo> list(WhsInboundOrderListBo bo, PageQuery pageQuery) {
        return inboundOrderService.queryInboundOrderList(bo, pageQuery);
    }

    /**
     * 获取入库单详情
     */
    @SaCheckPermission("wholesale:inbound:query")
    @GetMapping("/{id}")
    public R<WhsInboundOrderVo> getInfo(@NotNull(message = "入库单ID不能为空") @PathVariable Long id) {
        return R.ok(inboundOrderService.getInboundOrderById(id));
    }

    /**
     * 同步指定仓库的入库单（后台手动同步，查询最近一年数据）
     */
    @SaCheckPermission("wholesale:inbound:sync")
    @Log(title = "入库单同步", businessType = BusinessType.OTHER)
    @PostMapping("/sync/{warehouseId}")
    public R<Void> syncWarehouse(@NotNull(message = "仓库ID不能为空") @PathVariable Long warehouseId) {
        // 后台手动同步使用全量同步（365天），异步执行避免前端请求超时
        try {
            inboundOrderAsyncService.asyncSyncWarehouseFull(warehouseId);
            return R.ok("入库单同步请求已提交，正在后台处理（查询最近一年数据）");
        } catch (Exception e) {
            log.error("提交入库单同步任务失败", e);
            return R.failed("入库单同步请求提交失败：" + e.getMessage());
        }
    }

    /**
     * 同步所有仓库的入库单（后台手动同步，查询最近一年数据）
     */
    @SaCheckPermission("wholesale:inbound:sync")
    @Log(title = "入库单同步", businessType = BusinessType.OTHER)
    @PostMapping("/sync")
    public R<Void> syncAll() {
        // 后台手动同步使用全量同步，异步执行避免前端请求超时
        try {
            inboundOrderAsyncService.asyncSyncAllFull();
            return R.ok("入库单同步请求已提交，正在后台处理（查询最近一年数据）");
        } catch (Exception e) {
            log.error("提交入库单同步任务失败", e);
            return R.failed("入库单同步请求提交失败：" + e.getMessage());
        }
    }


}
