package com.imhuso.wholesale.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.bo.admin.WhsPackageStatusUpdateBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsPackageTrackingUpdateBo;
import com.imhuso.wholesale.core.service.IWhsLogisticsPackageService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 订单包裹管理
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order-package")
public class WhsOrderPackageController extends BaseController {

    private final IWhsLogisticsPackageService logisticsPackageService;

    /**
     * 更新包裹状态
     */
    @SaCheckPermission("wholesale:order:shipment")
    @Log(title = "订单包裹", businessType = BusinessType.UPDATE)
    @PutMapping("/{packageId}/status")
    public R<Void> updatePackageStatus(@PathVariable("packageId") @NotNull(message = "包裹ID不能为空") Long packageId,
                                       @RequestBody @Validated(EditGroup.class) WhsPackageStatusUpdateBo bo) {
        return toAjax(logisticsPackageService.updatePackageStatus(packageId, bo.getPackageStatus(), bo.getRemark()));
    }

    /**
     * 更新包裹跟踪信息
     */
    @SaCheckPermission("wholesale:order:shipment")
    @Log(title = "订单包裹", businessType = BusinessType.UPDATE)
    @PutMapping("/{packageId}/tracking")
    public R<Void> updatePackageTracking(@PathVariable("packageId") @NotNull(message = "包裹ID不能为空") Long packageId,
                                         @RequestBody @Validated(EditGroup.class) WhsPackageTrackingUpdateBo bo) {
        return toAjax(logisticsPackageService.updatePackageTracking(
            packageId, bo.getTrackingNumber(), bo.getCarrier(), bo.getRemark()));
    }
}
