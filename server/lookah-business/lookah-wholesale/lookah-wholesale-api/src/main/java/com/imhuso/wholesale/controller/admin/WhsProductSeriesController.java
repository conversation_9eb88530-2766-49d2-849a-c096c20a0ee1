package com.imhuso.wholesale.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductSeriesBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductSeriesVo;
import com.imhuso.wholesale.core.service.IWhsProductSeriesService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品系列
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/product/series")
@RequiredArgsConstructor
public class WhsProductSeriesController extends BaseController {

    private final IWhsProductSeriesService seriesService;

    /**
     * 获取系列列表
     */
    @SaCheckPermission("wholesale:productSeries:list")
    @GetMapping
    public TableDataInfo<WhsProductSeriesVo> list(WhsProductSeriesBo bo, PageQuery pageQuery) {
        return seriesService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取所有系列列表(不分页)
     * 支持OR权限：基础查看权限 OR 需要选择产品系列的业务权限
     */
    @SaCheckPermission(value = {
        "wholesale:productSeries:list",     // 基础产品系列列表权限
        "wholesale:order:manual:create"     // 手动创建订单需要选择产品系列
    }, mode = SaMode.OR)
    @GetMapping("/all")
    public R<List<WhsProductSeriesVo>> all() {
        return R.ok(seriesService.queryList(new WhsProductSeriesBo()));
    }

    /**
     * 获取系列详情
     */
    @SaCheckPermission("wholesale:productSeries:query")
    @GetMapping("/{id}")
    public R<WhsProductSeriesVo> getInfo(@NotNull(message = "系列ID不能为空") @PathVariable Long id) {
        return R.ok(seriesService.getById(id));
    }

    /**
     * 新增系列
     */
    @SaCheckPermission("wholesale:productSeries:add")
    @Log(title = "产品系列", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WhsProductSeriesBo bo) {
        return toAjax(seriesService.insertSeries(bo));
    }

    /**
     * 修改系列
     */
    @SaCheckPermission("wholesale:productSeries:edit")
    @Log(title = "产品系列", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WhsProductSeriesBo bo) {
        return toAjax(seriesService.updateSeries(bo));
    }

    /**
     * 删除系列
     */
    @SaCheckPermission("wholesale:productSeries:remove")
    @Log(title = "产品系列", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(seriesService.deleteSeriesByIds(ids));
    }
}
