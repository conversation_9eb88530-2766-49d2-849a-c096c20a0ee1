package com.imhuso.wholesale.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductAttributeVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVariantVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVo;
import com.imhuso.wholesale.core.service.IWhsProductService;
import com.imhuso.wholesale.core.service.IWhsProductVariantService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/product")
@Slf4j
public class WhsProductController extends BaseController {

    private final IWhsProductService productService;
    private final IWhsProductVariantService variantService;

    /**
     * 产品列表
     * 支持OR权限：基础查看权限 OR 需要选择产品的业务权限
     */
    @SaCheckPermission(value = {
        "wholesale:product:list",           // 基础产品列表权限
        "wholesale:order:manual:create"     // 手动创建订单需要选择产品
    }, mode = SaMode.OR)
    @GetMapping
    public TableDataInfo<WhsProductVo> list(WhsProductBo bo, PageQuery pageQuery) {
        return productService.queryPage(bo, pageQuery);
    }

    /**
     * 产品详情
     * 支持OR权限：基础查看权限 OR 需要查看产品详情的业务权限
     */
    @SaCheckPermission(value = {
        "wholesale:product:query",          // 基础产品详情权限
        "wholesale:order:manual:create",    // 手动创建订单需要查看产品详情
        "wholesale:stock:adjust"            // 库存调整需要了解产品信息
    }, mode = SaMode.OR)
    @GetMapping("/{id}")
    public R<WhsProductVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(productService.getProductInfo(id));
    }

    /**
     * 新增产品
     */
    @SaCheckPermission("wholesale:product:add")
    @Log(title = "产品管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WhsProductBo bo) {
        return toAjax(productService.insertProduct(bo));
    }

    /**
     * 修改产品
     */
    @SaCheckPermission("wholesale:product:edit")
    @Log(title = "产品管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WhsProductBo bo) {
        return toAjax(productService.updateProduct(bo));
    }

    /**
     * 删除产品
     */
    @SaCheckPermission("wholesale:product:remove")
    @Log(title = "产品管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Void> remove(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(productService.deleteProductById(id));
    }

    /**
     * 更新产品状态
     */
    @SaCheckPermission("wholesale:product:status:edit")
    @Log(title = "产品管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody WhsProductBo bo) {
        return toAjax(productService.updateStatus(bo.getId(), bo.getStatus()));
    }

    /**
     * 产品属性列表
     * 支持OR权限：基础查看权限 OR 需要查看产品属性的业务权限
     */
    @SaCheckPermission(value = {
        "wholesale:product:query",          // 基础产品查询权限
        "wholesale:order:manual:create"     // 手动创建订单需要查看产品属性
    }, mode = SaMode.OR)
    @GetMapping("/{productId}/attributes")
    public R<List<WhsProductAttributeVo>> getAttributes(@NotNull(message = "产品ID不能为空") @PathVariable Long productId) {
        return R.ok(productService.getProductAttributesByProductId(productId));
    }

    /**
     * 产品变体列表
     * 支持OR权限：基础查看权限 OR 需要查看产品变体的业务权限
     */
    @SaCheckPermission(value = {
        "wholesale:product:query",          // 基础产品查询权限
        "wholesale:order:manual:create"     // 手动创建订单需要查看产品变体
    }, mode = SaMode.OR)
    @GetMapping("/{productId}/variants")
    public TableDataInfo<WhsProductVariantVo> getVariants(@NotNull(message = "产品ID不能为空") @PathVariable Long productId,
            PageQuery pageQuery) {
        return variantService.queryPageByProductId(productId, pageQuery);
    }
}
