package com.imhuso.wholesale.controller.admin;

import com.imhuso.wholesale.core.config.ErpConfig;
import com.imhuso.wholesale.core.service.IWhsErpWebhookService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;



/**
 * ERP Webhook控制器
 * 接收ERP系统的库存变化通知
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/webhook/erp")
@RequiredArgsConstructor
public class WhsErpWebhookController {

    private final ErpConfig erpConfig;
    private final IWhsErpWebhookService webhookService;

    /**
     * 接收ERP Webhook通知 (支持多种事件类型)
     *
     * @param event     事件类型 (从URL路径获取)
     * @param payload   请求体
     * @param signature 签名
     * @return 处理结果
     */
    @PostMapping("/{event}")
    public ResponseEntity<String> handleWebhookEvent(
            @PathVariable String event,
            @RequestBody String payload,
            @RequestHeader(value = "X-Webhook-Signature", required = false) String signature) {

        log.info("收到ERP Webhook通知，事件类型: {}", event);

        // 检查Webhook是否启用
        if (!erpConfig.getWebhook().isEnabled()) {
            log.warn("ERP Webhook未启用，忽略通知");
            return ResponseEntity.ok("Webhook disabled");
        }

        // 验证签名
        if (!webhookService.verifyWebhookSignature(payload, signature)) {
            log.error("ERP Webhook签名验证失败");
            return ResponseEntity.status(401).body("Unauthorized");
        }

        // 处理Webhook事件
        boolean success = webhookService.handleWebhookEvent(event, payload);

        if (success) {
            return ResponseEntity.ok("OK");
        } else {
            return ResponseEntity.status(500).body("Event processing failed");
        }
    }
}
