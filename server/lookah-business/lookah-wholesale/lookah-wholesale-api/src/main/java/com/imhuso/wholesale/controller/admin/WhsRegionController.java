package com.imhuso.wholesale.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.wholesale.core.domain.vo.admin.WhsCountryVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsStateVo;
import com.imhuso.wholesale.core.service.IWhsCountryService;
import com.imhuso.wholesale.core.service.IWhsStateService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.constraints.NotBlank;

import java.util.List;

/**
 * 区域信息
 *
 * <AUTHOR>
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/region")
public class WhsRegionController {

    private final IWhsCountryService countryService;
    private final IWhsStateService stateService;

    /**
     * 获取所有启用的国家列表
     *
     * @return 国家列表
     */
    @GetMapping("/countries")
    public R<List<WhsCountryVo>> getAllCountries() {
        return R.ok(countryService.getAllEnabledCountries());
    }

    /**
     * 获取州/省列表
     *
     * @param countryCode 国家代码
     * @return 州/省列表
     */
    @GetMapping("/states/{countryCode}")
    public R<List<WhsStateVo>> getStatesByCountry(
        @NotBlank(message = "国家代码不能为空") @PathVariable("countryCode") String countryCode) {
        WhsCountryVo country = countryService.getCountryByCode(countryCode, WhsCountryVo.class);
        if (country == null) {
            return R.failed(MessageUtils.message("国家不存在"));
        }
        return R.ok(stateService.getStatesByCountryId(country.getId()));
    }
}
