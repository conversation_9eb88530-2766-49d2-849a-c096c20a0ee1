package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 仓库选择业务对象
 *
 * <AUTHOR>
 */
@Data
public class WarehouseSelectionBo {

    /**
     * 发货方案ID
     */
    @NotNull(message = "发货方案ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long planId;

    /**
     * 仓库选择项列表
     */
    @NotEmpty(message = "仓库选择项不能为空", groups = {AddGroup.class, EditGroup.class})
    @Valid
    private List<WarehouseSelectionItemBo> items;
}
