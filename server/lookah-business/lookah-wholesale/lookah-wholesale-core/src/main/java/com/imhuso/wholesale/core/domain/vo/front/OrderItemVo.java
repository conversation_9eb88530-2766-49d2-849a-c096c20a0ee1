package com.imhuso.wholesale.core.domain.vo.front;

import com.imhuso.common.translation.annotation.Translation;
import com.imhuso.wholesale.core.constant.TransConstant;
import com.imhuso.wholesale.core.domain.WhsOrderItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 批发订单项视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsOrderItem.class)
public class OrderItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单项ID
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品SKU
     */
    private String sku;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品规格信息
     */
    private String specs;

    /**
     * 包装类型
     */
    private Integer packagingType;

    /**
     * 包装类型文本
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, other = "packagingType", mapper = "packagingType")
    private String packagingTypeText;

    /**
     * 商品价格
     */
    private BigDecimal productPrice;

    /**
     * 采购单价
     */
    private BigDecimal purchasePrice;

    /**
     * 销售单价（手动填入）
     */
    private BigDecimal salesPrice;

    /**
     * 商品数量
     */
    private Integer quantity;

    /**
     * 商品主图
     */
    private String mainImage;

    /**
     * 商品总金额
     */
    private BigDecimal amount;
}
