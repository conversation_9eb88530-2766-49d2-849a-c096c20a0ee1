package com.imhuso.wholesale.core.mapper;

import com.imhuso.common.mybatis.core.mapper.BaseMapperPlus;
import com.imhuso.wholesale.core.domain.WhsInboundOrderItem;
import com.imhuso.wholesale.core.domain.vo.admin.WhsInboundOrderItemVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 入库单明细Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface WhsInboundOrderItemMapper extends BaseMapperPlus<WhsInboundOrderItem, WhsInboundOrderItemVo> {

    /**
     * 根据变体ID查询产品名称
     *
     * @param variantId 变体ID
     * @return 产品名称
     */
    String getProductNameByVariantId(@Param("variantId") Long variantId);

    /**
     * 批量计算多个变体的在途库存
     * 注意：此方法与WhsStockMapper.selectInTransitStockByVariantId功能类似，
     * 但返回格式不同，用于WhsInTransitStockServiceImpl批量更新场景
     *
     * @param variantIds 变体ID列表
     * @return 变体ID到在途库存数量的映射
     */
    List<WhsInboundOrderItem> calculateInTransitStockBatch(@Param("variantIds") List<Long> variantIds);

    /**
     * 批量插入入库单明细
     *
     * @param items 明细列表
     * @return 影响行数
     */
    int insertBatch(@Param("items") List<WhsInboundOrderItem> items);
}
