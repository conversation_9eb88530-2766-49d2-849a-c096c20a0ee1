package com.imhuso.wholesale.core.exception;

import com.imhuso.common.core.exception.base.BaseException;
import com.imhuso.wholesale.core.constant.WholesaleConstants;

import java.io.Serial;

/**
 * 批发业务异常
 *
 * <AUTHOR>
 */
public class WhsException extends BaseException {

    @Serial
    private static final long serialVersionUID = 1L;

    public WhsException(String code, Object... args) {
        super(WholesaleConstants.Module.NAME, code, args);
    }

    public WhsException(String defaultMessage) {
        super(WholesaleConstants.Module.NAME, defaultMessage);
    }

    public WhsException(String code, Object[] args, String defaultMessage) {
        super(WholesaleConstants.Module.NAME, code, args, defaultMessage);
    }
}
