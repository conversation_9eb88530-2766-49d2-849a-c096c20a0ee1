package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品属性定义对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_product_attribute")
public class WhsProductAttribute extends BaseEntity {

    /**
     * 属性ID
     */
    @TableId
    private Long id;

    /**
     * 属性名称(如color,material)
     */
    private String attrName;

    /**
     * 属性显示名称(如颜色,材质)
     */
    private String attrDisplay;

    /**
     * 是否必填
     */
    private String isRequired;

    /**
     * 排序顺序
     */
    private Integer sort;

    /**
     * 状态（0停用 1正常）
     */
    private String status;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;
}
