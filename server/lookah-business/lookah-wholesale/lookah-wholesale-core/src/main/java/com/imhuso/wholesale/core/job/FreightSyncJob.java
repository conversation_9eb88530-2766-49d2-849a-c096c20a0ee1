package com.imhuso.wholesale.core.job;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import com.aizuda.snailjob.common.log.SnailJobLog;
import com.imhuso.wholesale.core.service.IWhsOverseasFreightSyncService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 海外仓运费同步定时任务
 * 定期同步海外仓订单的运费信息
 * <p>
 * 注意：此类由SnailJob框架调度执行，不会被Spring直接调用
 * IDE可能会提示"未使用"，这是正常现象
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@SuppressWarnings("unused") // SnailJob框架调用，IDE检测不到使用
public class FreightSyncJob {

    private final IWhsOverseasFreightSyncService freightSyncService;

    /**
     * 执行海外仓运费同步任务
     *
     * @param jobArgs 任务参数
     * @return 执行结果
     */
    @JobExecutor(name = "syncOverseasWarehouseFreight")
    public ExecuteResult syncOverseasWarehouseFreight(JobArgs jobArgs) {
        SnailJobLog.LOCAL.info("开始执行海外仓运费同步任务");
        try {
            int syncCount = freightSyncService.syncAllWarehousesFreight();
            String message = String.format("海外仓运费同步任务执行完成，成功同步%d个订单的运费信息", syncCount);
            SnailJobLog.LOCAL.info(message);
            return ExecuteResult.success(message);
        } catch (Exception e) {
            String errorMessage = "海外仓运费同步任务执行失败: " +
                StringUtils.defaultIfEmpty(e.getMessage(), e.getClass().getName());
            SnailJobLog.LOCAL.error(errorMessage, e);
            return ExecuteResult.failure(errorMessage);
        }
    }
}
