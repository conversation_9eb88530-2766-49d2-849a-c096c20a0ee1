package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imhuso.wholesale.core.domain.WhsProductAttribute;
import com.imhuso.wholesale.core.domain.WhsProductAttributeValue;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.WhsProductVariantAttribute;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVariantAttributeVo;
import com.imhuso.wholesale.core.mapper.WhsProductAttributeMapper;
import com.imhuso.wholesale.core.mapper.WhsProductAttributeValueMapper;
import com.imhuso.wholesale.core.mapper.WhsProductVariantAttributeMapper;
import com.imhuso.wholesale.core.mapper.WhsProductVariantMapper;
import com.imhuso.wholesale.core.utils.StructDiffUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品变体属性辅助处理类
 * 专门处理与变体属性相关的数据处理操作
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WhsProductVariantAttributeHelper {

    private final WhsProductVariantMapper variantMapper;
    private final WhsProductAttributeMapper attributeMapper;
    private final WhsProductAttributeValueMapper attributeValueMapper;
    private final WhsProductVariantAttributeMapper variantAttributeMapper;

    /**
     * 更新变体规格信息
     *
     * @param variantId  变体ID
     * @param attributes 属性映射
     */
    public void updateVariantSpecs(Long variantId, Map<Long, Long> attributes) {
        if (variantId == null) {
            return;
        }

        JSONObject specsJson = new JSONObject();
        if (!CollUtil.isEmpty(attributes)) {
            Map<Long, WhsProductAttribute> attributeMap = getAttributesMap(new ArrayList<>(attributes.keySet()));
            Map<Long, WhsProductAttributeValue> valueMap = getAttributeValuesMap(
                    new ArrayList<>(attributes.values()));

            attributes.forEach((attrId, valueId) -> {
                WhsProductAttribute attr = attributeMap.get(attrId);
                WhsProductAttributeValue value = valueMap.get(valueId);
                if (attr != null && value != null) {
                    specsJson.set(attr.getAttrDisplay(), value.getValueDisplay());
                }
            });
        }

        WhsProductVariant variant = new WhsProductVariant();
        variant.setId(variantId);
        variant.setSpecs(specsJson.toString());
        variantMapper.updateById(variant);
    }

    /**
     * 处理属性变更
     *
     * @param variantId    变体ID
     * @param attributeMap 新的属性映射
     */
    public void processAttributeChanges(Long variantId, Map<Long, Long> attributeMap) {
        if (variantId == null) {
            return;
        }

        // 获取已存在的属性
        List<WhsProductVariantAttribute> existingAttributes = getExistingAttributes(variantId);

        // 如果没有新的属性映射，则删除所有现有属性
        if (CollUtil.isEmpty(attributeMap)) {
            deleteVariantAttributes(variantId);
            return;
        }

        // 转换新的属性映射为实体列表
        List<WhsProductVariantAttribute> newAttributes = convertMapToEntities(variantId, attributeMap);

        // 计算需要增删改的实体并执行相应操作
        applyAttributeChanges(existingAttributes, newAttributes);
    }

    /**
     * 获取变体关联的所有属性
     */
    public List<WhsProductVariantAttribute> getExistingAttributes(Long variantId) {
        if (variantId == null) {
            return Collections.emptyList();
        }

        return variantAttributeMapper.selectList(
                Wrappers.lambdaQuery(WhsProductVariantAttribute.class)
                        .eq(WhsProductVariantAttribute::getVariantId, variantId));
    }

    /**
     * 删除变体的所有属性关联
     */
    public void deleteVariantAttributes(Long variantId) {
        if (variantId == null) {
            return;
        }

        variantAttributeMapper.delete(
                Wrappers.lambdaQuery(WhsProductVariantAttribute.class)
                        .eq(WhsProductVariantAttribute::getVariantId, variantId));
    }

    /**
     * 批量插入变体属性关联
     */
    public void batchInsertAttributes(List<WhsProductVariantAttribute> attributes) {
        if (CollUtil.isEmpty(attributes)) {
            return;
        }

        attributes.forEach(variantAttributeMapper::insert);
    }

    /**
     * 应用属性变更（增删改）
     */
    private void applyAttributeChanges(List<WhsProductVariantAttribute> existingAttributes,
            List<WhsProductVariantAttribute> newAttributes) {
        // 使用StructDiffUtils计算差异
        StructDiffUtils<WhsProductVariantAttribute, Long> diff = StructDiffUtils.compute(
                existingAttributes,
                newAttributes,
                WhsProductVariantAttribute::getAttributeId,
                (oldAttr, newAttr) -> Objects.equals(oldAttr.getAttributeValueId(), newAttr.getAttributeValueId()));

        // 如果没有变化，直接返回
        if (!diff.hasChanges()) {
            return;
        }

        // 删除需要删除的
        if (!diff.getToDelete().isEmpty()) {
            List<Long> idsToDelete = diff.getToDelete().stream()
                    .map(WhsProductVariantAttribute::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (!idsToDelete.isEmpty()) {
                variantAttributeMapper.deleteByIds(idsToDelete);
            }
        }

        // 添加需要添加的
        if (!diff.getToAdd().isEmpty()) {
            batchInsertAttributes(diff.getToAdd());
        }

        // 更新需要更新的
        if (!diff.getToUpdate().isEmpty()) {
            // 为更新的属性设置正确的ID
            Map<Long, Long> existingIdMap = existingAttributes.stream()
                    .collect(Collectors.toMap(
                            WhsProductVariantAttribute::getAttributeId,
                            WhsProductVariantAttribute::getId));

            diff.getToUpdate().forEach(newAttr -> {
                // 设置旧记录的ID，以便正确更新
                Long existingId = existingIdMap.get(newAttr.getAttributeId());
                if (existingId != null) {
                    newAttr.setId(existingId);
                    variantAttributeMapper.updateById(newAttr);
                }
            });
        }
    }

    /**
     * 转换属性映射为实体列表
     */
    public List<WhsProductVariantAttribute> convertMapToEntities(Long variantId, Map<Long, Long> attributeMap) {
        if (variantId == null || CollUtil.isEmpty(attributeMap)) {
            return Collections.emptyList();
        }

        return attributeMap.entrySet().stream()
                .map(entry -> {
                    WhsProductVariantAttribute attr = new WhsProductVariantAttribute();
                    attr.setVariantId(variantId);
                    attr.setAttributeId(entry.getKey());
                    attr.setAttributeValueId(entry.getValue());
                    return attr;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取变体属性信息
     *
     * @param variantId 变体ID
     * @return 变体属性VO列表
     */
    public List<WhsProductVariantAttributeVo> getVariantAttributes(Long variantId) {
        if (variantId == null) {
            return Collections.emptyList();
        }

        return getVariantAttributesMap(Collections.singletonList(variantId))
                .getOrDefault(variantId, Collections.emptyList());
    }

    /**
     * 批量获取变体属性信息
     *
     * @param variantIds 变体ID列表
     * @return 变体ID到属性列表的映射
     */
    public Map<Long, List<WhsProductVariantAttributeVo>> getVariantAttributesMap(List<Long> variantIds) {
        if (CollUtil.isEmpty(variantIds)) {
            return Collections.emptyMap();
        }

        // 1. 获取所有变体属性关联
        List<WhsProductVariantAttribute> allAttributes = variantAttributeMapper.selectList(
                Wrappers.lambdaQuery(WhsProductVariantAttribute.class)
                        .in(WhsProductVariantAttribute::getVariantId, variantIds));

        if (CollUtil.isEmpty(allAttributes)) {
            return variantIds.stream().collect(Collectors.toMap(id -> id, id -> Collections.emptyList()));
        }

        // 2. 收集所有需要查询的属性ID和属性值ID
        Set<Long> attributeIds = allAttributes.stream()
                .map(WhsProductVariantAttribute::getAttributeId)
                .collect(Collectors.toSet());

        Set<Long> attributeValueIds = allAttributes.stream()
                .map(WhsProductVariantAttribute::getAttributeValueId)
                .collect(Collectors.toSet());

        // 3. 批量查询属性和属性值详情
        Map<Long, WhsProductAttribute> attributeMap = getAttributesMap(new ArrayList<>(attributeIds));
        Map<Long, WhsProductAttributeValue> valueMap = getAttributeValuesMap(new ArrayList<>(attributeValueIds));

        // 4. 按变体ID分组
        Map<Long, List<WhsProductVariantAttribute>> attributesByVariant = allAttributes.stream()
                .collect(Collectors.groupingBy(WhsProductVariantAttribute::getVariantId));

        // 5. 构建结果 HashMap
        Map<Long, List<WhsProductVariantAttributeVo>> result = new HashMap<>(variantIds.size());
        for (Long variantId : variantIds) {
            List<WhsProductVariantAttribute> variantAttrs = attributesByVariant
                    .getOrDefault(variantId, Collections.emptyList());

            List<WhsProductVariantAttributeVo> voList = variantAttrs.stream()
                    .map(attr -> convertToVo(attr, attributeMap.get(attr.getAttributeId()),
                            valueMap.get(attr.getAttributeValueId())))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            result.put(variantId, voList);
        }

        return result;
    }

    /**
     * 将实体转换为VO
     */
    private WhsProductVariantAttributeVo convertToVo(WhsProductVariantAttribute attribute,
            WhsProductAttribute productAttr,
            WhsProductAttributeValue attrValue) {
        if (attribute == null || productAttr == null || attrValue == null) {
            return null;
        }

        WhsProductVariantAttributeVo vo = new WhsProductVariantAttributeVo();
        vo.setId(attribute.getId());
        vo.setAttributeId(attribute.getAttributeId());
        vo.setAttributeValueId(attribute.getAttributeValueId());

        // 设置属性信息
        vo.setAttrName(productAttr.getAttrName());
        vo.setAttrDisplay(productAttr.getAttrDisplay());
        vo.setSort(productAttr.getSort());

        // 设置属性值信息
        vo.setValueKey(attrValue.getValueKey());
        vo.setValueDisplay(attrValue.getValueDisplay());

        return vo;
    }

    /**
     * 获取属性Map
     */
    public Map<Long, WhsProductAttribute> getAttributesMap(List<Long> attributeIds) {
        if (CollUtil.isEmpty(attributeIds)) {
            return new HashMap<>(0);
        }

        return attributeMapper.selectList(
                Wrappers.lambdaQuery(WhsProductAttribute.class)
                        .in(WhsProductAttribute::getId, attributeIds))
                .stream()
                .collect(Collectors.toMap(WhsProductAttribute::getId, attr -> attr));
    }

    /**
     * 获取属性值Map
     */
    public Map<Long, WhsProductAttributeValue> getAttributeValuesMap(List<Long> valueIds) {
        if (CollUtil.isEmpty(valueIds)) {
            return new HashMap<>(0);
        }

        return attributeValueMapper.selectList(
                Wrappers.lambdaQuery(WhsProductAttributeValue.class)
                        .in(WhsProductAttributeValue::getId, valueIds))
                .stream()
                .collect(Collectors.toMap(WhsProductAttributeValue::getId, value -> value));
    }
}
