package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.WhsStock;

import java.util.List;
import java.util.Map;

/**
 * 库存缓存服务接口
 * 提供库存相关的缓存操作方法
 *
 * <AUTHOR>
 */
public interface IWhsStockCacheService {
    /**
     * 从缓存获取变体可用库存，不查询数据库
     *
     * @param variantId 变体ID
     * @return 可用库存数量，缓存未命中返回null
     */
    Integer getVariantAvailableStockFromCache(Long variantId);

    /**
     * 从缓存批量获取变体可用库存，不查询数据库
     *
     * @param variantIds 变体ID列表
     * @return 变体ID与可用库存的映射，只包含缓存命中的结果
     */
    Map<Long, Integer> getVariantAvailableStocksFromCache(List<Long> variantIds);

    /**
     * 更新库存对象缓存
     *
     * @param stock 库存对象
     */
    void updateStockCache(WhsStock stock);

    /**
     * 刷新变体的库存缓存
     * 从数据库获取最新值并更新缓存
     *
     * @param variantIds 变体ID列表
     */
    void refreshVariantsStockCache(List<Long> variantIds);

    /**
     * 检查并修复Redis缓存中的库存数据
     */
    void checkAndRepairStockCache();

    /**
     * 预加载所有商品库存到缓存
     */
    void reloadAllProductStocksCache();
}
