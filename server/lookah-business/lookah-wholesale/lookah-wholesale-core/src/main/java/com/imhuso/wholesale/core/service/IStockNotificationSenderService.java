package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.bo.front.StockNotifyTemplateBo;

/**
 * 库存通知发送服务接口
 * 定义发送库存通知的方法接口
 *
 * <AUTHOR>
 */
public interface IStockNotificationSenderService {
    /**
     * 发送库存到货客户通知
     *
     * @param templateBo 通知模板数据
     */
    void sendStockCustomerNotification(StockNotifyTemplateBo templateBo);

    /**
     * 发送库存到货管理员通知
     *
     * @param templateBo 通知模板数据
     */
    void sendStockAdminNotification(StockNotifyTemplateBo templateBo);
}
