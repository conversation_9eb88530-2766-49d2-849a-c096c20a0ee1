package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.WhsOrderShipmentItem;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentConversionVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanItemVo;

import java.util.List;
import java.util.Map;

/**
 * 订单发货库存处理服务接口
 *
 * <AUTHOR>
 */
public interface IWhsShipmentStockProcessingService {

    /**
     * 为指定发货处理库存操作（分配释放、转换释放、出库、检查并释放剩余）
     *
     * @param orderId       订单ID
     * @param shipmentItems 发货项实体列表 (注意：不是Bo)
     * @param planItemMap   计划项映射 (planItemId -> planItemVo)
     * @param planItems     与此次发货相关的所有计划项列表
     * @param conversions   与当前计划相关的转换记录列表
     */
    void processStockForShipment(Long orderId,
                                 List<WhsOrderShipmentItem> shipmentItems,
                                 Map<Long, WhsShipmentPlanItemVo> planItemMap,
                                 List<WhsShipmentPlanItemVo> planItems,
                                 List<WhsShipmentConversionVo> conversions);
}
