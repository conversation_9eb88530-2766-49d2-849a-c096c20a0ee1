package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 产品变体对象 whs_product_variant
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_product_variant")
public class WhsProductVariant extends BaseEntity {

    /**
     * 变体ID
     */
    @TableId
    private Long id;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * UPC编码
     */
    private String upc;

    /**
     * 批发价格(Wholesale)
     */
    private BigDecimal wholesalePrice;

    /**
     * 建议零售价(MSRP)
     */
    private BigDecimal msrp;

    /**
     * 门店价格
     */
    private BigDecimal storePrice;

    /**
     * 主图URL
     */
    private String mainImage;

    /**
     * 规格属性JSON(冗余,用于快速访问)
     */
    private String specs;

    /**
     * 状态（0停用 1正常）
     */
    private String status;

    /**
     * 包装类型
     *
     * @see com.imhuso.wholesale.core.enums.PackagingType
     */
    private Integer packagingType;

    /**
     * 库存预警阈值
     */
    private Integer alertStock;

    /**
     * 成品库存数量
     */
    private Integer finishedStock;

    /**
     * 待产库存数量
     */
    private Integer pendingStock;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;
}
