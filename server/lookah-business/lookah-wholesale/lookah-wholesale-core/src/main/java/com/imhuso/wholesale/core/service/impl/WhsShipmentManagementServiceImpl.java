package com.imhuso.wholesale.core.service.impl;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsShipmentManagementBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentApprovalVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentManagementVo;
import com.imhuso.wholesale.core.enums.OrderStatus;
import com.imhuso.wholesale.core.enums.ShipmentApprovalStatus;
import com.imhuso.wholesale.core.enums.ShipmentStatus;
import com.imhuso.wholesale.core.service.IApprovalConfigService;
import com.imhuso.wholesale.core.service.IWhsOrderService;
import com.imhuso.wholesale.core.service.IWhsOrderShipmentApprovalService;
import com.imhuso.wholesale.core.service.IWhsShipmentManagementService;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 发货管理Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsShipmentManagementServiceImpl implements IWhsShipmentManagementService {

    private final IWhsOrderService orderService;
    private final Converter converter;
    private final IApprovalConfigService approvalConfigService;
    private final IWhsOrderShipmentApprovalService approvalService;

    @Override
    public TableDataInfo<WhsShipmentManagementVo> queryPendingShipmentOrders(WhsShipmentManagementBo bo, PageQuery pageQuery) {
        try {
            // 转换为订单查询BO，直接在SQL层面过滤审批状态
            WhsOrderBo orderBo = convertToOrderBo(bo);

            // 调用订单服务查询（已在SQL层面过滤了审批状态）
            TableDataInfo<WhsOrderVo> orderResult = orderService.queryPageList(orderBo, pageQuery);

            // 直接转换为发货管理VO，无需额外过滤
            List<WhsShipmentManagementVo> records = orderResult.getRows().stream()
                .map(this::convertToShipmentManagementVo)
                .collect(Collectors.toList());

            return new TableDataInfo<>(records, orderResult.getTotal());
        } catch (Exception e) {
            log.error("查询待发货订单列表时发生错误: {}", e.getMessage(), e);
            return new TableDataInfo<>(Collections.emptyList(), 0L);
        }
    }

    /**
     * 转换发货管理BO为订单BO
     */
    private WhsOrderBo convertToOrderBo(WhsShipmentManagementBo bo) {
        WhsOrderBo orderBo = new WhsOrderBo();

        if (bo != null) {
            // 复制基础查询条件
            orderBo.setOrderNo(bo.getOrderNo());
            orderBo.setCustomerOrderNo(bo.getCustomerOrderNo());
            orderBo.setShippingName(bo.getShippingName());
            orderBo.setShippingCountry(bo.getShippingCountry());
            orderBo.setSalespersonId(bo.getSalespersonId());
            orderBo.setParams(bo.getParams());
        }

        // 设置发货管理的核心查询条件
        orderBo.setOrderStatus(new Integer[]{OrderStatus.PROCESSING.getValue()}); // 只查询处理中的订单

        // 设置发货状态：待发货(0)或部分发货(2)
        if (bo != null && bo.getShipmentStatus() != null && bo.getShipmentStatus().length > 0) {
            orderBo.setShipmentStatusArray(bo.getShipmentStatus());
        } else {
            // 默认查询待发货和部分发货状态
            orderBo.setShipmentStatusArray(new Integer[]{
                ShipmentStatus.PENDING.getValue(),
                ShipmentStatus.PARTIAL_SHIPPED.getValue()
            });
        }

        // 发货管理需要根据审批配置智能判断可发货的订单
        setApprovalStatusForShipmentManagement(orderBo);

        return orderBo;
    }


    /**
     * 转换订单VO为发货管理VO
     */
    private WhsShipmentManagementVo convertToShipmentManagementVo(WhsOrderVo orderVo) {
        WhsShipmentManagementVo vo = converter.convert(orderVo, WhsShipmentManagementVo.class);

        // 设置默认值（这些字段在订单VO中可能没有，需要后续通过其他服务填充）
        if (vo.getShippedPcs() == null) {
            vo.setShippedPcs(0);
        }
        if (vo.getTotalCases() == null) {
            vo.setTotalCases(0);
        }
        if (vo.getShippedCases() == null) {
            vo.setShippedCases(0);
        }
        if (vo.getShippingWarehouses() == null) {
            vo.setShippingWarehouses("默认仓库");
        }

        // 审批状态直接从订单VO中获取（已通过数据库字段查询）
        vo.setApprovalStatus(orderVo.getShipmentApprovalStatus());

        // 获取审批意见（只有审批通过的订单才需要获取）
        if (orderVo.getShipmentApprovalStatus() != null && 
            orderVo.getShipmentApprovalStatus().equals(ShipmentApprovalStatus.APPROVED.getValue())) {
            try {
                WhsOrderShipmentApprovalVo approval = approvalService.queryLatestByOrderId(orderVo.getId());
                if (approval != null && approval.getApprovalStatus() != null && 
                    approval.getApprovalStatus().equals(ShipmentApprovalStatus.APPROVED.getValue())) {
                    vo.setApprovalComment(approval.getApprovalComment());
                }
            } catch (Exception e) {
                log.warn("获取订单{}的审批意见失败: {}", orderVo.getOrderNo(), e.getMessage());
                // 不影响主流程，审批意见获取失败时为null
            }
        }

        return vo;
    }

    /**
     * 根据审批配置设置发货管理的审批状态查询条件
     */
    private void setApprovalStatusForShipmentManagement(WhsOrderBo orderBo) {
        // 使用统一的审批配置逻辑
        List<Integer> shippableStatuses = approvalConfigService.getShippableApprovalStatuses();

        if (shippableStatuses == null) {
            // 不限制审批状态，使用特殊标记
            orderBo.setShipmentApprovalStatus(-1);
        } else if (shippableStatuses.size() == 1) {
            // 只有一个状态，直接设置
            orderBo.setShipmentApprovalStatus(shippableStatuses.get(0));
        } else {
            // 多个状态，使用特殊标记，让查询层处理
            orderBo.setShipmentApprovalStatus(-1);
        }
    }

}
