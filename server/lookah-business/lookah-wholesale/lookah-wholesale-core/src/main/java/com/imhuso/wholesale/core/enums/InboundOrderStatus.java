package com.imhuso.wholesale.core.enums;

import com.imhuso.common.translation.core.EnumTranslatableInterface;
import lombok.Getter;

/**
 * 入库单状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum InboundOrderStatus implements EnumTranslatableInterface {
    // 0:待入库,1部分入库,2:已入库,3:部分上架,4:已上架,10已作废
    /**
     * 待入库
     */
    PENDING_RECEIPT(0),

    /**
     * 部分入库
     */
    PARTIALLY_RECEIVED(1),

    /**
     * 已入库
     */
    RECEIVED(2),

    /**
     * 部分上架
     */
    PARTIALLY_SHELVED(3),

    /**
     * 已上架
     */
    SHELVED(4),

    /**
     * 已作废
     */
    CANCELLED(10);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 构造函数
     *
     * @param value 枚举值
     */
    InboundOrderStatus(int value) {
        this.value = value;
    }

    /**
     * 根据值获取枚举实例
     *
     * @param value 枚举值
     * @return 对应的枚举实例，如果没有找到则返回null
     */
    public static InboundOrderStatus getByValue(Integer value) {
        if (value == null) {
            return null;
        }

        return switch (value) {
            case 0 -> PENDING_RECEIPT;
            case 1 -> PARTIALLY_RECEIVED;
            case 2 -> RECEIVED;
            case 3 -> PARTIALLY_SHELVED;
            case 4 -> SHELVED;
            case 10 -> CANCELLED;
            default -> null;
        };
    }
}
