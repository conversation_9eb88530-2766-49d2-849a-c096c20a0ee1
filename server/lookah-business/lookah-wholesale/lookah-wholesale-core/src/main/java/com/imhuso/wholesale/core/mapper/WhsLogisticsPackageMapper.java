package com.imhuso.wholesale.core.mapper;

import com.imhuso.common.mybatis.core.mapper.BaseMapperPlus;
import com.imhuso.wholesale.core.domain.WhsLogisticsPackage;
import com.imhuso.wholesale.core.domain.vo.admin.WhsLogisticsPackageVo;
import com.imhuso.wholesale.core.domain.vo.front.LogisticsPackageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物流包裹 数据层
 *
 * <AUTHOR>
 */
@Mapper
public interface WhsLogisticsPackageMapper extends BaseMapperPlus<WhsLogisticsPackage, WhsLogisticsPackageVo> {

    /**
     * 根据发货ID查询物流包裹列表（包含物流方式名称、仓库名称和渠道信息）
     *
     * @param shipmentId 发货ID
     * @return 物流包裹列表
     */
    List<WhsLogisticsPackageVo> selectPackagesByShipmentId(@Param("shipmentId") Long shipmentId);

    /**
     * 根据订单ID查询物流包裹列表（用于用户端展示）
     *
     * @param orderId 订单ID
     * @return 物流包裹列表
     */
    List<LogisticsPackageVo> selectPackagesByOrderId(@Param("orderId") Long orderId);

    /**
     * 查询指定仓库未同步运费的包裹
     *
     * @param warehouseId 仓库ID
     * @return 未同步运费的包裹列表
     */
    List<WhsLogisticsPackage> selectUnsyncedPackagesByWarehouseId(@Param("warehouseId") Long warehouseId);
}
