package com.imhuso.wholesale.core.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Collections;
import java.util.List;

/**
 * 库存变更事件
 * 用于通知系统中其他组件库存发生了变化
 *
 * <AUTHOR>
 */
@Getter
public class StockChangeEvent extends ApplicationEvent {

    /**
     * 发生变更的变体ID列表
     */
    private final List<Long> variantIds;

    /**
     * 关联的产品ID列表
     */
    private final List<Long> productIds;

    /**
     * 标记该事件是否由产品信息更新触发
     * -- GETTER --
     * 判断事件是否由产品信息更新触发
     */
    @Getter
    private boolean productUpdated = false;

    /**
     * 构造函数
     *
     * @param source     事件源
     * @param variantIds 变体ID列表
     * @param productIds 产品ID列表
     */
    public StockChangeEvent(Object source, List<Long> variantIds, List<Long> productIds) {
        super(source);
        this.variantIds = variantIds != null ? Collections.unmodifiableList(variantIds) : Collections.emptyList();
        this.productIds = productIds != null ? Collections.unmodifiableList(productIds) : Collections.emptyList();
    }

    /**
     * 构造函数
     *
     * @param source         事件源
     * @param variantIds     变体ID列表
     * @param productIds     产品ID列表
     * @param productUpdated 是否由产品信息更新触发
     */
    public StockChangeEvent(Object source, List<Long> variantIds, List<Long> productIds, boolean productUpdated) {
        super(source);
        this.variantIds = variantIds != null ? Collections.unmodifiableList(variantIds) : Collections.emptyList();
        this.productIds = productIds != null ? Collections.unmodifiableList(productIds) : Collections.emptyList();
        this.productUpdated = productUpdated;
    }
}
