package com.imhuso.wholesale.core.domain.vo.warehouse;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 海外仓发货结果VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShippingResultVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 是否发货成功
     */
    private boolean success;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 外部系统发货单号
     */
    private String externalShipmentId;

    /**
     * 物流单号
     */
    private String trackingNumber;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 预计发货时间
     */
    private Date estimatedShipDate;

    /**
     * 预计送达时间
     */
    private Date estimatedDeliveryDate;

    /**
     * 响应数据
     */
    private Map<String, Object> responseData;
}
