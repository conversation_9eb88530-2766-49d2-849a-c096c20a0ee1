package com.imhuso.wholesale.core.domain.vo.admin;

import com.imhuso.common.translation.annotation.Translation;
import com.imhuso.wholesale.core.constant.TransConstant;
import com.imhuso.wholesale.core.domain.WhsShipmentPlanItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 发货方案项视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@AutoMapper(target = WhsShipmentPlanItem.class)
public class WhsShipmentPlanItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    private Long id;

    /**
     * 方案ID
     */
    private Long planId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * 变体规格
     */
    private String specsText;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 计划项数量 (例如: 1箱, 5个散件)
     */
    private Integer quantity;

    /**
     * 包装类型: 0-单品 1-展示盒 2-整箱
     */
    private Integer packagingType;

    /**
     * 包装类型名称
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, mapper = "packagingType", other = "packagingType")
    private String packagingTypeText;

    /**
     * 包装数量 (每计划单位包含的原始物品数)
     */
    private Integer packagingQuantity;

    /**
     * 已发货数量
     */
    private Integer shippedQuantity;

    /**
     * 创建时间
     */
    private Date createTime;
}
