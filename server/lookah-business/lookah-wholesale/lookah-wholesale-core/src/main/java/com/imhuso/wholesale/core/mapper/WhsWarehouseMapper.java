package com.imhuso.wholesale.core.mapper;

import com.imhuso.common.mybatis.core.mapper.BaseMapperPlus;
import com.imhuso.wholesale.core.domain.WhsWarehouse;
import com.imhuso.wholesale.core.domain.vo.admin.WhsWarehouseVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 仓库Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface WhsWarehouseMapper extends BaseMapperPlus<WhsWarehouse, WhsWarehouseVo> {

    /**
     * 原子性设置默认仓库
     * 使用 CASE WHEN 条件一次性更新所有记录
     *
     * @param warehouseId 要设置为默认的仓库ID
     * @return 更新的记录数
     */
    int setDefaultWarehouseAtomic(@Param("warehouseId") Long warehouseId);
}
