package com.imhuso.wholesale.core.service.impl;

import com.imhuso.wholesale.core.domain.WhsOrder;
import com.imhuso.wholesale.core.enums.OrderStatus;
import com.imhuso.wholesale.core.enums.ShipmentApprovalStatus;
import com.imhuso.wholesale.core.enums.ShipmentStatus;
import com.imhuso.wholesale.core.service.IApprovalConfigService;
import com.imhuso.wholesale.core.service.IShipmentPermissionService;
import com.imhuso.wholesale.core.service.IWhsOrderBaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 发货权限检查服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShipmentPermissionServiceImpl implements IShipmentPermissionService {

    private final IWhsOrderBaseService orderBaseService;
    private final IApprovalConfigService approvalConfigService;

    @Override
    public ApprovalConfigServiceImpl.PermissionResult checkShipmentPermission(WhsOrder order) {
        if (order == null) {
            log.warn("订单信息为空，无法检查发货权限");
            return ApprovalConfigServiceImpl.PermissionResult.ORDER_STATUS_INVALID;
        }

        // 1. 检查订单基础状态
        ApprovalConfigServiceImpl.PermissionResult basicCheck = checkBasicOrderStatus(order);
        if (basicCheck != ApprovalConfigServiceImpl.PermissionResult.CAN_SHIP) {
            return basicCheck;
        }

        // 2. 检查审批相关权限
        return checkApprovalPermission(order);
    }

    @Override
    public String getShipmentButtonText(WhsOrder order) {
        ApprovalConfigServiceImpl.PermissionResult permission = checkShipmentPermission(order);
        return getButtonTextByPermission(permission, order);
    }

    @Override
    public boolean shouldShowApprovalButton(WhsOrder order) {
        if (!isValidOrderForShipment(order)) {
            return false;
        }

        // 审批功能未启用，不显示审批按钮
        if (!approvalConfigService.isApprovalEnabled()) {
            return false;
        }

        // 自动发起申请，不需要显示审批按钮
        if (approvalConfigService.isAutoRequest()) {
            return false;
        }

        // 需要手动发起申请的情况下，检查当前状态
        Integer approvalStatus = order.getShipmentApprovalStatus();
        if (approvalStatus == null) {
            // 数据库默认值为0，这里处理兼容性
            approvalStatus = ShipmentApprovalStatus.NO_APPROVAL.getValue();
        }

        // 无需审批或审批被拒绝/已撤销时显示审批按钮
        return approvalStatus.equals(ShipmentApprovalStatus.NO_APPROVAL.getValue())
            || approvalStatus.equals(ShipmentApprovalStatus.REJECTED.getValue())
            || approvalStatus.equals(ShipmentApprovalStatus.CANCELLED.getValue());
    }

    @Override
    public boolean shouldShowShipmentButton(WhsOrder order) {
        if (!isValidOrderForShipment(order)) {
            return false;
        }

        ApprovalConfigServiceImpl.PermissionResult permission = checkShipmentPermission(order);

        // 只有可以发货或系统处理中时显示发货按钮
        return permission == ApprovalConfigServiceImpl.PermissionResult.CAN_SHIP
            || permission == ApprovalConfigServiceImpl.PermissionResult.SYSTEM_PROCESSING;
    }

    @Override
    public boolean shouldShowCancelApprovalButton(WhsOrder order) {
        return approvalConfigService.shouldShowCancelApprovalButton(order);
    }

    @Override
    public boolean shouldShowApproveButton(WhsOrder order) {
        return approvalConfigService.shouldShowApproveButton(order);
    }

    /**
     * 检查订单基础状态
     */
    private ApprovalConfigServiceImpl.PermissionResult checkBasicOrderStatus(WhsOrder order) {
        // 检查订单状态：必须是处理中
        if (!OrderStatus.PROCESSING.getValue().equals(order.getOrderStatus())) {
            log.debug("订单状态不允许发货: orderId={}, status={}", order.getId(), order.getOrderStatus());
            return ApprovalConfigServiceImpl.PermissionResult.ORDER_STATUS_INVALID;
        }

        // 检查发货状态：必须是待发货或部分发货
        Integer shipmentStatus = order.getShipmentStatus();
        if (!ShipmentStatus.PENDING.getValue().equals(shipmentStatus)
            && !ShipmentStatus.PARTIAL_SHIPPED.getValue().equals(shipmentStatus)) {
            log.debug("发货状态不允许发货: orderId={}, shipmentStatus={}", order.getId(), shipmentStatus);
            return ApprovalConfigServiceImpl.PermissionResult.ORDER_STATUS_INVALID;
        }

        // 基础检查通过
        return ApprovalConfigServiceImpl.PermissionResult.CAN_SHIP;
    }

    /**
     * 检查审批权限
     */
    private ApprovalConfigServiceImpl.PermissionResult checkApprovalPermission(WhsOrder order) {
        // 审批功能未启用，直接可以发货
        if (!approvalConfigService.isApprovalEnabled()) {
            log.debug("审批功能未启用，允许发货: orderId={}", order.getId());
            return ApprovalConfigServiceImpl.PermissionResult.CAN_SHIP;
        }

        // 获取当前审批状态
        Integer approvalStatus = order.getShipmentApprovalStatus();
        if (approvalStatus == null) {
            // 处理数据库默认值兼容性
            approvalStatus = ShipmentApprovalStatus.NO_APPROVAL.getValue();
        }

        // 根据配置和状态判断权限
        return determinePermissionByApprovalStatus(order, approvalStatus);
    }

    /**
     * 根据审批状态确定权限
     */
    private ApprovalConfigServiceImpl.PermissionResult determinePermissionByApprovalStatus(WhsOrder order, Integer approvalStatus) {
        if (approvalStatus.equals(ShipmentApprovalStatus.APPROVED.getValue())) {
            // 已审批通过，可以发货
            return ApprovalConfigServiceImpl.PermissionResult.CAN_SHIP;
        }

        if (approvalStatus.equals(ShipmentApprovalStatus.PENDING.getValue())) {
            // 待审批状态
            return ApprovalConfigServiceImpl.PermissionResult.WAITING_APPROVAL;
        }

        if (approvalStatus.equals(ShipmentApprovalStatus.REJECTED.getValue())
            || approvalStatus.equals(ShipmentApprovalStatus.CANCELLED.getValue())) {
            // 审批被拒绝或已撤销，需要重新申请
            return ApprovalConfigServiceImpl.PermissionResult.NEED_REAPPLY;
        }

        // 无需审批状态（值为0）
        if (approvalStatus.equals(ShipmentApprovalStatus.NO_APPROVAL.getValue())) {
            return handleNoApprovalStatus();
        }

        // 未知状态，保守处理
        log.warn("未知的审批状态: orderId={}, status={}", order.getId(), approvalStatus);
        return ApprovalConfigServiceImpl.PermissionResult.NEED_REQUEST;
    }

    /**
     * 处理"无需审批"状态
     */
    private ApprovalConfigServiceImpl.PermissionResult handleNoApprovalStatus() {
        // 如果配置为自动发起申请，系统会自动处理
        if (approvalConfigService.isAutoRequest()) {
            return ApprovalConfigServiceImpl.PermissionResult.SYSTEM_PROCESSING;
        }

        // 如果配置为手动发起，需要用户申请
        return ApprovalConfigServiceImpl.PermissionResult.NEED_REQUEST;
    }

    /**
     * 根据权限结果获取按钮文本
     */
    private String getButtonTextByPermission(ApprovalConfigServiceImpl.PermissionResult permission, WhsOrder order) {
        return switch (permission) {
            case CAN_SHIP -> getShipmentButtonTextByStatus(order);
            case NEED_REQUEST -> "申请发货";
            case WAITING_APPROVAL -> "待审批";
            case NEED_REAPPLY -> "重新申请";
            default -> "发货";
        };
    }

    /**
     * 获取发货按钮文本（根据发货状态）
     */
    private String getShipmentButtonTextByStatus(WhsOrder order) {
        if (order == null) {
            return "发货";
        }

        Integer shipmentStatus = order.getShipmentStatus();
        if (ShipmentStatus.PARTIAL_SHIPPED.getValue().equals(shipmentStatus)) {
            return "继续发货";
        }

        return "发货";
    }

    /**
     * 根据权限结果获取提示信息
     */
    private String getTooltipByPermission(ApprovalConfigServiceImpl.PermissionResult permission) {
        return switch (permission) {
            case CAN_SHIP -> "可以进行发货操作";
            case NEED_REQUEST -> "需要申请发货审批";
            case WAITING_APPROVAL -> "正在等待审批，请耐心等待";
            case NEED_REAPPLY -> "审批被拒绝或已撤销，需要重新申请";
            case SYSTEM_PROCESSING -> "系统正在自动处理审批流程";
            case ORDER_STATUS_INVALID -> "当前订单状态不允许发货";
            default -> "请联系管理员处理";
        };
    }

    /**
     * 检查订单是否可以进行发货相关操作
     */
    private boolean isValidOrderForShipment(WhsOrder order) {
        if (order == null) {
            return false;
        }

        // 订单必须是待处理或处理中状态
        Integer orderStatus = order.getOrderStatus();
        if (!OrderStatus.PENDING.getValue().equals(orderStatus)
            && !OrderStatus.PROCESSING.getValue().equals(orderStatus)) {
            return false;
        }

        // 发货状态必须是待发货或部分发货
        Integer shipmentStatus = order.getShipmentStatus();
        return ShipmentStatus.PENDING.getValue().equals(shipmentStatus)
            || ShipmentStatus.PARTIAL_SHIPPED.getValue().equals(shipmentStatus);
    }
}
