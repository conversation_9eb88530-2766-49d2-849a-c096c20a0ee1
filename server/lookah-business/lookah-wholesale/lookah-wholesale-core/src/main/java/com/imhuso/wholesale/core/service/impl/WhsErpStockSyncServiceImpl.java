package com.imhuso.wholesale.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.wholesale.core.adapter.ErpProviderFactory;
import com.imhuso.wholesale.core.adapter.IErpProvider;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.bo.admin.ErpStockSyncBo;
import com.imhuso.wholesale.core.mapper.WhsProductVariantMapper;
import com.imhuso.wholesale.core.service.IWhsErpStockSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * ERP库存同步服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsErpStockSyncServiceImpl implements IWhsErpStockSyncService {

    private final WhsProductVariantMapper productVariantMapper;
    private final ErpProviderFactory erpProviderFactory;

    @Override
    public int syncAllVariantErpStock() {
        log.info(MessageUtils.message("erp.stock.sync.all.start"));

        // 查询所有启用的产品变体
        LambdaQueryWrapper<WhsProductVariant> query = Wrappers.lambdaQuery(WhsProductVariant.class)
                .eq(WhsProductVariant::getStatus, BusinessConstants.NORMAL)
                .select(WhsProductVariant::getId, WhsProductVariant::getSkuCode);

        List<WhsProductVariant> variants = productVariantMapper.selectList(query);
        if (variants.isEmpty()) {
            log.warn(MessageUtils.message("erp.stock.sync.no.variants"));
            return 0;
        }

        log.info("找到{}个有效产品变体准备同步ERP库存", variants.size());

        // 提取SKU编码列表
        List<String> skuCodes = variants.stream()
                .map(WhsProductVariant::getSkuCode)
                .collect(Collectors.toList());

        // 从ERP系统获取库存数据
        List<ErpStockSyncBo> erpStockData = fetchErpStockData(skuCodes);
        if (erpStockData.isEmpty()) {
            log.warn("从ERP系统未获取到库存数据");
            return 0;
        }

        // 建立SKU到变体ID的映射
        Map<String, Long> skuToVariantIdMap = variants.stream()
                .collect(Collectors.toMap(WhsProductVariant::getSkuCode, WhsProductVariant::getId));

        // 为ERP数据设置变体ID
        erpStockData.forEach(data -> {
            Long variantId = skuToVariantIdMap.get(data.getSkuCode());
            data.setVariantId(variantId);
        });

        // 过滤掉无效的数据（找不到对应变体的）
        List<ErpStockSyncBo> validData = erpStockData.stream()
                .filter(data -> data.getVariantId() != null)
                .collect(Collectors.toList());

        if (validData.isEmpty()) {
            log.warn("没有有效的ERP库存数据可以更新");
            return 0;
        }

        // 更新库存数据
        int updatedCount = updateVariantErpStock(validData);
        log.info("ERP库存同步完成，成功更新{}个产品变体的库存数据", updatedCount);

        return updatedCount;
    }

    @Override
    public int syncVariantErpStock(List<Long> variantIds) {
        if (variantIds == null || variantIds.isEmpty()) {
            log.warn("产品变体ID列表为空，同步终止");
            return 0;
        }

        log.info("开始同步指定产品变体的ERP库存数据，变体数量: {}", variantIds.size());

        // 查询指定的产品变体
        LambdaQueryWrapper<WhsProductVariant> query = Wrappers.lambdaQuery(WhsProductVariant.class)
                .in(WhsProductVariant::getId, variantIds)
                .eq(WhsProductVariant::getStatus, BusinessConstants.NORMAL)
                .select(WhsProductVariant::getId, WhsProductVariant::getSkuCode);

        List<WhsProductVariant> variants = productVariantMapper.selectList(query);
        if (variants.isEmpty()) {
            log.warn("没有找到指定的有效产品变体");
            return 0;
        }

        // 提取SKU编码列表
        List<String> skuCodes = variants.stream()
                .map(WhsProductVariant::getSkuCode)
                .collect(Collectors.toList());

        // 从ERP系统获取库存数据
        List<ErpStockSyncBo> erpStockData = fetchErpStockData(skuCodes);

        // 建立SKU到变体ID的映射并设置变体ID
        Map<String, Long> skuToVariantIdMap = variants.stream()
                .collect(Collectors.toMap(WhsProductVariant::getSkuCode, WhsProductVariant::getId));

        erpStockData.forEach(data -> {
            Long variantId = skuToVariantIdMap.get(data.getSkuCode());
            data.setVariantId(variantId);
        });

        // 过滤有效数据并更新
        List<ErpStockSyncBo> validData = erpStockData.stream()
                .filter(data -> data.getVariantId() != null)
                .collect(Collectors.toList());

        int updatedCount = updateVariantErpStock(validData);
        log.info("指定产品变体ERP库存同步完成，成功更新{}个变体的库存数据", updatedCount);

        return updatedCount;
    }

    @Override
    public List<ErpStockSyncBo> fetchErpStockData(List<String> skuCodes) {
        // 获取可用的ERP提供商
        Optional<IErpProvider> providerOpt = erpProviderFactory.getAvailableProvider();
        if (providerOpt.isEmpty()) {
            log.warn(MessageUtils.message("erp.provider.none.available"));
            return new ArrayList<>();
        }

        IErpProvider provider = providerOpt.get();
        log.info(MessageUtils.message("erp.stock.sync.using.provider"),
                provider.getProviderName(), skuCodes.size());

        return provider.fetchStockData(skuCodes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateVariantErpStock(List<ErpStockSyncBo> stockDataList) {
        if (stockDataList == null || stockDataList.isEmpty()) {
            return 0;
        }

        int updatedCount = 0;
        for (ErpStockSyncBo stockData : stockDataList) {
            try {
                LambdaUpdateWrapper<WhsProductVariant> updateWrapper = Wrappers.lambdaUpdate(WhsProductVariant.class)
                        .eq(WhsProductVariant::getId, stockData.getVariantId())
                        .set(WhsProductVariant::getFinishedStock, stockData.getFinishedStock())
                        .set(WhsProductVariant::getPendingStock, stockData.getPendingStock());

                int result = productVariantMapper.update(null, updateWrapper);
                if (result > 0) {
                    updatedCount++;
                    log.debug("更新产品变体[{}]的ERP库存数据成功，成品库存: {}, 待产库存: {}",
                            stockData.getVariantId(), stockData.getFinishedStock(), stockData.getPendingStock());
                }
            } catch (Exception e) {
                log.error("更新产品变体[{}]的ERP库存数据失败", stockData.getVariantId(), e);
            }
        }

        log.info("批量更新ERP库存数据完成，成功更新{}条记录", updatedCount);
        return updatedCount;
    }
}
