package com.imhuso.wholesale.core.service;

/**
 * LTL追踪API服务接口
 *
 * <AUTHOR>
 */
public interface ILtlTrackingApiService {

    /**
     * 获取LTL承运商追踪链接
     *
     * @param trackingNumber 追踪号
     * @return 承运商追踪链接，获取失败返回null
     */
    String getCarrierTrackUrl(String trackingNumber);

    /**
     * 检查追踪号是否处于失败冷却期
     *
     * @param trackingNumber 追踪号
     * @return 是否在冷却期
     */
    boolean isInFailureCooldown(String trackingNumber);

    /**
     * 检查追踪号是否处于无追踪信息状态（API成功但暂无追踪链接）
     *
     * @param trackingNumber 追踪号
     * @return 是否处于无追踪信息状态
     */
    boolean isInNoTrackingInfoState(String trackingNumber);

    /**
     * 标记追踪号获取失败
     *
     * @param trackingNumber 追踪号
     */
    void markTrackingFailed(String trackingNumber);

    /**
     * 标记追踪号暂无追踪信息（API成功但无追踪链接）
     *
     * @param trackingNumber 追踪号
     */
    void markNoTrackingInfo(String trackingNumber);
}
