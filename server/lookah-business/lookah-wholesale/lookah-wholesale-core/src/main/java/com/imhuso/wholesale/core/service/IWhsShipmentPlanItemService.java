package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.bo.admin.WhsShipmentPlanItemBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanItemVo;

import java.util.List;

/**
 * 发货方案项服务接口
 *
 * <AUTHOR>
 */
public interface IWhsShipmentPlanItemService {
    /**
     * 根据方案ID列表批量获取发货方案项视图对象
     *
     * @param planIds 方案ID列表
     * @return 发货方案项视图对象列表 (包含SKU)
     */
    List<WhsShipmentPlanItemVo> getShipmentPlanItemsByPlanIds(List<Long> planIds);

    /**
     * 根据方案ID获取发货方案项BO对象列表
     *
     * @param planId 方案ID
     * @return 发货方案项BO列表
     */
    List<WhsShipmentPlanItemBo> getShipmentPlanItemBosByPlanId(Long planId);

    /**
     * 根据订单ID删除所有发货方案项
     *
     * @param orderId 订单ID
     */
    void deleteByOrderId(Long orderId);
}
