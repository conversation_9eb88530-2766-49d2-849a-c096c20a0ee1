package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.wholesale.core.domain.*;
import com.imhuso.wholesale.core.enums.PackagingType;
import com.imhuso.wholesale.core.mapper.*;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品变体属性校验器
 * 专门负责处理变体属性相关的校验逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WhsProductVariantAttributeValidator {

    private final WhsProductVariantMapper variantMapper;
    private final WhsProductAttributeMapper attributeMapper;
    private final WhsProductAttributeValueMapper attributeValueMapper;
    private final WhsProductAttributeRelationMapper attributeRelationMapper;
    private final WhsProductVariantAttributeMapper variantAttributeMapper;

    /**
     * 校验变体属性
     *
     * @param productId    产品ID
     * @param variantId    变体ID
     * @param attributeMap 属性映射
     */
    public void validateVariantAttributes(Long productId, Long variantId, Map<Long, Long> attributeMap) {
        WhsProductVariant variant = validateVariantExists(variantId);

        // 验证变体是否属于指定产品
        validateVariantBelongsToProduct(variant, productId);

        // 包装类型变体不需要校验属性
        boolean isPackageVariant = isPackageVariant(variant);
        if (isPackageVariant || CollUtil.isEmpty(attributeMap)) {
            return;
        }

        // 获取产品关联的属性信息（缓存起来避免重复查询）
        ValidateContext context = createValidateContext(productId, attributeMap);

        // 执行各种校验
        validateAttributesBelongToProduct(context);
        validateAttributeValuesValid(context);
        validateAttributeUniqueness(context);
        validateAttributeCombinationUniqueness(context, variantId);
        validateRequiredAttributes(context);
    }

    /**
     * 创建校验上下文，包含校验过程中需要的各种数据
     * 以避免重复查询数据库
     */
    private ValidateContext createValidateContext(Long productId, Map<Long, Long> attributeMap) {
        ValidateContext context = new ValidateContext(productId, attributeMap);

        // 1. 获取产品关联的所有属性关系
        List<WhsProductAttributeRelation> relations = attributeRelationMapper.selectList(
            Wrappers.lambdaQuery(WhsProductAttributeRelation.class)
                .eq(WhsProductAttributeRelation::getProductId, productId));

        if (CollUtil.isEmpty(relations)) {
            log.warn("产品[{}]没有关联任何属性", productId);
            throw new ServiceException("产品没有关联任何属性，无法添加变体属性");
        }

        context.setRelations(relations);

        // 2. 提取产品关联的属性ID集合
        Set<Long> productAttributeIds = relations.stream()
            .map(WhsProductAttributeRelation::getAttributeId)
            .collect(Collectors.toSet());

        context.setProductAttributeIds(productAttributeIds);

        // 3. 收集所有需要查询的属性ID（包括产品关联的和用户提交的）
        Set<Long> allAttributeIds = new HashSet<>(productAttributeIds);
        if (!CollUtil.isEmpty(attributeMap)) {
            allAttributeIds.addAll(attributeMap.keySet());
        }

        // 4. 获取所有属性详情
        List<WhsProductAttribute> attributes = attributeMapper.selectList(
            Wrappers.lambdaQuery(WhsProductAttribute.class)
                .in(WhsProductAttribute::getId, allAttributeIds));

        Map<Long, WhsProductAttribute> attributesMap = attributes.stream()
            .collect(Collectors.toMap(WhsProductAttribute::getId, a -> a, (a1, a2) -> a1));

        context.setAttributesMap(attributesMap);

        // 5. 获取所有属性值详情
        if (!CollUtil.isEmpty(attributeMap)) {
            // 收集所有需要查询的属性值ID
            Set<Long> allValueIds = new HashSet<>(attributeMap.values());

            List<WhsProductAttributeValue> values = attributeValueMapper.selectList(
                Wrappers.lambdaQuery(WhsProductAttributeValue.class)
                    .in(WhsProductAttributeValue::getId, allValueIds));

            Map<Long, WhsProductAttributeValue> valuesMap = values.stream()
                .collect(Collectors.toMap(WhsProductAttributeValue::getId, v -> v, (v1, v2) -> v1));

            context.setAttributeValuesMap(valuesMap);
        }

        return context;
    }

    /**
     * 验证变体是否存在
     */
    private WhsProductVariant validateVariantExists(Long variantId) {
        if (variantId == null) {
            throw new ServiceException("变体ID不能为空");
        }

        WhsProductVariant variant = variantMapper.selectById(variantId);
        if (variant == null) {
            log.warn("变体[{}]不存在", variantId);
            throw new ServiceException("变体不存在");
        }

        return variant;
    }

    /**
     * 验证变体是否属于指定产品
     */
    private void validateVariantBelongsToProduct(WhsProductVariant variant, Long productId) {
        if (productId == null) {
            throw new ServiceException("产品ID不能为空");
        }

        if (!Objects.equals(variant.getProductId(), productId)) {
            log.warn("变体[{}]不属于产品[{}]", variant.getId(), productId);
            throw new ServiceException("变体不属于当前产品");
        }
    }

    /**
     * 判断是否为包装类型变体
     */
    private boolean isPackageVariant(WhsProductVariant variant) {
        return variant.getPackagingType() != null && !variant.getPackagingType().equals(PackagingType.INDIVIDUAL.getValue());
    }

    /**
     * 检查提交的属性是否都属于该产品
     */
    private void validateAttributesBelongToProduct(ValidateContext context) {
        for (Long attributeId : context.getAttributeMap().keySet()) {
            if (!context.getProductAttributeIds().contains(attributeId)) {
                // 获取属性信息，提供更友好的错误提示
                WhsProductAttribute attribute = context.getAttributesMap().get(attributeId);
                String attrName = attribute != null ? attribute.getAttrDisplay() : String.valueOf(attributeId);

                log.warn("属性[{}]不属于产品[{}]", attrName, context.getProductId());
                throw new ServiceException("属性[" + attrName + "]不属于当前产品，无法添加");
            }
        }
    }

    /**
     * 检查属性值是否属于对应的属性
     */
    private void validateAttributeValuesValid(ValidateContext context) {
        for (Map.Entry<Long, Long> entry : context.getAttributeMap().entrySet()) {
            Long attributeId = entry.getKey();
            Long valueId = entry.getValue();

            // 获取属性和属性值信息
            WhsProductAttribute attribute = context.getAttributesMap().get(attributeId);
            WhsProductAttributeValue value = context.getAttributeValuesMap().get(valueId);

            // 验证该属性值是否属于该属性
            if (value == null || !Objects.equals(value.getAttributeId(), attributeId)) {
                String attrName = attribute != null ? attribute.getAttrDisplay() : String.valueOf(attributeId);
                String valueName = value != null ? value.getValueDisplay() : String.valueOf(valueId);

                log.warn("属性值[{}]不属于属性[{}]", valueName, attrName);
                throw new ServiceException("属性值[" + valueName + "]不属于属性[" + attrName + "]，请选择正确的属性值");
            }
        }
    }

    /**
     * 检查属性唯一性，确保同一个属性不会被添加多次
     */
    private void validateAttributeUniqueness(ValidateContext context) {
        // 检查是否有重复的属性名称
        Map<String, Long> attributeNameMap = new HashMap<>();

        for (Long attributeId : context.getAttributeMap().keySet()) {
            WhsProductAttribute attr = context.getAttributesMap().get(attributeId);
            if (attr != null && attr.getAttrDisplay() != null) {
                String attrName = attr.getAttrDisplay();
                if (attributeNameMap.containsKey(attrName)) {
                    Long duplicateAttrId = attributeNameMap.get(attrName);

                    log.warn("发现重复的属性名称: {}, 属性ID: {} 和 {}", attrName, duplicateAttrId, attributeId);
                    throw new ServiceException("变体属性[" + attrName + "]重复，同一个属性不能添加多次");
                }
                attributeNameMap.put(attrName, attributeId);
            }
        }
    }

    /**
     * 检查变体属性组合的唯一性，确保同一产品下不会有相同属性组合的变体
     */
    private void validateAttributeCombinationUniqueness(ValidateContext context, Long variantId) {
        // 如果当前正在编辑的变体ID为空（新增场景），则不需要排除任何变体
        if (variantId == null) {
            validateNewAttributeCombinationUniqueness(context);
            return;
        }

        // 编辑场景，需要先获取当前变体的原有属性，以判断属性是否有变化
        List<WhsProductVariantAttribute> currentVariantAttributes = variantAttributeMapper.selectList(
            Wrappers.lambdaQuery(WhsProductVariantAttribute.class)
                .eq(WhsProductVariantAttribute::getVariantId, variantId));

        // 如果当前变体没有属性，直接使用新增逻辑
        if (CollUtil.isEmpty(currentVariantAttributes)) {
            validateNewAttributeCombinationUniqueness(context);
            return;
        }

        // 构建当前变体的原有属性映射
        Map<Long, Long> currentAttributeMap = currentVariantAttributes.stream()
            .collect(Collectors.toMap(
                WhsProductVariantAttribute::getAttributeId,
                WhsProductVariantAttribute::getAttributeValueId,
                (v1, v2) -> v1)); // 如果有重复，保留第一个

        // 比较新旧属性组合，如果没有变化，则不需要进一步校验
        if (isAttributeCombinationSame(context.getAttributeMap(), currentAttributeMap)) {
            log.info("变体[{}]的属性组合没有变化，跳过唯一性检查", variantId);
            return;
        }

        // 属性有变化，需要验证新的属性组合是否与其他变体冲突
        validateNewAttributeCombinationUniqueness(context, variantId);
    }

    /**
     * 检查新变体的属性组合唯一性（新增场景）
     */
    private void validateNewAttributeCombinationUniqueness(ValidateContext context) {
        validateNewAttributeCombinationUniqueness(context, null);
    }

    /**
     * 检查新变体的属性组合唯一性（支持排除指定变体）
     */
    private void validateNewAttributeCombinationUniqueness(ValidateContext context, Long excludeVariantId) {
        // 1. 获取当前产品下的所有单品变体(排除指定变体)
        List<WhsProductVariant> variants = variantMapper.selectList(
            Wrappers.lambdaQuery(WhsProductVariant.class)
                .eq(WhsProductVariant::getProductId, context.getProductId())
                .eq(WhsProductVariant::getPackagingType, PackagingType.INDIVIDUAL.getValue())
                .ne(excludeVariantId != null, WhsProductVariant::getId, excludeVariantId));

        if (CollUtil.isEmpty(variants)) {
            return;
        }

        // 2. 获取这些变体的ID列表
        List<Long> otherVariantIds = variants.stream()
            .map(WhsProductVariant::getId)
            .collect(Collectors.toList());

        // 3. 获取所有这些变体的属性关联
        List<WhsProductVariantAttribute> allVariantAttributes = variantAttributeMapper.selectList(
            Wrappers.lambdaQuery(WhsProductVariantAttribute.class)
                .in(WhsProductVariantAttribute::getVariantId, otherVariantIds));

        if (CollUtil.isEmpty(allVariantAttributes)) {
            return;
        }

        // 4. 按变体ID分组整理属性组合
        Map<Long, Map<Long, Long>> variantAttributeMaps = new HashMap<>();
        for (WhsProductVariantAttribute attr : allVariantAttributes) {
            variantAttributeMaps
                .computeIfAbsent(attr.getVariantId(), k -> new HashMap<>())
                .put(attr.getAttributeId(), attr.getAttributeValueId());
        }

        // 5. 比较是否有相同的属性组合
        for (Map.Entry<Long, Map<Long, Long>> entry : variantAttributeMaps.entrySet()) {
            if (isAttributeCombinationSame(context.getAttributeMap(), entry.getValue())) {
                Long existingVariantId = entry.getKey();
                WhsProductVariant existingVariant = variants.stream()
                    .filter(v -> Objects.equals(v.getId(), existingVariantId))
                    .findFirst()
                    .orElse(null);

                String existingSkuInfo = existingVariant != null ? existingVariant.getSkuCode()
                    : String.valueOf(existingVariantId);

                // 构建属性组合的可读描述
                StringBuilder attributes = buildAttributeDescription(context);

                log.warn("发现相同属性组合的变体: {}, 属性组合: {}", existingSkuInfo, attributes);
                throw new ServiceException("已存在相同属性组合的变体(" + existingSkuInfo + ")，请修改属性组合");
            }
        }
    }

    /**
     * 构建属性描述
     */
    private StringBuilder buildAttributeDescription(ValidateContext context) {
        StringBuilder attributes = new StringBuilder();
        for (Map.Entry<Long, Long> entry : context.getAttributeMap().entrySet()) {
            WhsProductAttribute attr = context.getAttributesMap().get(entry.getKey());
            WhsProductAttributeValue value = context.getAttributeValuesMap().get(entry.getValue());

            if (attr != null && value != null) {
                if (!attributes.isEmpty()) {
                    attributes.append(", ");
                }
                attributes.append(attr.getAttrDisplay()).append(": ").append(value.getValueDisplay());
            }
        }
        return attributes;
    }

    /**
     * 比较两个属性组合是否相同
     */
    private boolean isAttributeCombinationSame(Map<Long, Long> combo1, Map<Long, Long> combo2) {
        if (combo1.size() != combo2.size()) {
            return false;
        }

        for (Map.Entry<Long, Long> entry : combo1.entrySet()) {
            Long attrId = entry.getKey();
            Long valueId = entry.getValue();

            if (!combo2.containsKey(attrId) || !Objects.equals(combo2.get(attrId), valueId)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查必填属性是否都有值
     */
    private void validateRequiredAttributes(ValidateContext context) {
        for (WhsProductAttribute attr : context.getAttributesMap().values()) {
            // 检查是否为必填属性 (isRequired为1表示必填)
            if ("1".equals(attr.getIsRequired())) {
                if (!context.getAttributeMap().containsKey(attr.getId())) {
                    log.warn("缺少必填属性: {}", attr.getAttrDisplay());
                    throw new ServiceException("缺少必填属性: " + attr.getAttrDisplay());
                }
            }
        }
    }

    /**
     * 校验上下文类，用于在不同校验方法间共享数据，避免重复查询
     */
    @Data
    private static class ValidateContext {
        private final Long productId;
        private final Map<Long, Long> attributeMap;
        private List<WhsProductAttributeRelation> relations;
        private Set<Long> productAttributeIds;
        private Map<Long, WhsProductAttribute> attributesMap = new HashMap<>();
        private Map<Long, WhsProductAttributeValue> attributeValuesMap = new HashMap<>();

        public ValidateContext(Long productId, Map<Long, Long> attributeMap) {
            this.productId = productId;
            this.attributeMap = attributeMap;
        }
    }
}
