package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.*;

import java.io.Serial;

/**
 * 仓库物流方式实体类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@TableName("whs_warehouse_logistics_method")
public class WhsWarehouseLogisticsMethod extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 提供商ID
     */
    private Long providerId;

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 物流方式代码
     */
    private String methodCode;

    /**
     * 物流方式名称
     */
    private String methodName;

    /**
     * 渠道ID(第三方系统)
     */
    private String channelId;

    /**
     * 状态（0停用 1启用）
     */
    private String status;

    /**
     * 优先级(1-10，数字越小优先级越高)
     */
    private Integer priority;
}
