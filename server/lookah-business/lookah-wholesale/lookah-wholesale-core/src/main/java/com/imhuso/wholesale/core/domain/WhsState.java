package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 州/省对象
 *
 * <AUTHOR>
 */
@Data
@TableName("whs_state")
public class WhsState {

    /**
     * 州/省ID
     */
    @TableId
    private Long id;

    /**
     * 国家ID
     */
    private Long countryId;

    /**
     * 州/省代码
     */
    private String code;

    /**
     * 州/省名称
     */
    private String name;

    /**
     * 州/省中文名称
     */
    private String nameZh;

    /**
     * 是否启用（0正常 1停用）
     */
    private String status;

    /**
     * 排序
     */
    private Integer sort;
}
