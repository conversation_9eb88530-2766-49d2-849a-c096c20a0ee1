package com.imhuso.wholesale.core.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.mybatis.core.mapper.BaseMapperPlus;
import com.imhuso.wholesale.core.domain.WhsInboundOrder;
import com.imhuso.wholesale.core.domain.vo.admin.WhsInboundOrderVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 入库单Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface WhsInboundOrderMapper extends BaseMapperPlus<WhsInboundOrder, WhsInboundOrderVo> {

    /**
     * 根据产品ID分页查询相关的在途入库单
     *
     * @param page         分页参数
     * @param queryWrapper 查询条件
     * @param productId    产品ID
     * @return 分页结果
     */
    Page<WhsInboundOrderVo> selectInboundOrdersByProductId(Page<WhsInboundOrderVo> page,
            @Param(Constants.WRAPPER) Wrapper<WhsInboundOrder> queryWrapper, @Param("productId") Long productId);

    /**
     * 根据变体ID分页查询相关的在途入库单
     *
     * @param page         分页参数
     * @param queryWrapper 查询条件
     * @param variantId    变体ID
     * @return 分页结果
     */
    Page<WhsInboundOrderVo> selectInboundOrdersByVariantId(Page<WhsInboundOrderVo> page,
            @Param(Constants.WRAPPER) Wrapper<WhsInboundOrder> queryWrapper, @Param("variantId") Long variantId);
}
