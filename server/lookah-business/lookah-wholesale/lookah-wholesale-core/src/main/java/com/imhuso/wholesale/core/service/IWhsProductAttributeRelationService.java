package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.WhsProductAttributeRelation;

import java.util.List;

/**
 * 产品属性关联Service接口
 *
 * <AUTHOR>
 */
public interface IWhsProductAttributeRelationService {
    /**
     * 删除产品的所有属性关联
     *
     * @param productId 产品ID
     */
    void removeRelationsByProductId(Long productId);

    /**
     * 批量获取产品的属性关联列表
     *
     * @param productIds 产品ID列表
     * @return 属性关联列表
     */
    List<WhsProductAttributeRelation> getRelationsByProductIds(List<Long> productIds);

    /**
     * 处理产品属性关联（增量更新）
     *
     * @param productId    产品ID
     * @param attributeIds 属性ID列表
     */
    void processProductAttributes(Long productId, List<Long> attributeIds);

    /**
     * 检查属性是否被产品使用
     *
     * @param attributeId 属性ID
     * @return 如果被使用，返回true；否则返回false
     */
    boolean isAttributeUsedByProducts(Long attributeId);
}
