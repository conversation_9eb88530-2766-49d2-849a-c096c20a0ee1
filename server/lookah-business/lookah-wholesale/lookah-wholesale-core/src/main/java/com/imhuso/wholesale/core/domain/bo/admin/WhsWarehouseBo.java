package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.wholesale.core.domain.WhsWarehouse;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 仓库业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WhsWarehouse.class)
public class WhsWarehouseBo extends BaseEntity {

    /**
     * 仓库ID
     */
    @NotNull(message = "wholesale.admin.warehouse.id.not.null", groups = {EditGroup.class})
    private Long id;

    /**
     * 仓库名称
     */
    @NotBlank(message = "wholesale.admin.warehouse.name.not.blank", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 100, message = "wholesale.admin.warehouse.name.length", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 仓库编码
     */
    @NotBlank(message = "wholesale.admin.warehouse.code.not.blank", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 50, message = "wholesale.admin.warehouse.code.length", groups = {AddGroup.class, EditGroup.class})
    private String code;

    /**
     * 仓库地址
     */
    @Size(max = 255, message = "wholesale.admin.warehouse.address.length", groups = {AddGroup.class, EditGroup.class})
    private String address;

    /**
     * 联系人
     */
    @Size(max = 100, message = "wholesale.admin.warehouse.contact.length", groups = {AddGroup.class, EditGroup.class})
    private String contact;

    /**
     * 联系电话
     */
    @Size(max = 20, message = "wholesale.admin.warehouse.phone.length", groups = {AddGroup.class, EditGroup.class})
    private String phone;

    /**
     * 状态（0停用 1正常）
     */
    private String status;

    /**
     * 是否默认（N否 Y是）
     */
    private String isDefault;

    /**
     * 仓库级别告警库存阈值
     * 默认为0表示使用变体级别告警库存
     */
    private Integer alertStock;
}
