package com.imhuso.wholesale.core.domain.vo.admin;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

import com.imhuso.wholesale.core.domain.WhsMemberPermission;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

/**
 * 客户权限视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsMemberPermission.class)
public class WhsMemberPermissionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 权限ID
     */
    private Long id;

    /**
     * 客户ID
     */
    private Long memberId;

    /**
     * 权限键
     */
    private String permissionKey;

    /**
     * 权限值
     */
    private String permissionValue;

    /**
     * 帐号状态（0停用 1正常）
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
