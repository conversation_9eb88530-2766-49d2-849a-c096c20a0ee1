package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imhuso.wholesale.core.domain.WhsPackageItem;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.mapper.WhsPackageItemMapper;
import com.imhuso.wholesale.core.mapper.WhsProductVariantMapper;
import com.imhuso.wholesale.core.service.IWhsPackageItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 产品包装明细Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsPackageItemServiceImpl implements IWhsPackageItemService {

    private final WhsPackageItemMapper packageItemMapper;
    private final WhsProductVariantMapper variantMapper;

    @Override
    public void savePackageItem(Long parentVariantId, Long childVariantId, Integer quantity) {
        if (parentVariantId == null || childVariantId == null || quantity == null || quantity <= 0) {
            return;
        }

        // 检查是否已存在
        LambdaQueryWrapper<WhsPackageItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhsPackageItem::getParentVariantId, parentVariantId).eq(WhsPackageItem::getChildVariantId, childVariantId);
        if (packageItemMapper.selectCount(queryWrapper) > 0) {
            // 更新数量
            WhsPackageItem existItem = packageItemMapper.selectOne(queryWrapper);
            existItem.setQuantity(quantity);
            packageItemMapper.updateById(existItem);
        } else {
            // 创建新记录
            WhsPackageItem packageItem = new WhsPackageItem();
            packageItem.setParentVariantId(parentVariantId);
            packageItem.setChildVariantId(childVariantId);
            packageItem.setQuantity(quantity);
            packageItemMapper.insert(packageItem);
        }
    }

    @Override
    public void deleteByParentVariantId(Long parentVariantId) {
        if (parentVariantId == null) {
            return;
        }

        LambdaQueryWrapper<WhsPackageItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhsPackageItem::getParentVariantId, parentVariantId);
        packageItemMapper.delete(queryWrapper);
    }

    @Override
    public List<Long> getChildVariantIds(Long parentVariantId) {
        if (parentVariantId == null) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<WhsPackageItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhsPackageItem::getParentVariantId, parentVariantId);
        return packageItemMapper.selectList(queryWrapper).stream().map(WhsPackageItem::getChildVariantId).collect(Collectors.toList());
    }

    @Override
    public boolean existsPackageForSourceAndType(Long sourceVariantId, Integer packagingType, Long excludeParentId) {
        if (sourceVariantId == null || packagingType == null) {
            return false;
        }

        // 查询所有包含该源变体作为内容物的包装变体
        LambdaQueryWrapper<WhsPackageItem> itemQuery = new LambdaQueryWrapper<>();
        itemQuery.eq(WhsPackageItem::getChildVariantId, sourceVariantId);

        // 如果提供了要排除的父变体ID，则排除该ID
        if (excludeParentId != null) {
            itemQuery.ne(WhsPackageItem::getParentVariantId, excludeParentId);
        }

        List<WhsPackageItem> items = packageItemMapper.selectList(itemQuery);

        if (items.isEmpty()) {
            return false;
        }

        // 获取所有包装变体ID
        List<Long> packageVariantIds = items.stream().map(WhsPackageItem::getParentVariantId).collect(Collectors.toList());

        // 查询这些包装变体中是否有指定类型的
        LambdaQueryWrapper<WhsProductVariant> variantQuery = new LambdaQueryWrapper<>();
        variantQuery.in(WhsProductVariant::getId, packageVariantIds).eq(WhsProductVariant::getPackagingType, packagingType);

        return variantMapper.selectCount(variantQuery) > 0;
    }

    @Override
    public Map<Long, Integer> getChildVariantQuantities(Long parentVariantId) {
        if (parentVariantId == null) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<WhsPackageItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhsPackageItem::getParentVariantId, parentVariantId);
        return getVariantQuantitiesMap(queryWrapper, WhsPackageItem::getChildVariantId);
    }

    @Override
    public boolean isChildInAnyPackage(Long childVariantId) {
        if (childVariantId == null) {
            return false;
        }

        LambdaQueryWrapper<WhsPackageItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhsPackageItem::getChildVariantId, childVariantId);
        return packageItemMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public Map<Long, Map<Long, Integer>> getAllPackageRelationships(List<Long> variantIds) {
        if (CollUtil.isEmpty(variantIds)) {
            return Collections.emptyMap();
        }

        // 查询这些变体相关的所有包装关系
        LambdaQueryWrapper<WhsPackageItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(WhsPackageItem::getParentVariantId, variantIds);
        List<WhsPackageItem> packageItems = packageItemMapper.selectList(queryWrapper);

        if (CollUtil.isEmpty(packageItems)) {
            return Collections.emptyMap();
        }

        // 构建并返回父变体ID到子变体及数量的映射关系
        return buildPackageRelationshipMap(packageItems);
    }

    /**
     * 构建父变体ID到子变体及数量的映射关系
     *
     * @param packageItems 包装明细列表
     * @return 父变体ID到子变体及数量的映射关系
     */
    private Map<Long, Map<Long, Integer>> buildPackageRelationshipMap(List<WhsPackageItem> packageItems) {
        Map<Long, Map<Long, Integer>> result = new HashMap<>(packageItems.size());

        for (WhsPackageItem item : packageItems) {
            Long parentVariantId = item.getParentVariantId();
            Long childVariantId = item.getChildVariantId();
            Integer quantity = item.getQuantity();

            // 确保数量有效
            if (quantity == null || quantity <= 0) {
                quantity = 1;
            }

            // 向结果map中添加关系
            Map<Long, Integer> childMap = result.computeIfAbsent(parentVariantId, k -> new HashMap<>());
            childMap.put(childVariantId, quantity);
        }

        return result;
    }

    /**
     * 通用方法：根据查询条件获取变体数量映射
     *
     * @param queryWrapper 查询条件
     * @param keyExtractor 从WhsPackageItem中提取ID的函数
     * @return 变体ID到数量的映射
     */
    private Map<Long, Integer> getVariantQuantitiesMap(LambdaQueryWrapper<WhsPackageItem> queryWrapper,
                                                       java.util.function.Function<WhsPackageItem, Long> keyExtractor) {
        List<WhsPackageItem> items = packageItemMapper.selectList(queryWrapper);

        Map<Long, Integer> result = new HashMap<>();
        for (WhsPackageItem item : items) {
            result.put(keyExtractor.apply(item), item.getQuantity());
        }
        return result;
    }
}
