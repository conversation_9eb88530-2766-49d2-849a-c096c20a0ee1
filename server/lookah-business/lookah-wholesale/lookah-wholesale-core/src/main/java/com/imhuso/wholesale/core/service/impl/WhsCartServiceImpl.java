package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.constant.MemberPermissionConstants;
import com.imhuso.wholesale.core.domain.WhsCart;
import com.imhuso.wholesale.core.domain.WhsProduct;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.bo.front.CartBo;
import com.imhuso.wholesale.core.domain.bo.front.CartCheckResultBo;
import com.imhuso.wholesale.core.domain.bo.front.CartItemBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVariantVo;
import com.imhuso.wholesale.core.domain.vo.front.CartVo;
import com.imhuso.wholesale.core.mapper.WhsCartMapper;
import com.imhuso.wholesale.core.mapper.WhsProductMapper;
import com.imhuso.wholesale.core.mapper.WhsProductVariantMapper;
import com.imhuso.wholesale.core.satoken.WholesaleLoginHelper;
import com.imhuso.wholesale.core.service.IWhsCartService;
import com.imhuso.wholesale.core.service.IWhsPackageItemService;
import com.imhuso.wholesale.core.service.IWhsProductVariantService;
import com.imhuso.wholesale.core.service.IWhsStockService;
import com.imhuso.wholesale.core.utils.MemberPermissionHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 批发购物车Service实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsCartServiceImpl implements IWhsCartService {

    private final WhsCartMapper cartMapper;
    private final WhsProductMapper productMapper;
    private final WhsProductVariantMapper variantMapper;
    private final IWhsProductVariantService variantService;
    private final IWhsPackageItemService packageItemService;
    private final IWhsStockService stockService;
    private final MemberPermissionHelper permissionHelper;

    /**
     * 商品状态-启用
     */
    private static final String STATUS_ENABLE = BusinessConstants.NORMAL;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(CartBo bo) {
        if (CollUtil.isEmpty(bo.getItems())) {
            return;
        }

        Long memberId = WholesaleLoginHelper.getUserId();
        log.info("添加购物车，会员ID：{}，商品数量：{}", memberId, bo.getItems().size());

        // 获取所有变体ID
        List<Long> variantIds = bo.getItems().stream()
            .map(CartItemBo::getVariantId)
            .collect(Collectors.toList());

        // 加载购物车相关数据
        Set<Long> productIds = loadProductIdsFromVariants(variantIds);
        CartDataContext dataContext = loadCartData(productIds, variantIds);

        // 验证购物车项
        validateCartItems(bo.getItems(), dataContext);

        // 查询已存在的购物车项
        List<WhsCart> existingCarts = getExistingCarts(memberId, variantIds);
        Map<Long, WhsCart> existingCartMap = existingCarts.stream()
            .collect(Collectors.toMap(WhsCart::getVariantId, c -> c, (c1, c2) -> c1));

        // 准备购物车操作
        List<WhsCart> cartsToInsert = new ArrayList<>();
        List<WhsCart> cartsToUpdate = new ArrayList<>();

        for (CartItemBo item : bo.getItems()) {
            WhsCart existCart = existingCartMap.get(item.getVariantId());

            if (existCart != null) {
                // 已存在，更新数量
                existCart.setQuantity(existCart.getQuantity() + item.getQuantity());
                cartsToUpdate.add(existCart);
            } else {
                // 不存在，新增
                WhsCart cart = new WhsCart();
                cart.setMemberId(memberId);
                cart.setVariantId(item.getVariantId());
                cart.setQuantity(item.getQuantity());

                // 设置产品ID
                WhsProductVariantVo variant = dataContext.variantMap.containsKey(item.getVariantId())
                    ? MapstructUtils.convert(dataContext.variantMap.get(item.getVariantId()),
                    WhsProductVariantVo.class)
                    : null;

                if (variant != null) {
                    cart.setProductId(variant.getProductId());
                }

                cartsToInsert.add(cart);
            }
        }

        // 执行批量操作
        executeBatchCartOperations(cartsToUpdate, cartsToInsert);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateQuantity(Long cartId, Integer quantity) {
        Long memberId = WholesaleLoginHelper.getUserId();
        log.info("更新购物车数量，会员ID：{}，购物车ID：{}，数量：{}", memberId, cartId, quantity);

        // 查询购物车项
        WhsCart cart = getCartByIdAndMemberId(cartId, memberId);

        // 检查库存
        checkStockForCart(cart, quantity);

        // 更新数量
        cart.setQuantity(quantity);
        cartMapper.updateById(cart);
        log.debug("购物车数量已更新，ID：{}，数量：{}", cartId, quantity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> cartIds) {
        if (CollUtil.isEmpty(cartIds)) {
            return;
        }

        Long memberId = WholesaleLoginHelper.getUserId();
        log.info("删除购物车，会员ID：{}，购物车ID：{}", memberId, cartIds);

        LambdaUpdateWrapper<WhsCart> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(WhsCart::getId, cartIds).eq(WhsCart::getMemberId, memberId);

        int count = cartMapper.delete(updateWrapper);
        log.debug("已删除{}个购物车项", count);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void clear() {
        Long memberId = WholesaleLoginHelper.getUserId();
        log.info("清空购物车，会员ID：{}", memberId);

        LambdaUpdateWrapper<WhsCart> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WhsCart::getMemberId, memberId);

        int count = cartMapper.delete(updateWrapper);
        log.debug("已清空购物车，删除{}个购物车项", count);
    }

    @Override
    public List<CartVo> list() {
        Long memberId = WholesaleLoginHelper.getUserId();
        log.info("查询购物车列表，会员ID：{}", memberId);

        // 查询购物车列表
        List<WhsCart> cartList = getCartListByMemberId(memberId);

        if (CollUtil.isEmpty(cartList)) {
            return new ArrayList<>();
        }

        // 提取所有产品ID和变体ID
        Set<Long> productIds = cartList.stream()
            .map(WhsCart::getProductId)
            .collect(Collectors.toSet());

        List<Long> variantIds = cartList.stream()
            .map(WhsCart::getVariantId)
            .collect(Collectors.toList());

        // 加载产品、变体和库存数据
        CartDataContext dataContext = loadCartData(productIds, variantIds);

        // 筛选有效的购物车项并处理无效购物车项
        List<WhsCart> validCarts = processCartList(cartList, dataContext, memberId);

        // 转换为CartVo
        return convertCartsToVos(validCarts, dataContext);
    }

    @Override
    public CartCheckResultBo getAndCheckCartItems() {
        List<CartVo> cartItems = list();
        if (CollUtil.isEmpty(cartItems)) {
            throw new ServiceException(MessageUtils.message("wholesale.cart.empty"));
        }

        // 校验购物车商品
        checkCartItems(cartItems);

        // 构建结果对象
        CartCheckResultBo result = new CartCheckResultBo();
        result.setCartItems(cartItems);
        result.setProductMap(getProductMapForCartItems(cartItems));

        return result;
    }

    @Override
    public void checkCartItems(List<CartVo> cartItems) {
        if (CollUtil.isEmpty(cartItems)) {
            throw new ServiceException(MessageUtils.message("wholesale.cart.empty"));
        }

        // 获取所有变体ID和产品ID
        Set<Long> productIds = cartItems.stream()
            .map(CartVo::getProductId)
            .collect(Collectors.toSet());

        List<Long> variantIds = cartItems.stream()
            .map(CartVo::getVariantId)
            .collect(Collectors.toList());

        // 加载产品、变体和库存数据
        CartDataContext dataContext = loadCartData(productIds, variantIds);

        // 校验并收集错误
        validateCartVoItems(cartItems, dataContext);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchInsertCarts(List<WhsCart> cartList) {
        if (CollUtil.isEmpty(cartList)) {
            return false;
        }
        return cartMapper.insertBatch(cartList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateCarts(List<WhsCart> cartList) {
        if (CollUtil.isEmpty(cartList)) {
            return false;
        }
        return cartMapper.updateBatchById(cartList);
    }

    /**
     * 获取购物车项的产品映射
     */
    private Map<Long, WhsProduct> getProductMapForCartItems(List<CartVo> cartItems) {
        Set<Long> productIds = cartItems.stream().map(CartVo::getProductId).collect(Collectors.toSet());
        List<WhsProduct> products = productMapper.selectByIds(productIds);
        return products.stream().collect(Collectors.toMap(WhsProduct::getId, product -> product, (p1, p2) -> p1));
    }

    /**
     * 购物车数据上下文，封装购物车相关数据
     */
    private record CartDataContext(Map<Long, WhsProduct> productMap, Map<Long, WhsProductVariant> variantMap,
                                   Map<Long, Integer> stockMap, Map<Long, Map<Long, Integer>> packageRelationships) {
    }

    /**
     * 从变体ID列表中加载产品ID集合
     */
    private Set<Long> loadProductIdsFromVariants(List<Long> variantIds) {
        if (CollUtil.isEmpty(variantIds)) {
            return Collections.emptySet();
        }

        List<WhsProductVariantVo> variants = variantService.getVariantsByIds(variantIds, true);
        if (CollUtil.isEmpty(variants)) {
            throw new ServiceException(MessageUtils.message("wholesale.product.variant.not.exists.batch"));
        }

        return variants.stream()
            .map(WhsProductVariantVo::getProductId)
            .collect(Collectors.toSet());
    }

    /**
     * 加载购物车相关数据（产品、变体、库存）
     */
    private CartDataContext loadCartData(Set<Long> productIds, List<Long> variantIds) {
        if (CollUtil.isEmpty(productIds) || CollUtil.isEmpty(variantIds)) {
            return new CartDataContext(
                Collections.emptyMap(),
                Collections.emptyMap(),
                Collections.emptyMap(),
                Collections.emptyMap());
        }

        // 查询产品信息
        List<WhsProduct> products = productMapper.selectByIds(productIds);
        Map<Long, WhsProduct> productMap = products.stream()
            .collect(Collectors.toMap(WhsProduct::getId, p -> p, (p1, p2) -> p1));

        // 查询变体信息
        List<WhsProductVariant> variants = variantMapper.selectByIds(variantIds);
        Map<Long, WhsProductVariant> variantMap = variants.stream()
            .collect(Collectors.toMap(WhsProductVariant::getId, v -> v, (v1, v2) -> v1));

        // 查询库存信息
        Map<Long, Integer> stockMap = stockService.getAvailableStocks(variantIds);

        // 获取包装关系信息
        Map<Long, Map<Long, Integer>> packageRelationships = packageItemService.getAllPackageRelationships(variantIds);

        return new CartDataContext(productMap, variantMap, stockMap, packageRelationships);
    }

    /**
     * 获取已存在的购物车项
     */
    private List<WhsCart> getExistingCarts(Long memberId, List<Long> variantIds) {
        if (CollUtil.isEmpty(variantIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<WhsCart> existsQuery = new LambdaQueryWrapper<>();
        existsQuery.eq(WhsCart::getMemberId, memberId).in(WhsCart::getVariantId, variantIds);
        return cartMapper.selectList(existsQuery);
    }

    /**
     * 根据ID和会员ID获取购物车项
     */
    private WhsCart getCartByIdAndMemberId(Long cartId, Long memberId) {
        LambdaQueryWrapper<WhsCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhsCart::getId, cartId).eq(WhsCart::getMemberId, memberId);
        WhsCart cart = cartMapper.selectOne(queryWrapper);

        if (cart == null) {
            throw new ServiceException(MessageUtils.message("wholesale.cart.not.exists"));
        }

        return cart;
    }

    /**
     * 根据会员ID获取购物车列表
     */
    private List<WhsCart> getCartListByMemberId(Long memberId) {
        LambdaQueryWrapper<WhsCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhsCart::getMemberId, memberId).orderByDesc(WhsCart::getCreateTime);
        return cartMapper.selectList(queryWrapper);
    }

    /**
     * 检查购物车项库存
     */
    private void checkStockForCart(WhsCart cart, Integer quantity) {
        List<Long> singleId = List.of(cart.getVariantId());
        Map<Long, Integer> stockMap = stockService.getAvailableStocks(singleId);
        Integer availableStock = stockMap.getOrDefault(cart.getVariantId(), 0);

        if (availableStock < quantity) {
            // 获取产品名称用于错误提示
            WhsProductVariant variant = variantMapper.selectById(cart.getVariantId());
            WhsProduct product = variant != null ? productMapper.selectById(cart.getProductId()) : null;
            String itemName = product != null ? product.getItemName() : "";

            // 获取当前登录的客户ID
            Long memberId = WholesaleLoginHelper.getUserId();

            // 检查是否有显示库存权限
            boolean showStock = memberId != null &&
                permissionHelper.hasPermission(memberId, MemberPermissionConstants.SHOW_STOCK);

            if (showStock) {
                throw new ServiceException(
                    MessageUtils.message("wholesale.product.stock.insufficient", itemName, availableStock, quantity));
            } else {
                throw new ServiceException(
                    MessageUtils.message("wholesale.product.stock.insufficient.no.permission", itemName));
            }
        }
    }

    /**
     * 执行批量购物车操作
     */
    private void executeBatchCartOperations(List<WhsCart> cartsToUpdate, List<WhsCart> cartsToInsert) {
        // 执行批量更新
        if (!cartsToUpdate.isEmpty()) {
            boolean success = cartMapper.updateBatchById(cartsToUpdate);
            if (!success) {
                throw new ServiceException(MessageUtils.message("wholesale.cart.batch.operation.failed"));
            }
            log.debug("更新购物车项数量：{}", cartsToUpdate.size());
        }

        // 执行批量插入
        if (!cartsToInsert.isEmpty()) {
            boolean success = cartMapper.insertBatch(cartsToInsert);
            if (!success) {
                throw new ServiceException(MessageUtils.message("wholesale.cart.batch.operation.failed"));
            }
            log.debug("新增购物车项数量：{}", cartsToInsert.size());
        }
    }

    /**
     * 处理购物车列表，筛选有效项并清理无效项
     */
    private List<WhsCart> processCartList(List<WhsCart> cartList, CartDataContext dataContext, Long memberId) {
        // 筛选有效的购物车项
        List<WhsCart> validCarts = cartList.stream()
            .filter(cart -> dataContext.productMap.containsKey(cart.getProductId())
                && dataContext.variantMap.containsKey(cart.getVariantId()))
            .collect(Collectors.toList());

        // 清理无效购物车项
        if (validCarts.size() < cartList.size()) {
            List<Long> invalidCartIds = cartList.stream()
                .filter(cart -> !dataContext.productMap.containsKey(cart.getProductId())
                    || !dataContext.variantMap.containsKey(cart.getVariantId()))
                .map(WhsCart::getId)
                .collect(Collectors.toList());

            if (!invalidCartIds.isEmpty()) {
                log.warn("清理{}个无效购物车项: {}", invalidCartIds.size(), invalidCartIds);
                cartMapper.deleteByIds(invalidCartIds);
            }
        }

        return validCarts;
    }

    /**
     * 将购物车项转换为VO
     */
    private List<CartVo> convertCartsToVos(List<WhsCart> validCarts, CartDataContext dataContext) {
        // 获取当前登录的客户ID
        Long memberId = WholesaleLoginHelper.getUserId();

        // 检查是否有显示价格权限
        boolean showPrice = memberId != null &&
            permissionHelper.hasPermission(memberId, MemberPermissionConstants.SHOW_PRICE);

        // 检查是否有显示库存权限
        boolean showStock = memberId != null &&
            permissionHelper.hasPermission(memberId, MemberPermissionConstants.SHOW_STOCK);

        return validCarts.stream()
            .map(cart -> {
                WhsProduct product = dataContext.productMap.get(cart.getProductId());
                WhsProductVariant variant = dataContext.variantMap.get(cart.getVariantId());

                if (product != null && variant != null) {
                    CartVo cartVo = MapstructUtils.convert(cart, CartVo.class);

                    // 设置商品信息
                    cartVo.setItemName(product.getItemName());

                    // 设置变体信息
                    cartVo.setSkuCode(variant.getSkuCode());
                    cartVo.setUpc(variant.getUpc());

                    // 根据权限设置价格信息
                    if (showPrice) {
                        cartVo.setWholesalePrice(variant.getWholesalePrice());
                        cartVo.setMsrp(variant.getMsrp());
                    } else {
                        cartVo.setWholesalePrice(null);
                        cartVo.setMsrp(null);
                    }

                    cartVo.setMainImage(
                        StringUtils.isNotEmpty(variant.getMainImage()) ? variant.getMainImage() : null);

                    // 设置包装类型信息
                    cartVo.setPackagingType(variant.getPackagingType());

                    // 根据权限设置库存信息
                    if (showStock) {
                        cartVo.setAvailableStock(dataContext.stockMap.getOrDefault(cart.getVariantId(), 0));
                    } else {
                        cartVo.setAvailableStock(null);
                    }

                    // 设置规格信息
                    if (StringUtils.isNotEmpty(variant.getSpecs())) {
                        cartVo.setSpecs(variant.getSpecs());
                    }

                    return cartVo;
                }
                return null;
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * 验证购物车项
     */
    private void validateCartItems(List<CartItemBo> items, CartDataContext dataContext) {
        List<String> errors = new ArrayList<>();

        for (CartItemBo item : items) {
            try {
                // 获取变体和产品信息
                WhsProductVariant variant = dataContext.variantMap.get(item.getVariantId());
                if (variant == null) {
                    throw new ServiceException(MessageUtils.message("wholesale.product.variant.not.exists.batch"));
                }

                WhsProduct product = dataContext.productMap.get(variant.getProductId());
                if (product == null) {
                    throw new ServiceException(MessageUtils.message("wholesale.product.not.exists.batch"));
                }

                // 验证库存
                Integer availableStock = dataContext.stockMap.getOrDefault(item.getVariantId(), 0);
                if (availableStock < item.getQuantity()) {
                    // 获取当前登录的客户ID
                    Long memberId = WholesaleLoginHelper.getUserId();

                    // 检查是否有显示库存权限
                    boolean showStock = memberId != null &&
                        permissionHelper.hasPermission(memberId, MemberPermissionConstants.SHOW_STOCK);

                    if (showStock) {
                        throw new ServiceException(MessageUtils.message("wholesale.product.stock.insufficient",
                            product.getItemName(), availableStock, item.getQuantity()));
                    } else {
                        throw new ServiceException(MessageUtils.message("wholesale.product.stock.insufficient.no.permission",
                            product.getItemName()));
                    }
                }
            } catch (ServiceException e) {
                errors.add(e.getMessage());
            }
        }

        if (!errors.isEmpty()) {
            throwMultipleErrors(errors);
        }
    }

    /**
     * 验证购物车VO项
     */
    private void validateCartVoItems(List<CartVo> cartItems, CartDataContext dataContext) {
        List<String> errors = new ArrayList<>();

        for (CartVo item : cartItems) {
            try {
                // 检查商品是否存在
                WhsProduct product = dataContext.productMap.get(item.getProductId());
                if (product == null) {
                    throw new ServiceException(
                        MessageUtils.message("wholesale.product.not.exists.with.name", item.getItemName()));
                }

                // 检查商品状态
                if (!STATUS_ENABLE.equals(product.getStatus())) {
                    throw new ServiceException(
                        MessageUtils.message("wholesale.product.not.available.with.name", item.getItemName()));
                }

                // 获取变体对象
                WhsProductVariant variant = dataContext.variantMap.get(item.getVariantId());
                if (variant == null) {
                    throw new ServiceException(
                        MessageUtils.message("wholesale.product.variant.not.exists.with.name", item.getItemName()));
                }

                // 检查变体状态
                if (!STATUS_ENABLE.equals(variant.getStatus())) {
                    throw new ServiceException(
                        MessageUtils.message("wholesale.product.variant.not.available.with.name", item.getItemName()));
                }

                // 检查库存
                Integer availableStock = dataContext.stockMap.getOrDefault(item.getVariantId(), 0);
                if (availableStock < item.getQuantity()) {
                    // 获取当前登录的客户ID
                    Long memberId = WholesaleLoginHelper.getUserId();

                    // 检查是否有显示库存权限
                    boolean showStock = memberId != null &&
                        permissionHelper.hasPermission(memberId, MemberPermissionConstants.SHOW_STOCK);

                    if (showStock) {
                        throw new ServiceException(MessageUtils.message("wholesale.product.stock.insufficient",
                            item.getItemName(), availableStock, item.getQuantity()));
                    } else {
                        throw new ServiceException(MessageUtils.message("wholesale.product.stock.insufficient.no.permission",
                            item.getItemName()));
                    }
                }
            } catch (ServiceException e) {
                errors.add(e.getMessage());
            }
        }

        if (!errors.isEmpty()) {
            throwMultipleErrors(errors);
        }
    }

    /**
     * 抛出多个错误信息
     */
    private void throwMultipleErrors(List<String> errors) {
        if (errors.size() == 1) {
            throw new ServiceException(errors.get(0));
        } else {
            throw new ServiceException(
                MessageUtils.message("wholesale.product.stock.insufficient.multiple", String.join("; ", errors)));
        }
    }
}
