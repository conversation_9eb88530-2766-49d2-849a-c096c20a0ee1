package com.imhuso.wholesale.core.client;

import com.imhuso.wholesale.core.config.ErpConfig;
import com.imhuso.wholesale.core.domain.dto.ErpStockBo;
import com.imhuso.wholesale.core.utils.erp.ErpApiUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Lookah ERP API客户端
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LookahErpApiClient {

    private final ErpConfig erpConfig;

    /**
     * 健康检查
     *
     * @return 连接是否正常
     */
    public boolean healthCheck() {
        return ErpApiUtils.healthCheck(erpConfig);
    }

    /**
     * 获取产品库存列表
     *
     * @param skuCodes SKU编码列表
     * @return 库存数据列表
     */
    public List<ErpStockBo> getProductStockList(List<String> skuCodes) {
        return ErpApiUtils.getProductStockList(erpConfig, skuCodes);
    }

}
