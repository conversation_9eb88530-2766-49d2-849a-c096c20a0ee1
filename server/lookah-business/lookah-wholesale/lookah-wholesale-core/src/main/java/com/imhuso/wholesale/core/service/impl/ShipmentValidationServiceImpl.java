package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.wholesale.core.constant.WholesaleConstants;
import com.imhuso.wholesale.core.domain.bo.admin.WarehouseSelectionBo;
import com.imhuso.wholesale.core.domain.bo.admin.WarehouseSelectionItemBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsShipmentPlanItemBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentStockResponseVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentWarehouseStockVo;
import com.imhuso.wholesale.core.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 发货验证服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ShipmentValidationServiceImpl implements IShipmentValidationService {

    private final IWhsShipmentPlanService shipmentPlanService;
    private final IWhsShipmentPlanItemService shipmentPlanItemService;
    private final IWhsWarehouseService warehouseService;
    private final IWhsOrderShipmentStockService orderShipmentStockService;

    @Override
    public Boolean previewAndValidateWarehouseAssignment(WarehouseSelectionBo selectionBo) {
        // 1. 验证基础参数和数据
        Long planId = validateBasicData(selectionBo);

        // 2. 获取必要数据
        Map<Long, WhsShipmentPlanItemBo> planItemMap = getPlanItemMap(planId);
        Map<Long, String> warehouseNameMap = getWarehouseNameMap();
        WhsShipmentStockResponseVo stockResponse = orderShipmentStockService.getStocksByPlanId(planId);

        // 3. 验证用户选择的发货项
        validateSelectionItems(selectionBo, planItemMap, warehouseNameMap, stockResponse);

        // 所有验证通过，返回true
        log.info("仓库分配验证成功，方案ID: {}", planId);
        return true;
    }

    /**
     * 验证基础数据
     */
    private Long validateBasicData(WarehouseSelectionBo selectionBo) {
        if (selectionBo == null || selectionBo.getPlanId() == null || CollUtil.isEmpty(selectionBo.getItems())) {
            throw new ServiceException("发货方案ID和选择项不能为空");
        }

        Long planId = selectionBo.getPlanId();
        log.info("开始验证发货方案ID {} 的仓库分配", planId);

        // 验证方案存在性和状态
        WhsShipmentPlanVo plan = shipmentPlanService.getShipmentPlanById(planId);
        if (plan == null) {
            throw new ServiceException("发货方案ID不存在: " + planId);
        }

        if (plan.getStatus() != null && plan.getStatus() > 2) {
            throw new ServiceException("发货方案状态不允许进行仓库分配：" + plan.getStatusText());
        }

        return planId;
    }

    /**
     * 获取发货方案项Map
     */
    private Map<Long, WhsShipmentPlanItemBo> getPlanItemMap(Long planId) {
        List<WhsShipmentPlanItemBo> planItemBos = shipmentPlanItemService.getShipmentPlanItemBosByPlanId(planId);
        if (CollUtil.isEmpty(planItemBos)) {
            throw new ServiceException("发货方案项不存在");
        }

        return planItemBos.stream()
                .collect(Collectors.toMap(WhsShipmentPlanItemBo::getId, item -> item));
    }

    /**
     * 获取仓库信息Map
     */
    private Map<Long, String> getWarehouseNameMap() {
        Map<Long, String> warehouseNameMap = warehouseService.getActiveWarehouseIdNameMap();
        if (CollUtil.isEmpty(warehouseNameMap)) {
            throw new ServiceException("未找到有效的仓库信息");
        }
        return warehouseNameMap;
    }

    /**
     * 验证选择项
     */
    private void validateSelectionItems(WarehouseSelectionBo selectionBo,
            Map<Long, WhsShipmentPlanItemBo> planItemMap,
            Map<Long, String> warehouseNameMap,
            WhsShipmentStockResponseVo stockResponse) {
        for (WarehouseSelectionItemBo item : selectionBo.getItems()) {
            // 跳过未选中的项
            if (!Boolean.TRUE.equals(item.getSelected())) {
                continue;
            }

            // 验证方案项是否存在
            WhsShipmentPlanItemBo planItem = validatePlanItem(item, planItemMap);

            // 验证发货数量是否合理
            Integer quantity = validateShipmentQuantity(item, planItem);

            // 验证仓库和库存
            validateWarehouseAndStock(item, planItem, warehouseNameMap, stockResponse, quantity);
        }
    }

    /**
     * 验证方案项是否存在
     */
    private WhsShipmentPlanItemBo validatePlanItem(WarehouseSelectionItemBo item,
            Map<Long, WhsShipmentPlanItemBo> planItemMap) {
        WhsShipmentPlanItemBo planItem = planItemMap.get(item.getPlanItemId());
        if (planItem == null) {
            throw new ServiceException("发货方案项ID不存在: " + item.getPlanItemId());
        }
        return planItem;
    }

    /**
     * 验证仓库和库存
     */
    private void validateWarehouseAndStock(WarehouseSelectionItemBo item,
            WhsShipmentPlanItemBo planItem,
            Map<Long, String> warehouseNameMap,
            WhsShipmentStockResponseVo stockResponse,
            Integer quantity) {
        Long warehouseId = item.getWarehouseId();

        // 如果仓库ID为0，代表本地仓发货，跳过仓库验证和库存验证
        if (WholesaleConstants.LOCAL_WAREHOUSE_ID.equals(warehouseId)) {
            return;
        }

        // 验证仓库是否存在
        if (!warehouseNameMap.containsKey(warehouseId)) {
            throw new ServiceException("无效的仓库ID: " + warehouseId);
        }

        // 验证库存是否足够
        validateStockAvailability(planItem.getVariantId(), warehouseId, quantity, stockResponse);
    }

    /**
     * 验证库存可用性
     */
    private void validateStockAvailability(Long variantId, Long warehouseId, Integer requiredQuantity,
            WhsShipmentStockResponseVo stockResponse) {
        boolean hasEnoughStock = false;

        if (stockResponse != null && stockResponse.getVariantWarehouseStocks() != null) {
            List<WhsShipmentWarehouseStockVo> warehouseStocks = stockResponse.getVariantWarehouseStocks()
                    .get(variantId);

            if (CollUtil.isNotEmpty(warehouseStocks)) {
                for (WhsShipmentWarehouseStockVo stockInfo : warehouseStocks) {
                    if (stockInfo.getWarehouseId().equals(warehouseId)) {
                        Integer effectiveStock = stockInfo.getEffectiveAvailableStock();

                        if (effectiveStock >= requiredQuantity) {
                            hasEnoughStock = true;
                            break;
                        } else {
                            throw new ServiceException("库存不足。变体ID: " + variantId +
                                    ", 仓库ID: " + warehouseId + ", 需要: " + requiredQuantity +
                                    ", 可用: " + effectiveStock);
                        }
                    }
                }
            }
        }

        if (!hasEnoughStock) {
            throw new ServiceException("未找到足够库存。变体ID: " + variantId + ", 仓库ID: " + warehouseId);
        }
    }

    @NotNull
    private Integer validateShipmentQuantity(WarehouseSelectionItemBo item, WhsShipmentPlanItemBo planItem) {
        Integer quantity = item.getQuantity();
        if (quantity == null || quantity <= 0) {
            throw new ServiceException("发货数量必须大于0");
        }

        // 验证发货数量是否超过可发货数量（考虑已发货数量）
        Integer maxShippableQuantity = planItem.getQuantity();
        // 如果有已发货数据，需要减去
        Integer alreadyShippedQuantity = 0; // 实际场景需要从数据库获取
        int remainingQuantity = maxShippableQuantity - alreadyShippedQuantity;

        if (quantity > remainingQuantity) {
            throw new ServiceException("发货数量超过可发货数量。项ID: " +
                    item.getPlanItemId() + ", 请求数量: " + quantity + ", 可发货数量: " + remainingQuantity);
        }
        return quantity;
    }
}
