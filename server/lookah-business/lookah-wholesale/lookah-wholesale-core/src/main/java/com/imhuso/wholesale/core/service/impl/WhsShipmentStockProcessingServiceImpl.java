package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.wholesale.core.constant.WholesaleConstants;
import com.imhuso.wholesale.core.domain.WhsOrderItem;
import com.imhuso.wholesale.core.domain.WhsOrderShipmentItem;
import com.imhuso.wholesale.core.domain.WhsStock;
import com.imhuso.wholesale.core.domain.WhsStockAllocation;
import com.imhuso.wholesale.core.domain.bo.admin.StockOperationBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentConversionVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanItemVo;
import com.imhuso.wholesale.core.enums.StockAllocationStatus;
import com.imhuso.wholesale.core.enums.StockOperationType;
import com.imhuso.wholesale.core.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单发货库存处理服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsShipmentStockProcessingServiceImpl implements IWhsShipmentStockProcessingService {

    private final IWhsStockAllocationService stockAllocationService;
    private final IWhsStockOperationService stockOperationService;
    private final IWhsStockService stockService;
    private final IWhsOrderItemService orderItemService;
    private final IWhsShipmentConversionService shipmentConversionService;
    private final IWhsPackageItemService packageItemService;

    /**
     * 为指定发货处理库存操作（分配释放、转换释放、出库、检查并释放剩余）
     *
     * @param orderId       订单ID
     * @param shipmentItems 发货项实体列表
     * @param planItemMap   计划项映射 (planItemId -> planItemVo)
     * @param planItems     与此次发货相关的所有计划项列表
     * @param conversions   与当前计划相关的转换记录列表
     */
    @Override
    public void processStockForShipment(Long orderId, List<WhsOrderShipmentItem> shipmentItems,
                                        Map<Long, WhsShipmentPlanItemVo> planItemMap, List<WhsShipmentPlanItemVo> planItems,
                                        List<WhsShipmentConversionVo> conversions) {
        log.info("开始管道化处理订单 {} 的库存操作", orderId);

        if (shipmentItems.isEmpty()) {
            log.warn("订单 {} 没有发货项，跳过库存处理", orderId);
            return;
        }

        // --- 准备阶段 ---
        log.debug("准备阶段：加载所需数据");

        // 1. 预加载所有相关的库存分配记录 (ALLOCATED 状态)
        Map<String, List<WhsStockAllocation>> allocationMap = loadAllocations(orderId);
        log.debug("加载了 {} 个订单项的相关库存分配记录", allocationMap.size());

        // 2. 准备转换记录映射 (actualVariantId -> List<Conversion>)
        Map<Long, List<WhsShipmentConversionVo>> conversionMap = prepareConversionMap(conversions);

        // 3. 初始化处理信息列表
        List<ShipmentItemProcessingInfo> processingList = initProcessingInfoList(shipmentItems, planItemMap);
        if (processingList.isEmpty()) {
            log.info("订单 {} 所有发货项均无效，跳过库存处理", orderId);
            return;
        }

        // --- 管道1: 处理直接原始满足 ---
        processDirectSatisfaction(orderId, processingList, allocationMap);

        // --- 管道2: 处理转换满足 ---
        processConversionSatisfaction(orderId, processingList, conversionMap, planItems, planItemMap);

        // --- 聚合阶段 ---
        Map<Long, Integer> aggregatedDirectReleases = new HashMap<>(); // allocationId -> quantityToRelease
        Map<String, Integer> aggregatedConversionReleases = new HashMap<>(); // originalVariantId:originalOrderItemId ->
        // totalPcsToRelease
        Map<String, StockOperationBo.OutboundItem> aggregatedOutboundItems = new HashMap<>(); // variantId:warehouseId:orderItemId
        // -> OutboundItem

        aggregateOperations(processingList, conversions, aggregatedDirectReleases, aggregatedConversionReleases,
            aggregatedOutboundItems);

        // --- 执行阶段 ---
        log.info("执行阶段：按顺序执行库存操作");

        // 1. 执行直接释放 (释放直接锁定的库存)
        executeDirectReleases(aggregatedDirectReleases);

        // 2. 执行转换释放 (释放转换所需的原始变体库存)
        executeConversionReleases(orderId, aggregatedConversionReleases, planItems, planItemMap);

        // 3. 执行出库 (扣减实际发货变体的库存)
        executeOutboundOperations(orderId, aggregatedOutboundItems);

        // 4. 检查并释放剩余库存 (对于完全发货的订单项)
        checkAndReleaseRemainingStock(orderId, planItems, planItemMap);

        log.info("订单 {} 的管道化库存操作处理完成", orderId);
    }

    /**
     * 初始化处理信息列表
     */
    private List<ShipmentItemProcessingInfo> initProcessingInfoList(List<WhsOrderShipmentItem> shipmentItems,
                                                                    Map<Long, WhsShipmentPlanItemVo> planItemMap) {

        List<ShipmentItemProcessingInfo> processingList = new ArrayList<>();

        for (WhsOrderShipmentItem shipmentItem : shipmentItems) {
            WhsShipmentPlanItemVo planItem = planItemMap.get(shipmentItem.getPlanItemId());
            if (planItem == null) {
                log.warn("未找到发货项对应的计划项，跳过处理: shipmentItemId={}, planItemId={}", shipmentItem.getId(),
                    shipmentItem.getPlanItemId());
                continue;
            }

            int quantityNeeded = shipmentItem.getQuantity() != null ? shipmentItem.getQuantity() : 0;
            if (quantityNeeded <= 0) {
                log.warn("发货项数量无效，跳过处理: shipmentItemId={}, quantity={}", shipmentItem.getId(),
                    shipmentItem.getQuantity());
                continue;
            }

            ShipmentItemProcessingInfo info = new ShipmentItemProcessingInfo();
            info.setPlanItem(planItem);
            info.setShipmentItem(shipmentItem);
            info.setTotalQuantityNeeded(quantityNeeded);
            info.setRemainingToSatisfy(quantityNeeded); // 初始剩余等于全部
            processingList.add(info);
        }

        return processingList;
    }

    /**
     * 准备转换记录映射
     */
    private Map<Long, List<WhsShipmentConversionVo>> prepareConversionMap(
        List<WhsShipmentConversionVo> conversions) {

        return CollUtil.isEmpty(conversions) ? Collections.emptyMap()
            : conversions.stream().collect(Collectors.groupingBy(WhsShipmentConversionVo::getActualVariantId));
    }

    /**
     * 处理直接原始满足管道
     */
    private void processDirectSatisfaction(Long orderId, List<ShipmentItemProcessingInfo> processingList,
                                           Map<String, List<WhsStockAllocation>> allocationMap) {

        log.debug("管道1：处理直接原始满足");

        for (ShipmentItemProcessingInfo info : processingList) {
            Long variantId = info.getPlanItem().getVariantId();
            Long warehouseId = info.getShipmentItem().getWarehouseId();

            if (variantId == null || warehouseId == null || orderId == null) {
                log.warn("发货项信息不完整 (variantId 或 warehouseId 或 orderId 为 null)，跳过直接满足处理: PlanItemId={}",
                    info.getPlanItem().getId());
                continue;
            }

            String allocationKey = buildAllocationMapKey(orderId, variantId);
            List<WhsStockAllocation> relevantAllocations = allocationMap.getOrDefault(allocationKey,
                Collections.emptyList());

            if (relevantAllocations.isEmpty()) {
                log.debug("发货项(PlanItemId:{}) 无直接库存分配记录", info.getPlanItem().getId());
                continue;
            }

            // 按ID排序，优先使用旧的分配
            relevantAllocations.sort(Comparator.comparing(WhsStockAllocation::getId));

            int needed = info.getTotalQuantityNeeded(); // 当前项总共需要发货的数量
            int satisfiedCount = 0;

            for (WhsStockAllocation allocation : relevantAllocations) {
                if (needed <= 0)
                    break; // 当前项已满足

                // 计算此分配记录剩余可释放的数量
                int releasedQty = allocation.getReleasedQuantity() != null ? allocation.getReleasedQuantity() : 0;
                int remainingAllocation = allocation.getQuantity() - releasedQty;

                if (remainingAllocation > 0) {
                    int canSatisfy = Math.min(needed, remainingAllocation);
                    info.getDirectAllocationReleaseDetails()
                        .add(new AllocationReleaseDetail(allocation.getId(), canSatisfy));
                    satisfiedCount += canSatisfy;
                    needed -= canSatisfy;

                    log.debug("发货项(PlanItemId:{}) 从直接分配(ID:{}) 满足 {} 件", info.getPlanItem().getId(), allocation.getId(),
                        canSatisfy);
                }
            }

            info.setSatisfiedFromOriginalDirect(satisfiedCount);
            info.setRemainingToSatisfy(info.getTotalQuantityNeeded() - satisfiedCount);

            if (satisfiedCount > 0) {
                log.debug("发货项(PlanItemId:{}) 直接满足总计: {}, 剩余需满足: {}", info.getPlanItem().getId(), satisfiedCount,
                    info.getRemainingToSatisfy());
            }
        }
    }

    /**
     * 处理转换满足管道
     */
    private void processConversionSatisfaction(Long orderId, List<ShipmentItemProcessingInfo> processingList,
                                               Map<Long, List<WhsShipmentConversionVo>> conversionMap, List<WhsShipmentPlanItemVo> planItems,
                                               Map<Long, WhsShipmentPlanItemVo> planItemMap) {

        log.debug("管道2：处理转换满足");

        for (ShipmentItemProcessingInfo info : processingList) {
            if (info.getRemainingToSatisfy() <= 0) {
                continue; // 已被直接满足
            }

            Long actualVariantId = info.getPlanItem().getVariantId();
            if (actualVariantId == null) {
                log.warn("发货项计划项缺少 variantId，无法处理转换满足: PlanItemId={}", info.getPlanItem().getId());
                continue;
            }

            List<WhsShipmentConversionVo> relevantConversions = conversionMap.getOrDefault(actualVariantId,
                Collections.emptyList());

            if (relevantConversions.isEmpty()) {
                log.warn("发货项(PlanItemId:{}) 需要满足 {} 件，但找不到对应的转换记录! 将尝试使用可用库存最大满足", info.getPlanItem().getId(),
                    info.getRemainingToSatisfy());
                // 没有转换记录时，标记剩余数量为0，表示已处理完毕（无法通过转换满足）
                info.setRemainingToSatisfy(0);
                continue; // 继续处理下一个发货项
            }

            // 获取并准备候选转换记录
            prepareConversionCandidates(orderId, info, actualVariantId, relevantConversions);

            // 处理转换记录
            processConversionCandidates(orderId, info, planItems, planItemMap);
        }
    }

    /**
     * 准备转换候选记录
     */
    private void prepareConversionCandidates(Long orderId, ShipmentItemProcessingInfo info, Long actualVariantId,
                                             List<WhsShipmentConversionVo> relevantConversions) {

        int remainingNeeded = info.getRemainingToSatisfy();
        log.debug("发货项(PlanItemId:{}) 需要通过转换满足 {} 件 (实际变体 {})", info.getPlanItem().getId(), remainingNeeded,
            actualVariantId);

        // 清空候选列表，以便重新填充
        info.getCandidateConversions().clear();

        // 如果有多个转化记录，需要排除从库存锁定记录中查找到已完全释放掉的转化记录，然后取包装类型最大的
        if (relevantConversions.size() > 1) {
            // 获取对应的库存分配记录，用于判断哪些转换记录已完全释放
            Map<Long, Boolean> originalVariantReleaseStatus = new HashMap<>();
            for (WhsShipmentConversionVo conv : relevantConversions) {
                if (conv.getOriginalVariantId() == null || orderId == null)
                    continue;

                // 查询原始变体对应的分配记录，判断是否已完全释放
                LambdaQueryWrapper<WhsStockAllocation> allocQuery = Wrappers.lambdaQuery(WhsStockAllocation.class)
                    .eq(WhsStockAllocation::getOrderId, orderId)
                    .eq(WhsStockAllocation::getVariantId, conv.getOriginalVariantId())
                    .eq(WhsStockAllocation::getStatus, StockAllocationStatus.ALLOCATED.getValue());

                List<WhsStockAllocation> allocations = stockAllocationService.list(allocQuery);

                // 只有在库存分配记录为空(即全部已释放)时，标记为已释放
                originalVariantReleaseStatus.put(conv.getOriginalVariantId(), allocations.isEmpty());

                if (allocations.isEmpty()) {
                    log.debug("原始变体 {} 的库存分配记录已全部释放", conv.getOriginalVariantId());
                }
            }

            // 过滤出未完全释放的转换记录
            List<WhsShipmentConversionVo> unreleased = relevantConversions.stream()
                .filter(conv -> conv.getOriginalVariantId() != null
                    && !Boolean.TRUE.equals(originalVariantReleaseStatus.get(conv.getOriginalVariantId())))
                .toList();

            if (!unreleased.isEmpty()) {
                // 将未释放的记录按包装类型降序排序（优先选择包装类型大的）
                List<WhsShipmentConversionVo> sortedUnreleased = unreleased.stream()
                    .sorted(Comparator.comparing(WhsShipmentConversionVo::getActualPackagingType).reversed())
                    .toList();

                // 将排序后的列表添加到候选列表中
                info.getCandidateConversions().addAll(sortedUnreleased);

                log.info("实际变体 {} 发现 {} 个未释放的转换记录，按包装类型降序排序后依次尝试", actualVariantId, sortedUnreleased.size());
            } else {
                // 如果所有记录都已释放，也按包装类型排序，但记录优先级较低
                List<WhsShipmentConversionVo> sortedReleased = relevantConversions.stream()
                    .filter(conv -> conv.getOriginalVariantId() != null)
                    .sorted(Comparator.comparing(WhsShipmentConversionVo::getActualPackagingType).reversed())
                    .toList();

                // 将已释放的记录添加到候选列表，优先级较低
                info.getCandidateConversions().addAll(sortedReleased);

                log.warn("实际变体 {} 的所有转换记录原始变体已全部释放，仍按包装类型降序排序后依次尝试", actualVariantId);
            }
        } else {
            // 只有一个转换记录，直接使用并添加到候选列表
            info.getCandidateConversions().add(relevantConversions.get(0));
            log.debug("实际变体 {} 只有1个转换记录，直接使用", actualVariantId);
        }
    }

    /**
     * 处理转换候选记录
     */
    private void processConversionCandidates(Long orderId, ShipmentItemProcessingInfo info,
                                             List<WhsShipmentPlanItemVo> planItems, Map<Long, WhsShipmentPlanItemVo> planItemMap) {

        int remainingNeeded = info.getRemainingToSatisfy();

        // 初始化剩余需要满足的数量（件数）
        int totalRemaining = remainingNeeded;
        // 初始化已处理的PCS总量
        int totalProcessedPcs = 0;
        // 跟踪需要释放的PCS总量（从第二个转换记录开始会减少）
        int totalPcsToRelease = totalRemaining * (info.getCandidateConversions().isEmpty() ? 0
            : getPackagingQuantity(info.getCandidateConversions().get(0)));
        // 循环尝试所有候选转换记录
        int conversionIndex = 0;
        List<ConversionReleaseResult> releaseResults = new ArrayList<>();

        while (totalRemaining > 0 && conversionIndex < info.getCandidateConversions().size()) {
            WhsShipmentConversionVo conversion = info.getCandidateConversions().get(conversionIndex);
            Long originalVariantId = conversion.getOriginalVariantId();

            if (originalVariantId == null) {
                log.error("转换记录缺少 originalVariantId，跳过: ConversionId={}", conversion.getId());
                conversionIndex++;
                continue;
            }

            // 提供默认包装数量为 1
            int packagingQty = getPackagingQuantity(conversion);

            // 获取原始变体的包装数量
            int originalPackagingQty = getOriginalPackagingQuantity(conversion);

            // 计算本次转换记录需要释放多少原始变体的 PCS 数量
            // 对于第一个转换记录，使用完整需求
            // 对于后续转换记录，减去已处理的PCS数量
            int originalPcsToRelease;
            if (conversionIndex == 0) {
                originalPcsToRelease = totalRemaining * packagingQty;
                totalPcsToRelease = originalPcsToRelease; // 记录初始PCS需求
            } else {
                // 计算剩余的PCS需求 = 总需求 - 已处理PCS
                originalPcsToRelease = totalPcsToRelease - totalProcessedPcs;
            }

            log.debug("尝试转换记录 #{}: 原始变体 {} (包装数量:{}), 需要释放 {} PCS ({} 件实际 * {} pcs/件)", conversionIndex + 1,
                originalVariantId, originalPackagingQty, originalPcsToRelease, totalRemaining, packagingQty);

            // 计算原始变体需要保留的数量
            int quantityToReserve = calculateQuantityToReserve(originalVariantId, planItems, planItemMap);
            log.debug("原始变体 {} 需要保留 {} 件用于原始订单发货", originalVariantId, quantityToReserve);

            // 查询原始变体是否还有足够的库存可以释放
            // 执行释放操作，获取实际释放的PCS数量
            int actuallyReleasedPcs = releaseOriginalVariantStockForConversion(orderId, originalVariantId,
                originalPcsToRelease, quantityToReserve); // 考虑保留数量

            // 计算实际满足的件数（向下取整，确保完整件数）
            int satisfiedItems = actuallyReleasedPcs / packagingQty;

            // 计算本次释放占用的需求数量，即使不足一个完整的包装
            double partialItemUsed = (double) actuallyReleasedPcs / packagingQty;

            // 如果有部分释放但不足一个完整单位
            if (actuallyReleasedPcs > 0 && satisfiedItems == 0) {
                log.debug("转换记录 #{} 部分释放: {} PCS (占用约 {} 件需求)", conversionIndex + 1, actuallyReleasedPcs,
                    String.format("%.2f", partialItemUsed));
            }

            // 记录释放结果
            ConversionReleaseResult result = new ConversionReleaseResult(conversion.getId(), originalVariantId,
                packagingQty, originalPcsToRelease, actuallyReleasedPcs, satisfiedItems);
            releaseResults.add(result);

            // 更新剩余需求和总处理量
            totalRemaining -= satisfiedItems;
            // 计算正确的PCS处理量，考虑原始变体和目标变体的比例关系
            int effectiveProcessedPcs = calculateEffectivePcsCount(actuallyReleasedPcs, originalPackagingQty);
            totalProcessedPcs += effectiveProcessedPcs;

            // 将实际释放的PCS数量累加到原始变体释放映射中
            info.getOriginalVariantsToReleasePcs().merge(originalVariantId, actuallyReleasedPcs, Integer::sum);

            log.info("转换记录 #{} (原始变体 {}): 目标释放 {} PCS, 实际释放 {} PCS, 满足 {} 件, 剩余需满足 {} 件, 有效PCS进度 {}",
                conversionIndex + 1, originalVariantId, originalPcsToRelease, actuallyReleasedPcs, satisfiedItems,
                totalRemaining, effectiveProcessedPcs);

            // 处理下一个转换记录
            conversionIndex++;
        }

        // 记录最终处理结果
        if (totalRemaining > 0) {
            log.warn("发货项(PlanItemId:{}) 尝试了所有 {} 个转换记录后仍有 {} 件未满足!", info.getPlanItem().getId(),
                info.getCandidateConversions().size(), totalRemaining);

            if (totalProcessedPcs > 0) {
                log.info("发货项(PlanItemId:{}) 已释放 {} PCS总量，部分满足了需求", info.getPlanItem().getId(), totalProcessedPcs);

                // 设置剩余待满足数量为未能满足的部分
                info.setRemainingToSatisfy(totalRemaining);
            } else {
                // 所有候选记录都无库存可释放
                log.warn("发货项(PlanItemId:{}) 所有 {} 个转换记录都无法释放库存! 将尝试使用最大可用库存满足", info.getPlanItem().getId(),
                    info.getCandidateConversions().size());

                // 设置剩余待满足数量为未能满足的部分
                info.setRemainingToSatisfy(totalRemaining);
            }
        } else {
            log.info("发货项(PlanItemId:{}) 通过 {} 个转换记录成功满足所有 {} 件需求，总释放 {} PCS", info.getPlanItem().getId(),
                conversionIndex, remainingNeeded, totalProcessedPcs);

            // 标记为已处理完成
            info.setRemainingToSatisfy(0);
        }

        // 记录详细的转换处理结果到日志
        if (log.isDebugEnabled() && !releaseResults.isEmpty()) {
            log.debug("发货项(PlanItemId:{}) 转换处理详情:", info.getPlanItem().getId());
            for (int i = 0; i < releaseResults.size(); i++) {
                ConversionReleaseResult r = releaseResults.get(i);
                log.debug("  #{}: 转换ID={}, 原始变体={}, 包装数量={}, 目标释放={} PCS, 实际释放={} PCS, 满足={} 件", i + 1, r.conversionId,
                    r.originalVariantId, r.packagingQty, r.requestedPcs, r.releasedPcs, r.satisfiedItems);
            }
        }
    }

    /**
     * 聚合所有操作
     */
    private void aggregateOperations(List<ShipmentItemProcessingInfo> processingList,
                                     List<WhsShipmentConversionVo> conversions, Map<Long, Integer> aggregatedDirectReleases,
                                     Map<String, Integer> aggregatedConversionReleases,
                                     Map<String, StockOperationBo.OutboundItem> aggregatedOutboundItems) {

        log.debug("聚合阶段：汇总所有库存操作");

        // 准备原始订单ID查找 (基于转换记录)
        Map<Long, Long> originalVariantToOrderIdMap = CollUtil.isEmpty(conversions) ? Collections.emptyMap()
            : conversions.stream().filter(c -> c.getOriginalVariantId() != null && c.getOrderId() != null)
            .collect(Collectors.toMap(WhsShipmentConversionVo::getOriginalVariantId,
                WhsShipmentConversionVo::getOrderId, (existing, replacement) -> existing // 如果有重复，保留第一个
            ));

        for (ShipmentItemProcessingInfo info : processingList) {
            // 聚合直接释放
            for (AllocationReleaseDetail detail : info.getDirectAllocationReleaseDetails()) {
                aggregatedDirectReleases.merge(detail.allocationId(), detail.quantityToRelease(), Integer::sum);
            }

            // 聚合转换释放
            for (Map.Entry<Long, Integer> entry : info.getOriginalVariantsToReleasePcs().entrySet()) {
                Long originalVariantId = entry.getKey();
                Integer pcsToRelease = entry.getValue();

                // 尝试从转换记录中获取原始订单项ID，如果找不到，使用当前发货项的订单项ID作为后备
                Long originalOrderId = originalVariantToOrderIdMap.getOrDefault(originalVariantId,
                    info.getPlanItem().getOrderId());

                if (originalOrderId == null) {
                    log.error("无法确定原始订单ID进行转换释放: originalVariantId={}, PlanItemId={}", originalVariantId,
                        info.getPlanItem().getId());
                    throw new ServiceException("无法确定原始订单ID以释放转换库存");
                }

                String conversionKey = originalVariantId + ":" + originalOrderId;
                aggregatedConversionReleases.merge(conversionKey, pcsToRelease, Integer::sum);
            }

            // 聚合出库
            WhsOrderShipmentItem si = info.getShipmentItem();
            WhsShipmentPlanItemVo pi = info.getPlanItem();

            // 确保关键信息不为空
            if (pi.getVariantId() == null || si.getWarehouseId() == null || pi.getOrderId() == null
                || si.getQuantity() == null) {
                log.error("无法聚合出库项，信息不完整: PlanItemId={}, ShipmentItemId={}", pi.getId(), si.getId());
                throw new ServiceException("无法聚合出库项，信息不完整");
            }

            StockOperationBo.OutboundItem outboundItem = new StockOperationBo.OutboundItem(pi.getVariantId(),
                si.getWarehouseId(), pi.getOrderId(), si.getQuantity(), pi.getPackagingType(),
                pi.getPackagingQuantity());

            aggregateOutboundItems(aggregatedOutboundItems, outboundItem.getKey(), outboundItem);
        }

        // 记录聚合结果
        if (log.isDebugEnabled()) {
            log.debug("聚合结果 - 直接释放: {}", aggregatedDirectReleases);
            log.debug("聚合结果 - 转换释放: {}", aggregatedConversionReleases);
            log.debug("聚合结果 - 出库: {}", aggregatedOutboundItems.values());
        }
    }

    /**
     * 执行直接释放
     */
    private void executeDirectReleases(Map<Long, Integer> aggregatedDirectReleases) {
        log.info("执行直接释放操作...");

        for (Map.Entry<Long, Integer> entry : aggregatedDirectReleases.entrySet()) {
            Long allocationId = entry.getKey();
            Integer quantityToRelease = entry.getValue();

            if (allocationId == null || quantityToRelease == null || quantityToRelease <= 0) {
                continue;
            }

            try {
                boolean success = stockAllocationService.releaseSpecificAllocation(allocationId, quantityToRelease,
                    "订单发货释放直接锁定库存");

                if (!success) {
                    log.error("释放直接锁定库存失败: allocationId={}, quantity={}", allocationId, quantityToRelease);
                    throw new ServiceException("释放直接锁定库存失败: allocationId=" + allocationId);
                } else {
                    log.info("成功释放直接锁定库存: allocationId={}, quantity={}", allocationId, quantityToRelease);
                }
            } catch (Exception e) {
                log.error("释放直接锁定库存时发生异常: allocationId={}, quantity={}, error={}", allocationId, quantityToRelease,
                    e.getMessage(), e);
                throw new ServiceException("释放直接锁定库存时发生异常: " + e.getMessage());
            }
        }
    }

    /**
     * 执行转换释放
     */
    private void executeConversionReleases(Long orderId, Map<String, Integer> aggregatedConversionReleases,
                                           List<WhsShipmentPlanItemVo> planItems, Map<Long, WhsShipmentPlanItemVo> planItemMap) {

        log.info("执行转换释放操作...");

        for (Map.Entry<String, Integer> entry : aggregatedConversionReleases.entrySet()) {
            String[] keyParts = entry.getKey().split(":");
            Long originalVariantId = Long.parseLong(keyParts[0]);
            Long originalOrderId = keyParts.length > 1 ? Long.parseLong(keyParts[1]) : orderId;
            Integer totalPcsToRelease = entry.getValue();

            if (totalPcsToRelease == null || totalPcsToRelease <= 0) {
                continue;
            }

            // 计算需要为该原始物料保留的库存
            int quantityToReserve = calculateQuantityToReserve(originalVariantId, planItems, planItemMap);

            try {
                // 尝试释放库存
                int actuallyReleased = releaseOriginalVariantStockForConversion(originalOrderId, originalVariantId,
                    totalPcsToRelease, quantityToReserve);

                // 如果未能完全释放所需数量，但当前释放操作不应终止发货流程（只记录警告）
                if (actuallyReleased < totalPcsToRelease) {
                    log.warn("原始变体 {} (订单 {}) 未能完全释放所需库存: 请求 {} PCS, 实际释放 {} PCS", originalVariantId, originalOrderId,
                        totalPcsToRelease, actuallyReleased);
                }
            } catch (Exception e) {
                log.error("释放原始变体库存 (转换) 时发生异常: originalVariantId={}, originalOrderId={}, pcs={}, error={}",
                    originalVariantId, originalOrderId, totalPcsToRelease, e.getMessage(), e);
                throw new ServiceException("释放原始变体库存 (转换) 时发生异常: " + e.getMessage());
            }
        }
    }

    /**
     * 计算需要保留的数量
     */
    private int calculateQuantityToReserve(Long originalVariantId, List<WhsShipmentPlanItemVo> planItems,
                                           Map<Long, WhsShipmentPlanItemVo> planItemMap) {

        int quantityToReserve = 0;

        if (planItems != null) {
            for (WhsShipmentPlanItemVo pi : planItems) {
                // 检查是否是当前原始变体的计划项
                if (pi.getVariantId() != null && pi.getVariantId().equals(originalVariantId)) {
                    // 使用辅助方法计算未发货数量
                    int unshipped = calculateUnshippedQuantity(pi, planItemMap);
                    quantityToReserve += unshipped;
                    log.debug("发现原始变体 {} 的计划项 {}, 未发货数量: {} 件", originalVariantId, pi.getId(), unshipped);
                }
            }
        }

        return quantityToReserve;
    }

    /**
     * 执行出库操作
     */
    private void executeOutboundOperations(Long orderId,
                                           Map<String, StockOperationBo.OutboundItem> aggregatedOutboundItems) {

        log.info("执行出库操作...");

        for (StockOperationBo.OutboundItem outboundItem : aggregatedOutboundItems.values()) {
            try {
                // 检查是否为本地仓库，如果是本地仓库则跳过出库操作
                if (WholesaleConstants.LOCAL_WAREHOUSE_ID.equals(outboundItem.getWarehouseId())) {
                    log.info("跳过本地仓库出库操作: variantId={}, warehouseId={}, quantity={} (本地仓库无需扣减库存)",
                        outboundItem.getVariantId(), outboundItem.getWarehouseId(), outboundItem.getQuantity());
                    continue;
                }

                performOutboundOperation(outboundItem.getVariantId(), outboundItem.getWarehouseId(),
                    outboundItem.getQuantity(), orderId, outboundItem.getOrderItemId(),
                    outboundItem.getPackagingType(), outboundItem.getPackagingQuantity());
            } catch (Exception e) {
                log.error("执行出库操作时发生异常: variantId={}, warehouseId={}, quantity={}, error={}",
                    outboundItem.getVariantId(), outboundItem.getWarehouseId(), outboundItem.getQuantity(),
                    e.getMessage(), e);
                // 出库失败通常是严重问题，应该抛出异常中断流程
                throw new ServiceException("执行出库操作时发生异常: " + e.getMessage());
            }
        }
    }

    // --- Private Helper Methods (Migrated) ---

    /**
     * 内部辅助类，用于在处理管道中跟踪每个发货项的状态
     */
    @lombok.Data
    private static class ShipmentItemProcessingInfo {
        private WhsShipmentPlanItemVo planItem;
        private WhsOrderShipmentItem shipmentItem;
        private int totalQuantityNeeded;
        private int satisfiedFromOriginalDirect = 0;
        private int remainingToSatisfy;
        // originalVariantId -> pcsToRelease
        private final Map<Long, Integer> originalVariantsToReleasePcs = new HashMap<>();
        private final List<AllocationReleaseDetail> directAllocationReleaseDetails = new ArrayList<>();
        // 候选转换记录列表，按优先级排序存储，用于在一个转换记录无法释放时尝试其他记录
        private final List<WhsShipmentConversionVo> candidateConversions = new ArrayList<>();
    }

    /**
     * 内部辅助类，记录需要从哪个直接分配记录释放多少数量
     */
    private record AllocationReleaseDetail(Long allocationId, int quantityToRelease) {
    }

    /**
     * 加载订单相关的库存分配记录
     *
     * @param orderId 订单ID
     * @return Map<String, List < WhsStockAllocation>> key:
     * variantId:orderItemId:warehouseId
     */
    private Map<String, List<WhsStockAllocation>> loadAllocations(Long orderId) {
        if (orderId == null) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<WhsStockAllocation> allocQuery = Wrappers.lambdaQuery(WhsStockAllocation.class)
            .eq(WhsStockAllocation::getOrderId, orderId)
            .eq(WhsStockAllocation::getStatus, StockAllocationStatus.ALLOCATED.getValue());

        List<WhsStockAllocation> allocations = stockAllocationService.list(allocQuery);

        return allocations.stream()
            .collect(Collectors.groupingBy(a -> buildAllocationMapKey(a.getOrderId(), a.getVariantId())));
    }

    /**
     * 构建库存分配映射的键
     */
    private String buildAllocationMapKey(Long orderId, Long variantId) {
        return (orderId != null ? orderId : "null") + ":" + (variantId != null ? variantId : "null"); // Use "0" if
    }

    /**
     * 释放转换所需的原始变体库存
     *
     * @param orderId           订单ID
     * @param originalVariantId 原始变体ID
     * @param totalPcsToRelease 需要释放的总PCS数量
     * @param quantityToReserve 计划需保留的PCS数量
     * @return 实际释放的PCS数量
     */
    private int releaseOriginalVariantStockForConversion(Long orderId, Long originalVariantId, int totalPcsToRelease,
                                                         int quantityToReserve) {
        if (totalPcsToRelease <= 0) {
            log.debug("原始变体 {} (订单项 {}) 转换无需释放库存 (totalPcsToRelease={})", originalVariantId, orderId,
                totalPcsToRelease);
            return 0;
        }

        log.info("开始释放原始变体 {} (订单项 {}) 的库存 (转换需求): 目标释放 {} PCS, 需保留 {} PCS", originalVariantId, orderId,
            totalPcsToRelease, quantityToReserve);

        // 查找原始变体对应的分配记录
        List<WhsStockAllocation> origAllocations = findOriginalAllocations(orderId, originalVariantId);

        if (CollUtil.isEmpty(origAllocations)) {
            log.warn("找不到原始变体 {} (订单项 {}) 的库存分配记录，无法释放 {} PCS", originalVariantId, orderId, totalPcsToRelease);
            return 0;
        }

        // 记录库存状态日志
        logStockStatus(originalVariantId, orderId, origAllocations);

        // 计算可释放的总量
        int totalAvailableAllocation = calculateTotalAvailableAllocation(origAllocations);

        // 检查总保留量是否超过总可用量
        if (quantityToReserve >= totalAvailableAllocation) {
            log.warn("原始变体 {} (订单项 {}) 需保留的计划库存 ({} PCS) 大于或等于总可用库存 ({} PCS)，无可释放库存", originalVariantId, orderId,
                quantityToReserve, totalAvailableAllocation);
            return 0;
        }

        log.debug("原始变体 {} (订单项 {}): 总可用分配={}, 需保留={}, 计划目标释放={}, 最大可释放={}", originalVariantId, orderId,
            totalAvailableAllocation, quantityToReserve, totalPcsToRelease,
            totalAvailableAllocation - quantityToReserve);

        // 智能分配保留量并计算可释放量
        if (origAllocations.size() > 1) {
            log.info("原始变体 {} (订单项 {}) 有多个锁定记录 ({}个), 执行智能释放策略", originalVariantId, orderId, origAllocations.size());
        }

        // 准备释放信息
        List<AllocationReleaseInfo> releaseInfos = prepareReleaseInfos(origAllocations, quantityToReserve);

        // 计算释放目标
        int maxReleasable = releaseInfos.stream().mapToInt(AllocationReleaseInfo::getReleasableQuantity).sum();

        int effectiveTotalPcsToRelease = Math.min(totalPcsToRelease, maxReleasable);

        // 打印智能分配结果
        if (log.isDebugEnabled()) {
            logReleaseAllocationDetails(releaseInfos);
        }

        // 若无可释放量，直接返回
        if (effectiveTotalPcsToRelease == 0) {
            log.info("原始变体 {} (订单项 {}) 库存状态: 总可用={}, 需保留={}, 可释放=0, 无可释放量", originalVariantId, orderId,
                totalAvailableAllocation, quantityToReserve);
            return 0;
        }

        // 执行释放
        return executeReleaseOperation(orderId, originalVariantId, effectiveTotalPcsToRelease, releaseInfos);
    }

    /**
     * 查找原始分配记录
     */
    private List<WhsStockAllocation> findOriginalAllocations(Long orderId, Long originalVariantId) {
        LambdaQueryWrapper<WhsStockAllocation> query = Wrappers.lambdaQuery(WhsStockAllocation.class)
            .eq(WhsStockAllocation::getOrderId, orderId).eq(WhsStockAllocation::getVariantId, originalVariantId)
            .eq(WhsStockAllocation::getStatus, StockAllocationStatus.ALLOCATED.getValue())
            .orderBy(true, false, WhsStockAllocation::getId); // 按ID倒序，优先处理新的记录

        return stockAllocationService.list(query);
    }

    /**
     * 记录库存状态日志
     */
    private void logStockStatus(Long variantId, Long orderId, List<WhsStockAllocation> allocations) {
        if (log.isInfoEnabled() && !CollUtil.isEmpty(allocations)) {
            log.info("转换记录原始库存数据 - 变体ID: {}, 订单ID: {}, 发现 {} 条锁定记录:", variantId, orderId, allocations.size());

            for (WhsStockAllocation alloc : allocations) {
                int releasedQty = alloc.getReleasedQuantity() != null ? alloc.getReleasedQuantity() : 0;
                int remainingAllocation = alloc.getQuantity() - releasedQty;
                log.info("  锁定记录ID: {}, 仓库ID: {}, 总锁定数量: {}, 已释放数量: {}, 剩余可释放: {}, 状态: {}", alloc.getId(),
                    alloc.getWarehouseId(), alloc.getQuantity(), releasedQty, remainingAllocation,
                    alloc.getStatus());

                // 获取关联的库存记录
                WhsStock stock = stockService.getStockByVariantAndWarehouse(alloc.getVariantId(),
                    alloc.getWarehouseId());
                if (stock != null) {
                    log.info("  关联库存 - 变体ID: {}, 仓库ID: {}, 总库存: {}, 可用库存: {}, 锁定库存: {}", stock.getVariantId(),
                        stock.getWarehouseId(), stock.getTotalStock(), stock.getAvailableStock(),
                        stock.getLockedStock());
                }
            }
        }
    }

    /**
     * 计算总可用分配量
     */
    private int calculateTotalAvailableAllocation(List<WhsStockAllocation> allocations) {
        return allocations.stream().mapToInt(alloc -> {
            int releasedQty = alloc.getReleasedQuantity() != null ? alloc.getReleasedQuantity() : 0;
            return alloc.getQuantity() - releasedQty;
        }).sum();
    }

    /**
     * 准备释放信息，智能分配保留量
     */
    private List<AllocationReleaseInfo> prepareReleaseInfos(List<WhsStockAllocation> allocations,
                                                            int quantityToReserve) {

        // 1. 创建分配记录的可释放信息列表
        List<AllocationReleaseInfo> releaseInfos = new ArrayList<>();
        for (WhsStockAllocation alloc : allocations) {
            int releasedQty = alloc.getReleasedQuantity() != null ? alloc.getReleasedQuantity() : 0;
            int remainingAllocation = alloc.getQuantity() - releasedQty;
            if (remainingAllocation > 0) {
                releaseInfos.add(new AllocationReleaseInfo(alloc, remainingAllocation, 0));
            }
        }

        // 2. 按照优先级排序：按可用量升序，ID降序排序
        // 这样做的目的是把小记录先完全释放，大记录承担保留量
        releaseInfos.sort(Comparator.comparingInt(AllocationReleaseInfo::getAvailableQuantity)
            .thenComparing(info -> info.getAllocation().getId(), Comparator.reverseOrder()));

        // 3. 分配保留量，保证尽可能多的记录可以完全释放
        int remainingReserve = quantityToReserve;

        // 从大到小逆序分配保留量
        for (int i = releaseInfos.size() - 1; i >= 0 && remainingReserve > 0; i--) {
            AllocationReleaseInfo info = releaseInfos.get(i);
            // 当前记录分配的保留量
            int reserveForThisAlloc = Math.min(remainingReserve, info.getAvailableQuantity());
            info.setReservedQuantity(reserveForThisAlloc);
            remainingReserve -= reserveForThisAlloc;
        }

        // 4. 计算每个记录的可释放量
        for (AllocationReleaseInfo info : releaseInfos) {
            info.setReleasableQuantity(info.getAvailableQuantity() - info.getReservedQuantity());
        }

        return releaseInfos;
    }

    /**
     * 记录释放分配详情日志
     */
    private void logReleaseAllocationDetails(List<AllocationReleaseInfo> releaseInfos) {
        // 按ID排序以便日志阅读
        List<AllocationReleaseInfo> logInfos = new ArrayList<>(releaseInfos);
        logInfos.sort(Comparator.comparing(info -> info.getAllocation().getId()));

        for (AllocationReleaseInfo info : logInfos) {
            log.debug("锁定记录ID: {}, 仓库ID: {}, 总量: {}, 可用: {}, 分配保留: {}, 可释放: {}", info.getAllocation().getId(),
                info.getAllocation().getWarehouseId(), info.getAllocation().getQuantity(),
                info.getAvailableQuantity(), info.getReservedQuantity(), info.getReleasableQuantity());
        }
    }

    /**
     * 执行释放操作
     */
    private int executeReleaseOperation(Long orderId, Long originalVariantId, int totalToRelease,
                                        List<AllocationReleaseInfo> releaseInfos) {

        // 按照可释放量降序排序，优先处理可释放量大的记录
        releaseInfos.sort(Comparator.comparing(AllocationReleaseInfo::getReleasableQuantity).reversed());

        int remainingToRelease = totalToRelease;
        int actuallyReleasedTotal = 0;
        Map<Long, Integer> allocationChanges = new HashMap<>(); // 记录变化 (allocationId -> releasedQty)

        for (AllocationReleaseInfo releaseInfo : releaseInfos) {
            if (remainingToRelease <= 0)
                break;

            WhsStockAllocation allocation = releaseInfo.getAllocation();
            int releasableQty = releaseInfo.getReleasableQuantity();

            if (releasableQty <= 0) {
                log.debug("锁定记录ID: {} 无可释放量，跳过", allocation.getId());
                continue;
            }

            int actualReleaseQty = Math.min(remainingToRelease, releasableQty);
            log.debug("处理原始分配记录(ID:{}): 可释放={}, 本次释放={}", allocation.getId(), releasableQty, actualReleaseQty);

            try {
                // 记录释放前的库存状态
                WhsStock beforeStock = stockService.getStockByVariantAndWarehouse(allocation.getVariantId(),
                    allocation.getWarehouseId());
                int beforeAvailableStock = beforeStock != null ? beforeStock.getAvailableStock() : 0;
                int beforeLockedStock = beforeStock != null ? beforeStock.getLockedStock() : 0;

                boolean success = stockAllocationService.releaseSpecificAllocation(allocation.getId(), actualReleaseQty,
                    "订单发货释放原始产品库存 (转换需求)");

                if (success) {
                    // 记录释放后的库存状态
                    WhsStock afterStock = stockService.getStockByVariantAndWarehouse(allocation.getVariantId(),
                        allocation.getWarehouseId());
                    int afterAvailableStock = afterStock != null ? afterStock.getAvailableStock() : 0;
                    int afterLockedStock = afterStock != null ? afterStock.getLockedStock() : 0;

                    log.info("成功释放原始变体 {} (订单项 {}) 的锁定库存: 数量: {}, 分配ID: {}, 库存变化: 可用库存 {} -> {}, 锁定库存 {} -> {}",
                        originalVariantId, orderId, actualReleaseQty, allocation.getId(), beforeAvailableStock,
                        afterAvailableStock, beforeLockedStock, afterLockedStock);

                    remainingToRelease -= actualReleaseQty;
                    actuallyReleasedTotal += actualReleaseQty;
                    allocationChanges.put(allocation.getId(), actualReleaseQty);
                } else {
                    log.error("释放原始变体库存分配记录失败: allocationId={}", allocation.getId());
                    throw new ServiceException("释放原始变体库存分配记录失败: allocationId=" + allocation.getId());
                }
            } catch (Exception e) {
                log.error("释放原始变体库存 (转换) 时发生异常: allocationId={}, error={}", allocation.getId(), e.getMessage(), e);
                throw new ServiceException("释放原始变体库存 (转换) 时发生异常: " + e.getMessage());
            }
        }

        // 记录最终释放结果日志
        if (remainingToRelease > 0) {
            log.warn("原始变体 {} (订单项 {}) 转换释放时实际可用库存不足: 有效释放目标 {}, 但仅找到并释放了 {} PCS。", originalVariantId, orderId,
                totalToRelease, actuallyReleasedTotal);
        } else {
            log.info("原始变体 {} (订单项 {}) 转换释放完成: 目标释放 {} PCS, 实际释放 {} PCS, 影响分配记录数: {}", originalVariantId, orderId,
                totalToRelease, actuallyReleasedTotal, allocationChanges.size());
        }

        // 记录释放后的库存状态
        if (log.isInfoEnabled() && !allocationChanges.isEmpty()) {
            logPostReleaseStatus(originalVariantId, orderId, allocationChanges);
        }

        return actuallyReleasedTotal;
    }

    /**
     * 内部辅助类，用于跟踪每个库存分配记录的释放信息
     */
    @lombok.Data
    private static class AllocationReleaseInfo {
        private final WhsStockAllocation allocation;
        private final int availableQuantity; // 可用量(quantity - releasedQuantity)
        private int reservedQuantity; // 需要保留的量
        private int releasableQuantity; // 可释放的量(availableQuantity - reservedQuantity)

        public AllocationReleaseInfo(WhsStockAllocation allocation, int availableQuantity, int reservedQuantity) {
            this.allocation = allocation;
            this.availableQuantity = availableQuantity;
            this.reservedQuantity = reservedQuantity;
            this.releasableQuantity = availableQuantity - reservedQuantity;
        }

        public void setReservedQuantity(int reservedQuantity) {
            this.reservedQuantity = reservedQuantity;
            this.releasableQuantity = this.availableQuantity - this.reservedQuantity;
        }
    }

    /**
     * 聚合出库项
     */
    private void aggregateOutboundItems(Map<String, StockOperationBo.OutboundItem> map, String key,
                                        StockOperationBo.OutboundItem newItem) {

        map.compute(key, (k, existingItem) -> {
            if (existingItem != null) {
                existingItem.setQuantity(existingItem.getQuantity() + newItem.getQuantity());
                return existingItem;
            } else {
                return newItem;
            }
        });
    }

    /**
     * 执行出库操作
     */
    private void performOutboundOperation(Long variantId, Long warehouseId, int quantity, Long orderId,
                                          Long orderItemId, Integer packagingType, Integer packagingQuantity) {

        // 获取库存对象
        WhsStock stock = stockService.getStockByVariantAndWarehouse(variantId, warehouseId);

        if (stock == null) {
            throw new ServiceException(String.format("未找到变体ID: %d, 仓库ID: %d 的库存记录，无法执行出库操作", variantId, warehouseId));
        }

        try {
            log.info("处理发货项出库: variantId={}, warehouseId={}, 发货数量={}（此操作会减少可用库存）", variantId, warehouseId, quantity);

            // 执行出库操作 (使用负数表示出库)
            stockOperationService.updateStock(stock, StockOperationType.OUTBOUND, -quantity, orderId, orderItemId,
                String.format("订单发货出库 (包装类型: %d, 包装数量: %d)（此操作会减少可用库存）", packagingType, packagingQuantity));
        } catch (Exception e) {
            log.error("库存出库操作失败: variantId={}, warehouseId={}, quantity={}, error={}", variantId, warehouseId, quantity,
                e.getMessage(), e);
            throw new ServiceException("出库操作失败: " + e.getMessage());
        }
    }

    /**
     * 检查并释放剩余库存
     *
     * @param orderId     订单ID
     * @param planItems   所有计划项
     * @param planItemMap 计划项映射 (用于快速查找)
     */
    private void checkAndReleaseRemainingStock(Long orderId, List<WhsShipmentPlanItemVo> planItems,
                                               Map<Long, WhsShipmentPlanItemVo> planItemMap) {
        log.info("检查并释放完全发货订单项的剩余锁定库存...");

        if (CollUtil.isEmpty(planItems)) {
            log.warn("订单 {} 检查剩余库存时 planItems 为空，跳过处理", orderId);
            return;
        }

        // 准备阶段: 获取原始订单项信息
        Map<Long, Long> orderItemIdToOriginalVariantIdMap = loadOriginalOrderItems(planItems);

        // 构建订单项映射和状态
        Map<Long, Boolean> orderItemFullyShippedStatus = new HashMap<>();

        // 检查每个计划项是否已完全发货
        checkPlanItemsShippingStatus(planItems, planItemMap, orderItemFullyShippedStatus);

        // 释放已完全发货订单项的剩余库存
        releaseRemainingStockForFullyShippedItems(orderId, orderItemFullyShippedStatus,
            orderItemIdToOriginalVariantIdMap);
    }

    /**
     * 加载原始订单项信息
     */
    private Map<Long, Long> loadOriginalOrderItems(List<WhsShipmentPlanItemVo> planItems) {
        Set<Long> involvedOrderItemIds = planItems.stream().map(WhsShipmentPlanItemVo::getOrderId)
            .filter(Objects::nonNull).collect(Collectors.toSet());

        if (involvedOrderItemIds.isEmpty()) {
            log.warn("未找到关联的订单项ID");
            return Collections.emptyMap();
        }

        List<WhsOrderItem> originalOrderItems = orderItemService.listByIds(involvedOrderItemIds);
        Map<Long, Long> result = originalOrderItems.stream()
            .filter(item -> item.getId() != null && item.getVariantId() != null)
            .collect(Collectors.toMap(WhsOrderItem::getId, WhsOrderItem::getVariantId, (v1, v2) -> v1)); // 保留第一个以防重复

        log.debug("获取到 {} 个原始订单项信息", result.size());
        return result;
    }

    /**
     * 检查计划项的发货状态
     */
    private void checkPlanItemsShippingStatus(List<WhsShipmentPlanItemVo> planItems,
                                              Map<Long, WhsShipmentPlanItemVo> planItemMap, Map<Long, Boolean> orderItemFullyShippedStatus) {

        for (WhsShipmentPlanItemVo planItem : planItems) {
            if (planItem.getId() == null)
                continue;

            int unshipped = calculateUnshippedQuantity(planItem, planItemMap);

            if (unshipped == 0) {
                log.debug("计划项 {} 已完全发货 (未发货数量: 0)", planItem.getId());
            } else {
                log.debug("计划项 {} 未完全发货 (剩余 {} 件)", planItem.getId(), unshipped);
                // 如果任何一个计划项未完全发货，则其所属订单项标记为未完全发货
                if (planItem.getOrderId() != null) {
                    orderItemFullyShippedStatus.put(planItem.getOrderId(), false);
                }
            }
        }
    }

    /**
     * 释放已完全发货订单项的剩余库存
     */
    private void releaseRemainingStockForFullyShippedItems(Long orderId, Map<Long, Boolean> orderItemFullyShippedStatus,
                                                           Map<Long, Long> orderItemIdToOriginalVariantIdMap) {

        // 对于标记为已完全发货的订单项，释放其对应原始变体的所有剩余锁定库存
        for (Map.Entry<Long, Boolean> entry : orderItemFullyShippedStatus.entrySet()) {
            Long orderItemId = entry.getKey();
            Boolean isFullyShipped = entry.getValue();

            if (!Boolean.TRUE.equals(isFullyShipped)) {
                continue; // 跳过未完全发货的订单项
            }

            // 获取该订单项对应的原始变体ID
            Long originalVariantId = orderItemIdToOriginalVariantIdMap.get(orderItemId);

            if (originalVariantId == null) {
                log.error("订单项 {} 已标记为完全发货，但在映射中找不到对应的原始变体ID，无法释放剩余库存！", orderItemId);
                continue;
            }

            // 检查是否涉及转换
            boolean hasConversion = shipmentConversionService.hasConversionForOriginalItem(orderId, originalVariantId);

            if (hasConversion) {
                log.info("订单项 {} (原始变体 {}) 涉及转换，其剩余库存由转换逻辑处理，此处跳过释放。", orderItemId, originalVariantId);
                continue;
            }

            // 如果没有转换，则释放原始变体的剩余库存
            releaseRemainingStockForOriginalVariant(orderId, orderItemId, originalVariantId);
        }
    }

    /**
     * 释放原始变体的剩余库存
     */
    private void releaseRemainingStockForOriginalVariant(Long orderId, Long orderItemId, Long originalVariantId) {
        log.info("订单项 {} (原始变体 {}) 未涉及转换，释放其所有剩余锁定库存", orderItemId, originalVariantId);

        LambdaQueryWrapper<WhsStockAllocation> query = Wrappers.lambdaQuery(WhsStockAllocation.class)
            .eq(WhsStockAllocation::getOrderId, orderId).eq(WhsStockAllocation::getVariantId, originalVariantId)
            .eq(WhsStockAllocation::getStatus, StockAllocationStatus.ALLOCATED.getValue());

        List<WhsStockAllocation> remainingAllocations = stockAllocationService.list(query);

        if (CollUtil.isEmpty(remainingAllocations)) {
            log.debug("未找到订单项 {} 原始变体 {} 的剩余锁定记录", orderItemId, originalVariantId);
            return;
        }

        log.debug("找到订单项 {} 原始变体 {} 的 {} 条剩余锁定记录进行释放", orderItemId, originalVariantId, remainingAllocations.size());

        for (WhsStockAllocation allocation : remainingAllocations) {
            try {
                int releasedQuantity = allocation.getReleasedQuantity() != null ? allocation.getReleasedQuantity() : 0;
                int remainingToRelease = allocation.getQuantity() - releasedQuantity;

                if (remainingToRelease <= 0) {
                    log.debug("原始库存分配记录 {} 已经全部释放，跳过", allocation.getId());
                    continue;
                }

                log.info("释放订单项 {} 原始变体 {} 的剩余锁定库存: {} 件 (Allocation ID: {}, 总分配: {}, 已释放: {})", orderItemId,
                    originalVariantId, remainingToRelease, allocation.getId(), allocation.getQuantity(),
                    releasedQuantity);

                stockAllocationService.releaseSpecificAllocation(allocation.getId(), remainingToRelease,
                    "订单项计划发货完毕，释放原始变体所有剩余锁定库存");
            } catch (Exception e) {
                log.error("释放原始变体 {} (Allocation ID: {}) 的剩余库存时发生异常: {}", originalVariantId, allocation.getId(),
                    e.getMessage(), e);
            }
        }
    }

    /**
     * 计算计划项的未发货数量
     *
     * @param planItem           计划项VO (基础信息)
     * @param currentPlanItemMap 包含当前发货相关计划项VO信息的Map (键: planItemId)
     * @return 未发货数量 (>= 0)
     */
    private int calculateUnshippedQuantity(WhsShipmentPlanItemVo planItem,
                                           Map<Long, WhsShipmentPlanItemVo> currentPlanItemMap) {
        if (planItem == null || planItem.getId() == null || planItem.getQuantity() == null) {
            log.warn("calculateUnshippedQuantity 接收到无效的 planItem，返回 0");
            return 0;
        }

        int requiredQuantity = planItem.getQuantity();
        int shippedQuantity = 0;

        // 尝试从Map中获取更新的信息
        if (currentPlanItemMap != null && currentPlanItemMap.containsKey(planItem.getId())) {
            WhsShipmentPlanItemVo currentItem = currentPlanItemMap.get(planItem.getId());

            if (currentItem != null) {
                // 使用Map中的数据 (优先)
                if (currentItem.getQuantity() != null) {
                    requiredQuantity = currentItem.getQuantity();
                }

                if (currentItem.getShippedQuantity() != null) {
                    shippedQuantity = currentItem.getShippedQuantity();
                }
            } else if (planItem.getShippedQuantity() != null) {
                // 回退使用传入的planItem的已发货数量
                shippedQuantity = planItem.getShippedQuantity();
            }
        } else if (planItem.getShippedQuantity() != null) {
            // 无Map或Map中无此项，使用传入的已发货数量
            shippedQuantity = planItem.getShippedQuantity();
        }

        return Math.max(0, requiredQuantity - shippedQuantity);
    }

    /**
     * 辅助方法，获取包装数量并提供默认值1
     */
    private int getPackagingQuantity(WhsShipmentConversionVo conversion) {
        // 从数据库中获取变体的包装数量，而不是直接从conversion中获取
        if (conversion == null || conversion.getActualVariantId() == null) {
            return 1;
        }

        Long actualVariantId = conversion.getActualVariantId();

        try {
            // 查询包装变体包含的子变体数量之和
            Map<Long, Integer> childQuantities = packageItemService.getChildVariantQuantities(actualVariantId);

            if (childQuantities == null || childQuantities.isEmpty()) {
                log.debug("变体ID {} 在包装表中未找到子项记录，使用默认包装数量:1", actualVariantId);
                return 1;
            }

            // 计算所有子项数量之和
            int totalQuantity = childQuantities.values().stream().mapToInt(Integer::intValue).sum();

            if (totalQuantity <= 0) {
                log.warn("变体ID {} 在包装表中的子项数量总和为 {}，使用默认包装数量:1", actualVariantId, totalQuantity);
                return 1;
            }

            log.debug("变体ID {} 从数据库获取的包装数量: {}", actualVariantId, totalQuantity);
            return totalQuantity;
        } catch (Exception e) {
            log.error("获取变体ID {} 的包装数量时发生异常: {}", actualVariantId, e.getMessage());
            return 1; // 发生异常时返回默认值
        }
    }

    /**
     * 辅助方法，获取原始变体的包装数量
     *
     * @param conversion 转换记录
     * @return 原始变体的包装数量，如果未定义则返回1
     */
    private int getOriginalPackagingQuantity(WhsShipmentConversionVo conversion) {
        // 从数据库中获取原始变体的包装数量，而不是直接从转换记录中获取
        if (conversion == null || conversion.getOriginalVariantId() == null) {
            return 1;
        }

        Long originalVariantId = conversion.getOriginalVariantId();

        try {
            // 查询原始变体包含的子变体数量之和
            Map<Long, Integer> childQuantities = packageItemService.getChildVariantQuantities(originalVariantId);

            if (childQuantities == null || childQuantities.isEmpty()) {
                log.debug("原始变体ID {} 在包装表中未找到子项记录，使用默认包装数量:1", originalVariantId);
                return 1;
            }

            // 计算所有子项数量之和
            int totalQuantity = childQuantities.values().stream().mapToInt(Integer::intValue).sum();

            if (totalQuantity <= 0) {
                log.warn("原始变体ID {} 在包装表中的子项数量总和为 {}，使用默认包装数量:1", originalVariantId, totalQuantity);
                return 1;
            }

            log.debug("原始变体ID {} 从数据库获取的包装数量: {}", originalVariantId, totalQuantity);
            return totalQuantity;
        } catch (Exception e) {
            log.error("获取原始变体ID {} 的包装数量时发生异常: {}", originalVariantId, e.getMessage());
            return 1; // 发生异常时返回默认值
        }
    }

    /**
     * 计算有效PCS数量，考虑原始变体和目标变体的包装差异
     *
     * @param actualReleasedPcs    实际释放的原始变体PCS数量
     * @param originalPackagingQty 原始变体的包装数量
     * @return 计算后的有效PCS进度
     */
    private int calculateEffectivePcsCount(int actualReleasedPcs, int originalPackagingQty) {
        // 如果原始变体是展示盒等多包装物品
        if (originalPackagingQty > 1) {
            // 例如：释放7个展示盒（每盒40个），相当于7*40=280个单品的等价物
            // 如果目标是要240个单品，释放7个展示盒（280个单品）等同于完成240个单品的等价物
            return actualReleasedPcs * originalPackagingQty;
        }
        return actualReleasedPcs; // 单品情况下直接返回原始数量
    }

    /**
     * 转换释放结果记录类
     * 用于跟踪每个转换记录的释放结果
     *
     * @param conversionId      转换记录ID
     * @param originalVariantId 原始变体ID
     * @param packagingQty      包装数量
     * @param requestedPcs      请求释放的PCS数量
     * @param releasedPcs       实际释放的PCS数量
     * @param satisfiedItems    满足的件数
     */
    private record ConversionReleaseResult(Long conversionId, Long originalVariantId, int packagingQty,
                                           int requestedPcs, int releasedPcs, int satisfiedItems) {
    }

    /**
     * 记录释放后的库存状态
     */
    private void logPostReleaseStatus(Long originalVariantId, Long orderId, Map<Long, Integer> allocationChanges) {
        List<Long> allocationIds = new ArrayList<>(allocationChanges.keySet());
        // 使用查询
        LambdaQueryWrapper<WhsStockAllocation> queryWrapper = Wrappers.lambdaQuery(WhsStockAllocation.class)
            .in(WhsStockAllocation::getId, allocationIds);
        List<WhsStockAllocation> updatedAllocations = stockAllocationService.list(queryWrapper);

        if (!CollUtil.isEmpty(updatedAllocations)) {
            log.info("转换记录释放后的库存数据 - 变体ID: {}, 订单ID: {}, 处理了 {} 条锁定记录:", originalVariantId, orderId,
                updatedAllocations.size());

            for (WhsStockAllocation alloc : updatedAllocations) {
                Integer released = allocationChanges.get(alloc.getId());
                if (released == null)
                    continue;

                int releasedQty = alloc.getReleasedQuantity() != null ? alloc.getReleasedQuantity() : 0;
                int remainingAllocation = alloc.getQuantity() - releasedQty;
                log.info("  锁定记录ID: {}, 仓库ID: {}, 总锁定数量: {}, 已释放数量: {}, 剩余可释放: {}, 状态: {}, 本次释放: {}", alloc.getId(),
                    alloc.getWarehouseId(), alloc.getQuantity(), releasedQty, remainingAllocation,
                    alloc.getStatus(), released);
            }
        }
    }
}
