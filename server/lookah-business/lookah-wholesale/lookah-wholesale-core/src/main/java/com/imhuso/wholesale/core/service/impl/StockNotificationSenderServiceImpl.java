package com.imhuso.wholesale.core.service.impl;

import com.aizuda.snailjob.client.core.annotation.Retryable;
import com.aizuda.snailjob.client.core.retryer.RetryType;
import com.imhuso.wholesale.core.callback.StockNotifyRetryCallback;
import com.imhuso.wholesale.core.domain.bo.front.StockNotifyTemplateBo;
import com.imhuso.wholesale.core.service.IWhsMailService;
import com.imhuso.wholesale.core.service.IStockNotificationSenderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 库存通知发送服务实现类
 * <p>
 * 专注于库存到货通知的发送功能，提供重试机制确保通知可靠送达。
 * 数据处理应由调用方完成。
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StockNotificationSenderServiceImpl implements IStockNotificationSenderService {

    private final IWhsMailService mailService;

    /**
     * 发送库存到货客户通知
     * <p>
     * 发送客户端邮件通知。支持重试机制确保通知可靠送达。
     * 调用方需确保提供完整的模板数据。
     * </p>
     *
     * @param templateBo 库存通知模板对象，包含通知相关信息
     */
    @Override
    @Retryable(scene = "客户到货通知", retryStrategy = RetryType.LOCAL_REMOTE, retryCompleteCallback = StockNotifyRetryCallback.class)
    public void sendStockCustomerNotification(StockNotifyTemplateBo templateBo) {
        try {
            log.info("发送库存到货客户通知 - 客户邮箱: {}, 模板: {}", templateBo.getCustomerEmail(), templateBo.getCustomerTemplate());

            mailService.sendCustomerTemplateEmail(
                templateBo.getCustomerTemplate(),
                templateBo.toBuilder().title(templateBo.getCustomerTitle()).build(),
                templateBo.getCustomerEmail());

            log.info("库存到货客户通知发送成功 - 客户邮箱: {}", templateBo.getCustomerEmail());
        } catch (Exception e) {
            log.error("库存到货客户通知发送失败 - 客户邮箱: {}, 错误: {}", templateBo.getCustomerEmail(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 发送库存到货管理员通知
     * <p>
     * 发送管理员邮件通知。支持重试机制确保通知可靠送达。
     * 调用方需确保提供完整的模板数据。
     * </p>
     *
     * @param templateBo 库存通知模板对象，包含通知相关信息
     */
    @Override
    @Retryable(scene = "管理员到货通知", retryStrategy = RetryType.LOCAL_REMOTE, retryCompleteCallback = StockNotifyRetryCallback.class)
    public void sendStockAdminNotification(StockNotifyTemplateBo templateBo) {
        try {
            log.info("发送库存到货管理员通知 - 管理员邮箱: {}, 模板: {}", templateBo.getAdminEmail(), templateBo.getAdminTemplate());

            mailService.sendAdminTemplateEmail(
                templateBo.getAdminTemplate(),
                templateBo.toBuilder().title(templateBo.getAdminTitle()).build(),
                templateBo.getAdminEmail());

            log.info("库存到货管理员通知发送成功 - 管理员邮箱: {}", templateBo.getAdminEmail());
        } catch (Exception e) {
            log.error("库存到货管理员通知发送失败 - 管理员邮箱: {}, 错误: {}", templateBo.getAdminEmail(), e.getMessage(), e);
            throw e;
        }
    }
}
