package com.imhuso.wholesale.core.strategy.orderNotification.impl;

import com.imhuso.common.core.domain.dto.NotificationDto;
import com.imhuso.common.core.service.NotificationService;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderVo;
import com.imhuso.wholesale.core.event.WhsOrderEvent.WhsOrderEventType;
import com.imhuso.wholesale.core.strategy.orderNotification.IOrderNotificationStrategy;
import com.imhuso.wholesale.core.service.INotificationConfigService;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;

import static com.imhuso.wholesale.core.event.WhsOrderEvent.WhsOrderEventType.*;

/**
 * SSE站内信订单通知策略实现
 * <p>
 * 使用SSE推送方式发送订单通知到管理员
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SseOrderNotificationStrategy implements IOrderNotificationStrategy {

    private final NotificationService notificationService;
    private final INotificationConfigService notificationConfigService;

    /**
     * 通知配置映射表
     */
    private static final Map<WhsOrderEventType, NotificationConfig> CONFIG_MAP = new EnumMap<>(WhsOrderEventType.class);

    static {
        // 初始化配置映射
        CONFIG_MAP.put(ORDER_CREATED, NotificationConfig.ORDER_CREATED);
        CONFIG_MAP.put(ORDER_PROCESSING, NotificationConfig.ORDER_PROCESSING);
        CONFIG_MAP.put(ORDER_CANCELED, NotificationConfig.ORDER_CANCELED);
        CONFIG_MAP.put(ORDER_PAID, NotificationConfig.ORDER_PAID);
        CONFIG_MAP.put(ORDER_SHIPPED, NotificationConfig.ORDER_SHIPPED);
        CONFIG_MAP.put(ORDER_PARTIAL_SHIPPED, NotificationConfig.ORDER_PARTIAL_SHIPPED);
        CONFIG_MAP.put(ORDER_COMPLETED, NotificationConfig.ORDER_COMPLETED);
        CONFIG_MAP.put(ORDER_INVOICE_SENT, NotificationConfig.ORDER_INVOICE_SENT);
        CONFIG_MAP.put(PACKAGE_STATUS_CHANGED, NotificationConfig.PACKAGE_STATUS_CHANGED);
    }

    @Override
    public String getName() {
        return "站内信";
    }

    /**
     * 订单通知配置枚举
     * 集中管理所有通知配置，使配置更加清晰和可维护
     */
    @Getter
    private enum NotificationConfig {
        // 订单创建：仅通知管理员
        ORDER_CREATED(true, false, (isAdmin, order) -> isAdmin ? String.format("📦 新订单：%s（%s）", order.getCustomerOrderNo(), order.getShippingName()) : null),

        // 订单处理中：通知管理员
        ORDER_PROCESSING(true, false, (isAdmin, order) -> isAdmin ? String.format("⚙️ 订单处理中：%s", order.getCustomerOrderNo()) : null),

        // 订单取消：通知管理员
        ORDER_CANCELED(true, false, (isAdmin, order) -> isAdmin ? String.format("❌ 订单已取消：%s", order.getCustomerOrderNo()) : null),

        // 订单已付款：通知管理员
        ORDER_PAID(true, false, (isAdmin, order) -> isAdmin ? String.format("💰 订单已付款：%s", order.getCustomerOrderNo()) : null),

        // 订单已发货：通知管理员
        ORDER_SHIPPED(true, false, (isAdmin, order) -> isAdmin ? String.format("🚚 订单已发货：%s", order.getCustomerOrderNo()) : null),

        // 订单部分发货：通知管理员
        ORDER_PARTIAL_SHIPPED(true, false, (isAdmin, order) -> isAdmin ? String.format("📦 订单部分发货：%s", order.getCustomerOrderNo()) : null),

        // 订单已完成：通知管理员
        ORDER_COMPLETED(true, false, (isAdmin, order) -> isAdmin ? String.format("✅ 订单已完成：%s", order.getCustomerOrderNo()) : null),

        // 发票已发送：通知管理员
        ORDER_INVOICE_SENT(true, false, (isAdmin, order) -> isAdmin ? String.format("📄 发票已发送：%s", order.getCustomerOrderNo()) : null),

        // 包裹状态变更：通知管理员
        PACKAGE_STATUS_CHANGED(true, false, (isAdmin, order) -> isAdmin ? String.format("📦 包裹状态更新：%s", order.getCustomerOrderNo()) : null);

        // 是否通知管理员
        private final boolean notifyAdmin;

        // 是否通知客户（站内信暂不支持客户通知）
        private final boolean notifyCustomer;

        // 消息内容获取函数
        private final BiFunction<Boolean, WhsOrderVo, String> messageResolver;

        NotificationConfig(boolean notifyAdmin, boolean notifyCustomer, BiFunction<Boolean, WhsOrderVo, String> messageResolver) {
            this.notifyAdmin = notifyAdmin;
            this.notifyCustomer = notifyCustomer;
            this.messageResolver = messageResolver;
        }

        /**
         * 获取通知消息内容
         *
         * @param isAdmin 是否为管理员
         * @param order   订单对象
         * @return 消息内容
         */
        public String getMessage(boolean isAdmin, WhsOrderVo order) {
            return messageResolver.apply(isAdmin, order);
        }
    }

    @Override
    public boolean isAdminNotificationEnabled(WhsOrderEventType eventType) {
        NotificationConfig config = CONFIG_MAP.get(eventType);
        return config != null && config.isNotifyAdmin();
    }

    @Override
    public boolean isCustomerNotificationEnabled(WhsOrderEventType eventType) {
        // 站内信暂不支持客户通知
        return false;
    }

    @Override
    public boolean sendAdminNotification(WhsOrderVo order, WhsOrderEventType eventType) {
        NotificationConfig config = CONFIG_MAP.get(eventType);
        if (config == null || !config.isNotifyAdmin()) {
            log.debug("站内信管理员通知未启用: eventType={}", eventType);
            return false;
        }

        try {
            // 获取通知接收者用户ID列表
            List<Long> adminUserIds = getAdminUserIds();
            if (adminUserIds.isEmpty()) {
                log.warn("未配置站内信通知接收者，无法发送通知: eventType={}, orderId={}", eventType, order.getId());
                return false;
            }

            // 生成通知消息
            String message = config.getMessage(true, order);
            if (StringUtils.isEmpty(message)) {
                log.warn("生成通知消息失败: eventType={}, orderId={}", eventType, order.getId());
                return false;
            }

            // 构建通知对象
            NotificationDto notification = buildNotificationDto(order, eventType, message);

            // 发送站内信通知给所有配置的管理员
            notificationService.sendSseNotification(notification, adminUserIds);

            log.info("站内信管理员通知发送完成: eventType={}, orderId={}, 接收者数量={}", eventType, order.getId(), adminUserIds.size());
            return true;

        } catch (Exception e) {
            log.error("发送站内信管理员通知异常: eventType={}, orderId={}, error={}", eventType, order.getId(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean sendCustomerNotification(WhsOrderVo order, WhsOrderEventType eventType) {
        // 站内信暂不支持客户通知
        log.debug("站内信不支持客户通知: eventType={}, orderId={}", eventType, order.getId());
        return false;
    }


    /**
     * 构建通知DTO对象
     *
     * @param order     订单信息
     * @param eventType 事件类型
     * @param message   基础消息
     * @return 通知DTO对象
     */
    private NotificationDto buildNotificationDto(WhsOrderVo order, WhsOrderEventType eventType, String message) {
        // 订单详细信息
        Map<String, Object> orderData = new HashMap<>();
        orderData.put("id", order.getId());
        orderData.put("customerOrderNo", order.getCustomerOrderNo());
        orderData.put("orderNo", order.getOrderNo());
        orderData.put("shippingName", order.getShippingName());
        orderData.put("totalAmount", order.getTotalAmount());
        orderData.put("orderStatus", order.getOrderStatus());
        orderData.put("paymentStatus", order.getPaymentStatus());
        orderData.put("shipmentStatus", order.getShipmentStatus());
        orderData.put("createTime", order.getCreateTime());

        // 将订单信息作为原始数据保存
        Map<String, Object> rawData = new HashMap<>();
        rawData.put("eventType", eventType.name());
        rawData.put("timestamp", System.currentTimeMillis());
        rawData.put("order", orderData);

        return NotificationDto.builder().type("notice") // 使用系统通知类型
            .title("订单通知").message(message).actionUrl("/wholesale/order/" + order.getId()).level("info") // 订单通知级别
            .autoClose(true).duration(8000) // 8秒显示时间
            .avatar("/src/assets/images/order.svg").rawData(rawData).build();
    }

    /**
     * 获取管理员用户ID列表
     *
     * @return 用户ID列表
     */
    private List<Long> getAdminUserIds() {
        List<String> adminUserIdsConfig = notificationConfigService.getOrderSseAdminUserIds();
        if (adminUserIdsConfig.isEmpty()) {
            return List.of();
        }

        try {
            return adminUserIdsConfig.stream().filter(StringUtils::isNotEmpty).map(Long::valueOf).toList();
        } catch (NumberFormatException e) {
            log.error("解析管理员用户ID配置失败: config={}, error={}", adminUserIdsConfig, e.getMessage());
            return List.of();
        }
    }
}
