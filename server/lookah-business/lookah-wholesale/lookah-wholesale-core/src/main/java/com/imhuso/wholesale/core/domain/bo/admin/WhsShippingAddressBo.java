package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.wholesale.core.domain.WhsShippingAddress;
import com.imhuso.wholesale.core.validate.ValidPostalCode;
import com.imhuso.wholesale.core.validate.ValidRecipientName;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会员地址管理业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WhsShippingAddress.class)
@ValidPostalCode(groups = {AddGroup.class, EditGroup.class})
@ValidRecipientName(groups = {AddGroup.class, EditGroup.class})
public class WhsShippingAddressBo extends BaseEntity {

    /**
     * 地址ID
     */
    @NotNull(message = "地址ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 会员ID
     */
    @NotNull(message = "会员ID不能为空", groups = {AddGroup.class})
    private Long memberId;

    /**
     * 名
     */
    @NotBlank(message = "名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String firstName;

    /**
     * 姓
     */
    @NotBlank(message = "姓不能为空", groups = {AddGroup.class, EditGroup.class})
    private String lastName;

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空", groups = {AddGroup.class, EditGroup.class})
    @Pattern(regexp = "^\\+?[0-9\\-\\s]+$", message = "联系电话格式不正确", groups = {AddGroup.class, EditGroup.class})
    private String phone;

    /**
     * 联系邮箱
     */
    @NotBlank(message = "联系邮箱不能为空", groups = {AddGroup.class, EditGroup.class})
    private String email;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 国家
     */
    @NotBlank(message = "国家不能为空", groups = {AddGroup.class, EditGroup.class})
    private String country;

    /**
     * 州
     */
    @NotBlank(message = "州不能为空", groups = {AddGroup.class, EditGroup.class})
    private String state;

    /**
     * 城市
     */
    @NotBlank(message = "城市不能为空", groups = {AddGroup.class, EditGroup.class})
    private String city;

    /**
     * a地址行1
     */
    @NotBlank(message = "地址行1不能为空", groups = {AddGroup.class, EditGroup.class})
    private String addressLine1;

    /**
     * 地址行2
     */
    private String addressLine2;

    /**
     * 邮政编码
     */
    @NotBlank(message = "邮政编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String zipCode;

    /**
     * 送货说明
     */
    @Size(max = 200, message = "送货说明不能超过200个字符", groups = {AddGroup.class, EditGroup.class})
    private String deliveryNotes;

    /**
     * 是否默认地址（0否 1是）
     */
    private String isDefault;

    /**
     * 搜索关键词（用于多字段模糊搜索）
     */
    private String searchKeyword;
}
