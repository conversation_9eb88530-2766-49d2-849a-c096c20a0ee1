package com.imhuso.wholesale.core.domain.vo.admin;

import com.imhuso.common.translation.annotation.Translation;
import com.imhuso.wholesale.core.constant.TransConstant;
import com.imhuso.wholesale.core.domain.WhsOrderShipment;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 批发订单发货后台视图对象
 * 采用"发货 > 包裹 > 产品"的层次化结构
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsOrderShipment.class)
public class WhsOrderShipmentVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 发货ID
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 发货状态
     */
    private Integer shipmentStatus;

    /**
     * 发货状态文本
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, other = "shipmentStatus", mapper = "shipmentStatus")
    private String shipmentStatusText;

    /**
     * 批次号
     */
    private Integer batchNumber;

    /**
     * 发货日期
     */
    private Date shippedDate;

    /**
     * 物流包裹列表，每个包裹包含其对应的发货项列表
     * 体现"发货 > 包裹 > 产品"的层次化结构
     */
    private List<WhsLogisticsPackageVo> logisticsPackages;

    /**
     * 发货汇总
     */
    private String shipmentSummary;
}
