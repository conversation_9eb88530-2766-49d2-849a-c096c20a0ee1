package com.imhuso.wholesale.core.callback;

import java.util.Arrays;

import org.springframework.stereotype.Component;

import com.aizuda.snailjob.client.core.callback.RetryCompleteCallback;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.domain.vo.warehouse.WarehouseShipmentRecord;
import com.imhuso.wholesale.core.service.IWecomService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 海外仓发货取消重试完成回调处理
 * (采用 doSuccessCallback / doMaxRetryCallback 结构)
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public class ShipmentCancellationRetryCallback implements RetryCompleteCallback {

    private final IWecomService wecomService; // 注入现有的 WecomService

    @Override
    public void doSuccessCallback(String sceneName, String executorName, Object[] params) {
        WarehouseShipmentRecord record = parseRecordFromParams(params);
        if (record != null) {
            log.info("海外仓发货取消重试成功 - Scene=[{}], Executor=[{}], Record=[{}]", sceneName, executorName, record);
        } else {
            log.info("海外仓发货取消重试成功 (参数解析失败) - Scene=[{}], Executor=[{}], Params=[{}]", sceneName, executorName, Arrays.toString(params));
        }
    }

    @Override
    public void doMaxRetryCallback(String sceneName, String executorName, Object[] params) {
        log.error("海外仓发货取消达到最大重试次数 - Scene=[{}], Executor=[{}], Params=[{}]", sceneName, executorName, Arrays.toString(params));

        WarehouseShipmentRecord record = parseRecordFromParams(params);
        String errorMessage = "达到最大重试次数，取消失败"; // 无法获取具体异常信息

        // 发送企业微信通知
        try {
            String title = "【警告】海外仓发货取消失败";
            String content;
            if (record != null) {
                log.warn("海外仓发货取消达到最大重试次数，将发送企业微信通知. Record=[{}]", record);
                content = buildMarkdownMessage(record, errorMessage);
            } else {
                // 构造一个临时的 record 用于通知格式
                WarehouseShipmentRecord tempRecord = new WarehouseShipmentRecord();
                tempRecord.setOrderNo("未知订单");
                tempRecord.setWarehouseId(0L);
                tempRecord.setPackageId(0L);
                tempRecord.setExternalShipmentId("未知外部单号");
                log.warn("海外仓发货取消达到最大重试次数，参数解析异常，将发送通用企业微信通知.");
                content = buildMarkdownMessage(tempRecord, errorMessage);
            }
            // 调用 WecomService 发送消息
            wecomService.sendMarkdownMessage(title, content);

        } catch (Exception notifyEx) {
            log.error("发送企业微信通知时发生异常: {}", notifyEx.getMessage(), notifyEx);
        }
    }

    /**
     * 从参数中安全地解析 WarehouseShipmentRecord
     */
    private WarehouseShipmentRecord parseRecordFromParams(Object[] params) {
        if (params != null && params.length > 0 && params[0] instanceof WarehouseShipmentRecord) {
            return (WarehouseShipmentRecord) params[0];
        } else {
            log.error("无法从重试回调参数中获取有效的 WarehouseShipmentRecord 参数！Params: {}", Arrays.toString(params));
            return null;
        }
    }

    /**
     * 构建用于企业微信的 Markdown 消息
     */
    private String buildMarkdownMessage(WarehouseShipmentRecord record, String errorMessage) {
        // 可以添加时间戳
        return "**【警告】海外仓发货取消失败**\n" + "> 请相关人员手动处理以下订单的海外仓取消操作。\n\n" + "- **订单号:** `" + StringUtils.defaultIfBlank(record.getOrderNo(), "N/A") + "`\n" + "- **仓库ID:** `" + record.getWarehouseId() + "`\n" + "- **包裹ID:** `" + record.getPackageId() + "`\n" + "- **外部单号:** `" + StringUtils.defaultIfBlank(record.getExternalShipmentId(), "N/A") + "`\n" + "- **失败原因:** <font color=\"warning\">" + StringUtils.defaultIfBlank(errorMessage, "未知错误") + "</font>\n";
    }
}
