package com.imhuso.wholesale.core.strategy.orderNotification.impl;

import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderVo;
import com.imhuso.wholesale.core.event.WhsOrderEvent.WhsOrderEventType;
import com.imhuso.wholesale.core.service.IWecomService;
import com.imhuso.wholesale.core.strategy.orderNotification.IOrderNotificationStrategy;
import com.imhuso.wholesale.core.service.INotificationConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import cn.hutool.core.util.StrUtil;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.EnumSet;
import java.util.List;
import java.util.Set;

import static com.imhuso.wholesale.core.event.WhsOrderEvent.WhsOrderEventType.*;

/**
 * 企业微信群机器人订单通知策略实现
 * <p>
 * 使用企业微信群机器人发送订单通知到指定群聊
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WecomOrderNotificationStrategy implements IOrderNotificationStrategy {

    private final IWecomService wecomService;
    private final INotificationConfigService notificationConfigService;

    // Emoji常量定义
    private static final String EMOJI_BELL = "🔔";
    private static final String EMOJI_PACKAGE = "📦";
    private static final String EMOJI_CALENDAR = "📅";
    private static final String EMOJI_EMAIL = "📧";
    private static final String EMOJI_PHONE = "📱";
    private static final String EMOJI_USER = "👤";
    private static final String EMOJI_TAG = "🏷️";
    private static final String EMOJI_MONEY = "💰";
    private static final String EMOJI_TRUCK = "🚚";
    private static final String EMOJI_CHECK = "✅";
    private static final String EMOJI_CANCEL = "❌";
    private static final String EMOJI_ALERT = "⚠️";
    private static final String EMOJI_STAR = "⭐";
    private static final String EMOJI_NOTE = "📝";
    private static final String EMOJI_NEW = "🆕";
    private static final String EMOJI_CLOCK = "⏰";
    private static final String EMOJI_LOCATION = "📍";



    // 支持管理员通知的事件类型
    private static final Set<WhsOrderEventType> ADMIN_NOTIFICATION_EVENTS = EnumSet.of(ORDER_CREATED, ORDER_CANCELED, ORDER_PAID, ORDER_SHIPPED, ORDER_PARTIAL_SHIPPED, ORDER_COMPLETED, PACKAGE_STATUS_CHANGED);

    @Override
    public String getName() {
        return "wecom";
    }

    @Override
    public boolean isAdminNotificationEnabled(WhsOrderEventType eventType) {
        return ADMIN_NOTIFICATION_EVENTS.contains(eventType);
    }

    @Override
    public boolean isCustomerNotificationEnabled(WhsOrderEventType eventType) {
        // 企业微信群机器人不支持客户通知
        return false;
    }

    @Override
    public boolean sendAdminNotification(WhsOrderVo order, WhsOrderEventType eventType) {
        if (!isAdminNotificationEnabled(eventType) || !isWecomEnabled()) {
            log.debug("企业微信订单通知未启用: eventType={}, enabled={}", eventType, isWecomEnabled());
            return false;
        }

        // 获取数据库配置的webhook key列表
        List<String> webhookKeys = notificationConfigService.getOrderWecomKeys();
        if (webhookKeys.isEmpty()) {
            log.warn("未配置订单通知企业微信 webhook key，跳过企业微信发送");
            return false;
        }

        try {
            // 构建企业微信消息内容
            String title = getEventTitle(eventType);
            String content = buildMarkdownContent(order, eventType);

            // 向每个配置的webhook key发送消息
            int successCount = 0;
            for (String webhookKey : webhookKeys) {
                try {
                    wecomService.sendMarkdownMessage(title, content, webhookKey);
                    successCount++;
                    log.info("企业微信订单通知发送成功: orderId={}, eventType={}, webhookKey={}", order.getId(), eventType, webhookKey);
                } catch (Exception e) {
                    log.error("企业微信订单通知发送失败: orderId={}, eventType={}, webhookKey={}, error={}",
                        order.getId(), eventType, webhookKey, e.getMessage(), e);
                }
            }

            boolean overallSuccess = successCount > 0;
            log.info("企业微信订单通知发送完成: orderId={}, eventType={}, 成功发送 {}/{} 个群", order.getId(), eventType, successCount, webhookKeys.size());
            return overallSuccess;
        } catch (Exception e) {
            log.error("企业微信订单通知发送失败: orderId={}, eventType={}, error={}", order.getId(), eventType, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean sendCustomerNotification(WhsOrderVo order, WhsOrderEventType eventType) {
        // 企业微信群机器人不支持客户通知
        return false;
    }

    /**
     * 获取事件标题
     */
    private String getEventTitle(WhsOrderEventType eventType) {
        return getNotificationTitle(eventType);
    }

    /**
     * 构建Markdown内容
     */
    private String buildMarkdownContent(WhsOrderVo order, WhsOrderEventType eventType) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        StringBuilder content = new StringBuilder();

        // 添加标题
        String title = getNotificationTitle(eventType);
        content.append("# ").append(title).append("\n\n");

        // 添加分隔线
        content.append("<font color=\"#1E88E5\">").append("—".repeat(16)).append("</font>\n\n");

        // 添加订单通知摘要
        switch (eventType) {
            case ORDER_CREATED:
                content.append("> ").append(EMOJI_NEW).append(" 有新订单需要处理\n\n");
                break;
            case ORDER_CANCELED:
                content.append("> ").append(EMOJI_CANCEL).append(" 订单已取消，请关注取消原因\n\n");
                break;
            case ORDER_PAID:
                content.append("> ").append(EMOJI_MONEY).append(" 订单已支付，请安排发货\n\n");
                break;
            case ORDER_SHIPPED:
                content.append("> ").append(EMOJI_TRUCK).append(" 订单已发货，请跟踪物流状态\n\n");
                break;
            case ORDER_PARTIAL_SHIPPED:
                content.append("> ").append(EMOJI_TRUCK).append(" 订单部分发货，请继续跟踪剩余物品\n\n");
                break;
            case ORDER_COMPLETED:
                content.append("> ").append(EMOJI_CHECK).append(" 订单已完成交付\n\n");
                break;
            case PACKAGE_STATUS_CHANGED:
                content.append("> ").append(EMOJI_PACKAGE).append(" 包裹状态已更新，请关注物流状态\n\n");
                break;
            default:
                content.append("> ").append(EMOJI_NOTE).append(" 订单状态已更新\n\n");
        }

        // 添加订单基本信息
        content.append("<font color=\"#546E7A\">").append(EMOJI_TAG).append(" 订单号：</font> ").append("`").append(order.getOrderNo()).append("`\n\n");

        // 添加客户订单号（如果存在）
        if (StrUtil.isNotBlank(order.getCustomerOrderNo())) {
            content.append("<font color=\"#546E7A\">").append(EMOJI_TAG).append(" 客户订单号：</font> ").append("`").append(order.getCustomerOrderNo()).append("`\n\n");
        }

        // 添加参考号（如果存在）
        if (StrUtil.isNotBlank(order.getReferenceNo())) {
            content.append("<font color=\"#546E7A\">").append(EMOJI_TAG).append(" 参考号：</font> ").append("`").append(order.getReferenceNo()).append("`\n\n");
        }

        content.append("<font color=\"#546E7A\">").append(EMOJI_CALENDAR).append(" 下单时间：</font> ").append(sdf.format(order.getCreateTime())).append("\n\n");

        content.append("<font color=\"#43A047\">").append(EMOJI_USER).append(" 客户：</font> ").append("<font color=\"#FF5722\">").append(order.getShippingName()).append("</font>\n\n");

        // 添加公司名称（如果存在）
        if (StrUtil.isNotBlank(order.getCompanyName())) {
            content.append("<font color=\"#43A047\">").append(EMOJI_USER).append(" 公司：</font> ").append("<font color=\"#FF5722\">").append(order.getCompanyName()).append("</font>\n\n");
        }

        // 添加业务代表（如果存在）
        if (StrUtil.isNotBlank(order.getSalespersonName())) {
            content.append("<font color=\"#673AB7\">").append(EMOJI_USER).append(" 业务代表：</font> ").append("<font color=\"#512DA8\">").append(order.getSalespersonName()).append("</font>\n\n");
        }

        if (StrUtil.isNotBlank(order.getShippingEmail())) {
            content.append("<font color=\"#0288D1\">").append(EMOJI_EMAIL).append(" 邮箱：</font> ").append(order.getShippingEmail()).append("\n\n");
        }

        if (StrUtil.isNotBlank(order.getShippingPhone())) {
            content.append("<font color=\"#00897B\">").append(EMOJI_PHONE).append(" 电话：</font> ").append(order.getShippingPhone()).append("\n\n");
        }

        content.append("<font color=\"#FB8C00\">").append(EMOJI_PACKAGE).append(" 产品数量：</font> ").append("<font color=\"#E65100\">").append(order.getTotalItems()).append("</font>\n\n");

        // 添加金额信息
        if (order.getTotalAmount() != null) {
            content.append("<font color=\"#4CAF50\">").append(EMOJI_MONEY).append(" 订单总金额：</font> ").append("<font color=\"#2E7D32\">$").append(order.getTotalAmount()).append("</font>\n\n");
        }

        // 添加折扣金额（如果存在）
        if (order.getDiscountAmount() != null && order.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
            content.append("<font color=\"#FF9800\">").append(EMOJI_MONEY).append(" 折扣金额：</font> ").append("<font color=\"#F57C00\">$").append(order.getDiscountAmount()).append("</font>\n\n");
        }

        // 添加实际支付金额（总金额减去折扣金额）
        if (order.getTotalAmount() != null) {
            BigDecimal actualAmount = order.getTotalAmount();
            if (order.getDiscountAmount() != null) {
                actualAmount = actualAmount.subtract(order.getDiscountAmount());
            }
            content.append("<font color=\"#4CAF50\">").append(EMOJI_MONEY).append(" 实际支付金额：</font> ").append("<font color=\"#1B5E20\">$").append(actualAmount).append("</font>\n\n");
        }

        // 添加订单备注信息
        if (StrUtil.isNotBlank(order.getRemark())) {
            content.append("<font color=\"#9C27B0\">").append(EMOJI_NOTE).append(" 备注：</font> ").append("<font color=\"#7B1FA2\">").append(order.getRemark()).append("</font>\n\n");
        }

        // 添加收货地址
        content.append("<font color=\"#795548\">").append(EMOJI_LOCATION).append(" 收货地址：</font> ").append(order.getFullShippingAddress()).append("\n\n");

        // 添加事件特定信息
        switch (eventType) {
            case ORDER_CREATED:
                content.append("<font color=\"#1E88E5\">").append(EMOJI_CLOCK).append(" 请及时处理新订单</font>\n");
                break;
            case ORDER_CANCELED:
                content.append("<font color=\"#F44336\">").append(EMOJI_ALERT).append(" 请确认取消原因并及时处理</font>\n");
                break;
            case ORDER_PAID:
                content.append("<font color=\"#4CAF50\">").append(EMOJI_CHECK).append(" 请及时安排发货</font>\n");
                break;
            case ORDER_SHIPPED:
                content.append("<font color=\"#FF9800\">").append(EMOJI_TRUCK).append(" 请关注物流状态</font>\n");
                break;
            case ORDER_PARTIAL_SHIPPED:
                content.append("<font color=\"#FF9800\">").append(EMOJI_TRUCK).append(" 订单部分发货，请安排剩余物品发货</font>\n");
                break;
            case ORDER_COMPLETED:
                content.append("<font color=\"#8BC34A\">").append(EMOJI_STAR).append(" 订单已完成，感谢您的处理</font>\n");
                break;
            case PACKAGE_STATUS_CHANGED:
                content.append("<font color=\"#03A9F4\">").append(EMOJI_PACKAGE).append(" 包裹状态已更新，请关注物流状态</font>\n");
                break;
            default:
                content.append("<font color=\"#9E9E9E\">").append(EMOJI_NOTE).append(" 订单状态已更新，请查看详情</font>\n");
        }

        return content.toString();
    }

    /**
     * 获取通知标题
     */
    private String getNotificationTitle(WhsOrderEventType eventType) {
        return
            switch (eventType) {
                case ORDER_CREATED -> EMOJI_BELL + " 新订单通知";
                case ORDER_CANCELED -> EMOJI_CANCEL + " 订单取消通知";
                case ORDER_PAID -> EMOJI_MONEY + " 订单支付通知";
                case ORDER_SHIPPED -> EMOJI_TRUCK + " 订单发货通知";
                case ORDER_PARTIAL_SHIPPED -> EMOJI_TRUCK + " 订单部分发货通知";
                case ORDER_COMPLETED -> EMOJI_CHECK + " 订单完成通知";
                case PACKAGE_STATUS_CHANGED -> EMOJI_PACKAGE + " 包裹状态更新通知";
                default -> EMOJI_NOTE + " 订单状态更新";
            };
    }

    /**
     * 检查企业微信是否启用
     */
    private boolean isWecomEnabled() {
        // 使用数据库配置检查企业微信订单通知是否启用
        boolean notificationEnabled = notificationConfigService.isOrderWecomEnabled();
        boolean serviceEnabled = wecomService.isEnabled();
        boolean enabled = serviceEnabled && notificationEnabled;

        log.debug("企业微信订单通知状态: {}, serviceEnabled={}, notificationEnabled={}",
            enabled ? "已启用" : "未启用", serviceEnabled, notificationEnabled);
        return enabled;
    }
}
