package com.imhuso.wholesale.core.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.util.StringJoiner;

import org.springframework.stereotype.Service;
import com.imhuso.wholesale.core.service.IWhsProductSpecService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 产品规格服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsProductSpecServiceImpl implements IWhsProductSpecService {
    /**
     * 将规格JSON字符串格式化为易读的字符串
     * 例如：将 {"颜色":"红色","尺寸":"大号"} 格式化为 "红色 - 大号"
     *
     * @param specsJson 规格JSON字符串
     * @return 格式化后的字符串
     */
    @Override
    public String formatSpecsToString(String specsJson) {
        if (StringUtils.isBlank(specsJson)) {
            return "";
        }

        try {
            JSONObject specs = JSONUtil.parseObj(specsJson);
            if (specs.isEmpty()) {
                return "";
            }

            StringJoiner joiner = new StringJoiner(" - ");
            for (String key : specs.keySet()) {
                Object value = specs.get(key);
                if (value != null && StringUtils.isNotBlank(value.toString())) {
                    joiner.add(value.toString());
                }
            }

            log.info("格式化规格JSON成功: {}", joiner);

            return joiner.toString();
        } catch (Exception e) {
            log.warn("格式化规格JSON失败，原始数据：{}", specsJson, e);
            return specsJson; // 返回原始字符串
        }
    }
}
