package com.imhuso.wholesale.core.domain.vo.admin;

import java.io.Serial;
import java.io.Serializable;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 产品变体属性关联视图对象
 * 用于表示变体选择的具体属性和值
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class WhsProductVariantAttributeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 变体属性关联ID
     */
    private Long id;

    /**
     * 属性ID
     */
    private Long attributeId;

    /**
     * 属性名称
     */
    private String attrName;

    /**
     * 属性显示名称
     */
    private String attrDisplay;

    /**
     * 属性排序
     */
    private Integer sort;

    /**
     * 属性值ID
     */
    private Long attributeValueId;

    /**
     * 属性值键名
     */
    private String valueKey;

    /**
     * 属性值显示名称
     */
    private String valueDisplay;
}
