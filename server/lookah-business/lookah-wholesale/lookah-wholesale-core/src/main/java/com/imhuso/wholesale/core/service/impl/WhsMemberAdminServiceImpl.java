package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.satoken.utils.LoginHelper;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.WhsMember;
import com.imhuso.wholesale.core.domain.bo.admin.MemberPasswordResetBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsMemberBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsMemberPermissionBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsShippingAddressBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsMemberPermissionVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsMemberVo;
import com.imhuso.wholesale.core.mapper.WhsMemberMapper;
import com.imhuso.wholesale.core.service.IWhsMemberAdminService;
import com.imhuso.wholesale.core.service.IWhsMemberPermissionService;
import com.imhuso.wholesale.core.service.IWhsShippingAddressService;
import com.imhuso.wholesale.core.utils.MemberPermissionHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 会员管理Service实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsMemberAdminServiceImpl implements IWhsMemberAdminService {

    private final WhsMemberMapper baseMapper;
    private final IWhsMemberPermissionService permissionService;
    private final MemberPermissionHelper permissionHelper;
    private final IWhsShippingAddressService addressService;

    /**
     * 查询会员列表
     */
    @Override
    public TableDataInfo<WhsMemberVo> queryPageList(WhsMemberBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WhsMember> lqw = buildQueryWrapper(bo);
        Page<WhsMemberVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        // 过滤敏感信息
        result.getRecords().forEach(WhsMemberVo::filterSensitiveData);

        return TableDataInfo.build(result);
    }

    /**
     * 查询会员详情
     */
    @Override
    public WhsMemberVo getMemberDetail(Long memberId) {
        WhsMemberVo memberVo = baseMapper.selectVoById(memberId);
        if (memberVo != null) {
            // 过滤敏感信息
            memberVo.filterSensitiveData();
        }
        return memberVo;
    }

    /**
     * 新增会员
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertMember(WhsMemberBo bo) {
        // 验证自定义字段
        validateCustomFields(bo);

        // 验证邮箱唯一性
        validateEmailUniqueness(bo.getEmail(), null);

        WhsMember insertMember = MapstructUtils.convert(bo, WhsMember.class);

        // 创建客户时始终生成随机密码，不管前端是否传递密码
        // 用户可通过重置密码功能获取新密码
        String password = generateRandomPassword();
        log.info("客户创建成功，已设置初始密码");

        insertMember.setPassword(BCrypt.hashpw(password));

        // 如果客户编码为空，自动生成
        if (StringUtils.isBlank(insertMember.getCustomerCode())) {
            insertMember.setCustomerCode(generateCustomerCode());
        }

        // 如果当前用户是销售代表且未指定销售代表，自动绑定当前用户
        if (insertMember.getSalespersonId() == null) {
            try {
                // 获取当前登录用户信息
                var loginUser = LoginHelper.getLoginUser();
                if (loginUser != null && "1".equals(loginUser.getIsSalesperson())) {
                    insertMember.setSalespersonId(loginUser.getUserId());
                    log.info("自动绑定销售代表，用户ID: {}, 销售代表ID: {}", loginUser.getUserId(), loginUser.getUserId());
                }
            } catch (Exception e) {
                log.warn("获取当前用户信息失败，跳过自动绑定销售代表: {}", e.getMessage());
            }
        }

        int result = baseMapper.insert(insertMember);

        // 如果会员创建成功且提供了地址信息，则创建地址（自动填充基本信息）
        if (result > 0 && hasRequiredShippingAddressInfo(bo)) {
            createShippingAddressWithBasicInfo(insertMember.getId(), bo);
        }

        return result;
    }

    /**
     * 更新会员信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateMember(WhsMemberBo bo) {
        // 验证自定义字段
        validateCustomFields(bo);

        // 验证邮箱唯一性（排除当前会员）
        validateEmailUniqueness(bo.getEmail(), bo.getId());

        WhsMember updateMember = MapstructUtils.convert(bo, WhsMember.class);
        // 更新时不允许直接修改密码
        updateMember.setPassword(null);

        return baseMapper.updateById(updateMember);
    }

    /**
     * 删除会员
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteMember(Long memberId) {
        return baseMapper.deleteById(memberId);
    }

    /**
     * 重置会员密码
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int resetPassword(MemberPasswordResetBo bo) {
        if (StringUtils.isEmpty(bo.getPassword())) {
            throw new ServiceException(MessageUtils.message("wholesale.member.password.required"));
        }

        // 更新会员密码
        WhsMember member = new WhsMember();
        member.setId(bo.getMemberId());
        member.setPassword(BCrypt.hashpw(bo.getPassword()));
        return baseMapper.updateById(member);
    }

    /**
     * 修改会员状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int changeStatus(Long memberId, String status) {
        WhsMember member = new WhsMember();
        member.setId(memberId);
        member.setStatus(status);
        return baseMapper.updateById(member);
    }

    /**
     * 获取会员权限列表
     */
    @Override
    public TableDataInfo<WhsMemberPermissionVo> getMemberPermissions(Long memberId, PageQuery pageQuery) {
        return permissionService.queryPageList(new WhsMemberPermissionBo() {
            {
                setMemberId(memberId);
            }
        }, pageQuery);
    }

    /**
     * 获取会员权限Map
     */
    @Override
    public Map<String, String> getMemberPermissionMap(Long memberId) {
        return permissionService.getMemberPermissionMap(memberId);
    }

    /**
     * 设置会员权限
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setMemberPermissions(Long memberId, Map<String, String> permissions) {
        boolean result = permissionService.batchSetPermissions(memberId, permissions);
        if (result) {
            // 清除权限缓存
            permissionHelper.clearPermissionCache(memberId);
        }
        return result;
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<WhsMember> buildQueryWrapper(WhsMemberBo bo) {
        LambdaQueryWrapper<WhsMember> lqw = Wrappers.lambdaQuery();
        Map<String, Object> params = bo.getParams();

        lqw.eq(bo.getId() != null, WhsMember::getId, bo.getId());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerCode()), WhsMember::getCustomerCode, bo.getCustomerCode());
        lqw.like(StringUtils.isNotBlank(bo.getEmail()), WhsMember::getEmail, bo.getEmail());
        lqw.like(StringUtils.isNotBlank(bo.getFirstName()), WhsMember::getFirstName, bo.getFirstName());
        lqw.like(StringUtils.isNotBlank(bo.getLastName()), WhsMember::getLastName, bo.getLastName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), WhsMember::getStatus, bo.getStatus());

        // 按注册时间范围查询
        lqw.between(params.get("from") != null && params.get("to") != null, WhsMember::getCreateTime, params.get("from"), params.get("to"));

        // 按创建时间倒序排序
        lqw.orderByDesc(WhsMember::getCreateTime);

        return lqw;
    }

    /**
     * 生成客户编码
     */
    @Override
    public String generateCustomerCode() {
        String prefix = "LCS#";
        int maxAttempts = 10;

        for (int i = 0; i < maxAttempts; i++) {
            // 生成5位随机数字
            int randomNumber = ThreadLocalRandom.current().nextInt(10000, 100000);
            String customerCode = prefix + String.format("%05d", randomNumber);

            // 检查是否已存在
            long count = baseMapper.selectCount(Wrappers.<WhsMember>lambdaQuery().eq(WhsMember::getCustomerCode, customerCode));

            if (count == 0) {
                return customerCode;
            }
        }

        // 如果多次尝试都重复，使用时间戳确保唯一性
        long timestamp = System.currentTimeMillis() % 100000;
        return prefix + String.format("%05d", timestamp);
    }


    /**
     * 检查是否提供了必需的收货地址信息
     * 只检查前端提供的地址字段，基本信息字段会自动填充
     */
    private boolean hasRequiredShippingAddressInfo(WhsMemberBo bo) {
        return StringUtils.isNotBlank(bo.getShippingCountry()) && StringUtils.isNotBlank(bo.getShippingState()) && StringUtils.isNotBlank(bo.getShippingCity()) && StringUtils.isNotBlank(bo.getShippingAddressLine1()) && StringUtils.isNotBlank(bo.getShippingZipCode());
    }

    /**
     * 创建收货地址（使用基本信息自动填充联系信息）
     */
    private void createShippingAddressWithBasicInfo(Long memberId, WhsMemberBo bo) {
        try {
            WhsShippingAddressBo addressBo = new WhsShippingAddressBo();
            addressBo.setMemberId(memberId);

            // 使用基本信息自动填充联系信息，添加空值检查避免空指针异常
            addressBo.setFirstName(bo.getFirstName() != null ? bo.getFirstName() : "");
            addressBo.setLastName(bo.getLastName() != null ? bo.getLastName() : "");
            addressBo.setPhone(bo.getPhone() != null ? bo.getPhone() : "");
            addressBo.setEmail(bo.getEmail() != null ? bo.getEmail() : "");
            addressBo.setCompanyName(bo.getCompanyName() != null ? bo.getCompanyName() : "");

            // 使用用户填写的地址信息
            addressBo.setCountry(bo.getShippingCountry());
            addressBo.setState(bo.getShippingState());
            addressBo.setCity(bo.getShippingCity());
            addressBo.setAddressLine1(bo.getShippingAddressLine1());
            addressBo.setAddressLine2(bo.getShippingAddressLine2()); // 可选字段
            addressBo.setZipCode(bo.getShippingZipCode());
            addressBo.setDeliveryNotes(""); // 默认为空
            addressBo.setIsDefault(BusinessConstants.YES); // 设为默认地址

            addressService.createAddress(addressBo);
            log.info("为会员[{}]创建收货地址成功", memberId);
        } catch (Exception e) {
            log.error("为会员[{}]创建收货地址失败: {}", memberId, e.getMessage(), e);
            // 不抛出异常，避免影响会员创建
        }
    }

    /**
     * 验证自定义字段
     * 当选择"其他"选项时，必须提供相应的自定义内容
     *
     * @param bo 会员业务对象
     * @throws ServiceException 当自定义字段验证失败时
     */
    private void validateCustomFields(WhsMemberBo bo) {
        // 验证公司类型自定义字段
        if ("others".equals(bo.getCompanyType())) {
            if (StringUtils.isBlank(bo.getCustomCompanyType())) {
                throw new ServiceException(MessageUtils.message("wholesale.member.custom.company.type.required"));
            }
            // 验证自定义公司类型长度
            if (bo.getCustomCompanyType().length() > 100) {
                throw new ServiceException(MessageUtils.message("wholesale.member.custom.company.type.too.long"));
            }
        }

        // 验证客户来源自定义字段
        if ("others".equals(bo.getCustomerSource())) {
            if (StringUtils.isBlank(bo.getCustomSource())) {
                throw new ServiceException(MessageUtils.message("wholesale.member.custom.source.required"));
            }
            // 验证自定义来源长度
            if (bo.getCustomSource().length() > 100) {
                throw new ServiceException(MessageUtils.message("wholesale.member.custom.source.too.long"));
            }
        }
    }

    /**
     * 验证邮箱唯一性
     *
     * @param email     邮箱地址
     * @param excludeId 排除的会员ID（更新时使用）
     * @throws ServiceException 当邮箱已存在时
     */
    private void validateEmailUniqueness(String email, Long excludeId) {
        if (StringUtils.isBlank(email)) {
            return;
        }

        LambdaQueryWrapper<WhsMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhsMember::getEmail, email);

        // 如果是更新操作，排除当前会员
        if (excludeId != null) {
            queryWrapper.ne(WhsMember::getId, excludeId);
        }

        long count = baseMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw new ServiceException(MessageUtils.message("wholesale.member.email.already.exists"));
        }
    }

    // 密码生成相关常量
    private static final int PASSWORD_LENGTH = 12;

    /**
     * 生成随机密码
     * 使用Hutool工具库生成安全的随机密码
     * 密码包含大小写字母、数字和特殊字符，长度为12位
     *
     * @return 随机密码
     */
    private String generateRandomPassword() {
        // 使用Hutool的RandomUtil生成包含字母、数字和特殊字符的随机密码
        // RandomUtil内部使用SecureRandom，确保密码生成的安全性
        String baseChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
        return RandomUtil.randomString(baseChars, PASSWORD_LENGTH);
    }
}
