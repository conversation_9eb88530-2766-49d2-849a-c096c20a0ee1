package com.imhuso.wholesale.core.domain.vo.admin;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.imhuso.common.translation.annotation.Translation;
import com.imhuso.wholesale.core.constant.TransConstant;
import com.imhuso.wholesale.core.domain.WhsStockLog;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 库存操作日志视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsStockLog.class)
public class WhsStockLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    private Long id;

    /**
     * 库存ID
     */
    private Long stockId;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 操作类型（1初始化 2更新 3锁定 4释放 5扣减 6退回）
     */
    private Integer operationType;

    /**
     * 操作类型文本
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, mapper = "operationType", other = "stockOperationType")
    private String operationTypeText;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 操作前库存
     */
    private Integer beforeStock;

    /**
     * 操作后库存
     */
    private Integer afterStock;

    /**
     * 关联订单ID
     */
    private Long orderId;

    /**
     * 关联订单号
     */
    private String orderNo;

    /**
     * 关联订单发票号
     */
    @JsonIgnore
    private Long invoiceId;

    /**
     * 关联订单项ID
     */
    private Long orderItemId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建人名称
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "createBy")
    private String createByName;
}
