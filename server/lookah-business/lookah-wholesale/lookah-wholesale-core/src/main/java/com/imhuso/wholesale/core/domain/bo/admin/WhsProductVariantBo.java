package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 产品变体业务对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsProductVariant.class)
public class WhsProductVariantBo {
    /**
     * 变体ID
     */
    @NotNull(message = "变体ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = { AddGroup.class })
    private Long productId;

    /**
     * SKU编码
     */
    @NotBlank(message = "SKU编码不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 50, message = "SKU编码不能超过50个字符", groups = { AddGroup.class, EditGroup.class })
    private String skuCode;

    /**
     * UPC编码
     */
    @Size(max = 50, message = "UPC编码不能超过50个字符", groups = { AddGroup.class, EditGroup.class })
    private String upc;

    /**
     * 批发价格(W-Sale)
     */
    @NotNull(message = "批发价格不能为空", groups = { AddGroup.class, EditGroup.class })
    @Positive(message = "批发价格必须大于0", groups = { AddGroup.class, EditGroup.class })
    @DecimalMin(value = "0.00", message = "批发价格不能小于0", groups = { AddGroup.class, EditGroup.class })
    @DecimalMax(value = "99999999.99", message = "批发价格不能超过99999999.99", groups = { AddGroup.class, EditGroup.class })
    @Digits(integer = 8, fraction = 2, message = "批发价格最多8位整数，2位小数", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal wholesalePrice;

    /**
     * 建议零售价(MSRP)
     */
    @NotNull(message = "建议零售价不能为空", groups = { AddGroup.class, EditGroup.class })
    @Positive(message = "建议零售价必须大于0", groups = { AddGroup.class, EditGroup.class })
    @DecimalMin(value = "0.00", message = "建议零售价不能小于0", groups = { AddGroup.class, EditGroup.class })
    @DecimalMax(value = "99999999.99", message = "建议零售价不能超过99999999.99", groups = { AddGroup.class, EditGroup.class })
    @Digits(integer = 8, fraction = 2, message = "建议零售价最多8位整数，2位小数", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal msrp;

    /**
     * 门店价格
     */
    @DecimalMin(value = "0.00", message = "门店价格不能小于0", groups = { AddGroup.class, EditGroup.class })
    @DecimalMax(value = "99999999.99", message = "门店价格不能超过99999999.99", groups = { AddGroup.class, EditGroup.class })
    @Digits(integer = 8, fraction = 2, message = "门店价格最多8位整数，2位小数", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal storePrice;

    /**
     * 主图URL
     */
    private String mainImage;

    /**
     * 规格属性JSON(冗余,用于快速访问)
     */
    private String specs;

    /**
     * 状态（0停用 1正常）
     */
    private String status;

    /**
     * 库存预警阈值
     */
    @Min(value = 0, message = "库存预警阈值不能小于0", groups = { AddGroup.class, EditGroup.class })
    @Max(value = 9999, message = "库存预警阈值不能超过9999", groups = { AddGroup.class, EditGroup.class })
    private Integer alertStock;

    /**
     * 变体属性映射（key: 属性ID, value: 属性值ID）
     */
    private Map<Long, Long> attributes;
}
