package com.imhuso.wholesale.core.constant;

/**
 * LTL追踪缓存名称常量
 *
 * <AUTHOR>
 */
public final class LtlCacheNames {

    /**
     * 成功获取的LTL追踪链接缓存60天
     * 格式：cacheNames#ttl
     */
    public static final String LTL_TRACKING_SUCCESS = "ltl:tracking:success#60d";

    /**
     * LTL追踪API调用失败冷却期缓存名称
     */
    public static final String LTL_TRACKING_FAILURE_NAME = "ltl:tracking:failure";

    /**
     * LTL追踪API调用失败冷却期缓存配置（用于@Cacheable注解）
     * 格式：cacheNames#ttl#maxIdleTime#maxSize
     */
    public static final String LTL_TRACKING_FAILURE = "ltl:tracking:failure#1m#1m#1000";

    /**
     * LTL追踪API成功但暂无追踪信息冷却期缓存名称
     */
    public static final String LTL_TRACKING_NO_INFO_NAME = "ltl:tracking:no-info";

    /**
     * LTL追踪API成功但暂无追踪信息冷却期缓存配置（用于@Cacheable注解）
     * 格式：cacheNames#ttl#maxIdleTime#maxSize
     */
    public static final String LTL_TRACKING_NO_INFO = "ltl:tracking:no-info#30m#30m#1000";

    /**
     * 失败冷却期时间（分钟）
     */
    public static final int FAILURE_COOLDOWN_MINUTES = 1;

    /**
     * 无追踪信息冷却期时间（分钟）
     */
    public static final int NO_INFO_COOLDOWN_MINUTES = 30;

    private LtlCacheNames() {
        // 工具类不允许实例化
    }
}
