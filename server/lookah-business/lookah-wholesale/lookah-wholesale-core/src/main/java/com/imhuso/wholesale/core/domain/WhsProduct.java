package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批发产品对象 (SPU)
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_product")
public class WhsProduct extends BaseEntity {

    /**
     * 产品ID
     */
    @TableId
    private Long id;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 系列ID
     */
    private Long seriesId;

    /**
     * 产品名称
     */
    private String itemName;

    /**
     * 默认变体ID
     */
    private Long defaultVariantId;

    /**
     * 状态（0停用 1正常）
     */
    private String status;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;
}
