package com.imhuso.wholesale.core.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.imhuso.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批发到货通知对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_stock_notify")
@AutoMapper(target = WhsStockNotify.class)
public class WhsStockNotify extends BaseEntity {

    /**
     * 通知ID
     */
    @TableId
    private Long id;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * 通知邮箱
     */
    private String email;

    /**
     * WhatsApp号码
     */
    private String whatsapp;

    /**
     * 状态（0待通知 1已通知 2已取消）
     */
    private Integer status;

    /**
     * 通知时间
     */
    private Date notifyTime;
}
