package com.imhuso.wholesale.core.listener;

import com.imhuso.wholesale.core.domain.WhsOrder;
import com.imhuso.wholesale.core.event.WhsOrderEvent;
import com.imhuso.wholesale.core.event.WhsOrderEvent.WhsOrderEventType;
import com.imhuso.wholesale.core.service.IWhsOrderBaseService;
import com.imhuso.wholesale.core.service.IWhsOrderStatusLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * 订单状态日志事件监听器
 * 负责监听订单状态变更事件并记录相应的状态日志
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WhsOrderStatusLogEventListener {

    private final IWhsOrderStatusLogService orderStatusLogService;
    private final IWhsOrderBaseService orderBaseService;

    /**
     * 处理订单状态变更事件
     * 记录订单状态日志并执行相关业务逻辑
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleOrderStatusChange(WhsOrderEvent event) {
        try {
            Long orderId = event.getOrderId();
            if (orderId == null) {
                log.error("订单ID为空，无法处理事件");
                return;
            }

            // 查询订单信息
            WhsOrder order = orderBaseService.getOrderById(orderId);
            if (order == null) {
                log.error("订单不存在, orderId={}", orderId);
                return;
            }

            log.info("接收到订单状态变更事件: 订单号={}, 事件类型={}",
                order.getInvoiceId(), event.getEventType());

            WhsOrderEventType eventType = event.getEventType();
            String remark = event.getRemark() != null ? event.getRemark() : "系统自动记录";

            // 记录订单状态日志
            recordOrderStatusLog(order, eventType, remark, event);
        } catch (cn.dev33.satoken.exception.SaTokenContextException e) {
            // 在异步环境中SaToken上下文未初始化是正常情况，只记录debug级别日志
            log.debug("异步环境中SaToken上下文未初始化，订单状态变更事件处理跳过: orderId={}, eventType={}",
                event.getOrderId(), event.getEventType());
        } catch (Exception e) {
            log.error("处理订单状态变更事件出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 记录订单状态日志
     */
    private void recordOrderStatusLog(WhsOrder order, WhsOrderEventType eventType, String remark,
                                      WhsOrderEvent event) {
        log.info("记录订单状态日志: orderId={}, status={}, eventType={}",
            order.getId(), order.getOrderStatus(), eventType);

        try {
            // 根据事件类型记录相应的日志及前缀
            String finalRemark = getOrderStatusLogRemark(eventType, remark);

            // 使用带事件类型的方法创建日志
            orderStatusLogService.createStatusLog(order, order.getOrderStatus(), null, finalRemark,
                event.getOperatorId(), eventType);

        } catch (Exception e) {
            log.error("记录订单状态日志失败: {}", e.getMessage(), e);
        }
    }

    private static String getOrderStatusLogRemark(WhsOrderEventType eventType, String remark) {
        String remarkPrefix = switch (eventType) {
            case ORDER_CREATED -> "订单创建: ";
            case ORDER_PROCESSING -> "订单处理中: ";
            case ORDER_CANCELED -> "订单取消: ";
            case ORDER_COMPLETED -> "订单完成: ";
            case ORDER_PAID -> "订单支付完成: ";
            case ORDER_PARTIAL_SHIPPED -> "订单部分发货: ";
            case ORDER_SHIPPED -> "订单已发货: ";
            case ORDER_INVOICE_SENT -> "发票已发送: ";
            case ORDER_REVERTED -> "订单撤回: ";
            default -> "";
        };

        // 组合备注（如果有前缀则添加前缀）
        return remarkPrefix.isEmpty() ? remark : remarkPrefix + remark;
    }

}
