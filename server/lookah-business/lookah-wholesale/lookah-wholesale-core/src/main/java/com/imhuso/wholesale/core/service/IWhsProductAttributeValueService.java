package com.imhuso.wholesale.core.service;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductAttributeValueBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductAttributeValueVo;

import java.util.List;

/**
 * 产品属性值Service接口
 *
 * <AUTHOR>
 */
public interface IWhsProductAttributeValueService {
    /**
     * 分页查询属性值列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 属性值分页列表
     */
    TableDataInfo<WhsProductAttributeValueVo> queryPage(WhsProductAttributeValueBo bo, PageQuery pageQuery);

    /**
     * 根据ID查询属性值
     *
     * @param id 属性值ID
     * @return 属性值
     */
    WhsProductAttributeValueVo getById(Long id);

    /**
     * 新增属性值
     *
     * @param bo 属性值信息
     * @return 结果
     */
    int insertAttributeValue(WhsProductAttributeValueBo bo);

    /**
     * 修改属性值
     *
     * @param bo 属性值信息
     * @return 结果
     */
    int updateAttributeValue(WhsProductAttributeValueBo bo);

    /**
     * 批量删除属性值
     *
     * @param ids 需要删除的属性值ID数组
     * @return 结果
     */
    int deleteAttributeValueByIds(Long[] ids);

    /**
     * 根据属性ID查询属性值列表
     *
     * @param attributeId 属性ID
     * @return 属性值列表
     */
    List<WhsProductAttributeValueVo> listByAttributeId(Long attributeId);

    /**
     * 检查属性值键是否存在
     *
     * @param valueKey    属性值键
     * @param attributeId 属性ID
     * @param id          排除的ID（更新时使用）
     * @return 存在返回true，不存在返回false
     */
    boolean valueKeyExists(String valueKey, Long attributeId, Long id);
}
