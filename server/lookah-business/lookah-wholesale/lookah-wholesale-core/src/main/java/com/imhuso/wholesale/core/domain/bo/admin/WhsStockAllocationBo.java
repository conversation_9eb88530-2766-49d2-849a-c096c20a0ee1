package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 库存分配业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WhsStockAllocationBo extends BaseEntity {

    /**
     * 分配ID
     */
    @NotNull(message = "分配ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 仓库ID
     */
    @NotNull(message = "仓库ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long warehouseId;

    /**
     * 变体ID
     */
    @NotNull(message = "变体ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long variantId;

    /**
     * 分配数量
     */
    @NotNull(message = "分配数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer quantity;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单项ID
     */
    private Long orderItemId;

    /**
     * 状态：0-待处理 1-已分配 2-已释放 3-已发货
     */
    private Integer status;
}
