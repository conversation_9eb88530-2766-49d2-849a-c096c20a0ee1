package com.imhuso.wholesale.core.service;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.WhsWarehouse;
import com.imhuso.wholesale.core.domain.bo.admin.WhsWarehouseBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsWarehouseVo;

import java.util.List;
import java.util.Map;

/**
 * 店铺仓库服务接口
 *
 * <AUTHOR>
 */
public interface IWhsWarehouseService {

    /**
     * 查询仓库列表（不分页）
     *
     * @param bo 仓库查询业务对象
     * @return 仓库列表
     */
    List<WhsWarehouseVo> queryList(WhsWarehouseBo bo);

    /**
     * 查询仓库分页列表
     *
     * @param bo        仓库查询业务对象
     * @param pageQuery 分页参数
     * @return 仓库分页列表
     */
    TableDataInfo<WhsWarehouseVo> queryPageList(WhsWarehouseBo bo, PageQuery pageQuery);

    /**
     * 根据ID查询仓库详情
     *
     * @param id 仓库ID
     * @return 仓库详情
     */
    WhsWarehouseVo getById(Long id);

    /**
     * 新增仓库
     *
     * @param bo 仓库业务对象
     * @return 结果
     */
    int insertWarehouse(WhsWarehouseBo bo);

    /**
     * 修改仓库
     *
     * @param bo 仓库业务对象
     * @return 结果
     */
    int updateWarehouse(WhsWarehouseBo bo);

    /**
     * 删除仓库
     *
     * @param id 仓库ID
     * @return 结果
     */
    int deleteWarehouseById(Long id);

    /**
     * 设置默认仓库
     *
     * @param id 仓库ID
     * @return 结果
     */
    int setDefaultWarehouse(Long id);

    /**
     * 检查仓库编码是否已被占用 (除自身外)
     *
     * @param bo 仓库业务对象 (需要包含 id 和 code)
     * @return true 已被占用 / false 未被占用 (唯一)
     */
    boolean isWarehouseCodeDuplicate(WhsWarehouseBo bo);

    /**
     * 获取仓库列表
     *
     * @return 仓库列表
     */
    List<WhsWarehouse> list();

    /**
     * 根据仓库ID获取对象
     *
     * @param id 仓库ID
     * @return 仓库对象
     */
    WhsWarehouse selectById(Long id);

    /**
     * 更新仓库
     *
     * @param warehouse 仓库对象
     * @return 是否成功
     */
    boolean updateById(WhsWarehouse warehouse);

    /**
     * 获取所有状态正常的仓库ID到名称的映射
     *
     * @return Map<仓库ID, 仓库名称>，只包含状态正常的仓库
     */
    Map<Long, String> getActiveWarehouseIdNameMap();

    /**
     * 获取所有状态正常的仓库ID到编码的映射
     *
     * @return Map<仓库ID, 仓库编码>，只包含状态正常的仓库
     */
    Map<Long, String> getActiveWarehouseIdCodeMap();
}
