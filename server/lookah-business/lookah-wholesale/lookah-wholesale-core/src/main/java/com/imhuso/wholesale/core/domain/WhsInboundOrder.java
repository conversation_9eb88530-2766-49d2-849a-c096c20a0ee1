package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 入库单对象 whs_inbound_order
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_inbound_order")
public class WhsInboundOrder extends BaseEntity {

    /**
     * 入库单ID
     */
    @TableId
    private Long id;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 外部入库单号
     */
    private String externalOrderNo;

    /**
     * 我方订单号
     */
    private String orderNo;

    /**
     * 状态（数字）
     * 0:待入库,1:部分入库,2:已入库,3:部分上架,4:已上架,10:已作废
     */
    private Integer status;

    /**
     * 状态文本
     */
    private String statusText;

    /**
     * 状态编码
     */
    private String statusCode;

    /**
     * 预计到达时间
     */
    private Date expectedArrival;

    /**
     * 实际到达时间
     */
    private Date actualArrival;

    /**
     * 备注
     */
    private String remark;

    /**
     * 外部数据(JSON)
     */
    private String externalData;

    /**
     * 最后同步时间
     */
    private Date lastSyncTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;
}
