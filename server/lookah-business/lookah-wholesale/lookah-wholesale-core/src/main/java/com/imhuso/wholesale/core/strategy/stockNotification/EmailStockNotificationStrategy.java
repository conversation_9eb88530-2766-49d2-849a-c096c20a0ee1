package com.imhuso.wholesale.core.strategy.stockNotification;

import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.domain.WhsProduct;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.WhsStockNotify;
import com.imhuso.wholesale.core.domain.bo.front.StockNotifyTemplateBo;
import com.imhuso.wholesale.core.enums.PackagingType;
import com.imhuso.wholesale.core.event.WhsStockNotifyEvent.StockNotifyEventType;
import com.imhuso.wholesale.core.mapper.WhsProductMapper;
import com.imhuso.wholesale.core.mapper.WhsProductVariantMapper;
import com.imhuso.wholesale.core.service.IStockNotificationSenderService;
import com.imhuso.wholesale.core.service.INotificationConfigService;
import com.imhuso.wholesale.core.utils.WhsEnumTranslationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import static com.imhuso.wholesale.core.event.WhsStockNotifyEvent.StockNotifyEventType.NOTIFY_REGISTERED;
import static com.imhuso.wholesale.core.event.WhsStockNotifyEvent.StockNotifyEventType.NOTIFY_SENT;

/**
 * 邮件库存通知策略
 * <p>
 * 使用邮件方式发送库存到货通知
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EmailStockNotificationStrategy implements IStockNotificationStrategy {

    private final IStockNotificationSenderService notificationSender;
    private final WhsProductVariantMapper variantMapper;
    private final WhsProductMapper productMapper;
    private final INotificationConfigService notificationConfigService;

    // 模板配置常量
    private static final String TEMPLATE_PREFIX = "stock_notify_";
    private static final String ADMIN_TEMPLATE_SUFFIX = "_admin";

    // 邮件标题映射 - 管理员（中文）
    private static final Map<StockNotifyEventType, String> ADMIN_TITLE_MAP = Map.of(
        NOTIFY_REGISTERED, "库存通知订阅",
        NOTIFY_SENT, "库存到货通知"
    );

    // 邮件标题映射 - 客户（英文）
    private static final Map<StockNotifyEventType, String> CUSTOMER_TITLE_MAP = Map.of(
        NOTIFY_REGISTERED, "Stock Notification Subscription",
        NOTIFY_SENT, "Product Back in Stock"
    );

    // 模板名称映射
    private static final Map<StockNotifyEventType, String> TEMPLATE_SUFFIX_MAP = Map.of(
        NOTIFY_REGISTERED, "registered",
        NOTIFY_SENT, "sent"
    );

    /**
     * 通知配置映射
     */
    private final Map<StockNotifyEventType, NotificationConfig> configMap = createConfigMap();

    /**
     * 创建通知配置映射
     */
    private Map<StockNotifyEventType, NotificationConfig> createConfigMap() {
        Map<StockNotifyEventType, NotificationConfig> map = new EnumMap<>(StockNotifyEventType.class);

        // 注册通知：通知管理员和客户
        map.put(NOTIFY_REGISTERED, new NotificationConfig(true, true));

        // 发送到货通知：通知管理员和客户
        map.put(NOTIFY_SENT, new NotificationConfig(true, true));

        return map;
    }

    @Override
    public Map<StockNotifyEventType, NotificationConfig> getConfigMap() {
        return configMap;
    }

    @Override
    public String getName() {
        return "邮件通知";
    }

    @Override
    public boolean sendAdminNotification(WhsStockNotify stockNotify, StockNotifyEventType eventType) {
        return sendNotification(stockNotify, eventType, true,
            notificationSender::sendStockAdminNotification);
    }

    @Override
    public boolean sendCustomerNotification(WhsStockNotify stockNotify, StockNotifyEventType eventType) {
        return sendNotification(stockNotify, eventType, false,
            notificationSender::sendStockCustomerNotification);
    }

    /**
     * 发送通知的通用方法
     *
     * @param stockNotify      库存通知记录
     * @param eventType        事件类型
     * @param isAdmin          是否是管理员通知
     * @param notificationFunc 通知发送函数
     * @return 是否发送成功
     */
    private boolean sendNotification(WhsStockNotify stockNotify, StockNotifyEventType eventType,
                                     boolean isAdmin, Consumer<StockNotifyTemplateBo> notificationFunc) {
        // 检查是否启用
        boolean emailEnabled = notificationConfigService.isStockEmailEnabled();
        if ((isAdmin && !isAdminNotificationEnabled(eventType)) ||
            (!isAdmin && !isCustomerNotificationEnabled(eventType)) ||
            !emailEnabled) {
            log.debug("邮件{}通知未启用: eventType={}, emailEnable={}",
                isAdmin ? "管理员" : "客户", eventType, emailEnabled);
            return false;
        }

        try {
            // 获取变体和产品信息
            WhsProductVariant variant = variantMapper.selectById(stockNotify.getVariantId());
            if (variant == null) {
                log.error("变体不存在，无法发送通知: variantId={}", stockNotify.getVariantId());
                return false;
            }

            WhsProduct product = productMapper.selectById(variant.getProductId());
            if (product == null) {
                log.error("产品不存在，无法发送通知: productId={}", variant.getProductId());
                return false;
            }

            // 准备通知数据
            StockNotifyTemplateBo templateBo = buildTemplateData(stockNotify, variant, product, eventType);

            // 发送通知
            notificationFunc.accept(templateBo);
            return true;
        } catch (Exception e) {
            log.error("发送邮件{}通知失败: notifyId={}, eventType={}, error={}",
                isAdmin ? "管理员" : "客户", stockNotify.getId(), eventType, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 构建模板数据
     */
    private StockNotifyTemplateBo buildTemplateData(WhsStockNotify stockNotify, WhsProductVariant variant,
                                                    WhsProduct product, StockNotifyEventType eventType) {
        // 获取包装类型文本
        String packagingTypeText = null;
        if (variant.getPackagingType() != null) {
            packagingTypeText = WhsEnumTranslationUtils.translate(PackagingType.class, variant.getPackagingType());
        }

        // 获取管理员邮箱列表，取第一个作为默认管理员邮箱
        List<String> adminEmails = notificationConfigService.getStockEmailList();
        String adminEmail = adminEmails.isEmpty() ? null : adminEmails.get(0);

        // 构建模板数据
        return StockNotifyTemplateBo.builder()
            .notify(stockNotify)
            .products(Collections.singletonList(product))
            .customerEmail(stockNotify.getEmail())
            .adminEmail(adminEmail)
            .customerTemplate(getTemplateNameByEventType(eventType, false))
            .adminTemplate(getTemplateNameByEventType(eventType, true))
            .customerTitle(getTitleByEventType(eventType, false))
            .adminTitle(getTitleByEventType(eventType, true))
            .packagingTypeText(packagingTypeText)
            .whatsapp(StringUtils.isNotEmpty(stockNotify.getWhatsapp()) ? stockNotify.getWhatsapp() : null)
            .skuCode(variant.getSkuCode())
            .build();
    }

    /**
     * 根据事件类型和目标用户获取邮件标题
     */
    private String getTitleByEventType(StockNotifyEventType eventType, boolean isAdmin) {
        Map<StockNotifyEventType, String> titleMap = isAdmin ? ADMIN_TITLE_MAP : CUSTOMER_TITLE_MAP;
        return titleMap.getOrDefault(eventType, isAdmin ? "库存通知" : "Stock Notification");
    }

    /**
     * 根据事件类型和目标用户获取模板名称
     */
    private String getTemplateNameByEventType(StockNotifyEventType eventType, boolean isAdmin) {
        String templateSuffix = TEMPLATE_SUFFIX_MAP.getOrDefault(eventType, "notification");
        String templateName = TEMPLATE_PREFIX + templateSuffix;
        return isAdmin ? templateName + ADMIN_TEMPLATE_SUFFIX : templateName;
    }
}
