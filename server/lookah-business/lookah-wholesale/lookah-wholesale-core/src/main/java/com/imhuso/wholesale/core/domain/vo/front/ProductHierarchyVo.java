package com.imhuso.wholesale.core.domain.vo.front;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 产品层级结构视图对象 (优化后的前端显示版本)
 * 包含产品 > 变体 > 包装变体的层级
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductHierarchyVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品ID
     */
    private Long id;

    /**
     * 所属系列ID
     */
    private Long seriesId;

    /**
     * 产品名称
     */
    private String itemName;

    /**
     * 产品描述
     */
    private String description;

    /**
     * 主图URL
     */
    private String mainImage;

    /**
     * 产品图片列表
     */
    private List<String> images;

    /**
     * 产品变体列表(单品变体)
     */
    private List<ProductVariantHierarchyVo> variants;
}
