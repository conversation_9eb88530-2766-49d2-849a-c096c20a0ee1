package com.imhuso.wholesale.core.domain.vo.front;

import com.imhuso.common.translation.annotation.Translation;
import com.imhuso.wholesale.core.constant.TransConstant;
import com.imhuso.wholesale.core.domain.WhsOrder;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 批发订单视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsOrder.class)
public class OrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long id;

    /**
     * 客户订单号
     */
    private String orderNo;

    /**
     * 收货人姓名
     */
    private String shippingName;

    /**
     * 收货人电话
     */
    private String shippingPhone;

    /**
     * 收货人邮箱
     */
    private String shippingEmail;

    /**
     * 收货国家
     */
    private String shippingCountry;

    /**
     * 收货州/省
     */
    private String shippingState;

    /**
     * 收货人城市
     */
    private String shippingCity;

    /**
     * 收货人地址
     */
    private String shippingAddress;

    /**
     * 收货人邮编
     */
    private String shippingZip;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 订单状态描述
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, other = "orderStatus", mapper = "orderStatus")
    private String orderStatusText;

    /**
     * 支付状态
     */
    private Integer paymentStatus;

    /**
     * 支付状态描述
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, other = "paymentStatus", mapper = "paymentStatus")
    private String paymentStatusText;

    /**
     * 发货状态
     */
    private Integer shipmentStatus;

    /**
     * 发货状态文本
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, other = "shipmentStatus", mapper = "shipmentStatus")
    private String shipmentStatusText;

    /**
     * 发票状态
     */
    private Integer invoiceStatus;

    /**
     * 发票状态文本
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, other = "invoiceStatus", mapper = "invoiceStatus")
    private String invoiceStatusText;

    /**
     * 订单总数量 PCS
     */
    private Integer totalPcs;

    /**
     * 订单项数量
     */
    private Integer itemCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 订单项列表（仅在详情时返回）
     */
    private List<OrderItemVo> items;

    /**
     * 物流包裹列表（仅在详情时返回）
     */
    private List<LogisticsPackageVo> packages;
}
