package com.imhuso.wholesale.core.domain.bo.front;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.File;
import java.io.Serial;
import java.io.Serializable;

/**
 * 邮件发送参数对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MailSenderBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 收件人
     */
    private String to;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 是否为HTML
     */
    private boolean isHtml;

    /**
     * 附件列表
     */
    private File[] attachments;
}
