package com.imhuso.wholesale.core.domain.vo.warehouse;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 入库单同步结果视图对象
 *
 * 通用的入库单同步结果视图对象，适用于所有海外仓提供商
 *
 * <AUTHOR>
 */
@Data
public class InboundOrderSyncResultVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 当前页码
     */
    private int pageIndex;

    /**
     * 每页记录数
     */
    private int pageSize;

    /**
     * 总页数
     */
    private int pageCount;

    /**
     * 总记录数
     */
    private int totalCount;

    /**
     * 处理的入库单数量
     */
    private int processedCount;

    /**
     * 成功处理的入库单数量
     */
    private int successCount;

    /**
     * 失败的入库单数量
     */
    private int failedCount;

    /**
     * 跳过的入库单数量
     */
    private int skippedCount;

    /**
     * 入库单数据列表
     */
    private List<InboundOrderVo> records = new ArrayList<>();

    /**
     * 额外数据
     * 注意：此字段仅用于存储不适合放入标准字段的临时数据，不应在业务逻辑中依赖
     */
    private Map<String, Object> extraData = new HashMap<>();

    /**
     * 添加入库单数据
     *
     * @param inboundOrder 入库单数据
     */
    public void addInboundOrder(InboundOrderVo inboundOrder) {
        this.records.add(inboundOrder);
        this.successCount++;
    }

    /**
     * 设置分页信息
     *
     * @param pageIndex 当前页码
     * @param pageSize  每页记录数
     * @param pageCount 总页数
     * @param totalCount 总记录数
     */
    public void setPagination(int pageIndex, int pageSize, int pageCount, int totalCount) {
        this.pageIndex = pageIndex;
        this.pageSize = pageSize;
        this.pageCount = pageCount;
        this.totalCount = totalCount;
    }

    /**
     * 设置处理结果
     *
     * @param success 是否成功
     * @param errorMessage 错误信息
     */
    public void setResult(boolean success, String errorMessage) {
        this.success = success;
        this.errorMessage = errorMessage;
    }

    /**
     * 设置处理统计
     *
     * @param processedCount 处理的入库单数量
     * @param successCount 成功处理的入库单数量
     * @param failedCount 失败的入库单数量
     * @param skippedCount 跳过的入库单数量
     */
    public void setStatistics(int processedCount, int successCount, int failedCount, int skippedCount) {
        this.processedCount = processedCount;
        this.successCount = successCount;
        this.failedCount = failedCount;
        this.skippedCount = skippedCount;
    }
}
