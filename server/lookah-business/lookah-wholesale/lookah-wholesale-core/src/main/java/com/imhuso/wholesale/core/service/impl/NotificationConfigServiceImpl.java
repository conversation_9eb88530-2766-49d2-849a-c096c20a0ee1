package com.imhuso.wholesale.core.service.impl;

import com.imhuso.common.core.service.ConfigService;
import com.imhuso.wholesale.core.service.INotificationConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 通知配置服务实现
 * 提供简化的通知配置读取功能，通过 ConfigService 获取系统配置
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationConfigServiceImpl implements INotificationConfigService {

    private final ConfigService configService;

    /**
     * 通知配置键名常量
     */
    public static final class Keys {
        // 默认企业微信配置
        public static final String DEFAULT_WECOM_LIST = "wholesale.notification.wecom.list";

        // 订单通知配置
        public static final String ORDER_EMAIL_LIST = "wholesale.notification.order.email.list";
        public static final String ORDER_WECOM_ENABLED = "wholesale.notification.order.wecom.enabled";
        public static final String ORDER_WECOM_LIST = "wholesale.notification.order.wecom.list";
        public static final String ORDER_SSE_ADMIN_USER_IDS = "wholesale.notification.order.sse.admin_user_ids";

        // 补货通知配置
        public static final String STOCK_EMAIL_ENABLED = "wholesale.notification.stock.email.enabled";
        public static final String STOCK_EMAIL_LIST = "wholesale.notification.stock.email.list";
        public static final String STOCK_WECOM_ENABLED = "wholesale.notification.stock.wecom.enabled";
        public static final String STOCK_WECOM_LIST = "wholesale.notification.stock.wecom.list";

        // 默认值
        public static final String DEFAULT_ADMIN_USER_ID = "1";

        private Keys() {}
    }

    // ===== 订单通知配置 =====

    @Override
    public List<String> getOrderEmailList() {
        return parseStringList(configService.getConfigValue(Keys.ORDER_EMAIL_LIST));
    }

    @Override
    public boolean isOrderWecomEnabled() {
        return parseBoolean(configService.getConfigValue(Keys.ORDER_WECOM_ENABLED));
    }

    @Override
    public List<String> getOrderWecomKeys() {
        return parseStringList(configService.getConfigValue(Keys.ORDER_WECOM_LIST));
    }

    @Override
    public List<String> getOrderSseAdminUserIds() {
        String value = configService.getConfigValue(Keys.ORDER_SSE_ADMIN_USER_IDS);
        return parseStringList(value, List.of(Keys.DEFAULT_ADMIN_USER_ID));
    }

    // ===== 补货通知配置 =====

    @Override
    public boolean isStockEmailEnabled() {
        return parseBoolean(configService.getConfigValue(Keys.STOCK_EMAIL_ENABLED));
    }

    @Override
    public List<String> getStockEmailList() {
        return parseStringList(configService.getConfigValue(Keys.STOCK_EMAIL_LIST));
    }

    @Override
    public boolean isStockWecomEnabled() {
        return parseBoolean(configService.getConfigValue(Keys.STOCK_WECOM_ENABLED));
    }

    @Override
    public List<String> getStockWecomKeys() {
        return parseStringList(configService.getConfigValue(Keys.STOCK_WECOM_LIST));
    }

    // ===== 企业微信配置 =====

    @Override
    public List<String> getDefaultWecomKeys() {
        return parseStringList(configService.getConfigValue(Keys.DEFAULT_WECOM_LIST));
    }

    // ===== 工具方法 =====

    /**
     * 解析布尔值
     */
    private boolean parseBoolean(String value) {
        if (!StringUtils.hasText(value)) {
            return true;
        }
        return "true".equalsIgnoreCase(value.trim());
    }

    /**
     * 解析字符串列表（逗号分隔）
     */
    private List<String> parseStringList(String value) {
        return parseStringList(value, List.of());
    }

    /**
     * 解析字符串列表（逗号分隔）
     */
    private List<String> parseStringList(String value, List<String> defaultValue) {
        if (!StringUtils.hasText(value)) {
            return defaultValue;
        }

        return Arrays.stream(value.split(","))
            .map(String::trim)
            .filter(StringUtils::hasText)
            .collect(Collectors.toList());
    }
}