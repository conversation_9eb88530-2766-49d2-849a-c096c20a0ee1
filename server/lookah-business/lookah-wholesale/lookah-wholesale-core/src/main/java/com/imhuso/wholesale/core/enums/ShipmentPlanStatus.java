package com.imhuso.wholesale.core.enums;

import com.imhuso.common.translation.core.EnumTranslatableInterface;

import lombok.Getter;

/**
 * 发货方案状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ShipmentPlanStatus implements EnumTranslatableInterface {

    /**
     * 待确认 - 方案已生成但未确认执行
     */
    PENDING(0),

    /**
     * 已执行 - 方案已执行过至少一次发货，但未全部发货完成
     */
    EXECUTED(1),

    /**
     * 失效 - 方案已失效，不能再执行发货
     */
    UNAVAILABLE(2);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 构造函数
     */
    ShipmentPlanStatus(int value) {
        this.value = value;
    }
}
