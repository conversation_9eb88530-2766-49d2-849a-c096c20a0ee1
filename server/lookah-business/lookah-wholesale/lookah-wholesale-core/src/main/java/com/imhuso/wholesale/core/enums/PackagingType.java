package com.imhuso.wholesale.core.enums;

import com.imhuso.common.translation.core.EnumTranslatableInterface;
import lombok.Getter;

/**
 * 包装类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum PackagingType implements EnumTranslatableInterface {
    /**
     * 单品
     */
    INDIVIDUAL(0),

    /**
     * 展示盒
     */
    DISPLAY(1),

    /**
     * 整箱
     */
    CASE(2);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 构造函数
     */
    PackagingType(int value) {
        this.value = value;
    }
}
