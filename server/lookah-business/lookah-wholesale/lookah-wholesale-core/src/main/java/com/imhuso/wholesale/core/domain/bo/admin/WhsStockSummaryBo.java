package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 库存汇总查询业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WhsStockSummaryBo extends BaseEntity {

    /**
     * 产品名称或SKU（模糊查询）
     */
    private String keyword;

    /**
     * 系列ID
     */
    private Long seriesId;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 库存状态（0-正常，1-预警，2-紧缺）
     */
    private Integer stockStatus;

    /**
     * 是否只显示有库存的产品
     */
    private Boolean onlyWithStock;

    /**
     * 包装类型列表（多选筛选）
     */
    private List<Integer> packagingTypes;
}
