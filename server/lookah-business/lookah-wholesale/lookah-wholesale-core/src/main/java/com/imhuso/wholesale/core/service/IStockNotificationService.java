package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.WhsStockNotify;
import com.imhuso.wholesale.core.event.WhsStockNotifyEvent.StockNotifyEventType;

/**
 * 缺货通知服务接口
 *
 * <AUTHOR>
 */
public interface IStockNotificationService {

    /**
     * 处理缺货通知
     *
     * @param stockNotify 缺货通知对象
     * @param eventType 事件类型
     */
    void processNotification(WhsStockNotify stockNotify, StockNotifyEventType eventType);
}
