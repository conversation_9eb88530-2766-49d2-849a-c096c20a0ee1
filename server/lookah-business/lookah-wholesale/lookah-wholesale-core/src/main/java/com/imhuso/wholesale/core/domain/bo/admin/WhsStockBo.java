package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.wholesale.core.domain.WhsStock;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 库存业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WhsStock.class, reverseConvertGenerate = false)
public class WhsStockBo extends BaseEntity {

    /**
     * 库存ID
     */
    @NotNull(message = "库存ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 仓库ID
     */
    @NotNull(message = "仓库ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long warehouseId;

    /**
     * 变体ID
     */
    @NotNull(message = "变体ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long variantId;

    /**
     * 总库存
     */
    @NotNull(message = "总库存不能为空", groups = {AddGroup.class, EditGroup.class})
    @Min(value = 0, message = "总库存不能小于0", groups = {AddGroup.class, EditGroup.class})
    private Integer totalStock;

    /**
     * 可用库存
     */
    @NotNull(message = "可用库存不能为空", groups = {AddGroup.class, EditGroup.class})
    @Min(value = 0, message = "可用库存不能小于0", groups = {AddGroup.class, EditGroup.class})
    private Integer availableStock;

    /**
     * 锁定库存
     */
    @NotNull(message = "锁定库存不能为空", groups = {AddGroup.class, EditGroup.class})
    @Min(value = 0, message = "锁定库存不能小于0", groups = {AddGroup.class, EditGroup.class})
    private Integer lockedStock;

    /**
     * 状态（0停用 1正常）
     */
    private String status;

    /**
     * SKU编码
     */
    private String skuCode;
}
