package com.imhuso.wholesale.core.service.impl;

import com.imhuso.common.mail.utils.MailUtils;
import com.imhuso.wholesale.core.domain.bo.front.MailSenderBo;
import com.imhuso.wholesale.core.service.IMailSenderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * 邮件发送服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MailSenderServiceImpl implements IMailSenderService {

    @Override
    public void sendMail(MailSenderBo mailSenderBo) {
        File[] attachments = mailSenderBo.getAttachments();

        if (mailSenderBo.isHtml()) {
            MailUtils.sendHtml(mailSenderBo.getTo(), mailSenderBo.getTitle(), mailSenderBo.getContent(), attachments);
        } else {
            MailUtils.sendText(mailSenderBo.getTo(), mailSenderBo.getTitle(), mailSenderBo.getContent(), attachments);
        }
    }
}
