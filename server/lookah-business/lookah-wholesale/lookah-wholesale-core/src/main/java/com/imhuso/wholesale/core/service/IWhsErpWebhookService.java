package com.imhuso.wholesale.core.service;

/**
 * ERP Webhook事件处理服务接口
 * 负责处理来自ERP系统的各种Webhook事件
 *
 * <AUTHOR>
 */
public interface IWhsErpWebhookService {

    /**
     * 处理Webhook事件
     *
     * @param eventType 事件类型
     * @param payload   事件载荷
     * @return 处理结果
     */
    boolean handleWebhookEvent(String eventType, String payload);

    /**
     * 验证Webhook签名
     *
     * @param payload   请求体
     * @param signature 签名
     * @return 验证结果
     */
    boolean verifyWebhookSignature(String payload, String signature);
}
