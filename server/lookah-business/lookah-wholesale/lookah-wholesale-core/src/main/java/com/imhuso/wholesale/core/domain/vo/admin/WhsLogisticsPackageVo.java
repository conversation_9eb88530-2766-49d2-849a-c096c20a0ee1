package com.imhuso.wholesale.core.domain.vo.admin;

import com.imhuso.common.translation.annotation.Translation;
import com.imhuso.wholesale.core.constant.TransConstant;
import com.imhuso.wholesale.core.domain.WhsLogisticsPackage;
import com.imhuso.wholesale.core.utils.carrier.TrackingNumberUtils;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 物流包裹视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsLogisticsPackage.class)
public class WhsLogisticsPackageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 包裹ID
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 关联的发货单ID
     */
    private Long shipmentId;

    /**
     * 跟踪号
     */
    private String trackingNumber;

    /**
     * 承运商
     */
    private String carrier;

    /**
     * 物流方式ID
     */
    private Long logisticsMethodId;

    /**
     * 物流方式名称
     */
    private String logisticsMethodName;

    /**
     * 提供商名称
     */
    private String providerName;

    /**
     * 包裹状态(0-待处理 1-已发货 2-运输中 3-已送达 4-异常)
     */
    private Integer packageStatus;

    /**
     * 包裹状态文本
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, other = "packageStatus", mapper = "packageStatus")
    private String packageStatusText;

    /**
     * 发货仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 发货日期
     */
    private Date shippedDate;

    /**
     * 海外仓订单号
     */
    private String externalOrderNo;

    /**
     * 物流追踪链接
     */
    private String trackingUrl;

    /**
     * 该包裹包含的发货项列表
     */
    private List<WhsOrderShipmentItemVo> shipmentItems;

    /**
     * 获取物流追踪链接(确保在JSON序列化时被调用)
     */
    public String getTrackingUrl() {
        return TrackingNumberUtils.getTrackingUrl(trackingNumber, carrier);
    }
}
