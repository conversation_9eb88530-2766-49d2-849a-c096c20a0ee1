package com.imhuso.wholesale.core.domain.bo.front;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.wholesale.core.domain.WhsOrder;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 批发订单业务对象 whs_order
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WhsOrder.class)
public class OrderBo extends BaseEntity {

    /**
     * 订单ID（系统控制）
     */
    @JsonIgnore
    private Long id;

    /**
     * 收货地址ID（用户输入）
     */
    private Long addressId;

    /**
     * 订单编号（系统控制）
     */
    @JsonIgnore
    private String orderNo;

    /**
     * 会员ID（系统控制）
     */
    @JsonIgnore
    private Long memberId;

    /**
     * 发票号
     */
    @JsonIgnore
    private Long invoiceId;

    /**
     * 支付状态（系统控制）
     */
    @JsonIgnore
    private Integer paymentStatus;

    /**
     * 订单状态（系统控制）
     */
    @JsonIgnore
    private Integer orderStatus;

    /**
     * 发票状态（系统控制）
     */
    @JsonIgnore
    private Integer invoiceStatus;

    /**
     * 备注（用户输入）
     */
    @Size(max = 500, message = "wholesale.order.remark.size")
    private String remark;

    /**
     * 构造函数 - 用于查询
     */
    public OrderBo(Long id) {
        this.id = id;
    }
}
