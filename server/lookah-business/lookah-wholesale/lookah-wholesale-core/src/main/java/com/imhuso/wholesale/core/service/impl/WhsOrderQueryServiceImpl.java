package com.imhuso.wholesale.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.*;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderBo;
import com.imhuso.wholesale.core.domain.bo.front.OrderListBo;
import com.imhuso.wholesale.core.domain.bo.admin.ShipmentPlanStatisticsBo;
import com.imhuso.wholesale.core.domain.vo.admin.PackingSlipAvailabilityVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderItemVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentApprovalVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderStatusLogVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderVo;
import com.imhuso.wholesale.core.domain.vo.front.LogisticsPackageVo;
import com.imhuso.wholesale.core.domain.vo.front.MemberVo;
import com.imhuso.wholesale.core.domain.vo.front.OrderItemVo;
import com.imhuso.wholesale.core.domain.vo.front.OrderVo;
import com.imhuso.wholesale.core.enums.*;
import com.imhuso.wholesale.core.mapper.*;
import com.imhuso.wholesale.core.satoken.WholesaleLoginHelper;
import com.imhuso.wholesale.core.service.*;
import com.imhuso.wholesale.core.utils.WarehouseNameUtils;
import com.imhuso.wholesale.core.utils.WhsEnumTranslationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 批发订单查询服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsOrderQueryServiceImpl implements IWhsOrderQueryService {

    private final WhsOrderMapper baseMapper;
    private final WhsOrderItemMapper orderItemMapper;
    private final WhsOrderFreightMapper orderFreightMapper;
    private final WhsShipmentPlanMapper shipmentPlanMapper;
    private final WhsShipmentPlanItemMapper shipmentPlanItemMapper;
    private final IWhsCountryService countryService;
    private final IWhsStateService stateService;
    private final IWhsOrderItemService orderItemService;
    private final IWhsOrderStatusLogService orderStatusLogService;
    private final IWhsOrderBaseService orderBaseService;
    private final IWhsOrderNoFormatService orderNoFormatService;
    private final WhsLogisticsPackageMapper logisticsPackageMapper;
    private final IWhsOrderShipmentQueryService orderShipmentQueryService;
    private final IApprovalConfigService approvalConfigService;
    private final IWhsProductVariantService productVariantService;
    private final IWhsMemberService memberService;
    private final IWhsWarehouseService warehouseService;
    private final IWhsOrderStockStatusService orderStockStatusService;
    private final IShipmentPermissionService shipmentPermissionService;
    private final IPackingSlipService packingSlipService;
    private final IWhsOrderShipmentApprovalService approvalService;

    /**
     * 分页查询会员订单列表
     * 【前台】
     */
    @Override
    public TableDataInfo<OrderVo> queryMemberOrderPageList(OrderListBo bo, PageQuery pageQuery) {
        // 构建查询条件
        LambdaQueryWrapper<WhsOrder> lqw = buildMemberOrderQueryWrapper(bo);

        // 分页查询订单实体
        Page<WhsOrder> page = baseMapper.selectPage(pageQuery.build(), lqw);

        // 转换为VO并处理
        List<OrderVo> orderVos = page.getRecords().stream().map(order -> {
            // 转换为VO
            OrderVo orderVo = MapstructUtils.convert(order, OrderVo.class);
            // 设置订单号
            orderVo.setOrderNo(orderNoFormatService.getCustomerOrderNoByOrder(order));
            return orderVo;
        }).collect(Collectors.toList());

        // 处理地址信息
        processOrderAddressInfo(orderVos);

        // 返回分页数据
        return new TableDataInfo<>(orderVos, page.getTotal());
    }

    /**
     * 查询订单详情
     * 【前台】
     */
    @Override
    public OrderVo selectOrderById(Long orderId) {
        // 1. 查询订单和订单项
        WhsOrder order = orderBaseService.checkOrderOwnership(orderId);
        List<WhsOrderItem> items = orderItemService.getOrderItems(orderId);

        // 2. 转换为VO并处理地址信息
        OrderVo orderVo = MapstructUtils.convert(order, OrderVo.class);

        // 3. 设置订单号
        orderVo.setOrderNo(orderNoFormatService.getCustomerOrderNoByOrder(order));

        // 4. 添加订单项信息
        if (!items.isEmpty()) {
            // 4.1 收集所有变体ID用于批量查询产品图片
            List<Long> variantIds = items.stream()
                .map(WhsOrderItem::getVariantId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            // 4.2 批量获取变体信息（包括主图）
            Map<Long, String> variantMainImageMap = Collections.emptyMap();
            if (!variantIds.isEmpty()) {
                try {
                    List<WhsProductVariant> variants = productVariantService.selectByIds(variantIds);
                    variantMainImageMap = variants.stream()
                        .filter(v -> v.getMainImage() != null && !v.getMainImage().isEmpty())
                        .collect(Collectors.toMap(
                            WhsProductVariant::getId,
                            WhsProductVariant::getMainImage,
                            (existing, replacement) -> existing
                        ));
                } catch (Exception e) {
                    log.warn("批量获取变体主图失败: {}", e.getMessage());
                }
            }

            // 4.3 转换订单项并设置主图
            final Map<Long, String> finalVariantMainImageMap = variantMainImageMap;
            List<OrderItemVo> itemVos = items.stream().map(item -> {
                OrderItemVo itemVo = MapstructUtils.convert(item, OrderItemVo.class);

                // 设置SKU
                itemVo.setSku(item.getSkuCode());
                // 设置specs
                itemVo.setSpecs(item.getSpecsSnapshot());

                // 设置单价
                itemVo.setProductPrice(item.getPrice());
                // 设置总金额 - 使用销售价格计算
                BigDecimal calculationPrice = item.getSalesPrice() != null ? item.getSalesPrice() : item.getPrice();
                if (calculationPrice != null) {
                    itemVo.setAmount(calculationPrice.multiply(new BigDecimal(item.getQuantity())));
                }

                // 设置产品主图
                if (item.getVariantId() != null && finalVariantMainImageMap.containsKey(item.getVariantId())) {
                    itemVo.setMainImage(finalVariantMainImageMap.get(item.getVariantId()));
                }

                return itemVo;
            }).collect(Collectors.toList());

            orderVo.setItems(itemVos);
            // 设置订单项数量
            orderVo.setItemCount(itemVos.size());
        }

        // 5. 处理地址信息
        processOrderAddressInfo(Collections.singletonList(orderVo));

        // 6. 查询物流包裹信息（如果已发货）
        if (order.getShipmentStatus() != null && !order.getShipmentStatus().equals(ShipmentStatus.PENDING.getValue())) {
            List<LogisticsPackageVo> packages = logisticsPackageMapper.selectPackagesByOrderId(orderId);
            // 初始化每个包裹的追踪链接
            if (packages != null) {
                packages.forEach(LogisticsPackageVo::getTrackingUrl);
            }
            orderVo.setPackages(packages);
        }

        return orderVo;
    }

    /**
     * 后台订单分页查询
     * 【后台】
     * 性能优化版本：减少N+1查询，提升响应速度
     */
    @Override
    public TableDataInfo<WhsOrderVo> queryPageList(WhsOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WhsOrder> lqw = buildAdminQueryWrapper(bo);
        Page<WhsOrder> page = baseMapper.selectPage(pageQuery.build(), lqw);

        // 如果没有数据，直接返回
        if (page.getRecords().isEmpty()) {
            return new TableDataInfo<>(Collections.emptyList(), 0L);
        }

        // 处理订单基本数据
        List<WhsOrderVo> records = page.getRecords().stream().map(this::buildOrderVoWithBasicInfo)
            .collect(Collectors.toList());

        // 性能优化：移除异步处理，使用同步批量处理
        fillShippingWarehousesInfo(records);
        fillStockStatusInfo(records);
        fillAdminOrderShipmentStatistics(records);
        fillOrderFreightInfo(records);

        // 过滤敏感信息
        records.forEach(WhsOrderVo::filterSensitiveData);

        TableDataInfo<WhsOrderVo> result = new TableDataInfo<>(records, page.getTotal());

        // 添加统计信息到扩展字段中
        OrderStatistics statistics = calculateOrderStatistics(bo);
        result.setExtra(statistics);

        return result;
    }

    /**
     * 计算订单统计信息
     *
     * @param bo 查询条件对象
     * @return 订单统计信息，包含订单总数、总箱数、总件数
     */
    private OrderStatistics calculateOrderStatistics(WhsOrderBo bo) {
        LambdaQueryWrapper<WhsOrder> lqw = buildAdminQueryWrapper(bo);

        // 查询符合条件的所有订单（不分页）
        List<WhsOrder> allOrders = baseMapper.selectList(lqw);

        long totalOrders = allOrders.size();
        int totalCases = 0;
        int totalPcs = 0;

        // 计算总PCS（WhsOrder实体中有totalPcs字段）
        for (WhsOrder order : allOrders) {
            if (order.getTotalPcs() != null) {
                totalPcs += order.getTotalPcs();
            }
        }

        // 计算总箱数：通过订单项统计包装类型为整箱(CASE=2)的数量
        if (!allOrders.isEmpty()) {
            List<Long> orderIds = allOrders.stream().map(WhsOrder::getId).toList();
            totalCases = calculateTotalCasesByOrderIds(orderIds);
        }

        return new OrderStatistics(totalOrders, totalCases, totalPcs);
    }

    /**
     * 根据订单ID列表计算总箱数
     *
     * @param orderIds 订单ID列表
     * @return 总箱数，如果没有找到整箱订单项则返回0
     */
    private Integer calculateTotalCasesByOrderIds(List<Long> orderIds) {
        if (orderIds.isEmpty()) {
            return 0;
        }

        // 查询这些订单的所有订单项，筛选包装类型为整箱的项目
        LambdaQueryWrapper<WhsOrderItem> itemWrapper = new LambdaQueryWrapper<>();
        itemWrapper.in(WhsOrderItem::getOrderId, orderIds)
            .eq(WhsOrderItem::getPackagingType, PackagingType.CASE.getValue());

        List<WhsOrderItem> caseItems = orderItemMapper.selectList(itemWrapper);

        // 统计总箱数（每个订单项的数量就是箱数）
        return caseItems.stream()
            .mapToInt(item -> item.getQuantity() != null ? item.getQuantity() : 0)
            .sum();
    }

    /**
     * 订单统计信息内部类
     */
    private record OrderStatistics(Long totalOrders, Integer totalCases, Integer totalPcs) {
    }

    /**
     * 后台查询订单详情
     */
    @Override
    public WhsOrderVo getOrderDetail(Long id) {
        // 1. 查询订单基本信息
        WhsOrder order = orderBaseService.getOrderById(id);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        // 2. 转换为VO对象并填充基本信息
        WhsOrderVo orderVo = buildOrderVoWithBasicInfo(order);

        // 2.1 填充客户信息
        fillMemberInfo(orderVo, order.getMemberId());

        // 3. 查询订单项（包含关联信息）- 一次性查询所有需要的数据
        List<WhsOrderItemVo> itemVos = orderItemService.getOrderItemsWithDetails(id);
        orderVo.setItems(itemVos);

        // 4. 查询订单状态日志
        List<WhsOrderStatusLogVo> statusLogs = orderStatusLogService.getOrderStatusLogs(id);
        orderVo.setStatusLogs(statusLogs);

        // 5. 查询订单发货记录
        try {
            List<WhsOrderShipmentVo> shipments = orderShipmentQueryService.getOrderShipmentRecords(id);
            if (shipments != null && !shipments.isEmpty()) {
                orderVo.setShipment(shipments);
            }
        } catch (Exception e) {
            log.error("加载订单[{}]的发货记录时发生错误: {}", id, e.getMessage());
        }

        // 6. 获取装箱清单可用性信息
        try {
            log.debug("开始检查订单[{}]的装箱单可用性", id);
            PackingSlipAvailabilityVo packingSlipAvailability = packingSlipService.checkPackingSlipAvailability(id);
            // 添加null检查，确保始终设置一个有效的对象
            if (packingSlipAvailability == null) {
                packingSlipAvailability = PackingSlipAvailabilityVo.unavailable("获取装箱清单可用性信息失败");
            }
            log.debug("订单[{}]装箱单可用性检查完成: {}", id, Boolean.TRUE.equals(packingSlipAvailability.getAvailable()) ? "可用" : "不可用");
            orderVo.setPackingSlipAvailability(packingSlipAvailability);
        } catch (Exception e) {
            log.error("获取订单[{}]装箱清单可用性信息时发生错误: {}", id, e.getMessage());
            // 设置默认的不可用状态
            orderVo.setPackingSlipAvailability(PackingSlipAvailabilityVo.unavailable("获取装箱清单可用性信息时发生错误"));
        }

        // 7. 获取审批意见（只有审批通过的订单才需要获取）
        if (orderVo.getShipmentApprovalStatus() != null && 
            orderVo.getShipmentApprovalStatus().equals(ShipmentApprovalStatus.APPROVED.getValue())) {
            try {
                WhsOrderShipmentApprovalVo approval = approvalService.queryLatestByOrderId(id);
                if (approval != null && approval.getApprovalStatus() != null && 
                    approval.getApprovalStatus().equals(ShipmentApprovalStatus.APPROVED.getValue())) {
                    orderVo.setApprovalComment(approval.getApprovalComment());
                }
            } catch (Exception e) {
                log.warn("获取订单{}的审批意见失败: {}", orderVo.getOrderNo(), e.getMessage());
                // 不影响主流程，审批意见获取失败时为null
            }
        }

        // 8. 过滤敏感信息
        orderVo.filterSensitiveData();

        // 9. 设置按钮显示逻辑
        setButtonDisplayLogic(orderVo, id);

        return orderVo;
    }

    /**
     * 构建订单VO对象并填充基本信息
     */
    private WhsOrderVo buildOrderVoWithBasicInfo(WhsOrder order) {
        WhsOrderVo orderVo = MapstructUtils.convert(order, WhsOrderVo.class);

        // 手动设置格式化的订单号
        orderVo.setOrderNo(orderNoFormatService.getOrderNoByOrder(order));

        // 手动设置客户订单号 - 对于补货订单，保持原始值（可能为空）
        if (Boolean.TRUE.equals(order.getIsReplenishment())) {
            // 补货订单：保持原始的客户订单号（通常为空）
            orderVo.setCustomerOrderNo(order.getCustomerOrderNo());
        } else {
            // 普通订单：使用格式化服务
            orderVo.setCustomerOrderNo(orderNoFormatService.getCustomerOrderNoByOrder(order));
        }

        // 手动设置公司名称（优先从收货信息中获取）
        orderVo.setCompanyName(order.getShippingCompanyName());

        // 手动设置各种状态文本
        setStatusTexts(orderVo, order);

        // 设置按钮显示逻辑 - 基于新的审批权限系统
        setButtonDisplayLogic(orderVo, order.getId());

        // 设置格式化的收货地址 - 直接格式化而不是再次查询数据库
        if (StringUtils.isNotBlank(order.getShippingAddress())) {
            orderVo.setFullShippingAddress(formatShippingAddress(order));
        }

        return orderVo;
    }

    /**
     * 填充客户信息
     */
    private void fillMemberInfo(WhsOrderVo orderVo, Long memberId) {
        if (memberId == null) {
            return;
        }

        try {
            // 查询客户信息
            MemberVo memberInfo = memberService.getMemberInfo(memberId);
            if (memberInfo != null) {
                orderVo.setMemberEmail(memberInfo.getEmail());
                // 设置客户名称（如果需要）
                if (memberInfo.getFirstName() != null || memberInfo.getLastName() != null) {
                    String memberName = (memberInfo.getFirstName() != null ? memberInfo.getFirstName() : "") +
                        " " + (memberInfo.getLastName() != null ? memberInfo.getLastName() : "");
                    orderVo.setMemberName(memberName.trim());
                }
                // 设置公司名称（如果收货地址中没有公司名称，则使用会员的公司名称作为fallback）
                if (StringUtils.isBlank(orderVo.getCompanyName()) && StringUtils.isNotBlank(memberInfo.getCompanyName())) {
                    orderVo.setCompanyName(memberInfo.getCompanyName());
                }
            }
        } catch (Exception e) {
            log.warn("获取客户信息失败，客户ID: {}, 错误: {}", memberId, e.getMessage());
        }
    }

    /**
     * 设置订单状态相关文本
     */
    private void setStatusTexts(WhsOrderVo orderVo, WhsOrder order) {
        // 订单状态 - 状态码字段保持数字，文本字段设置翻译
        orderVo.setOrderStatus(order.getOrderStatus().toString());
        orderVo.setOrderStatusText(WhsEnumTranslationUtils.translate(OrderStatus.class, order.getOrderStatus()));

        // 支付状态
        orderVo.setPaymentStatus(order.getPaymentStatus().toString());
        orderVo.setPaymentStatusText(WhsEnumTranslationUtils.translate(PaymentStatus.class, order.getPaymentStatus()));

        // 发货状态
        orderVo.setShipmentStatus(order.getShipmentStatus().toString());
        orderVo.setShipmentStatusText(WhsEnumTranslationUtils.translate(ShipmentStatus.class, order.getShipmentStatus()));

        // 发票状态
        orderVo.setInvoiceStatus(order.getInvoiceStatus().toString());
        orderVo.setInvoiceStatusText(WhsEnumTranslationUtils.translate(InvoiceStatus.class, order.getInvoiceStatus()));
    }

    /**
     * 格式化订单收货地址
     */
    private String formatShippingAddress(WhsOrder order) {
        // 获取国家和州/省名称
        String countryName = countryService.getCountryNameByCode(order.getShippingCountry());
        String stateName = stateService.getStateNameByCountryCodeAndStateCode(order.getShippingCountry(),
            order.getShippingState());

        return String.format("%s, %s, %s, %s, %s", order.getShippingAddress(), order.getShippingCity(), stateName,
            order.getShippingZip(), countryName);
    }

    /**
     * 处理订单地址信息 - 优化版本，避免N+1查询
     */
    private void processOrderAddressInfo(List<OrderVo> orders) {
        if (orders == null || orders.isEmpty()) {
            return;
        }

        try {
            // 1. 收集所有需要查询的国家代码和州/省代码
            Set<String> countryCodes = new HashSet<>();
            Map<String, Set<String>> countryStateMap = new HashMap<>();

            orders.forEach(orderVo -> {
                if (StringUtils.isNotEmpty(orderVo.getShippingCountry())) {
                    String countryCode = orderVo.getShippingCountry();
                    countryCodes.add(countryCode);

                    // 收集每个国家对应的州/省代码
                    if (StringUtils.isNotEmpty(orderVo.getShippingState())) {
                        countryStateMap.computeIfAbsent(countryCode, k -> new HashSet<>())
                            .add(orderVo.getShippingState());
                    }
                }
            });

            // 2. 批量预加载国家信息到缓存
            for (String countryCode : countryCodes) {
                countryService.getCountryNameByCode(countryCode);
            }

            // 3. 批量预加载州/省信息到缓存
            for (Map.Entry<String, Set<String>> entry : countryStateMap.entrySet()) {
                String countryCode = entry.getKey();
                Set<String> stateCodes = entry.getValue();
                for (String stateCode : stateCodes) {
                    stateService.getStateNameByCountryCodeAndStateCode(countryCode, stateCode);
                }
            }

            // 4. 处理每个订单的地址信息（现在会命中缓存，避免重复查询）
            orders.forEach(orderVo -> {
                // 保存原始国家代码用于州/省转换
                String countryCode = orderVo.getShippingCountry();

                // 转换国家代码为名称
                if (StringUtils.isNotEmpty(countryCode)) {
                    String countryName = countryService.getCountryNameByCode(countryCode);
                    orderVo.setShippingCountry(countryName);
                }

                // 转换州/省代码为名称
                if (StringUtils.isNotEmpty(orderVo.getShippingState()) && StringUtils.isNotEmpty(countryCode)) {
                    String stateName = stateService.getStateNameByCountryCodeAndStateCode(countryCode,
                        orderVo.getShippingState());
                    orderVo.setShippingState(stateName);
                }
            });

        } catch (Exception e) {
            log.error("处理订单地址信息时发生错误: {}", e.getMessage());
        }
    }

    /**
     * 构建会员订单查询条件
     */
    private LambdaQueryWrapper<WhsOrder> buildMemberOrderQueryWrapper(OrderListBo bo) {
        LambdaQueryWrapper<WhsOrder> lqw = Wrappers.lambdaQuery();

        // 限制只能查询自己的订单
        lqw.eq(WhsOrder::getMemberId, WholesaleLoginHelper.getUserId());

        // 设置订单状态条件 - 支持多状态筛选，过滤掉Draft状态
        if (bo.getStatus() != null && bo.getStatus().length > 0) {
            // 过滤掉null值和Draft状态(-1)
            Integer[] validStatuses = Arrays.stream(bo.getStatus())
                .filter(Objects::nonNull)
                .filter(status -> !status.equals(OrderStatus.DRAFT.getValue()))
                .toArray(Integer[]::new);

            if (validStatuses.length > 0) {
                if (validStatuses.length == 1) {
                    lqw.eq(WhsOrder::getOrderStatus, validStatuses[0]);
                } else {
                    lqw.in(WhsOrder::getOrderStatus, Arrays.asList(validStatuses));
                }
            }
        } else {
            // 如果没有指定状态筛选，默认排除Draft状态
            lqw.ne(WhsOrder::getOrderStatus, OrderStatus.DRAFT.getValue());
        }

        // 日期范围筛选
        if (bo.getFrom() != null) {
            lqw.ge(WhsOrder::getCreateTime, bo.getFrom());
        }
        if (bo.getTo() != null) {
            lqw.le(WhsOrder::getCreateTime, bo.getTo());
        }

        // 订单号搜索（支持系统订单号和客户订单号的模糊搜索）
        if (StringUtils.isNotBlank(bo.getOrderNo())) {
            lqw.and(wrapper -> wrapper
                .like(WhsOrder::getInternalOrderNo, bo.getOrderNo())
                .or()
                .like(WhsOrder::getCustomerOrderNo, bo.getOrderNo())
            );
        }

        // 收货人信息筛选
        if (StringUtils.isNotBlank(bo.getShippingName())) {
            lqw.like(WhsOrder::getShippingName, bo.getShippingName());
        }
        if (StringUtils.isNotBlank(bo.getShippingPhone())) {
            lqw.like(WhsOrder::getShippingPhone, bo.getShippingPhone());
        }
        if (StringUtils.isNotBlank(bo.getShippingEmail())) {
            lqw.like(WhsOrder::getShippingEmail, bo.getShippingEmail());
        }

        // 按创建时间倒排
        lqw.orderByDesc(WhsOrder::getCreateTime);

        return lqw;
    }

    /**
     * 构建后台查询条件
     * 性能优化版本：优化查询条件顺序，充分利用数据库索引
     */
    private LambdaQueryWrapper<WhsOrder> buildAdminQueryWrapper(WhsOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WhsOrder> lqw = new LambdaQueryWrapper<>();

        // 性能优化：优先使用有索引的字段进行过滤，提升查询效率
        // 注意：MyBatis Plus会自动处理逻辑删除，无需手动添加del_flag条件

        // 1. 按订单ID查询（主键索引）- 精确匹配，最高效
        lqw.eq(bo.getId() != null, WhsOrder::getId, bo.getId());

        // 2. 按订单状态查询（有索引 idx_order_status）- 支持多选，过滤掉Draft状态
        if (bo.getOrderStatus() != null && bo.getOrderStatus().length > 0) {
            // 过滤掉Draft状态(-1)
            Integer[] validStatuses = Arrays.stream(bo.getOrderStatus())
                .filter(status -> !status.equals(OrderStatus.DRAFT.getValue()))
                .toArray(Integer[]::new);

            if (validStatuses.length > 0) {
                lqw.in(WhsOrder::getOrderStatus, Arrays.asList(validStatuses));
            } else {
                // 如果过滤后没有有效状态，排除Draft和Canceled状态
                lqw.notIn(WhsOrder::getOrderStatus, Arrays.asList(
                    OrderStatus.DRAFT.getValue(),
                    OrderStatus.CANCELED.getValue()
                ));
            }
        } else {
            // 默认过滤Draft和已取消订单：如果没有指定订单状态，则排除Draft和已取消状态
            lqw.notIn(WhsOrder::getOrderStatus, Arrays.asList(
                OrderStatus.DRAFT.getValue(),
                OrderStatus.CANCELED.getValue()
            ));
        }

        // 3. 按支付状态查询（有索引 idx_payment_status）
        lqw.eq(bo.getPaymentStatus() != null, WhsOrder::getPaymentStatus, bo.getPaymentStatus());

        // 4. 按发货状态查询（有索引 idx_shipment_status）
        if (bo.getShipmentStatusArray() != null && bo.getShipmentStatusArray().length > 0) {
            // 支持多状态查询（发货管理使用）
            lqw.in(WhsOrder::getShipmentStatus, Arrays.asList(bo.getShipmentStatusArray()));
        } else {
            // 单状态查询（订单列表使用）
            lqw.eq(bo.getShipmentStatus() != null, WhsOrder::getShipmentStatus, bo.getShipmentStatus());
        }

        // 5. 按发票状态查询（有索引 idx_invoice_status）
        lqw.eq(bo.getInvoiceStatus() != null, WhsOrder::getInvoiceStatus, bo.getInvoiceStatus());

        // 6. 按发货审批状态查询（有索引 idx_shipment_approval_status）
        if (bo.getShipmentApprovalStatus() != null) {
            if (bo.getShipmentApprovalStatus() == -1) {
                // 特殊标记：查询可发货的订单（根据审批配置动态判断）
                List<Integer> shippableStatuses = approvalConfigService.getShippableApprovalStatuses();
                if (shippableStatuses != null && !shippableStatuses.isEmpty()) {
                    lqw.in(WhsOrder::getShipmentApprovalStatus, shippableStatuses);
                }
                // 如果 shippableStatuses 为 null，表示不限制审批状态，不添加查询条件
            } else {
                // 普通查询：按指定状态精确匹配
                lqw.eq(WhsOrder::getShipmentApprovalStatus, bo.getShipmentApprovalStatus());
            }
        }

        // 7. 按系统订单号查询（模糊查询，放在后面）
        lqw.like(StringUtils.isNotBlank(bo.getOrderNo()), WhsOrder::getInternalOrderNo, bo.getOrderNo());

        // 7. 按客户订单号查询（模糊查询）
        lqw.like(StringUtils.isNotBlank(bo.getCustomerOrderNo()), WhsOrder::getCustomerOrderNo, bo.getCustomerOrderNo());

        // 8. 按收货人信息查询（模糊查询，性能较低，放在最后）
        lqw.like(StringUtils.isNotBlank(bo.getShippingName()), WhsOrder::getShippingName, bo.getShippingName());
        lqw.like(StringUtils.isNotBlank(bo.getShippingPhone()), WhsOrder::getShippingPhone, bo.getShippingPhone());
        lqw.like(StringUtils.isNotBlank(bo.getShippingEmail()), WhsOrder::getShippingEmail, bo.getShippingEmail());

        // 9. 按客户注册邮箱查询（子查询方式，性能较低）
        if (StringUtils.isNotBlank(bo.getMemberEmail())) {
            lqw.exists("SELECT 1 FROM whs_member m WHERE m.id = whs_order.member_id AND m.email LIKE CONCAT('%', {0}, '%')", bo.getMemberEmail());
        }

        // 10. 按收货国家查询
        lqw.eq(StringUtils.isNotBlank(bo.getShippingCountry()), WhsOrder::getShippingCountry,
            bo.getShippingCountry());

        // 11. 按销售代表查询（有索引 idx_salesperson_id）
        lqw.eq(bo.getSalespersonId() != null, WhsOrder::getSalespersonId, bo.getSalespersonId());

        // 12. 按时间范围查询（有索引 idx_create_time）- 范围查询，放在精确查询之后
        lqw.between(params.get("from") != null && params.get("to") != null, WhsOrder::getCreateTime,
            params.get("from"), params.get("to"));

        // 12. 按订单金额范围查询（无索引，性能较低）
        lqw.ge(bo.getMinAmount() != null, WhsOrder::getTotalAmount, bo.getMinAmount());
        lqw.le(bo.getMaxAmount() != null, WhsOrder::getTotalAmount, bo.getMaxAmount());

        // 13. 按备注查询（无索引，全文搜索，性能最低）
        lqw.like(StringUtils.isNotBlank(bo.getRemark()), WhsOrder::getRemark, bo.getRemark());

        // 性能优化：使用复合索引进行排序
        // 利用 idx_del_flag_create_time 复合索引，提升排序性能
        lqw.orderByDesc(WhsOrder::getCreateTime);

        return lqw;
    }

    /**
     * 提取订单ID列表
     *
     * @param orderVos 订单VO列表
     * @return 订单ID列表
     */
    private List<Long> extractOrderIds(List<WhsOrderVo> orderVos) {
        return orderVos.stream()
            .map(WhsOrderVo::getId)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * 设置订单库存状态信息
     *
     * @param orderVo              订单VO
     * @param stockStatus          库存状态
     * @param hasInsufficientStock 是否库存不足
     */
    private void setOrderStockStatus(WhsOrderVo orderVo, StockStatus stockStatus, Boolean hasInsufficientStock) {
        // 防御性编程：确保即使传入 null 也设置默认值，避免前端收到 null
        if (stockStatus != null) {
            orderVo.setStockStatus(stockStatus.getValue());
            orderVo.setStockStatusText(WhsEnumTranslationUtils.translate(StockStatus.class, stockStatus.getValue()));
        } else {
            orderVo.setStockStatus(StockStatus.OUT_OF_STOCK.getValue());
            orderVo.setStockStatusText(WhsEnumTranslationUtils.translate(StockStatus.class, StockStatus.OUT_OF_STOCK.getValue()));
        }

        orderVo.setHasInsufficientStock(hasInsufficientStock != null ? hasInsufficientStock : true);
    }

    /**
     * 批量填充订单的发货仓库信息
     *
     * @param orderVos 订单VO列表
     */
    private void fillShippingWarehousesInfo(List<WhsOrderVo> orderVos) {
        if (orderVos == null || orderVos.isEmpty()) {
            return;
        }

        try {
            // 1. 获取所有订单ID
            List<Long> orderIds = extractOrderIds(orderVos);
            if (orderIds.isEmpty()) {
                return;
            }

            // 2. 批量查询所有订单的发货记录中的仓库ID
            Map<Long, Set<Long>> orderWarehouseMap = getOrderWarehouseMapping(orderIds);

            // 3. 获取仓库ID到编码的映射（现在已缓存）
            Map<Long, String> warehouseIdCodeMap = warehouseService.getActiveWarehouseIdCodeMap();

            // 4. 为每个订单设置发货仓库信息
            orderVos.forEach(orderVo ->
                setOrderWarehouseInfo(orderVo, orderWarehouseMap, warehouseIdCodeMap));

        } catch (Exception e) {
            log.error("填充订单发货仓库信息时发生错误: {}", e.getMessage());
            orderVos.forEach(orderVo -> orderVo.setShippingWarehouses(""));
        }
    }

    /**
     * 设置单个订单的仓库信息
     */
    private void setOrderWarehouseInfo(WhsOrderVo orderVo, Map<Long, Set<Long>> orderWarehouseMap,
                                       Map<Long, String> warehouseIdCodeMap) {
        Set<Long> warehouseIds = orderWarehouseMap.get(orderVo.getId());
        String shippingWarehouses = WarehouseNameUtils.formatWarehouseDisplayByIds(warehouseIds, warehouseIdCodeMap);
        orderVo.setShippingWarehouses(shippingWarehouses);
    }


    /**
     * 获取订单ID到发货仓库ID集合的映射
     *
     * @param orderIds 订单ID列表
     * @return 订单ID -> 仓库ID集合的映射
     */
    private Map<Long, Set<Long>> getOrderWarehouseMapping(List<Long> orderIds) {
        if (orderIds == null || orderIds.isEmpty()) {
            return Collections.emptyMap();
        }

        return queryOrderShipmentWarehouses(orderIds);
    }

    /**
     * 查询订单的发货仓库映射 - 优化版本，使用批量查询避免N+1问题
     *
     * @param orderIds 订单ID列表
     * @return 订单ID -> 仓库ID集合的映射
     */
    private Map<Long, Set<Long>> queryOrderShipmentWarehouses(List<Long> orderIds) {
        try {
            // 使用批量查询获取所有订单的发货记录
            Map<Long, List<WhsOrderShipmentVo>> orderShipmentsMap =
                orderShipmentQueryService.batchGetOrderShipmentRecords(orderIds);

            // 转换为订单ID -> 仓库ID集合的映射
            Map<Long, Set<Long>> result = new HashMap<>();

            for (Long orderId : orderIds) {
                List<WhsOrderShipmentVo> shipments = orderShipmentsMap.get(orderId);
                if (shipments != null && !shipments.isEmpty()) {
                    Set<Long> warehouseIds = shipments.stream()
                        .map(WhsOrderShipmentVo::getWarehouseId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                    result.put(orderId, warehouseIds);
                } else {
                    result.put(orderId, Collections.emptySet());
                }
            }

            return result;
        } catch (Exception e) {
            log.error("批量查询订单发货仓库映射时发生错误: {}", e.getMessage());
            return orderIds.stream().collect(Collectors.toMap(
                orderId -> orderId,
                orderId -> Collections.emptySet()
            ));
        }
    }

    /**
     * 判断订单是否需要计算库存状态
     * 已完成、已取消、已发货、已送达的订单不需要计算库存状态
     */
    private boolean shouldCalculateStockStatus(WhsOrderVo orderVo) {
        if (orderVo == null) {
            return false;
        }

        // 检查订单状态
        String orderStatus = orderVo.getOrderStatus();
        if (orderStatus != null) {
            // 已完成或已取消的订单不需要计算库存状态
            if (String.valueOf(OrderStatus.COMPLETED.getValue()).equals(orderStatus) ||
                String.valueOf(OrderStatus.CANCELED.getValue()).equals(orderStatus)) {
                return false;
            }
        }

        // 检查发货状态
        String shipmentStatus = orderVo.getShipmentStatus();
        if (shipmentStatus != null) {
            // 已发货或已送达的订单不需要计算库存状态
            return !String.valueOf(ShipmentStatus.SHIPPED.getValue()).equals(shipmentStatus) &&
                !String.valueOf(ShipmentStatus.DELIVERED.getValue()).equals(shipmentStatus);
        }

        return true;
    }

    /**
     * 批量填充订单的库存状态信息
     *
     * @param orderVos 订单VO列表
     */
    private void fillStockStatusInfo(List<WhsOrderVo> orderVos) {
        if (orderVos == null || orderVos.isEmpty()) {
            return;
        }

        try {
            // 1. 过滤需要计算库存状态的订单
            List<WhsOrderVo> ordersNeedingStockStatus = orderVos.stream()
                .filter(this::shouldCalculateStockStatus)
                .toList();

            if (ordersNeedingStockStatus.isEmpty()) {
                return;
            }

            // 2. 获取需要计算库存状态的订单ID
            List<Long> orderIds = ordersNeedingStockStatus.stream()
                .map(WhsOrderVo::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            if (orderIds.isEmpty()) {
                return;
            }

            // 3. 批量计算库存状态（一次计算，复用结果）
            Map<Long, StockStatus> stockStatusMap = orderStockStatusService.batchCalculateOrderStockStatus(orderIds);

            // 4. 基于已计算的库存状态直接推导库存不足信息（避免重复计算）
            Map<Long, Boolean> insufficientStockMap = new HashMap<>();
            for (Long orderId : orderIds) {
                StockStatus status = stockStatusMap.get(orderId);
                boolean hasInsufficient = status == StockStatus.INSUFFICIENT ||
                    status == StockStatus.OUT_OF_STOCK ||
                    status == StockStatus.PARTIAL_STOCK;
                insufficientStockMap.put(orderId, hasInsufficient);
            }

            // 5. 为需要的订单设置库存状态信息
            ordersNeedingStockStatus.forEach(orderVo -> {
                Long orderId = orderVo.getId();
                if (orderId != null) {
                    setOrderStockStatus(orderVo, stockStatusMap.get(orderId), insufficientStockMap.get(orderId));
                }
            });

        } catch (Exception e) {
            log.error("批量填充订单库存状态信息时发生错误: {}", e.getMessage());
            setDefaultStockStatusForOrders(orderVos);
        }
    }

    /**
     * 为订单列表设置默认库存状态
     */
    private void setDefaultStockStatusForOrders(List<WhsOrderVo> orderVos) {
        orderVos.forEach(orderVo -> {
            orderVo.setStockStatus(StockStatus.OUT_OF_STOCK.getValue());
            orderVo.setStockStatusText(WhsEnumTranslationUtils.translate(StockStatus.class, StockStatus.OUT_OF_STOCK.getValue()));
            orderVo.setHasInsufficientStock(true);
        });
    }

    /**
     * 批量填充后台订单的发货统计信息
     */
    private void fillAdminOrderShipmentStatistics(List<WhsOrderVo> orderVos) {
        if (orderVos == null || orderVos.isEmpty()) {
            return;
        }

        // 获取所有订单ID
        List<Long> orderIds = orderVos.stream()
            .map(WhsOrderVo::getId)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        if (orderIds.isEmpty()) {
            return;
        }

        // 批量查询发货计划统计信息
        Map<Long, ShipmentPlanStatisticsBo> planStatisticsMap = batchGetShipmentPlanStatistics(orderIds);

        // 批量查询发货记录
        Map<Long, List<WhsOrderShipmentVo>> orderShipmentsMap =
            orderShipmentQueryService.batchGetOrderShipmentRecords(orderIds);

        // 批量获取没有发货计划的订单的订单项
        List<Long> ordersWithoutPlanIds = orderIds.stream()
            .filter(orderId -> !planStatisticsMap.containsKey(orderId))
            .collect(Collectors.toList());
        Map<Long, List<WhsOrderItem>> ordersWithoutPlanItems = new HashMap<>();
        if (!ordersWithoutPlanIds.isEmpty()) {
            ordersWithoutPlanItems.putAll(
                orderItemService.batchGetOrderItemsByOrderIds(ordersWithoutPlanIds)
            );
        }

        // 为每个订单设置统计信息
        orderVos.forEach(orderVo -> {
            Long orderId = orderVo.getId();
            ShipmentPlanStatisticsBo statistics = planStatisticsMap.get(orderId);
            List<WhsOrderShipmentVo> shipments = orderShipmentsMap.get(orderId);

            // 设置统计信息（如果没有发货计划则从订单项计算）
            if (statistics != null) {
                orderVo.setTotalCases(statistics.getTotalCases());
                orderVo.setTotalPcs(statistics.getTotalPcs());
                orderVo.setShippedCases(statistics.getShippedCases());
                orderVo.setShippedPcs(statistics.getShippedPcs());
            } else {
                List<WhsOrderItem> items = ordersWithoutPlanItems.get(orderId);
                if (items != null) {
                    ShipmentPlanStatisticsBo itemStats = calculateStatisticsFromOrderItems(items);
                    orderVo.setTotalCases(itemStats.getTotalCases());
                    orderVo.setTotalPcs(itemStats.getTotalPcs());
                }
                orderVo.setShippedCases(0);
                orderVo.setShippedPcs(0);
            }

            // 设置发货时间
            String shipmentDatesText = formatShipmentDatesText(shipments);
            orderVo.setShipmentDatesText(shipmentDatesText);
        });
    }

    /**
     * 格式化发货时间文本
     */
    private String formatShipmentDatesText(List<WhsOrderShipmentVo> shipments) {
        if (shipments == null || shipments.isEmpty()) {
            return "";
        }

        return shipments.stream()
            .filter(shipment -> shipment.getShippedDate() != null)
            .map(shipment -> new java.text.SimpleDateFormat("yyyy-MM-dd")
                .format(shipment.getShippedDate()))
            .distinct()
            .collect(Collectors.joining(", "));
    }

    /**
     * 批量获取发货计划统计信息
     */
    private Map<Long, ShipmentPlanStatisticsBo> batchGetShipmentPlanStatistics(List<Long> orderIds) {
        if (orderIds == null || orderIds.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<Long, ShipmentPlanStatisticsBo> result = new HashMap<>();

        // 查询已执行的发货计划
        LambdaQueryWrapper<WhsShipmentPlan> planWrapper = Wrappers.lambdaQuery();
        planWrapper.in(WhsShipmentPlan::getOrderId, orderIds)
            .eq(WhsShipmentPlan::getStatus, ShipmentPlanStatus.EXECUTED.getValue());
        List<WhsShipmentPlan> plans = shipmentPlanMapper.selectList(planWrapper);

        if (plans.isEmpty()) {
            return result;
        }

        // 获取计划ID列表
        List<Long> planIds = plans.stream().map(WhsShipmentPlan::getId).collect(Collectors.toList());
        Map<Long, Long> planToOrderMap = plans.stream()
            .collect(Collectors.toMap(WhsShipmentPlan::getId, WhsShipmentPlan::getOrderId));

        // 查询发货计划项
        LambdaQueryWrapper<WhsShipmentPlanItem> itemWrapper = Wrappers.lambdaQuery();
        itemWrapper.in(WhsShipmentPlanItem::getPlanId, planIds);
        List<WhsShipmentPlanItem> planItems = shipmentPlanItemMapper.selectList(itemWrapper);

        // 按订单ID分组计算统计信息
        Map<Long, List<WhsShipmentPlanItem>> orderPlanItemsMap = planItems.stream()
            .collect(Collectors.groupingBy(item -> planToOrderMap.get(item.getPlanId())));

        orderPlanItemsMap.forEach((orderId, items) -> {
            ShipmentPlanStatisticsBo statistics = calculateStatisticsFromPlanItems(items);
            result.put(orderId, statistics);
        });

        return result;
    }

    /**
     * 从发货计划项计算统计信息
     */
    private ShipmentPlanStatisticsBo calculateStatisticsFromPlanItems(List<WhsShipmentPlanItem> planItems) {
        int totalCases = 0;
        int totalPcs = 0;
        int shippedCases = 0;
        int shippedPcs = 0;

        for (WhsShipmentPlanItem item : planItems) {
            int quantity = item.getQuantity() != null ? item.getQuantity() : 0;
            int packagingQuantity = item.getPackagingQuantity() != null ? item.getPackagingQuantity() : 1;
            int shippedQuantity = item.getShippedQuantity() != null ? item.getShippedQuantity() : 0;
            Integer packagingType = item.getPackagingType();

            // 计算总PCS
            totalPcs += quantity * packagingQuantity;

            // 计算已发货PCS
            shippedPcs += shippedQuantity * packagingQuantity;

            // 计算箱数（包装类型为2表示整箱）
            if (packagingType != null && packagingType == 2) {
                totalCases += quantity;
                shippedCases += shippedQuantity;
            }
        }

        return new ShipmentPlanStatisticsBo(totalCases, totalPcs, shippedCases, shippedPcs);
    }

    private ShipmentPlanStatisticsBo calculateStatisticsFromOrderItems(List<WhsOrderItem> orderItems) {
        int totalCases = 0;
        int totalPcs = 0;

        for (WhsOrderItem item : orderItems) {
            int quantity = item.getQuantity() != null ? item.getQuantity() : 0;
            int packagingQuantity = item.getPackagingQuantity() != null ? item.getPackagingQuantity() : 1;
            Integer packagingType = item.getPackagingType();

            // 计算总PCS
            totalPcs += quantity * packagingQuantity;

            // 计算箱数（包装类型为2表示整箱）
            if (packagingType != null && packagingType == 2) {
                totalCases += quantity;
            }
        }

        return new ShipmentPlanStatisticsBo(totalCases, totalPcs, 0, 0);
    }

    /**
     * 填充订单运费信息
     * 汇总每个订单所有包裹的运费
     */
    private void fillOrderFreightInfo(List<WhsOrderVo> records) {
        if (records.isEmpty()) {
            return;
        }

        // 提取所有订单ID
        List<Long> orderIds = records.stream()
            .map(WhsOrderVo::getId)
            .collect(Collectors.toList());

        // 批量查询所有订单的运费信息
        LambdaQueryWrapper<WhsOrderFreight> freightQuery = new LambdaQueryWrapper<>();
        freightQuery.in(WhsOrderFreight::getOrderId, orderIds);
        freightQuery.eq(WhsOrderFreight::getDelFlag, "0");
        List<WhsOrderFreight> freightList = orderFreightMapper.selectList(freightQuery);

        // 按订单ID分组运费信息
        Map<Long, List<WhsOrderFreight>> freightMap = freightList.stream()
            .collect(Collectors.groupingBy(WhsOrderFreight::getOrderId));

        // 为每个订单计算运费汇总
        for (WhsOrderVo orderVo : records) {
            List<WhsOrderFreight> orderFreights = freightMap.get(orderVo.getId());
            if (orderFreights != null && !orderFreights.isEmpty()) {
                // 计算总实际运费
                BigDecimal totalActualFreight = orderFreights.stream()
                    .map(WhsOrderFreight::getActualFreight)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                // 设置运费信息（只有大于0才设置）
                if (totalActualFreight.compareTo(BigDecimal.ZERO) > 0) {
                    orderVo.setActualFreight(totalActualFreight);
                    // 设置运费币种（取第一个运费记录的币种）
                    orderVo.setFreightCurrency(orderFreights.get(0).getFreightCurrency());
                }
            }
        }
    }

    /**
     * 设置按钮显示逻辑
     * 发货审批按钮和发货按钮互斥显示
     *
     * @param orderVo 订单VO对象
     * @param orderId 订单ID
     */
    private void setButtonDisplayLogic(WhsOrderVo orderVo, Long orderId) {
        try {
            // 默认都不显示
            orderVo.setShowApprovalButton(false);
            orderVo.setShowShipmentButton(false);
            orderVo.setShowCancelApprovalButton(false);
            orderVo.setShowApproveButton(false);

            // 使用新的权限检查系统
            WhsOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                log.warn("订单不存在: orderId={}", orderId);
                return;
            }

            // 检查发货按钮显示权限
            boolean shouldShowShipmentButton = shipmentPermissionService.shouldShowShipmentButton(order);
            orderVo.setShowShipmentButton(shouldShowShipmentButton);

            // 检查审批申请按钮显示权限
            boolean shouldShowApprovalButton = shipmentPermissionService.shouldShowApprovalButton(order);
            orderVo.setShowApprovalButton(shouldShowApprovalButton);

            // 检查撤回审批按钮显示权限
            boolean shouldShowCancelApprovalButton = shipmentPermissionService.shouldShowCancelApprovalButton(order);
            orderVo.setShowCancelApprovalButton(shouldShowCancelApprovalButton);

            // 检查审批操作按钮显示权限
            boolean shouldShowApproveButton = shipmentPermissionService.shouldShowApproveButton(order);
            orderVo.setShowApproveButton(shouldShowApproveButton);

            // 设置发货按钮文本
            if (shouldShowShipmentButton) {
                String buttonText = shipmentPermissionService.getShipmentButtonText(order);
                orderVo.setShipmentButtonText(buttonText);
            }

            log.debug("订单[{}]按钮显示逻辑 - 发货:{}, 申请审批:{}, 撤回审批:{}, 审批操作:{}",
                orderId, shouldShowShipmentButton, shouldShowApprovalButton,
                shouldShowCancelApprovalButton, shouldShowApproveButton);

        } catch (Exception e) {
            log.error("设置订单[{}]按钮显示逻辑时发生错误: {}", orderId, e.getMessage(), e);
            // 出错时保守处理，都不显示
            orderVo.setShowApprovalButton(false);
            orderVo.setShowShipmentButton(false);
            orderVo.setShowCancelApprovalButton(false);
            orderVo.setShowApproveButton(false);
        }
    }


}
