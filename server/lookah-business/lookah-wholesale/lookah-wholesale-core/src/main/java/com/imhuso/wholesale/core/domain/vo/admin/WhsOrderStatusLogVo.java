package com.imhuso.wholesale.core.domain.vo.admin;

import com.imhuso.common.translation.annotation.Translation;
import com.imhuso.wholesale.core.constant.TransConstant;
import com.imhuso.wholesale.core.domain.WhsOrderStatusLog;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

import static com.imhuso.common.translation.constant.TransConstant.USER_ID_TO_NAME;

/**
 * 批发订单状态日志后台视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsOrderStatusLog.class)
public class WhsOrderStatusLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 订单状态描述
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, mapper = "orderStatus")
    private String orderStatusText;

    /**
     * 支付状态
     */
    private String paymentStatus;

    /**
     * 支付状态描述
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, mapper = "paymentStatus")
    private String paymentStatusText;

    /**
     * 操作备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * 上传人
     */
    private Long createBy;

    /**
     * 操作者
     */
    @Translation(type = USER_ID_TO_NAME, mapper = "createBy")
    private String createByName;
}
