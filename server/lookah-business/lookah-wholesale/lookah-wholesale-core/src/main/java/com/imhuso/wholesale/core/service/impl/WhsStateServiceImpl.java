package com.imhuso.wholesale.core.service.impl;

import java.util.List;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.constant.WhsCacheConstants;
import com.imhuso.wholesale.core.domain.WhsCountry;
import com.imhuso.wholesale.core.domain.WhsState;
import com.imhuso.wholesale.core.domain.vo.admin.WhsStateVo;
import com.imhuso.wholesale.core.mapper.WhsCountryMapper;
import com.imhuso.wholesale.core.mapper.WhsStateMapper;
import com.imhuso.wholesale.core.service.IWhsStateService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 州/省服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsStateServiceImpl implements IWhsStateService {

    private final WhsStateMapper baseMapper;
    private final WhsCountryMapper countryMapper;

    /**
     * 根据国家ID和州/省代码获取州/省信息
     *
     * @param countryId 国家ID
     * @param code      州/省代码
     * @return 州/省信息
     */
    @Override
    @Cacheable(cacheNames = WhsCacheConstants.STATE_CACHE, key = "'country:' + #countryId + ':code:' + #code")
    public WhsStateVo getStateByCountryIdAndCode(Long countryId, String code) {
        if (countryId == null || StringUtils.isEmpty(code)) {
            return null;
        }

        // 从数据库查询
        LambdaQueryWrapper<WhsState> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WhsState::getCountryId, countryId);
        lqw.eq(WhsState::getCode, code);
        lqw.eq(WhsState::getStatus, BusinessConstants.NORMAL);
        return baseMapper.selectVoOne(lqw, WhsStateVo.class);
    }

    /**
     * 根据国家ID和州/省代码获取州/省名称
     *
     * @param countryId 国家ID
     * @param code      州/省代码
     * @return 州/省名称
     */
    @Override
    @Cacheable(cacheNames = WhsCacheConstants.STATE_CACHE, key = "'name:country:' + #countryId + ':code:' + #code")
    public String getStateNameByCountryIdAndCode(Long countryId, String code) {
        if (countryId == null || StringUtils.isEmpty(code)) {
            return code;
        }

        WhsState state = queryStateByCountryIdAndCode(countryId, code);
        return state != null ? state.getName() : code;
    }

    /**
     * 根据国家代码和州/省代码获取州/省名称
     *
     * @param countryCode 国家代码
     * @param stateCode   州/省代码
     * @return 州/省名称
     */
    @Override
    @Cacheable(cacheNames = WhsCacheConstants.STATE_CACHE, key = "'name:countryCode:' + #countryCode + ':stateCode:' + #stateCode")
    public String getStateNameByCountryCodeAndStateCode(String countryCode, String stateCode) {
        if (StringUtils.isEmpty(countryCode) || StringUtils.isEmpty(stateCode)) {
            return stateCode;
        }

        // 直接使用countryMapper查询国家实体，避免类型转换问题
        WhsCountry country = countryMapper.selectOne(new LambdaQueryWrapper<WhsCountry>()
            .eq(WhsCountry::getCode, countryCode)
            .eq(WhsCountry::getStatus, BusinessConstants.NORMAL));
        if (country == null) {
            return stateCode;
        }

        WhsState state = queryStateByCountryIdAndCode(country.getId(), stateCode);
        return state != null ? state.getName() : stateCode;
    }

    /**
     * 根据国家ID获取州/省列表
     *
     * @param countryId 国家ID
     * @return 州/省列表
     */
    @Override
    @Cacheable(cacheNames = WhsCacheConstants.STATE_CACHE, key = "'country:' + #countryId + ':list:' + T(com.imhuso.wholesale.core.domain.vo.admin.WhsStateVo).class.getName()")
    public List<WhsStateVo> getStatesByCountryId(Long countryId) {
        if (countryId == null) {
            return null;
        }

        // 直接查询数据库，避免自调用
        return baseMapper.selectVoList(buildStateQuery(countryId), WhsStateVo.class);
    }

    /**
     * 根据国家ID获取州/省列表（支持指定返回类型）
     *
     * @param countryId 国家ID
     * @param clazz     返回类型的Class
     * @param <T>       返回类型
     * @return 州/省列表
     */
    @Override
    @Cacheable(cacheNames = WhsCacheConstants.STATE_CACHE, key = "'country:' + #countryId + ':list:' + #clazz.getName()")
    public <T> List<T> getStatesByCountryId(Long countryId, Class<T> clazz) {
        if (countryId == null) {
            return null;
        }

        return baseMapper.selectVoList(buildStateQuery(countryId), clazz);
    }

    /**
     * 构建州/省查询条件
     *
     * @param countryId 国家ID
     * @return 查询条件
     */
    private LambdaQueryWrapper<WhsState> buildStateQuery(Long countryId) {
        LambdaQueryWrapper<WhsState> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WhsState::getCountryId, countryId);
        lqw.eq(WhsState::getStatus, BusinessConstants.NORMAL);
        lqw.orderByAsc(WhsState::getSort);
        return lqw;
    }

    /**
     * 根据国家ID和州/省代码查询州/省实体
     *
     * @param countryId 国家ID
     * @param code      州/省代码
     * @return 州/省实体
     */
    private WhsState queryStateByCountryIdAndCode(Long countryId, String code) {
        return baseMapper.selectOne(new LambdaQueryWrapper<WhsState>()
            .eq(WhsState::getCountryId, countryId)
            .eq(WhsState::getCode, code)
            .eq(WhsState::getStatus, BusinessConstants.NORMAL));
    }
}
