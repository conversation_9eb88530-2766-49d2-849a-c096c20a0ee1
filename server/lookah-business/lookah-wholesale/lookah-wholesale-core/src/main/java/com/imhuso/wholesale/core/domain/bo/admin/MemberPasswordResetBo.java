package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 会员密码重置业务对象
 *
 * <AUTHOR>
 */
@Data
public class MemberPasswordResetBo {

    /**
     * 会员ID
     */
    @NotNull(message = "会员ID不能为空", groups = {EditGroup.class})
    private Long memberId;

    /**
     * 新密码
     */
    @NotBlank(message = "密码不能为空", groups = {EditGroup.class})
    @Size(min = 6, max = 20, message = "密码长度必须在6到20个字符之间", groups = {EditGroup.class})
    private String password;
}
