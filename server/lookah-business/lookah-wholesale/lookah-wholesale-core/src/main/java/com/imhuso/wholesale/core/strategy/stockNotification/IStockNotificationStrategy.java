package com.imhuso.wholesale.core.strategy.stockNotification;

import com.imhuso.wholesale.core.domain.WhsStockNotify;
import com.imhuso.wholesale.core.event.WhsStockNotifyEvent.StockNotifyEventType;

import java.util.Map;

/**
 * 库存通知策略接口
 * <p>
 * 定义不同通知渠道的通用行为，各种渠道（如邮件、短信、应用推送等）需要实现此接口
 * </p>
 *
 * <AUTHOR>
 */
public interface IStockNotificationStrategy {

    /**
     * 通知配置类
     * 封装各种通知事件的配置
     *
     * @param notifyAdmin    是否通知管理员
     * @param notifyCustomer 是否通知客户
     */
    record NotificationConfig(boolean notifyAdmin, boolean notifyCustomer) {
        /**
         * 构造函数
         */
        public NotificationConfig {
        }

        /**
         * 是否通知管理员
         */
        @Override
        public boolean notifyAdmin() {
            return notifyAdmin;
        }

        /**
         * 是否通知客户
         */
        @Override
        public boolean notifyCustomer() {
            return notifyCustomer;
        }
    }

    /**
     * 获取通知配置映射
     * 实现类需要提供具体的配置
     *
     * @return 通知配置映射
     */
    Map<StockNotifyEventType, NotificationConfig> getConfigMap();

    /**
     * 获取策略名称
     *
     * @return 通知策略名称
     */
    String getName();

    /**
     * 发送管理员通知
     *
     * @param stockNotify 库存通知对象
     * @param eventType   事件类型
     * @return 发送结果
     */
    boolean sendAdminNotification(WhsStockNotify stockNotify, StockNotifyEventType eventType);

    /**
     * 发送客户通知
     *
     * @param stockNotify 库存通知对象
     * @param eventType   事件类型
     * @return 发送结果
     */
    boolean sendCustomerNotification(WhsStockNotify stockNotify, StockNotifyEventType eventType);

    /**
     * 判断是否启用管理员通知
     *
     * @param eventType 事件类型
     * @return 是否启用
     */
    default boolean isAdminNotificationEnabled(StockNotifyEventType eventType) {
        NotificationConfig config = getConfigMap().get(eventType);
        return config != null && config.notifyAdmin();
    }

    /**
     * 判断是否启用客户通知
     *
     * @param eventType 事件类型
     * @return 是否启用
     */
    default boolean isCustomerNotificationEnabled(StockNotifyEventType eventType) {
        NotificationConfig config = getConfigMap().get(eventType);
        return config != null && config.notifyCustomer();
    }
}
