package com.imhuso.wholesale.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.satoken.utils.LoginHelper;
import com.imhuso.wholesale.core.constant.WholesaleConstants;
import com.imhuso.wholesale.core.domain.WhsOrder;
import com.imhuso.wholesale.core.domain.WhsOrderShipmentApproval;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentApprovalVo;
import com.imhuso.wholesale.core.enums.OrderStatus;
import com.imhuso.wholesale.core.enums.ShipmentApprovalStatus;
import com.imhuso.wholesale.core.enums.ShipmentApprovalOperationType;
import com.imhuso.wholesale.core.enums.ShipmentApprovalRecordStatus;
import com.imhuso.wholesale.core.mapper.WhsOrderMapper;
import com.imhuso.wholesale.core.mapper.WhsOrderShipmentApprovalMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 订单发货审批事务服务
 * 专门处理涉及事务的审批操作，避免自调用问题
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsOrderShipmentApprovalTransactionService {

    private final WhsOrderShipmentApprovalMapper approvalMapper;
    private final WhsOrderMapper orderMapper;

    /**
     * 在事务中执行审批申请的数据库操作
     *
     * @param orderId           订单ID
     * @param applicationReason 申请理由
     * @return 审批记录ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long submitApprovalRequestInTransaction(Long orderId, String applicationReason) {
        // 1. 验证订单状态
        validateOrderForApproval(orderId);

        // 2. 检查是否已有待审核的申请
        WhsOrderShipmentApprovalVo existingApproval = queryLatestByOrderId(orderId);
        if (existingApproval != null && ShipmentApprovalStatus.PENDING.getValue().equals(existingApproval.getApprovalStatus())) {
            throw new ServiceException("该订单已有待审核的发货申请，请勿重复提交");
        }

        // 3. 创建审核申请记录
        Long approvalId = createApprovalRecord(orderId, ShipmentApprovalOperationType.SUBMIT,
            ShipmentApprovalStatus.PENDING, applicationReason, null);

        // 4. 同步审批状态到订单表
        syncOrderApprovalStatus(orderId, ShipmentApprovalStatus.PENDING.getValue());

        log.info("发货审批申请提交成功: orderId={}, approvalId={}, applicant={}",
            orderId, approvalId, LoginHelper.getUsername());

        return approvalId;
    }

    /**
     * 系统自动提交审批申请（事务方法）
     *
     * @param orderId           订单ID
     * @param applicationReason 申请理由
     * @return 审批记录ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long submitApprovalRequestBySystem(Long orderId, String applicationReason) {
        // 1. 验证订单状态
        validateOrderForApproval(orderId);

        // 2. 检查是否已有待审核的申请
        WhsOrderShipmentApprovalVo existingApproval = queryLatestByOrderId(orderId);
        if (existingApproval != null && ShipmentApprovalStatus.PENDING.getValue().equals(existingApproval.getApprovalStatus())) {
            log.warn("订单[{}]已有待审核的发货申请，跳过系统自动申请", orderId);
            return existingApproval.getId();
        }

        // 3. 创建审核申请记录（使用系统身份）
        WhsOrderShipmentApproval approval = new WhsOrderShipmentApproval();
        approval.setOrderId(orderId);
        approval.setApplicantId(WholesaleConstants.System.ADMIN_USER_ID);
        approval.setApplicantName(WholesaleConstants.System.AUTO_USER_NAME);
        approval.setApplicationReason(applicationReason);
        approval.setApprovalStatus(ShipmentApprovalStatus.PENDING.getValue());
        approval.setOperationType(ShipmentApprovalOperationType.SUBMIT.getValue());
        approval.setOperationSequence(getNextOperationSequence(orderId));

        approvalMapper.insert(approval);

        // 4. 同步审批状态到订单表
        syncOrderApprovalStatus(orderId, ShipmentApprovalStatus.PENDING.getValue());

        log.info("系统自动发货审批申请提交成功: orderId={}, approvalId={}", orderId, approval.getId());

        return approval.getId();
    }

    /**
     * 审批发货申请（事务方法）
     *
     * @param approvalId      审批记录ID
     * @param approved        是否通过
     * @param approvalComment 审批意见
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean approveShipmentRequest(Long approvalId, boolean approved, String approvalComment) {
        // 1. 查询原始审核记录（SUBMIT记录）
        WhsOrderShipmentApproval originalApproval = approvalMapper.selectById(approvalId);
        if (originalApproval == null) {
            throw new ServiceException("审核记录不存在");
        }

        // 2. 检查订单当前是否还有待审核状态
        WhsOrderShipmentApprovalVo latestApproval = queryLatestByOrderId(originalApproval.getOrderId());
        if (latestApproval == null || !ShipmentApprovalStatus.PENDING.getValue().equals(latestApproval.getApprovalStatus())) {
            throw new ServiceException("该审核申请已处理，无法重复操作");
        }

        // 3. 创建新的审批操作记录
        ShipmentApprovalOperationType operationType = approved ? ShipmentApprovalOperationType.APPROVE : ShipmentApprovalOperationType.REJECT;
        ShipmentApprovalStatus approvalStatus = approved ? ShipmentApprovalStatus.APPROVED : ShipmentApprovalStatus.REJECTED;

        Long newApprovalId = createApprovalRecord(originalApproval.getOrderId(), operationType,
            approvalStatus, null, approvalComment);

        if (newApprovalId != null) {
            // 同步审批状态到订单表
            syncOrderApprovalStatus(originalApproval.getOrderId(), approvalStatus.getValue());

            log.info("发货审批处理完成: originalApprovalId={}, newApprovalId={}, approved={}, approver={}",
                approvalId, newApprovalId, approved, LoginHelper.getUsername());

            return true;
        }

        return false;
    }

    /**
     * 撤销审批申请（事务方法）
     *
     * @param approvalId 审批记录ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelApprovalRequest(Long approvalId) {
        // 1. 查询原始审核记录
        WhsOrderShipmentApproval originalApproval = approvalMapper.selectById(approvalId);
        if (originalApproval == null) {
            throw new ServiceException("审核记录不存在");
        }

        // 2. 检查订单当前是否还有待审核状态
        WhsOrderShipmentApprovalVo latestApproval = queryLatestByOrderId(originalApproval.getOrderId());
        if (latestApproval == null || !ShipmentApprovalStatus.PENDING.getValue().equals(latestApproval.getApprovalStatus())) {
            throw new ServiceException("只有待审核状态的申请才能撤销");
        }

        // 3. 检查权限（只有申请人可以撤销）
        Long currentUserId = LoginHelper.getUserId();
        Long applicantId = originalApproval.getApplicantId();
        if (applicantId == null || !applicantId.equals(currentUserId)) {
            throw new ServiceException("只有申请人才能撤销审核申请");
        }

        // 4. 创建撤销操作记录
        Long cancelApprovalId = createApprovalRecord(originalApproval.getOrderId(),
            ShipmentApprovalOperationType.CANCEL,
            ShipmentApprovalStatus.CANCELLED, null, null);

        if (cancelApprovalId != null) {
            // 同步审批状态到订单表
            syncOrderApprovalStatus(originalApproval.getOrderId(), ShipmentApprovalStatus.CANCELLED.getValue());

            log.info("发货审批申请已撤销: originalApprovalId={}, cancelApprovalId={}, applicant={}",
                approvalId, cancelApprovalId, LoginHelper.getUsername());

            return true;
        }

        return false;
    }

    /**
     * 系统级别撤销审批申请（事务方法）
     * 用于系统自动操作，如订单强制完成时撤销相关审批
     * 不进行申请人权限检查
     *
     * @param approvalId 审批记录ID
     * @param reason     撤销原因
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelApprovalRequestBySystem(Long approvalId, String reason) {
        // 1. 查询原始审核记录
        WhsOrderShipmentApproval originalApproval = approvalMapper.selectById(approvalId);
        if (originalApproval == null) {
            throw new ServiceException("审核记录不存在");
        }

        // 2. 检查订单当前是否还有待审核状态
        WhsOrderShipmentApprovalVo latestApproval = queryLatestByOrderId(originalApproval.getOrderId());
        if (latestApproval == null || !ShipmentApprovalStatus.PENDING.getValue().equals(latestApproval.getApprovalStatus())) {
            log.warn("系统撤销审批申请时发现状态不是待审核: orderId={}, currentStatus={}",
                originalApproval.getOrderId(), latestApproval != null ? latestApproval.getApprovalStatus() : "无记录");
            return true; // 已经不是待审核状态，认为操作成功
        }

        // 3. 创建撤销操作记录（系统操作，不检查申请人权限）
        Long cancelApprovalId = createApprovalRecord(originalApproval.getOrderId(),
            ShipmentApprovalOperationType.CANCEL,
            ShipmentApprovalStatus.CANCELLED, reason, null);

        if (cancelApprovalId != null) {
            // 同步审批状态到订单表
            syncOrderApprovalStatus(originalApproval.getOrderId(), ShipmentApprovalStatus.CANCELLED.getValue());

            log.info("系统已撤销发货审批申请: originalApprovalId={}, cancelApprovalId={}, reason={}",
                approvalId, cancelApprovalId, reason);

            return true;
        }

        return false;
    }

    /**
     * 重置订单的审核数据（状态和记录）
     * 用于订单撤回时清理审核相关数据
     *
     * @param orderId 订单ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void resetOrderApprovalData(Long orderId) {
        log.info("重置订单审核数据: orderId={}", orderId);

        // 1. 重置订单审核状态为无需审批
        syncOrderApprovalStatus(orderId, ShipmentApprovalStatus.NO_APPROVAL.getValue());

        // 2. 删除所有审核记录
        LambdaQueryWrapper<WhsOrderShipmentApproval> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(WhsOrderShipmentApproval::getOrderId, orderId);

        List<WhsOrderShipmentApproval> approvalRecords = approvalMapper.selectList(queryWrapper);
        if (approvalRecords != null && !approvalRecords.isEmpty()) {
            int deletedCount = approvalMapper.delete(queryWrapper);
            log.info("已清理订单审核记录: orderId={}, deletedCount={}", orderId, deletedCount);
        }
    }

    // ============== 私有辅助方法 ==============

    /**
     * 验证订单是否可以进行审批申请
     *
     * @param orderId 订单ID
     */
    private void validateOrderForApproval(Long orderId) {
        WhsOrder order = orderMapper.selectById(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        if (!OrderStatus.PROCESSING.getValue().equals(order.getOrderStatus()) &&
            !OrderStatus.PENDING.getValue().equals(order.getOrderStatus())) {
            throw new ServiceException("只有待处理或处理中状态的订单才能申请发货审批");
        }

    }

    /**
     * 查询订单最新的审批记录
     *
     * @param orderId 订单ID
     * @return 最新审批记录
     */
    private WhsOrderShipmentApprovalVo queryLatestByOrderId(Long orderId) {
        LambdaQueryWrapper<WhsOrderShipmentApproval> lqw = Wrappers.lambdaQuery();
        lqw.eq(WhsOrderShipmentApproval::getOrderId, orderId)
            .eq(WhsOrderShipmentApproval::getRecordStatus, ShipmentApprovalRecordStatus.ACTIVE.getValue())
            .orderByDesc(WhsOrderShipmentApproval::getOperationSequence)
            .orderByDesc(WhsOrderShipmentApproval::getCreateTime)
            .last("LIMIT 1");
        return approvalMapper.selectVoOneWithOrderNo(lqw);
    }

    /**
     * 同步审批状态到订单表
     *
     * @param orderId        订单ID
     * @param approvalStatus 审批状态
     */
    private void syncOrderApprovalStatus(Long orderId, Integer approvalStatus) {
        try {
            WhsOrder orderUpdate = new WhsOrder();
            orderUpdate.setId(orderId);
            orderUpdate.setShipmentApprovalStatus(approvalStatus);

            int result = orderMapper.updateById(orderUpdate);
            if (result > 0) {
                log.debug("订单审批状态同步成功: orderId={}, status={}", orderId, approvalStatus);
            } else {
                log.warn("订单审批状态同步失败: orderId={}, status={}", orderId, approvalStatus);
            }
        } catch (Exception e) {
            log.error("同步订单审批状态时发生异常: orderId={}, status={}, error={}",
                orderId, approvalStatus, e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 获取订单的下一个操作序号
     *
     * @param orderId 订单ID
     * @return 下一个操作序号
     */
    private Integer getNextOperationSequence(Long orderId) {
        LambdaQueryWrapper<WhsOrderShipmentApproval> lqw = Wrappers.lambdaQuery();
        lqw.eq(WhsOrderShipmentApproval::getOrderId, orderId)
            .orderByDesc(WhsOrderShipmentApproval::getOperationSequence)
            .last("LIMIT 1");

        WhsOrderShipmentApproval lastRecord = approvalMapper.selectOne(lqw);
        return lastRecord != null ? lastRecord.getOperationSequence() + 1 : 1;
    }

    /**
     * 创建审批操作记录
     *
     * @param orderId           订单ID
     * @param operationType     操作类型
     * @param approvalStatus    审批状态
     * @param applicationReason 申请理由（仅提交时需要）
     * @param approvalComment   审批意见（仅审批时需要）
     * @return 创建的记录ID
     */
    private Long createApprovalRecord(Long orderId, ShipmentApprovalOperationType operationType,
                                      ShipmentApprovalStatus approvalStatus, String applicationReason,
                                      String approvalComment) {
        // 1. 先将该订单的所有现有记录标记为SUPERSEDED
        markExistingRecordsAsSuperseded(orderId);

        // 2. 创建基础审批记录
        WhsOrderShipmentApproval approval = createBaseApprovalRecord(orderId, operationType, approvalStatus);

        // 3. 根据操作类型设置特定字段
        switch (operationType) {
            case SUBMIT:
                fillSubmitApprovalFields(approval, applicationReason);
                break;
            case APPROVE:
            case REJECT:
                fillApprovalFields(approval, orderId, approvalComment);
                break;
            case CANCEL:
                fillCancelApprovalFields(approval, orderId);
                break;
        }

        approvalMapper.insert(approval);
        return approval.getId();
    }

    /**
     * 创建基础审批记录
     */
    private WhsOrderShipmentApproval createBaseApprovalRecord(Long orderId, ShipmentApprovalOperationType operationType,
                                                              ShipmentApprovalStatus approvalStatus) {
        WhsOrderShipmentApproval approval = new WhsOrderShipmentApproval();
        approval.setOrderId(orderId);
        approval.setOperationType(operationType.getValue());
        approval.setOperationSequence(getNextOperationSequence(orderId));
        approval.setApprovalStatus(approvalStatus.getValue());
        approval.setRecordStatus(ShipmentApprovalRecordStatus.ACTIVE.getValue());
        return approval;
    }

    /**
     * 填充提交申请的字段
     */
    private void fillSubmitApprovalFields(WhsOrderShipmentApproval approval, String applicationReason) {
        approval.setApplicantId(LoginHelper.getUserId());
        approval.setApplicantName(LoginHelper.getUsername());
        approval.setApplicationReason(applicationReason);
    }

    /**
     * 填充审批操作的字段
     */
    private void fillApprovalFields(WhsOrderShipmentApproval approval, Long orderId, String approvalComment) {
        // 复制最新提交记录的申请信息
        WhsOrderShipmentApprovalVo latestSubmit = getLatestSubmitRecord(orderId);
        if (latestSubmit != null) {
            approval.setApplicantId(latestSubmit.getApplicantId());
            approval.setApplicantName(latestSubmit.getApplicantName());
            approval.setApplicationReason(latestSubmit.getApplicationReason());
        } else {
            // 回退：如果找不到SUBMIT记录，使用当前用户信息
            approval.setApplicantId(LoginHelper.getUserId());
            approval.setApplicantName(LoginHelper.getUsername());
            approval.setApplicationReason("审批处理");
        }

        // 设置审批人信息
        approval.setApproverId(LoginHelper.getUserId());
        approval.setApproverName(LoginHelper.getUsername());
        approval.setApprovalTime(new Date());
        approval.setApprovalComment(approvalComment);
    }

    /**
     * 填充撤销申请的字段
     */
    private void fillCancelApprovalFields(WhsOrderShipmentApproval approval, Long orderId) {
        // 复制最新提交记录的申请信息
        WhsOrderShipmentApprovalVo latestSubmit = getLatestSubmitRecord(orderId);
        if (latestSubmit != null) {
            approval.setApplicantId(latestSubmit.getApplicantId());
            approval.setApplicantName(latestSubmit.getApplicantName());
            approval.setApplicationReason(latestSubmit.getApplicationReason());
        } else {
            // 回退：如果找不到SUBMIT记录，使用当前用户信息
            approval.setApplicantId(LoginHelper.getUserId());
            approval.setApplicantName(LoginHelper.getUsername());
            approval.setApplicationReason("撤销申请");
        }
    }

    /**
     * 获取最新的提交记录
     *
     * @param orderId 订单ID
     * @return 最新的提交记录
     */
    private WhsOrderShipmentApprovalVo getLatestSubmitRecord(Long orderId) {
        LambdaQueryWrapper<WhsOrderShipmentApproval> lqw = Wrappers.lambdaQuery();
        lqw.eq(WhsOrderShipmentApproval::getOrderId, orderId)
            .eq(WhsOrderShipmentApproval::getOperationType, ShipmentApprovalOperationType.SUBMIT.getValue())
            .orderByDesc(WhsOrderShipmentApproval::getOperationSequence)
            .last("LIMIT 1");
        return approvalMapper.selectVoOneWithOrderNo(lqw);
    }

    /**
     * 将指定订单的所有现有记录标记为SUPERSEDED状态
     * 使用批量更新优化性能，避免N+1问题
     *
     * @param orderId 订单ID
     */
    private void markExistingRecordsAsSuperseded(Long orderId) {
        LambdaUpdateWrapper<WhsOrderShipmentApproval> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(WhsOrderShipmentApproval::getOrderId, orderId)
            .eq(WhsOrderShipmentApproval::getRecordStatus, ShipmentApprovalRecordStatus.ACTIVE.getValue())
            .set(WhsOrderShipmentApproval::getRecordStatus, ShipmentApprovalRecordStatus.SUPERSEDED.getValue());

        int updateCount = approvalMapper.update(null, updateWrapper);
        if (updateCount > 0) {
            log.debug("将订单[{}]的{}条现有审批记录标记为SUPERSEDED状态", orderId, updateCount);
        }
    }
}
