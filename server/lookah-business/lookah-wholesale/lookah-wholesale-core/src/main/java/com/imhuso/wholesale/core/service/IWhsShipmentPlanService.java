package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanVo;

import java.util.List;

/**
 * 发货方案服务接口
 *
 * <AUTHOR>
 */
public interface IWhsShipmentPlanService {

    /**
     * 根据订单ID获取发货方案列表
     *
     * @param orderId 订单ID
     * @return 发货方案列表
     */
    List<WhsShipmentPlanVo> getShipmentPlansByOrderId(Long orderId);

    /**
     * 选择发货方案
     *
     * @param planId 方案ID
     * @return 操作结果
     */
    boolean selectShipmentPlan(Long planId);

    /**
     * 根据方案ID获取发货方案
     *
     * @param planId 方案ID
     * @return 发货方案
     */
    WhsShipmentPlanVo getShipmentPlanById(Long planId);

    /**
     * 将订单的其他所有计划标记为不可用
     * 在一个计划成功发货后调用此方法，防止重复发货
     *
     * @param orderId        订单ID
     * @param executedPlanId 已执行的计划ID
     */
    void markOtherPlansAsUnavailable(Long orderId, Long executedPlanId);

    /**
     * 更新单个发货方案的状态
     *
     * @param planId 方案ID
     * @param status 新的状态值 (参考 ShipmentPlanStatus 枚举)
     */
    void updatePlanStatus(Long planId, Integer status);

    /**
     * 删除订单的所有发货方案及相关数据
     * 包括发货方案、发货方案项和发货转换记录
     *
     * @param orderId 订单ID
     */
    void deleteAllShipmentPlansByOrderId(Long orderId);
}
