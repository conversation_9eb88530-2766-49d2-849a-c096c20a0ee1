package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.wholesale.core.domain.WhsOrder;
import com.imhuso.wholesale.core.domain.WhsOrderItem;
import com.imhuso.wholesale.core.domain.WhsProduct;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderCreateBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderCreateItemBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderFileCreateBo;
import com.imhuso.wholesale.core.domain.bo.front.CartCheckResultBo;
import com.imhuso.wholesale.core.domain.bo.front.OrderBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVariantVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShippingAddressVo;
import com.imhuso.wholesale.core.domain.vo.front.CartVo;
import com.imhuso.wholesale.core.domain.vo.front.ShippingAddressVo;
import com.imhuso.wholesale.core.enums.*;
import com.imhuso.wholesale.core.mapper.WhsOrderItemMapper;
import com.imhuso.wholesale.core.mapper.WhsOrderMapper;
import com.imhuso.wholesale.core.satoken.WholesaleLoginHelper;
import com.imhuso.wholesale.core.service.*;
import com.imhuso.wholesale.core.service.IApprovalConfigService;
import com.imhuso.wholesale.core.utils.WhsInvoiceIdGenerator;
import com.imhuso.wholesale.enums.OrderFileType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 批发订单创建服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsOrderCreationServiceImpl implements IWhsOrderCreationService {

    private static final String CUSTOMER_ORDER_DESC = "客户下单";
    private static final String ADMIN_MANUAL_ORDER_DESC = "管理员手动下单";
    private static final String COLON_SEPARATOR = ":";
    private static final String COMMA_SEPARATOR = ",";
    private static final String SEMICOLON_SEPARATOR = ";";
    private static final String LEFT_BRACE = "{";
    private static final String RIGHT_BRACE = "}";
    private static final String EMPTY_JSON_OBJECT = "{}";
    private static final int DEFAULT_MAP_SIZE = 16;

    private final WhsOrderMapper baseMapper;
    private final WhsOrderItemMapper itemMapper;
    private final IWhsCartService cartService;
    private final IWhsShippingAddressService addressService;
    private final IWhsProductVariantService variantService;
    private final IWhsPackageItemService packageItemService;
    private final IWhsOrderStatusService orderStatusService;
    private final WhsInvoiceIdGenerator invoiceIdGenerator;
    private final IWhsStockAllocationService stockAllocationService;
    private final IWhsOrderFileService orderFileService;
    private final IWhsOrderItemService orderItemService;
    private final IWhsProductService productService;
    private final IApprovalConfigService approvalConfigService;
    private final IWhsShipmentPlanService shipmentPlanService;
    private final IWhsMemberAdminService memberService;

    /**
     * 创建订单（从购物车获取商品信息，不依赖前端传递商品详情）
     * 【前台】
     */
    @Override
    @Log(title = "订单创建", businessType = BusinessType.INSERT)
    @Transactional(rollbackFor = Exception.class)
    public Long createOrder(OrderBo bo) {
        Long memberId = WholesaleLoginHelper.getUserId();
        log.info("开始创建订单，会员ID: {}", memberId);

        // 1. 校验收货地址和设置基本信息
        ShippingAddressVo address = validateAddressAndPrepareOrder(bo, memberId);
        log.debug("订单地址校验通过，地址ID: {}", bo.getAddressId());

        // 2. 获取购物车商品并校验
        CartCheckResultBo cartResult = cartService.getAndCheckCartItems();
        List<CartVo> cartItems = cartResult.getCartItems();

        if (CollUtil.isEmpty(cartItems)) {
            log.error("创建订单失败：购物车为空，会员ID: {}", memberId);
            throw new ServiceException(MessageUtils.message("wholesale.cart.is.empty"));
        }
        log.debug("获取购物车商品成功，商品数量: {}", cartItems.size());

        // 3. 创建订单
        WhsOrder order = MapstructUtils.convert(bo, WhsOrder.class);
        setOrderShippingInfo(order, address);
        order.setInvoiceId(generateInvoiceId());

        // 设置销售代表信息（客户下单时自动使用客户绑定的销售代表）
        setSalespersonForOrder(order, null, memberId);

        // 标记为客户下单
        order.setIsManualOrder(Boolean.FALSE);
        // 设置基础状态
        setInitialOrderStatus(order);
        // 手动设置创建和更新字段，避免依赖MetaObjectHandler
        setOrderAuditFields(order, memberId);
        baseMapper.insert(order);
        // 获取插入后的订单ID
        Long orderId = order.getId();
        log.info("订单基本信息创建成功，订单ID: {}, 发票号: {}", orderId, order.getInvoiceId());

        // 4. 创建订单项（批量）
        List<WhsOrderItem> orderItems = createOrderItems(orderId, cartItems, cartResult.getProductMap());
        log.debug("订单项创建完成，订单ID: {}, 订单项数量: {}", orderId, orderItems.size());

        // 5. 更新订单总数量信息
        updateOrderTotals(order, orderItems);
        log.debug("更新订单总计信息，订单ID: {}, 总项数: {}, 总PCS: {}", orderId, order.getTotalItems(), order.getTotalPcs());

        // 6. 锁定库存 - 使用带订单项ID的方法
        if (!lockProductStockWithOrderItems(orderItems, orderId)) {
            log.error("创建订单失败：库存锁定失败，订单ID: {}", orderId);
            throw new ServiceException(MessageUtils.message("wholesale.product.stock.insufficient"));
        }

        // 7. 清空购物车
        cartService.clear();
        log.debug("清空购物车成功，会员ID: {}", memberId);

        // 8. 设置初始状态 - 记录状态变更
        createInitialOrderStatus(orderId, CUSTOMER_ORDER_DESC);
        log.info("订单创建完成，订单ID: {}, 会员ID: {}", orderId, memberId);

        return orderId;
    }

    /**
     * 管理员手动下单
     * 【后台】
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean manualOrder(WhsOrderCreateBo bo) {
        log.info("开始后台手动创建订单，客户ID: {}, 客户订单号: {}, 参考号: {}", bo.getCustomerId(), bo.getCustomerOrderNo(), bo.getReferenceNo());

        // 1. 校验客户地址
        WhsShippingAddressVo address = addressService.selectAddressDetailById(bo.getCustomerAddressId());
        if (address == null) {
            log.error("后台下单失败：指定的客户地址不存在，地址ID: {}", bo.getCustomerAddressId());
            throw new ServiceException("指定的客户地址不存在");
        }

        // 2. 创建订单基本信息
        WhsOrder order = new WhsOrder();
        // 设置订单号
        order.setInternalOrderNo(bo.getOrderNo());
        order.setCustomerOrderNo(bo.getCustomerOrderNo());
        order.setReferenceNo(bo.getReferenceNo());
        order.setMemberId(bo.getCustomerId());
        order.setInvoiceId(generateInvoiceId());

        // 设置销售代表信息（管理员手动下单时可以指定销售代表，如果未指定则使用客户绑定的销售代表）
        setSalespersonForOrder(order, bo.getSalespersonId(), bo.getCustomerId());

        // 标记为管理员手动创建的订单
        order.setIsManualOrder(Boolean.TRUE);

        // 设置基础状态
        setInitialOrderStatus(order);

        // 设置收货地址信息
        setOrderShippingInfo(order, address);

        // 设置折扣金额
        order.setDiscountAmount(bo.getDiscountAmount() != null ? bo.getDiscountAmount() : BigDecimal.ZERO);

        // 设置补货单标识
        order.setIsReplenishment(bo.getIsReplenishment() != null ? bo.getIsReplenishment() : Boolean.FALSE);

        baseMapper.insert(order);
        // 获取插入后的订单ID
        Long orderId = order.getId();
        log.info("后台订单基本信息创建成功，订单ID: {}, 内部订单号: {}, 客户订单号: {}, 参考号: {}", orderId, order.getInternalOrderNo(), order.getCustomerOrderNo(), order.getReferenceNo());

        // 3. 创建订单项（批量）
        List<WhsOrderItem> orderItems = createAdminOrderItems(orderId, bo.getItems());
        log.info("管理员订单项创建完成，订单ID: {}, 订单项数量: {}", orderId, orderItems.size());

        // 4. 计算订单总数量和总PCS数量（包含折扣价）
        updateOrderTotalsWithDiscount(order, orderItems, bo.getDiscountAmount());
        log.info("更新管理员订单总计信息，订单ID: {}, 总项数: {}, 总PCS: {}, 折扣金额: {}",
            orderId, order.getTotalItems(), order.getTotalPcs(), order.getDiscountAmount());

        // 5. 设置初始状态 - 记录状态变更（后台创建的订单无需校验和锁定库存）
        createInitialOrderStatus(orderId, ADMIN_MANUAL_ORDER_DESC);

        // 6. 关联文件到订单
        if (CollUtil.isNotEmpty(bo.getFiles())) {
            int fileCount = orderFileService.addOrderFiles(orderId, bo.getFiles());
            log.info("订单文件关联完成，订单ID: {}, 关联文件数: {}", orderId, fileCount);

            // 如果上传了INVOICE文件，自动设置发票状态为已发送，并触发订单状态同步更新
            if (hasInvoiceFile(bo.getFiles())) {
                // 使用统一的订单状态更新服务，确保订单状态逻辑一致
                String updateRemark = String.format("上传INVOICE文件，自动设置发票状态为已发送，INVOICE文件数: %d",
                    countInvoiceFiles(bo.getFiles()));
                orderStatusService.updateOrderStatus(orderId, null, null, null,
                    InvoiceStatus.SENT.getValue(), updateRemark);
                log.info("检测到INVOICE文件，通过统一状态服务设置发票状态为已发送，订单ID: {}, INVOICE文件数: {}",
                    orderId, countInvoiceFiles(bo.getFiles()));
            }
        }

        log.info("后台订单创建完成，订单ID: {}, 客户ID: {}", orderId, bo.getCustomerId());

        return true;
    }

    /**
     * 设置订单初始状态
     */
    private void setInitialOrderStatus(WhsOrder order) {
        order.setOrderStatus(OrderStatus.PENDING.getValue());
        order.setPaymentStatus(PaymentStatus.PENDING.getValue());
        order.setShipmentStatus(ShipmentStatus.PENDING.getValue());
        order.setInvoiceStatus(InvoiceStatus.PENDING.getValue());

        // 根据系统配置设置审批状态初始值
        if (approvalConfigService.isApprovalEnabled()) {
            // 如果启用审批功能，设置为无需审批状态（等待申请或自动申请）
            order.setShipmentApprovalStatus(ShipmentApprovalStatus.NO_APPROVAL.getValue());
            log.debug("订单审批功能已启用，设置初始审批状态为: {}", ShipmentApprovalStatus.NO_APPROVAL.getValue());
        } else {
            // 如果未启用审批功能，保持null（真正无需审批）
            order.setShipmentApprovalStatus(null);
            log.debug("订单审批功能未启用，审批状态保持为null");
        }
    }

    /**
     * 手动设置订单的审计字段，避免依赖MetaObjectHandler
     */
    private void setOrderAuditFields(WhsOrder order, Long memberId) {
        Date now = new Date();
        order.setCreateTime(now);
        order.setUpdateTime(now);
        order.setCreateBy(memberId);
        order.setUpdateBy(memberId);
        order.setCreateDept(0L); // wholesale模块统一使用部门ID为0
    }

    /**
     * 计算订单总计信息
     */
    private BigDecimal calculateOrderTotals(WhsOrder order, List<WhsOrderItem> orderItems) {
        order.setTotalItems(orderItems.size());
        order.setTotalPcs(orderItemService.calculateTotalPcs(orderItems));

        // 计算订单总金额 - 基于销售价格
        BigDecimal totalAmount = orderItems.stream()
            .map(item -> {
                BigDecimal price = item.getSalesPrice() != null ? item.getSalesPrice() :
                    (item.getPrice() != null ? item.getPrice() : BigDecimal.ZERO);
                return price.multiply(new BigDecimal(item.getQuantity()));
            })
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        order.setTotalAmount(totalAmount);
        return totalAmount;
    }

    /**
     * 更新订单总计信息
     */
    private void updateOrderTotals(WhsOrder order, List<WhsOrderItem> orderItems) {
        calculateOrderTotals(order, orderItems);
        baseMapper.updateById(order);
    }

    /**
     * 更新订单总计信息（包含折扣价）
     */
    private void updateOrderTotalsWithDiscount(WhsOrder order, List<WhsOrderItem> orderItems, BigDecimal discountAmount) {
        BigDecimal totalAmount = calculateOrderTotals(order, orderItems);

        // 验证折扣金额不能大于订单总金额
        BigDecimal finalDiscountAmount = discountAmount != null ? discountAmount : BigDecimal.ZERO;
        if (finalDiscountAmount.compareTo(totalAmount) > 0) {
            log.error("折扣金额不能大于订单总金额，订单ID: {}, 总金额: {}, 折扣金额: {}",
                order.getId(), totalAmount, finalDiscountAmount);
            throw new ServiceException("wholesale.admin.order.discount.amount.exceeds.total");
        }

        // 设置折扣金额
        order.setDiscountAmount(finalDiscountAmount);
        baseMapper.updateById(order);
    }

    /**
     * 创建订单初始状态记录
     */
    private void createInitialOrderStatus(Long orderId, String operationDesc) {
        orderStatusService.updateOrderStatus(orderId, OrderStatus.PENDING.getValue(), PaymentStatus.PENDING.getValue(), ShipmentStatus.PENDING.getValue(), InvoiceStatus.PENDING.getValue(), operationDesc);
    }

    /**
     * 创建订单项 (批量处理)
     * 【前台】
     */
    private List<WhsOrderItem> createOrderItems(Long orderId, List<CartVo> cartItems, Map<Long, WhsProduct> productMap) {
        if (CollUtil.isEmpty(cartItems)) {
            return Collections.emptyList();
        }

        // 准备数据
        OrderItemCreationData creationData = prepareOrderItemCreationData(cartItems, orderId);
        if (creationData == null) {
            return Collections.emptyList();
        }

        // 构建订单项列表
        List<WhsOrderItem> orderItems = buildOrderItemsFromCart(orderId, cartItems, productMap, creationData);

        // 批量插入订单项
        return batchInsertOrderItems(orderItems, orderId, "购物车");
    }

    /**
     * 准备订单项创建所需的数据
     */
    private OrderItemCreationData prepareOrderItemCreationData(List<CartVo> cartItems, Long orderId) {
        // 提取所有有效变体ID
        List<Long> validVariantIds = cartItems.stream().map(CartVo::getVariantId).filter(Objects::nonNull).distinct().collect(Collectors.toList());

        if (CollUtil.isEmpty(validVariantIds)) {
            log.error("购物车中没有有效的变体ID，订单ID: {}", orderId);
            return null;
        }

        Map<Long, WhsProductVariantVo> variantMap = variantService.getVariantsByIds(validVariantIds, true).stream().collect(Collectors.toMap(WhsProductVariantVo::getId, Function.identity(), (v1, v2) -> v2));

        // 获取包装关系
        Map<Long, Map<Long, Integer>> packageRelationships = packageItemService.getAllPackageRelationships(validVariantIds);

        return new OrderItemCreationData(variantMap, packageRelationships);
    }

    /**
     * 从购物车构建订单项列表
     */
    private List<WhsOrderItem> buildOrderItemsFromCart(Long orderId, List<CartVo> cartItems, Map<Long, WhsProduct> productMap, OrderItemCreationData creationData) {
        List<WhsOrderItem> orderItems = new ArrayList<>(cartItems.size());

        for (CartVo cartItem : cartItems) {
            WhsOrderItem orderItem = createOrderItemFromCart(orderId, cartItem, productMap, creationData);
            if (orderItem != null) {
                orderItems.add(orderItem);
            }
        }

        return orderItems;
    }

    /**
     * 从单个购物车项创建订单项
     */
    private WhsOrderItem createOrderItemFromCart(Long orderId, CartVo cartItem, Map<Long, WhsProduct> productMap, OrderItemCreationData creationData) {
        // 获取产品信息
        WhsProduct product = productMap.get(cartItem.getProductId());
        String productName = getProductName(cartItem.getProductId(), product, cartItem.getItemName());

        // 获取变体信息（从预查询结果）
        WhsProductVariantVo variant = null;
        if (cartItem.getVariantId() != null) {
            variant = creationData.variantMap().get(cartItem.getVariantId());
        }

        if (variant == null && cartItem.getVariantId() != null) {
            log.error("购物车商品变体未找到或已失效，无法创建订单项，变体ID: {}, 订单ID: {}", cartItem.getVariantId(), orderId);
            return null;
        }

        // 计算包装信息
        PackagingInfo packagingInfo = calculatePackagingInfo(cartItem, creationData.packageRelationships());

        // 处理规格快照和SKU编码
        String specsSnapshot = processSpecsSnapshot(cartItem.getSpecs());
        String skuCode = getOrGenerateSkuCode(cartItem, variant);

        // 创建订单项
        return buildOrderItem(orderId, cartItem.getProductId(), skuCode, cartItem.getVariantId(), cartItem.getQuantity(), productName, packagingInfo.packagingType(), packagingInfo.packagingQuantity(), specsSnapshot);
    }

    /**
     * 创建管理员订单项 (批量处理)
     * 【后台】
     */
    private List<WhsOrderItem> createAdminOrderItems(Long orderId, List<WhsOrderCreateItemBo> items) {
        if (CollUtil.isEmpty(items)) {
            return Collections.emptyList();
        }

        // 准备数据
        AdminOrderItemCreationData creationData = prepareAdminOrderItemCreationData(items, orderId);
        if (creationData == null) {
            return Collections.emptyList();
        }

        // 构建订单项列表
        List<WhsOrderItem> orderItems = buildOrderItemsFromAdmin(orderId, items, creationData);

        // 批量插入订单项
        return batchInsertOrderItems(orderItems, orderId, "管理员");
    }

    /**
     * 准备管理员订单项创建所需的数据
     */
    private AdminOrderItemCreationData prepareAdminOrderItemCreationData(List<WhsOrderCreateItemBo> items, Long orderId) {
        // 提取所有有效变体ID
        List<Long> validVariantIds = items.stream().map(WhsOrderCreateItemBo::getVariantId).filter(Objects::nonNull).distinct().collect(Collectors.toList());

        if (CollUtil.isEmpty(validVariantIds)) {
            log.error("管理员下单项中没有有效的变体ID，订单ID: {}", orderId);
            return null;
        }

        Map<Long, WhsProductVariantVo> variantMap = variantService.getVariantsByIds(validVariantIds, true).stream().collect(Collectors.toMap(WhsProductVariantVo::getId, Function.identity(), (v1, v2) -> v2));

        // 提取产品ID并去重
        List<Long> productIds = variantMap.values().stream().map(WhsProductVariantVo::getProductId).filter(Objects::nonNull).distinct().collect(Collectors.toList());

        // 批量查询产品信息
        Map<Long, WhsProductVo> productMap = new HashMap<>(Math.max(productIds.size(), DEFAULT_MAP_SIZE));
        if (CollUtil.isNotEmpty(productIds)) {
            productMap = productService.getProductsByIds(productIds).stream().collect(Collectors.toMap(WhsProductVo::getId, Function.identity(), (v1, v2) -> v2));
        }

        // 获取包装关系（与普通客户下单保持一致）
        Map<Long, Map<Long, Integer>> packageRelationships = packageItemService.getAllPackageRelationships(validVariantIds);

        return new AdminOrderItemCreationData(variantMap, packageRelationships, productMap);
    }

    /**
     * 从管理员订单项构建订单项列表
     */
    private List<WhsOrderItem> buildOrderItemsFromAdmin(Long orderId, List<WhsOrderCreateItemBo> items, AdminOrderItemCreationData creationData) {
        List<WhsOrderItem> orderItems = new ArrayList<>(items.size());

        for (WhsOrderCreateItemBo itemBo : items) {
            WhsOrderItem orderItem = createOrderItemFromAdmin(orderId, itemBo, creationData);
            if (orderItem != null) {
                orderItems.add(orderItem);
            }
        }

        return orderItems;
    }

    /**
     * 从单个管理员订单项创建订单项
     */
    private WhsOrderItem createOrderItemFromAdmin(Long orderId, WhsOrderCreateItemBo itemBo, AdminOrderItemCreationData creationData) {
        WhsProductVariantVo variant = creationData.variantMap().get(itemBo.getVariantId());
        if (variant == null) {
            log.error("商品变体未找到或已失效，无法创建订单项，变体ID: {}, 订单ID: {}", itemBo.getVariantId(), orderId);
            return null;
        }

        // 获取商品名称
        WhsProductVo productVo = variant.getProductId() != null ? creationData.productMap().get(variant.getProductId()) : null;
        String productName = getProductName(variant.getProductId(), productVo);

        // 计算包装信息
        PackagingInfo packagingInfo = calculateAdminPackagingInfo(variant, itemBo, creationData.packageRelationships());

        // 创建订单项（使用正确的包装类型和数量，并传递价格参数）
        return buildOrderItem(orderId, variant.getProductId(), variant.getSkuCode(), itemBo.getVariantId(), itemBo.getQuantity(), productName, packagingInfo.packagingType(), packagingInfo.packagingQuantity(), variant.getSpecs(), itemBo.getPurchasePrice(), itemBo.getSalesPrice());
    }

    /**
     * 构建订单项对象（不保存）
     *
     * @param orderId           订单ID
     * @param productId         产品ID
     * @param skuCode           SKU编码
     * @param variantId         变体ID
     * @param quantity          购买数量
     * @param productName       产品名称
     * @param packagingType     包装类型
     * @param packagingQuantity 包装数量
     * @param specsSnapshot     规格快照
     * @return WhsOrderItem 构建好的订单项对象，如果 quantity <= 0 则返回 null
     */
    private WhsOrderItem buildOrderItem(Long orderId, Long productId, String skuCode, Long variantId, Integer quantity, String productName, Integer packagingType, Integer packagingQuantity, String specsSnapshot) {
        return buildOrderItem(orderId, productId, skuCode, variantId, quantity, productName, packagingType, packagingQuantity, specsSnapshot, null, null);
    }

    /**
     * 构建订单项对象（不保存） - 支持价格参数的重载版本
     *
     * @param orderId           订单ID
     * @param productId         产品ID
     * @param skuCode           SKU编码
     * @param variantId         变体ID
     * @param quantity          购买数量
     * @param productName       产品名称
     * @param packagingType     包装类型
     * @param packagingQuantity 包装数量
     * @param specsSnapshot     规格快照
     * @param purchasePrice     采购单价
     * @param salesPrice        销售单价
     * @return WhsOrderItem 构建好的订单项对象，如果 quantity <= 0 则返回 null
     */
    private WhsOrderItem buildOrderItem(Long orderId, Long productId, String skuCode, Long variantId, Integer quantity, String productName, Integer packagingType, Integer packagingQuantity, String specsSnapshot, BigDecimal purchasePrice, BigDecimal salesPrice) {
        // 校验数量
        if (quantity == null || quantity <= 0) {
            log.error("订单项数量无效，跳过创建。订单ID: {}, 产品ID: {}, 变体ID: {}, 数量: {}", orderId, productId, variantId, quantity);
            return null;
        }

        // 创建订单项
        WhsOrderItem item = new WhsOrderItem();

        // 设置基本信息
        item.setOrderId(orderId);
        item.setProductId(productId);
        item.setVariantId(variantId);
        item.setQuantity(quantity);
        item.setSkuCode(StringUtils.trimToNull(skuCode));
        item.setProductName(StringUtils.trimToNull(productName));

        // 设置包装信息
        item.setPackagingType(packagingType != null ? packagingType : PackagingType.INDIVIDUAL.getValue());
        item.setPackagingQuantity(packagingQuantity != null && packagingQuantity > 0 ? packagingQuantity : 1);

        // 设置规格快照
        item.setSpecsSnapshot(processSpecsSnapshot(specsSnapshot));

        // 设置价格信息
        item.setPurchasePrice(purchasePrice);
        item.setSalesPrice(salesPrice);

        // 设置价格和金额 - 使用销售价格计算订单金额
        BigDecimal finalPrice = BigDecimal.ZERO;
        item.setPrice(finalPrice);
        // 计算金额: 销售价格 * 数量（优先使用销售价格，否则使用默认价格）
        BigDecimal calculationPrice = salesPrice != null ? salesPrice : finalPrice;
        item.setAmount(calculationPrice.multiply(new BigDecimal(quantity)));

        return item;
    }

    /**
     * 处理规格快照，确保返回有效的JSON字符串
     *
     * @param specs 原始规格数据
     * @return 规范化的JSON格式规格快照
     */
    private String processSpecsSnapshot(String specs) {
        // 若为空，返回空对象
        if (StringUtils.isBlank(specs)) {
            return EMPTY_JSON_OBJECT;
        }

        // 若已经是有效的JSON，直接返回
        if (isValidJson(specs)) {
            return specs;
        }

        // 尝试将不同格式的specs转换为JSON
        try {
            boolean hasColonSeparator = specs.contains(COLON_SEPARATOR);
            boolean hasCommaSeparator = specs.contains(COMMA_SEPARATOR);
            boolean hasSemicolonSeparator = specs.contains(SEMICOLON_SEPARATOR);
            boolean hasValidSeparators = hasCommaSeparator || hasSemicolonSeparator;

            if (hasColonSeparator && hasValidSeparators) {
                String separator = hasCommaSeparator ? COMMA_SEPARATOR : SEMICOLON_SEPARATOR;
                Map<String, String> specMap = Stream.of(specs.split(separator)).map(s -> s.split(COLON_SEPARATOR, 2)).filter(arr -> arr.length == 2).filter(arr -> StringUtils.isNotBlank(arr[0]) && StringUtils.isNotBlank(arr[1])).collect(Collectors.toMap(arr -> arr[0].trim(), arr -> arr[1].trim(), (v1, v2) -> v2));

                if (!specMap.isEmpty()) {
                    return JSONUtil.toJsonStr(specMap);
                }
            }

            // 格式2: 简单字符串，可能是单个规格描述，转为单键值对
            if (!specs.contains(LEFT_BRACE) && !specs.contains(RIGHT_BRACE)) {
                Map<String, String> singleSpec = new HashMap<>(DEFAULT_MAP_SIZE);
                singleSpec.put("description", specs.trim());
                return JSONUtil.toJsonStr(singleSpec);
            }

            // 对于其他无法识别的格式，记录为非结构化数据
            log.warn("无法识别的规格格式，记录为原始数据: {}", specs);
            Map<String, String> rawData = new HashMap<>(DEFAULT_MAP_SIZE);
            rawData.put("raw", specs);
            return JSONUtil.toJsonStr(rawData);
        } catch (Exception e) {
            // 转换失败，记录错误并返回安全的空对象
            log.error("规格快照转换失败: {}, 错误: {}", specs, e.getMessage());
            return EMPTY_JSON_OBJECT;
        }
    }

    /**
     * 判断字符串是否为有效的JSON
     */
    private boolean isValidJson(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }

        try {
            // 先检查是否是JSON类型
            if (!JSONUtil.isTypeJSON(str)) {
                return false;
            }

            // 额外验证：确保是对象格式 {...}，不是数组或简单值
            String trimmed = str.trim();
            return trimmed.startsWith("{") && trimmed.endsWith("}");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取或生成SKU编码
     *
     * @param cartItem 购物车项
     * @param variant  预查询的变体信息 (可选)
     * @return SKU编码
     */
    private String getOrGenerateSkuCode(CartVo cartItem, WhsProductVariantVo variant) {
        // 1. 优先使用购物车中的 SKU 码 (如果非空)
        if (StringUtils.isNotBlank(cartItem.getSkuCode())) {
            return cartItem.getSkuCode();
        }

        // 2. 如果传入了变体信息，且变体有 SKU 码
        if (variant != null && StringUtils.isNotBlank(variant.getSkuCode())) {
            return variant.getSkuCode();
        }

        // 3. 如果购物车项没有变体ID，无法生成，返回 null 或空字符串
        if (cartItem.getVariantId() == null) {
            log.error("购物车项缺少变体ID，无法生成SKU。产品ID: {}", cartItem.getProductId());
            return null;
        }

        // 4. 尝试从数据库获取变体 SKU (作为最后手段，理论上应在预查询中获取)
        // 通常不应执行到这里，如果执行到说明预查询逻辑有误或数据不一致
        if (variant == null) {
            log.warn("预查询未找到变体信息，尝试再次查询 SKU。变体ID: {}", cartItem.getVariantId());
            WhsProductVariantVo dbVariant = variantService.getVariant(cartItem.getVariantId(), false);
            if (dbVariant != null && StringUtils.isNotBlank(dbVariant.getSkuCode())) {
                return dbVariant.getSkuCode();
            }
        }

        // 5. 生成默认 SKU 格式
        log.warn("无法获取现有SKU，生成默认SKU。产品ID: {}, 变体ID: {}", cartItem.getProductId(), cartItem.getVariantId());
        return String.format("SKU-%d-%d", cartItem.getProductId(), cartItem.getVariantId());
    }

    /**
     * 锁定产品库存（关联订单项）
     *
     * @param orderItems 订单项列表 (必须包含有效的ID，即批量插入后返回的列表)
     * @param orderId    订单ID
     * @return 是否全部锁定成功
     */
    private boolean lockProductStockWithOrderItems(List<WhsOrderItem> orderItems, Long orderId) {
        if (CollUtil.isEmpty(orderItems)) {
            log.debug("订单项为空，无需锁定库存，订单ID: {}", orderId);
            return true;
        }

        // 过滤有效的订单项（必须有ID和变体ID）
        List<WhsOrderItem> validItems = orderItems.stream().filter(item -> item.getId() != null).filter(item -> item.getVariantId() != null).collect(Collectors.toList());

        if (validItems.isEmpty()) {
            log.error("有效订单项为空，无法锁定库存，订单ID: {}", orderId);
            throw new ServiceException("No valid order items available for stock locking");
        }

        if (validItems.size() < orderItems.size()) {
            log.error("部分订单项无效，将被跳过库存锁定，订单ID: {}, 有效项: {}, 总项: {}", orderId, validItems.size(), orderItems.size());
        }

        log.info("开始锁定订单库存，订单ID: {}, 有效商品项数量: {}", orderId, validItems.size());

        // 使用库存分配服务锁定库存
        try {
            boolean result = stockAllocationService.lockStockForOrderItems(validItems, orderId);
            if (result) {
                log.info("库存锁定成功，订单ID: {}", orderId);
            } else {
                log.error("库存锁定失败，订单ID: {}", orderId);
            }
            return result;
        } catch (Exception e) {
            log.error("锁定库存过程中发生异常，订单ID: {}，错误: {}", orderId, e.getMessage(), e);
            // 如果是我们自己抛出的异常，直接向上传递
            if (e instanceof ServiceException) {
                throw (ServiceException) e;
            }
            // 其他异常使用国际化消息
            throw new ServiceException(MessageUtils.message("wholesale.product.stock.insufficient"));
        }
    }

    /**
     * 生成发票号
     */
    private Long generateInvoiceId() {
        return invoiceIdGenerator.nextInvoiceId();
    }

    /**
     * 设置订单的收货地址信息
     */
    private <T> void setOrderShippingInfo(WhsOrder order, T address) {
        // 统一转换为ShippingAddressVo处理
        ShippingAddressVo addressVo;

        if (address instanceof ShippingAddressVo) {
            addressVo = (ShippingAddressVo) address;
        } else if (address instanceof WhsShippingAddressVo) {
            // 使用MapStruct转换后台地址对象为前台地址对象
            addressVo = MapstructUtils.convert(address, ShippingAddressVo.class);
        } else {
            log.error("无法识别的地址类型: {}", address != null ? address.getClass().getName() : "null");
            return;
        }

        // 统一设置地址信息
        order.setShippingName(addressVo.getLastName() + " " + addressVo.getFirstName());
        order.setShippingPhone(addressVo.getPhone());
        order.setShippingEmail(addressVo.getEmail());
        order.setShippingCountry(addressVo.getCountry());
        order.setShippingState(addressVo.getState());
        order.setShippingCity(addressVo.getCity());
        order.setShippingAddress(addressVo.getAddressLine1());
        order.setShippingAddress2(addressVo.getAddressLine2());
        order.setShippingZip(addressVo.getZipCode());
        order.setShippingCompanyName(addressVo.getCompanyName());
    }

    /**
     * 获取产品名称（重载方法 - 产品对象为WhsProduct）
     *
     * @param productId    产品ID
     * @param product      预查询的产品对象
     * @param fallbackName 备选名称（可为null）
     * @return 产品名称，如果都没有则返回 "Unknown Product"
     */
    private String getProductName(Long productId, WhsProduct product, String fallbackName) {
        // 1. 优先使用传入的 product 对象
        if (product != null && StringUtils.isNotBlank(product.getItemName())) {
            return product.getItemName();
        }

        return getProductNameInternal(productId, fallbackName);
    }

    /**
     * 获取产品名称（重载方法 - 产品对象为WhsProductVo）
     *
     * @param productId 产品ID
     * @param productVo 预查询的产品VO对象
     * @return 产品名称，如果都没有则返回 "Unknown Product"
     */
    private String getProductName(Long productId, WhsProductVo productVo) {
        // 1. 优先使用传入的 productVo 对象
        if (productVo != null && StringUtils.isNotBlank(productVo.getItemName())) {
            return productVo.getItemName();
        }

        return getProductNameInternal(productId, "");
    }

    /**
     * 获取产品名称（内部通用方法）
     *
     * @param productId    产品ID
     * @param fallbackName 备选名称（可为null）
     * @return 产品名称，如果都没有则返回 "Unknown Product"
     */
    private String getProductNameInternal(Long productId, String fallbackName) {
        // 1. 如果有 productId，尝试查询数据库 (作为后备)
        if (productId != null) {
            log.warn("预查询未找到产品信息，尝试再次查询产品名称。产品ID: {}", productId);
            WhsProductVo productVo = productService.getProductInfo(productId);
            if (productVo != null && StringUtils.isNotBlank(productVo.getItemName())) {
                return productVo.getItemName();
            }
        }

        // 2. 使用备选名称
        if (StringUtils.isNotBlank(fallbackName)) {
            return fallbackName;
        }

        // 3. 返回默认值
        log.error("无法确定产品名称。产品ID: {}, 备选名称: {}", productId, fallbackName);
        return "Unknown Product";
    }

    /**
     * 校验收货地址并设置订单基本信息
     */
    private ShippingAddressVo validateAddressAndPrepareOrder(OrderBo bo, Long memberId) {
        ShippingAddressVo address = addressService.selectAddressById(bo.getAddressId());
        if (address == null) {
            throw new ServiceException(MessageUtils.message("wholesale.address.not.exists"));
        }

        bo.setMemberId(memberId);
        bo.setInvoiceId(generateInvoiceId());
        bo.setOrderStatus(OrderStatus.PENDING.getValue());
        bo.setPaymentStatus(PaymentStatus.PENDING.getValue());
        bo.setInvoiceStatus(InvoiceStatus.PENDING.getValue());

        return address;
    }

    /**
     * 计算包装信息
     */
    private PackagingInfo calculatePackagingInfo(CartVo cartItem, Map<Long, Map<Long, Integer>> packageRelationships) {
        Integer packagingType = cartItem.getPackagingType() != null ? cartItem.getPackagingType() : PackagingType.INDIVIDUAL.getValue();
        Long variantId = cartItem.getVariantId();
        return calculatePackagingInfoInternal(packagingType, variantId, packageRelationships);
    }

    /**
     * 批量插入订单项
     */
    private List<WhsOrderItem> batchInsertOrderItems(List<WhsOrderItem> orderItems, Long orderId, String orderType) {
        if (CollUtil.isEmpty(orderItems)) {
            return orderItems;
        }

        try {
            boolean insertResult = itemMapper.insertBatch(orderItems);
            if (!insertResult) {
                log.error("{}订单项批量插入失败，订单ID: {}", orderType, orderId);
                throw new ServiceException("Failed to create " + orderType.toLowerCase() + " order items.");
            }

            // 确认所有项都有ID
            if (orderItems.stream().anyMatch(item -> item.getId() == null)) {
                log.error("批量插入后{}订单项缺少ID，订单ID: {}", orderType, orderId);
                throw new ServiceException(orderType + " order item IDs not generated properly after batch insert.");
            }

            log.info("{}订单项批量插入成功，订单ID: {}, 数量: {}", orderType, orderId, orderItems.size());
        } catch (Exception e) {
            log.error("{}订单项批量插入异常，订单ID: {}, 错误: {}", orderType, orderId, e.getMessage(), e);
            throw new ServiceException("Error creating " + orderType.toLowerCase() + " order items: " + e.getMessage());
        }

        return orderItems;
    }

    /**
     * 订单项创建数据封装类
     */
    private record OrderItemCreationData(Map<Long, WhsProductVariantVo> variantMap,
                                         Map<Long, Map<Long, Integer>> packageRelationships) {

    }

    /**
     * 包装信息封装类
     */
    private record PackagingInfo(Integer packagingType, Integer packagingQuantity) {

    }

    /**
     * 管理员订单项创建数据封装类
     */
    private record AdminOrderItemCreationData(Map<Long, WhsProductVariantVo> variantMap,
                                              Map<Long, Map<Long, Integer>> packageRelationships,
                                              Map<Long, WhsProductVo> productMap) {
    }

    /**
     * 计算管理员订单的包装信息
     */
    private PackagingInfo calculateAdminPackagingInfo(WhsProductVariantVo variant, WhsOrderCreateItemBo itemBo, Map<Long, Map<Long, Integer>> packageRelationships) {
        Integer packagingType = variant.getPackagingType() != null ? variant.getPackagingType() : PackagingType.INDIVIDUAL.getValue();
        Long variantId = itemBo.getVariantId();
        return calculatePackagingInfoInternal(packagingType, variantId, packageRelationships);
    }

    /**
     * 计算包装信息的通用内部方法
     */
    private PackagingInfo calculatePackagingInfoInternal(Integer packagingType, Long variantId, Map<Long, Map<Long, Integer>> packageRelationships) {
        Integer packagingQuantity = 1;

        boolean isNonIndividualPackaging = !packagingType.equals(PackagingType.INDIVIDUAL.getValue());
        boolean hasVariantId = variantId != null;
        boolean hasPackageRelation = packageRelationships.containsKey(variantId);

        if (isNonIndividualPackaging && hasVariantId && hasPackageRelation) {
            Map<Long, Integer> childQuantities = packageRelationships.get(variantId);
            if (childQuantities != null && !childQuantities.isEmpty()) {
                // 假设包装只包含一种子项
                packagingQuantity = childQuantities.values().stream().findFirst().orElse(1);
            }
        }

        return new PackagingInfo(packagingType, packagingQuantity);
    }

    /**
     * 编辑草稿状态订单
     * 【后台】
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editDraftOrder(Long orderId, WhsOrderCreateBo bo) {
        log.info("开始编辑草稿状态订单，订单ID: {}, 客户ID: {}, 客户订单号: {}", orderId, bo.getCustomerId(), bo.getCustomerOrderNo());

        // 1. 验证订单是否存在且为草稿状态
        WhsOrder existingOrder = validateDraftOrder(orderId);

        // 2. 校验客户地址
        WhsShippingAddressVo address = addressService.selectAddressDetailById(bo.getCustomerAddressId());
        if (address == null) {
            log.error("编辑订单失败：指定的客户地址不存在，地址ID: {}", bo.getCustomerAddressId());
            throw new ServiceException("指定的客户地址不存在");
        }

        log.info("客户地址校验通过，地址ID: {}", bo.getCustomerAddressId());

        // 3. 删除现有订单项
        deleteExistingOrderItems(orderId);

        // 4. 删除现有发货方案及相关数据（确保数据一致性）
        deleteExistingShipmentPlans(orderId);

        // 5. 更新订单基本信息
        updateOrderBasicInfo(existingOrder, bo, address);

        // 6. 创建新的订单项
        List<WhsOrderItem> orderItems = createAdminOrderItems(orderId, bo.getItems());
        log.info("编辑订单项创建完成，订单ID: {}, 订单项数量: {}", orderId, orderItems.size());

        // 7. 更新订单总计信息
        updateOrderTotals(existingOrder, orderItems);
        log.info("更新编辑订单总计信息，订单ID: {}, 总项数: {}, 总PCS: {}", orderId, existingOrder.getTotalItems(), existingOrder.getTotalPcs());

        // 8. 记录订单编辑日志（设置为待处理状态，与新创建订单一致）
        orderStatusService.updateOrderStatus(orderId, OrderStatus.PENDING.getValue(), PaymentStatus.PENDING.getValue(), ShipmentStatus.PENDING.getValue(), InvoiceStatus.PENDING.getValue(), "管理员编辑草稿状态订单");

        // 更新文件关联（先清空现有关联，再添加新的）
        if (CollUtil.isNotEmpty(bo.getFiles())) {
            orderFileService.clearOrderFiles(orderId);
            int fileCount = orderFileService.addOrderFiles(orderId, bo.getFiles());
            log.info("订单文件关联更新完成，订单ID: {}, 关联文件数: {}", orderId, fileCount);

            // 如果编辑时上传了INVOICE文件，自动设置发票状态为已发送，并触发订单状态同步更新
            if (hasInvoiceFile(bo.getFiles())) {
                // 使用统一的订单状态更新服务，确保订单状态逻辑一致
                String updateRemark = String.format("编辑草稿订单时上传INVOICE文件，自动设置发票状态为已发送，INVOICE文件数: %d",
                    countInvoiceFiles(bo.getFiles()));
                orderStatusService.updateOrderStatus(orderId, null, null, null,
                    InvoiceStatus.SENT.getValue(), updateRemark);
                log.info("编辑草稿订单时检测到INVOICE文件，通过统一状态服务设置发票状态为已发送，订单ID: {}, INVOICE文件数: {}",
                    orderId, countInvoiceFiles(bo.getFiles()));
            }
        }

        log.info("草稿状态订单编辑完成，订单ID: {}, 客户ID: {}, 状态已重置为待处理", orderId, bo.getCustomerId());

        return true;
    }

    /**
     * 验证订单是否存在且为草稿状态
     */
    private WhsOrder validateDraftOrder(Long orderId) {
        WhsOrder order = baseMapper.selectById(orderId);
        if (order == null) {
            log.error("编辑订单失败：订单不存在，订单ID: {}", orderId);
            throw new ServiceException("订单不存在");
        }

        if (!OrderStatus.DRAFT.getValue().equals(order.getOrderStatus())) {
            log.error("编辑订单失败：只能编辑草稿状态的订单，当前状态: {}, 订单ID: {}", order.getOrderStatus(), orderId);
            throw new ServiceException("只能编辑草稿状态的订单");
        }

        return order;
    }

    /**
     * 删除现有订单项
     */
    private void deleteExistingOrderItems(Long orderId) {
        try {
            // 删除现有订单项
            orderItemService.deleteByOrderId(orderId);
            log.info("删除现有订单项成功，订单ID: {}", orderId);
        } catch (Exception e) {
            log.error("删除现有订单项失败，订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException("删除现有订单项失败");
        }
    }

    /**
     * 删除现有发货方案及相关数据
     * 确保订单编辑后数据一致性，避免发货异常
     */
    private void deleteExistingShipmentPlans(Long orderId) {
        try {
            // 删除发货方案及相关数据（发货方案、发货方案项、发货转换记录）
            shipmentPlanService.deleteAllShipmentPlansByOrderId(orderId);
            log.info("删除现有发货方案成功，订单ID: {}", orderId);
        } catch (Exception e) {
            log.error("删除现有发货方案失败，订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException("删除现有发货方案失败");
        }
    }

    /**
     * 更新订单基本信息
     */
    private void updateOrderBasicInfo(WhsOrder order, WhsOrderCreateBo bo, WhsShippingAddressVo address) {
        // 更新订单号和客户信息
        order.setInternalOrderNo(bo.getOrderNo());
        order.setCustomerOrderNo(bo.getCustomerOrderNo());
        order.setReferenceNo(bo.getReferenceNo());
        order.setMemberId(bo.getCustomerId());

        // 设置销售代表信息（编辑订单时可以重新指定销售代表）
        setSalespersonForOrder(order, bo.getSalespersonId(), bo.getCustomerId());

        // 更新收货地址信息
        setOrderShippingInfo(order, address);

        // 设置补货单标识
        order.setIsReplenishment(bo.getIsReplenishment() != null ? bo.getIsReplenishment() : Boolean.FALSE);

        // 注意：不在这里更新订单状态，状态更新统一在 updateOrderStatus 方法中处理
        // 这样可以确保事件发布逻辑正确执行

        // 保存订单基本信息
        baseMapper.updateById(order);
        log.info("更新订单基础信息成功，订单ID: {}, 客户ID: {}", order.getId(), bo.getCustomerId());
    }

    /**
     * 为订单设置销售代表信息
     *
     * @param order                  订单对象
     * @param specifiedSalespersonId 指定的销售代表ID（可为null）
     * @param customerId             客户ID
     */
    private void setSalespersonForOrder(WhsOrder order, Long specifiedSalespersonId, Long customerId) {
        Long salespersonId = null;

        try {
            // 如果指定了销售代表ID，直接使用
            if (specifiedSalespersonId != null) {
                salespersonId = specifiedSalespersonId;
                log.info("订单使用指定的销售代表，销售代表ID: {}", specifiedSalespersonId);
            } else {
                // 否则从客户信息中获取绑定的销售代表
                try {
                    var memberDetail = memberService.getMemberDetail(customerId);
                    if (memberDetail != null && memberDetail.getSalespersonId() != null) {
                        salespersonId = memberDetail.getSalespersonId();
                        log.info("订单使用客户绑定的销售代表，客户ID: {}, 销售代表ID: {}", customerId, salespersonId);
                    } else {
                        log.info("客户未绑定销售代表，客户ID: {}", customerId);
                    }
                } catch (Exception e) {
                    log.error("获取客户销售代表信息失败，客户ID: {}, 错误: {}", customerId, e.getMessage(), e);
                    // 获取客户销售代表信息失败，不影响订单创建，继续处理
                }
            }

            order.setSalespersonId(salespersonId);

        } catch (Exception e) {
            log.error("设置订单销售代表信息时发生错误，客户ID: {}, 指定销售代表ID: {}, 错误: {}",
                customerId, specifiedSalespersonId, e.getMessage(), e);
            // 发生异常时设置为null，确保订单创建不受影响
            order.setSalespersonId(null);
        }
    }

    /**
     * 检查文件列表中是否包含INVOICE文件
     *
     * @param files 文件列表
     * @return 是否包含INVOICE文件
     */
    private boolean hasInvoiceFile(List<WhsOrderFileCreateBo> files) {
        if (CollUtil.isEmpty(files)) {
            return false;
        }
        return files.stream()
            .anyMatch(file -> OrderFileType.INVOICE.getCode().equals(file.getFileType()));
    }

    /**
     * 统计INVOICE文件数量
     *
     * @param files 文件列表
     * @return INVOICE文件数量
     */
    private long countInvoiceFiles(List<WhsOrderFileCreateBo> files) {
        if (CollUtil.isEmpty(files)) {
            return 0;
        }
        return files.stream()
            .filter(file -> OrderFileType.INVOICE.getCode().equals(file.getFileType()))
            .count();
    }
}
