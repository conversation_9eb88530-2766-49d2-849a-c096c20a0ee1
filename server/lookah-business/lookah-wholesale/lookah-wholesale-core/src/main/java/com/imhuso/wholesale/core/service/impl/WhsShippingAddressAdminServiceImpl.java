package com.imhuso.wholesale.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.satoken.utils.LoginHelper;
import com.imhuso.wholesale.core.domain.WhsShippingAddress;
import com.imhuso.wholesale.core.domain.bo.admin.WhsShippingAddressBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShippingAddressVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsCountryVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsStateVo;
import com.imhuso.wholesale.core.mapper.WhsShippingAddressMapper;
import com.imhuso.wholesale.core.service.IWhsCountryService;
import com.imhuso.wholesale.core.service.IWhsShippingAddressAdminService;
import com.imhuso.wholesale.core.service.IWhsStateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 会员地址管理Service实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsShippingAddressAdminServiceImpl implements IWhsShippingAddressAdminService {

    private final WhsShippingAddressMapper baseMapper;
    private final IWhsCountryService countryService;
    private final IWhsStateService stateService;

    @Override
    public TableDataInfo<WhsShippingAddressVo> queryPageList(WhsShippingAddressBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WhsShippingAddress> lqw = buildQueryWrapper(bo);
        Page<WhsShippingAddressVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw, WhsShippingAddressVo.class);
        return TableDataInfo.build(result);
    }

    @Override
    public WhsShippingAddressVo getAddressDetail(Long addressId) {
        return baseMapper.selectVoById(addressId, WhsShippingAddressVo.class);
    }

    @Override
    public List<WhsShippingAddressVo> getAddressByMemberId(Long memberId) {
        LambdaQueryWrapper<WhsShippingAddress> lqw = Wrappers.lambdaQuery(WhsShippingAddress.class)
                .eq(WhsShippingAddress::getMemberId, memberId)
                .orderByDesc(WhsShippingAddress::getIsDefault)
                .orderByDesc(WhsShippingAddress::getCreateTime);
        return baseMapper.selectVoList(lqw, WhsShippingAddressVo.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertAddress(WhsShippingAddressBo bo) {
        WhsShippingAddress address = MapstructUtils.convert(bo, WhsShippingAddress.class);
        
        // 一次性验证并设置地区信息，避免重复查询
        validateAndSetRegionInfo(address);
        
        // 处理默认地址
        if (BusinessConstants.YES.equals(bo.getIsDefault())) {
            handleDefaultAddress(bo.getMemberId(), null);
        }
        
        return baseMapper.insert(address);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateAddress(WhsShippingAddressBo bo) {
        // 验证地址是否存在
        WhsShippingAddress existingAddress = baseMapper.selectById(bo.getId());
        if (existingAddress == null) {
            throw new ServiceException("地址不存在");
        }
        
        WhsShippingAddress address = MapstructUtils.convert(bo, WhsShippingAddress.class);
        
        // 一次性验证并设置地区信息，避免重复查询
        validateAndSetRegionInfo(address);
        
        // 处理默认地址
        if (BusinessConstants.YES.equals(bo.getIsDefault())) {
            handleDefaultAddress(existingAddress.getMemberId(), bo.getId());
        }
        
        return baseMapper.updateById(address);
    }

    @Override
    public int deleteAddress(Long addressId) {
        // 验证地址是否存在
        WhsShippingAddress address = baseMapper.selectById(addressId);
        if (address == null) {
            throw new ServiceException("地址不存在");
        }
        
        // 如果是默认地址，不允许删除
        if (BusinessConstants.YES.equals(address.getIsDefault())) {
            throw new ServiceException("默认地址不能删除");
        }
        
        return baseMapper.deleteById(addressId);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<WhsShippingAddress> buildQueryWrapper(WhsShippingAddressBo bo) {
        LambdaQueryWrapper<WhsShippingAddress> lqw = Wrappers.lambdaQuery(WhsShippingAddress.class);
        
        // 多字段模糊搜索条件
        addSearchConditions(lqw, bo.getSearchKeyword());
        
        // 精确查询条件
        addExactConditions(lqw, bo);
        
        // 排序条件
        addSortConditions(lqw);
        
        return lqw;
    }
    
    /**
     * 添加多字段模糊搜索条件
     */
    private void addSearchConditions(LambdaQueryWrapper<WhsShippingAddress> lqw, String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return;
        }
        
        // 安全检查：限制搜索关键字长度，防止恶意攻击
        if (keyword.length() > 100) {
            throw new ServiceException("搜索关键字长度不能超过100个字符");
        }
        
        // 额外安全检查：防止潜在的SQL注入模式
        if (keyword.matches(".*[';\"\\\\].*")) {
            log.warn("Potentially malicious search keyword blocked: {}", keyword);
            throw new ServiceException("搜索关键字包含非法字符");
        }
        
        String trimmedKeyword = keyword.trim();
        if (StringUtils.isBlank(trimmedKeyword)) {
            return;
        }
        
        lqw.and(wrapper -> {
            wrapper.like(WhsShippingAddress::getFirstName, trimmedKeyword)
                   .or().like(WhsShippingAddress::getLastName, trimmedKeyword)
                   .or().like(WhsShippingAddress::getPhone, trimmedKeyword)
                   .or().like(WhsShippingAddress::getEmail, trimmedKeyword)
                   .or().like(WhsShippingAddress::getCompanyName, trimmedKeyword)
                   .or().like(WhsShippingAddress::getAddressLine1, trimmedKeyword)
                   .or().like(WhsShippingAddress::getCity, trimmedKeyword);
        });
    }
    
    /**
     * 添加精确查询条件
     */
    private void addExactConditions(LambdaQueryWrapper<WhsShippingAddress> lqw, WhsShippingAddressBo bo) {
        lqw.eq(bo.getMemberId() != null, WhsShippingAddress::getMemberId, bo.getMemberId())
           .eq(StringUtils.isNotBlank(bo.getFirstName()), WhsShippingAddress::getFirstName, bo.getFirstName())
           .eq(StringUtils.isNotBlank(bo.getLastName()), WhsShippingAddress::getLastName, bo.getLastName())
           .eq(StringUtils.isNotBlank(bo.getCountry()), WhsShippingAddress::getCountry, bo.getCountry())
           .eq(StringUtils.isNotBlank(bo.getState()), WhsShippingAddress::getState, bo.getState())
           .eq(StringUtils.isNotBlank(bo.getCity()), WhsShippingAddress::getCity, bo.getCity())
           .eq(StringUtils.isNotBlank(bo.getIsDefault()), WhsShippingAddress::getIsDefault, bo.getIsDefault());
    }
    
    /**
     * 添加排序条件
     */
    private void addSortConditions(LambdaQueryWrapper<WhsShippingAddress> lqw) {
        lqw.orderByDesc(WhsShippingAddress::getIsDefault)
           .orderByDesc(WhsShippingAddress::getCreateTime);
    }
    
    /**
     * 一次性验证并设置地区信息（优化性能，避免重复查询）
     */
    private void validateAndSetRegionInfo(WhsShippingAddress address) {
        String countryCode = address.getCountry();
        String stateCode = address.getState();
        
        // 校验国家代码并获取国家信息
        WhsCountryVo country = countryService.getCountryByCode(countryCode, WhsCountryVo.class);
        if (country == null) {
            throw new ServiceException("无效的国家代码: " + countryCode);
        }
        
        // 设置国家名称
        address.setCountryName(country.getName());
        
        // 获取该国家下的所有州/省信息
        List<WhsStateVo> states = stateService.getStatesByCountryId(country.getId());
        
        // 校验并设置州/省信息
        WhsStateVo matchedState = states.stream()
                .filter(s -> s.getCode().equals(stateCode))
                .findFirst()
                .orElse(null);
                
        if (matchedState == null) {
            throw new ServiceException("无效的州/省代码: " + stateCode);
        }
        
        // 设置州名称
        address.setStateName(matchedState.getName());
        
        log.debug("Region info validated and set - Country: {}({}), State: {}({})", 
                country.getName(), countryCode, matchedState.getName(), stateCode);
    }
    
    /**
     * 处理默认地址
     */
    private void handleDefaultAddress(Long memberId, Long currentAddressId) {
        // 将该会员的其他地址设置为非默认
        LambdaQueryWrapper<WhsShippingAddress> updateWrapper = Wrappers.lambdaQuery(WhsShippingAddress.class)
                .eq(WhsShippingAddress::getMemberId, memberId)
                .eq(WhsShippingAddress::getIsDefault, BusinessConstants.YES);
        
        if (currentAddressId != null) {
            updateWrapper.ne(WhsShippingAddress::getId, currentAddressId);
        }
        
        WhsShippingAddress updateEntity = new WhsShippingAddress();
        updateEntity.setIsDefault(BusinessConstants.NO);
        baseMapper.update(updateEntity, updateWrapper);
    }
}