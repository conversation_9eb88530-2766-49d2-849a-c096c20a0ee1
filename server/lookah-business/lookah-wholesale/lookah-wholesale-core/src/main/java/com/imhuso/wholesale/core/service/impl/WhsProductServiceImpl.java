package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductAttributeVo;
import com.imhuso.wholesale.core.domain.WhsProduct;
import com.imhuso.wholesale.core.domain.WhsProductAttributeRelation;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVariantVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVo;
import com.imhuso.wholesale.core.service.IWhsProductService;
import com.imhuso.wholesale.core.enums.PackagingType;
import com.imhuso.wholesale.core.event.StockChangeEvent;
import com.imhuso.wholesale.core.mapper.WhsProductMapper;
import com.imhuso.wholesale.core.mapper.WhsProductVariantMapper;
import com.imhuso.wholesale.core.service.*;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 批发产品Service实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsProductServiceImpl implements IWhsProductService {

    private final WhsProductMapper productMapper;
    private final WhsProductVariantMapper variantMapper;
    private final IWhsProductCategoryService categoryService;
    private final IWhsProductVariantService variantService;
    private final IWhsProductSeriesService seriesService;
    private final IWhsProductAttributeRelationService attributeRelationService;
    private final IWhsProductAttributeService attributeService;
    private final IWhsStockService stockService;
    private final ApplicationEventPublisher applicationEventPublisher;

    @Override
    public List<WhsProductVo> queryList(WhsProductBo bo) {
        LambdaQueryWrapper<WhsProduct> lqw = buildQueryWrapper(bo);
        List<WhsProductVo> list = productMapper.selectVoList(lqw);
        if (CollUtil.isNotEmpty(list)) {
            batchFillData(list, false);
        }
        return list;
    }

    @Override
    public TableDataInfo<WhsProductVo> queryPage(WhsProductBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WhsProduct> lqw = buildQueryWrapper(bo);
        Page<WhsProductVo> page = productMapper.selectVoPage(pageQuery.build(), lqw);
        if (CollUtil.isNotEmpty(page.getRecords())) {
            batchFillData(page.getRecords(), false);
        }
        return TableDataInfo.build(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertProduct(WhsProductBo bo) {
        // 1. 转换并插入产品
        WhsProduct insert = BeanUtil.toBean(bo, WhsProduct.class);
        int rows = productMapper.insert(insert);

        // 2. 处理关联数据
        Long productId = insert.getId();
        processRelatedData(bo, productId);

        return rows;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateProduct(WhsProductBo bo) {
        return Optional.ofNullable(productMapper.selectById(bo.getId())).map(product -> {
            // 1. 转换并更新基本信息
            WhsProduct productToUpdate = BeanUtil.toBean(bo, WhsProduct.class);
            int rows = productMapper.updateById(productToUpdate);

            // 2. 处理关联数据
            processRelatedData(bo, bo.getId());

            // 3. 发布产品更新事件，通知相关缓存进行刷新
            publishProductUpdateEvent(Collections.singletonList(bo.getId()));

            return rows;
        }).orElse(0);
    }

    @Override
    public WhsProductVo getProductInfo(@NotNull(message = "主键不能为空") Long id) {
        WhsProductVo productVo = productMapper.selectVoById(id);
        if (productVo != null) {
            fillProductData(productVo);
        }
        return productVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteProductById(@NotEmpty(message = "主键不能为空") Long id) {
        // 检查是否存在包装变体，如果存在则不能删除
        LambdaQueryWrapper<WhsProductVariant> packageQuery = new LambdaQueryWrapper<>();
        packageQuery.eq(WhsProductVariant::getProductId, id).ne(WhsProductVariant::getPackagingType,
            PackagingType.INDIVIDUAL.getValue());
        if (variantMapper.exists(packageQuery)) {
            throw new ServiceException("产品存在包装变体，不能删除");
        }

        // 检查是否存在任何变体，如果存在则不能删除
        LambdaQueryWrapper<WhsProductVariant> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhsProductVariant::getProductId, id);
        if (variantMapper.exists(queryWrapper)) {
            throw new ServiceException("产品存在变体，不能删除");
        }

        // 删除产品及相关数据
        int rows = productMapper.deleteById(id);

        // 发布产品更新事件
        if (rows > 0) {
            publishProductUpdateEvent(Collections.singletonList(id));
        }

        return rows;
    }

    /**
     * 更新产品状态
     *
     * @param id     产品ID
     * @param status 状态值
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateStatus(Long id, String status) {
        // 更新产品状态
        WhsProduct product = new WhsProduct();
        product.setId(id);
        product.setStatus(status);

        int rows = productMapper.updateById(product);

        // 如果产品状态更新成功，同步更新关联的产品变体状态
        if (rows > 0) {
            WhsProductVariant variantUpdate = new WhsProductVariant();
            variantUpdate.setStatus(status);

            LambdaQueryWrapper<WhsProductVariant> lqw = new LambdaQueryWrapper<>();
            lqw.eq(WhsProductVariant::getProductId, id);

            variantMapper.update(variantUpdate, lqw);

            // 发布产品更新事件
            publishProductUpdateEvent(Collections.singletonList(id));
        }

        return rows;
    }

    @Override
    public List<WhsProductAttributeVo> getProductAttributesByProductId(
        Long productId) {
        if (productId == null) {
            return Collections.emptyList();
        }
        return getProductAttributes(Collections.singletonList(productId), true).getOrDefault(productId,
            Collections.emptyList());
    }

    /**
     * 填充单个产品数据
     */
    private void fillProductData(WhsProductVo product) {
        if (product == null) {
            return;
        }
        batchFillData(Collections.singletonList(product), true);
    }

    /**
     * 批量查询产品属性
     *
     * @param productIds 产品ID列表
     * @param fillValues 是否填充属性值
     * @return 产品ID到属性列表的映射
     */
    private Map<Long, List<WhsProductAttributeVo>> getProductAttributes(
        List<Long> productIds, boolean fillValues) {
        if (CollUtil.isEmpty(productIds)) {
            return Collections.emptyMap();
        }

        // 获取产品的属性关联
        List<WhsProductAttributeRelation> attributeRelations = attributeRelationService
            .getRelationsByProductIds(productIds);
        if (CollUtil.isEmpty(attributeRelations)) {
            return Collections.emptyMap();
        }

        // 获取属性详细信息
        Set<Long> attributeIds = attributeRelations.stream().map(WhsProductAttributeRelation::getAttributeId)
            .collect(Collectors.toSet());
        Map<Long, WhsProductAttributeVo> attributeVoMap = attributeService
            .getAttributeMap(new ArrayList<>(attributeIds), fillValues);

        // 按产品ID分组并转换为属性列表
        return attributeRelations.stream()
            .collect(Collectors.groupingBy(WhsProductAttributeRelation::getProductId,
                Collectors.mapping(relation -> attributeVoMap.get(relation.getAttributeId()),
                    Collectors.filtering(Objects::nonNull, Collectors.toList()))));
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<WhsProduct> buildQueryWrapper(WhsProductBo bo) {
        LambdaQueryWrapper<WhsProduct> lqw = new LambdaQueryWrapper<>();

        // 添加基础查询条件
        lqw.eq(bo.getId() != null, WhsProduct::getId, bo.getId());
        lqw.eq(bo.getCategoryId() != null, WhsProduct::getCategoryId, bo.getCategoryId());
        lqw.eq(bo.getSeriesId() != null, WhsProduct::getSeriesId, bo.getSeriesId());
        lqw.like(StringUtils.isNotBlank(bo.getItemName()), WhsProduct::getItemName, bo.getItemName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), WhsProduct::getStatus, bo.getStatus());

        // 处理变体SKU查询：通过变体SKU搜索相关产品
        if (StringUtils.isNotBlank(bo.getSkuCode())) {
            // 查询包含指定SKU的变体的产品ID
            LambdaQueryWrapper<WhsProductVariant> variantQuery = new LambdaQueryWrapper<>();
            variantQuery.like(WhsProductVariant::getSkuCode, bo.getSkuCode());
            variantQuery.select(WhsProductVariant::getProductId);
            
            List<WhsProductVariant> matchedVariants = variantMapper.selectList(variantQuery);
            if (CollUtil.isNotEmpty(matchedVariants)) {
                // 提取产品ID列表
                List<Long> productIds = matchedVariants.stream()
                    .map(WhsProductVariant::getProductId)
                    .distinct()
                    .collect(Collectors.toList());
                
                // 添加产品ID过滤条件
                lqw.in(WhsProduct::getId, productIds);
                
                log.debug("通过变体SKU '{}' 找到相关产品数量: {}", bo.getSkuCode(), productIds.size());
            } else {
                // 没有找到匹配的变体，返回空结果
                lqw.eq(WhsProduct::getId, -1L); // 使用不存在的ID，确保返回空结果
                log.debug("通过变体SKU '{}' 未找到任何相关产品", bo.getSkuCode());
            }
        }

        // 排序，先通过sort，再通过create_time
        lqw.orderByDesc(WhsProduct::getSort).orderByDesc(WhsProduct::getCreateTime);

        return lqw;
    }

    /**
     * 处理产品关联数据 (属性)
     */
    private void processRelatedData(WhsProductBo bo, Long productId) {
        // 处理属性关联
        List<Long> attributeIds = bo.getAttributeIds();
        if (attributeIds != null) {
            // 使用增量更新处理属性关联
            attributeRelationService.processProductAttributes(productId, attributeIds);
        }
    }

    /**
     * 批量填充产品数据
     */
    private void batchFillData(List<WhsProductVo> products, boolean fillAttributes) {
        if (CollUtil.isEmpty(products)) {
            return;
        }

        // 收集需要的ID
        Set<Long> productIds = products.stream().map(WhsProductVo::getId).collect(Collectors.toSet());
        Set<Long> categoryIds = products.stream().map(WhsProductVo::getCategoryId).filter(Objects::nonNull)
            .collect(Collectors.toSet());
        Set<Long> seriesIds = products.stream().map(WhsProductVo::getSeriesId).filter(Objects::nonNull)
            .collect(Collectors.toSet());

        // 批量查询分类名称
        Map<Long, String> categoryNames = categoryService.getCategoryNames(new ArrayList<>(categoryIds));
        Map<Long, String> seriesNames = seriesService.getSeriesNames(new ArrayList<>(seriesIds));

        // 只在需要时查询属性信息
        Map<Long, List<WhsProductAttributeVo>> attributesMap = fillAttributes
            ? getProductAttributes(new ArrayList<>(productIds), false)
            : Collections.emptyMap();

        // 批量查询变体数据
        Map<Long, List<WhsProductVariant>> variantMap = variantService
            .getVariantsByProductIds(new ArrayList<>(productIds));

        // 填充数据
        for (WhsProductVo product : products) {
            // 填充分类和系列名称
            Optional.ofNullable(product.getCategoryId()).map(categoryNames::get).ifPresent(product::setCategoryName);
            Optional.ofNullable(product.getSeriesId()).map(seriesNames::get).ifPresent(product::setSeriesName);

            // 只在需要时填充属性信息
            if (fillAttributes) {
                product.setAttributes(attributesMap.getOrDefault(product.getId(), Collections.emptyList()));
            }

            // 填充变体信息用于计算统计数据
            List<WhsProductVariant> productVariants = variantMap.getOrDefault(product.getId(),
                Collections.emptyList());
            List<WhsProductVariantVo> variantVos = variantService.convertToVoList(productVariants);

            // 填充变体的库存信息
            for (WhsProductVariantVo variantVo : variantVos) {
                // 填充库存信息
                Integer availableStock = stockService.getAvailableStocks(List.of(variantVo.getId()))
                    .getOrDefault(variantVo.getId(), 0);
                variantVo.setAvailableStock(availableStock);
            }

            // 设置产品主图：优先使用默认变体的主图，如果没有默认变体则使用第一个变体的主图
            setProductMainImage(product, variantVos);

            // 计算统计数据
            calculatePriceAndStockSummary(product, variantVos);
        }
    }

    /**
     * 设置产品主图
     * 优先使用默认变体的主图，如果没有默认变体则使用第一个变体的主图
     *
     * @param product  产品对象
     * @param variants 变体列表
     */
    private void setProductMainImage(WhsProductVo product, List<WhsProductVariantVo> variants) {
        if (CollUtil.isEmpty(variants)) {
            return;
        }

        // 优先使用默认变体的主图
        if (product.getDefaultVariantId() != null) {
            variants.stream()
                .filter(v -> v.getId().equals(product.getDefaultVariantId()))
                .findFirst()
                .map(WhsProductVariantVo::getMainImage)
                .ifPresent(product::setMainImage);
        }

        // 如果还没有设置主图（没有默认变体或默认变体没有主图），则使用第一个变体的主图
        if (StringUtils.isEmpty(product.getMainImage())) {
            variants.stream()
                .findFirst()
                .map(WhsProductVariantVo::getMainImage)
                .ifPresent(product::setMainImage);
        }
    }

    /**
     * 计算价格和库存汇总
     */
    private void calculatePriceAndStockSummary(WhsProductVo product, List<WhsProductVariantVo> variants) {
        // 变体为空时设置默认值
        if (CollUtil.isEmpty(variants)) {
            return;
        }

        // 设置变体数量
        product.setVariantCount(variants.size());

        // 计算批发价范围
        calculatePriceRange(variants, WhsProductVariantVo::getWholesalePrice, product::setMinWholesalePrice,
            product::setMaxWholesalePrice);

        // 计算零售价范围
        calculatePriceRange(variants, WhsProductVariantVo::getMsrp, product::setMinMsrp, product::setMaxMsrp);

        // 计算门店价格范围
        calculatePriceRange(variants, WhsProductVariantVo::getStorePrice, product::setMinStorePrice, product::setMaxStorePrice);

        // 计算库存统计
        int totalStock = variants.stream().mapToInt(v -> Optional.ofNullable(v.getAvailableStock()).orElse(0)).sum();

        product.setTotalStock(totalStock);
        product.setAvailableStock(totalStock);
    }

    /**
     * 计算价格范围
     *
     * @param variants    变体列表
     * @param priceGetter 价格获取器
     * @param minSetter   最小价格设置器
     * @param maxSetter   最大价格设置器
     */
    private <T> void calculatePriceRange(List<T> variants, Function<T, BigDecimal> priceGetter,
                                         Consumer<BigDecimal> minSetter, Consumer<BigDecimal> maxSetter) {

        minSetter.accept(variants.stream().map(priceGetter).filter(Objects::nonNull).min(BigDecimal::compareTo)
            .orElse(BigDecimal.ZERO));

        maxSetter.accept(variants.stream().map(priceGetter).filter(Objects::nonNull).max(BigDecimal::compareTo)
            .orElse(BigDecimal.ZERO));
    }

    /**
     * 发布产品更新事件
     * 用于通知系统中的其他组件产品数据已更新
     *
     * @param productIds 更新的产品ID列表
     */
    private void publishProductUpdateEvent(List<Long> productIds) {
        try {
            if (CollUtil.isEmpty(productIds)) {
                return;
            }

            // 构造一个特殊的StockChangeEvent，标记为产品更新
            StockChangeEvent event = new StockChangeEvent(this, Collections.emptyList(), productIds, true);
            applicationEventPublisher.publishEvent(event);
            log.debug("已发布产品更新事件，产品ID数量: {}", productIds.size());
        } catch (Exception e) {
            log.error("发布产品更新事件失败: {}", e.getMessage());
        }
    }

    /**
     * 根据产品ID列表批量获取产品基本信息
     *
     * @param productIds 产品ID列表
     * @return 产品信息列表 (只包含基本信息，如ID和名称)
     */
    @Override
    public List<WhsProductVo> getProductsByIds(List<Long> productIds) {
        if (CollUtil.isEmpty(productIds)) {
            return Collections.emptyList();
        }
        // 使用 selectVoList 只查询需要的 VO
        // 注意：如果 WhsProductVo 包含很多字段，考虑创建一个只包含 id 和 itemName 的轻量级 VO
        // 或者修改 Mapper 的 selectVoList 方法以支持选择特定列
        LambdaQueryWrapper<WhsProduct> lqw = new LambdaQueryWrapper<>();
        lqw.in(WhsProduct::getId, productIds);
        // 假设 WhsProductVo 中包含 id 和 itemName
        return productMapper.selectVoList(lqw);
    }
}
