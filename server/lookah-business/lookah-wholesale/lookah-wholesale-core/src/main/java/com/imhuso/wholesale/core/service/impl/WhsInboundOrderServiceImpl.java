package com.imhuso.wholesale.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.WhsInboundOrder;
import com.imhuso.wholesale.core.domain.WhsInboundOrderItem;
import com.imhuso.wholesale.core.domain.WhsWarehouse;
import com.imhuso.wholesale.core.domain.bo.admin.WhsInboundOrderListBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsInboundOrderItemVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsInboundOrderVo;
import com.imhuso.wholesale.core.mapper.WhsInboundOrderItemMapper;
import com.imhuso.wholesale.core.mapper.WhsInboundOrderMapper;
import com.imhuso.wholesale.core.mapper.WhsWarehouseMapper;
import com.imhuso.wholesale.core.service.IWhsInboundOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 入库单服务实现类
 * <p>
 * 注意：入库单数据由海外仓同步服务自动同步，后台API仅提供只读操作
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsInboundOrderServiceImpl implements IWhsInboundOrderService {

    private final WhsInboundOrderMapper inboundOrderMapper;
    private final WhsInboundOrderItemMapper inboundOrderItemMapper;
    private final WhsWarehouseMapper warehouseMapper;

    /**
     * 查询入库单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 入库单列表
     */
    @Override
    public TableDataInfo<WhsInboundOrderVo> queryInboundOrderList(WhsInboundOrderListBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WhsInboundOrder> lqw = buildQueryWrapper(bo);

        // 排序逻辑：
        // 1. 首先按状态排序，未完成的排在前面（状态值小的在前）
        // 2. 然后按预计到达时间排序，即将到达的排在前面
        // 3. 最后按创建时间倒序排序
        lqw.last("ORDER BY status ASC, " +
                 "CASE WHEN expected_arrival IS NULL THEN 1 ELSE 0 END, " + // 有预计到达时间的排在前面
                 "expected_arrival ASC, " +
                 "create_time DESC");
        Page<WhsInboundOrderVo> result = inboundOrderMapper.selectVoPage(pageQuery.build(), lqw);

        // 处理结果数据，填充仓库名称和汇总数据
        if (result.getRecords() != null && !result.getRecords().isEmpty()) {
            // 获取所有仓库ID
            Set<Long> warehouseIds = result.getRecords().stream()
                    .map(WhsInboundOrderVo::getWarehouseId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 批量查询仓库信息
            Map<Long, String> warehouseMap = new HashMap<>();
            if (!warehouseIds.isEmpty()) {
                List<WhsWarehouse> warehouses = warehouseMapper.selectByIds(warehouseIds);
                warehouseMap = warehouses.stream()
                        .collect(Collectors.toMap(WhsWarehouse::getId, WhsWarehouse::getName, (a, b) -> a));
            }

            // 批量查询入库单ID
            List<Long> orderIds = result.getRecords().stream()
                    .map(WhsInboundOrderVo::getId)
                    .collect(Collectors.toList());

            // 批量查询入库单明细汇总数据
            Map<Long, Integer> totalQuantityMap = new HashMap<>();
            Map<Long, Integer> totalReceivedQuantityMap = new HashMap<>();
            Map<Long, Integer> totalInTransitQuantityMap = new HashMap<>();

            if (!orderIds.isEmpty()) {
                // 查询所有入库单明细
                LambdaQueryWrapper<WhsInboundOrderItem> itemWrapper = Wrappers.lambdaQuery();
                itemWrapper.in(WhsInboundOrderItem::getInboundOrderId, orderIds);
                List<WhsInboundOrderItem> items = inboundOrderItemMapper.selectList(itemWrapper);

                // 按入库单ID分组，计算汇总数据
                Map<Long, List<WhsInboundOrderItem>> itemsGroupByOrderId = items.stream()
                        .collect(Collectors.groupingBy(WhsInboundOrderItem::getInboundOrderId));

                for (Map.Entry<Long, List<WhsInboundOrderItem>> entry : itemsGroupByOrderId.entrySet()) {
                    Long orderId = entry.getKey();
                    List<WhsInboundOrderItem> orderItems = entry.getValue();

                    int totalQuantity = orderItems.stream()
                            .mapToInt(item -> item.getQuantity() != null ? item.getQuantity() : 0)
                            .sum();

                    int totalReceivedQuantity = orderItems.stream()
                            .mapToInt(item -> item.getReceivedQuantity() != null ? item.getReceivedQuantity() : 0)
                            .sum();

                    int totalInTransitQuantity = totalQuantity - totalReceivedQuantity;

                    totalQuantityMap.put(orderId, totalQuantity);
                    totalReceivedQuantityMap.put(orderId, totalReceivedQuantity);
                    totalInTransitQuantityMap.put(orderId, totalInTransitQuantity);
                }
            }

            // 填充数据
            for (WhsInboundOrderVo vo : result.getRecords()) {
                // 填充仓库名称
                if (vo.getWarehouseId() != null) {
                    vo.setWarehouseName(warehouseMap.get(vo.getWarehouseId()));
                }

                // 填充汇总数据
                Long orderId = vo.getId();
                vo.setTotalQuantity(totalQuantityMap.getOrDefault(orderId, 0));
                vo.setTotalReceivedQuantity(totalReceivedQuantityMap.getOrDefault(orderId, 0));
                vo.setTotalInTransitQuantity(totalInTransitQuantityMap.getOrDefault(orderId, 0));
            }
        }

        return TableDataInfo.build(result);
    }

    /**
     * 构建查询条件
     *
     * @param bo 查询条件
     * @return 查询条件
     */
    private LambdaQueryWrapper<WhsInboundOrder> buildQueryWrapper(WhsInboundOrderListBo bo) {
        LambdaQueryWrapper<WhsInboundOrder> lqw = Wrappers.lambdaQuery();

        // 基本查询条件
        lqw.eq(bo.getWarehouseId() != null, WhsInboundOrder::getWarehouseId, bo.getWarehouseId());

        // 单号支持同时查询入库单号和外部入库单号 - 修复or逻辑
        if (StringUtils.isNotBlank(bo.getOrderNo())) {
            lqw.and(wrapper -> wrapper
                .like(WhsInboundOrder::getExternalOrderNo, bo.getOrderNo())
                .or()
                .like(WhsInboundOrder::getOrderNo, bo.getOrderNo())
            );
        }

        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), WhsInboundOrder::getStatus, bo.getStatus());

        // 按SKU编码查询 - 需要关联入库单明细表
        if (StringUtils.isNotBlank(bo.getSkuCode())) {
            // 查询包含指定SKU的入库单ID列表
            LambdaQueryWrapper<WhsInboundOrderItem> itemWrapper = Wrappers.lambdaQuery();
            itemWrapper.like(WhsInboundOrderItem::getSkuCode, bo.getSkuCode());
            List<WhsInboundOrderItem> items = inboundOrderItemMapper.selectList(itemWrapper);

            if (items != null && !items.isEmpty()) {
                List<Long> orderIds = items.stream()
                        .map(WhsInboundOrderItem::getInboundOrderId)
                        .distinct()
                        .collect(Collectors.toList());

                lqw.in(WhsInboundOrder::getId, orderIds);
            } else {
                // 如果没有找到匹配的SKU，返回空结果
                lqw.eq(WhsInboundOrder::getId, -1L);
            }
        }

        return lqw;
    }

    /**
     * 查询入库单详情
     *
     * @param id 入库单ID
     * @return 入库单详情
     */
    @Override
    public WhsInboundOrderVo getInboundOrderById(Long id) {
        // 查询入库单基本信息
        WhsInboundOrderVo vo = inboundOrderMapper.selectVoById(id);
        if (vo == null) {
            return null;
        }

        // 填充仓库名称
        if (vo.getWarehouseId() != null) {
            WhsWarehouse warehouse = warehouseMapper.selectById(vo.getWarehouseId());
            if (warehouse != null) {
                vo.setWarehouseName(warehouse.getName());
            }
        }

        // 查询入库单明细并填充产品名称
        LambdaQueryWrapper<WhsInboundOrderItem> itemWrapper = Wrappers.lambdaQuery();
        itemWrapper.eq(WhsInboundOrderItem::getInboundOrderId, id);
        List<WhsInboundOrderItemVo> items = inboundOrderItemMapper.selectVoList(itemWrapper);

        // 使用Java 8 Stream API处理明细数据
        if (!items.isEmpty()) {
            // 批量获取所有变体ID
            Set<Long> variantIds = items.stream()
                    .map(WhsInboundOrderItemVo::getVariantId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 批量查询产品名称
            Map<Long, String> productNameMap = new HashMap<>();
            for (Long variantId : variantIds) {
                String productName = inboundOrderItemMapper.getProductNameByVariantId(variantId);
                if (productName != null) {
                    productNameMap.put(variantId, productName);
                }
            }

            // 填充产品名称
            items.forEach(item -> {
                if (item.getVariantId() != null) {
                    item.setProductName(productNameMap.get(item.getVariantId()));
                }
            });

            // 计算汇总数据
            int totalQuantity = items.stream()
                    .mapToInt(item -> item.getQuantity() != null ? item.getQuantity() : 0)
                    .sum();

            int totalReceivedQuantity = items.stream()
                    .mapToInt(item -> item.getReceivedQuantity() != null ? item.getReceivedQuantity() : 0)
                    .sum();

            vo.setItems(items);
            vo.setTotalQuantity(totalQuantity);
            vo.setTotalReceivedQuantity(totalReceivedQuantity);
            vo.setTotalInTransitQuantity(totalQuantity - totalReceivedQuantity);
        } else {
            // 没有明细时设置默认值
            vo.setItems(List.of());
            vo.setTotalQuantity(0);
            vo.setTotalReceivedQuantity(0);
            vo.setTotalInTransitQuantity(0);
        }

        return vo;
    }

    /**
     * 根据产品ID查询相关的在途入库单
     *
     * @param productId 产品ID
     * @param pageQuery 分页参数
     * @return 入库单列表
     */
    @Override
    public TableDataInfo<WhsInboundOrderVo> queryInboundOrdersByProductId(Long productId, PageQuery pageQuery) {
        LambdaQueryWrapper<WhsInboundOrder> lqw = Wrappers.lambdaQuery();
        // 基础查询条件在XML中实现，这里只是占位
        Page<WhsInboundOrderVo> result = inboundOrderMapper.selectInboundOrdersByProductId(pageQuery.build(), lqw, productId);
        return TableDataInfo.build(result);
    }

    /**
     * 根据变体ID查询相关的在途入库单
     *
     * @param variantId 变体ID
     * @param pageQuery 分页参数
     * @return 入库单列表
     */
    @Override
    public TableDataInfo<WhsInboundOrderVo> queryInboundOrdersByVariantId(Long variantId, PageQuery pageQuery) {
        LambdaQueryWrapper<WhsInboundOrder> lqw = Wrappers.lambdaQuery();
        // 基础查询条件在XML中实现，这里只是占位
        Page<WhsInboundOrderVo> result = inboundOrderMapper.selectInboundOrdersByVariantId(pageQuery.build(), lqw, variantId);
        return TableDataInfo.build(result);
    }
}
