package com.imhuso.wholesale.core.service.impl;

import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.file.FileUtils;
import com.imhuso.wholesale.core.domain.vo.admin.PackingSlipAvailabilityVo;
import com.imhuso.wholesale.core.service.IWhsOrderBaseService;
import com.imhuso.wholesale.core.service.IWhsOrderShipmentQueryService;
import com.imhuso.wholesale.core.service.IPackingSlipService;
import com.imhuso.wholesale.core.service.IWhsOrderNoFormatService;
import com.imhuso.wholesale.core.service.IWhsOrderPackingSlipService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

/**
 * 订单装箱单服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsOrderPackingSlipServiceImpl implements IWhsOrderPackingSlipService {

    private final IWhsOrderBaseService orderBaseService;
    private final IWhsOrderShipmentQueryService shipmentQueryService;
    private final IPackingSlipService packingSlipService;
    private final IWhsOrderNoFormatService orderNoFormatService;

    @Override
    public boolean isPackingSlipAvailable(Long orderId) {
        try {
            // 1. 检查订单是否存在
            if (orderBaseService.getOrderById(orderId) == null) {
                log.warn("订单[{}]不存在，无法下载装箱单", orderId);
                return false;
            }

            // 2. 检查订单是否有发货记录
            var shipments = shipmentQueryService.getOrderShipmentRecords(orderId);
            if (shipments == null || shipments.isEmpty()) {
                log.warn("订单[{}]没有发货记录，无法下载装箱单", orderId);
                return false;
            }

            log.info("订单[{}]可以下载装箱单", orderId);
            return true;
        } catch (Exception e) {
            log.error("检查订单[{}]装箱单可用性时发生错误: {}", orderId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public PackingSlipAvailabilityVo getPackingSlipAvailability(Long orderId) {
        return packingSlipService.checkPackingSlipAvailability(orderId);
    }


    @Override
    public void downloadOrderPackingSlip(Long orderId, HttpServletResponse response) {
        try {
            // 使用统一的装箱单生成服务，自动获取最新的发货记录
            File packingSlipFile = packingSlipService.generatePackingSlipExcelById(orderId);

            // 发送文件到客户端
            sendPackingSlipToClient(packingSlipFile, orderId, response);
        } catch (RuntimeException e) {
            log.error("生成订单[{}]装箱单时发生错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        } catch (Exception e) {
            log.error("下载订单[{}]装箱单失败", orderId, e);
            throw new ServiceException("下载装箱单失败");
        }
    }

    /**
     * 发送装箱单文件到客户端
     *
     * @param packingSlipFile 装箱单文件
     * @param orderId         订单ID
     * @param response        HTTP响应对象
     * @throws Exception 处理过程中的异常
     */
    private void sendPackingSlipToClient(File packingSlipFile, Long orderId,
                                         HttpServletResponse response) throws Exception {
        if (packingSlipFile == null || !packingSlipFile.exists()) {
            throw new ServiceException("装箱单文件不存在");
        }

        try {
            // 获取订单号用于文件名
            var order = orderBaseService.getOrderById(orderId);
            String customerOrderNo = order != null ? orderNoFormatService.getCustomerOrderNoByOrder(order) : orderId.toString();

            // 构建文件名 - 使用客户订单号
            String filename = "PACKING-SLIP-" + customerOrderNo + ".xlsx";

            response.reset();

            // 针对Cloudflare代理的综合处理
            // 1. 缓存控制 - 确保内容不被缓存
            response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");

            // 2. 跨域支持 - 确保跨域下载正常
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            response.setHeader("Access-Control-Allow-Headers", "Content-Type, Content-Disposition, Content-Length");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition, Content-Length, Content-Type, Cache-Control, download-filename");

            // 3. 内容处理 - 确保Cloudflare正确处理内容
            response.setHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");
            response.setHeader("X-Content-Type-Options", "nosniff");

            // 4. 标准文件下载头
            FileUtils.setAttachmentResponseHeader(response, filename);

            // 设置内容类型
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setCharacterEncoding("UTF-8");
            response.setContentLengthLong(packingSlipFile.length());

            // 确保流正确关闭
            try (var outputStream = response.getOutputStream()) {
                Files.copy(packingSlipFile.toPath(), outputStream);
                outputStream.flush();
            }
        } finally {
            try {
                // 删除临时文件
                if (!Files.deleteIfExists(packingSlipFile.toPath())) {
                    log.warn("临时文件删除失败: {}", packingSlipFile.getAbsolutePath());
                }
            } catch (IOException e) {
                log.warn("删除临时文件失败", e);
            }
        }
    }
}
