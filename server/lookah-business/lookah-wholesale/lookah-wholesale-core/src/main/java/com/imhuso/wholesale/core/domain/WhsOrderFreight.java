package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单运费信息对象（与包裹关联）
 * 第三方同步数据，不继承BaseEntity
 *
 * <AUTHOR>
 */
@Data
@TableName("whs_order_freight")
public class WhsOrderFreight {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 包裹ID，关联whs_logistics_package表
     */
    private Long packageId;

    /**
     * 订单ID（冗余字段，便于查询）
     */
    private Long orderId;

    /**
     * 预估运费
     */
    private BigDecimal estimatedFreight;

    /**
     * 实际运费
     */
    private BigDecimal actualFreight;

    /**
     * 运费币种
     */
    private String freightCurrency;

    /**
     * 运费同步时间
     */
    private Date syncTime;

    /**
     * 同步状态（0未同步 1已同步）
     */
    private String syncStatus;

    /**
     * 提供商类型（如：haotong、other）
     */
    private String providerType;

    /**
     * 提供商跟踪号
     */
    private String providerTrackingNo;

    /**
     * 提供商订单号
     */
    private String providerOrderNo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private String delFlag;

}
