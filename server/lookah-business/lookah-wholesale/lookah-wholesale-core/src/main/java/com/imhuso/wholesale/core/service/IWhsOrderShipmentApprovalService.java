package com.imhuso.wholesale.core.service;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderShipmentApprovalBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentApprovalVo;

import java.util.List;

/**
 * 订单发货审批服务接口
 *
 * <AUTHOR>
 */
public interface IWhsOrderShipmentApprovalService {

    /**
     * 查询发货审批分页列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    TableDataInfo<WhsOrderShipmentApprovalVo> queryPageList(WhsOrderShipmentApprovalBo bo, PageQuery pageQuery);

    /**
     * 根据ID查询发货审批详情
     *
     * @param id 审核ID
     * @return 审核详情
     */
    WhsOrderShipmentApprovalVo queryById(Long id);

    /**
     * 根据订单ID查询最新的发货审批记录
     *
     * @param orderId 订单ID
     * @return 审核记录，如果没有则返回null
     */
    WhsOrderShipmentApprovalVo queryLatestByOrderId(Long orderId);

    /**
     * 根据订单ID查询所有发货审批历史记录
     *
     * @param orderId 订单ID
     * @return 审核记录列表，按创建时间倒序排列
     */
    List<WhsOrderShipmentApprovalVo> queryHistoryByOrderId(Long orderId);

    /**
     * 提交发货审批申请
     *
     * @param orderId           订单ID
     * @param applicationReason 申请理由
     * @return 审核ID
     */
    Long submitApprovalRequest(Long orderId, String applicationReason);

    /**
     * 系统自动提交发货审批申请
     * 适用于异步监听器等系统级别的自动操作，不依赖当前用户上下文
     *
     * @param orderId           订单ID
     * @param applicationReason 申请理由
     * @return 审核ID
     */
    Long submitApprovalRequestBySystem(Long orderId, String applicationReason);

    /**
     * 审核发货申请
     *
     * @param approvalId      审核ID
     * @param approved        是否通过审核
     * @param approvalComment 审核意见
     * @return 是否成功
     */
    boolean approveShipmentRequest(Long approvalId, boolean approved, String approvalComment);

    /**
     * 撤销发货审批申请
     *
     * @param approvalId 审核ID
     * @return 是否成功
     */
    boolean cancelApprovalRequest(Long approvalId);

    /**
     * 系统级别撤销发货审批申请
     * 用于系统自动操作，如订单强制完成时撤销相关审批
     * 不进行申请人权限检查
     *
     * @param approvalId 审核ID
     * @param reason     撤销原因
     * @return 是否成功
     */
    boolean cancelApprovalRequestBySystem(Long approvalId, String reason);

    /**
     * 检查订单是否需要发货审批
     *
     * @param orderId 订单ID
     * @return 是否需要审核
     */
    boolean isShipmentApprovalRequired(Long orderId);

    /**
     * 检查订单是否已通过发货审批
     *
     * @param orderId 订单ID
     * @return 是否已通过审核
     */
    boolean isShipmentApproved(Long orderId);

    /**
     * 重置订单的审核数据（状态和记录）
     * 用于订单撤回时清理审核相关数据
     *
     * @param orderId 订单ID
     */
    void resetOrderApprovalData(Long orderId);
}
