package com.imhuso.wholesale.core.domain.vo.admin;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.imhuso.wholesale.core.domain.WhsMember;
import com.imhuso.common.translation.annotation.Translation;
import com.imhuso.common.translation.constant.TransConstant;
import cn.dev33.satoken.stp.StpUtil;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 会员管理视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsMember.class)
public class WhsMemberVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员ID
     */
    private Long id;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 销售代表ID，关联sys_user表
     */
    private Long salespersonId;

    /**
     * 销售代表姓名（关联字段，不存储在数据库）
     */
    @Translation(type = TransConstant.USER_ID_TO_NICKNAME, mapper = "salespersonId")
    private String salespersonName;

    /**
     * 会员邮箱
     */
    private String email;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * EIN税号
     */
    private String ein;

    /**
     * 名字
     */
    private String firstName;

    /**
     * 姓氏
     */
    private String lastName;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 帐号状态（0停用 1正常）
     */
    private String status;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 最后登录位置
     */
    private String loginLocation;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 公司类型（wholesale,retail,chain,cash_carry）
     */
    private String companyType;

    /**
     * 店面数量
     */
    private Integer storeCount;

    /**
     * 客户来源
     */
    private String customerSource;

    /**
     * 自定义客户来源（当customerSource为others时使用）
     */
    private String customSource;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 自定义公司类型（当companyType为others时使用）
     */
    private String customCompanyType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 检查是否有查看敏感信息的权限
     */
    @JsonIgnore
    public boolean hasViewSensitivePermission() {
        try {
            return StpUtil.hasPermission("wholesale:member:sensitive:view");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 过滤敏感信息（如果没有权限）
     */
    public void filterSensitiveData() {
        if (!hasViewSensitivePermission()) {
            this.loginIp = null;
            this.loginDate = null;
            this.loginLocation = null;
            this.registerTime = null;
        }
    }
}
