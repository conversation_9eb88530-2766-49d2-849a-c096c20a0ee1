package com.imhuso.wholesale.core.enums;

import com.imhuso.common.translation.core.EnumTranslatableInterface;
import lombok.Getter;

/**
 * 订单操作类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum OrderOperationType implements EnumTranslatableInterface {

    /**
     * 创建订单
     */
    CREATE(0, "创建"),

    /**
     * 支付订单
     */
    PAYMENT(1, "支付"),

    /**
     * 取消订单
     */
    CANCEL(2, "取消"),

    /**
     * 处理订单
     */
    PROCESS(3, "处理"),

    /**
     * 发货
     */
    SHIP(4, "发货"),

    /**
     * 部分发货
     */
    PARTIAL_SHIP(5, "部分发货"),

    /**
     * 完成订单
     */
    COMPLETE(6, "完成"),

    /**
     * 撤回订单
     */
    REVERT(7, "撤回");

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 构造函数
     */
    OrderOperationType(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
