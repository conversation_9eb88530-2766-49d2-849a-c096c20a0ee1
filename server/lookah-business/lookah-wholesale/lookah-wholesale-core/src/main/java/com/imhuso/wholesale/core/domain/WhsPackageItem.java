package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品包装明细对象 whs_package_item
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("whs_package_item")
public class WhsPackageItem extends BaseEntity {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 包装变体ID
     */
    private Long parentVariantId;

    /**
     * 内容物变体ID
     */
    private Long childVariantId;

    /**
     * 包含数量
     */
    private Integer quantity;
}
