package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.vo.warehouse.*;

import java.util.List;

/**
 * 海外仓服务接口
 *
 * <AUTHOR>
 */
public interface IWhsOverseasWarehouseService {
    /**
     * 创建仓库上下文
     *
     * @param warehouseId 仓库ID
     * @return 仓库上下文
     */
    WarehouseContextVo createWarehouseContext(Long warehouseId);

    /**
     * 同步仓库库存
     *
     * @param warehouseId 仓库ID
     * @param variants    变体列表
     * @return 同步结果
     */
    StockSyncResultVo syncStock(Long warehouseId, List<WhsProductVariant> variants);

    /**
     * 创建发货单
     *
     * @param warehouseId  仓库ID
     * @param shipmentInfo 发货信息
     * @param packageId    包裹ID
     * @param batchNumber  批次号 (用于部分提供商格式化参考号)
     * @return 发货结果
     */
    ShippingResultVo createShipment(Long warehouseId, ShipmentInfoVo shipmentInfo, Long packageId, Integer batchNumber);

    /**
     * 取消海外仓订单
     *
     * @param warehouseId        仓库ID
     * @param packageId          包裹ID
     * @param externalShipmentId 外部系统（如海外仓）返回的发货单号
     * @return 取消结果
     */
    CancelOrderResultVo cancelOrder(Long warehouseId, Long packageId, String externalShipmentId);
}
