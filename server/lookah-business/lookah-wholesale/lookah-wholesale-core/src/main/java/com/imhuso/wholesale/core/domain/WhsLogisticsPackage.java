package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 物流包裹对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_logistics_package")
public class WhsLogisticsPackage extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 包裹ID
     */
    @TableId
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 关联的发货单ID
     */
    private Long shipmentId;

    /**
     * 跟踪号
     */
    private String trackingNumber;

    /**
     * 承运商(如：FedEx、DHL、UPS)
     */
    private String carrier;

    /**
     * 物流方式ID
     */
    private Long logisticsMethodId;

    /**
     * 包裹状态(0-待处理 1-已发货 2-运输中 3-已送达 4-异常)
     */
    private Integer packageStatus;

    /**
     * 发货仓库ID
     */
    private Long warehouseId;

    /**
     * 发货日期
     */
    private Date shippedDate;

    /**
     * 预计送达日期
     */
    private Date expectedDelivery;

    /**
     * 实际送达日期
     */
    private Date actualDelivery;

    /**
     * 海外仓订单号
     */
    private String externalOrderNo;
}
