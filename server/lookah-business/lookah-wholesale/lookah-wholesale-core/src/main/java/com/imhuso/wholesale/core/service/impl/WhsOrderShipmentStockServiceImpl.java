package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.wholesale.core.domain.bo.admin.WhsShipmentPlanItemBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentStockResponseVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentWarehouseStockVo;
import com.imhuso.wholesale.core.service.IWhsOrderShipmentStockService;
import com.imhuso.wholesale.core.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单发货库存查询服务实现类 (查询实际变体库存)
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsOrderShipmentStockServiceImpl implements IWhsOrderShipmentStockService {

    private final IWhsShipmentPlanItemService shipmentPlanItemService;
    private final IWhsStockService stockService;
    private final IWhsWarehouseService warehouseService;
    private final IWhsStockAllocationService stockAllocationService;

    /**
     * 根据发货方案ID获取相关变体的库存信息
     * 返回方案中变体在各仓库的有效可用库存
     */
    @Override
    public WhsShipmentStockResponseVo getStocksByPlanId(Long planId) {
        if (planId == null) {
            throw new ServiceException("查询发货库存时，发货计划ID不能为空");
        }

        // 获取发货方案项
        List<WhsShipmentPlanItemBo> planItems = shipmentPlanItemService.getShipmentPlanItemBosByPlanId(planId);

        if (CollUtil.isEmpty(planItems)) {
            log.warn("未找到发货计划ID为 {} 的计划项", planId);
            return WhsShipmentStockResponseVo.builder()
                .planId(planId)
                .variantWarehouseStocks(MapUtil.newHashMap())
                .build();
        }

        // 提取方案项中的变体ID
        List<Long> variantIds = planItems.stream()
            .map(WhsShipmentPlanItemBo::getVariantId)
            .distinct()
            .collect(Collectors.toList());

        if (CollUtil.isEmpty(variantIds)) {
            log.warn("发货计划ID {} 的所有计划项中未找到有效的变体ID", planId);
            return WhsShipmentStockResponseVo.builder()
                .planId(planId)
                .variantWarehouseStocks(MapUtil.newHashMap())
                .build();
        }

        // 获取各仓库的可用库存分布
        Map<Long, Map<Long, Integer>> warehouseStocksMap = stockService.getAvailableStocksByWarehouse(variantIds);
        log.info("获取到变体在各仓库的可用库存分布: {}", warehouseStocksMap);

        // 获取锁定库存信息（已按变体ID和仓库ID分组）
        Map<Long, Map<Long, Integer>> lockedStockMap = stockAllocationService.getLockedStockByPlanId(planId);
        log.info("获取到锁定库存信息: {}", lockedStockMap);

        // 获取有效仓库信息
        Map<Long, String> warehouseNameMap = warehouseService.getActiveWarehouseIdNameMap();

        // 构建结果
        Map<Long, List<WhsShipmentWarehouseStockVo>> resultStockMap = MapUtil.newHashMap();

        for (Long variantId : variantIds) {
            // 获取此变体的锁定库存（按仓库ID分组）
            Map<Long, Integer> variantLockedStock = lockedStockMap.getOrDefault(variantId, MapUtil.newHashMap());

            // 获取此变体在各仓库的可用库存
            Map<Long, Integer> variantWarehouseStocks = warehouseStocksMap.getOrDefault(variantId,
                MapUtil.newHashMap());

            // 记录该变体的库存信息
            List<WhsShipmentWarehouseStockVo> warehouseStockInfos = new ArrayList<>();

            // 处理有物理库存的仓库
            processWarehousesWithStock(variantId, warehouseNameMap, variantLockedStock, variantWarehouseStocks,
                warehouseStockInfos);

            // 处理无物理库存但有锁定库存的仓库
            processWarehousesWithOnlyLockedStock(variantId, warehouseNameMap, variantLockedStock,
                variantWarehouseStocks, warehouseStockInfos);

            if (CollUtil.isNotEmpty(warehouseStockInfos)) {
                resultStockMap.put(variantId, warehouseStockInfos);
            }
        }

        // 构建并返回最终响应
        return WhsShipmentStockResponseVo.builder()
            .planId(planId)
            .variantWarehouseStocks(resultStockMap)
            .build();
    }

    /**
     * 处理有物理库存的仓库
     */
    private void processWarehousesWithStock(
        Long variantId,
        Map<Long, String> warehouseNameMap,
        Map<Long, Integer> variantLockedStock,
        Map<Long, Integer> variantWarehouseStocks,
        List<WhsShipmentWarehouseStockVo> warehouseStockInfos) {

        for (Map.Entry<Long, Integer> entry : variantWarehouseStocks.entrySet()) {
            Long warehouseId = entry.getKey();

            // 跳过无效仓库
            if (!warehouseNameMap.containsKey(warehouseId)) {
                continue;
            }

            Integer availableStock = entry.getValue();

            // 计算此仓库已锁定的库存
            int locked = variantLockedStock.getOrDefault(warehouseId, 0);

            // 物理可用库存 + 已锁定库存 = 有效可用库存
            int effectiveAvailableStock = availableStock + locked;

            // 构建库存信息对象
            WhsShipmentWarehouseStockVo stockInfo = WhsShipmentWarehouseStockVo.builder()
                .warehouseId(warehouseId)
                .warehouseName(warehouseNameMap.get(warehouseId))
                .effectiveAvailableStock(effectiveAvailableStock)
                .build();
            warehouseStockInfos.add(stockInfo);

            log.info("变体ID={} 仓库ID={} 物理可用库存={} 锁定库存={} 有效可用库存={}",
                variantId, warehouseId, availableStock, locked, effectiveAvailableStock);
        }
    }

    /**
     * 处理无物理库存但有锁定库存的仓库
     */
    private void processWarehousesWithOnlyLockedStock(
        Long variantId,
        Map<Long, String> warehouseNameMap,
        Map<Long, Integer> variantLockedStock,
        Map<Long, Integer> variantWarehouseStocks,
        List<WhsShipmentWarehouseStockVo> warehouseStockInfos) {

        for (Map.Entry<Long, Integer> entry : variantLockedStock.entrySet()) {
            Long warehouseId = entry.getKey();

            // 跳过无效仓库或已处理过的仓库
            if (!warehouseNameMap.containsKey(warehouseId) || variantWarehouseStocks.containsKey(warehouseId)) {
                continue;
            }

            int locked = entry.getValue();

            // 已锁定库存就是有效可用库存
            WhsShipmentWarehouseStockVo stockInfo = WhsShipmentWarehouseStockVo.builder()
                .warehouseId(warehouseId)
                .warehouseName(warehouseNameMap.get(warehouseId))
                .effectiveAvailableStock(locked)
                .build();
            warehouseStockInfos.add(stockInfo);

            log.info("变体ID={} 仓库ID={} 只有锁定库存={}", variantId, warehouseId, locked);
        }
    }
}
