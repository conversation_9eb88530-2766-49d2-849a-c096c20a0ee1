package com.imhuso.wholesale.core.domain.bo.front;

import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.wholesale.core.domain.WhsMember;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 商店客户(WhsMember)业务对象
 *
 * <AUTHOR>
 * @since 2025-02-11 16:56:57
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WhsMember.class)
public class MemberBo extends BaseEntity {
    /**
     * 客户ID
     */
    private Long id;

    /**
     * 客户邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 名字
     */
    private String firstName;

    /**
     * 姓氏
     */
    private String lastName;

    /**
     * 手机号码
     */
    @Pattern(regexp = "^\\+?[0-9\\-\\s]+$", message = "手机号码格式不正确")
    private String phone;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 帐号状态（0停用 1正常）
     */
    private String status;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 创建部门
     */
    private Long createDept;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private String delFlag;

    /**
     * 版本号
     */
    private Long version;

}
