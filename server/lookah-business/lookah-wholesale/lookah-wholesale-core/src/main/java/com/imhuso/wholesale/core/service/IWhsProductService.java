package com.imhuso.wholesale.core.service;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductAttributeVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVo;

import java.util.List;

/**
 * 批发产品Service接口
 *
 * <AUTHOR>
 */
public interface IWhsProductService {
    /**
     * 查询产品列表
     *
     * @param bo 产品业务对象
     * @return 产品列表
     */
    List<WhsProductVo> queryList(WhsProductBo bo);

    /**
     * 查询产品分页
     *
     * @param bo        产品业务对象
     * @param pageQuery 分页对象
     * @return 分页结果
     */
    TableDataInfo<WhsProductVo> queryPage(WhsProductBo bo, PageQuery pageQuery);

    /**
     * 新增产品
     *
     * @param bo 产品业务对象
     * @return 结果
     */
    int insertProduct(WhsProductBo bo);

    /**
     * 修改产品
     *
     * @param bo 产品业务对象
     * @return 结果
     */
    int updateProduct(WhsProductBo bo);

    /**
     * 获取产品信息
     *
     * @param id 产品ID
     * @return 产品信息
     */
    WhsProductVo getProductInfo(Long id);

    /**
     * 删除产品信息
     *
     * @param id 产品ID
     * @return 结果
     */
    int deleteProductById(Long id);

    /**
     * 更新产品状态
     *
     * @param id     产品ID
     * @param status 状态值
     * @return 结果
     */
    int updateStatus(Long id, String status);

    /**
     * 根据产品ID获取产品属性列表
     *
     * @param productId 产品ID
     * @return 产品属性列表
     */
    List<WhsProductAttributeVo> getProductAttributesByProductId(Long productId);

    /**
     * 根据产品ID列表批量获取产品基本信息
     *
     * @param productIds 产品ID列表
     * @return 产品信息列表 (只包含基本信息，如ID和名称)
     */
    List<WhsProductVo> getProductsByIds(List<Long> productIds);
}
