package com.imhuso.wholesale.core.strategy.orderNotification.impl;

import com.imhuso.wholesale.core.domain.bo.front.OrderNotifyTemplateBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderVo;
import com.imhuso.wholesale.core.event.WhsOrderEvent.WhsOrderEventType;
import com.imhuso.wholesale.core.service.IInvoiceService;
import com.imhuso.wholesale.core.service.INotificationConfigService;
import com.imhuso.wholesale.core.service.IOrderNotificationSenderService;
import com.imhuso.wholesale.core.service.IPackingSlipService;
import com.imhuso.wholesale.core.service.IWhsOrderShipmentQueryService;
import com.imhuso.wholesale.core.strategy.orderNotification.IOrderNotificationStrategy;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;

import static com.imhuso.wholesale.core.event.WhsOrderEvent.WhsOrderEventType.*;

/**
 * 邮件订单通知策略实现
 * <p>
 * 使用邮件方式发送订单通知
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EmailOrderNotificationStrategy implements IOrderNotificationStrategy {

    private final IOrderNotificationSenderService notificationSender;
    private final IInvoiceService invoiceService;
    private final IPackingSlipService packingSlipService;
    private final INotificationConfigService notificationConfigService;
    private final IWhsOrderShipmentQueryService orderShipmentQueryService;

    @Override
    public String getName() {
        return "邮件";
    }

    /**
     * 订单通知配置枚举
     * 集中管理所有通知配置，使配置更加清晰和可维护
     */
    @Getter
    private enum NotificationConfig {
        // 订单创建：仅通知管理员，使用特殊模板，包含发票
        ORDER_CREATED(true, false, true, false, "order_created",
            (isAdmin, order) -> isAdmin
                ? String.format("Purchase Order %s-%s-%s", order.getCustomerOrderNo(), order.getShippingName(),
                new SimpleDateFormat("yyyy年MM月dd日").format(order.getCreateTime()))
                : null),

        // 订单处理中：通知管理员和客户，使用通用模板
        ORDER_PROCESSING(true, true, false, false, "order_update",
            (isAdmin, order) -> isAdmin ? "订单处理中通知" : "Order Processing"),

        // 订单取消：通知管理员和客户，使用订单取消模板
        ORDER_CANCELED(true, true, false, false, "order_canceled",
            (isAdmin, order) -> isAdmin ? "订单取消通知" : "Order Cancellation"),

        // 订单支付：通知管理员和客户，使用通用模板
        ORDER_PAID(true, true, false, false, "order_update",
            (isAdmin, order) -> isAdmin ? "订单支付通知" : "Payment Confirmation"),

        // 订单部分发货：通知管理员和客户，使用订单发货模板，附带装箱单
        ORDER_PARTIAL_SHIPPED(true, true, false, true, "order_shipped",
            (isAdmin, order) -> isAdmin ? "订单部分发货通知" : "Partial Shipping Confirmation"),

        // 订单发货：通知管理员和客户，使用订单发货模板，附带装箱单
        ORDER_SHIPPED(true, true, false, true, "order_shipped",
            (isAdmin, order) -> isAdmin ? "订单发货通知" : "Shipping Confirmation"),

        // 订单完成：通知管理员和客户，使用通用模板
        ORDER_COMPLETED(true, true, false, false, "order_update",
            (isAdmin, order) -> isAdmin ? "订单完成通知" : "Order Completed"),

        // 发票已发送：通知管理员和客户，使用通用模板
        ORDER_INVOICE_SENT(true, true, false, false, "order_update",
            (isAdmin, order) -> isAdmin ? "发票已发送通知" : "Invoice Sent"),

        // 包裹状态变更：通知管理员和客户，使用通用模板
        PACKAGE_STATUS_CHANGED(true, true, false, false, "order_update",
            (isAdmin, order) -> isAdmin ? "包裹状态更新通知" : "Package Status Update");

        // 是否通知管理员
        private final boolean notifyAdmin;

        // 是否通知客户
        private final boolean notifyCustomer;

        // 是否需要发送发票附件
        private final boolean withInvoice;

        // 是否需要发送装箱单附件
        private final boolean withPackingSlip;

        // 模板基础名称（不包含_admin后缀）
        private final String templateBaseName;

        // 邮件标题获取函数
        private final BiFunction<Boolean, WhsOrderVo, String> titleResolver;

        /**
         * 构造函数
         */
        NotificationConfig(boolean notifyAdmin, boolean notifyCustomer, boolean withInvoice, boolean withPackingSlip,
                           String templateBaseName, BiFunction<Boolean, WhsOrderVo, String> titleResolver) {
            this.notifyAdmin = notifyAdmin;
            this.notifyCustomer = notifyCustomer;
            this.withInvoice = withInvoice;
            this.withPackingSlip = withPackingSlip;
            this.templateBaseName = templateBaseName;
            this.titleResolver = titleResolver;
        }

        /**
         * 获取模板名称
         * - 管理员模板: templateBaseName_admin
         * - 客户模板: templateBaseName
         */
        public String getTemplateName(boolean isAdmin) {
            if (templateBaseName == null) {
                return null;
            }
            return isAdmin ? templateBaseName + "_admin" : templateBaseName;
        }

        /**
         * 获取邮件标题
         */
        public String getTitle(boolean isAdmin, WhsOrderVo order) {
            return titleResolver != null ? titleResolver.apply(isAdmin, order)
                : isAdmin ? "订单状态更新" : "Order Status Update";
        }
    }

    // 事件类型到通知配置的映射
    private final Map<WhsOrderEventType, NotificationConfig> configMap = createConfigMap();

    /**
     * 初始化配置映射
     */
    private Map<WhsOrderEventType, NotificationConfig> createConfigMap() {
        Map<WhsOrderEventType, NotificationConfig> map = new EnumMap<>(WhsOrderEventType.class);
        map.put(ORDER_CREATED, NotificationConfig.ORDER_CREATED);
        map.put(ORDER_PROCESSING, NotificationConfig.ORDER_PROCESSING);
        map.put(ORDER_CANCELED, NotificationConfig.ORDER_CANCELED);
        map.put(ORDER_PAID, NotificationConfig.ORDER_PAID);
        map.put(ORDER_PARTIAL_SHIPPED, NotificationConfig.ORDER_PARTIAL_SHIPPED);
        map.put(ORDER_SHIPPED, NotificationConfig.ORDER_SHIPPED);
        map.put(ORDER_COMPLETED, NotificationConfig.ORDER_COMPLETED);
        map.put(ORDER_INVOICE_SENT, NotificationConfig.ORDER_INVOICE_SENT);
        map.put(PACKAGE_STATUS_CHANGED, NotificationConfig.PACKAGE_STATUS_CHANGED);
        return map;
    }

    @Override
    public boolean isAdminNotificationEnabled(WhsOrderEventType eventType) {
        NotificationConfig config = configMap.get(eventType);
        return config != null && config.isNotifyAdmin();
    }

    @Override
    public boolean isCustomerNotificationEnabled(WhsOrderEventType eventType) {
        NotificationConfig config = configMap.get(eventType);
        return config != null && config.isNotifyCustomer();
    }

    /**
     * 为通知准备装箱单附件
     *
     * @param order 订单信息
     * @param eventType 事件类型
     * @param isAdmin 是否为管理员通知
     * @return 装箱单文件，如果生成失败则返回null
     */
    private File preparePackingSlipAttachment(WhsOrderVo order, WhsOrderEventType eventType, boolean isAdmin) {
        try {
            // 如果是发货事件，确保订单包含发货记录
            if (eventType == WhsOrderEventType.ORDER_SHIPPED || eventType == WhsOrderEventType.ORDER_PARTIAL_SHIPPED) {
                // 确保有发货记录
                ensureShipmentRecords(order, isAdmin);
            }

            // 生成装箱单 - 使用所有发货记录
            // 注意：在WhsPackingSlipServiceImpl中会过滤掉没有有效追踪号的物流包裹
            File packingSlipFile = packingSlipService.generatePackingSlipExcel(order);
            log.info("已为{}订单[{}]生成装箱单附件", isAdmin ? "" : "客户", order.getId());
            return packingSlipFile;
        } catch (Exception e) {
            log.error("生成{}装箱单附件失败: orderId={}, 错误: {}",
                isAdmin ? "" : "客户",
                order.getId(),
                e.getMessage());
            // 返回null，调用方会继续发送通知，即使装箱单生成失败
            return null;
        }
    }

    /**
     * 确保订单包含发货记录
     * 如果发货记录为空，尝试重新查询
     */
    @SuppressWarnings("ConstantConditions")
    private void ensureShipmentRecords(WhsOrderVo order, boolean isAdmin) {
        List<WhsOrderShipmentVo> currentShipments = order.getShipment();
        boolean hasShipments = currentShipments != null && !currentShipments.isEmpty();
        
        if (hasShipments) {
            // 已有发货记录，记录日志
            WhsOrderShipmentVo latestShipment = currentShipments.stream()
                .filter(s -> s.getShippedDate() != null)
                .max(Comparator.comparing(WhsOrderShipmentVo::getShippedDate))
                .orElse(currentShipments.get(currentShipments.size() - 1));

            log.info("为{}订单[{}]找到最新发货记录，ID: {}, 批次: {}",
                isAdmin ? "" : "客户",
                order.getId(),
                latestShipment.getId(),
                latestShipment.getBatchNumber());
        } else {
            // 没有发货记录，尝试重新查询
            log.warn("{}订单[{}]没有发货记录，但收到了发货事件通知，尝试重新查询发货记录",
                isAdmin ? "" : "客户",
                order.getId());
                
            try {
                List<WhsOrderShipmentVo> shipments = orderShipmentQueryService.getOrderShipmentRecords(order.getId());
                if (shipments != null && !shipments.isEmpty()) {
                    order.setShipment(shipments);
                    log.info("{}订单[{}]重新查询到{}条发货记录",
                        isAdmin ? "" : "客户",
                        order.getId(),
                        shipments.size());
                        
                    // 记录最新发货记录信息
                    WhsOrderShipmentVo latestShipment = shipments.stream()
                        .filter(s -> s.getShippedDate() != null)
                        .max(Comparator.comparing(WhsOrderShipmentVo::getShippedDate))
                        .orElse(shipments.get(shipments.size() - 1));
                        
                    log.info("{}订单[{}]最新发货记录，ID: {}, 批次: {}",
                        isAdmin ? "" : "客户",
                        order.getId(),
                        latestShipment.getId(),
                        latestShipment.getBatchNumber());
                } else {
                    log.error("{}订单[{}]重新查询后仍无发货记录，可能存在数据一致性问题",
                        isAdmin ? "" : "客户",
                        order.getId());
                }
            } catch (Exception e) {
                log.error("{}订单[{}]重新查询发货记录失败: {}",
                    isAdmin ? "" : "客户",
                    order.getId(),
                    e.getMessage());
            }
        }
    }

    @Override
    public boolean sendAdminNotification(WhsOrderVo order, WhsOrderEventType eventType) {
        NotificationConfig config = configMap.get(eventType);
        if (config == null || !config.isNotifyAdmin()) {
            log.warn("管理员通知未启用: eventType={}", eventType);
            return false;
        }

        try {
            // 获取管理员邮箱列表，取第一个作为默认管理员邮箱
            List<String> adminEmails = notificationConfigService.getOrderEmailList();
            String adminEmail = adminEmails.isEmpty() ? null : adminEmails.get(0);

            // 准备管理员通知数据
            OrderNotifyTemplateBo templateBo = OrderNotifyTemplateBo.builder()
                .order(order)
                .isAdmin(true)
                .eventType(eventType)
                .toEmail(adminEmail)
                .templateName(config.getTemplateName(true))
                .title(config.getTitle(true, order))
                .build();

            // 准备附件
            File[] attachments = null;

            // 如果需要附加发票，生成并设置发票文件
            if (config.isWithInvoice()) {
                File invoiceFile = invoiceService.generateExcelInvoice(order);
                templateBo.setInvoiceFile(invoiceFile);
                attachments = new File[]{invoiceFile};
            }

            // 如果需要附加装箱单，生成并设置装箱单文件
            if (config.isWithPackingSlip()) {
                File packingSlipFile = preparePackingSlipAttachment(order, eventType, true);
                if (packingSlipFile != null) {
                    // 设置附件
                    if (attachments == null) {
                        attachments = new File[]{packingSlipFile};
                    } else {
                        // 如果已经有发票附件，则创建包含发票和装箱单的数组
                        File[] newAttachments = new File[attachments.length + 1];
                        System.arraycopy(attachments, 0, newAttachments, 0, attachments.length);
                        newAttachments[attachments.length] = packingSlipFile;
                        attachments = newAttachments;
                    }
                }
            }

            // 发送管理员通知
            if (attachments != null) {
                notificationSender.sendOrderAdminNotification(templateBo.toBuilder().attachments(attachments).build());
            } else {
                notificationSender.sendOrderAdminNotification(templateBo);
            }

            log.info("已发送订单管理员邮件通知: orderId={}, eventType={}, 模板={}, 附件数量={}",
                order.getId(), eventType, config.getTemplateName(true), attachments != null ? attachments.length : 0);
            return true;
        } catch (Exception e) {
            log.error("发送订单管理员邮件通知失败: orderId={}", order.getId(), e);
            return false;
        }
    }

    @Override
    public boolean sendCustomerNotification(WhsOrderVo order, WhsOrderEventType eventType) {
        NotificationConfig config = configMap.get(eventType);
        if (config == null || !config.isNotifyCustomer()) {
            log.warn("客户通知未启用: eventType={}", eventType);
            return false;
        }

        try {
            // 验证客户邮箱
            String customerEmail = order.getShippingEmail();
            if (customerEmail == null || customerEmail.isEmpty()) {
                log.warn("订单客户邮箱为空，无法发送通知: orderId={}", order.getId());
                return false;
            }

            // 准备客户通知数据
            OrderNotifyTemplateBo templateBo = OrderNotifyTemplateBo.builder()
                .order(order)
                .isAdmin(false)
                .eventType(eventType)
                .toEmail(customerEmail)
                .templateName(config.getTemplateName(false))
                .title(config.getTitle(false, order))
                .build();

            // 准备附件
            File[] attachments = null;

            // 如果需要附加装箱单，生成并设置装箱单文件
            if (config.isWithPackingSlip()) {
                File packingSlipFile = preparePackingSlipAttachment(order, eventType, false);
                if (packingSlipFile != null) {
                    // 设置附件
                    attachments = new File[]{packingSlipFile};
                }
            }

            // 发送客户通知
            if (attachments != null) {
                notificationSender.sendOrderCustomerNotification(templateBo.toBuilder().attachments(attachments).build());
            } else {
                notificationSender.sendOrderCustomerNotification(templateBo);
            }

            log.info("已发送订单客户邮件通知: orderId={}, eventType={}, email={}, 模板={}, 附件数量={}",
                order.getId(), eventType, customerEmail, config.getTemplateName(false),
                attachments != null ? attachments.length : 0);
            return true;
        } catch (Exception e) {
            log.error("发送订单客户邮件通知失败: orderId={}", order.getId(), e);
            return false;
        }
    }
}
