package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 海外仓账号对象 whs_overseas_warehouse_account
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_overseas_warehouse_account")
public class WhsOverseasWarehouseAccount extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 账号ID
     */
    @TableId
    private Long id;

    /**
     * 账号名称
     */
    private String name;

    /**
     * 账号编码
     */
    private String code;

    /**
     * 关联的提供商ID
     */
    private Long providerId;

    /**
     * 账号配置信息(加密JSON)
     */
    private String accountConfig;

    /**
     * 状态（0停用 1启用）
     */
    private String status;

    /**
     * 回调URL
     */
    private String callbackUrl;

    /**
     * 最后同步时间
     */
    private Date lastSyncTime;

    /**
     * 是否默认账号（N否 Y是）
     */
    private String isDefault;

    /**
     * 是否支持签名服务（0不支持 1支持）
     */
    private String supportSignatureService;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 乐观锁版本号
     */
    private Integer version;
}
