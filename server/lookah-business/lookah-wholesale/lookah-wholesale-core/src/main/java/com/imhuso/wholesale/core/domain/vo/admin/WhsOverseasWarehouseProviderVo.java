package com.imhuso.wholesale.core.domain.vo.admin;

import com.imhuso.wholesale.core.domain.WhsOverseasWarehouseProvider;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 海外仓提供商视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsOverseasWarehouseProvider.class)
public class WhsOverseasWarehouseProviderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 提供商ID
     */
    private Long id;

    /**
     * 提供商名称
     */
    private String name;

    /**
     * 提供商编码
     */
    private String code;

    /**
     * 提供商类型
     */
    private String providerType;

    /**
     * 状态（0停用 1启用）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
