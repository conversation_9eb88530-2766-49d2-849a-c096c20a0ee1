package com.imhuso.wholesale.core.domain.bo.front;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.wholesale.core.domain.WhsShippingAddress;
import com.imhuso.wholesale.core.validate.ValidPostalCode;
import com.imhuso.wholesale.core.validate.ValidRecipientName;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 批发收货地址业务对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsShippingAddress.class)
@NoArgsConstructor
@ValidPostalCode(groups = {AddGroup.class, EditGroup.class})
@ValidRecipientName(groups = {AddGroup.class, EditGroup.class})
public class ShippingAddressBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 地址ID
     */
    @NotNull(message = "wholesale.address.id.not.null", groups = EditGroup.class)
    private Long id;

    /**
     * 名
     */
    @NotBlank(message = "wholesale.address.first.name.not.blank", groups = {AddGroup.class, EditGroup.class})
    @Size(min = 2, max = 50, message = "wholesale.address.first.name.length", groups = {AddGroup.class, EditGroup.class})
    private String firstName;

    /**
     * 姓
     */
    @NotBlank(message = "wholesale.address.last.name.not.blank", groups = {AddGroup.class, EditGroup.class})
    @Size(min = 2, max = 50, message = "wholesale.address.last.name.length", groups = {AddGroup.class, EditGroup.class})
    private String lastName;

    /**
     * 联系电话
     */
    @NotBlank(message = "wholesale.address.phone.not.blank", groups = {AddGroup.class, EditGroup.class})
    @Pattern(regexp = "^(\\+?1[ -]?)?(\\([0-9]{3}\\)|[0-9]{3})[ -]?[0-9]{3}[ -]?[0-9]{4}$", message = "wholesale.address.phone.not.valid", groups = {
        AddGroup.class, EditGroup.class})
    private String phone;

    /**
     * 联系邮箱
     */
    @NotBlank(message = "wholesale.address.email.not.blank", groups = {AddGroup.class, EditGroup.class})
    @Email(regexp = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", message = "wholesale.address.email.not.valid", groups = {
        AddGroup.class, EditGroup.class})
    @Size(max = 100, message = "wholesale.address.email.length", groups = {AddGroup.class, EditGroup.class})
    private String email;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 国家
     */
    @NotBlank(message = "wholesale.address.country.not.blank", groups = {AddGroup.class, EditGroup.class})
    @Size(min = 2, max = 50, message = "wholesale.address.country.length", groups = {AddGroup.class, EditGroup.class})
    private String country;

    /**
     * 州
     */
    @NotBlank(message = "wholesale.address.state.not.blank", groups = {AddGroup.class, EditGroup.class})
    @Pattern(regexp = "^[A-Z]{2}$", message = "wholesale.address.state.not.valid", groups = {AddGroup.class,
        EditGroup.class})
    private String state;

    /**
     * 城市
     */
    @NotBlank(message = "wholesale.address.city.not.blank", groups = {AddGroup.class, EditGroup.class})
    @Size(min = 2, max = 50, message = "wholesale.address.city.length", groups = {AddGroup.class, EditGroup.class})
    private String city;

    /**
     * 地址行1
     */
    @NotBlank(message = "wholesale.address.line1.not.blank", groups = {AddGroup.class, EditGroup.class})
    @Size(min = 5, max = 100, message = "wholesale.address.line1.length", groups = {AddGroup.class, EditGroup.class})
    private String addressLine1;

    /**
     * 地址行2（公寓、套房、单元等）
     */
    @Size(max = 100, message = "wholesale.address.line2.length", groups = {AddGroup.class, EditGroup.class})
    private String addressLine2;

    /**
     * 邮政编码
     */
    @NotBlank(message = "wholesale.address.zip.code.not.blank", groups = {AddGroup.class, EditGroup.class})
    private String zipCode;

    /**
     * 送货说明
     */
    @Size(max = 500, message = "wholesale.address.delivery.notes.length", groups = {AddGroup.class, EditGroup.class})
    private String deliveryNotes;

    /**
     * 是否默认地址（N否 Y是)
     */
    private String isDefault;
}
