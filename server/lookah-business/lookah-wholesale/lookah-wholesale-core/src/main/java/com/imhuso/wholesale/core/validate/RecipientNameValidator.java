package com.imhuso.wholesale.core.validate;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.lang.reflect.Field;
import lombok.extern.slf4j.Slf4j;

/**
 * 收件人姓名长度验证器
 * 验证lastName + " " + firstName的总长度不超过指定限制
 * 
 * <AUTHOR>
 */
@Slf4j
public class RecipientNameValidator implements ConstraintValidator<ValidRecipientName, Object> {
    
    private int maxLength;
    
    public RecipientNameValidator() {
        log.debug("RecipientNameValidator instantiated");
    }
    
    @Override
    public void initialize(ValidRecipientName constraintAnnotation) {
        this.maxLength = constraintAnnotation.maxLength();
        log.debug("RecipientNameValidator initialized with maxLength: {}", maxLength);
    }
    
    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        log.debug("RecipientNameValidator.isValid called with value: {}", value);
        
        if (value == null) {
            log.debug("Value is null, returning true");
            return true;
        }
        
        try {
            // 使用反射获取firstName和lastName字段的值
            Field firstNameField = findField(value.getClass(), "firstName");
            Field lastNameField = findField(value.getClass(), "lastName");
            
            log.debug("Found fields - firstName: {}, lastName: {}", firstNameField != null, lastNameField != null);
            
            if (firstNameField == null || lastNameField == null) {
                return true;
            }
            
            firstNameField.setAccessible(true);
            lastNameField.setAccessible(true);
            
            String firstName = (String) firstNameField.get(value);
            String lastName = (String) lastNameField.get(value);
            
            log.debug("Extracted values - firstName: '{}', lastName: '{}'", firstName, lastName);
            
            // 如果firstName或lastName为空，交给@NotBlank注解处理
            if (firstName == null || lastName == null || firstName.isEmpty() || lastName.isEmpty()) {
                log.debug("First name or last name is null/empty, returning true");
                return true;
            }
            
            // 计算RecipientName的长度: lastName + " " + firstName
            int totalLength = lastName.length() + 1 + firstName.length();
            
            log.debug("RecipientName validation - lastName: '{}' ({}), firstName: '{}' ({}), total: {}/{}", 
                lastName, lastName.length(), firstName, firstName.length(), totalLength, maxLength);
            
            if (totalLength > maxLength) {
                // 自定义错误消息，提供更详细的信息
                String errorMessage = String.format("收件人姓名总长度不能超过%d个字符，当前长度为%d（姓:%d + 空格:1 + 名:%d）", 
                    maxLength, totalLength, lastName.length(), firstName.length());
                log.warn("Validation failed: {}", errorMessage);
                
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(errorMessage).addConstraintViolation();
                return false;
            }
            
            log.debug("Validation passed: total length {} <= {}", totalLength, maxLength);
            return true;
            
        } catch (IllegalAccessException e) {
            // 记录详细的异常信息，便于问题排查
            log.warn("Failed to access fields for recipient name validation on class {}: {}", 
                    value.getClass().getSimpleName(), e.getMessage());
            log.debug("Full exception details for recipient name validation", e);
            
            // 如果无法获取字段值，返回true（不阻塞验证，但已记录问题）
            return true;
        } catch (Exception e) {
            // 捕获其他可能的异常，确保验证过程不会崩溃
            log.error("Unexpected error during recipient name validation on class {}: {}", 
                     value.getClass().getSimpleName(), e.getMessage());
            log.debug("Full exception details for unexpected validation error", e);
            
            // 出现未预期的异常时，为了安全起见返回false（防止无效数据通过验证）
            return false;
        }
    }
    
    /**
     * 递归查找字段，包括父类中的字段
     */
    private Field findField(Class<?> clazz, String fieldName) {
        Class<?> searchType = clazz;
        while (searchType != null && searchType != Object.class) {
            try {
                return searchType.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                // 继续在父类中查找
                searchType = searchType.getSuperclass();
            }
        }
        return null;
    }
}