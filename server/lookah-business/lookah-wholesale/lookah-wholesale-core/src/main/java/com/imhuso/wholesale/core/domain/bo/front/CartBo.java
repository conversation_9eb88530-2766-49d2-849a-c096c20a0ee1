package com.imhuso.wholesale.core.domain.bo.front;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 批发购物车业务对象
 *
 * <AUTHOR>
 */
@Data
public class CartBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 购物车项列表
     */
    @NotEmpty(message = "wholesale.cart.items.not.empty")
    @Valid
    private List<CartItemBo> items;
}
