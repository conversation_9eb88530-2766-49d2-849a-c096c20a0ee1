package com.imhuso.wholesale.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.WhsProductAttributeValue;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductAttributeValueBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductAttributeValueVo;
import com.imhuso.wholesale.core.mapper.WhsProductAttributeValueMapper;
import com.imhuso.wholesale.core.service.IWhsProductAttributeValueService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 产品属性值Service业务层处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class WhsProductAttributeValueServiceImpl implements IWhsProductAttributeValueService {

    private final WhsProductAttributeValueMapper attributeValueMapper;

    @Override
    public TableDataInfo<WhsProductAttributeValueVo> queryPage(WhsProductAttributeValueBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WhsProductAttributeValue> lqw = buildQueryWrapper(bo);
        Page<WhsProductAttributeValueVo> result = attributeValueMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public WhsProductAttributeValueVo getById(Long id) {
        return attributeValueMapper.selectVoById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertAttributeValue(WhsProductAttributeValueBo bo) {
        // 检查值键是否已存在
        if (valueKeyExists(bo.getValueKey(), bo.getAttributeId(), null)) {
            throw new ServiceException("属性值键已存在");
        }
        WhsProductAttributeValue attributeValue = MapstructUtils.convert(bo, WhsProductAttributeValue.class);
        return attributeValueMapper.insert(attributeValue);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateAttributeValue(WhsProductAttributeValueBo bo) {
        // 检查值键是否已存在
        if (valueKeyExists(bo.getValueKey(), bo.getAttributeId(), bo.getId())) {
            throw new ServiceException("属性值键已存在");
        }
        WhsProductAttributeValue attributeValue = MapstructUtils.convert(bo, WhsProductAttributeValue.class);
        return attributeValueMapper.updateById(attributeValue);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteAttributeValueByIds(Long[] ids) {
        // 执行删除操作
        return attributeValueMapper.deleteByIds(Arrays.asList(ids));
    }

    @Override
    public List<WhsProductAttributeValueVo> listByAttributeId(Long attributeId) {
        LambdaQueryWrapper<WhsProductAttributeValue> lqw = new LambdaQueryWrapper<>();
        lqw.eq(attributeId != null, WhsProductAttributeValue::getAttributeId, attributeId)
           .orderByAsc(WhsProductAttributeValue::getSort);
        return attributeValueMapper.selectVoList(lqw);
    }

    /**
     * 检查值键是否已存在
     *
     * @param valueKey    值键
     * @param attributeId 属性ID
     * @param id          排除的ID（更新时使用）
     * @return 存在返回true，不存在返回false
     */
    @Override
    public boolean valueKeyExists(String valueKey, Long attributeId, Long id) {
        LambdaQueryWrapper<WhsProductAttributeValue> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StringUtils.isNotBlank(valueKey), WhsProductAttributeValue::getValueKey, valueKey)
           .eq(attributeId != null, WhsProductAttributeValue::getAttributeId, attributeId)
           .ne(id != null, WhsProductAttributeValue::getId, id);
        return attributeValueMapper.selectCount(lqw) > 0;
    }

    /**
     * 构建查询条件
     *
     * @param bo 查询参数
     * @return 查询条件构造器
     */
    private LambdaQueryWrapper<WhsProductAttributeValue> buildQueryWrapper(WhsProductAttributeValueBo bo) {
        LambdaQueryWrapper<WhsProductAttributeValue> lqw = new LambdaQueryWrapper<>();
        lqw.eq(bo.getAttributeId() != null, WhsProductAttributeValue::getAttributeId, bo.getAttributeId())
           .eq(StringUtils.isNotBlank(bo.getStatus()), WhsProductAttributeValue::getStatus, bo.getStatus())
           .like(StringUtils.isNotBlank(bo.getValueDisplay()), WhsProductAttributeValue::getValueDisplay, bo.getValueDisplay())
           .like(StringUtils.isNotBlank(bo.getValueKey()), WhsProductAttributeValue::getValueKey, bo.getValueKey())
           .orderByAsc(WhsProductAttributeValue::getSort);
        return lqw;
    }
}
