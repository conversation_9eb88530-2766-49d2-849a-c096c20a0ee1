package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品属性关联对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_product_attribute_relation")
public class WhsProductAttributeRelation extends BaseEntity {

    /**
     * 关联ID
     */
    private Long id;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 属性ID
     */
    private Long attributeId;

    /**
     * 排序
     */
    private Integer sortOrder;
}
