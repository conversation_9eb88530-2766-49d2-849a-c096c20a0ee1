package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.wholesale.core.domain.WhsOrder;
import com.imhuso.wholesale.core.domain.WhsOrderStatusLog;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderStatusLogVo;
import com.imhuso.wholesale.core.enums.OrderOperationType;
import com.imhuso.wholesale.core.enums.OrderStatus;
import com.imhuso.wholesale.core.event.WhsOrderEvent.WhsOrderEventType;
import com.imhuso.wholesale.core.mapper.WhsOrderStatusLogMapper;
import com.imhuso.wholesale.core.service.IWhsOrderStatusLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单状态日志Service实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WhsOrderStatusLogServiceImpl implements IWhsOrderStatusLogService {
    private final WhsOrderStatusLogMapper statusLogMapper;

    /**
     * 创建订单状态变更日志
     * 根据eventType参数是否为null，使用不同的方式确定操作类型
     */
    @Override
    public void createStatusLog(WhsOrder order, Integer newStatus, Integer oldStatus, String remark, Long operatorId,
                                WhsOrderEventType eventType) {
        log.debug("记录订单状态变更日志: orderId={}, newStatus={}, oldStatus={}, remark={}, operatorId={}, eventType={}",
            order.getId(), newStatus, oldStatus, remark, operatorId, eventType);

        WhsOrderStatusLog statusLog = new WhsOrderStatusLog();
        statusLog.setOrderId(order.getId());
        statusLog.setOrderStatus(newStatus);
        statusLog.setPaymentStatus(order.getPaymentStatus());
        statusLog.setShipmentStatus(order.getShipmentStatus());
        statusLog.setInvoiceStatus(order.getInvoiceStatus());
        statusLog.setBeforeStatus(oldStatus);

        // 根据事件类型或状态确定操作类型
        int operationType;
        if (eventType != null) {
            // 如果提供了事件类型，根据事件类型确定操作类型
            operationType = getOperationTypeByEvent(eventType);
        } else {
            // 如果没有提供事件类型，根据状态确定操作类型
            operationType = getOperationType(newStatus);
        }

        statusLog.setOperationType(operationType);
        statusLog.setRemark(remark);

        // 手动设置操作人员ID，因为在异步线程中自动填充会丢失用户上下文
        if (ObjectUtil.isNotNull(operatorId) && operatorId > 0) {
            statusLog.setCreateBy(operatorId);
            statusLog.setUpdateBy(operatorId);
        } else {
            // 如果没有提供操作人员ID，设置默认值0表示系统操作
            statusLog.setCreateBy(0L);
            statusLog.setUpdateBy(0L);
        }

        statusLogMapper.insert(statusLog);
    }

    /**
     * 根据新状态获取操作类型
     */
    private static int getOperationType(Integer newStatus) {
        int operationType = OrderOperationType.CREATE.getValue();
        if (newStatus == null) {
            return operationType;
        }

        int statusCode = newStatus;
        if (statusCode == OrderStatus.CANCELED.getValue()) {
            operationType = OrderOperationType.CANCEL.getValue();
        } else if (statusCode == OrderStatus.PROCESSING.getValue()) {
            operationType = OrderOperationType.PROCESS.getValue();
        } else if (statusCode == OrderStatus.COMPLETED.getValue()) {
            operationType = OrderOperationType.COMPLETE.getValue();
        }
        return operationType;
    }

    /**
     * 根据事件类型获取操作类型
     *
     * @param eventType 事件类型
     * @return 操作类型值
     */
    private static int getOperationTypeByEvent(WhsOrderEventType eventType) {
        if (eventType == null) {
            return OrderOperationType.CREATE.getValue();
        }

        return switch (eventType) {
            case ORDER_PAID -> OrderOperationType.PAYMENT.getValue();
            case ORDER_CANCELED -> OrderOperationType.CANCEL.getValue();
            case ORDER_PROCESSING -> OrderOperationType.PROCESS.getValue();
            case ORDER_SHIPPED -> OrderOperationType.SHIP.getValue();
            case ORDER_PARTIAL_SHIPPED -> OrderOperationType.PARTIAL_SHIP.getValue();
            case ORDER_COMPLETED -> OrderOperationType.COMPLETE.getValue();
            case ORDER_REVERTED -> OrderOperationType.REVERT.getValue();
            default -> OrderOperationType.CREATE.getValue();
        };
    }

    @Override
    public List<WhsOrderStatusLogVo> getOrderStatusLogs(Long orderId) {
        return statusLogMapper.selectVoList(new LambdaQueryWrapper<WhsOrderStatusLog>()
            .eq(WhsOrderStatusLog::getOrderId, orderId).orderByDesc(WhsOrderStatusLog::getCreateTime));
    }
}
