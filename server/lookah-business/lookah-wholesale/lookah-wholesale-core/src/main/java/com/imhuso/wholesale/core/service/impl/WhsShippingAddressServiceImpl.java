package com.imhuso.wholesale.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.WhsShippingAddress;
import com.imhuso.wholesale.core.domain.bo.admin.WhsShippingAddressBo;
import com.imhuso.wholesale.core.domain.bo.front.ShippingAddressBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShippingAddressVo;
import com.imhuso.wholesale.core.domain.vo.front.CountryVo;
import com.imhuso.wholesale.core.domain.vo.front.ShippingAddressVo;
import com.imhuso.wholesale.core.domain.vo.front.StateVo;
import com.imhuso.wholesale.core.mapper.WhsShippingAddressMapper;
import com.imhuso.wholesale.core.satoken.WholesaleLoginHelper;
import com.imhuso.wholesale.core.service.IWhsCountryService;
import com.imhuso.wholesale.core.service.IWhsShippingAddressService;
import com.imhuso.wholesale.core.service.IWhsStateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 收货地址服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsShippingAddressServiceImpl implements IWhsShippingAddressService {

    private final WhsShippingAddressMapper baseMapper;
    private final IWhsCountryService countryService;
    private final IWhsStateService stateService;

    // ==================== 前台接口实现 ====================

    @Override
    public List<ShippingAddressVo> selectAddressList() {
        return baseMapper.selectVoList(
            buildQueryWrapper(WholesaleLoginHelper.getUserId(), null, true),
            ShippingAddressVo.class);
    }

    @Override
    public ShippingAddressVo selectAddressById(Long addressId) {
        return baseMapper.selectVoOne(
            buildQueryWrapper(WholesaleLoginHelper.getUserId(), addressId, false),
            ShippingAddressVo.class);
    }

    @Override
    public ShippingAddressVo getDefaultAddress() {
        LambdaQueryWrapper<WhsShippingAddress> lqw = Wrappers.lambdaQuery(WhsShippingAddress.class)
            .eq(WhsShippingAddress::getMemberId, WholesaleLoginHelper.getUserId())
            .eq(WhsShippingAddress::getIsDefault, BusinessConstants.YES);
        return baseMapper.selectVoOne(lqw, ShippingAddressVo.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createAddress(ShippingAddressBo bo) {
        Long memberId = WholesaleLoginHelper.getUserId();


        // 校验国家和州/省代码
        validateCountryAndState(bo.getCountry(), bo.getState());

        // 处理对象并保存
        WhsShippingAddress address = MapstructUtils.convert(bo, WhsShippingAddress.class);
        address.setMemberId(memberId);

        // 设置国家名称和州名称
        setRegionNames(address);

        // 处理默认地址
        handleDefaultAddress(memberId, null, BusinessConstants.YES.equals(bo.getIsDefault()));

        baseMapper.insert(address);
        return address.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAddress(ShippingAddressBo bo) {
        Long memberId = WholesaleLoginHelper.getUserId();

        // 验证地址是否存在且属于当前用户
        verifyAddressOwnership(memberId, bo.getId());

        // 校验国家和州/省代码
        validateCountryAndState(bo.getCountry(), bo.getState());

        // 处理对象并更新
        WhsShippingAddress address = MapstructUtils.convert(bo, WhsShippingAddress.class);
        address.setMemberId(memberId); // 确保memberId不被修改

        // 设置国家名称和州名称
        setRegionNames(address);

        // 处理默认地址
        handleDefaultAddress(memberId, bo.getId(), BusinessConstants.YES.equals(bo.getIsDefault()));

        baseMapper.updateById(address);
    }

    @Override
    public void deleteAddress(Long addressId) {
        // 确保只删除当前用户的地址
        baseMapper.delete(
            Wrappers.lambdaQuery(WhsShippingAddress.class)
                .eq(WhsShippingAddress::getMemberId, WholesaleLoginHelper.getUserId())
                .eq(WhsShippingAddress::getId, addressId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDefaultAddress(Long addressId) {
        Long memberId = WholesaleLoginHelper.getUserId();

        // 验证地址是否存在且属于当前用户
        verifyAddressOwnership(memberId, addressId);

        // 将当前地址设置为默认
        WhsShippingAddress address = new WhsShippingAddress();
        address.setId(addressId);
        address.setIsDefault(BusinessConstants.YES);
        baseMapper.updateById(address);

        // 将其他地址设置为非默认
        resetDefaultAddress(memberId, addressId);
    }

    // ==================== 后台接口实现 ====================

    @Override
    public TableDataInfo<WhsShippingAddressVo> queryPageList(WhsShippingAddressBo bo, PageQuery pageQuery) {
        Page<WhsShippingAddressVo> result = baseMapper.selectVoPage(
            pageQuery.build(),
            buildAdminQueryWrapper(bo),
            WhsShippingAddressVo.class);

        // 处理结果数据，填充会员信息
        enrichMemberInfo(result.getRecords());

        return TableDataInfo.build(result);
    }

    @Override
    public WhsShippingAddressVo selectAddressDetailById(Long addressId) {
        WhsShippingAddressVo vo = baseMapper.selectVoById(addressId, WhsShippingAddressVo.class);
        if (vo != null) {
            enrichMemberInfo(List.of(vo));
        }
        return vo;
    }

    @Override
    public List<WhsShippingAddressVo> getAddressesByMemberId(Long memberId) {
        return baseMapper.selectVoList(
            buildQueryWrapper(memberId, null, true),
            WhsShippingAddressVo.class);
    }

    @Override
    public TableDataInfo<WhsShippingAddressVo> getAddressesByMemberIdPage(Long memberId, PageQuery pageQuery) {
        Page<WhsShippingAddressVo> result = baseMapper.selectVoPage(
            pageQuery.build(),
            buildQueryWrapper(memberId, null, true),
            WhsShippingAddressVo.class);
        return TableDataInfo.build(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createAddress(WhsShippingAddressBo bo) {
        // 校验国家和州/省代码
        validateCountryAndState(bo.getCountry(), bo.getState());

        WhsShippingAddress address = MapstructUtils.convert(bo, WhsShippingAddress.class);

        // 设置国家名称和州名称
        setRegionNames(address);

        // 处理默认地址
        handleDefaultAddress(bo.getMemberId(), null, BusinessConstants.YES.equals(bo.getIsDefault()));

        return baseMapper.insert(address);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateAddress(WhsShippingAddressBo bo) {
        // 校验国家和州/省代码
        validateCountryAndState(bo.getCountry(), bo.getState());

        WhsShippingAddress address = MapstructUtils.convert(bo, WhsShippingAddress.class);

        // 设置国家名称和州名称
        setRegionNames(address);

        // 处理默认地址
        handleDefaultAddress(bo.getMemberId(), bo.getId(), BusinessConstants.YES.equals(bo.getIsDefault()));

        return baseMapper.updateById(address);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteAddressById(Long addressId) {
        return baseMapper.deleteById(addressId);
    }

    // ==================== 工具方法 ====================

    /**
     * 构建基础查询条件
     *
     * @param memberId  会员ID
     * @param addressId 地址ID (可选)
     * @param withOrder 是否添加排序
     * @return 查询条件
     */
    private LambdaQueryWrapper<WhsShippingAddress> buildQueryWrapper(Long memberId, Long addressId,
                                                                     boolean withOrder) {
        LambdaQueryWrapper<WhsShippingAddress> lqw = Wrappers.lambdaQuery(WhsShippingAddress.class)
            .eq(WhsShippingAddress::getMemberId, memberId);

        if (addressId != null) {
            lqw.eq(WhsShippingAddress::getId, addressId);
        }

        if (withOrder) {
            lqw.orderByDesc(WhsShippingAddress::getIsDefault)
                .orderByDesc(WhsShippingAddress::getCreateTime);
        }

        return lqw;
    }

    /**
     * 构建后台查询条件
     */
    private LambdaQueryWrapper<WhsShippingAddress> buildAdminQueryWrapper(WhsShippingAddressBo bo) {
        LambdaQueryWrapper<WhsShippingAddress> lqw = Wrappers.lambdaQuery();

        lqw.eq(bo.getId() != null, WhsShippingAddress::getId, bo.getId());
        lqw.eq(bo.getMemberId() != null, WhsShippingAddress::getMemberId, bo.getMemberId());
        lqw.like(StringUtils.isNotBlank(bo.getFirstName()), WhsShippingAddress::getFirstName, bo.getFirstName());
        lqw.like(StringUtils.isNotBlank(bo.getLastName()), WhsShippingAddress::getLastName, bo.getLastName());
        lqw.like(StringUtils.isNotBlank(bo.getPhone()), WhsShippingAddress::getPhone, bo.getPhone());
        lqw.like(StringUtils.isNotBlank(bo.getEmail()), WhsShippingAddress::getEmail, bo.getEmail());
        lqw.eq(StringUtils.isNotBlank(bo.getCountry()), WhsShippingAddress::getCountry, bo.getCountry());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), WhsShippingAddress::getState, bo.getState());
        lqw.like(StringUtils.isNotBlank(bo.getCity()), WhsShippingAddress::getCity, bo.getCity());
        lqw.eq(StringUtils.isNotBlank(bo.getIsDefault()), WhsShippingAddress::getIsDefault, bo.getIsDefault());
        lqw.orderByDesc(WhsShippingAddress::getCreateTime);

        return lqw;
    }

    /**
     * 为地址列表补充会员信息
     */
    private void enrichMemberInfo(List<WhsShippingAddressVo> addresses) {
        if (addresses == null || addresses.isEmpty()) {
            return;
        }

        addresses.forEach(vo -> {
            // 补充国家和州省名称 - 如果数据库中的字段为空时才查询
            if (StringUtils.isEmpty(vo.getCountryName()) && StringUtils.isNotEmpty(vo.getCountry())) {
                var country = countryService.getCountryByCode(vo.getCountry(), CountryVo.class);
                if (country != null) {
                    vo.setCountryName(country.getName());

                    // 补充州省名称
                    if (StringUtils.isEmpty(vo.getStateName()) && StringUtils.isNotEmpty(vo.getState())
                        && country.getId() != null) {
                        var states = stateService.getStatesByCountryId(country.getId(), StateVo.class);
                        states.stream()
                            .filter(s -> vo.getState().equals(s.getCode()))
                            .findFirst().ifPresent(state -> vo.setStateName(state.getName()));
                    }
                }
            }
        });
    }

    /**
     * 验证地址是否属于指定会员
     */
    private void verifyAddressOwnership(Long memberId, Long addressId) {
        long count = baseMapper.selectCount(buildQueryWrapper(memberId, addressId, false));
        if (count == 0) {
            throw new ServiceException(MessageUtils.message("wholesale.address.not.exists"));
        }
    }

    /**
     * 处理默认地址逻辑
     *
     * @param memberId         会员ID
     * @param excludeAddressId 排除的地址ID (可选)
     * @param isDefault        是否设为默认
     */
    private void handleDefaultAddress(Long memberId, Long excludeAddressId, boolean isDefault) {
        if (isDefault) {
            if (excludeAddressId != null) {
                resetDefaultAddress(memberId, excludeAddressId);
            } else {
                resetDefaultAddress(memberId);
            }
        }
    }

    /**
     * 校验国家代码和州/省代码是否有效
     */
    private void validateCountryAndState(String countryCode, String stateCode) {
        if (StringUtils.isEmpty(countryCode)) {
            throw new ServiceException(MessageUtils.message("wholesale.address.country.not.blank"));
        }

        // 校验国家代码是否有效，使用前台CountryVo类型
        var country = countryService.getCountryByCode(countryCode, CountryVo.class);
        if (country == null) {
            log.warn("无效的国家代码: {}", countryCode);
            throw new ServiceException(MessageUtils.message("wholesale.address.country.not.valid"));
        }

        if (StringUtils.isEmpty(stateCode)) {
            throw new ServiceException(MessageUtils.message("wholesale.address.state.not.blank"));
        }

        // 校验州/省代码是否有效，使用前台的StateVo类型
        var state = stateService.getStatesByCountryId(country.getId(), StateVo.class)
            .stream()
            .filter(s -> s.getCode().equals(stateCode))
            .findFirst()
            .orElse(null);
        if (state == null) {
            log.warn("无效的州/省代码: {}, 国家ID: {}", stateCode, country.getId());
            throw new ServiceException(MessageUtils.message("wholesale.address.state.not.valid"));
        }
    }

    /**
     * 重置会员的默认地址
     */
    private void resetDefaultAddress(Long memberId) {
        LambdaUpdateWrapper<WhsShippingAddress> luw = Wrappers.lambdaUpdate();
        luw.eq(WhsShippingAddress::getMemberId, memberId);
        luw.set(WhsShippingAddress::getIsDefault, BusinessConstants.NO);
        baseMapper.update(null, luw);
    }

    /**
     * 重置会员的默认地址（排除指定地址）
     */
    private void resetDefaultAddress(Long memberId, Long excludeAddressId) {
        LambdaUpdateWrapper<WhsShippingAddress> luw = Wrappers.lambdaUpdate();
        luw.eq(WhsShippingAddress::getMemberId, memberId);
        luw.ne(WhsShippingAddress::getId, excludeAddressId);
        luw.set(WhsShippingAddress::getIsDefault, BusinessConstants.NO);
        baseMapper.update(null, luw);
    }

    /**
     * 设置国家名称和州名称
     */
    private void setRegionNames(WhsShippingAddress address) {
        if (StringUtils.isNotEmpty(address.getCountry())) {
            var country = countryService.getCountryByCode(address.getCountry(), CountryVo.class);
            if (country != null) {
                address.setCountryName(country.getName());

                // 设置州名称
                if (StringUtils.isNotEmpty(address.getState()) && country.getId() != null) {
                    var states = stateService.getStatesByCountryId(country.getId(), StateVo.class);
                    states.stream()
                        .filter(s -> address.getState().equals(s.getCode()))
                        .findFirst().ifPresent(state -> address.setStateName(state.getName()));
                }
            }
        }
    }
}
