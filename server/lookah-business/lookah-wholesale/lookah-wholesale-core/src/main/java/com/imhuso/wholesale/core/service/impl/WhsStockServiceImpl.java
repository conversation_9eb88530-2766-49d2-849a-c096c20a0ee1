package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.WhsStock;
import com.imhuso.wholesale.core.domain.bo.admin.WhsStockBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsStockSummaryBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsStockSummaryVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsStockVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsVariantStockByWarehouseVo;
import com.imhuso.wholesale.core.mapper.WhsStockMapper;
import com.imhuso.wholesale.core.mapper.WhsWarehouseMapper;
import com.imhuso.wholesale.core.service.IWhsStockService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 库存Service实现类
 * <p>
 * 提供前台和后台的库存管理功能
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsStockServiceImpl implements IWhsStockService {

    private final WhsStockMapper stockMapper;
    private final WhsWarehouseMapper warehouseMapper;

    // 查询相关方法 ----------------------------------------
    @Override
    public Map<Long, Integer> getAvailableStocks(List<Long> variantIds) {
        if (CollUtil.isEmpty(variantIds)) {
            return Collections.emptyMap();
        }

        Map<Long, Integer> result = new HashMap<>(variantIds.size());

        try {
            // 直接从数据库查询库存，确保数据准确性
            List<WhsStock> stockList = stockMapper.selectList(
                new LambdaQueryWrapper<WhsStock>()
                    .in(WhsStock::getVariantId, variantIds)
            );

            // 按变体ID聚合可用库存（跨所有仓库）
            Map<Long, Integer> stockMap = stockList.stream()
                .collect(Collectors.groupingBy(
                    WhsStock::getVariantId,
                    Collectors.summingInt(stock -> {
                        Integer availableStock = stock.getAvailableStock();
                        return Math.max(0, availableStock != null ? availableStock : 0);
                    })
                ));

            // 确保所有请求的变体都有结果，没有库存的设为0
            for (Long variantId : variantIds) {
                result.put(variantId, stockMap.getOrDefault(variantId, 0));
            }

        } catch (Exception e) {
            log.error("批量查询变体库存时发生错误: {}", e.getMessage(), e);
            for (Long variantId : variantIds) {
                result.put(variantId, 0);
            }
        }

        return result;
    }

    @Override
    public Map<Long, Map<Long, Integer>> getAvailableStocksByWarehouse(List<Long> variantIds) {
        if (CollUtil.isEmpty(variantIds)) {
            return Collections.emptyMap();
        }

        // 从数据库获取变体在各仓库的可用库存
        Map<Long, Map<Long, Integer>> result = MapUtil.newHashMap();

        try {
            // 查询指定变体ID在所有仓库的库存数据
            List<WhsStock> stockList = stockMapper.selectList(
                new LambdaQueryWrapper<WhsStock>()
                    .in(WhsStock::getVariantId, variantIds)
                    .gt(WhsStock::getAvailableStock, 0)  // 只查询有可用库存的记录
            );

            // 组织返回数据：Map<变体ID, Map<仓库ID, 可用库存数量>>
            for (WhsStock stock : stockList) {
                Long variantId = stock.getVariantId();
                Long warehouseId = stock.getWarehouseId();
                Integer availableQuantity = stock.getAvailableStock();

                // 确保变体ID对应的Map存在
                if (!result.containsKey(variantId)) {
                    result.put(variantId, MapUtil.newHashMap());
                }

                // 设置该变体在特定仓库的可用库存
                result.get(variantId).put(warehouseId, availableQuantity);
            }

            return result;
        } catch (Exception e) {
            log.error("获取变体在各仓库的可用库存时发生错误", e);
            return Collections.emptyMap();
        }
    }

    // 查询相关方法 ----------------------------------------

    @Override
    public TableDataInfo<WhsStockVo> queryPageList(WhsStockBo bo, PageQuery pageQuery) {
        try {
            Page<WhsStockVo> page = stockMapper.selectStockVoPage(pageQuery.build(), buildQueryWrapper(bo), bo);
            return TableDataInfo.build(page);
        } catch (Exception e) {
            log.error("查询库存分页列表出错: {}", e.getMessage());
            return TableDataInfo.build(new ArrayList<>());
        }
    }

    @Override
    public WhsStock getStockByVariantAndWarehouse(Long variantId, Long warehouseId) {
        if (variantId == null || warehouseId == null) {
            return null;
        }

        try {
            LambdaQueryWrapper<WhsStock> lqw = new LambdaQueryWrapper<>();
            lqw.eq(WhsStock::getVariantId, variantId)
                .eq(WhsStock::getWarehouseId, warehouseId);

            return stockMapper.selectOne(lqw);
        } catch (Exception e) {
            log.error("获取库存对象时发生错误: {}", e.getMessage());
            return null;
        }
    }

    private LambdaQueryWrapper<WhsStock> buildQueryWrapper(WhsStockBo bo) {
        LambdaQueryWrapper<WhsStock> lqw = new LambdaQueryWrapper<>();

        if (bo.getId() != null) {
            lqw.eq(WhsStock::getId, bo.getId());
        }
        if (bo.getWarehouseId() != null) {
            lqw.eq(WhsStock::getWarehouseId, bo.getWarehouseId());
        }
        if (bo.getVariantId() != null) {
            lqw.eq(WhsStock::getVariantId, bo.getVariantId());
        }

        return lqw;
    }

    @Override
    public List<WhsStockSummaryVo> queryStockSummaryList(WhsStockSummaryBo bo) {
        try {
            List<WhsStockSummaryVo> list = stockMapper.selectStockSummaryVoList(buildSummaryQueryWrapper(), bo);

            // 查询每个产品的变体库存详情
            list.forEach(summary -> loadVariantStockDetails(summary, bo));

            return list;
        } catch (Exception e) {
            log.error("查询库存汇总列表出错: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public WhsStockSummaryVo.StockSummaryResponse queryStockSummaryWithStatistics(WhsStockSummaryBo bo) {
        try {
            // 获取库存汇总列表
            List<WhsStockSummaryVo> list = queryStockSummaryList(bo);

            // 计算统计信息 - 基于变体视角
            WhsStockSummaryVo.StockSummaryStatistics statistics = new WhsStockSummaryVo.StockSummaryStatistics();

            // 统计所有变体数据
            int totalVariants = 0;  // 变体总数
            int variantsWithStock = 0;  // 有库存的变体数量
            int variantsWithInTransit = 0;  // 有在途库存的变体数量
            int totalAvailable = 0;  // 总可用库存数量
            int totalInTransit = 0;  // 总在途库存数量

            for (WhsStockSummaryVo summary : list) {
                // 统计变体数量
                if (summary.getVariantStockDetails() != null) {
                    totalVariants += summary.getVariantStockDetails().size();

                    // 遍历每个变体，确保每个变体只统计一次
                    for (WhsStockSummaryVo.VariantStockDetail variant : summary.getVariantStockDetails()) {
                        Integer availableStock = variant.getTotalAvailableStock();
                        Integer inTransitStock = variant.getTotalInTransitStock();

                        // 安全处理null值
                        int available = (availableStock != null) ? availableStock : 0;
                        int inTransit = (inTransitStock != null) ? inTransitStock : 0;

                        // 累加库存数量
                        totalAvailable += available;
                        totalInTransit += inTransit;

                        // 统计有库存的变体数量（可用库存 > 0 OR 在途库存 > 0）
                        if (available > 0 || inTransit > 0) {
                            variantsWithStock++;
                        }

                        // 统计有在途库存的变体数量（在途库存 > 0）
                        if (inTransit > 0) {
                            variantsWithInTransit++;
                        }
                    }
                }
            }

            // 修复：统一使用变体维度进行统计
            statistics.setTotal(totalVariants);  // 修复：设置变体总数而不是产品总数
            statistics.setWithStock(variantsWithStock);
            statistics.setWithInTransit(variantsWithInTransit);
            statistics.setTotalAvailable(totalAvailable);
            statistics.setTotalInTransit(totalInTransit);

            // 构建响应对象
            WhsStockSummaryVo.StockSummaryResponse response = new WhsStockSummaryVo.StockSummaryResponse();
            response.setData(list);
            response.setStatistics(statistics);

            return response;
        } catch (Exception e) {
            log.error("查询库存汇总带统计信息出错: {}", e.getMessage(), e);
            // 返回空的响应对象
            WhsStockSummaryVo.StockSummaryResponse response = new WhsStockSummaryVo.StockSummaryResponse();
            response.setData(new ArrayList<>());
            response.setStatistics(new WhsStockSummaryVo.StockSummaryStatistics());
            return response;
        }
    }

    /**
     * 加载产品的变体库存详情
     */
    private void loadVariantStockDetails(WhsStockSummaryVo summary, WhsStockSummaryBo bo) {
        try {
            // 查询原始的仓库库存数据，传递仓库过滤条件
            List<Map<String, Object>> rawData = stockMapper.selectVariantStockDetailsByProductId(summary.getProductId(), bo.getWarehouseId());

            // 按变体分组
            Map<Long, List<Map<String, Object>>> variantGrouped = rawData.stream()
                .collect(Collectors.groupingBy(item -> (Long) item.get("variant_id")));

            // 转换为变体库存详情
            List<WhsStockSummaryVo.VariantStockDetail> details = variantGrouped.entrySet().stream()
                .filter(entry -> {
                    List<Map<String, Object>> records = entry.getValue();
                    if (records.isEmpty()) {
                        return false;
                    }

                    // 包装类型筛选
                    if (bo.getPackagingTypes() != null && !bo.getPackagingTypes().isEmpty()) {
                        Integer packagingType = Convert.toInt(records.get(0).get("packaging_type"), 0);
                        if (!bo.getPackagingTypes().contains(packagingType)) {
                            return false;
                        }
                    }

                    // 只显示有库存的变体筛选
                    if (bo.getOnlyWithStock() != null && bo.getOnlyWithStock()) {
                        // 计算该变体的总库存
                        int totalAvailable = 0;
                        int totalInTransit = 0;

                        for (Map<String, Object> record : records) {
                            Integer availableStock = Convert.toInt(record.get("available_stock"), 0);
                            Integer inTransitStock = Convert.toInt(record.get("in_transit_stock"), 0);
                            totalAvailable += availableStock;
                            // 修复：在途库存已经是按变体聚合的总数，直接赋值而不是取最大值
                            if (inTransitStock != null && inTransitStock > 0) {
                                totalInTransit = inTransitStock;  // 直接赋值，因为SQL已经按变体聚合
                            }
                        }

                        // 只保留有可用库存或有在途库存的变体
                        return totalAvailable > 0 || totalInTransit > 0;
                    }

                    return true;
                }).map(entry -> {
                    Long variantId = entry.getKey();
                    List<Map<String, Object>> records = entry.getValue();

                    WhsStockSummaryVo.VariantStockDetail detail = new WhsStockSummaryVo.VariantStockDetail();

                    // 设置变体基本信息（从第一条记录获取）
                    Map<String, Object> firstRecord = records.get(0);
                    detail.setVariantId(variantId);
                    detail.setSkuCode((String) firstRecord.get("sku_code"));
                    detail.setPackagingType(Convert.toInt(firstRecord.get("packaging_type"), 0));
                    detail.setFinishedStock(Convert.toInt(firstRecord.get("finished_stock"), 0));
                    detail.setPendingStock(Convert.toInt(firstRecord.get("pending_stock"), 0));

                    // 处理仓库库存数据
                    List<WhsStockSummaryVo.WarehouseStock> warehouseStocks = new ArrayList<>();
                    int totalAvailable = 0;
                    int totalInTransit = 0;

                    for (Map<String, Object> record : records) {
                        Long warehouseId = (Long) record.get("warehouse_id");
                        String warehouseName = (String) record.get("warehouse_name");
                        Integer availableStock = Convert.toInt(record.get("available_stock"), 0);
                        Integer inTransitStock = Convert.toInt(record.get("in_transit_stock"), 0);

                        // 如果有仓库信息，创建仓库库存记录
                        if (warehouseId != null && warehouseName != null) {
                            WhsStockSummaryVo.WarehouseStock stock = new WhsStockSummaryVo.WarehouseStock();
                            stock.setWarehouseId(warehouseId);
                            stock.setWarehouseName(warehouseName);
                            stock.setAvailableStock(availableStock);
                            stock.setInTransitStock(0); // 在途库存按变体统计，不按仓库
                            warehouseStocks.add(stock);

                            totalAvailable += availableStock;
                        }

                        // 修复：在途库存已经是按变体聚合的总数，直接赋值
                        if (inTransitStock != null && inTransitStock > 0) {
                            totalInTransit = inTransitStock;  // 直接赋值，因为SQL已经按变体聚合
                        }
                    }

                    detail.setWarehouseStocks(warehouseStocks);
                    detail.setTotalAvailableStock(totalAvailable);
                    detail.setTotalInTransitStock(totalInTransit);

                    return detail;
                }).sorted((a, b) -> {
                    // 首先按包装类型降序排序
                    int packagingCompare = Integer.compare(b.getPackagingType() != null ? b.getPackagingType() : 0, a.getPackagingType() != null ? a.getPackagingType() : 0);
                    if (packagingCompare != 0) {
                        return packagingCompare;
                    }
                    // 包装类型相同时按SKU排序
                    String skuA = a.getSkuCode() != null ? a.getSkuCode() : "";
                    String skuB = b.getSkuCode() != null ? b.getSkuCode() : "";
                    return skuA.compareTo(skuB);
                })
                .collect(Collectors.toList());

            log.debug("产品{}的变体库存详情: {}", summary.getProductId(), details);
            summary.setVariantStockDetails(details);
        } catch (Exception e) {
            log.error("查询产品{}的变体库存详情出错: {}", summary.getProductId(), e.getMessage(), e);
            summary.setVariantStockDetails(new ArrayList<>());
        }
    }

    private LambdaQueryWrapper<WhsStock> buildSummaryQueryWrapper() {
        return new LambdaQueryWrapper<>();
    }

    @Override
    public List<WhsVariantStockByWarehouseVo> getVariantStockByWarehouses(Long variantId) {
        log.info("查询变体{}在各仓库的库存分布", variantId);

        // 1. 获取所有仓库列表
        List<com.imhuso.wholesale.core.domain.WhsWarehouse> warehouses = warehouseMapper.selectList(null);
        if (warehouses.isEmpty()) {
            log.warn("未找到任何仓库");
            return new ArrayList<>();
        }

        // 2. 查询该变体在各仓库的可用库存
        List<WhsStock> stocks = stockMapper.selectList(
            new LambdaQueryWrapper<WhsStock>()
                .eq(WhsStock::getVariantId, variantId)
        );

        // 创建仓库ID到可用库存的映射
        Map<Long, Integer> warehouseToAvailableStock = stocks.stream()
            .collect(Collectors.toMap(
                WhsStock::getWarehouseId,
                stock -> stock.getAvailableStock() != null ? stock.getAvailableStock() : 0,
                (existing, replacement) -> existing
            ));

        // 3. 计算在途库存（通过入库单计算）
        Map<Long, Integer> warehouseToInTransitStock = calculateInTransitStock(variantId);

        // 4. 构建结果列表
        List<WhsVariantStockByWarehouseVo> result = new ArrayList<>();
        for (com.imhuso.wholesale.core.domain.WhsWarehouse warehouse : warehouses) {
            WhsVariantStockByWarehouseVo vo = new WhsVariantStockByWarehouseVo();
            vo.setWarehouseId(warehouse.getId());
            vo.setWarehouseName(warehouse.getName());
            vo.setAvailableStock(warehouseToAvailableStock.getOrDefault(warehouse.getId(), 0));
            vo.setInTransitStock(warehouseToInTransitStock.getOrDefault(warehouse.getId(), 0));
            result.add(vo);
        }

        log.info("变体{}的库存分布查询完成，返回{}个仓库的数据", variantId, result.size());
        return result;
    }

    /**
     * 计算变体在各仓库的在途库存
     * 通过查询入库单和入库单项目计算
     *
     * @param variantId 变体ID
     * @return 仓库ID到在途库存数量的映射
     */
    private Map<Long, Integer> calculateInTransitStock(Long variantId) {
        try {
            // 使用MyBatis查询在途库存，执行真正的数据库查询
            List<Map<String, Object>> inTransitResults = stockMapper.selectInTransitStockByVariantId(variantId);

            Map<Long, Integer> result = new HashMap<>();
            for (Map<String, Object> row : inTransitResults) {
                // 使用Convert工具类的null版本避免逻辑错误，仓库ID为0是无效的
                Long warehouseId = Convert.toLong(row.get("warehouse_id"), null);
                Integer inTransitStock = Convert.toInt(row.get("in_transit_stock"), 0);

                // 只保存有效的仓库ID和库存数据
                if (warehouseId != null && warehouseId > 0 && inTransitStock != null && inTransitStock > 0) {
                    result.put(warehouseId, inTransitStock);
                    log.debug("变体{}在仓库{}的在途库存: {}", variantId, warehouseId, inTransitStock);
                }
            }

            log.info("变体{}的在途库存计算完成: {}", variantId, result);
            return result;

        } catch (Exception e) {
            log.error("计算变体{}在途库存时出错: {}", variantId, e.getMessage(), e);
            return new HashMap<>();
        }
    }
}
