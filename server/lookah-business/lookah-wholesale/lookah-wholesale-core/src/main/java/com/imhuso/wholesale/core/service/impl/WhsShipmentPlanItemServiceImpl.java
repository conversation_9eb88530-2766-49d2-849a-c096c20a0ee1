package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.domain.WhsOrderItem;
import com.imhuso.wholesale.core.domain.WhsShipmentPlanItem;
import com.imhuso.wholesale.core.domain.bo.admin.WhsShipmentPlanItemBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanItemVo;
import com.imhuso.wholesale.core.mapper.WhsOrderItemMapper;
import com.imhuso.wholesale.core.mapper.WhsShipmentPlanItemMapper;
import com.imhuso.wholesale.core.service.IWhsOrderItemService;
import com.imhuso.wholesale.core.service.IWhsShipmentPlanItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 发货方案项服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsShipmentPlanItemServiceImpl implements IWhsShipmentPlanItemService {

    private final WhsShipmentPlanItemMapper shipmentPlanItemMapper;
    private final WhsOrderItemMapper orderItemMapper;
    private final IWhsOrderItemService orderItemService;

    @Override
    public List<WhsShipmentPlanItemVo> getShipmentPlanItemsByPlanIds(List<Long> planIds) {
        if (CollUtil.isEmpty(planIds)) {
            return Collections.emptyList();
        }

        // 使用 IN 查询批量获取方案项
        LambdaQueryWrapper<WhsShipmentPlanItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WhsShipmentPlanItem::getPlanId, planIds);
        List<WhsShipmentPlanItem> items = shipmentPlanItemMapper.selectList(queryWrapper);

        if (CollUtil.isEmpty(items)) {
            return Collections.emptyList();
        }

        // 转换为VO
        List<WhsShipmentPlanItemVo> itemVos = MapstructUtils.convert(items, WhsShipmentPlanItemVo.class);

        // 从订单项中获取产品名称和规格信息
        enrichPlanItemsWithOrderItemDetails(itemVos);

        return itemVos;
    }

    /**
     * 从订单项中补充方案项的产品名称和规格信息
     *
     * @param itemVos 方案项VO列表
     */
    private void enrichPlanItemsWithOrderItemDetails(List<WhsShipmentPlanItemVo> itemVos) {
        if (CollUtil.isEmpty(itemVos)) {
            return;
        }

        // 收集所有订单ID
        List<Long> orderIds = itemVos.stream()
            .map(WhsShipmentPlanItemVo::getOrderId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        if (CollUtil.isEmpty(orderIds)) {
            return;
        }

        // 批量查询订单项
        LambdaQueryWrapper<WhsOrderItem> orderItemQueryWrapper = new LambdaQueryWrapper<>();
        orderItemQueryWrapper.in(WhsOrderItem::getId, orderIds);
        List<WhsOrderItem> orderItems = orderItemMapper.selectList(orderItemQueryWrapper);

        if (CollUtil.isEmpty(orderItems)) {
            return;
        }

        // 构建订单项ID到订单项的映射
        Map<Long, WhsOrderItem> orderItemMap = orderItems.stream()
            .collect(Collectors.toMap(WhsOrderItem::getId, item -> item));

        // 补充方案项VO的产品名称和规格文本
        for (WhsShipmentPlanItemVo itemVo : itemVos) {
            Long orderId = itemVo.getOrderId();
            if (orderId != null && orderItemMap.containsKey(orderId)) {
                WhsOrderItem orderItem = orderItemMap.get(orderId);

                // 设置产品名称
                itemVo.setProductName(orderItem.getProductName());

                // 设置规格文本 - 使用OrderItemService中的方法解析规格快照
                if (StringUtils.isNotEmpty(orderItem.getSpecsSnapshot())) {
                    // 调用OrderItemService的方法处理规格快照，避免代码重复
                    String specsText = orderItemService.parseSpecsSnapshotToText(orderItem.getSpecsSnapshot());
                    itemVo.setSpecsText(specsText);
                }
            }
        }
    }

    @Override
    public List<WhsShipmentPlanItemBo> getShipmentPlanItemBosByPlanId(Long planId) {
        if (planId == null) {
            return Collections.emptyList();
        }

        List<WhsShipmentPlanItem> items = getItemsByPlanId(planId);
        if (CollUtil.isEmpty(items)) {
            return Collections.emptyList();
        }

        List<WhsShipmentPlanItemBo> result = MapstructUtils.convert(items, WhsShipmentPlanItemBo.class);

        // 后处理：确保转换后的BO对象中关键字段不为null
        // 注意：当前Mapstruct映射已经完整，无需额外处理

        return result;
    }

    /**
     * 获取指定方案ID的方案项列表
     *
     * @param planId 方案ID
     * @return 方案项实体列表
     */
    private List<WhsShipmentPlanItem> getItemsByPlanId(Long planId) {
        LambdaQueryWrapper<WhsShipmentPlanItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhsShipmentPlanItem::getPlanId, planId);
        return shipmentPlanItemMapper.selectList(queryWrapper);
    }

    /**
     * 根据订单ID删除所有发货方案项
     *
     * @param orderId 订单ID
     */
    @Override
    public void deleteByOrderId(Long orderId) {
        if (orderId == null) {
            return;
        }

        LambdaQueryWrapper<WhsShipmentPlanItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhsShipmentPlanItem::getOrderId, orderId);
        int deletedCount = shipmentPlanItemMapper.delete(queryWrapper);

        if (deletedCount > 0) {
            log.info("删除订单 {} 的发货方案项，共删除 {} 条记录", orderId, deletedCount);
        }
    }
}
