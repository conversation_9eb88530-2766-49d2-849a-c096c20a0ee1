package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.WhsProduct;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.WhsStock;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductVariantBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductVariantListBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsGetSkusInfoVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVariantAttributeVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVariantListVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVariantVo;
import com.imhuso.wholesale.core.service.IWhsPackageItemService;
import com.imhuso.wholesale.core.enums.PackagingType;
import com.imhuso.wholesale.core.event.StockChangeEvent;
import com.imhuso.wholesale.core.mapper.WhsProductMapper;
import com.imhuso.wholesale.core.mapper.WhsProductVariantMapper;
import com.imhuso.wholesale.core.mapper.WhsStockMapper;
import com.imhuso.wholesale.core.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品变体Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsProductVariantServiceImpl implements IWhsProductVariantService {

    private final WhsProductVariantMapper baseMapper;
    private final WhsProductMapper productMapper;
    private final WhsStockMapper stockMapper;
    private final IWhsProductVariantAttributeService variantAttributeService;
    private final IWhsPackageItemService packageItemService;
    private final IWhsStockService stockService;
    private final IWhsInTransitStockService inTransitStockService;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertVariant(WhsProductVariantBo variant) {
        log.info("添加产品变体，参数: {}", variant);

        checkSkuUniqueness(variant.getSkuCode(), null);

        WhsProductVariant entity = MapstructUtils.convert(variant, WhsProductVariant.class);
        entity.setPackagingType(PackagingType.INDIVIDUAL.getValue());

        int rows = baseMapper.insert(entity);
        variant.setId(entity.getId());

        // 处理变体属性
        if (variant.getAttributes() != null) {
            variantAttributeService.processVariantAttributes(entity.getProductId(), entity.getId(),
                variant.getAttributes());
        }

        // 如果产品没有默认变体，设置当前变体为默认
        WhsProduct product = productMapper.selectById(entity.getProductId());
        if (product != null && product.getDefaultVariantId() == null) {
            product.setDefaultVariantId(entity.getId());
            productMapper.updateById(product);
        }

        return rows;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateVariant(WhsProductVariantBo variant) {
        log.info("更新产品变体，参数: {}", variant);

        WhsProductVariant originalVariant = baseMapper.selectById(variant.getId());
        if (originalVariant == null) {
            throw new ServiceException("变体不存在");
        }

        // 只允许更新单品变体
        if (originalVariant.getPackagingType() != null
            && !originalVariant.getPackagingType().equals(PackagingType.INDIVIDUAL.getValue())) {
            throw new ServiceException("只能更新单品变体");
        }

        checkSkuUniqueness(variant.getSkuCode(), variant.getId());

        WhsProductVariant entity = MapstructUtils.convert(variant, WhsProductVariant.class);
        entity.setPackagingType(originalVariant.getPackagingType());

        int rows = baseMapper.updateById(entity);

        // 处理变体属性
        if (variant.getAttributes() != null) {
            variantAttributeService.processVariantAttributes(originalVariant.getProductId(), entity.getId(),
                variant.getAttributes());
        }

        // 发布产品更新事件，确保缓存会被刷新
        if (rows > 0) {
            publishProductUpdateEvent(originalVariant.getProductId());
        }

        return rows;
    }

    /**
     * 发布产品更新事件
     *
     * @param productId 产品ID
     */
    private void publishProductUpdateEvent(Long productId) {
        try {
            if (productId == null) {
                return;
            }

            // 创建并发布产品更新事件
            StockChangeEvent event = new StockChangeEvent(this, Collections.emptyList(),
                Collections.singletonList(productId), true);
            eventPublisher.publishEvent(event);
            log.debug("已发布产品更新事件，产品ID: {}", productId);
        } catch (Exception e) {
            log.error("发布产品更新事件失败: {}", e.getMessage());
        }
    }

    @Override
    public TableDataInfo<WhsProductVariantVo> queryPageByProductId(Long productId, PageQuery pageQuery) {
        // 只查询单品变体
        LambdaQueryWrapper<WhsProductVariant> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WhsProductVariant::getProductId, productId)
            .eq(WhsProductVariant::getPackagingType, PackagingType.INDIVIDUAL.getValue())
            .orderByDesc(WhsProductVariant::getId);

        Page<WhsProductVariant> page = baseMapper.selectPage(pageQuery.build(), lqw);
        List<WhsProductVariantVo> rows = MapstructUtils.convert(page.getRecords(), WhsProductVariantVo.class);

        if (CollUtil.isNotEmpty(rows)) {
            // 获取所有包装变体
            LambdaQueryWrapper<WhsProductVariant> packageQuery = new LambdaQueryWrapper<>();
            packageQuery.eq(WhsProductVariant::getProductId, productId)
                .ne(WhsProductVariant::getPackagingType, PackagingType.INDIVIDUAL.getValue())
                .orderByDesc(WhsProductVariant::getId);
            List<WhsProductVariant> packageVariants = baseMapper.selectList(packageQuery);

            if (CollUtil.isNotEmpty(packageVariants)) {
                List<WhsProductVariantVo> packageVariantVos = MapstructUtils.convert(packageVariants,
                    WhsProductVariantVo.class);

                // 获取包装关系数据
                List<Long> packageVariantIds = packageVariants.stream().map(WhsProductVariant::getId)
                    .collect(Collectors.toList());

                PackageData packageData = getPackageData(packageVariantIds);

                // 填充包装变体的类型名称和数量
                fillPackageVariantInfo(packageVariantVos, packageData);

                // 填充单品变体的包装变体信息
                fillIndividualVariantPackageInfo(rows, packageVariantVos, packageData);
            }

            // 填充其他必要信息（库存、有效预警库存等）
            rows.forEach(this::fillVariantData);
        }

        return TableDataInfo.build(rows);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteById(Long id) {

        log.info("删除产品变体: {}", id);

        WhsProductVariant variant = baseMapper.selectById(id);
        if (variant == null) {
            throw new ServiceException("变体不存在");
        }

        // 1. 检查变体是否是包装变体，如果是包装变体，禁止删除
        if (variant.getPackagingType() != null
            && !variant.getPackagingType().equals(PackagingType.INDIVIDUAL.getValue())) {
            throw new ServiceException("包装变体不能删除");
        }

        // 2. 检查是否作为内容物被包装在其他变体中
        if (packageItemService.isChildInAnyPackage(id)) {
            throw new ServiceException("该变体下存在包装关系，不能删除，如需删除，请先删除包装关系");
        }

        // 3. 如果是默认变体，需要将默认变体设置为产品的其他变体
        WhsProduct product = productMapper.selectById(variant.getProductId());
        if (product != null && product.getDefaultVariantId() != null && product.getDefaultVariantId().equals(id)) {

            // 查找同一产品的其他变体
            LambdaQueryWrapper<WhsProductVariant> query = new LambdaQueryWrapper<>();
            query.eq(WhsProductVariant::getProductId, variant.getProductId()).ne(WhsProductVariant::getId, id)
                // 优先选择单品变体作为默认
                .eq(WhsProductVariant::getPackagingType, PackagingType.INDIVIDUAL.getValue())
                .last("LIMIT 1");

            WhsProductVariant newDefaultVariant = baseMapper.selectOne(query);

            if (newDefaultVariant != null) {
                // 更新默认变体
                product.setDefaultVariantId(newDefaultVariant.getId());
                productMapper.updateById(product);
                log.info("转移默认变体: 从 {} 到 {}", id, newDefaultVariant.getId());
            } else {
                // 如果没有其他变体，将默认变体设置为null
                product.setDefaultVariantId(null);
                productMapper.updateById(product);
                log.info("移除默认变体: {}", id);
            }
        }

        // 删除变体属性
        variantAttributeService.deleteByVariantId(id);
        return baseMapper.deleteById(id);
    }

    @Override
    public WhsProductVariantVo getVariant(Long id, boolean needDetail) {
        if (id == null) {
            return null;
        }

        WhsProductVariant variant = baseMapper.selectById(id);
        if (variant == null) {
            return null;
        }

        WhsProductVariantVo variantVo = MapstructUtils.convert(variant, WhsProductVariantVo.class);

        if (needDetail) {
            fillVariantData(variantVo);
        } else {
            fillStockInfo(variantVo);
        }

        return variantVo;
    }

    @Override
    public List<WhsProductVariant> selectByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return baseMapper.selectByIds(ids);
    }

    @Override
    public Map<Long, List<WhsProductVariant>> getVariantsByProductIds(List<Long> productIds) {
        if (CollUtil.isEmpty(productIds)) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<WhsProductVariant> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WhsProductVariant::getProductId, productIds).orderByDesc(WhsProductVariant::getId);

        List<WhsProductVariant> allVariants = baseMapper.selectList(queryWrapper);
        return allVariants.stream().collect(Collectors.groupingBy(WhsProductVariant::getProductId));
    }

    @Override
    public List<WhsProductVariantVo> convertToVoList(List<WhsProductVariant> variants) {
        if (CollUtil.isEmpty(variants)) {
            return Collections.emptyList();
        }

        List<WhsProductVariantVo> variantVos = MapstructUtils.convert(variants, WhsProductVariantVo.class);
        if (CollUtil.isEmpty(variantVos)) {
            return Collections.emptyList();
        }

        variantVos.forEach(this::fillStockInfo);
        return variantVos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int setDefaultVariant(Long id) {
        WhsProductVariant variant = baseMapper.selectById(id);
        if (variant == null) {
            throw new ServiceException("变体不存在");
        }

        Long productId = variant.getProductId();
        if (productId == null) {
            throw new ServiceException("变体未关联产品");
        }

        LambdaUpdateWrapper<WhsProduct> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WhsProduct::getId, productId).set(WhsProduct::getDefaultVariantId, id);

        return productMapper.update(null, updateWrapper);
    }

    @Override
    public boolean isSkuCodeUnique(String skuCode, Long excludeId) {
        if (StringUtils.isBlank(skuCode)) {
            return true;
        }

        LambdaQueryWrapper<WhsProductVariant> query = new LambdaQueryWrapper<>();
        query.eq(WhsProductVariant::getSkuCode, skuCode);

        if (excludeId != null) {
            query.ne(WhsProductVariant::getId, excludeId);
        }

        return baseMapper.selectCount(query) == 0;
    }

    @Override
    public Map<String, Object> copyVariantWithRelations(Long sourceVariantId, String newSkuCode, String newUpc) {
        if (sourceVariantId == null || StringUtils.isBlank(newSkuCode)) {
            throw new ServiceException("复制变体所需的参数缺失");
        }

        WhsProductVariant sourceVariant = baseMapper.selectById(sourceVariantId);
        if (sourceVariant == null) {
            throw new ServiceException("源变体不存在");
        }

        if (isSkuCodeDuplicate(newSkuCode, null)) {
            throw new ServiceException("SKU编码 [" + newSkuCode + "] 已存在");
        }

        WhsProductVariant newVariant = new WhsProductVariant();
        BeanUtil.copyProperties(sourceVariant, newVariant, "id", "createTime", "updateTime", "createBy", "updateBy");

        newVariant.setId(null);
        newVariant.setSkuCode(newSkuCode);
        newVariant.setUpc(newUpc);

        // 复制变体属性
        Map<Long, Long> attributeMap = new HashMap<>(16);
        List<WhsProductVariantAttributeVo> sourceAttributes = variantAttributeService
            .getVariantAttributes(sourceVariantId);

        if (CollUtil.isNotEmpty(sourceAttributes)) {
            for (WhsProductVariantAttributeVo attr : sourceAttributes) {
                attributeMap.put(attr.getAttributeId(), attr.getAttributeValueId());
            }
        }

        return Map.of("variant", newVariant, "attributeMap", attributeMap);
    }

    /**
     * 检查SKU编码是否重复
     *
     * @param skuCode   SKU编码
     * @param excludeId 排除的变体ID
     */
    private boolean isSkuCodeDuplicate(String skuCode, Long excludeId) {
        return !isSkuCodeUnique(skuCode, excludeId);
    }

    /**
     * 检查SKU编码唯一性
     *
     * @param skuCode   SKU编码
     * @param excludeId 排除的变体ID
     */
    private void checkSkuUniqueness(String skuCode, Long excludeId) {
        if (StringUtils.isBlank(skuCode)) {
            return;
        }

        if (isSkuCodeDuplicate(skuCode, excludeId)) {
            throw new ServiceException("SKU编码 [" + skuCode + "] 已存在");
        }
    }

    /**
     * 包装数据封装类
     */
    private record PackageData(Map<Long, List<Long>> relations, Map<Long, Integer> quantities) {
    }

    /**
     * 获取包装数据
     *
     * @param packageVariantIds 包装变体ID列表
     * @return 包装数据，包含变体关系和数量信息
     */
    private PackageData getPackageData(List<Long> packageVariantIds) {
        if (CollUtil.isEmpty(packageVariantIds)) {
            return new PackageData(Collections.emptyMap(), Collections.emptyMap());
        }

        Map<Long, List<Long>> relations = new HashMap<>(packageVariantIds.size());
        Map<Long, Integer> quantities = new HashMap<>(packageVariantIds.size());

        for (Long packageVariantId : packageVariantIds) {
            Map<Long, Integer> childQuantities = packageItemService.getChildVariantQuantities(packageVariantId);
            if (childQuantities != null && !childQuantities.isEmpty()) {
                relations.put(packageVariantId, new ArrayList<>(childQuantities.keySet()));
                quantities.put(packageVariantId, childQuantities.values().iterator().next());
            }
        }

        return new PackageData(relations, quantities);
    }

    /**
     * 填充包装变体的额外信息
     *
     * @param packageVariants 包装变体列表
     * @param packageData     包装数据
     */
    private void fillPackageVariantInfo(List<WhsProductVariantVo> packageVariants, PackageData packageData) {
        for (WhsProductVariantVo packageVariant : packageVariants) {
            if (packageVariant.getPackagingType() != null &&
                !PackagingType.INDIVIDUAL.getValue().equals(packageVariant.getPackagingType())) {
                // 设置包装数量
                packageVariant.setPackageQuantity(packageData.quantities().getOrDefault(packageVariant.getId(), 1));
                // 填充库存信息和有效预警库存
                fillVariantData(packageVariant);
            }
        }
    }

    /**
     * 填充单品变体的包装变体信息
     *
     * @param individualVariants 单品变体列表
     * @param packageVariants    包装变体列表
     * @param packageData        包装数据
     */
    private void fillIndividualVariantPackageInfo(List<WhsProductVariantVo> individualVariants,
                                                  List<WhsProductVariantVo> packageVariants, PackageData packageData) {

        Map<Long, List<Long>> relations = packageData.relations();
        if (relations.isEmpty()) {
            return;
        }

        Map<Long, WhsProductVariantVo> packageVariantMap = packageVariants.stream()
            .collect(Collectors.toMap(WhsProductVariantVo::getId, v -> v));

        for (WhsProductVariantVo individualVariant : individualVariants) {
            List<WhsProductVariantVo> childPackageVariants = new ArrayList<>();

            for (Map.Entry<Long, List<Long>> entry : relations.entrySet()) {
                if (entry.getValue() != null && entry.getValue().contains(individualVariant.getId())) {
                    WhsProductVariantVo packageVariant = packageVariantMap.get(entry.getKey());
                    if (packageVariant != null) {
                        childPackageVariants.add(packageVariant);
                    }
                }
            }

            if (!childPackageVariants.isEmpty()) {
                individualVariant.setPackageVariants(childPackageVariants);
            }
        }
    }

    /**
     * 填充变体数据（库存信息和属性信息）
     *
     * @param variantVo 变体VO对象
     */
    private void fillVariantData(WhsProductVariantVo variantVo) {
        if (variantVo == null || variantVo.getId() == null) {
            return;
        }

        fillStockInfo(variantVo);
        variantVo.setAttributes(variantAttributeService.getVariantAttributes(variantVo.getId()));

        // 不再计算有效预警库存，只使用变体自身的预警库存
    }

    /**
     * 填充库存信息
     *
     * @param variantVo 变体VO对象
     */
    private void fillStockInfo(WhsProductVariantVo variantVo) {
        if (variantVo == null || variantVo.getId() == null) {
            return;
        }

        // 获取变体可用库存
        List<Long> singleId = List.of(variantVo.getId());
        Map<Long, Integer> stockMap = stockService.getAvailableStocks(singleId);
        Integer availableStock = stockMap.getOrDefault(variantVo.getId(), 0);
        variantVo.setAvailableStock(availableStock);
    }

    @Override
    public List<WhsProductVariantVo> getVariantsByIds(List<Long> variantIds, boolean onlyBase) {
        if (CollUtil.isEmpty(variantIds)) {
            return new ArrayList<>();
        }

        // 查询变体数据
        LambdaQueryWrapper<WhsProductVariant> lqw = new LambdaQueryWrapper<>();
        lqw.in(WhsProductVariant::getId, variantIds);
        List<WhsProductVariant> variants = baseMapper.selectList(lqw);

        List<WhsProductVariantVo> result = MapstructUtils.convert(variants, WhsProductVariantVo.class);

        if (CollUtil.isEmpty(result)) {
            return new ArrayList<>();
        }

        // 是否需要附加详细信息
        if (!onlyBase) {
            result.forEach(this::fillVariantData);
        }

        return result;
    }

    @Override
    public Map<String, Long> getVariantIdsBySkuCodes(List<String> skuCodes) {
        if (CollUtil.isEmpty(skuCodes)) {
            return new HashMap<>(0);
        }
        // 统一将传入的skuCodes转为大写
        List<String> normalizedSkuCodes = skuCodes.stream().filter(Objects::nonNull).map(String::toUpperCase)
            .collect(Collectors.toList());
        log.debug("批量查询SKU到变体ID的映射(不区分大小写), SKU数量: {}", normalizedSkuCodes.size());

        LambdaQueryWrapper<WhsProductVariant> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(WhsProductVariant::getId, WhsProductVariant::getSkuCode)
            .in(WhsProductVariant::getSkuCode, normalizedSkuCodes)
            .eq(WhsProductVariant::getStatus, BusinessConstants.NORMAL);

        List<WhsProductVariant> variants = baseMapper.selectList(wrapper);

        // 返回时也统一大写，保证调用方用大写查找
        return variants.stream().collect(Collectors.toMap(
            v -> v.getSkuCode() != null ? v.getSkuCode().toUpperCase() : "",
            WhsProductVariant::getId,
            (existing, replacement) -> existing
        ));
    }

    @Override
    public List<WhsProductVariant> getAllActiveVariants() {
        LambdaQueryWrapper<WhsProductVariant> query = new LambdaQueryWrapper<>();
        query.eq(WhsProductVariant::getStatus, BusinessConstants.NORMAL);
        return baseMapper.selectList(query);
    }

    /**
     * 获取所有变体列表，包含详细信息
     */
    @Override
    public TableDataInfo<WhsProductVariantListVo> queryVariantList(WhsProductVariantListBo bo,
                                                                     PageQuery pageQuery) {
        // 构建查询条件
        LambdaQueryWrapper<WhsProductVariant> lqw = buildQueryWrapper(bo);

        // 执行分页查询
        Page<WhsProductVariantListVo> page = baseMapper.selectVoPage(pageQuery.build(), lqw,
            WhsProductVariantListVo.class);
        List<WhsProductVariantListVo> variantList = page.getRecords();

        if (CollUtil.isEmpty(variantList)) {
            return TableDataInfo.build(page);
        }

        // 填充变体列表的产品信息和库存信息
        fillVariantListInfo(variantList);

        // 处理产品名称查询条件 - 由于产品名称需要关联查询，放在内存中过滤
        String productNameKeyword = bo.getProductName();
        if (StringUtils.isNotBlank(productNameKeyword)) {
            List<WhsProductVariantListVo> filteredList = variantList.stream()
                .filter(vo -> vo.getProductName() != null && vo.getProductName().contains(productNameKeyword))
                .collect(Collectors.toList());

            // 替换原始记录并更新总数
            page.setRecords(filteredList);
            page.setTotal(filteredList.size());
        }

        return TableDataInfo.build(page);
    }

    /**
     * 填充变体列表的产品信息和库存信息
     *
     * @param variantList 变体列表
     */
    private void fillVariantListInfo(List<WhsProductVariantListVo> variantList) {
        if (CollUtil.isEmpty(variantList)) {
            return;
        }

        // 获取所有变体ID
        List<Long> variantIds = variantList.stream()
            .map(WhsProductVariantListVo::getId)
            .collect(Collectors.toList());

        // 获取所有产品ID
        List<Long> productIds = variantList.stream()
            .map(WhsProductVariantListVo::getProductId)
            .distinct()
            .collect(Collectors.toList());

        // 批量获取产品信息
        Map<Long, WhsProduct> productMap = new HashMap<>(productIds.size());
        if (CollUtil.isNotEmpty(productIds)) {
            List<WhsProduct> products = productMapper.selectByIds(productIds);
            productMap = products.stream()
                .collect(Collectors.toMap(WhsProduct::getId, p -> p, (k1, k2) -> k1));
        }

        // 查询所有库存记录以获取总库存、可用库存和锁定库存
        List<WhsStock> allStocks = stockMapper.selectList(
            new LambdaQueryWrapper<WhsStock>().in(WhsStock::getVariantId, variantIds));

        // 计算各变体的库存信息
        Map<Long, Integer> totalStockMap = new HashMap<>(variantIds.size());
        Map<Long, Integer> availableStockMap = new HashMap<>(variantIds.size());
        Map<Long, Integer> lockedStockMap = new HashMap<>(variantIds.size());

        for (WhsStock stock : allStocks) {
            Long variantId = stock.getVariantId();

            // 累加总库存
            totalStockMap.merge(variantId, stock.getTotalStock(), Integer::sum);

            // 累加锁定库存
            lockedStockMap.merge(variantId, stock.getLockedStock(), Integer::sum);

            // 累加可用库存
            availableStockMap.merge(variantId, stock.getAvailableStock(), Integer::sum);
        }

        // 批量获取在途库存信息，提高性能
        Map<Long, Integer> inTransitStockMap;
        try {
            inTransitStockMap = inTransitStockService.getInTransitStocks(variantIds);
            log.debug("批量获取{}个变体的在途库存信息成功", variantIds.size());
        } catch (Exception e) {
            log.error("批量获取变体在途库存异常", e);
            inTransitStockMap = new HashMap<>(variantIds.size());
        }

        // 填充产品名称和库存信息
        for (WhsProductVariantListVo vo : variantList) {
            // 填充产品信息
            WhsProduct product = productMap.getOrDefault(vo.getProductId(), null);
            if (product != null) {
                vo.setProductName(product.getItemName());
            }

            // 填充库存信息：总库存、可用库存、锁定库存和在途库存
            vo.setTotalStock(totalStockMap.getOrDefault(vo.getId(), 0));
            vo.setAvailableStock(availableStockMap.getOrDefault(vo.getId(), 0));
            vo.setLockedStock(lockedStockMap.getOrDefault(vo.getId(), 0));
            vo.setInTransitStock(inTransitStockMap.getOrDefault(vo.getId(), 0));

            // 不再计算有效预警库存，只使用变体自身的预警库存
        }
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<WhsProductVariant> buildQueryWrapper(WhsProductVariantListBo bo) {
        LambdaQueryWrapper<WhsProductVariant> lqw = new LambdaQueryWrapper<>();

        // 根据变体ID查询
        lqw.eq(bo.getId() != null, WhsProductVariant::getId, bo.getId());

        // 根据产品ID查询
        lqw.eq(bo.getProductId() != null, WhsProductVariant::getProductId, bo.getProductId());

        // 根据SKU编码模糊查询
        lqw.like(StringUtils.isNotBlank(bo.getSkuCode()), WhsProductVariant::getSkuCode, bo.getSkuCode());

        // 根据UPC编码模糊查询
        lqw.like(StringUtils.isNotBlank(bo.getUpc()), WhsProductVariant::getUpc, bo.getUpc());

        // 根据包装类型查询
        lqw.eq(bo.getPackagingType() != null, WhsProductVariant::getPackagingType, bo.getPackagingType());

        // 根据状态查询
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), WhsProductVariant::getStatus, bo.getStatus());

        // 默认按更新时间倒序排序
        lqw.orderByDesc(WhsProductVariant::getUpdateTime);

        return lqw;
    }

    @Override
    public List<WhsGetSkusInfoVo> getSkuInfoBySkuCodes(List<String> skuCodes) {
        if (CollUtil.isEmpty(skuCodes)) {
            return Collections.emptyList();
        }

        log.debug("批量获取SKU信息，SKU数量: {}", skuCodes.size());

        // 1. 获取SKU到变体ID的映射
        Map<String, Long> skuToVariantIdMap = getVariantIdsBySkuCodes(skuCodes);

        // 2. 构建不存在的SKU列表
        List<String> notExistSkus = skuCodes.stream()
            .filter(Objects::nonNull)
            .map(String::toUpperCase)
            .filter(sku -> !skuToVariantIdMap.containsKey(sku))
            .collect(Collectors.toList());

        // 3. 提取变体ID列表
        List<Long> variantIds = new ArrayList<>(skuToVariantIdMap.values());

        if (CollUtil.isEmpty(variantIds)) {
            // 如果没有找到任何变体，直接返回只有不存在SKU的结果
            WhsGetSkusInfoVo result = new WhsGetSkusInfoVo();
            result.setVariants(Collections.emptyList());
            result.setNotExistSkus(notExistSkus);
            return Collections.singletonList(result);
        }

        // 4. 构建查询条件，获取完整的变体信息
        LambdaQueryWrapper<WhsProductVariant> lqw = new LambdaQueryWrapper<>();
        lqw.in(WhsProductVariant::getId, variantIds);

        // 5. 执行查询，获取变体列表
        List<WhsProductVariant> variants = baseMapper.selectList(lqw);

        // 6. 使用 MapstructUtils 转换为 WhsProductVariantListVo
        List<WhsProductVariantListVo> variantListVos = MapstructUtils.convert(variants, WhsProductVariantListVo.class);

        // 7. 填充产品和库存信息
        if (CollUtil.isNotEmpty(variantListVos)) {
            fillVariantListInfo(variantListVos);
        }

        // 8. 创建并返回结果
        WhsGetSkusInfoVo result = new WhsGetSkusInfoVo();
        result.setVariants(variantListVos);
        result.setNotExistSkus(notExistSkus);

        return Collections.singletonList(result);
    }
}
