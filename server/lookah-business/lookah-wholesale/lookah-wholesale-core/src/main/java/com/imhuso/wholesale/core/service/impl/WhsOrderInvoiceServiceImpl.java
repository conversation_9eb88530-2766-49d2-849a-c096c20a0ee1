package com.imhuso.wholesale.core.service.impl;

import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.file.FileUtils;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderVo;
import com.imhuso.wholesale.core.service.IInvoiceService;
import com.imhuso.wholesale.core.service.IWhsOrderInvoiceService;
import com.imhuso.wholesale.core.service.IWhsOrderQueryService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

/**
 * 订单发票服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsOrderInvoiceServiceImpl implements IWhsOrderInvoiceService {

    private final IWhsOrderQueryService orderQueryService;
    private final IInvoiceService invoiceService;

    @Override
    public void downloadOrderInvoice(Long orderId, HttpServletResponse response) throws Exception {
        WhsOrderVo orderVo = orderQueryService.getOrderDetail(orderId);
        if (orderVo == null) {
            throw new ServiceException("订单不存在");
        }

        File invoiceFile = invoiceService.generateExcelInvoice(orderVo);
        if (invoiceFile == null || !invoiceFile.exists()) {
            throw new ServiceException("发票生成失败");
        }

        try {
            String filename = "INVOICE_" + orderVo.getOrderNo() + ".xlsx";
            response.reset();

            // 针对Cloudflare代理的综合处理
            // 1. 缓存控制 - 确保内容不被缓存
            response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");

            // 2. 跨域支持 - 确保跨域下载正常
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            response.setHeader("Access-Control-Allow-Headers", "Content-Type, Content-Disposition, Content-Length");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition, Content-Length, Content-Type, Cache-Control, download-filename");

            // 3. 内容处理 - 确保Cloudflare正确处理内容
            response.setHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");
            response.setHeader("X-Content-Type-Options", "nosniff");

            // 4. 标准文件下载头
            FileUtils.setAttachmentResponseHeader(response, filename);

            // 设置内容类型
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setCharacterEncoding("UTF-8");
            response.setContentLengthLong(invoiceFile.length());

            // 确保流正确关闭
            try (var outputStream = response.getOutputStream()) {
                Files.copy(invoiceFile.toPath(), outputStream);
                outputStream.flush();
            }
        } catch (IOException e) {
            log.error("下载发票失败", e);
            throw new ServiceException("下载发票失败");
        } finally {
            try {
                // 删除临时文件
                if (invoiceFile.exists() && !Files.deleteIfExists(invoiceFile.toPath())) {
                    log.warn("临时文件删除失败: {}", invoiceFile.getAbsolutePath());
                }
            } catch (IOException e) {
                log.warn("删除临时文件失败", e);
            }
        }
    }
}
