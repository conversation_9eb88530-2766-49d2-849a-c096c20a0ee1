package com.imhuso.wholesale.core.job;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import com.aizuda.snailjob.common.log.SnailJobLog;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.service.IWhsInboundOrderSyncService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 入库单同步定时任务
 * <p>
 * 定期同步所有海外仓入库单
 * 配置为每天凌晨3点执行一次
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@SuppressWarnings("unused") // SnailJob框架调用，IDE检测不到使用
public class InboundOrderSyncJob {

    private final IWhsInboundOrderSyncService inboundOrderSyncService;

    /**
     * 执行所有海外仓入库单同步任务
     *
     * @param jobArgs 任务参数
     * @return 执行结果
     */
    @JobExecutor(name = "syncAllWarehouseInboundOrders")
    public ExecuteResult syncAllWarehouseInboundOrders(JobArgs jobArgs) {
        SnailJobLog.LOCAL.info("开始执行海外仓入库单同步任务");
        try {
            int count = inboundOrderSyncService.syncAllWarehouses();
            String message = String.format("海外仓入库单同步任务执行完成，成功同步%d个仓库入库单", count);
            SnailJobLog.LOCAL.info(message);
            return ExecuteResult.success(message);
        } catch (Exception e) {
            String errorMessage = "海外仓入库单同步任务执行失败: " +
                StringUtils.defaultIfEmpty(e.getMessage(), e.getClass().getName());
            SnailJobLog.LOCAL.error(errorMessage, e);
            return ExecuteResult.failure(errorMessage);
        }
    }
}
