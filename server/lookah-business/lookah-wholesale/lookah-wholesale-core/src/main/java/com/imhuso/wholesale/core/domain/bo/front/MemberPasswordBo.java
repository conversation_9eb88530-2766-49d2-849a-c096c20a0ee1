package com.imhuso.wholesale.core.domain.bo.front;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 批发会员密码更新业务对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class MemberPasswordBo implements Serializable {

    /**
     * 旧密码
     */
    @NotBlank(message = "{wholesale.member.old.password.not.blank}")
    private String oldPassword;

    /**
     * 新密码
     */
    @NotBlank(message = "{wholesale.member.new.password.not.blank}")
    @Size(min = 6, max = 50, message = "{wholesale.member.password.size}")
    private String newPassword;

}
