package com.imhuso.wholesale.core.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.domain.WhsProduct;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.enums.PackagingType;
import com.imhuso.wholesale.core.mapper.WhsProductVariantMapper;
import com.imhuso.wholesale.core.service.IWhsProductFeatureService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 商品特征服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsProductFeatureServiceImpl implements IWhsProductFeatureService {

    private final WhsProductVariantMapper variantMapper;

    /**
     * 判断两个产品是否有相同的规格属性（除包装类型外）
     */
    @Override
    public boolean hasSameSkuSpecs(WhsProduct p1, WhsProduct p2) {
        try {
            // 获取产品1的默认变体
            WhsProductVariant variant1 = variantMapper.selectOne(new LambdaQueryWrapper<WhsProductVariant>().eq(WhsProductVariant::getProductId, p1.getId()).eq(WhsProductVariant::getStatus, BusinessConstants.NORMAL).orderByAsc(WhsProductVariant::getId).last("LIMIT 1"));

            // 获取产品2的默认变体
            WhsProductVariant variant2 = variantMapper.selectOne(new LambdaQueryWrapper<WhsProductVariant>().eq(WhsProductVariant::getProductId, p2.getId()).eq(WhsProductVariant::getStatus, BusinessConstants.NORMAL).orderByAsc(WhsProductVariant::getId).last("LIMIT 1"));

            if (variant1 == null || variant2 == null) {
                return false;
            }

            // 获取产品1的规格属性
            JSONObject specsObj1 = null;
            if (StringUtils.isNotBlank(variant1.getSpecs())) {
                specsObj1 = JSONUtil.parseObj(variant1.getSpecs());
            }

            // 获取产品2的规格属性
            JSONObject specsObj2 = null;
            if (StringUtils.isNotBlank(variant2.getSpecs())) {
                specsObj2 = JSONUtil.parseObj(variant2.getSpecs());
            }

            // 如果任一产品没有规格属性，返回false
            if (specsObj1 == null || specsObj2 == null) {
                return false;
            }

            // 比较各种规格属性
            String[] specKeys = {"color", "flavor", "function", "size", "material"};
            for (String key : specKeys) {
                if (!hasSameSkuSpec(p1, p2, key)) {
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("比较产品规格属性失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 比较特定规格属性
     */
    @Override
    public boolean hasSameSkuSpec(WhsProduct p1, WhsProduct p2, String specKey) {
        try {
            // 获取产品1的默认变体
            WhsProductVariant variant1 = variantMapper.selectOne(new LambdaQueryWrapper<WhsProductVariant>().eq(WhsProductVariant::getProductId, p1.getId()).eq(WhsProductVariant::getStatus, BusinessConstants.NORMAL).orderByAsc(WhsProductVariant::getId).last("LIMIT 1"));

            // 获取产品2的默认变体
            WhsProductVariant variant2 = variantMapper.selectOne(new LambdaQueryWrapper<WhsProductVariant>().eq(WhsProductVariant::getProductId, p2.getId()).eq(WhsProductVariant::getStatus, BusinessConstants.NORMAL).orderByAsc(WhsProductVariant::getId).last("LIMIT 1"));

            if (variant1 == null || variant2 == null) {
                return false;
            }

            // 获取产品1的规格属性值
            String spec1 = null;
            if (StringUtils.isNotBlank(variant1.getSpecs())) {
                JSONObject specsObj1 = JSONUtil.parseObj(variant1.getSpecs());
                spec1 = specsObj1.getStr(specKey);
            }

            // 获取产品2的规格属性值
            String spec2 = null;
            if (StringUtils.isNotBlank(variant2.getSpecs())) {
                JSONObject specsObj2 = JSONUtil.parseObj(variant2.getSpecs());
                spec2 = specsObj2.getStr(specKey);
            }

            // 如果两个规格都为空，视为相同
            if (StringUtils.isBlank(spec1) && StringUtils.isBlank(spec2)) {
                return true;
            }

            // 如果只有一个规格为空，视为不同
            if (StringUtils.isBlank(spec1) || StringUtils.isBlank(spec2)) {
                return false;
            }

            // 比较规格值是否相同
            return spec1.equals(spec2);
        } catch (Exception e) {
            log.error("比较产品规格[{}]属性失败: {}", specKey, e.getMessage());
            return false;
        }
    }

    /**
     * 判断是否存在指定包装类型且规格相同的产品
     */
    @Override
    public boolean hasNoPackagingTypeWithSameSpecs(List<WhsProduct> variants, Integer packagingType, WhsProduct targetProduct) {
        return variants.stream().noneMatch(p -> {
            WhsProductVariant variant = variantMapper.selectOne(new LambdaQueryWrapper<WhsProductVariant>().eq(WhsProductVariant::getProductId, p.getId()).eq(WhsProductVariant::getStatus, BusinessConstants.NORMAL).orderByAsc(WhsProductVariant::getId).last("LIMIT 1"));

            if (variant == null || StringUtils.isBlank(variant.getSpecs())) {
                return false;
            }

            try {
                JSONObject specsObj = JSONUtil.parseObj(variant.getSpecs());
                String packagingTypeStr = specsObj.getStr("packaging");
                return String.valueOf(packagingType).equals(packagingTypeStr) && hasSameSkuSpecs(p, targetProduct);
            } catch (Exception e) {
                return false;
            }
        });
    }

    /**
     * 生成产品特征键
     */
    @Override
    public String generateFeatureKey(WhsProductVariant variant) {
        if (variant == null || StringUtils.isBlank(variant.getSpecs())) {
            return "";
        }

        try {
            JSONObject specsObj = JSONUtil.parseObj(variant.getSpecs());
            StringBuilder key = new StringBuilder();

            // 添加各规格属性到键中，忽略包装类型
            appendSpecToKey(key, specsObj, "color");
            appendSpecToKey(key, specsObj, "flavor");
            appendSpecToKey(key, specsObj, "function");
            appendSpecToKey(key, specsObj, "size");
            appendSpecToKey(key, specsObj, "material");

            return key.toString();
        } catch (Exception e) {
            log.error("生成产品特征键失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 在特征键中添加规格
     */
    private void appendSpecToKey(StringBuilder key, JSONObject specsObj, String specKey) {
        String specValue = specsObj.getStr(specKey);
        if (StringUtils.isNotBlank(specValue)) {
            if (!key.isEmpty()) {
                key.append("-");
            }
            key.append(specKey).append(":").append(specValue);
        }
    }

    /**
     * 判断是否应在变体中显示
     */
    @Override
    public boolean shouldShowInVariants(WhsProduct product, List<WhsProduct> variants) {
        try {
            // 获取产品默认变体
            WhsProductVariant variant = variantMapper.selectOne(new LambdaQueryWrapper<WhsProductVariant>().eq(WhsProductVariant::getProductId, product.getId()).eq(WhsProductVariant::getStatus, "0").orderByAsc(WhsProductVariant::getId).last("LIMIT 1"));

            if (variant == null || StringUtils.isBlank(variant.getSpecs())) {
                return true;
            }

            // 获取包装类型
            JSONObject specsObj = JSONUtil.parseObj(variant.getSpecs());
            String packagingTypeStr = specsObj.getStr("packaging");
            if (StringUtils.isBlank(packagingTypeStr)) {
                return true;
            }

            // 如果是箱装，始终显示
            if (String.valueOf(PackagingType.CASE.getValue()).equals(packagingTypeStr)) {
                return true;
            }

            // 如果是展示盒，只有在同系列同规格下没有箱装时才显示
            if (String.valueOf(PackagingType.DISPLAY.getValue()).equals(packagingTypeStr)) {
                return hasNoPackagingTypeWithSameSpecs(variants, PackagingType.CASE.getValue(), product);
            }

            // 如果是单品，只有在同系列同规格下既没有箱装也没有展示盒时才显示
            return String.valueOf(PackagingType.INDIVIDUAL.getValue()).equals(packagingTypeStr) && hasNoPackagingTypeWithSameSpecs(variants, PackagingType.CASE.getValue(), product) && hasNoPackagingTypeWithSameSpecs(variants, PackagingType.DISPLAY.getValue(), product);
        } catch (Exception e) {
            log.error("判断产品显示逻辑失败: {}", e.getMessage());
            return true;
        }
    }

    /**
     * 判断是否是最大包装规格
     */
    @Override
    public boolean isLargestPackaging(WhsProduct product, List<WhsProduct> sameFeatureProducts) {
        try {
            // 获取当前产品变体
            WhsProductVariant currentVariant = variantMapper.selectOne(new LambdaQueryWrapper<WhsProductVariant>().eq(WhsProductVariant::getProductId, product.getId()).eq(WhsProductVariant::getStatus, "0").orderByAsc(WhsProductVariant::getId).last("LIMIT 1"));

            if (currentVariant == null || StringUtils.isBlank(currentVariant.getSpecs())) {
                return false;
            }

            // 获取当前产品的包装类型
            JSONObject currentSpecs = JSONUtil.parseObj(currentVariant.getSpecs());
            String currentPackagingStr = currentSpecs.getStr("packaging");
            if (StringUtils.isBlank(currentPackagingStr)) {
                return false;
            }

            int currentPackaging = Integer.parseInt(currentPackagingStr);

            // 检查是否有包装类型大于当前产品的同特征产品
            for (WhsProduct otherProduct : sameFeatureProducts) {
                if (Objects.equals(otherProduct.getId(), product.getId())) {
                    continue; // 跳过当前产品
                }

                // 获取其他产品变体
                WhsProductVariant otherVariant = variantMapper.selectOne(new LambdaQueryWrapper<WhsProductVariant>().eq(WhsProductVariant::getProductId, otherProduct.getId()).eq(WhsProductVariant::getStatus, "0").orderByAsc(WhsProductVariant::getId).last("LIMIT 1"));

                if (otherVariant == null || StringUtils.isBlank(otherVariant.getSpecs())) {
                    continue;
                }

                // 检查其他产品的包装类型
                JSONObject otherSpecs = JSONUtil.parseObj(otherVariant.getSpecs());
                String otherPackagingStr = otherSpecs.getStr("packaging");
                if (StringUtils.isBlank(otherPackagingStr)) {
                    continue;
                }

                int otherPackaging = Integer.parseInt(otherPackagingStr);

                // 如果存在包装类型大于当前产品的，则当前产品不是最大包装
                if (otherPackaging > currentPackaging) {
                    return false;
                }
            }

            // 没有找到包装类型更大的同特征产品，当前产品是最大包装
            return true;
        } catch (Exception e) {
            log.error("判断产品包装大小失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 根据包装类型计算数量
     */
    @Override
    public int calculateQuantityByPackagingType(int parentType, int childType) {
        // 箱装 -> 展示盒
        if (parentType == PackagingType.CASE.getValue() && childType == PackagingType.DISPLAY.getValue()) {
            return 5; // 一箱包含5个展示盒
        }

        // 箱装 -> 单品
        if (parentType == PackagingType.CASE.getValue() && childType == PackagingType.INDIVIDUAL.getValue()) {
            return 30; // 一箱包含30个单品
        }

        // 展示盒 -> 单品
        if (parentType == PackagingType.DISPLAY.getValue() && childType == PackagingType.INDIVIDUAL.getValue()) {
            return 6; // 一个展示盒包含6个单品
        }

        // 其他情况，默认为1
        return 1;
    }

    /**
     * 计算产品件数
     */
    @Override
    public Integer calculatePcs(WhsProductVariant variant) {
        if (variant == null || StringUtils.isBlank(variant.getSpecs())) {
            return 1; // 默认为1件
        }

        try {
            JSONObject specsObj = JSONUtil.parseObj(variant.getSpecs());
            String packagingTypeStr = specsObj.getStr("packaging");
            String pcsStr = specsObj.getStr("pcs");

            // 如果指定了件数，直接返回
            if (StringUtils.isNotBlank(pcsStr)) {
                try {
                    return Integer.parseInt(pcsStr);
                } catch (NumberFormatException e) {
                    // 解析失败，使用默认逻辑
                }
            }

            // 根据包装类型计算件数
            if (StringUtils.isNotBlank(packagingTypeStr)) {
                int packagingType = Integer.parseInt(packagingTypeStr);

                if (packagingType == PackagingType.CASE.getValue()) {
                    return 30; // 箱装默认30件
                } else if (packagingType == PackagingType.DISPLAY.getValue()) {
                    return 6; // 展示盒默认6件
                }
            }

            // 默认为1件
            return 1;
        } catch (Exception e) {
            log.error("计算产品件数失败: {}", e.getMessage());
            return 1;
        }
    }
}
