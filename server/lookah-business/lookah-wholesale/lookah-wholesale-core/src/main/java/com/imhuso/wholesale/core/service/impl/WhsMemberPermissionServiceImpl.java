package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.constant.MemberPermissionConstants;
import com.imhuso.wholesale.core.domain.WhsMemberPermission;
import com.imhuso.wholesale.core.domain.bo.admin.WhsMemberPermissionBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsMemberPermissionVo;
import com.imhuso.wholesale.core.mapper.WhsMemberPermissionMapper;
import com.imhuso.wholesale.core.service.IWhsMemberPermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 客户权限服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsMemberPermissionServiceImpl implements IWhsMemberPermissionService {

    private final WhsMemberPermissionMapper baseMapper;

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<WhsMemberPermission> buildQueryWrapper(WhsMemberPermissionBo bo) {
        LambdaQueryWrapper<WhsMemberPermission> lqw = new LambdaQueryWrapper<>();
        lqw.eq(bo.getMemberId() != null, WhsMemberPermission::getMemberId, bo.getMemberId());
        lqw.eq(StringUtils.isNotBlank(bo.getPermissionKey()), WhsMemberPermission::getPermissionKey, bo.getPermissionKey());
        lqw.eq(StringUtils.isNotBlank(bo.getPermissionValue()), WhsMemberPermission::getPermissionValue, bo.getPermissionValue());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), WhsMemberPermission::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 查询客户权限列表
     */
    @Override
    public TableDataInfo<WhsMemberPermissionVo> queryPageList(WhsMemberPermissionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WhsMemberPermission> lqw = buildQueryWrapper(bo);
        Page<WhsMemberPermissionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 更新客户权限信息
     */
    @Override
    public int updatePermission(WhsMemberPermissionBo bo) {
        WhsMemberPermission permission = MapstructUtils.convert(bo, WhsMemberPermission.class);
        return baseMapper.updateById(permission);
    }

    /**
     * 获取客户所有权限
     */
    @Override
    public List<WhsMemberPermissionVo> getMemberPermissions(Long memberId) {
        LambdaQueryWrapper<WhsMemberPermission> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WhsMemberPermission::getMemberId, memberId);
        lqw.eq(WhsMemberPermission::getStatus, BusinessConstants.NORMAL);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 获取客户权限Map
     */
    @Override
    public Map<String, String> getMemberPermissionMap(Long memberId) {
        List<WhsMemberPermissionVo> permissions = getMemberPermissions(memberId);
        if (CollUtil.isEmpty(permissions)) {
            return new HashMap<>();
        }
        return permissions.stream().collect(Collectors.toMap(WhsMemberPermissionVo::getPermissionKey, WhsMemberPermissionVo::getPermissionValue));
    }

    /**
     * 批量设置客户权限
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSetPermissions(Long memberId, Map<String, String> permissions) {
        if (ObjectUtil.isNull(memberId) || CollUtil.isEmpty(permissions)) {
            return false;
        }

        // 先删除客户所有权限
        LambdaQueryWrapper<WhsMemberPermission> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WhsMemberPermission::getMemberId, memberId);
        baseMapper.delete(lqw);

        // 批量插入新权限
        for (Map.Entry<String, String> entry : permissions.entrySet()) {
            WhsMemberPermission permission = new WhsMemberPermission();
            permission.setMemberId(memberId);
            permission.setPermissionKey(entry.getKey());
            permission.setPermissionValue(entry.getValue());
            permission.setStatus(BusinessConstants.NORMAL);
            baseMapper.insert(permission);
        }

        return true;
    }

    @Override
    public Map<String, String> getDefaultPermissions() {
        Map<String, String> defaults = new HashMap<>();
        defaults.put(MemberPermissionConstants.SHOW_PRICE, MemberPermissionConstants.VALUE_TRUE);
        defaults.put(MemberPermissionConstants.ALLOW_ORDER, MemberPermissionConstants.VALUE_TRUE);
        defaults.put(MemberPermissionConstants.SHOW_STOCK, MemberPermissionConstants.VALUE_TRUE);
        return defaults;
    }
}
