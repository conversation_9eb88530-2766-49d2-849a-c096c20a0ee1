package com.imhuso.wholesale.core.domain.vo.admin;

import com.imhuso.wholesale.core.domain.WhsOrderFile;
import com.imhuso.common.translation.annotation.Translation;
import com.imhuso.common.translation.constant.TransConstant;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 批发订单文件关联视图对象 whs_order_file
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsOrderFile.class)
public class WhsOrderFileVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 文件关联ID
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * OSS文件ID
     */
    private Long ossId;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件类型描述
     */
    private String fileTypeText;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件MIME类型
     */
    private String contentType;

    /**
     * 文件URL（通过OSS服务获取）
     */
    private String fileUrl;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建者姓名（关联字段）
     */
    @Translation(type = TransConstant.USER_ID_TO_NICKNAME, mapper = "createBy")
    private String createByName;

    /**
     * 更新者
     */
    private String updateBy;
}
