package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 订单发货对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_order_shipment")
public class WhsOrderShipment extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 发货ID
     */
    @TableId
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 发货状态(0-待发货 1-处理中 2-部分发货 3-已发货 4-已送达)
     */
    private Integer shipmentStatus;

    /**
     * 批次号
     */
    private Integer batchNumber;

    /**
     * 发货日期
     */
    private Date shippedDate;
}
