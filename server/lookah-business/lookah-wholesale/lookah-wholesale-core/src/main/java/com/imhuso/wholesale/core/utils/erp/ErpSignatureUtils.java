package com.imhuso.wholesale.core.utils.erp;

import com.imhuso.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.UUID;

/**
 * ERP签名工具类 - 最佳实践版本
 * 基于行业标准的API Key + Request Hash方案
 * 
 * 设计原则：
 * 1. 简化签名算法，消除对HTTP参数格式的依赖
 * 2. 使用标准化的字符串构建方式  
 * 3. 对请求内容进行整体hash，避免参数排序问题
 * 4. 易于理解、实现和维护
 *
 * <AUTHOR>
 */
@Slf4j
public class ErpSignatureUtils {

    private static final String HMAC_SHA256 = "HmacSHA256";
    private static final String SHA256 = "SHA-256";

    /**
     * 签名结果对象
     */
    public static class SignatureResult {
        private final String signature;
        private final String timestamp;
        private final String nonce;

        public SignatureResult(String signature, String timestamp, String nonce) {
            this.signature = signature;
            this.timestamp = timestamp;
            this.nonce = nonce;
        }

        public String getSignature() { return signature; }
        public String getTimestamp() { return timestamp; }
        public String getNonce() { return nonce; }
    }

    /**
     * 生成API签名 - 最佳实践版本
     * 
     * 签名算法：
     * 1. StringToSign = HTTPMethod + "\n" + RequestURI + "\n" + QueryStringHash + "\n" + Timestamp + "\n" + Nonce
     * 2. Signature = HMAC-SHA256(AppSecret, StringToSign)
     *
     * @param method        HTTP方法 (GET, POST等)
     * @param uri           请求URI (不含域名，如: /api/v1/product_stock/getProductStockList)
     * @param queryString   完整的查询字符串 (如: skus[]=v1&skus[]=v2&page=1)
     * @param appSecret     应用密钥
     * @return 签名结果对象
     */
    public static SignatureResult generateSignature(String method, String uri, String queryString, String appSecret) {
        try {
            // 1. 生成时间戳和随机数
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
            String nonce = UUID.randomUUID().toString().replace("-", "").substring(0, 16);

            // 2. 对查询字符串进行SHA-256 hash，避免参数格式依赖
            String queryHash = "";
            if (queryString != null && !queryString.trim().isEmpty()) {
                queryHash = sha256Hash(queryString);
            }

            // 3. 构建标准化的签名字符串
            StringBuilder stringToSign = new StringBuilder();
            stringToSign.append(method.toUpperCase()).append("\n");
            stringToSign.append(uri).append("\n");
            stringToSign.append(queryHash).append("\n");
            stringToSign.append(timestamp).append("\n");
            stringToSign.append(nonce);

            // 4. 使用HMAC-SHA256计算签名
            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec secretKey = new SecretKeySpec(appSecret.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
            mac.init(secretKey);
            byte[] hash = mac.doFinal(stringToSign.toString().getBytes(StandardCharsets.UTF_8));

            // 5. 转换为十六进制
            String signature = bytesToHex(hash);

            log.debug("签名算法V2 - StringToSign: {}", stringToSign.toString());
            log.debug("签名算法V2 - QueryString: {}", queryString);
            log.debug("签名算法V2 - QueryHash: {}", queryHash);
            log.debug("签名算法V2 - Signature: {}", signature);
            log.debug("签名算法V2 - Timestamp: {}, Nonce: {}", timestamp, nonce);

            return new SignatureResult(signature, timestamp, nonce);

        } catch (Exception e) {
            log.error("生成签名失败", e);
            throw new ServiceException("生成签名失败: " + e.getMessage());
        }
    }

    /**
     * 便捷方法：从参数Map生成查询字符串并签名
     * 注意：这个方法会根据HTTP库的实际发送格式构建查询字符串
     * 
     * @param method     HTTP方法
     * @param uri        请求URI
     * @param params     参数Map（支持数组）
     * @param appSecret  应用密钥
     * @return 签名结果对象
     */
    public static SignatureResult generateSignatureFromParams(String method, String uri, 
                                                             java.util.Map<String, String[]> params, 
                                                             String appSecret) {
        // 构建与Hutool HTTP库一致的查询字符串格式
        StringBuilder queryString = new StringBuilder();
        if (params != null && !params.isEmpty()) {
            boolean first = true;
            for (java.util.Map.Entry<String, String[]> entry : params.entrySet()) {
                String key = entry.getKey();
                String[] values = entry.getValue();
                
                for (String value : values) {
                    if (!first) {
                        queryString.append("&");
                    }
                    // Hutool对数组参数自动添加[]，这里模拟相同的行为
                    if (values.length > 1 && !key.endsWith("[]")) {
                        queryString.append(key).append("[]");
                    } else {
                        queryString.append(key);
                    }
                    queryString.append("=").append(value != null ? value : "");
                    first = false;
                }
            }
        }
        
        return generateSignature(method, uri, queryString.toString(), appSecret);
    }

    /**
     * SHA-256 hash计算
     */
    private static String sha256Hash(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance(SHA256);
            byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hash);
        } catch (Exception e) {
            log.error("SHA-256 hash计算失败", e);
            throw new ServiceException("Hash计算失败: " + e.getMessage());
        }
    }

    /**
     * 字节数组转十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                result.append('0');
            }
            result.append(hex);
        }
        return result.toString();
    }

    /**
     * 生成随机字符串
     */
    public static String generateNonce(int length) {
        return UUID.randomUUID().toString().replace("-", "").substring(0, Math.min(length, 32));
    }

    /**
     * 验证签名（服务端使用）
     * 
     * @param method        HTTP方法
     * @param uri           请求URI  
     * @param queryString   查询字符串
     * @param timestamp     时间戳
     * @param nonce         随机数
     * @param signature     客户端签名
     * @param appSecret     应用密钥
     * @return 验证结果
     */
    public static boolean verifySignature(String method, String uri, String queryString, 
                                        String timestamp, String nonce, String signature, 
                                        String appSecret) {
        try {
            // 1. 检查时间戳有效性（5分钟内）
            long currentTime = System.currentTimeMillis() / 1000;
            long requestTime = Long.parseLong(timestamp);
            if (Math.abs(currentTime - requestTime) > 300) {
                log.warn("签名验证失败：时间戳过期，当前时间: {}, 请求时间: {}", currentTime, requestTime);
                return false;
            }

            // 2. 重新计算签名
            String queryHash = "";
            if (queryString != null && !queryString.trim().isEmpty()) {
                queryHash = sha256Hash(queryString);
            }

            StringBuilder stringToSign = new StringBuilder();
            stringToSign.append(method.toUpperCase()).append("\n");
            stringToSign.append(uri).append("\n");
            stringToSign.append(queryHash).append("\n");
            stringToSign.append(timestamp).append("\n");
            stringToSign.append(nonce);

            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec secretKey = new SecretKeySpec(appSecret.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
            mac.init(secretKey);
            byte[] hash = mac.doFinal(stringToSign.toString().getBytes(StandardCharsets.UTF_8));
            String expectedSignature = bytesToHex(hash);

            boolean valid = expectedSignature.equals(signature);
            
            if (!valid) {
                log.debug("签名验证失败详情:");
                log.debug("StringToSign: {}", stringToSign.toString());
                log.debug("QueryString: {}", queryString);
                log.debug("QueryHash: {}", queryHash);
                log.debug("Expected: {}", expectedSignature);
                log.debug("Actual: {}", signature);
            }
            
            return valid;

        } catch (Exception e) {
            log.error("签名验证异常", e);
            return false;
        }
    }

    /**
     * 验证Webhook签名
     *
     * @param payload   请求体
     * @param signature 签名
     * @param appSecret 应用密钥
     * @return 验证结果
     */
    public static boolean verifyWebhookSignature(String payload, String signature, String appSecret) {
        if (signature == null || !signature.startsWith("sha256=")) {
            log.warn("无效的签名格式: {}", signature);
            return false;
        }

        String expectedSignature = signature.substring(7); // 移除 "sha256=" 前缀

        try {
            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec secretKey = new SecretKeySpec(appSecret.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
            mac.init(secretKey);
            byte[] hash = mac.doFinal(payload.getBytes(StandardCharsets.UTF_8));

            String hexString = bytesToHex(hash);
            boolean isValid = expectedSignature.equals(hexString);
            log.debug("Webhook签名验证结果: {}", isValid);
            return isValid;

        } catch (Exception e) {
            log.error("Webhook签名验证失败", e);
            return false;
        }
    }
}
