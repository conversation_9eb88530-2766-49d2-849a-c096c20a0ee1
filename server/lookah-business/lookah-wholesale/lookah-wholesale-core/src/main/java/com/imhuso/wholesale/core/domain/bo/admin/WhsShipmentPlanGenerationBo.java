package com.imhuso.wholesale.core.domain.bo.admin;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.imhuso.wholesale.core.domain.WhsOrderItem;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 发货方案项生成业务对象
 * 用于在发货方案生成过程中传递和处理发货项的相关信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WhsShipmentPlanGenerationBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 方案ID，生成后自动填充
     */
    @JsonIgnore
    private Long planId;

    /**
     * 是否为自定义方案
     * 数据交换时使用，不可以从用户输入
     */
    @JsonIgnore
    private boolean isCustom;

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    /**
     * 方案名称
     */
    private String planName;

    /**
     * 订单项
     */
    @JsonIgnore
    private List<WhsOrderItem> orderItems;

    /**
     * 转换记录
     */
    @JsonIgnore
    private List<WhsShipmentConversionBo> conversions;

    /**
     * 分配详情列表
     */
    @Valid
    @NotEmpty(message = "方案明细不能为空")
    private List<PlanItem> planItems;

    /**
     * 方案明细项：变体ID-数量
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PlanItem implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 变体ID
         */
        @NotNull(message = "变体ID不能为空")
        private Long variantId;

        /**
         * 分配数量
         */
        @NotNull(message = "分配数量不能为空")
        private Integer quantity;
    }
}
