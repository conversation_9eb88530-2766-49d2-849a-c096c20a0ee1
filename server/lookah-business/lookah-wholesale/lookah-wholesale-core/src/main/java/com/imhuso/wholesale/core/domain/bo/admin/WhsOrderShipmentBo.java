package com.imhuso.wholesale.core.domain.bo.admin;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 订单发货业务对象 (用于确认发货)
 *
 * <AUTHOR>
 */
@Data
public class WhsOrderShipmentBo {

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    /**
     * 发货方案ID (确认发货时必须提供)
     */
    @NotNull(message = "发货方案ID不能为空")
    private Long planId;

    /**
     * 发货明细列表
     */
    @Valid
    @NotEmpty(message = "发货明细不能为空")
    private List<WhsOrderShipmentItemBo> items;
}
