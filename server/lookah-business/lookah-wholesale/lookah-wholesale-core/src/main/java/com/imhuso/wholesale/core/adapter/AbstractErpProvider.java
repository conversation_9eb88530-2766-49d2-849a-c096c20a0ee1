package com.imhuso.wholesale.core.adapter;

import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.wholesale.core.domain.bo.admin.ErpStockSyncBo;
import com.imhuso.wholesale.core.domain.vo.admin.ErpSyncResultVo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * ERP提供商抽象基类
 * 提供通用的ERP操作实现
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractErpProvider implements IErpProvider {

    /**
     * 验证SKU编码列表
     *
     * @param skuCodes SKU编码列表
     * @return 是否有效
     */
    protected boolean validateSkuCodes(List<String> skuCodes) {
        if (skuCodes == null || skuCodes.isEmpty()) {
            log.warn(MessageUtils.message("erp.stock.sync.empty.sku.codes"));
            return false;
        }
        return true;
    }

    /**
     * 验证库存数据列表
     *
     * @param stockDataList 库存数据列表
     * @return 是否有效
     */
    protected boolean validateStockData(List<ErpStockSyncBo> stockDataList) {
        if (stockDataList == null || stockDataList.isEmpty()) {
            log.warn(MessageUtils.message("erp.stock.sync.empty.stock.data"));
            return false;
        }
        return true;
    }

    /**
     * 记录同步开始日志
     *
     * @param operation 操作类型
     * @param count 数据数量
     */
    protected void logSyncStart(String operation, int count) {
        log.info(MessageUtils.message("erp.stock.sync.operation.start"), 
                getProviderName(), operation, count);
    }

    /**
     * 记录同步完成日志
     *
     * @param operation 操作类型
     * @param result 同步结果
     */
    protected void logSyncComplete(String operation, ErpSyncResultVo result) {
        if (result.getSuccess()) {
            log.info(MessageUtils.message("erp.stock.sync.operation.success"), 
                    getProviderName(), operation, result.getSuccessCount(), result.getTotalCount());
        } else {
            log.error(MessageUtils.message("erp.stock.sync.operation.failure"), 
                    getProviderName(), operation, result.getErrorMessage());
        }
    }

    /**
     * 处理ERP API异常
     *
     * @param operation 操作类型
     * @param e 异常
     * @return 失败结果
     */
    protected ErpSyncResultVo handleException(String operation, Exception e) {
        String errorMessage = MessageUtils.message("erp.stock.sync.api.error", operation, e.getMessage());
        log.error(errorMessage, e);
        return ErpSyncResultVo.failure(errorMessage, getProviderName());
    }

    /**
     * 创建空的库存数据列表（用于连接失败时）
     *
     * @return 空列表
     */
    protected List<ErpStockSyncBo> createEmptyStockList() {
        return new ArrayList<>();
    }

    /**
     * 检查ERP连接状态的默认实现
     * 子类可以重写此方法实现具体的连接检查逻辑
     *
     * @return 连接状态
     */
    @Override
    public boolean isAvailable() {
        try {
            return checkConnection();
        } catch (Exception e) {
            log.warn(MessageUtils.message("erp.provider.connection.check.failed"), 
                    getProviderName(), e.getMessage());
            return false;
        }
    }

    /**
     * 检查ERP系统连接
     * 子类需要实现具体的连接检查逻辑
     *
     * @return 连接是否正常
     */
    protected abstract boolean checkConnection();

    /**
     * 获取ERP系统配置信息的默认实现
     *
     * @return 配置信息
     */
    @Override
    public String getConfigInfo() {
        return String.format("ERP Provider: %s, Type: %s, Available: %s", 
                getProviderName(), getProviderType(), isAvailable());
    }
}
