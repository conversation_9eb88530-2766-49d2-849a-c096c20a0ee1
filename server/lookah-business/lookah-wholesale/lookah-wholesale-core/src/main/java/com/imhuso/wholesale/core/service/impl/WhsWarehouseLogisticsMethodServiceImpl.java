package com.imhuso.wholesale.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.constant.WhsCacheConstants;
import com.imhuso.wholesale.core.domain.WhsWarehouseLogisticsMethod;
import com.imhuso.wholesale.core.domain.bo.admin.WhsWarehouseLogisticsMethodBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsWarehouseLogisticsMethodVo;
import com.imhuso.wholesale.core.domain.vo.warehouse.LogisticsChannelSyncResultVo;
import com.imhuso.wholesale.core.mapper.WhsWarehouseLogisticsMethodMapper;
import com.imhuso.wholesale.core.service.IWhsLogisticsChannelSyncService;
import com.imhuso.wholesale.core.service.IWhsWarehouseLogisticsMethodService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

/**
 * 仓库物流方式服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsWarehouseLogisticsMethodServiceImpl implements IWhsWarehouseLogisticsMethodService {

    private final WhsWarehouseLogisticsMethodMapper baseMapper;
    private final IWhsLogisticsChannelSyncService logisticsChannelSyncService;

    /**
     * 查询仓库物流方式分页列表
     *
     * @param bo        查询参数
     * @param pageQuery 分页参数
     * @return 分页列表
     */
    @Override
    public TableDataInfo<WhsWarehouseLogisticsMethodVo> queryPageList(WhsWarehouseLogisticsMethodBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WhsWarehouseLogisticsMethod> lqw = buildQueryWrapper(bo);
        Page<WhsWarehouseLogisticsMethod> page = pageQuery.build();
        Page<WhsWarehouseLogisticsMethodVo> pageResult = baseMapper.selectVoPage(page, lqw);
        return TableDataInfo.build(pageResult);
    }

    /**
     * 查询仓库物流方式列表
     *
     * @param bo 查询参数
     * @return 列表
     */
    @Override
    public List<WhsWarehouseLogisticsMethodVo> queryList(WhsWarehouseLogisticsMethodBo bo) {
        LambdaQueryWrapper<WhsWarehouseLogisticsMethod> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw, WhsWarehouseLogisticsMethodVo.class);
    }

    /**
     * 构建查询条件
     *
     * @param bo 业务对象
     * @return 查询条件
     */
    private LambdaQueryWrapper<WhsWarehouseLogisticsMethod> buildQueryWrapper(WhsWarehouseLogisticsMethodBo bo) {
        LambdaQueryWrapper<WhsWarehouseLogisticsMethod> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getWarehouseId() != null, WhsWarehouseLogisticsMethod::getWarehouseId, bo.getWarehouseId());
        lqw.eq(bo.getProviderId() != null, WhsWarehouseLogisticsMethod::getProviderId, bo.getProviderId());
        lqw.eq(bo.getAccountId() != null, WhsWarehouseLogisticsMethod::getAccountId, bo.getAccountId());
        lqw.like(StringUtils.isNotBlank(bo.getMethodCode()), WhsWarehouseLogisticsMethod::getMethodCode, bo.getMethodCode());
        lqw.like(StringUtils.isNotBlank(bo.getMethodName()), WhsWarehouseLogisticsMethod::getMethodName, bo.getMethodName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), WhsWarehouseLogisticsMethod::getStatus, bo.getStatus());
        lqw.orderByAsc(WhsWarehouseLogisticsMethod::getPriority);
        lqw.orderByDesc(WhsWarehouseLogisticsMethod::getCreateTime);

        return lqw;
    }

    /**
     * 根据ID获取详情
     *
     * @param id ID
     * @return 详情
     */
    @Override
    @Cacheable(cacheNames = WhsCacheConstants.WAREHOUSE_LOGISTICS_METHOD_CACHE, key = "'id:' + #id", unless = "#result == null")
    public WhsWarehouseLogisticsMethodVo getById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 根据仓库ID查询物流方式
     *
     * @param warehouseId 仓库ID
     * @return 物流方式列表
     */
    @Override
    @Cacheable(cacheNames = WhsCacheConstants.WAREHOUSE_LOGISTICS_METHOD_CACHE, key = "'all:' + #warehouseId", unless = "#result == null || #result.isEmpty()")
    public List<WhsWarehouseLogisticsMethodVo> queryByWarehouseId(Long warehouseId) {
        if (warehouseId == null) {
            return Collections.emptyList();
        }

        // 移除手动缓存检查
        LambdaQueryWrapper<WhsWarehouseLogisticsMethod> lqw = Wrappers.lambdaQuery();
        lqw.eq(WhsWarehouseLogisticsMethod::getWarehouseId, warehouseId);
        lqw.orderByAsc(WhsWarehouseLogisticsMethod::getPriority);
        lqw.orderByDesc(WhsWarehouseLogisticsMethod::getCreateTime);

        List<WhsWarehouseLogisticsMethodVo> result = baseMapper.selectVoList(lqw);

        // 移除手动缓存设置

        return result != null ? result : Collections.emptyList(); // 确保返回非null
    }

    /**
     * 同步海外仓物流渠道
     *
     * @param warehouseId 仓库ID
     * @return 同步结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {@CacheEvict(cacheNames = WhsCacheConstants.WAREHOUSE_LOGISTICS_METHOD_CACHE, key = "'all:' + #warehouseId"), @CacheEvict(cacheNames = WhsCacheConstants.WAREHOUSE_LOGISTICS_METHOD_CACHE, key = "'available:' + #warehouseId")})
    public Boolean syncLogisticsChannels(Long warehouseId) {
        log.info("开始同步海外仓物流渠道: warehouseId={}", warehouseId);

        if (warehouseId == null) {
            throw new ServiceException("仓库ID不能为空");
        }

        try {
            // 调用专门的物流渠道同步服务
            LogisticsChannelSyncResultVo result = logisticsChannelSyncService.syncLogisticsChannels(warehouseId);

            return result.isSuccess();
        } catch (Exception e) {
            log.error("同步海外仓物流渠道异常", e);
            throw new ServiceException("同步海外仓物流渠道失败: " + e.getMessage());
        }
    }
}
