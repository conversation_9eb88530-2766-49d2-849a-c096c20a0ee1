package com.imhuso.wholesale.core.domain.vo.admin;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.imhuso.wholesale.core.domain.WhsOrderItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 批发订单项后台视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsOrderItem.class)
public class WhsOrderItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单项ID
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品名称（冗余）
     */
    private String productName;

    /**
     * 变体ID(SKU)
     */
    private Long variantId;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 采购单价
     */
    private BigDecimal purchasePrice;

    /**
     * 销售单价（手动填入）
     */
    private BigDecimal salesPrice;

    /**
     * 小计金额
     */
    private BigDecimal amount;

    /**
     * 包装类型
     */
    private Integer packagingType;

    /**
     * 包装类型文本
     */
    private String packagingTypeText;

    /**
     * 包装数量(PCS)
     */
    @JsonIgnore
    private Integer packagingQuantity;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 规格快照
     */
    private String specsSnapshot;

    /**
     * 规格快照文本
     */
    private String specsSnapshotText;

    /**
     * 创建时间
     */
    private Date createTime;
}
