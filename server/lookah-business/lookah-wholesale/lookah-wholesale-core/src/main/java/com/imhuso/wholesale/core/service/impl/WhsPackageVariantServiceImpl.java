package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.bo.admin.WhsPackageVariantBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVariantAttributeVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVariantVo;
import com.imhuso.wholesale.core.enums.PackagingType;
import com.imhuso.wholesale.core.mapper.WhsProductVariantMapper;
import com.imhuso.wholesale.core.service.IWhsPackageItemService;
import com.imhuso.wholesale.core.service.IWhsPackageVariantService;
import com.imhuso.wholesale.core.service.IWhsProductVariantAttributeService;
import com.imhuso.wholesale.core.service.IWhsProductVariantService;
import com.imhuso.wholesale.core.utils.WhsEnumTranslationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 包装变体服务实现类
 * 注意：包装变体的价格优先使用前端传入值，如果未传入，则会根据源单品价格和包装数量自动计算。
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsPackageVariantServiceImpl implements IWhsPackageVariantService {

    private final WhsProductVariantMapper variantMapper;
    private final IWhsProductVariantAttributeService variantAttributeService;
    private final IWhsPackageItemService packageItemService;
    private final IWhsProductVariantService variantService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WhsProductVariantVo createPackageVariant(WhsPackageVariantBo packageVariantBo) {
        log.info("创建包装变体，参数: {}", JSONUtil.toJsonStr(packageVariantBo));

        // 1. 验证创建请求
        WhsProductVariant sourceVariant = validateCreateRequest(packageVariantBo);

        // 2. 准备变体数据
        WhsProductVariant newVariant = prepareVariantForCreation(packageVariantBo, sourceVariant);

        // 3. 计算包装价格
        calculatePackagePricing(packageVariantBo, sourceVariant, newVariant);

        // 4. 保存变体和关系
        return saveVariantAndRelations(packageVariantBo, sourceVariant, newVariant);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WhsProductVariantVo updatePackageVariant(WhsPackageVariantBo packageVariantBo) {
        log.info("更新包装变体，参数: {}", JSONUtil.toJsonStr(packageVariantBo));

        // 1. 验证更新请求
        WhsProductVariant variant = validateUpdateRequest(packageVariantBo);

        // 2. 更新包装关系
        updatePackageRelations(packageVariantBo, variant);

        // 3. 更新变体字段
        updateVariantFields(packageVariantBo);

        // 4. 返回更新后的变体详情
        return variantService.getVariant(variant.getId(), true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deletePackageVariant(Long id) {
        log.info("删除包装变体，ID: {}", id);

        // 1. 检查变体是否存在
        WhsProductVariant variant = variantMapper.selectById(id);
        if (variant == null) {
            throw new ServiceException("变体不存在");
        }

        // 2. 检查是否为包装变体
        if (variant.getPackagingType() == null || variant.getPackagingType().equals(PackagingType.INDIVIDUAL.getValue())) {
            throw new ServiceException("只能删除包装类型变体");
        }

        // 3. 先删除包装关系
        packageItemService.deleteByParentVariantId(id);

        // 4. 删除变体属性关联
        variantAttributeService.deleteByVariantId(id);

        // 5. 删除变体本身
        return variantMapper.deleteById(id);
    }

    /**
     * 检查源变体是否已经有对应的包装变体，支持排除指定变体ID
     *
     * @param sourceVariantId  源变体ID
     * @param packagingType    包装类型
     * @param excludeVariantId 需要排除的变体ID
     * @throws ServiceException 如果已存在对应包装类型的变体则抛出异常
     */
    private void checkPackageVariantUniqueness(Long sourceVariantId, Integer packagingType, Long excludeVariantId) {
        if (sourceVariantId == null || packagingType == null) {
            return;
        }

        // 查询是否已存在该源变体的包装关系，排除指定ID
        boolean exists = packageItemService.existsPackageForSourceAndType(sourceVariantId, packagingType, excludeVariantId);
        if (exists) {
            // 使用接口的静态翻译方法获取包装类型名称
            String typeText = WhsEnumTranslationUtils.translate(PackagingType.class, packagingType);
            throw new ServiceException("该单品已存在" + typeText + "类型的包装变体，不能重复创建");
        }
    }

    /**
     * 检查SKU编码唯一性
     *
     * @param skuCode   要检查的SKU编码
     * @param excludeId 需要排除的变体ID（用于更新场景）
     * @throws ServiceException 如果SKU已存在则抛出异常
     */
    private void checkSkuUniqueness(String skuCode, Long excludeId) {
        if (!StringUtils.hasText(skuCode)) {
            return;
        }

        WhsProductVariant existsVariant = variantMapper.selectOne(new LambdaQueryWrapper<WhsProductVariant>().eq(WhsProductVariant::getSkuCode, skuCode).ne(excludeId != null, WhsProductVariant::getId, excludeId));

        if (existsVariant != null) {
            throw new ServiceException("SKU编码 [" + skuCode + "] 已存在");
        }
    }

    /**
     * 验证创建包装变体的请求参数
     */
    private WhsProductVariant validateCreateRequest(WhsPackageVariantBo packageVariantBo) {
        // 检查源变体是否存在并验证为单品类型
        WhsProductVariant sourceVariant = validateSourceVariant(packageVariantBo.getSourceVariantId());

        // 检查源变体是否已经有对应的包装变体
        Long excludeVariantId = packageVariantBo.getId();
        checkPackageVariantUniqueness(sourceVariant.getId(), packageVariantBo.getPackagingType(), excludeVariantId);

        // 检查SKU唯一性
        checkSkuUniqueness(packageVariantBo.getSkuCode(), excludeVariantId);

        return sourceVariant;
    }

    /**
     * 准备变体数据用于创建或更新
     */
    private WhsProductVariant prepareVariantForCreation(WhsPackageVariantBo packageVariantBo, WhsProductVariant sourceVariant) {
        Long excludeVariantId = packageVariantBo.getId();
        WhsProductVariant newVariant;

        if (excludeVariantId != null) {
            // 编辑已有变体
            newVariant = variantMapper.selectById(excludeVariantId);
            if (newVariant == null) {
                throw new ServiceException("变体不存在");
            }

            // 更新变体信息
            newVariant.setPackagingType(packageVariantBo.getPackagingType());
            if (StringUtils.hasText(packageVariantBo.getSkuCode())) {
                newVariant.setSkuCode(packageVariantBo.getSkuCode());
            }
            if (packageVariantBo.getUpc() != null) {
                newVariant.setUpc(packageVariantBo.getUpc());
            }
        } else {
            // 创建新变体：使用通用的复制变体方法
            Map<String, Object> copyResult = variantService.copyVariantWithRelations(packageVariantBo.getSourceVariantId(), packageVariantBo.getSkuCode(), packageVariantBo.getUpc());

            newVariant = (WhsProductVariant) copyResult.get("variant");
        }

        // 设置包装变体特有属性
        newVariant.setPackagingType(packageVariantBo.getPackagingType());
        if (StringUtils.hasText(packageVariantBo.getStatus())) {
            newVariant.setStatus(packageVariantBo.getStatus());
        }

        // 包装变体直接继承源变体的specs，确保不为空
        if (newVariant.getSpecs() == null) {
            newVariant.setSpecs(sourceVariant.getSpecs() != null ? sourceVariant.getSpecs() : "{}");
        }

        return newVariant;
    }

    /**
     * 计算包装变体的价格
     */
    private void calculatePackagePricing(WhsPackageVariantBo packageVariantBo, WhsProductVariant sourceVariant, WhsProductVariant newVariant) {
        Integer quantity = packageVariantBo.getPackageQuantity();

        // 检查包装数量是否有效
        if (quantity == null || quantity <= 0) {
            throw new ServiceException("包装数量无效，无法计算价格");
        }

        // 设置批发价: 如果未传入，则基于源单品计算
        if (packageVariantBo.getWholesalePrice() == null) {
            if (sourceVariant.getWholesalePrice() == null) {
                throw new ServiceException("源单品批发价未设置，无法自动计算包装批发价");
            }
            BigDecimal calculatedWholesalePrice = sourceVariant.getWholesalePrice().multiply(BigDecimal.valueOf(quantity));
            newVariant.setWholesalePrice(calculatedWholesalePrice);
        } else {
            newVariant.setWholesalePrice(packageVariantBo.getWholesalePrice());
        }

        if (packageVariantBo.getMsrp() == null) {
            if (sourceVariant.getMsrp() == null) {
                throw new ServiceException("源单品建议零售价未设置，无法自动计算包装建议零售价");
            }
            BigDecimal calculatedMsrp = sourceVariant.getMsrp().multiply(BigDecimal.valueOf(quantity));
            newVariant.setMsrp(calculatedMsrp);
        } else {
            newVariant.setMsrp(packageVariantBo.getMsrp());
        }
    }

    /**
     * 保存变体和相关关系
     */
    private WhsProductVariantVo saveVariantAndRelations(WhsPackageVariantBo packageVariantBo, WhsProductVariant sourceVariant, WhsProductVariant newVariant) {
        Long excludeVariantId = packageVariantBo.getId();
        Integer quantity = packageVariantBo.getPackageQuantity();

        // 保存变体
        if (excludeVariantId != null) {
            variantMapper.updateById(newVariant);
        } else {
            variantMapper.insert(newVariant);

            // 复制源变体属性关联（只有新建时才需要复制属性)
            copyVariantAttributes(sourceVariant, newVariant);
        }

        // 保存包装关系
        if (excludeVariantId != null) {
            // 如果是更新，先删除旧关系
            packageItemService.deleteByParentVariantId(newVariant.getId());
        }
        packageItemService.savePackageItem(newVariant.getId(), sourceVariant.getId(), quantity);

        // 返回变体信息
        return variantService.getVariant(newVariant.getId(), true);
    }

    /**
     * 复制源变体的属性关联
     */
    private void copyVariantAttributes(WhsProductVariant sourceVariant, WhsProductVariant newVariant) {
        List<WhsProductVariantAttributeVo> sourceAttributes = variantAttributeService.getVariantAttributes(sourceVariant.getId());

        if (CollUtil.isNotEmpty(sourceAttributes)) {
            // 将属性转为映射格式
            Map<Long, Long> attributeMap = sourceAttributes.stream().collect(Collectors.toMap(WhsProductVariantAttributeVo::getAttributeId, WhsProductVariantAttributeVo::getAttributeValueId));

            // 处理变体属性关联
            variantAttributeService.processVariantAttributes(sourceVariant.getProductId(), newVariant.getId(), attributeMap);
        }
    }

    /**
     * 验证更新包装变体的请求参数
     */
    private WhsProductVariant validateUpdateRequest(WhsPackageVariantBo packageVariantBo) {
        // 检查变体是否存在
        WhsProductVariant variant = variantMapper.selectById(packageVariantBo.getId());
        if (variant == null) {
            throw new ServiceException("变体不存在");
        }

        // 检查是否为包装变体
        if (variant.getPackagingType() == null || variant.getPackagingType().equals(PackagingType.INDIVIDUAL.getValue())) {
            throw new ServiceException("只能更新包装类型变体");
        }

        // 检查SKU唯一性
        if (StringUtils.hasText(packageVariantBo.getSkuCode())) {
            checkSkuUniqueness(packageVariantBo.getSkuCode(), packageVariantBo.getId());
        }

        return variant;
    }

    /**
     * 更新包装关系
     */
    private void updatePackageRelations(WhsPackageVariantBo packageVariantBo, WhsProductVariant variant) {
        // 获取包装变体的源变体
        List<Long> childVariantIds = packageItemService.getChildVariantIds(variant.getId());
        if (childVariantIds.isEmpty()) {
            throw new ServiceException("包装变体数据异常，未找到关联的源变体");
        }
        Long sourceVariantId = childVariantIds.get(0);

        // 如果要修改源变体，需要检查新的源变体是否已被包装
        if (packageVariantBo.getSourceVariantId() != null && !packageVariantBo.getSourceVariantId().equals(sourceVariantId)) {

            validateNewSourceVariant(packageVariantBo, variant);

            // 更新包装关系
            packageItemService.deleteByParentVariantId(variant.getId());
            packageItemService.savePackageItem(variant.getId(), packageVariantBo.getSourceVariantId(), packageVariantBo.getPackageQuantity());
        } else if (packageVariantBo.getPackageQuantity() != null) {
            // 只更新数量
            packageItemService.savePackageItem(variant.getId(), sourceVariantId, packageVariantBo.getPackageQuantity());
        }
    }

    /**
     * 验证新的源变体
     */
    private void validateNewSourceVariant(WhsPackageVariantBo packageVariantBo, WhsProductVariant variant) {
        // 检查新源变体是否存在并验证为单品类型
        validateSourceVariant(packageVariantBo.getSourceVariantId());

        // 检查新源变体是否已经有对应的包装变体，排除当前变体
        boolean exists = packageItemService.existsPackageForSourceAndType(packageVariantBo.getSourceVariantId(), variant.getPackagingType(), packageVariantBo.getId());

        if (exists) {
            String typeText = Objects.equals(variant.getPackagingType(), PackagingType.DISPLAY.getValue()) ? "展示盒" : "箱装";
            throw new ServiceException("该单品已存在" + typeText + "类型的包装变体，不能重复创建");
        }
    }

    /**
     * 验证源变体是否存在且为单品类型
     */
    private WhsProductVariant validateSourceVariant(Long sourceVariantId) {
        WhsProductVariant sourceVariant = variantMapper.selectById(sourceVariantId);
        if (sourceVariant == null) {
            throw new ServiceException("源变体不存在");
        }

        // 检查源变体是否为单品类型
        if (sourceVariant.getPackagingType() != null && !sourceVariant.getPackagingType().equals(PackagingType.INDIVIDUAL.getValue())) {
            throw new ServiceException("只能基于单品变体创建包装变体");
        }

        return sourceVariant;
    }

    /**
     * 更新变体字段
     */
    private void updateVariantFields(WhsPackageVariantBo packageVariantBo) {
        // 使用 BeanUtil 复制非空字段，忽略包装类型等不应更新的字段
        WhsProductVariant updateEntity = new WhsProductVariant();
        BeanUtil.copyProperties(packageVariantBo, updateEntity, "sourceVariantId", "packageQuantity", "packagingType");

        // 执行更新
        variantMapper.updateById(updateEntity);
    }
}
