package com.imhuso.wholesale.core.domain.vo.warehouse;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 物流渠道同步结果视图对象
 *
 * <AUTHOR>
 */
@Data
public class LogisticsChannelSyncResultVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 同步是否成功
     */
    private boolean success = false;

    /**
     * 错误消息（如果同步失败）
     */
    private String errorMessage;

    /**
     * 警告消息（如果有）
     */
    private List<String> warnings = new ArrayList<>();

    /**
     * 处理的渠道数量
     */
    private int processedCount;

    /**
     * 成功同步的渠道数量
     */
    private int successCount;

    /**
     * 失败的渠道数量
     */
    private int failedCount;

    /**
     * 跳过的渠道数量（如未变更）
     */
    private int skippedCount;

    /**
     * 新增的渠道数量
     */
    private int addedCount;

    /**
     * 更新的渠道数量
     */
    private int updatedCount;

    /**
     * 删除的渠道数量
     */
    private int deletedCount;

    /**
     * 渠道同步结果
     * key: 渠道ID或编码
     * value: 渠道状态 "added", "updated", "deleted", "skipped", "failed"
     */
    private Map<String, String> channelResults = new HashMap<>();

    /**
     * 额外的响应数据
     */
    private Map<String, Object> extraData = new HashMap<>();

    /**
     * 添加单个渠道的处理结果
     *
     * @param channelId 渠道ID或编码
     * @param result    处理结果
     */
    public void addChannelResult(String channelId, String result) {
        channelResults.put(channelId, result);
        switch (result) {
            case "added" -> addedCount++;
            case "updated" -> updatedCount++;
            case "deleted" -> deletedCount++;
            case "skipped" -> skippedCount++;
            case "failed" -> failedCount++;
        }
    }

    /**
     * 添加警告消息
     *
     * @param warning 警告消息
     */
    public void addWarning(String warning) {
        warnings.add(warning);
    }
}
