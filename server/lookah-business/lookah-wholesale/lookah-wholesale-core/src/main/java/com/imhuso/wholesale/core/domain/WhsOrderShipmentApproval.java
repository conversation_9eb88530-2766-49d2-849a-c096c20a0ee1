package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.wholesale.core.enums.ShipmentApprovalStatus;
import com.imhuso.wholesale.core.enums.ShipmentApprovalRecordStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 订单发货审批对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_order_shipment_approval")
public class WhsOrderShipmentApproval extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 审核ID
     */
    @TableId
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 申请人ID
     */
    private Long applicantId;

    /**
     * 申请人姓名
     */
    private String applicantName;

    /**
     * 申请理由
     */
    private String applicationReason;

    /**
     * 审核状态
     * @see ShipmentApprovalStatus
     */
    private Integer approvalStatus;

    /**
     * 操作类型
     * @see com.imhuso.wholesale.core.enums.ShipmentApprovalOperationType
     */
    private String operationType;

    /**
     * 操作序号，同一订单的操作按时间顺序递增
     */
    private Integer operationSequence;

    /**
     * 记录状态
     * @see ShipmentApprovalRecordStatus
     */
    private String recordStatus;

    /**
     * 审核人ID
     */
    private Long approverId;

    /**
     * 审核人姓名
     */
    private String approverName;

    /**
     * 审核时间
     */
    private Date approvalTime;

    /**
     * 审核意见
     */
    private String approvalComment;

    /**
     * 备注
     */
    private String remark;
}
