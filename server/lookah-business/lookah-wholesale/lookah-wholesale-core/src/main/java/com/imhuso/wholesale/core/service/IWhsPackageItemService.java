package com.imhuso.wholesale.core.service;

import java.util.List;
import java.util.Map;

/**
 * 产品包装明细Service接口
 *
 * <AUTHOR>
 */
public interface IWhsPackageItemService {

    /**
     * 保存包装明细（创建新关系或更新已存在的关系）
     *
     * @param parentVariantId 包装变体ID
     * @param childVariantId  内容物变体ID
     * @param quantity        数量
     */
    void savePackageItem(Long parentVariantId, Long childVariantId, Integer quantity);

    /**
     * 根据包装变体ID删除包装明细
     *
     * @param parentVariantId 包装变体ID
     */
    void deleteByParentVariantId(Long parentVariantId);

    /**
     * 查询包装变体包含的内容物变体列表
     *
     * @param parentVariantId 包装变体ID
     * @return 内容物变体ID列表
     */
    List<Long> getChildVariantIds(Long parentVariantId);

    /**
     * 检查源变体是否已经有指定类型的包装变体，并排除指定的父变体ID
     *
     * @param sourceVariantId 源变体ID
     * @param packagingType   包装类型
     * @param excludeParentId 需要排除的父变体ID（用于更新场景）
     * @return 是否存在
     */
    boolean existsPackageForSourceAndType(Long sourceVariantId, Integer packagingType, Long excludeParentId);

    /**
     * 获取包装变体包含的子变体ID及其对应数量
     *
     * @param parentVariantId 包装变体ID
     * @return 子变体ID到数量的映射
     */
    Map<Long, Integer> getChildVariantQuantities(Long parentVariantId);

    /**
     * 检查变体是否被包含在任何包装变体中
     *
     * @param childVariantId 子变体ID
     * @return 是否被包含
     */
    boolean isChildInAnyPackage(Long childVariantId);

    /**
     * 获取所有包装关系
     *
     * @param variantIds 变体ID列表
     * @return 包装关系映射 Map<父变体ID, Map<子变体ID, 数量>>
     */
    Map<Long, Map<Long, Integer>> getAllPackageRelationships(List<Long> variantIds);
}
