package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 批发购物车对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_cart")
public class WhsCart extends BaseEntity {

    /**
     * 购物车ID
     */
    @TableId
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 过期时间
     */
    private Date expireTime;
}
