package com.imhuso.wholesale.core.validate;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 验证收件人姓名长度
 * 用于确保firstName和lastName拼接后的总长度不超过海外仓要求的限制
 * RecipientName = lastName + " " + firstName，总长度不超过{@value #DEFAULT_MAX_LENGTH}个字符
 * 
 * <AUTHOR>
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = RecipientNameValidator.class)
@Documented
public @interface ValidRecipientName {
    
    /** 默认最大长度限制 */
    int DEFAULT_MAX_LENGTH = 35;
    
    String message() default "收件人姓名总长度不能超过" + DEFAULT_MAX_LENGTH + "个字符（姓+空格+名）";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
    
    /**
     * 最大长度限制
     */
    int maxLength() default DEFAULT_MAX_LENGTH;
}