package com.imhuso.wholesale.core.domain.vo.admin;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 装箱单可用性视图对象
 *
 * <AUTHOR>
 */
@Data
public class PackingSlipAvailabilityVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 是否可以下载装箱单
     */
    private Boolean available;

    /**
     * 不可用原因
     */
    private String reason;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 订单状态文本
     */
    private String orderStatusText;

    /**
     * 是否有发货记录
     */
    private Boolean hasShipment;

    /**
     * 发货记录数量
     */
    private Integer shipmentCount;

    /**
     * 构造可用的装箱单状态
     *
     * @param shipmentCount 发货记录数量
     * @return 装箱单可用性对象
     */
    public static PackingSlipAvailabilityVo available(Integer shipmentCount) {
        PackingSlipAvailabilityVo vo = new PackingSlipAvailabilityVo();
        vo.setAvailable(true);
        vo.setHasShipment(true);
        vo.setShipmentCount(shipmentCount);
        vo.setReason("装箱单可以下载");
        return vo;
    }

    /**
     * 构造不可用的装箱单状态
     *
     * @param reason 不可用原因
     * @return 装箱单可用性对象
     */
    public static PackingSlipAvailabilityVo unavailable(String reason) {
        PackingSlipAvailabilityVo vo = new PackingSlipAvailabilityVo();
        vo.setAvailable(false);
        vo.setHasShipment(false);
        vo.setShipmentCount(0);
        vo.setReason(reason);
        return vo;
    }

    /**
     * 构造订单不存在的状态
     *
     * @return 装箱单可用性对象
     */
    public static PackingSlipAvailabilityVo orderNotFound() {
        return unavailable("订单不存在");
    }

    /**
     * 构造无发货记录的状态
     *
     * @return 装箱单可用性对象
     */
    public static PackingSlipAvailabilityVo noShipment() {
        return unavailable("订单没有发货记录，无法生成装箱单");
    }
}
