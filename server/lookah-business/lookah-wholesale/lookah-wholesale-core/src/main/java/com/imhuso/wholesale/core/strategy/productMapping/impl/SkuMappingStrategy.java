package com.imhuso.wholesale.core.strategy.productMapping.impl;

import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.vo.warehouse.WarehouseContextVo;
import com.imhuso.wholesale.core.service.IWhsProductVariantService;
import com.imhuso.wholesale.core.strategy.productMapping.IProductMappingStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 基于SKU的产品映射策略
 * 默认使用SKU作为外部系统的标识符
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SkuMappingStrategy implements IProductMappingStrategy {

    private final IWhsProductVariantService variantService;

    /**
     * 策略类型
     */
    private static final String STRATEGY_TYPE = "SKU";

    @Override
    public String getStrategyType() {
        return STRATEGY_TYPE;
    }

    @Override
    public String getStrategyName() {
        return "SKU映射";
    }

    @Override
    public boolean isApplicable(WarehouseContextVo context) {
        // 默认策略，适用于所有仓库
        return true;
    }

    @Override
    public Map<Long, String> getExternalCodes(WarehouseContextVo context, List<WhsProductVariant> variants) {
        if (variants == null || variants.isEmpty()) {
            return new HashMap<>();
        }

        // 批量处理，将变体ID映射到SKU
        return variants.stream().collect(Collectors.toMap(WhsProductVariant::getId, WhsProductVariant::getSkuCode, (existing, replacement) -> existing // 如果有重复的ID，保留第一个值
        ));
    }

    @Override
    public Map<String, Long> getVariantIdsByExternalCodes(WarehouseContextVo context, List<String> externalCodes) {
        if (externalCodes == null || externalCodes.isEmpty()) {
            return new HashMap<>();
        }

        // 将外部编码(SKU)映射到变体ID
        return variantService.getVariantIdsBySkuCodes(externalCodes);
    }
}
