package com.imhuso.wholesale.core.domain.bo.front;

import com.imhuso.wholesale.core.domain.WhsOrderItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单项创建业务对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@AutoMapper(target = WhsOrderItem.class)
public class OrderItemCreateBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 变体ID(SKU)
     */
    private Long variantId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品价格
     */
    private BigDecimal productPrice;

    /**
     * 采购单价
     */
    private BigDecimal purchasePrice;

    /**
     * 销售单价（手动填入）
     */
    private BigDecimal salesPrice;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 包装类型（0单品 1展示盒 2箱装）
     */
    private Integer packagingType;

    /**
     * 包装数量(PCS)
     */
    private Integer packagingQuantity;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 规格快照
     */
    private String specsSnapshot;
}
