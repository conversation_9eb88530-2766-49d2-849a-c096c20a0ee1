package com.imhuso.wholesale.core.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.mybatis.core.mapper.BaseMapperPlus;
import com.imhuso.wholesale.core.domain.WhsStock;
import com.imhuso.wholesale.core.domain.bo.admin.WhsStockBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsReplenishmentBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsStockSummaryBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsStockVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsReplenishmentVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsStockSummaryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 库存Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface WhsStockMapper extends BaseMapperPlus<WhsStock, WhsStockVo> {
    /**
     * 自定义分页查询库存列表
     * 使用JOIN方式关联仓库、产品和变体信息
     *
     * @param page         分页参数
     * @param queryWrapper 查询条件
     * @param bo           查询条件
     * @return 分页结果
     */
    Page<WhsStockVo> selectStockVoPage(Page<WhsStock> page,
            @Param(Constants.WRAPPER) Wrapper<WhsStock> queryWrapper, @Param("bo") WhsStockBo bo);

    /**
     * 查询补货单列表（不分页）
     *
     * @param bo 查询条件
     * @return 补货单列表
     */
    List<WhsReplenishmentVo> selectReplenishmentList(@Param("bo") WhsReplenishmentBo bo);

    /**
     * 查询库存汇总列表（不分页）
     * 按产品分组统计库存信息
     *
     * @param queryWrapper 查询条件
     * @param bo           查询条件
     * @return 库存汇总列表
     */
    List<WhsStockSummaryVo> selectStockSummaryVoList(
            @Param(Constants.WRAPPER) Wrapper<WhsStock> queryWrapper, @Param("bo") WhsStockSummaryBo bo);

    /**
     * 根据产品ID查询变体库存详情
     *
     * @param productId 产品ID
     * @param warehouseId 仓库ID（可选，用于过滤特定仓库）
     * @return 变体库存详情列表（原始Map数据）
     */
    List<Map<String, Object>> selectVariantStockDetailsByProductId(@Param("productId") Long productId, @Param("warehouseId") Long warehouseId);

    /**
     * 查询变体在各仓库的在途库存
     * 通过入库单和入库单项目计算
     *
     * @param variantId 变体ID
     * @return 仓库ID到在途库存数量的映射
     */
    List<Map<String, Object>> selectInTransitStockByVariantId(@Param("variantId") Long variantId);
}
