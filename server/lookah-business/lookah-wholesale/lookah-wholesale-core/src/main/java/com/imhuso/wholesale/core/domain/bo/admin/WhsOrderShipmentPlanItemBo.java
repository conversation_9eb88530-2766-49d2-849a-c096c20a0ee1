package com.imhuso.wholesale.core.domain.bo.admin;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

/**
 * 订单发货方案项业务对象
 *
 * <AUTHOR>
 */
@Data
public class WhsOrderShipmentPlanItemBo {

    /**
     * 发货方案项ID
     */
    @NotNull(message = "发货方案项ID不能为空")
    private Long planItemId;

    /**
     * 发货数量
     */
    @NotNull(message = "发货数量不能为空")
    @Positive(message = "发货数量必须大于0")
    private Integer quantity;
}
