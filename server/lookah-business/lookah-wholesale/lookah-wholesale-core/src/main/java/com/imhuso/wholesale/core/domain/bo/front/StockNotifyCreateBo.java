package com.imhuso.wholesale.core.domain.bo.front;

import com.imhuso.wholesale.core.domain.WhsStockNotify;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 批发到货通知创建业务对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsStockNotify.class)
public class StockNotifyCreateBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 变体ID
     */
    @NotNull(message = "wholesale.variant.id.not.null")
    private Long variantId;

    /**
     * 通知邮箱
     */
    @NotBlank(message = "wholesale.notify.email.not.blank")
    @Email(message = "wholesale.notify.email.not.valid")
    private String email;

    /**
     * WhatsApp号码
     */
    private String whatsapp;
}
