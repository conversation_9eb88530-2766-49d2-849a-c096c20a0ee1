package com.imhuso.wholesale.core.job;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import com.aizuda.snailjob.common.log.SnailJobLog;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.service.IWhsStockCacheService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 库存缓存修复任务
 * <p>
 * 定期检查Redis缓存中的库存数据与数据库的一致性，并修复不一致的数据
 * 配置为每10分钟执行一次
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@SuppressWarnings("unused") // SnailJob框架调用，IDE检测不到使用
public class StockCacheRepairJob {

    private final IWhsStockCacheService whsStockCacheService;

    /**
     * 执行库存缓存检查和修复任务
     *
     * @param jobArgs 任务参数
     * @return 执行结果
     */
    @JobExecutor(name = "repairStockCache")
    public ExecuteResult jobExecute(JobArgs jobArgs) {
        SnailJobLog.LOCAL.info("开始执行库存缓存数据检查和修复任务");
        try {
            whsStockCacheService.checkAndRepairStockCache();
            String message = "库存缓存数据检查和修复任务执行完成";
            SnailJobLog.LOCAL.info(message);
            return ExecuteResult.success(message);
        } catch (Exception e) {
            String errorMessage = "库存缓存数据检查和修复任务执行失败: " + StringUtils.defaultIfEmpty(e.getMessage(), e.getClass().getName());
            SnailJobLog.LOCAL.error(errorMessage, e);
            return ExecuteResult.failure(errorMessage);
        }
    }
}
