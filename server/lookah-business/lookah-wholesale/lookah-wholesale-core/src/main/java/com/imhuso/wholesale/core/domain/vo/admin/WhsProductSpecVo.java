package com.imhuso.wholesale.core.domain.vo.admin;

import com.imhuso.wholesale.core.domain.WhsProductAttribute;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 产品规格视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AutoMapper(target = WhsProductAttribute.class)
public class WhsProductSpecVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 规格键
     */
    private String key;

    /**
     * 规格名称
     */
    private String name;

    /**
     * 规格值列表
     */
    private List<WhsProductSpecValueVo> values;
}
