package com.imhuso.wholesale.core.domain.vo.admin;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 补货单视图对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ExcelIgnoreUnannotated
public class WhsReplenishmentVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    @ExcelProperty("仓库名称")
    private String warehouseName;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * SKU编码
     */
    @ExcelProperty("SKU编码")
    private String skuCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 总库存
     */
    @ExcelProperty("总库存")
    private Integer totalStock;

    /**
     * 可用库存
     */
    @ExcelProperty("可用库存")
    private Integer availableStock;

    /**
     * 锁定库存
     */
    @ExcelProperty("锁定库存")
    private Integer lockedStock;

    /**
     * 在途库存
     */
    @ExcelProperty("在途库存")
    private Integer inTransitStock;

    /**
     * 变体级别告警库存阈值
     */
    private Integer alertStock;

    /**
     * 仓库级别告警库存阈值
     */
    private Integer warehouseAlertStock;

    /**
     * 推荐补货数量
     * 计算逻辑：告警库存 - 可用库存，如果小于等于0则为0
     * 注意：此字段不在Excel中导出
     */
    private Integer recommendedQuantity;

    /**
     * 是否需要补货
     * 当可用库存小于告警库存时为true
     */
    private Boolean needReplenishment;
}
