package com.imhuso.wholesale.core.domain.vo.warehouse;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 库存同步汇总结果
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockSyncSummary {

    /**
     * 海外仓同步成功数量
     */
    private Integer warehouseSuccessCount;

    /**
     * 海外仓同步失败数量
     */
    private Integer warehouseFailureCount;

    /**
     * ERP库存同步成功数量
     */
    private Integer erpSuccessCount;

    /**
     * ERP库存同步失败数量
     */
    private Integer erpFailureCount;

    /**
     * 总同步耗时（毫秒）
     */
    private Long totalDuration;

    /**
     * 同步开始时间
     */
    private Long startTime;

    /**
     * 同步结束时间
     */
    private Long endTime;

    /**
     * 是否全部成功
     */
    public boolean isAllSuccess() {
        return (warehouseFailureCount == null || warehouseFailureCount == 0) &&
               (erpFailureCount == null || erpFailureCount == 0);
    }

    /**
     * 获取总成功数量
     */
    public int getTotalSuccessCount() {
        return (warehouseSuccessCount == null ? 0 : warehouseSuccessCount) +
               (erpSuccessCount == null ? 0 : erpSuccessCount);
    }

    /**
     * 获取总失败数量
     */
    public int getTotalFailureCount() {
        return (warehouseFailureCount == null ? 0 : warehouseFailureCount) +
               (erpFailureCount == null ? 0 : erpFailureCount);
    }

    /**
     * 创建成功的汇总结果
     */
    public static StockSyncSummary success(int warehouseCount, int erpCount, long duration) {
        return StockSyncSummary.builder()
                .warehouseSuccessCount(warehouseCount)
                .warehouseFailureCount(0)
                .erpSuccessCount(erpCount)
                .erpFailureCount(0)
                .totalDuration(duration)
                .build();
    }

    /**
     * 创建部分成功的汇总结果
     */
    public static StockSyncSummary partial(int warehouseSuccess, int warehouseFailure,
                                          int erpSuccess, int erpFailure, long duration) {
        return StockSyncSummary.builder()
                .warehouseSuccessCount(warehouseSuccess)
                .warehouseFailureCount(warehouseFailure)
                .erpSuccessCount(erpSuccess)
                .erpFailureCount(erpFailure)
                .totalDuration(duration)
                .build();
    }
}
