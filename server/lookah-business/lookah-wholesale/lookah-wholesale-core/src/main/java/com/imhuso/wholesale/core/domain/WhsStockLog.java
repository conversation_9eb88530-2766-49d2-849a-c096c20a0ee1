package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 库存操作日志实体类
 * <p>
 * 记录所有库存变动的操作，包括库存初始化、更新、锁定、释放、扣减和退回等。
 * 通过记录操作前后的库存状态，方便追溯库存变动历史和审计。
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_stock_log")
public class WhsStockLog extends BaseEntity {

    /**
     * 日志ID
     */
    @TableId
    private Long id;

    /**
     * 库存ID，关联到whs_stock表
     */
    private Long stockId;

    /**
     * 仓库ID，关联到whs_warehouse表
     */
    private Long warehouseId;

    /**
     * 变体ID，关联到whs_product_variant表
     */
    private Long variantId;

    /**
     * 操作类型
     * <ul>
     * <li>1: CREATE - 初始化库存</li>
     * <li>2: ADJUST - 手动调整库存</li>
     * <li>3: LOCK - 锁定库存（减少可用库存，增加锁定库存）</li>
     * <li>4: RELEASE - 释放库存（增加可用库存，减少锁定库存）</li>
     * <li>5: DEDUCT - 扣减库存</li>
     * <li>6: SYNC - 同步库存</li>
     * <li>7: OUTBOUND - 发货出库（减少可用库存）</li>
     * </ul>
     * @see com.imhuso.wholesale.enums.StockOperationType
     */
    private Integer operationType;

    /**
     * 操作数量（正数表示增加，负数表示减少）
     */
    private Integer quantity;

    /**
     * 操作前库存值
     * 根据操作类型不同，记录对应的库存类型值：
     * - LOCK/RELEASE: 记录的是锁定库存的变化
     * - 其他操作类型: 记录的是可用库存的变化
     * 具体记录的是哪种库存类型，可通过备注中的"库存类型"说明来确定
     */
    private Integer beforeStock;

    /**
     * 操作后库存值
     * 根据操作类型不同，记录对应的库存类型值：
     * - LOCK/RELEASE: 记录的是锁定库存的变化
     * - 其他操作类型: 记录的是可用库存的变化
     * 具体记录的是哪种库存类型，可通过备注中的"库存类型"说明来确定
     */
    private Integer afterStock;

    /**
     * 关联订单ID（如果此操作与订单相关）
     */
    private Long orderId;

    /**
     * 关联订单项ID（如果此操作与特定订单项相关）
     */
    private Long orderItemId;

    /**
     * 备注信息
     */
    private String remark;
}
