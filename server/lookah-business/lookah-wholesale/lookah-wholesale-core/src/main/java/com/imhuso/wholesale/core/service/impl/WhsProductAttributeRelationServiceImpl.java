package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.wholesale.core.domain.WhsProductAttribute;
import com.imhuso.wholesale.core.domain.WhsProductAttributeRelation;
import com.imhuso.wholesale.core.domain.WhsProductVariantAttribute;
import com.imhuso.wholesale.core.mapper.WhsProductAttributeMapper;
import com.imhuso.wholesale.core.mapper.WhsProductAttributeRelationMapper;
import com.imhuso.wholesale.core.mapper.WhsProductVariantAttributeMapper;
import com.imhuso.wholesale.core.service.IWhsProductAttributeRelationService;
import com.imhuso.wholesale.core.utils.StructDiffUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品属性关联Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsProductAttributeRelationServiceImpl implements IWhsProductAttributeRelationService {

    private final WhsProductAttributeRelationMapper baseMapper;
    private final WhsProductVariantAttributeMapper variantAttributeMapper;
    private final WhsProductAttributeMapper attributeMapper;

    /**
     * 删除产品的所有属性关联
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeRelationsByProductId(Long productId) {
        if (productId == null) {
            return;
        }

        LambdaQueryWrapper<WhsProductAttributeRelation> wrapper = Wrappers
            .lambdaQuery(WhsProductAttributeRelation.class)
            .eq(WhsProductAttributeRelation::getProductId, productId);

        baseMapper.delete(wrapper);
    }

    /**
     * 批量获取产品的属性关联列表
     */
    @Override
    public List<WhsProductAttributeRelation> getRelationsByProductIds(List<Long> productIds) {
        if (CollUtil.isEmpty(productIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<WhsProductAttributeRelation> wrapper = Wrappers.lambdaQuery();
        wrapper.in(WhsProductAttributeRelation::getProductId, productIds)
            .orderByAsc(WhsProductAttributeRelation::getSortOrder);

        return baseMapper.selectList(wrapper);
    }

    /**
     * 批量插入产品属性关联
     */
    private void batchInsert(List<WhsProductAttributeRelation> relations) {
        if (CollUtil.isEmpty(relations)) {
            return;
        }

        // 确保每个关联都设置了正确的排序顺序
        relations.forEach(this::prepareEntityBeforeSave);

        // 逐个插入，后续可考虑批量插入优化
        relations.forEach(baseMapper::insert);
    }

    /**
     * 批量删除产品属性关联
     */
    private void batchDelete(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }

        baseMapper.deleteByIds(ids);
    }

    /**
     * 检查属性是否被变体使用
     */
    private void checkAttributeUsageByVariants(List<Long> attributeIds) {
        if (CollUtil.isEmpty(attributeIds)) {
            return;
        }

        // 查询各属性被变体使用的次数
        Map<Long, Integer> usageCountMap = new HashMap<>();
        for (Long attributeId : attributeIds) {
            LambdaQueryWrapper<WhsProductVariantAttribute> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(WhsProductVariantAttribute::getAttributeId, attributeId);

            int count = Math.toIntExact(variantAttributeMapper.selectCount(wrapper));
            if (count > 0) {
                usageCountMap.put(attributeId, count);
            }
        }

        // 如果有属性被使用，获取属性详情并构建错误信息
        if (!usageCountMap.isEmpty()) {
            StringBuilder errorMsg = new StringBuilder();
            boolean isFirst = true;

            for (Map.Entry<Long, Integer> entry : usageCountMap.entrySet()) {
                Long attributeId = entry.getKey();
                Integer count = entry.getValue();

                // 获取属性详情
                WhsProductAttribute attribute = attributeMapper.selectById(attributeId);
                String attributeName = attribute != null ? attribute.getAttrDisplay() : "未知属性(" + attributeId + ")";

                if (!isFirst) {
                    errorMsg.append("；");
                }
                errorMsg.append("属性【").append(attributeName).append("】已被").append(count).append("个变体使用，禁止删除");
                isFirst = false;
            }

            throw new ServiceException(errorMsg.toString());
        }
    }

    /**
     * 处理产品属性关联（增量更新）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processProductAttributes(Long productId, List<Long> attributeIds) {
        // 参数校验
        if (productId == null) {
            log.warn("处理产品属性关联时产品ID为空");
            return;
        }

        // 1. 查询现有属性关联
        List<WhsProductAttributeRelation> existingRelations = getRelationsByProductIds(
            Collections.singletonList(productId));

        log.info("产品{}的现有属性: {}", productId,
            existingRelations.isEmpty() ? "无"
                : existingRelations.stream()
                .map(r -> String.format("ID=%d,AttributeID=%d", r.getId(), r.getAttributeId()))
                .collect(Collectors.joining(", ")));

        // 如果属性列表为空，并且现有属性也为空，则不需要任何操作
        if (CollUtil.isEmpty(attributeIds) && existingRelations.isEmpty()) {
            log.info("产品{}没有现有属性且新属性列表为空，无需处理", productId);
            return;
        }

        // 如果属性列表为空，则需要删除所有现有属性，先检查是否有属性被变体使用
        if (CollUtil.isEmpty(attributeIds)) {
            log.info("产品{}的新属性列表为空，将检查现有属性是否可以全部删除", productId);

            // 获取需要删除的所有属性ID
            List<Long> attributesToDelete = existingRelations.stream()
                .map(WhsProductAttributeRelation::getAttributeId)
                .collect(Collectors.toList());

            if (!attributesToDelete.isEmpty()) {
                log.debug("检查产品{}的待删除属性是否被变体使用: {}", productId,
                    attributesToDelete.stream().map(String::valueOf).collect(Collectors.joining(", ")));

                // 检查属性是否被变体使用
                checkAttributeUsageByVariants(attributesToDelete);

                // 如果检查通过（没有抛出异常），则可以安全删除
                log.info("产品{}的所有属性检查通过，执行删除操作", productId);
                removeRelationsByProductId(productId);
            }
            return;
        }

        // 2. 转换新属性列表为关联实体
        List<WhsProductAttributeRelation> newRelations = attributeIds.stream()
            .map(attributeId -> {
                WhsProductAttributeRelation relation = new WhsProductAttributeRelation();
                relation.setProductId(productId);
                relation.setAttributeId(attributeId);
                relation.setSortOrder(0); // 默认排序为0
                return relation;
            })
            .collect(Collectors.toList());

        log.info("产品{}的新属性列表: {}", productId,
            newRelations.stream()
                .map(r -> String.format("AttributeID=%d", r.getAttributeId()))
                .collect(Collectors.joining(", ")));

        // 3. 使用StructDiffUtils计算差异
        StructDiffUtils<WhsProductAttributeRelation, Long> diff = StructDiffUtils.compute(
            existingRelations,
            newRelations,
            WhsProductAttributeRelation::getAttributeId,
            (oldRelation, newRelation) -> true // 属性ID相同就认为是同一个关系，不需要更新
        );

        // 记录差异详情
        if (diff.hasChanges()) {
            log.info("产品{}的属性变更详情:", productId);
            if (!diff.getToAdd().isEmpty()) {
                log.info("- 待新增属性: {}",
                    diff.getToAdd().stream()
                        .map(r -> String.format("AttributeID=%d", r.getAttributeId()))
                        .collect(Collectors.joining(", ")));
            }
            if (!diff.getToDelete().isEmpty()) {
                log.info("- 待删除属性: {}",
                    diff.getToDelete().stream()
                        .map(r -> String.format("ID=%d,AttributeID=%d", r.getId(), r.getAttributeId()))
                        .collect(Collectors.joining(", ")));
            }
        } else {
            log.info("产品{}的属性没有变化，跳过更新", productId);
            return;
        }

        // 4. 检查要删除的属性是否被变体使用
        if (!diff.getToDelete().isEmpty()) {
            List<Long> attributesToDelete = diff.getToDelete().stream()
                .map(WhsProductAttributeRelation::getAttributeId)
                .collect(Collectors.toList());

            log.debug("检查产品{}的待删除属性是否被变体使用: {}", productId,
                attributesToDelete.stream().map(String::valueOf).collect(Collectors.joining(", ")));

            checkAttributeUsageByVariants(attributesToDelete);
        }

        // 5. 处理需要删除的关联
        if (!diff.getToDelete().isEmpty()) {
            List<Long> relationIds = diff.getToDelete().stream()
                .map(WhsProductAttributeRelation::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            if (!relationIds.isEmpty()) {
                log.info("删除产品{}的属性关联: {}", productId,
                    relationIds.stream().map(String::valueOf).collect(Collectors.joining(", ")));
                batchDelete(relationIds);
            }
        }

        // 6. 处理需要新增的关联
        if (!diff.getToAdd().isEmpty()) {
            log.info("新增产品{}的属性关联: {}", productId,
                diff.getToAdd().stream()
                    .map(r -> String.format("AttributeID=%d", r.getAttributeId()))
                    .collect(Collectors.joining(", ")));
            batchInsert(diff.getToAdd());
        }

        log.info("产品{}的属性关联处理完成", productId);
    }

    /**
     * 检查属性是否被产品使用
     *
     * @param attributeId 属性ID
     * @return 如果被使用，返回true；否则返回false
     */
    @Override
    public boolean isAttributeUsedByProducts(Long attributeId) {
        if (attributeId == null) {
            return false;
        }

        LambdaQueryWrapper<WhsProductAttributeRelation> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(WhsProductAttributeRelation::getAttributeId, attributeId);

        return baseMapper.exists(wrapper);
    }

    /**
     * 为实体设置必要的字段
     */
    private void prepareEntityBeforeSave(WhsProductAttributeRelation entity) {
        // 如果排序为空，则设置为0
        if (entity.getSortOrder() == null) {
            entity.setSortOrder(0);
        }
    }
}
