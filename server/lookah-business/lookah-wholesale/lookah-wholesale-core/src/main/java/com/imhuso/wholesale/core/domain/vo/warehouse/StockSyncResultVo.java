package com.imhuso.wholesale.core.domain.vo.warehouse;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 库存同步结果视图对象
 *
 * <AUTHOR>
 */
@Data
public class StockSyncResultVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 同步是否成功
     */
    private boolean success = false;

    /**
     * 错误消息（如果同步失败）
     */
    private String errorMessage;

    /**
     * 警告消息（如果有）
     */
    private List<String> warnings;

    /**
     * 处理的商品数量
     */
    private int processedCount;

    /**
     * 成功更新的商品数量
     */
    private int successCount;

    /**
     * 失败的商品数量
     */
    private int failedCount;

    /**
     * 跳过的商品数量（如未变更）
     */
    private int skippedCount;

    /**
     * 无法识别的外部编码列表
     */
    private List<String> unrecognizedCodes;

    /**
     * 单个商品的处理结果
     * key: 外部编码(可能是SKU、UPC或其他标识符)
     * value: "success", "failed", "skipped" 等
     */
    private Map<String, String> itemResults = new HashMap<>();

    /**
     * 单个商品的库存更新量
     * key: 外部编码(可能是SKU、UPC或其他标识符)
     * value: 更新后的库存数量
     */
    private Map<String, Integer> stockUpdates = new HashMap<>();

    /**
     * 额外的响应数据
     */
    private Map<String, Object> extraData = new HashMap<>();

    /**
     * 添加单个商品的处理结果
     *
     * @param code   商品外部编码(可能是SKU、UPC或其他标识符)
     * @param result 处理结果
     * @param stock  更新后的库存数量
     */
    public void addItemResult(String code, String result, Integer stock) {
        itemResults.put(code, result);
        if (stock != null) {
            stockUpdates.put(code, stock);
        }
    }
}
