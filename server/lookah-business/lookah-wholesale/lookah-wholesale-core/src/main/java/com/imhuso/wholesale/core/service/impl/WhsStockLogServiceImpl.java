package com.imhuso.wholesale.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.satoken.utils.LoginHelper;
import com.imhuso.wholesale.core.satoken.WholesaleLoginHelper;
import com.imhuso.wholesale.core.domain.WhsStock;
import com.imhuso.wholesale.core.domain.WhsStockLog;
import com.imhuso.wholesale.core.domain.bo.admin.WhsStockLogBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsStockLogVo;
import com.imhuso.wholesale.core.enums.StockOperationType;
import com.imhuso.wholesale.core.mapper.WhsStockLogMapper;
import com.imhuso.wholesale.core.service.IWhsOrderNoFormatService;
import com.imhuso.wholesale.core.service.IWhsStockLogService;
import com.imhuso.wholesale.core.utils.WhsEnumTranslationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 库存操作日志服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsStockLogServiceImpl implements IWhsStockLogService {

    private final WhsStockLogMapper stockLogMapper;
    private final IWhsOrderNoFormatService orderNoFormatService;

    /**
     * 创建库存操作日志
     * 统一处理所有类型的库存操作日志记录
     * 根据操作类型记录相应库存类型的变化：
     * - LOCK(3): 记录锁定库存的变化（锁定库存增加）
     * - RELEASE(4): 记录锁定库存的变化（锁定库存减少）
     * - OUTBOUND(7): 记录可用库存的变化（可用库存减少）
     * - 其他类型: 记录可用库存的变化
     * 实现原则：
     * 根据"操作谁，谁就是before/after"的原则，将操作主要影响的库存类型的变化记录到
     * beforeStock和afterStock字段中，使日志记录更加准确、有意义。
     * 日志备注中会包含"库存类型"信息，清晰标识记录的是哪种库存类型的变化。
     */
    @Override
    public void createStockLog(WhsStock stock, StockOperationType operationType, int quantity, int beforeStock,
                               int afterStock, Long orderId, Long orderItemId, String remark) {
        WhsStockLog logEntry = new WhsStockLog();
        logEntry.setStockId(stock.getId());
        logEntry.setWarehouseId(stock.getWarehouseId());
        logEntry.setVariantId(stock.getVariantId());
        logEntry.setOperationType(operationType.getValue());
        logEntry.setQuantity(quantity);

        // 记录根据操作类型对应的库存变化
        logEntry.setBeforeStock(beforeStock);
        logEntry.setAfterStock(afterStock);

        logEntry.setOrderId(orderId);
        logEntry.setOrderItemId(orderItemId);
        logEntry.setRemark(remark);

        // 智能获取用户ID：优先获取系统管理员ID（后台操作），然后获取批发用户ID（前台操作）
        Long userId = getCurrentOperatorUserId();
        logEntry.setCreateBy(userId);

        stockLogMapper.insert(logEntry);

        // 确定日志记录的是哪种库存类型
        String stockTypeDesc;
        if (operationType == StockOperationType.LOCK || operationType == StockOperationType.RELEASE) {
            stockTypeDesc = "锁定库存";
        } else if (operationType == StockOperationType.OUTBOUND || operationType == StockOperationType.DEDUCT) {
            stockTypeDesc = "可用库存";
        } else {
            stockTypeDesc = "可用库存";
        }

        log.info("库存操作记录: 库存ID={}, 操作类型={}({}), 数量={}, 前{}={}, 后{}={}, 订单ID={}, 订单项ID={}, 备注={}",
            stock.getId(), operationType.name(), WhsEnumTranslationUtils.translate(StockOperationType.class, operationType.getValue()),
            quantity, stockTypeDesc, beforeStock, stockTypeDesc, afterStock,
            orderId != null ? orderId : "-",
            orderItemId != null ? orderItemId : "-",
            remark);
    }

    @Override
    public List<Long> findStockIdsByOperationLog(Long variantId, Long orderId, Integer operationType) {
        if (variantId == null || orderId == null || operationType == null) {
            return Collections.emptyList();
        }

        // 构建查询条件
        LambdaQueryWrapper<WhsStockLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhsStockLog::getVariantId, variantId).eq(WhsStockLog::getOrderId, orderId)
            .eq(WhsStockLog::getOperationType, operationType).select(WhsStockLog::getStockId);

        // 查询库存日志记录并提取库存ID
        List<WhsStockLog> logs = stockLogMapper.selectList(queryWrapper);
        if (logs.isEmpty()) {
            return Collections.emptyList();
        }

        // 返回不重复的库存ID列表
        return logs.stream().map(WhsStockLog::getStockId).distinct().collect(Collectors.toList());
    }

    @Override
    public List<Long> findStockIdsByOperationLogWithItem(Long variantId, Long orderId, Long orderItemId,
                                                         Integer operationType) {
        if (variantId == null || orderId == null || orderItemId == null || operationType == null) {
            return Collections.emptyList();
        }

        // 构建更精确查询条件，包含订单项ID
        LambdaQueryWrapper<WhsStockLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhsStockLog::getVariantId, variantId).eq(WhsStockLog::getOrderId, orderId)
            .eq(WhsStockLog::getOrderItemId, orderItemId).eq(WhsStockLog::getOperationType, operationType)
            .select(WhsStockLog::getStockId);

        // 查询库存日志记录并提取库存ID
        List<WhsStockLog> logs = stockLogMapper.selectList(queryWrapper);
        if (logs.isEmpty()) {
            log.debug("未找到订单项对应的库存日志: orderItemId={}, variantId={}, operationType={}", orderItemId, variantId,
                operationType);
            return Collections.emptyList();
        }

        // 返回不重复的库存ID列表
        return logs.stream().map(WhsStockLog::getStockId).distinct().collect(Collectors.toList());
    }

    @Override
    public TableDataInfo<WhsStockLogVo> getStockLogsPageByStockId(Long stockId, PageQuery pageQuery) {
        // 构建查询条件
        WhsStockLogBo bo = new WhsStockLogBo();
        bo.setStockId(stockId);
        LambdaQueryWrapper<WhsStockLog> lqw = buildQueryWrapper(bo);
        // 执行自定义JOIN查询，框架自动处理分页和排序
        Page<WhsStockLogVo> page = stockLogMapper.selectStockLogVoPage(pageQuery.build(), lqw);

        /*
         * // 处理orderNo字段 - 根据orderId格式化生成
         * if (ObjectUtil.isEmpty(page)) {
         * return TableDataInfo.build(Collections.emptyList());
         * }
         */

        for (WhsStockLogVo vo : page.getRecords()) {
            if (vo.getOrderId() != null) {
                vo.setOrderNo(orderNoFormatService.getOrderNoByOrderId(vo.getOrderId()));
            }
        }

        // 返回分页结果
        return TableDataInfo.build(page);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<WhsStockLog> buildQueryWrapper(WhsStockLogBo bo) {
        LambdaQueryWrapper<WhsStockLog> lqw = Wrappers.lambdaQuery();

        // 根据库存ID查询
        lqw.eq(bo.getStockId() != null, WhsStockLog::getStockId, bo.getStockId());

        // 根据仓库ID查询
        lqw.eq(bo.getWarehouseId() != null, WhsStockLog::getWarehouseId, bo.getWarehouseId());

        // 根据变体ID查询
        lqw.eq(bo.getVariantId() != null, WhsStockLog::getVariantId, bo.getVariantId());

        // 根据操作类型查询
        lqw.eq(bo.getOperationType() != null, WhsStockLog::getOperationType, bo.getOperationType());

        // 根据订单ID查询
        lqw.eq(bo.getOrderId() != null, WhsStockLog::getOrderId, bo.getOrderId());

        // 根据时间范围查询
        lqw.between(bo.getBeginTime() != null && bo.getEndTime() != null, WhsStockLog::getCreateTime,
            bo.getBeginTime(), bo.getEndTime());

        // 处理关联表的查询条件（通过params传递给XML）
        if (StringUtils.isNotBlank(bo.getSkuCode()) || StringUtils.isNotBlank(bo.getProductName())
            || StringUtils.isNotBlank(bo.getOrderNo())) {

            // 使用params传递关联表需要的查询参数
            lqw.getEntity().setParams(bo.getParams());

            // 确保params中有关联字段的查询条件
            if (StringUtils.isNotBlank(bo.getSkuCode())) {
                bo.getParams().put("skuCode", bo.getSkuCode());
            }
            if (StringUtils.isNotBlank(bo.getProductName())) {
                bo.getParams().put("productName", bo.getProductName());
            }
            if (StringUtils.isNotBlank(bo.getOrderNo())) {
                bo.getParams().put("orderNo", bo.getOrderNo());
            }
        }

        return lqw;
    }

    /**
     * 智能获取当前操作用户ID
     * 优先尝试获取系统管理员用户ID（后台管理操作），如果失败则尝试获取批发用户ID（前台操作）
     *
     * @return 当前操作用户ID，如果都获取失败则返回0L（系统用户）
     */
    private Long getCurrentOperatorUserId() {
        // 首先尝试获取系统管理员用户ID（后台管理操作）
        try {
            if (LoginHelper.isLogin()) {
                Long adminUserId = LoginHelper.getUserId();
                if (adminUserId != null && adminUserId > 0) {
                    log.debug("库存日志记录：成功获取到系统管理员用户ID: {}", adminUserId);
                    return adminUserId;
                }
            }
        } catch (cn.dev33.satoken.exception.SaTokenContextException e) {
            log.debug("异步环境中无法获取系统管理员用户ID: {}", e.getMessage());
        } catch (Exception e) {
            log.debug("获取系统管理员用户ID失败: {}", e.getMessage());
        }

        // 如果没有管理员用户，则尝试获取批发用户ID（前台操作）
        try {
            if (WholesaleLoginHelper.isLogin()) {
                Long wholesaleUserId = WholesaleLoginHelper.getUserId();
                if (wholesaleUserId != null && wholesaleUserId > 0) {
                    log.debug("库存日志记录：成功获取到批发用户ID: {}", wholesaleUserId);
                    return wholesaleUserId;
                }
            }
        } catch (cn.dev33.satoken.exception.SaTokenContextException e) {
            log.debug("异步环境中无法获取批发用户ID: {}", e.getMessage());
        } catch (Exception e) {
            log.debug("获取批发用户ID失败: {}", e.getMessage());
        }

        log.debug("无法获取任何有效的用户ID，库存日志将使用系统用户ID(0)");
        return 0L;
    }
}
