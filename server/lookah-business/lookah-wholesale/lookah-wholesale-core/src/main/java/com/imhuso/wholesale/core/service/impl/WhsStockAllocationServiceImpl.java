package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.WhsOrderItem;
import com.imhuso.wholesale.core.domain.WhsShipmentPlanItem;
import com.imhuso.wholesale.core.domain.WhsStock;
import com.imhuso.wholesale.core.domain.WhsStockAllocation;
import com.imhuso.wholesale.core.domain.bo.admin.WhsStockAllocationBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsStockAllocationVo;
import com.imhuso.wholesale.core.enums.StockAllocationStatus;
import com.imhuso.wholesale.core.enums.StockOperationType;
import com.imhuso.wholesale.core.mapper.WhsShipmentPlanItemMapper;
import com.imhuso.wholesale.core.mapper.WhsStockAllocationMapper;
import com.imhuso.wholesale.core.mapper.WhsStockMapper;
import com.imhuso.wholesale.core.service.IWhsStockAllocationService;
import com.imhuso.wholesale.core.service.IWhsStockOperationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 库存分配服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsStockAllocationServiceImpl implements IWhsStockAllocationService {

    private final WhsStockAllocationMapper baseMapper;
    private final WhsStockMapper stockMapper;
    private final IWhsStockOperationService stockOperationService;
    private final WhsShipmentPlanItemMapper shipmentPlanItemMapper;

    /**
     * 查询库存分配分页列表
     */
    @Override
    public TableDataInfo<WhsStockAllocationVo> queryPageList(WhsStockAllocationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WhsStockAllocation> lqw = buildQueryWrapper(bo);
        Page<WhsStockAllocationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<WhsStockAllocation> buildQueryWrapper(WhsStockAllocationBo bo) {
        LambdaQueryWrapper<WhsStockAllocation> lqw = new LambdaQueryWrapper<>();
        lqw.eq(bo.getWarehouseId() != null, WhsStockAllocation::getWarehouseId, bo.getWarehouseId());
        lqw.eq(bo.getVariantId() != null, WhsStockAllocation::getVariantId, bo.getVariantId());
        lqw.eq(bo.getOrderId() != null, WhsStockAllocation::getOrderId, bo.getOrderId());
        lqw.eq(bo.getOrderItemId() != null, WhsStockAllocation::getOrderItemId, bo.getOrderItemId());
        lqw.eq(bo.getStatus() != null, WhsStockAllocation::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 查询库存分配详情
     */
    @Override
    public WhsStockAllocationVo getById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 锁定订单项对应的库存
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean lockStockForOrderItems(List<WhsOrderItem> orderItems, Long orderId) {
        if (CollUtil.isEmpty(orderItems) || orderId == null) {
            return true;
        }

        // 过滤有效的订单项
        List<WhsOrderItem> validItems = filterValidOrderItems(orderItems);
        if (validItems.isEmpty()) {
            log.debug("没有有效的商品项需要锁定库存");
            return true;
        }

        // 优化：可以考虑批量查询所有 variantId 的可用库存，然后内存匹配，减少 DB 查询次数
        // 但当前实现为逐个处理，保证逻辑清晰
        try {
            for (WhsOrderItem item : validItems) {
                // 查找可用库存
                WhsStock stock = findStockWithAvailableQuantity(item.getVariantId(), item.getQuantity());
                if (stock == null) {
                    // 直接使用订单项中的SKU编码
                    String skuCode = item.getSkuCode() != null ? item.getSkuCode() : String.valueOf(item.getVariantId());

                    // 日志中使用中文详细信息
                    String logErrorMsg = String.format("找不到足够的可用库存: variantId=%d, skuCode=%s, 需要数量=%d",
                        item.getVariantId(), skuCode, item.getQuantity());
                    log.error(logErrorMsg);

                    // 抛出异常时使用国际化消息，只提示变体SKU库存不足
                    throw new ServiceException(MessageUtils.message("wholesale.product.stock.insufficient", skuCode));
                }

                // 锁定库存
                if (!lockStock(stock, item, orderId)) {
                    // lockStock 内部失败时会抛出异常，理论上这里不会执行
                    // 为保险起见，如果 lockStock 修改为返回 boolean，这里也应抛出异常
                    // 直接使用订单项中的SKU编码
                    String skuCode = item.getSkuCode() != null ? item.getSkuCode() : String.valueOf(item.getVariantId());

                    // 日志中使用中文详细信息
                    log.error("锁定库存失败: variantId={}, skuCode={}, quantity={}",
                        item.getVariantId(), skuCode, item.getQuantity());

                    // 抛出异常时使用国际化消息
                    throw new ServiceException(MessageUtils.message("wholesale.product.stock.insufficient", skuCode));
                }
            }
            return true;
        } catch (Exception e) {
            log.error("锁定库存异常: orderId={}, error={}", orderId, e.getMessage(), e);
            // 重新抛出，确保事务回滚
            // 如果是我们自己抛出的异常，直接向上传递
            if (e instanceof ServiceException) {
                throw (ServiceException) e;
            }
            // 其他异常使用国际化消息
            throw new ServiceException(MessageUtils.message("wholesale.product.stock.insufficient"));
        }
    }

    /**
     * 过滤有效的订单项
     */
    private List<WhsOrderItem> filterValidOrderItems(List<WhsOrderItem> orderItems) {
        return orderItems.stream().filter(item -> item.getVariantId() != null && item.getQuantity() != null && item.getQuantity() > 0).collect(Collectors.toList());
    }

    /**
     * 查找具有足够可用数量的库存
     */
    private WhsStock findStockWithAvailableQuantity(Long variantId, int quantity) {
        LambdaQueryWrapper<WhsStock> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WhsStock::getVariantId, variantId);
        lqw.ge(WhsStock::getAvailableStock, quantity);
        lqw.orderByDesc(WhsStock::getAvailableStock);
        lqw.last("limit 1");
        return stockMapper.selectOne(lqw);
    }

    /**
     * 锁定库存
     */
    private boolean lockStock(WhsStock stock, WhsOrderItem item, Long orderId) {
        // 创建库存分配记录
        WhsStockAllocation allocation = new WhsStockAllocation();
        allocation.setWarehouseId(stock.getWarehouseId());
        allocation.setVariantId(item.getVariantId());
        allocation.setQuantity(item.getQuantity());
        allocation.setOrderId(orderId);
        allocation.setOrderItemId(item.getId());
        allocation.setStatus(StockAllocationStatus.ALLOCATED.getValue());
        baseMapper.insert(allocation);

        // 锁定库存
        int result = stockOperationService.updateStock(stock, StockOperationType.LOCK, item.getQuantity(), orderId, item.getId(), "订单锁定库存");

        return result > 0;
    }

    /**
     * 释放订单已锁定的库存
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean releaseStockByOrderId(Long orderId) {
        if (orderId == null) {
            return false;
        }

        // 查询订单的所有库存分配
        LambdaQueryWrapper<WhsStockAllocation> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WhsStockAllocation::getOrderId, orderId);
        lqw.eq(WhsStockAllocation::getStatus, StockAllocationStatus.ALLOCATED.getValue());
        List<WhsStockAllocation> allocations = baseMapper.selectList(lqw);

        if (CollUtil.isEmpty(allocations)) {
            return true;
        }

        // 优化：批量获取相关的库存信息
        Set<Long> variantIds = new HashSet<>();
        Set<Long> warehouseIds = new HashSet<>();
        for (WhsStockAllocation alloc : allocations) {
            variantIds.add(alloc.getVariantId());
            warehouseIds.add(alloc.getWarehouseId());
        }

        Map<String, WhsStock> stockMap = new HashMap<>();
        if (!variantIds.isEmpty()) {
            LambdaQueryWrapper<WhsStock> stockLqw = new LambdaQueryWrapper<>();
            stockLqw.in(WhsStock::getVariantId, variantIds);
            stockLqw.in(WhsStock::getWarehouseId, warehouseIds);
            List<WhsStock> stocks = stockMapper.selectList(stockLqw);
            stockMap = stocks.stream().collect(Collectors.toMap(s -> s.getWarehouseId() + ":" + s.getVariantId(), s -> s));
        }

        try {
            for (WhsStockAllocation allocation : allocations) {
                String stockKey = allocation.getWarehouseId() + ":" + allocation.getVariantId();
                WhsStock stock = stockMap.get(stockKey);

                if (!releaseSingleStockAllocation(allocation, stock)) {
                    // releaseSingleStockAllocation 失败时会抛出异常，理论上不会到这里
                    // 但为保险起见，如果修改了逻辑，这里也应抛出
                    throw new ServiceException(String.format("释放库存分配失败: allocationId=%d", allocation.getId()));
                }
            }

            return true;
        } catch (Exception e) {
            // 异常已在 releaseSingleStockAllocation 内部处理并可能重新抛出
            // 这里可以捕获特定异常或直接向上抛出
            if (e instanceof ServiceException) {
                throw (ServiceException) e;
            } else {
                log.error("释放订单 {} 库存时发生未知异常: {}", orderId, e.getMessage(), e);
                throw new ServiceException("释放库存时发生未知错误: " + e.getMessage());
            }
        }
    }

    /**
     * 释放单个库存分配
     *
     * @param allocation 分配记录
     * @param stock      库存记录（从外部传入，避免重复查询）
     * @return true 如果成功释放
     * @throws ServiceException 如果操作失败
     */
    private boolean releaseSingleStockAllocation(WhsStockAllocation allocation, WhsStock stock) {
        try {
            if (stock == null) {
                log.error("找不到要释放的库存记录: warehouseId={}, variantId={}", allocation.getWarehouseId(), allocation.getVariantId());
                // 抛出异常以回滚事务
                throw new ServiceException(String.format("释放库存失败：找不到库存记录 for allocation %d", allocation.getId()));
            }

            // 计算实际可释放数量（总分配量 - 已释放量）
            int releasedQty = allocation.getReleasedQuantity() != null ? allocation.getReleasedQuantity() : 0;
            int remainingQty = allocation.getQuantity() - releasedQty;

            // 如果没有剩余可释放的数量，直接返回成功
            if (remainingQty <= 0) {
                log.debug("库存分配 {} 已经完全释放，跳过", allocation.getId());
                return true;
            }

            // 释放库存，只释放尚未释放的部分
            int result = stockOperationService.updateStock(stock, StockOperationType.RELEASE, remainingQty, allocation.getOrderId(), allocation.getOrderItemId(), "订单释放锁定库存（此操作会增加可用库存）");

            if (result == 0) {
                log.info("释放库存操作结果为0，可能是因为锁定库存已经被释放: allocationId={}, stockId={}, 请求释放={}",
                    allocation.getId(), stock.getId(), remainingQty);
            }

            // 当result为0时也允许继续，表示没有实际释放任何库存（可能是因为锁定库存已经为0）
            // 更新已释放数量
            allocation.setReleasedQuantity(allocation.getQuantity());

            // 更新状态为已释放
            allocation.setStatus(StockAllocationStatus.RELEASED.getValue());

            int updateResult = baseMapper.updateById(allocation);
            if (updateResult <= 0) {
                log.error("更新库存分配状态失败 (并发修改?): allocationId={}", allocation.getId());
                // 抛出异常，因为库存已释放但分配状态未更新
                throw new ServiceException(String.format("更新库存分配状态失败: allocationId=%d", allocation.getId()));
            }
            return true;
        } catch (Exception e) {
            log.error("释放库存异常: allocationId={}, error={}", allocation.getId(), e.getMessage(), e);
            // 重新抛出，确保事务回滚
            if (e instanceof ServiceException) {
                throw (ServiceException) e;
            } else {
                throw new ServiceException("释放库存时发生异常: " + e.getMessage());
            }
        }
    }

    /**
     * 获取指定计划锁定的库存
     * 返回计划中各变体在各仓库的锁定库存
     *
     * @param planId 发货计划ID
     * @return 变体ID到仓库锁定库存的映射 Map<变体ID, Map<仓库ID, 锁定数量>>
     */
    @Override
    public Map<Long, Map<Long, Integer>> getLockedStockByPlanId(Long planId) {
        if (planId == null) {
            return Collections.emptyMap();
        }

        Map<Long, Map<Long, Integer>> result = new HashMap<>();

        try {
            // 1. 获取发货计划关联的订单ID
            LambdaQueryWrapper<WhsShipmentPlanItem> planQuery = new LambdaQueryWrapper<>();
            planQuery.eq(WhsShipmentPlanItem::getPlanId, planId).select(WhsShipmentPlanItem::getOrderId).last("LIMIT 1");
            WhsShipmentPlanItem planItem = shipmentPlanItemMapper.selectOne(planQuery);

            if (planItem == null || planItem.getOrderId() == null) {
                log.warn("未找到发货计划 {} 的订单信息", planId);
                return Collections.emptyMap();
            }

            Long orderId = planItem.getOrderId();

            // 2. 获取订单的所有锁定库存分配
            LambdaQueryWrapper<WhsStockAllocation> allocQuery = new LambdaQueryWrapper<>();
            allocQuery.eq(WhsStockAllocation::getOrderId, orderId).eq(WhsStockAllocation::getStatus, StockAllocationStatus.ALLOCATED.getValue());
            List<WhsStockAllocation> allocations = baseMapper.selectList(allocQuery);

            if (CollUtil.isEmpty(allocations)) {
                return Collections.emptyMap();
            }

            // 3. 统计每个变体在每个仓库的锁定数量
            for (WhsStockAllocation alloc : allocations) {
                Long variantId = alloc.getVariantId();
                Long warehouseId = alloc.getWarehouseId();
                int quantity = alloc.getQuantity();

                Map<Long, Integer> warehouseMap = result.computeIfAbsent(variantId, k -> new HashMap<>());
                warehouseMap.put(warehouseId, warehouseMap.getOrDefault(warehouseId, 0) + quantity);
            }

            return result;
        } catch (Exception e) {
            log.error("获取发货计划 {} 锁定库存信息失败: {}", planId, e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    /**
     * 释放指定数量的库存分配
     *
     * @param allocationId      库存分配ID
     * @param quantityToRelease 要释放的数量
     * @param remark            释放备注
     * @return 是否释放成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean releaseSpecificAllocation(Long allocationId, int quantityToRelease, String remark) {
        if (allocationId == null || quantityToRelease <= 0) {
            return false;
        }

        try {
            // 查询分配记录
            WhsStockAllocation allocation = baseMapper.selectById(allocationId);
            if (allocation == null) {
                log.warn("库存分配记录不存在: allocationId={}", allocationId);
                return false;
            }

            // 验证分配状态
            if (!StockAllocationStatus.ALLOCATED.getValue().equals(allocation.getStatus())) {
                log.warn("库存分配记录状态不是已分配: allocationId={}, status={}", allocationId, allocation.getStatus());
                return false;
            }

            // 验证并调整释放数量
            int actualQuantityToRelease = Math.min(quantityToRelease, allocation.getQuantity());
            if (actualQuantityToRelease <= 0) {
                log.warn("没有可释放的数量: allocationId={}, requestedQuantity={}, availableQuantity={}", allocationId, quantityToRelease, allocation.getQuantity());
                return false;
            }

            // 查询对应的库存记录
            WhsStock stock = stockMapper.selectOne(new LambdaQueryWrapper<WhsStock>().eq(WhsStock::getVariantId, allocation.getVariantId()).eq(WhsStock::getWarehouseId, allocation.getWarehouseId()));

            if (stock == null) {
                log.error("找不到对应的库存记录: variantId={}, warehouseId={}", allocation.getVariantId(), allocation.getWarehouseId());
                return false;
            }

            // 更新已释放数量
            int releasedQty = allocation.getReleasedQuantity() != null ? allocation.getReleasedQuantity() : 0;
            allocation.setReleasedQuantity(releasedQty + actualQuantityToRelease);

            // 如果全部释放完毕，更新状态
            if (allocation.getReleasedQuantity() >= allocation.getQuantity()) {
                allocation.setStatus(StockAllocationStatus.RELEASED.getValue());
            }

            // 更新分配记录
            baseMapper.updateById(allocation);

            // 执行库存释放操作
            int result = stockOperationService.updateStock(stock, StockOperationType.RELEASE, actualQuantityToRelease, allocation.getOrderId(), allocation.getOrderItemId(), remark);

            if (result == 0) {
                log.info("指定库存释放操作结果为0，可能是因为锁定库存已经被释放: allocationId={}, stockId={}, 请求释放={}",
                    allocationId, stock.getId(), actualQuantityToRelease);
            }

            // 即使result为0（没有实际释放任何库存），也认为操作成功
            return true;
        } catch (Exception e) {
            log.error("释放指定数量的库存分配失败: allocationId={}, quantity={}, error={}", allocationId, quantityToRelease, e.getMessage(), e);
            if (e instanceof ServiceException) {
                throw (ServiceException) e;
            } else {
                throw new ServiceException("释放库存分配失败: " + e.getMessage());
            }
        }
    }

    /**
     * 根据条件查询库存分配记录
     *
     * @param queryWrapper 查询条件
     * @return 库存分配记录列表
     */
    @Override
    public List<WhsStockAllocation> list(LambdaQueryWrapper<WhsStockAllocation> queryWrapper) {
        return baseMapper.selectList(queryWrapper);
    }
}
