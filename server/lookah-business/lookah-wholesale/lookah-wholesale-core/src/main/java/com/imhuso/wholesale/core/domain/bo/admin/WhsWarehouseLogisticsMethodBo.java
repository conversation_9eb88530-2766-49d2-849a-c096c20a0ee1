package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.wholesale.core.domain.WhsWarehouseLogisticsMethod;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 仓库物流方式业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WhsWarehouseLogisticsMethod.class)
public class WhsWarehouseLogisticsMethodBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 仓库ID
     */
    @NotNull(message = "仓库ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long warehouseId;

    /**
     * 提供商ID
     */
    @NotNull(message = "提供商ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long providerId;

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 物流方式代码
     */
    @NotBlank(message = "物流方式代码不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 50, message = "物流方式代码长度不能超过50个字符")
    private String methodCode;

    /**
     * 物流方式名称
     */
    @NotBlank(message = "物流方式名称不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 100, message = "物流方式名称长度不能超过100个字符")
    private String methodName;

    /**
     * 渠道ID(第三方系统)
     */
    private String channelId;

    /**
     * 状态（0停用 1启用）
     */
    private String status;

    /**
     * 优先级(1-10，数字越小优先级越高)
     */
    private Integer priority;

    /**
     * 预估成本
     */
    private BigDecimal estimatedCost;

    /**
     * 预估天数
     */
    private Integer estimatedDays;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 物流方式属性(JSON)
     */
    private String methodProperties;

    /**
     * 备注
     */
    private String remark;
}
