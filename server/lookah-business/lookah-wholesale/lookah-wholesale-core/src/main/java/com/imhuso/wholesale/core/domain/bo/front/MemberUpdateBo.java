package com.imhuso.wholesale.core.domain.bo.front;

import com.imhuso.wholesale.core.domain.WhsMember;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 批发会员信息更新业务对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AutoMapper(target = WhsMember.class)
public class MemberUpdateBo implements Serializable {

    /**
     * 名
     */
    @NotBlank(message = "{wholesale.member.first.name.not.blank}")
    @Size(max = 50, message = "{wholesale.member.first.name.size}")
    private String firstName;

    /**
     * 姓
     */
    @NotBlank(message = "{wholesale.member.last.name.not.blank}")
    @Size(max = 50, message = "{wholesale.member.last.name.size}")
    private String lastName;

    /**
     * 公司类型（wholesale,retail,chain,cash_carry）
     */
    private String companyType;

    /**
     * 店面数量
     */
    private Integer storeCount;

    /**
     * 客户来源
     */
    private String customerSource;

    /**
     * 公司名称
     */
    private String companyName;

}
