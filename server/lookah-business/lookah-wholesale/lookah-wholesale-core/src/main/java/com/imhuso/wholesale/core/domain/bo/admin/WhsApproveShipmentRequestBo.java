package com.imhuso.wholesale.core.domain.bo.admin;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 审核申请业务对象
 *
 * <AUTHOR>
 */
@Data
public class WhsApproveShipmentRequestBo {

    /**
     * 审核结果
     */
    @NotNull(message = "审核结果不能为空")
    private Boolean approved;

    /**
     * 审核意见
     */
    @NotBlank(message = "审核意见不能为空")
    private String approvalComment;
}