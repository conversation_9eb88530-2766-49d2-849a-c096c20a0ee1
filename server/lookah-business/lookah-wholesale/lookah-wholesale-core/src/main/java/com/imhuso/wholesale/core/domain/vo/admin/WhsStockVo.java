package com.imhuso.wholesale.core.domain.vo.admin;

import com.imhuso.common.translation.annotation.Translation;
import com.imhuso.wholesale.core.constant.TransConstant;
import com.imhuso.wholesale.core.domain.WhsStock;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 库存视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsStock.class)
public class WhsStockVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 库存ID
     */
    private Long id;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 总库存
     */
    private Integer totalStock;

    /**
     * 可用库存
     */
    private Integer availableStock;

    /**
     * 锁定库存
     */
    private Integer lockedStock;

    /**
     * 库存预警阈值
     */
    private Integer alertThreshold;

    /**
     * 库存状态（0-正常，1-预警，2-紧缺）
     */
    private Integer stockStatus;

    /**
     * 库存状态文本
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, mapper = "stockStatus")
    private String stockStatusText;

    /**
     * 在途库存
     */
    private Integer inTransitStock;

    /**
     * 成品库存数量
     */
    private Integer finishedStock;

    /**
     * 待产库存数量
     */
    private Integer pendingStock;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
