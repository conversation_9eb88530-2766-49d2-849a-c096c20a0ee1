package com.imhuso.wholesale.core.enums;

import com.imhuso.common.translation.core.EnumTranslatableInterface;
import lombok.Getter;

/**
 * 物流包裹状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum PackageStatus implements EnumTranslatableInterface {

    /**
     * 待处理
     */
    PENDING(0),

    /**
     * 已发货
     */
    SHIPPED(1),

    /**
     * 运输中
     */
    IN_TRANSIT(2),

    /**
     * 已送达
     */
    DELIVERED(3),

    /**
     * 异常
     */
    EXCEPTION(4);


    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 构造函数
     */
    PackageStatus(int value) {
        this.value = value;
    }
}
