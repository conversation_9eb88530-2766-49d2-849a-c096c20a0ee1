package com.imhuso.wholesale.core.domain.bo.front;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 购物车项业务对象
 *
 * <AUTHOR>
 */
@Data
public class CartItemBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 变体ID
     */
    @NotNull(message = "wholesale.cart.variant.id.not.null")
    private Long variantId;

    /**
     * 数量
     */
    @NotNull(message = "wholesale.cart.quantity.not.null")
    @Min(value = 1, message = "wholesale.cart.quantity.min")
    private Integer quantity;
}
