package com.imhuso.wholesale.core.domain.vo.warehouse;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Builder;
import lombok.AllArgsConstructor;

/**
 * 海外仓创建订单结果VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateOrderResultVo {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误消息 (如果 success 为 false)
     */
    private String errorMessage;

    /**
     * 海外仓返回的跟踪号
     */
    private String trackingNo;

    /**
     * 海外仓返回的系统订单号
     */
    private String orderNo;

    /**
     * 海外仓返回的客户参考号
     */
    private String csRefNo;

}
