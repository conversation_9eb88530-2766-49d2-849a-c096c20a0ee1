package com.imhuso.wholesale.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imhuso.wholesale.core.domain.WhsInTransitStock;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 在途库存Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface WhsInTransitStockMapper extends BaseMapper<WhsInTransitStock> {

    /**
     * 批量更新在途库存
     *
     * @param stocks 在途库存列表
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("stocks") List<WhsInTransitStock> stocks);
}
