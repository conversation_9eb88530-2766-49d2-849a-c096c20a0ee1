package com.imhuso.wholesale.core.domain.vo.front;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.imhuso.wholesale.core.domain.WhsState;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 州/省视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsState.class)
public class StateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 州/省ID
     */
    private Long id;

    /**
     * 国家ID
     */
    private Long countryId;

    /**
     * 州/省代码
     */
    private String code;

    /**
     * 州/省名称
     */
    private String name;

    /**
     * 州/省中文名称
     */
    @JsonIgnore
    private String nameZh;

    /**
     * 是否启用（0正常 1停用）
     */
    private String status;

    /**
     * 排序
     */
    private Integer sort;
}
