package com.imhuso.wholesale.core.domain.bo.admin;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 产品规格业务对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "产品规格业务对象")
public class WhsProductSpecBo {

    /**
     * 规格键
     */
    @Schema(description = "规格键")
    private String key;

    /**
     * 规格名称
     */
    @Schema(description = "规格名称")
    private String name;

    /**
     * 规格值列表
     */
    @Schema(description = "规格值列表")
    private List<SpecValueBo> values;

    /**
     * 规格值业务对象
     */
    @Data
    @Schema(description = "规格值业务对象")
    public static class SpecValueBo {

        /**
         * 值标识
         */
        @Schema(description = "值标识")
        private String code;

        /**
         * 值名称
         */
        @Schema(description = "值名称")
        private String name;
    }
}
