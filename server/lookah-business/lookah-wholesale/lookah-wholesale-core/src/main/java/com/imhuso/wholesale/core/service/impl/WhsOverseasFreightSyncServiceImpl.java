package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.SpringUtils;
import com.imhuso.wholesale.core.adapter.impl.HaotongOverseasWarehouseProvider;
import com.imhuso.wholesale.core.constant.FreightSyncConstants;
import com.imhuso.wholesale.core.domain.WhsLogisticsPackage;
import com.imhuso.wholesale.core.domain.WhsOrderFreight;
import com.imhuso.wholesale.core.domain.WhsWarehouse;
import com.imhuso.wholesale.core.domain.vo.warehouse.WarehouseContextVo;
import com.imhuso.wholesale.core.mapper.WhsLogisticsPackageMapper;
import com.imhuso.wholesale.core.mapper.WhsOrderFreightMapper;
import com.imhuso.wholesale.core.mapper.WhsWarehouseMapper;
import com.imhuso.wholesale.core.service.IWhsOverseasFreightSyncService;
import com.imhuso.wholesale.core.service.IWhsOverseasWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 海外仓运费同步服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsOverseasFreightSyncServiceImpl implements IWhsOverseasFreightSyncService {

    private final WhsWarehouseMapper warehouseMapper;
    private final WhsLogisticsPackageMapper logisticsPackageMapper;
    private final WhsOrderFreightMapper orderFreightMapper;
    private final IWhsOverseasWarehouseService overseasWarehouseService;
    private final HaotongOverseasWarehouseProvider haotongProvider;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int syncAllWarehousesFreight() {
        log.info("开始同步所有海外仓的运费信息");

        // 查询所有海外仓
        LambdaQueryWrapper<WhsWarehouse> warehouseQuery = new LambdaQueryWrapper<>();
        warehouseQuery.eq(WhsWarehouse::getIsOverseas, BusinessConstants.YES);
        List<WhsWarehouse> overseasWarehouses = warehouseMapper.selectList(warehouseQuery);

        if (CollUtil.isEmpty(overseasWarehouses)) {
            log.info("未找到海外仓，跳过运费同步");
            return 0;
        }

        int totalSyncCount = 0;
        for (WhsWarehouse warehouse : overseasWarehouses) {
            try {
                int syncCount = SpringUtils.getAopProxy(this).syncWarehouseFreight(warehouse.getId());
                totalSyncCount += syncCount;
                log.info("仓库 {} 运费同步完成，同步订单数: {}", warehouse.getName(), syncCount);
            } catch (Exception e) {
                log.error("仓库 {} 运费同步失败: {}", warehouse.getName(), e.getMessage(), e);
            }
        }

        log.info("所有海外仓运费同步完成，总计同步订单数: {}", totalSyncCount);
        return totalSyncCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int syncWarehouseFreight(Long warehouseId) {
        if (warehouseId == null) {
            log.warn("仓库ID不能为空");
            throw new ServiceException("仓库ID不能为空");
        }

        log.info("开始同步仓库 {} 的运费信息", warehouseId);

        try {
            // 获取海外仓上下文（使用缓存）
            WarehouseContextVo context = SpringUtils.getAopProxy(this).getCachedWarehouseContext(warehouseId);
            if (context == null) {
                log.warn("无法创建仓库 {} 的海外仓上下文", warehouseId);
                return 0;
            }

            // 查询该仓库的所有物流包裹（有外部订单号且未同步运费的）
            List<WhsLogisticsPackage> packages = getUnsyncedPackages(warehouseId);
            if (CollUtil.isEmpty(packages)) {
                log.info("仓库 {} 没有找到需要同步运费的物流包裹（可能都已同步）", warehouseId);
                return 0;
            }

            // 按外部订单号分组，避免重复查询
            Map<String, List<WhsLogisticsPackage>> packagesByExternalOrderNo = packages.stream()
                .collect(Collectors.groupingBy(WhsLogisticsPackage::getExternalOrderNo));

            // 批量查询昊通订单信息
            return syncFreightByExternalOrderNos(context, packagesByExternalOrderNo);

        } catch (Exception e) {
            log.error("同步仓库 {} 运费信息异常: {}", warehouseId, e.getMessage(), e);
            throw new ServiceException("同步仓库运费信息失败: " + e.getMessage());
        }
    }


    /**
     * 根据外部订单号批量同步运费信息
     */
    private int syncFreightByExternalOrderNos(WarehouseContextVo context,
                                              Map<String, List<WhsLogisticsPackage>> packagesByExternalOrderNo) {
        int syncCount = 0;

        // 构建外部订单号列表，用于批量查询
        List<String> externalOrderNos = new ArrayList<>(packagesByExternalOrderNo.keySet());

        // 分批处理，避免单次查询过多
        int batchSize = FreightSyncConstants.SyncConfig.BATCH_SIZE;
        for (int i = 0; i < externalOrderNos.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, externalOrderNos.size());
            List<String> batchOrderNos = externalOrderNos.subList(i, endIndex);

            try {
                syncCount += processBatchOrderNos(context, batchOrderNos, packagesByExternalOrderNo);
            } catch (Exception e) {
                log.error("批量处理外部订单号失败: {}", batchOrderNos, e);
            }
        }

        return syncCount;
    }

    /**
     * 处理一批外部订单号
     */
    private int processBatchOrderNos(WarehouseContextVo context, List<String> batchOrderNos,
                                     Map<String, List<WhsLogisticsPackage>> packagesByExternalOrderNo) {
        // 调用昊通API查询订单信息（状态3和4）
        Map<String, Object> queryResult = queryHaotongOrders(context, batchOrderNos);

        if (queryResult == null || !Boolean.TRUE.equals(queryResult.get("success"))) {
            log.warn("查询昊通订单信息失败: {}", batchOrderNos);
            return 0;
        }

        // 解析查询结果并同步运费
        return processHaotongOrderResults(queryResult, packagesByExternalOrderNo);
    }

    /**
     * 查询昊通订单信息
     * 不设置OrderStatus参数，获取所有状态的订单，然后在代码中过滤待发货（3）和已发货（4）状态
     */
    private Map<String, Object> queryHaotongOrders(WarehouseContextVo context, List<String> orderNos) {
        try {
            // 构建查询参数
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("OrderNoList", String.join(",", orderNos));
            // 不设置OrderStatus，获取所有状态的订单
            queryParams.put("PageSize", FreightSyncConstants.SyncConfig.API_PAGE_SIZE);
            queryParams.put("PageIndex", FreightSyncConstants.SyncConfig.DEFAULT_PAGE_INDEX);

            // 调用昊通订单查询API
            return haotongProvider.queryOrders(context, queryParams);

        } catch (Exception e) {
            log.error("查询昊通订单信息异常: {}", orderNos, e);
            return null;
        }
    }

    /**
     * 处理昊通订单查询结果
     */
    private int processHaotongOrderResults(Map<String, Object> queryResult,
                                           Map<String, List<WhsLogisticsPackage>> packagesByExternalOrderNo) {
        int syncCount = 0;

        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> orders = (List<Map<String, Object>>) queryResult.get("data");

            if (CollUtil.isEmpty(orders)) {
                log.info("昊通查询结果为空");
                return 0;
            }

            for (Map<String, Object> orderData : orders) {
                String externalOrderNo = String.valueOf(orderData.get("OrderNo"));
                log.info("处理订单 [{}]，订单数据: {}", externalOrderNo, orderData);

                // 过滤订单状态：只处理待发货（3）和已发货（4）的订单
                Integer orderStatus = Convert.toInt(orderData.get("Status"));
                if (orderStatus == null ||
                    (orderStatus != FreightSyncConstants.HaotongOrderStatus.PENDING_SHIPMENT &&
                        orderStatus != FreightSyncConstants.HaotongOrderStatus.SHIPPED)) {
                    log.info("跳过订单 [{}]，状态为 [{}]，只处理待发货（3）和已发货（4）状态", externalOrderNo, orderStatus);
                    continue;
                }

                log.info("订单 [{}] 状态为 [{}]，符合同步条件", externalOrderNo, orderStatus);

                List<WhsLogisticsPackage> packages = packagesByExternalOrderNo.get(externalOrderNo);

                if (CollUtil.isEmpty(packages)) {
                    log.info("订单 [{}] 没有找到对应的包裹信息", externalOrderNo);
                    continue;
                }

                log.info("订单 [{}] 找到 {} 个包裹", externalOrderNo, packages.size());

                // 解析运费信息
                Object feeObj = orderData.get("Fee");
                log.info("订单 [{}] 原始运费数据: {}", externalOrderNo, feeObj);

                BigDecimal freight = parseFreight(feeObj);
                log.info("订单 [{}] 解析后运费: {}", externalOrderNo, freight);

                if (freight != null && freight.compareTo(BigDecimal.ZERO) > 0) {
                    log.info("订单 [{}] 开始同步运费 {} 到 {} 个包裹", externalOrderNo, freight, packages.size());
                    // 为每个包裹同步运费信息
                    for (WhsLogisticsPackage pkg : packages) {
                        if (syncPackageFreightData(pkg, freight, orderData, externalOrderNo)) {
                            syncCount++;
                            log.info("包裹 [{}] 运费同步成功", pkg.getId());
                        } else {
                            log.warn("包裹 [{}] 运费同步失败", pkg.getId());
                        }
                    }
                } else {
                    log.warn("订单 [{}] 运费为空或为0，跳过同步", externalOrderNo);
                }
            }

        } catch (Exception e) {
            log.error("处理昊通订单查询结果异常", e);
        }

        return syncCount;
    }


    /**
     * 同步订单运费数据到数据库
     */
    private boolean syncPackageFreightData(WhsLogisticsPackage pkg, BigDecimal freight,
                                           Map<String, Object> orderData, String externalOrderNo) {
        try {
            // 查询是否已存在运费记录
            LambdaQueryWrapper<WhsOrderFreight> query = new LambdaQueryWrapper<>();
            query.eq(WhsOrderFreight::getPackageId, pkg.getId());
            WhsOrderFreight existingFreight = orderFreightMapper.selectOne(query);

            WhsOrderFreight freightEntity = new WhsOrderFreight();
            if (existingFreight != null) {
                freightEntity.setId(existingFreight.getId());
            }

            // 设置基本信息
            freightEntity.setPackageId(pkg.getId());
            freightEntity.setOrderId(pkg.getOrderId());
            freightEntity.setActualFreight(freight);
            freightEntity.setFreightCurrency(FreightSyncConstants.SyncConfig.DEFAULT_CURRENCY);
            freightEntity.setProviderType(FreightSyncConstants.ProviderType.HAOTONG);
            freightEntity.setProviderOrderNo(externalOrderNo);

            // 设置跟踪号（如果有）
            String trackingNo = String.valueOf(orderData.getOrDefault("TrackingNo", ""));
            if (StringUtils.isNotBlank(trackingNo) && !"null".equals(trackingNo)) {
                freightEntity.setProviderTrackingNo(trackingNo);
            }

            freightEntity.setSyncTime(new Date());
            freightEntity.setSyncStatus(FreightSyncConstants.SyncStatus.SYNCED);

            // 保存或更新
            boolean success;
            if (existingFreight != null) {
                success = orderFreightMapper.updateById(freightEntity) > 0;
                log.info("更新包裹 {} 运费信息成功，运费: {} USD", pkg.getId(), freight);
            } else {
                success = orderFreightMapper.insert(freightEntity) > 0;
                log.info("新增包裹 {} 运费信息成功，运费: {} USD", pkg.getId(), freight);
            }

            return success;

        } catch (Exception e) {
            log.error("同步包裹 {} 运费数据异常", pkg.getId(), e);
            return false;
        }
    }

    /**
     * 缓存的仓库上下文创建方法
     */
    @Cacheable(value = "warehouse_context", key = "#warehouseId", unless = "#result == null")
    public WarehouseContextVo getCachedWarehouseContext(Long warehouseId) {
        return overseasWarehouseService.createWarehouseContext(warehouseId);
    }

    /**
     * 查询指定仓库未同步运费的包裹
     */
    private List<WhsLogisticsPackage> getUnsyncedPackages(Long warehouseId) {
        return logisticsPackageMapper.selectUnsyncedPackagesByWarehouseId(warehouseId);
    }


    /**
     * 解析运费金额
     */
    private BigDecimal parseFreight(Object freightObj) {
        if (freightObj == null) {
            return null;
        }

        try {
            String freightStr = String.valueOf(freightObj);
            if (StringUtils.isBlank(freightStr) || "null".equals(freightStr)) {
                return null;
            }
            return new BigDecimal(freightStr);
        } catch (NumberFormatException e) {
            log.warn("解析运费金额失败: {}", freightObj);
            return null;
        }
    }

    /**
     * 异步同步所有海外仓的运费信息
     * 该方法在后台异步执行，避免前端请求超时
     */
    @Override
    @Async
    public void asyncSyncAllWarehousesFreight() {
        try {
            log.info("开始异步同步所有海外仓的运费信息");
            int syncCount = SpringUtils.getAopProxy(this).syncAllWarehousesFreight();
            log.info("所有海外仓运费同步完成，成功同步 {} 个订单的运费信息", syncCount);
        } catch (Exception e) {
            log.error("所有海外仓运费同步失败: {}", e.getMessage(), e);
        }
    }

}
