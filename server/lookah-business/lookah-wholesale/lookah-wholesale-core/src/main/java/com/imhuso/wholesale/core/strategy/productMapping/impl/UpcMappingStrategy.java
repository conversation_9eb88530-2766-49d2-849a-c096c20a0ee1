package com.imhuso.wholesale.core.strategy.productMapping.impl;

import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.vo.warehouse.WarehouseContextVo;
import com.imhuso.wholesale.core.service.IWhsProductVariantService;
import com.imhuso.wholesale.core.strategy.productMapping.IProductMappingStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 基于UPC的产品映射策略
 * 使用UPC作为外部系统的标识符，如果UPC为空则回退到SKU
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UpcMappingStrategy implements IProductMappingStrategy {

    private final IWhsProductVariantService variantService;

    /**
     * 策略类型
     */
    private static final String STRATEGY_TYPE = "UPC";

    @Override
    public String getStrategyType() {
        return STRATEGY_TYPE;
    }

    @Override
    public String getStrategyName() {
        return "UPC映射";
    }

    @Override
    public boolean isApplicable(WarehouseContextVo context) {
        // 检查仓库配置中是否指定使用UPC
        Map<String, String> config = context.getWarehouseConfig();
        if (config != null) {
            String useUpc = config.get("useUpc");
            return Boolean.parseBoolean(useUpc);
        }
        return false;
    }

    @Override
    public Map<Long, String> getExternalCodes(WarehouseContextVo context, List<WhsProductVariant> variants) {
        if (variants == null || variants.isEmpty()) {
            return new HashMap<>();
        }

        // 批量处理，优先使用UPC，为空则使用SKU
        return variants.stream().collect(Collectors.toMap(WhsProductVariant::getId, variant -> StringUtils.isNotEmpty(variant.getUpc()) ? variant.getUpc() : variant.getSkuCode(), (existing, replacement) -> existing // 如果有重复的ID，保留第一个值
        ));
    }

    @Override
    public Map<String, Long> getVariantIdsByExternalCodes(WarehouseContextVo context, List<String> externalCodes) {
        if (externalCodes == null || externalCodes.isEmpty()) {
            return new HashMap<>();
        }

        // 首先尝试通过UPC批量查找
        // 同样，这里需要添加一个根据UPC批量查找变体的方法
        // 实际应该实现根据UPC查找变体的方法，这里为了简化示例，直接使用SKU查找
        return variantService.getVariantIdsBySkuCodes(externalCodes);
    }
}
