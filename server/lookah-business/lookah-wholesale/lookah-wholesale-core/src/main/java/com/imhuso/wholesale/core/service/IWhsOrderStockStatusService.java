package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.enums.StockStatus;

import java.util.List;
import java.util.Map;

/**
 * 订单库存状态服务接口
 *
 * <AUTHOR>
 */
public interface IWhsOrderStockStatusService {

    /**
     * 计算单个订单的库存状态
     *
     * @param orderId 订单ID
     * @return 库存状态
     */
    StockStatus calculateOrderStockStatus(Long orderId);

    /**
     * 批量计算多个订单的库存状态
     *
     * @param orderIds 订单ID列表
     * @return 订单ID到库存状态的映射
     */
    Map<Long, StockStatus> batchCalculateOrderStockStatus(List<Long> orderIds);

    /**
     * 检查订单是否有库存不足的商品
     *
     * @param orderId 订单ID
     * @return 是否有库存不足的商品
     */
    boolean hasInsufficientStock(Long orderId);

    /**
     * 批量检查多个订单是否有库存不足的商品
     *
     * @param orderIds 订单ID列表
     * @return 订单ID到是否有库存不足商品的映射
     */
    Map<Long, Boolean> batchCheckInsufficientStock(List<Long> orderIds);
}
