package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.WhsOrder;

import java.util.List;

/**
 * 发货审批配置服务接口
 * 统一管理审批相关的配置读取和权限判断逻辑
 *
 * <AUTHOR>
 */
public interface IApprovalConfigService {

    /**
     * 获取审批功能是否启用
     */
    boolean isApprovalEnabled();

    /**
     * 获取是否自动发起审批申请
     */
    boolean isAutoRequest();

    /**
     * 获取是否自动审批通过
     */
    boolean isAutoApprove();

    /**
     * 检查当前用户是否有审批权限
     * 基于菜单权限 wholesale:shipment:approve 进行判断
     */
    boolean hasApprovalPermission();

    /**
     * 检查是否应该显示撤回审批按钮
     */
    boolean shouldShowCancelApprovalButton(WhsOrder order);

    /**
     * 检查是否应该显示审批操作按钮（通过/拒绝）
     */
    boolean shouldShowApproveButton(WhsOrder order);

    /**
     * 获取可发货的审批状态列表
     * 根据当前审批配置动态判断哪些审批状态的订单可以发货
     *
     * @return 可发货的审批状态列表，如果返回null表示查询所有状态
     */
    List<Integer> getShippableApprovalStatuses();
}
