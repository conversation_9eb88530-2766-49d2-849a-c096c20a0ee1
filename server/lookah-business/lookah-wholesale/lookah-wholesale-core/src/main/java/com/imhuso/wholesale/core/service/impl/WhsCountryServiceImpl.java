package com.imhuso.wholesale.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.constant.WhsCacheConstants;
import com.imhuso.wholesale.core.domain.WhsCountry;
import com.imhuso.wholesale.core.domain.vo.admin.WhsCountryVo;
import com.imhuso.wholesale.core.mapper.WhsCountryMapper;
import com.imhuso.wholesale.core.service.IWhsCountryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 国家服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsCountryServiceImpl implements IWhsCountryService {

    private final WhsCountryMapper baseMapper;

    /**
     * 根据国家代码获取国家信息
     *
     * @param code 国家代码
     * @return 国家信息
     */
    @Override
    @Cacheable(cacheNames = WhsCacheConstants.COUNTRY_CACHE, key = "'code:' + #code")
    public WhsCountryVo getCountryByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }

        // 直接查询数据库，避免自调用
        LambdaQueryWrapper<WhsCountry> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WhsCountry::getCode, code);
        lqw.eq(WhsCountry::getStatus, BusinessConstants.NORMAL);
        return baseMapper.selectVoOne(lqw, WhsCountryVo.class);
    }

    /**
     * 根据国家代码获取国家信息（支持指定返回类型）
     *
     * @param code  国家代码
     * @param clazz 返回类型的Class
     * @param <T>   返回类型
     * @return 国家信息
     */
    @Override
    @Cacheable(cacheNames = WhsCacheConstants.COUNTRY_CACHE, key = "'code:' + #code + ':' + #clazz.getName()")
    public <T> T getCountryByCode(String code, Class<T> clazz) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }

        // 从数据库查询
        LambdaQueryWrapper<WhsCountry> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WhsCountry::getCode, code);
        lqw.eq(WhsCountry::getStatus, BusinessConstants.NORMAL);
        return baseMapper.selectVoOne(lqw, clazz);
    }

    /**
     * 根据国家代码获取国家名称
     *
     * @param code 国家代码
     * @return 国家名称
     */
    @Override
    @Cacheable(cacheNames = WhsCacheConstants.COUNTRY_CACHE, key = "'name:' + #code")
    public String getCountryNameByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return code;
        }

        // 直接使用baseMapper的selectOne方法查询实体，避免类型转换问题
        LambdaQueryWrapper<WhsCountry> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WhsCountry::getCode, code);
        lqw.eq(WhsCountry::getStatus, BusinessConstants.NORMAL);
        WhsCountry country = baseMapper.selectOne(lqw);
        return country != null ? country.getName() : code;
    }

    /**
     * 获取所有启用的国家列表
     *
     * @return 国家列表
     */
    @Override
    @Cacheable(cacheNames = WhsCacheConstants.COUNTRY_CACHE, key = "'all:' + T(com.imhuso.wholesale.core.domain.vo.admin.WhsCountryVo).class.getName()")
    public List<WhsCountryVo> getAllEnabledCountries() {
        // 直接查询数据库，避免自调用
        LambdaQueryWrapper<WhsCountry> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WhsCountry::getStatus, BusinessConstants.NORMAL);
        lqw.orderByAsc(WhsCountry::getSort);
        return baseMapper.selectVoList(lqw, WhsCountryVo.class);
    }

    /**
     * 获取所有启用的国家列表（支持指定返回类型）
     *
     * @param clazz 返回类型的Class
     * @param <T>   返回类型
     * @return 国家列表
     */
    @Override
    @Cacheable(cacheNames = WhsCacheConstants.COUNTRY_CACHE, key = "'all:' + #clazz.getName()")
    public <T> List<T> getAllEnabledCountries(Class<T> clazz) {
        // 从数据库查询所有启用的国家
        LambdaQueryWrapper<WhsCountry> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WhsCountry::getStatus, BusinessConstants.NORMAL);
        lqw.orderByAsc(WhsCountry::getSort);
        return baseMapper.selectVoList(lqw, clazz);
    }
}
