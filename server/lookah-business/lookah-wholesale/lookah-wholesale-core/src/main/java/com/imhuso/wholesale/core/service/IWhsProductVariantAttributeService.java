package com.imhuso.wholesale.core.service;

import java.util.List;
import java.util.Map;

import com.imhuso.wholesale.core.domain.WhsProductVariantAttribute;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVariantAttributeVo;

/**
 * 产品变体属性关联Service接口
 *
 * <AUTHOR>
 */
public interface IWhsProductVariantAttributeService {

    /**
     * 删除变体的所有属性关联
     *
     * @param variantId 变体ID
     */
    void deleteByVariantId(Long variantId);

    /**
     * 批量插入变体属性关联
     *
     * @param attributes 变体属性关联列表
     */
    void batchInsert(List<WhsProductVariantAttribute> attributes);

    /**
     * 处理变体属性关联（增删改）
     *
     * @param productId       产品ID
     * @param variantId       变体ID
     * @param newAttributeMap 属性ID到属性值ID的映射
     */
    void processVariantAttributes(Long productId, Long variantId, Map<Long, Long> newAttributeMap);

    /**
     * 获取变体的属性列表
     *
     * @param variantId 变体ID
     * @return 变体选择的属性列表
     */
    List<WhsProductVariantAttributeVo> getVariantAttributes(Long variantId);

    /**
     * 批量获取变体的属性列表
     *
     * @param variantIds 变体ID列表
     * @return 变体ID到属性列表的映射
     */
    Map<Long, List<WhsProductVariantAttributeVo>> getVariantAttributesMap(List<Long> variantIds);
}
