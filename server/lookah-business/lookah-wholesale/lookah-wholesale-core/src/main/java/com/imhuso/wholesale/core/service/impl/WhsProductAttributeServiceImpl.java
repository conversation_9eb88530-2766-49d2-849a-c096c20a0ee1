package com.imhuso.wholesale.core.service.impl;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.WhsProductAttribute;
import com.imhuso.wholesale.core.domain.WhsProductVariantAttribute;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductAttributeBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductAttributeValueVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductAttributeVo;
import com.imhuso.wholesale.core.mapper.WhsProductAttributeMapper;
import com.imhuso.wholesale.core.mapper.WhsProductVariantAttributeMapper;
import com.imhuso.wholesale.core.service.IWhsProductAttributeService;
import com.imhuso.wholesale.core.service.IWhsProductAttributeValueService;
import com.imhuso.wholesale.core.service.IWhsProductAttributeRelationService;

import lombok.RequiredArgsConstructor;
import cn.hutool.core.collection.CollUtil;

import java.util.Collections;
import java.util.Set;

/**
 * 产品属性Service业务层处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class WhsProductAttributeServiceImpl implements IWhsProductAttributeService {

    private final WhsProductAttributeMapper attributeMapper;
    private final IWhsProductAttributeValueService attributeValueService;
    private final WhsProductVariantAttributeMapper variantAttributeMapper;
    private final IWhsProductAttributeRelationService attributeRelationService;

    @Override
    public List<WhsProductAttributeVo> queryList(WhsProductAttributeBo bo) {
        LambdaQueryWrapper<WhsProductAttribute> lqw = buildQueryWrapper(bo);
        return attributeMapper.selectVoList(lqw);
    }

    @Override
    public TableDataInfo<WhsProductAttributeVo> queryPage(WhsProductAttributeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WhsProductAttribute> lqw = buildQueryWrapper(bo);
        Page<WhsProductAttributeVo> page = attributeMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    @Override
    public WhsProductAttributeVo getById(Long id) {
        WhsProductAttributeVo vo = attributeMapper.selectVoById(id);
        if (vo != null) {
            // 获取属性值列表
            List<WhsProductAttributeValueVo> values = attributeValueService.listByAttributeId(id);
            vo.setValues(values);
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertAttribute(WhsProductAttributeBo bo) {
        // 检查名称是否唯一
        if (attributeNameExists(bo.getAttrName(), null)) {
            throw new ServiceException("属性已存在");
        }
        WhsProductAttribute attribute = MapstructUtils.convert(bo, WhsProductAttribute.class);
        return attributeMapper.insert(attribute);
    }

    @Override
    public int updateAttribute(WhsProductAttributeBo bo) {
        // 检查名称是否唯一
        if (attributeNameExists(bo.getAttrName(), bo.getId())) {
            throw new ServiceException("属性已存在");
        }

        // 转换并更新
        WhsProductAttribute attribute = MapstructUtils.convert(bo, WhsProductAttribute.class);
        return attributeMapper.updateById(attribute);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteAttributeById(Long id) {
        // 检查属性是否被产品使用
        if (attributeRelationService.isAttributeUsedByProducts(id)) {
            throw new ServiceException("属性已被产品绑定，不能删除");
        }

        // 检查属性是否被变体使用
        LambdaQueryWrapper<WhsProductVariantAttribute> variantQuery = Wrappers.lambdaQuery();
        variantQuery.eq(WhsProductVariantAttribute::getAttributeId, id);
        if (variantAttributeMapper.exists(variantQuery)) {
            throw new ServiceException("属性已被产品变体使用，不能删除");
        }

        return attributeMapper.deleteById(id);
    }

    /**
     * 检查属性名是否已存在
     */
    public boolean attributeNameExists(String name, Long id) {
        LambdaQueryWrapper<WhsProductAttribute> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WhsProductAttribute::getAttrName, name).ne(id != null, WhsProductAttribute::getId, id);
        return attributeMapper.selectCount(lqw) > 0;
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<WhsProductAttribute> buildQueryWrapper(WhsProductAttributeBo bo) {
        LambdaQueryWrapper<WhsProductAttribute> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getAttrName()), WhsProductAttribute::getAttrName, bo.getAttrName());
        lqw.like(StringUtils.isNotBlank(bo.getAttrDisplay()), WhsProductAttribute::getAttrDisplay,
                bo.getAttrDisplay());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), WhsProductAttribute::getStatus, bo.getStatus());
        lqw.orderByAsc(WhsProductAttribute::getSort);
        return lqw;
    }

    @Override
    public Map<Long, WhsProductAttributeVo> getAttributeMap(List<Long> ids, boolean fillValues) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<WhsProductAttribute> wrapper = Wrappers.lambdaQuery();
        wrapper.in(WhsProductAttribute::getId, ids);

        Map<Long, WhsProductAttributeVo> attributeMap = attributeMapper.selectVoList(wrapper).stream()
                .collect(Collectors.toMap(WhsProductAttributeVo::getId, Function.identity()));

        if (!attributeMap.isEmpty()) {
            // 1. 获取被变体使用的属性ID集合
            Set<Long> usedAttributeIds = variantAttributeMapper.selectList(
                    Wrappers.lambdaQuery(WhsProductVariantAttribute.class)
                            .select(WhsProductVariantAttribute::getAttributeId)
                            .in(WhsProductVariantAttribute::getAttributeId, ids))
                    .stream()
                    .map(WhsProductVariantAttribute::getAttributeId)
                    .collect(Collectors.toSet());

            // 2. 设置属性是否被变体使用的标志
            attributeMap.forEach((id, attribute) -> attribute.setIsUsedByVariants(usedAttributeIds.contains(id)));

            // 3. 如果需要填充属性值
            if (fillValues) {
                attributeMap.values().forEach(attribute -> {
                    List<WhsProductAttributeValueVo> values = attributeValueService
                            .listByAttributeId(attribute.getId());
                    attribute.setValues(values);
                });
            }
        }

        return attributeMap;
    }
}
