package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.Version;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批发收货地址对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_shipping_address")
public class WhsShippingAddress extends BaseEntity {

    /**
     * 地址ID
     */
    @TableId
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 名
     */
    private String firstName;

    /**
     * 姓
     */
    private String lastName;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 联系邮箱
     */
    private String email;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 国家
     */
    private String country;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 州
     */
    private String state;

    /**
     * 州名称
     */
    private String stateName;

    /**
     * 城市
     */
    private String city;

    /**
     * 地址行1
     */
    private String addressLine1;

    /**
     * 地址行2（公寓、套房、单元等）
     */
    private String addressLine2;

    /**
     * 邮政编码
     */
    private String zipCode;

    /**
     * 送货说明
     */
    private String deliveryNotes;

    /**
     * 是否默认地址（N否 Y是）
     */
    private String isDefault;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 乐观锁版本号
     */
    @Version
    private Integer version;
}
