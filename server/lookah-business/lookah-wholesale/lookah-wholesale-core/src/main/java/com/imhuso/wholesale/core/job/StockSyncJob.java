package com.imhuso.wholesale.core.job;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import com.aizuda.snailjob.common.log.SnailJobLog;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.service.IWhsStockSyncService;
import com.imhuso.wholesale.core.domain.vo.warehouse.StockSyncSummary;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 库存同步定时任务
 * <p>
 * 定期同步所有库存（海外仓 + ERP）
 * 配置为每天凌晨2点执行一次
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@SuppressWarnings("unused") // SnailJob框架调用，IDE检测不到使用
public class StockSyncJob {

    private final IWhsStockSyncService stockSyncService;

    /**
     * 执行完整库存同步任务（海外仓 + ERP）
     *
     * @param jobArgs 任务参数
     * @return 执行结果
     */
    @JobExecutor(name = "syncAllStocks")
    public ExecuteResult syncAllStocks(JobArgs jobArgs) {
        SnailJobLog.LOCAL.info("开始执行完整库存同步任务（海外仓 + ERP）");
        try {
            StockSyncSummary summary = stockSyncService.syncAllStocks();
            String message = String.format(
                "完整库存同步任务执行完成，海外仓成功: %d, ERP成功: %d, 总耗时: %dms",
                summary.getWarehouseSuccessCount(),
                summary.getErpSuccessCount(),
                summary.getTotalDuration()
            );
            SnailJobLog.LOCAL.info(message);

            if (summary.isAllSuccess()) {
                return ExecuteResult.success(message);
            } else {
                String warningMessage = message + String.format(
                    ", 失败情况: 海外仓失败: %d, ERP失败: %d",
                    summary.getWarehouseFailureCount(),
                    summary.getErpFailureCount()
                );
                return ExecuteResult.success(warningMessage); // 部分成功也算成功
            }
        } catch (Exception e) {
            String errorMessage = "完整库存同步任务执行失败: " +
                StringUtils.defaultIfEmpty(e.getMessage(), e.getClass().getName());
            SnailJobLog.LOCAL.error(errorMessage, e);
            return ExecuteResult.failure(errorMessage);
        }
    }


}
