package com.imhuso.wholesale.core.mapper;

import com.imhuso.common.mybatis.core.mapper.BaseMapperPlus;
import com.imhuso.wholesale.core.domain.WhsOverseasWarehouseAccount;
import com.imhuso.wholesale.core.domain.vo.admin.OverseasWarehouseAccountVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 海外仓账号Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface WhsOverseasWarehouseAccountMapper extends BaseMapperPlus<WhsOverseasWarehouseAccount, OverseasWarehouseAccountVo> {

    /**
     * 根据仓库ID查询海外仓账户配置
     *
     * @param warehouseId 仓库ID
     * @return 海外仓账户配置，如果未找到返回null
     */
    WhsOverseasWarehouseAccount selectByWarehouseId(@Param("warehouseId") Long warehouseId);
}
