package com.imhuso.wholesale.core.strategy.shipmentPlan;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-04-22
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class WhsShipmentPlanStrategyFactory {

    /**
     * 所有策略的列表
     */
    private final List<IWhsShipmentPlanStrategy> strategies;

    /**
     * 获取所有启用的方案策略
     */
    public List<IWhsShipmentPlanStrategy> getAllEnabledStrategies() {
        // 记录所有策略及其状态，用于调试
        if (log.isDebugEnabled()) {
            for (IWhsShipmentPlanStrategy strategy : strategies) {
                log.debug("发货方案策略: 类型=[{}], 名称=[{}], 启用状态=[{}]",
                    strategy.getStrategyType(), strategy.getStrategyName(), strategy.isEnabled());
            }
        }

        if (strategies.isEmpty()) {
            log.warn("没有找到任何发货方案策略实现类，请检查配置");
            return strategies;
        }

        List<IWhsShipmentPlanStrategy> enabledStrategies = strategies.stream()
                .filter(IWhsShipmentPlanStrategy::isEnabled)
                .collect(Collectors.toList());

        log.info("获取启用的策略: 总策略数={}, 启用策略数={}", strategies.size(), enabledStrategies.size());
        return enabledStrategies;
    }
}
