package com.imhuso.wholesale.core.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.constant.WhsCacheConstants;
import com.imhuso.wholesale.core.domain.WhsOrderItem;
import com.imhuso.wholesale.core.domain.bo.front.OrderItemListBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderItemVo;
import com.imhuso.wholesale.core.mapper.WhsOrderItemMapper;
import com.imhuso.wholesale.core.service.IWhsOrderItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 批发订单项服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsOrderItemServiceImpl implements IWhsOrderItemService {

    private final WhsOrderItemMapper baseMapper;
    private final WhsOrderItemMapper itemMapper;

    /**
     * 获取订单项 - 添加缓存，订单创建后基本不变
     */
    @Cacheable(cacheNames = WhsCacheConstants.WHS_ORDER_ITEMS, key = "#orderId")
    @Override
    public List<WhsOrderItem> getOrderItems(Long orderId) {
        OrderItemListBo itemBo = new OrderItemListBo();
        itemBo.setId(orderId);
        return itemMapper.selectList(buildItemQueryWrapper(itemBo));
    }

    /**
     * 获取订单项并填充详细信息（一次性查询所有需要的数据）
     */
    @Override
    public List<WhsOrderItemVo> getOrderItemsWithDetails(Long orderId) {
        List<WhsOrderItem> items = baseMapper.selectOrderItemsWithDetails(orderId);
        return items.stream()
            .map(item -> {
                WhsOrderItemVo vo = MapstructUtils.convert(item, WhsOrderItemVo.class);
                // 解析规格快照为可读文本
                if (StringUtils.isNotBlank(item.getSpecsSnapshot())) {
                    vo.setSpecsSnapshotText(parseSpecsSnapshotToText(item.getSpecsSnapshot()));
                }
                return vo;
            })
            .collect(Collectors.toList());
    }


    /**
     * 计算订单项的总PCS数量
     * 考虑包装数量，例如：一箱=24个单品，一展示盒=6个单品
     */
    @Override
    public int calculateTotalPcs(List<WhsOrderItem> items) {
        if (items == null || items.isEmpty()) {
            return 0;
        }

        return items.stream().mapToInt(item -> {
            int packagingQuantity = item.getPackagingQuantity() != null ? item.getPackagingQuantity() : 1;
            return item.getQuantity() * packagingQuantity;
        }).sum();
    }


    /**
     * 解析规格快照为可读文本
     */
    @Override
    public String parseSpecsSnapshotToText(String specsSnapshot) {
        if (StringUtils.isEmpty(specsSnapshot) || "{}".equals(specsSnapshot)) {
            return "";
        }

        try {
            JSONObject specs = JSONUtil.parseObj(specsSnapshot);
            if (!specs.isEmpty()) {
                StringJoiner joiner = new StringJoiner(" - ");
                specs.forEach((key, value) -> {
                    if (value != null && StringUtils.isNotBlank(value.toString())) {
                        joiner.add(value.toString());
                    }
                });

                String specsText = joiner.toString();
                return StringUtils.isNotEmpty(specsText) ? specsText : "";
            }
            return "";
        } catch (Exception e) {
            log.warn("解析规格JSON失败：{}, 错误: {}", specsSnapshot, e.getMessage());
            return "";
        }
    }

    /**
     * 根据订单ID删除订单项 - 清除相关缓存
     */
    @CacheEvict(cacheNames = WhsCacheConstants.WHS_ORDER_ITEMS, key = "#orderId")
    @Override
    public void deleteByOrderId(Long orderId) {
        if (orderId == null) {
            return;
        }
        LambdaQueryWrapper<WhsOrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhsOrderItem::getOrderId, orderId);
        itemMapper.delete(queryWrapper);
    }

    /**
     * 构建订单项查询条件
     * 【前台】
     *
     * @param bo 查询参数
     * @return 查询条件
     */
    private LambdaQueryWrapper<WhsOrderItem> buildItemQueryWrapper(OrderItemListBo bo) {
        LambdaQueryWrapper<WhsOrderItem> wrapper = new LambdaQueryWrapper<>();

        if (bo == null) {
            return wrapper;
        }

        // 按订单ID查询 - 使用id作为订单ID
        if (bo.getId() != null) {
            wrapper.eq(WhsOrderItem::getOrderId, bo.getId());
        }

        // 按商品ID查询
        if (bo.getProductId() != null) {
            wrapper.eq(WhsOrderItem::getProductId, bo.getProductId());
        }

        return wrapper;
    }

    /**
     * 根据ID获取订单项
     *
     * @param id 订单项ID
     * @return 订单项
     */
    @Override
    public WhsOrderItem getById(Long id) {
        if (id == null) {
            return null;
        }
        return itemMapper.selectById(id);
    }

    /**
     * 更新订单项 - 清除相关缓存
     *
     * @param orderItem 订单项
     * @return 操作结果
     */
    @CacheEvict(cacheNames = WhsCacheConstants.WHS_ORDER_ITEMS,
        key = "#orderItem != null ? #orderItem.orderId : 'null'",
        condition = "#orderItem != null and #orderItem.orderId != null")
    @Override
    public boolean updateById(WhsOrderItem orderItem) {
        if (orderItem == null || orderItem.getId() == null) {
            return false;
        }
        int rows = itemMapper.updateById(orderItem);
        return rows > 0;
    }

    /**
     * 根据ID列表批量获取订单项
     *
     * @param ids ID列表
     * @return 订单项列表
     */
    @Override
    public List<WhsOrderItem> listByIds(Set<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return Collections.emptyList();
        }
        return itemMapper.selectByIds(ids);
    }

    /**
     * 批量获取多个订单的订单项
     * 避免N+1查询问题
     *
     * @param orderIds 订单ID列表
     * @return 订单ID -> 订单项列表的映射
     */
    @Override
    public Map<Long, List<WhsOrderItem>> batchGetOrderItemsByOrderIds(List<Long> orderIds) {
        if (orderIds == null || orderIds.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            // 使用IN查询批量获取所有订单项
            LambdaQueryWrapper<WhsOrderItem> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(WhsOrderItem::getOrderId, orderIds)
                .orderByAsc(WhsOrderItem::getOrderId, WhsOrderItem::getId); // 添加排序确保结果一致性
            List<WhsOrderItem> allItems = itemMapper.selectList(wrapper);

            // 按订单ID分组
            return allItems.stream()
                .collect(Collectors.groupingBy(WhsOrderItem::getOrderId));
        } catch (Exception e) {
            log.error("批量获取订单项时发生错误: {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    /**
     * 生成批量缓存的键
     * 用于缓存注解中的SpEL表达式
     * <p>
     * 注意：此方法通过SpEL表达式在@Cacheable注解中调用，IDE可能显示为未使用
     *
     * @param orderIds 订单ID列表
     * @return 缓存键
     */
    @SuppressWarnings("unused") // 通过SpEL表达式在缓存注解中使用
    public String generateBatchCacheKey(List<Long> orderIds) {
        if (orderIds == null || orderIds.isEmpty()) {
            return "empty";
        }
        // 对ID列表排序，确保相同的ID集合生成相同的键
        List<Long> sortedIds = new ArrayList<>(orderIds);
        Collections.sort(sortedIds);
        return sortedIds.toString();
    }

    /**
     * 重置订单项的销售单价
     *
     * @param orderId 订单ID
     */
    @Override
    @CacheEvict(cacheNames = WhsCacheConstants.WHS_ORDER_ITEMS, key = "#orderId")
    public void resetSalesPrice(Long orderId) {
        log.info("重置订单项销售单价: orderId={}", orderId);

        // 直接查询数据库，避免缓存自调用问题
        LambdaQueryWrapper<WhsOrderItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WhsOrderItem::getOrderId, orderId);
        List<WhsOrderItem> orderItems = itemMapper.selectList(wrapper);
        
        if (orderItems != null && !orderItems.isEmpty()) {
            int updatedCount = 0;
            for (WhsOrderItem item : orderItems) {
                if (item.getSalesPrice() != null) {
                    item.setSalesPrice(null);
                    baseMapper.updateById(item);
                    updatedCount++;
                }
            }
            if (updatedCount > 0) {
                log.info("已重置订单项销售单价: orderId={}, updatedCount={}", orderId, updatedCount);
            }
        }
    }

}
