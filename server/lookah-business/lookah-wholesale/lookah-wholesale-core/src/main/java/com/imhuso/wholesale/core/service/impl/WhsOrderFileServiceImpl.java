package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.imhuso.common.core.utils.MapstructUtils;

import com.imhuso.wholesale.core.domain.WhsOrderFile;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderFileCreateBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderFileVo;
import com.imhuso.wholesale.core.mapper.WhsOrderFileMapper;
import com.imhuso.wholesale.core.service.IWhsOrderFileService;
import com.imhuso.wholesale.enums.OrderFileType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 批发订单文件关联服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsOrderFileServiceImpl implements IWhsOrderFileService {

    private final WhsOrderFileMapper baseMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addOrderFile(Long orderId, WhsOrderFileCreateBo fileBo) {
        log.info("添加订单文件关联: orderId={}, ossId={}, fileType={}", orderId, fileBo.getOssId(), fileBo.getFileType());

        // 验证文件类型
        if (!OrderFileType.isValidCode(fileBo.getFileType())) {
            log.error("无效的文件类型: {}", fileBo.getFileType());
            return false;
        }

        WhsOrderFile orderFile = MapstructUtils.convert(fileBo, WhsOrderFile.class);
        orderFile.setOrderId(orderId);

        int result = baseMapper.insert(orderFile);
        log.info("添加订单文件关联结果: orderId={}, ossId={}, result={}", orderId, fileBo.getOssId(), result > 0);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int addOrderFiles(Long orderId, List<WhsOrderFileCreateBo> files) {
        if (CollUtil.isEmpty(files)) {
            return 0;
        }

        log.info("批量添加订单文件关联: orderId={}, fileCount={}", orderId, files.size());

        // 批量数据验证和转换
        List<WhsOrderFile> orderFiles = new ArrayList<>();
        List<String> invalidFiles = new ArrayList<>();
        
        for (WhsOrderFileCreateBo fileBo : files) {
            // 验证文件类型
            if (!OrderFileType.isValidCode(fileBo.getFileType())) {
                log.warn("跳过无效的文件类型: fileType={}, ossId={}", fileBo.getFileType(), fileBo.getOssId());
                invalidFiles.add("ossId:" + fileBo.getOssId());
                continue;
            }

            WhsOrderFile orderFile = MapstructUtils.convert(fileBo, WhsOrderFile.class);
            orderFile.setOrderId(orderId);
            orderFiles.add(orderFile);
        }

        if (orderFiles.isEmpty()) {
            log.warn("没有有效的文件数据需要插入: orderId={}", orderId);
            return 0;
        }

        // 批量插入
        int successCount = 0;
        try {
            for (WhsOrderFile orderFile : orderFiles) {
                int result = baseMapper.insert(orderFile);
                if (result > 0) {
                    successCount++;
                }
            }
        } catch (Exception e) {
            log.error("批量插入订单文件关联失败: orderId={}", orderId, e);
            throw e; // 事务回滚
        }

        if (!invalidFiles.isEmpty()) {
            log.warn("批量添加订单文件关联完成，部分文件跳过: orderId={}, successCount={}, totalCount={}, invalidFiles={}",
                orderId, successCount, files.size(), invalidFiles);
        } else {
            log.info("批量添加订单文件关联完成: orderId={}, successCount={}/{}", orderId, successCount, files.size());
        }
        
        return successCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeOrderFile(Long orderId, Long fileId) {
        log.info("删除订单文件关联: orderId={}, fileId={}", orderId, fileId);

        LambdaUpdateWrapper<WhsOrderFile> wrapper = new LambdaUpdateWrapper<WhsOrderFile>()
            .eq(WhsOrderFile::getOrderId, orderId)
            .eq(WhsOrderFile::getId, fileId);

        int result = baseMapper.delete(wrapper);
        log.info("删除订单文件关联结果: orderId={}, fileId={}, result={}", orderId, fileId, result > 0);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeOrderFileByOssId(Long orderId, Long ossId) {
        log.info("根据OSS ID删除订单文件关联: orderId={}, ossId={}", orderId, ossId);

        LambdaUpdateWrapper<WhsOrderFile> wrapper = new LambdaUpdateWrapper<WhsOrderFile>()
            .eq(WhsOrderFile::getOrderId, orderId)
            .eq(WhsOrderFile::getOssId, ossId);

        int result = baseMapper.delete(wrapper);
        log.info("根据OSS ID删除订单文件关联结果: orderId={}, ossId={}, result={}", orderId, ossId, result > 0);
        return result > 0;
    }

    @Override
    public List<WhsOrderFileVo> getOrderFiles(Long orderId) {
        log.debug("获取订单所有文件: orderId={}", orderId);

        LambdaQueryWrapper<WhsOrderFile> wrapper = new LambdaQueryWrapper<WhsOrderFile>()
            .eq(WhsOrderFile::getOrderId, orderId)
            .orderByDesc(WhsOrderFile::getCreateTime);

        List<WhsOrderFileVo> files = baseMapper.selectVoList(wrapper);

        // 填充文件类型描述
        enrichFileTypeInfo(files);

        log.debug("获取订单所有文件完成: orderId={}, fileCount={}", orderId, files.size());
        return files;
    }

    @Override
    public List<WhsOrderFileVo> getOrderFilesByType(Long orderId, String fileType) {
        log.debug("获取订单指定类型文件: orderId={}, fileType={}", orderId, fileType);

        LambdaQueryWrapper<WhsOrderFile> wrapper = new LambdaQueryWrapper<WhsOrderFile>()
            .eq(WhsOrderFile::getOrderId, orderId)
            .eq(WhsOrderFile::getFileType, fileType)
            .orderByDesc(WhsOrderFile::getCreateTime);

        List<WhsOrderFileVo> files = baseMapper.selectVoList(wrapper);

        // 填充文件类型描述
        enrichFileTypeInfo(files);

        log.debug("获取订单指定类型文件完成: orderId={}, fileType={}, fileCount={}", orderId, fileType, files.size());
        return files;
    }

    @Override
    public List<WhsOrderFileVo> getOrderInvoiceFiles(Long orderId) {
        return getOrderFilesByType(orderId, OrderFileType.INVOICE.getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int clearOrderFiles(Long orderId) {
        log.info("清空订单所有文件关联: orderId={}", orderId);

        LambdaUpdateWrapper<WhsOrderFile> wrapper = new LambdaUpdateWrapper<WhsOrderFile>()
            .eq(WhsOrderFile::getOrderId, orderId);

        int result = baseMapper.delete(wrapper);
        log.info("清空订单所有文件关联完成: orderId={}, deletedCount={}", orderId, result);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int clearOrderFilesByType(Long orderId, String fileType) {
        log.info("清空订单指定类型文件关联: orderId={}, fileType={}", orderId, fileType);

        LambdaUpdateWrapper<WhsOrderFile> wrapper = new LambdaUpdateWrapper<WhsOrderFile>()
            .eq(WhsOrderFile::getOrderId, orderId)
            .eq(WhsOrderFile::getFileType, fileType);

        int result = baseMapper.delete(wrapper);
        log.info("清空订单指定类型文件关联完成: orderId={}, fileType={}, deletedCount={}", orderId, fileType, result);
        return result;
    }

    /**
     * 填充文件类型描述
     */
    private void enrichFileTypeInfo(List<WhsOrderFileVo> files) {
        if (CollUtil.isEmpty(files)) {
            return;
        }

        for (WhsOrderFileVo file : files) {
            // 填充文件类型描述
            OrderFileType fileType = OrderFileType.fromCode(file.getFileType());
            if (fileType != null) {
                file.setFileTypeText(fileType.getDescription());
            }
        }
    }
}
