package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 发货管理查询业务对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class WhsShipmentManagementBo extends BaseEntity {

    /**
     * 系统订单号
     */
    private String orderNo;

    /**
     * 客户订单号
     */
    private String customerOrderNo;

    /**
     * 收货人姓名
     */
    private String shippingName;

    /**
     * 收货国家
     */
    private String shippingCountry;

    /**
     * 销售代表ID
     */
    private Long salespersonId;

    /**
     * 发货状态（支持多选）
     * 默认查询：待发货(0)、部分发货(2)
     */
    private Integer[] shipmentStatus;

    /**
     * 是否只查询有库存不足的订单
     */
    private Boolean onlyInsufficientStock;

    /**
     * 仓库ID（用于筛选特定仓库的待发货订单）
     */
    private Long warehouseId;
}
