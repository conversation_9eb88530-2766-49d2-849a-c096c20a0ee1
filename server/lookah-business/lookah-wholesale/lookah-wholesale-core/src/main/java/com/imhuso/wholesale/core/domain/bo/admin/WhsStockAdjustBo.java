package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 库存调整业务对象
 *
 * <AUTHOR>
 */
@Data
public class WhsStockAdjustBo {

    /**
     * 库存ID
     */
    @NotNull(message = "库存ID不能为空", groups = {EditGroup.class})
    private Long stockId;

    /**
     * 调整数量（正数增加，负数减少）
     */
    @NotNull(message = "调整数量不能为空", groups = {EditGroup.class})
    private Integer quantity;
}
