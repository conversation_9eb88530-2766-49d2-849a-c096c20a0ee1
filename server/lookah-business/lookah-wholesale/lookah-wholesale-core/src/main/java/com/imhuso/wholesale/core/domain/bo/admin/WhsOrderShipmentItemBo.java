package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.wholesale.core.domain.WhsOrderShipmentItem;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.util.List;

/**
 * 订单发货项业务对象（按仓库汇总）
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsOrderShipmentItem.class)
public class WhsOrderShipmentItemBo {

    /**
     * 发货仓库ID
     */
    @NotNull(message = "发货仓库ID不能为空")
    private Long warehouseId;

    /**
     * 物流方式ID
     */
    @NotNull(message = "物流方式ID不能为空")
    private Long logisticsMethodId;

    /**
     * 包裹数量（pieceCount）
     * 代表昊通仓库中发货接口中所需要的 BillQty
     * 同时接口数据中有几个 pieceCount 就要构造几个 OrderBags 子对象
     */
    @NotNull(message = "包裹数量不能为空")
    @Positive(message = "包裹数量必须大于0")
    private Integer pieceCount;

    /**
     * 是否启用签名服务
     */
    private Boolean signatureService;

    /**
     * 发货方案项列表
     */
    @Valid
    @NotEmpty(message = "发货方案项列表不能为空")
    private List<WhsOrderShipmentPlanItemBo> planItems;
}
