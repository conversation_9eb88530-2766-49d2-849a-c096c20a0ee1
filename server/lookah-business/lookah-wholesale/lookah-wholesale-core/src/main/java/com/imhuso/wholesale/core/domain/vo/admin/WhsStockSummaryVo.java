package com.imhuso.wholesale.core.domain.vo.admin;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 库存汇总视图对象
 *
 * <AUTHOR>
 */
@Data
public class WhsStockSummaryVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 系列ID
     */
    private Long seriesId;

    /**
     * 系列名称
     */
    private String seriesName;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 变体数量
     */
    private Integer variantCount;

    /**
     * 总可用库存（所有变体在所有仓库的可用库存总和）
     */
    private Integer totalAvailableStock;

    /**
     * 总在途库存（所有变体的在途库存总和）
     */
    private Integer totalInTransitStock;

    /**
     * 变体库存详情列表
     */
    private List<VariantStockDetail> variantStockDetails;



    /**
     * 变体库存详情
     */
    @Data
    public static class VariantStockDetail implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 变体ID
         */
        private Long variantId;

        /**
         * SKU编码
         */
        private String skuCode;

        /**
         * 包装类型
         */
        private Integer packagingType;

        /**
         * 仓库库存列表
         */
        private List<WarehouseStock> warehouseStocks;

        /**
         * 总可用库存
         */
        private Integer totalAvailableStock;

        /**
         * 总在途库存
         */
        private Integer totalInTransitStock;

        /**
         * 成品库存数量
         */
        private Integer finishedStock;

        /**
         * 待产库存数量
         */
        private Integer pendingStock;
    }

    /**
     * 仓库库存信息
     */
    @Data
    public static class WarehouseStock implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 仓库ID
         */
        private Long warehouseId;

        /**
         * 仓库名称
         */
        private String warehouseName;

        /**
         * 可用库存
         */
        private Integer availableStock;

        /**
         * 在途库存
         */
        private Integer inTransitStock;
    }

    /**
     * 库存汇总响应数据
     */
    @Data
    public static class StockSummaryResponse implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 产品列表
         */
        private List<WhsStockSummaryVo> data;

        /**
         * 统计信息
         */
        private StockSummaryStatistics statistics;
    }

    /**
     * 库存汇总统计信息
     */
    @Data
    public static class StockSummaryStatistics implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 总变体数
         */
        private Integer total;

        /**
         * 有库存的变体数量
         */
        private Integer withStock;

        /**
         * 有在途库存的变体数量
         */
        private Integer withInTransit;

        /**
         * 总可用库存数量
         */
        private Integer totalAvailable;

        /**
         * 总在途库存数量
         */
        private Integer totalInTransit;
    }
}
