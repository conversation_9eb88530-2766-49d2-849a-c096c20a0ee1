package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.wholesale.core.domain.WhsProductAttributeValue;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品属性值业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@AutoMapper(target = WhsProductAttributeValue.class)
public class WhsProductAttributeValueBo extends BaseEntity {
    /**
     * 属性值ID
     */
    @NotNull(message = "属性值ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 属性ID
     */
    @NotNull(message = "属性ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long attributeId;

    /**
     * 属性值键
     */
    @NotBlank(message = "属性值键不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 50, message = "属性值键不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String valueKey;

    /**
     * 属性值显示名
     */
    @NotBlank(message = "属性值显示名不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 50, message = "属性值显示名不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String valueDisplay;

    /**
     * 显示顺序
     */
    private Integer sort;

    /**
     * 状态（0停用 1正常）
     */
    private String status;
}
