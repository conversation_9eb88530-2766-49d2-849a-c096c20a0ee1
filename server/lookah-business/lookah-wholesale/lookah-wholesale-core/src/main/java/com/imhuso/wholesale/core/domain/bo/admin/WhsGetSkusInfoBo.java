package com.imhuso.wholesale.core.domain.bo.admin;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * SKU信息获取业务对象
 *
 * <AUTHOR>
 */
@Data
public class WhsGetSkusInfoBo {

    /**
     * SKU编码列表
     */
    @NotEmpty(message = "SKU编码列表不能为空")
    @Size(min = 1, max = 500, message = "SKU编码列表数量必须在1到500之间")
    private List<String> skuCodes;
}
