package com.imhuso.wholesale.core.strategy.shipmentPlan;

import com.imhuso.wholesale.core.domain.bo.admin.WhsShipmentPlanGenerationBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanGenerationVo;

/**
 * 包装优化策略接口
 * 所有的包装优化策略都应该实现此接口
 *
 * <AUTHOR>
 */
public interface IWhsShipmentPlanStrategy {

    /**
     * 获取策略类型
     *
     * @return 策略类型代码
     */
    String getStrategyType();

    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    String getStrategyName();

    /**
     * 检查策略是否启用
     *
     * @return 是否启用
     */
    boolean isEnabled();

    /**
     * 执行优化策略，生成方案项
     *
     * @return WhsShipmentPlanGenerationBo 方案数据
     */
    WhsShipmentPlanGenerationBo generate(WhsShipmentPlanGenerationVo generationVo);
}
