package com.imhuso.wholesale.core.service.impl;

import com.aizuda.snailjob.client.core.annotation.Retryable;
import com.aizuda.snailjob.client.core.retryer.RetryType;
import com.imhuso.wholesale.core.callback.ShipmentCancellationRetryCallback;
import com.imhuso.wholesale.core.domain.vo.warehouse.WarehouseShipmentRecord;
import com.imhuso.wholesale.core.service.IOverseasShipmentCancellationService;
import com.imhuso.wholesale.core.service.IWhsOverseasWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 海外仓发货取消服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OverseasShipmentCancellationServiceImpl implements IOverseasShipmentCancellationService {

    private final IWhsOverseasWarehouseService overseasWarehouseService;

    /**
     * 尝试取消单个海外仓发货记录（带重试）
     *
     * @param record 需要取消的发货记录
     */
    @Override
    @Retryable(scene = "海外仓发货取消", retryStrategy = RetryType.LOCAL_REMOTE,   // 使用本地和远程重试策略
        localInterval = 5, retryCompleteCallback = ShipmentCancellationRetryCallback.class)
    public void attemptCancellation(WarehouseShipmentRecord record) {
        log.info("开始尝试取消海外仓发货 (带重试) - 仓库ID: {}, 包裹ID: {}, 外部单号: {}", record.getWarehouseId(), record.getPackageId(), record.getExternalShipmentId());

        try {
            // 调用实际的取消服务
            overseasWarehouseService.cancelOrder(record.getWarehouseId(), record.getPackageId(), record.getExternalShipmentId());
            log.info("海外仓发货取消请求发送成功 - 仓库ID: {}, 包裹ID: {}, 外部单号: {}", record.getWarehouseId(), record.getPackageId(), record.getExternalShipmentId());
        } catch (Exception e) {
            // @Retryable 会捕获异常并触发重试，这里记录一下初始失败日志
            log.error("海外仓发货取消尝试失败 (将触发重试) - 仓库ID: {}, 包裹ID: {}, 外部单号: {}, 错误: {}", record.getWarehouseId(), record.getPackageId(), record.getExternalShipmentId(), e.getMessage());
            // 重新抛出异常，以便 @Retryable 生效
            throw e;
        }
    }
}
