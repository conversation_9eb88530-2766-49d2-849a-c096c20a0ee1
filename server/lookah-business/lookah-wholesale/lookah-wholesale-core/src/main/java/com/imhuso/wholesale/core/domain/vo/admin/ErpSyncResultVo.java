package com.imhuso.wholesale.core.domain.vo.admin;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * ERP同步结果视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ErpSyncResultVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 同步是否成功
     */
    private Boolean success;

    /**
     * 成功同步的数量
     */
    private Integer successCount;

    /**
     * 失败的数量
     */
    private Integer failureCount;

    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 失败的SKU列表
     */
    private List<String> failedSkus;

    /**
     * ERP提供商名称
     */
    private String providerName;

    /**
     * 同步耗时（毫秒）
     */
    private Long duration;

    /**
     * 创建成功结果
     *
     * @param successCount 成功数量
     * @param totalCount 总数量
     * @param providerName 提供商名称
     * @param duration 耗时
     * @return 成功结果
     */
    public static ErpSyncResultVo success(Integer successCount, Integer totalCount, String providerName, Long duration) {
        ErpSyncResultVo result = new ErpSyncResultVo();
        result.setSuccess(true);
        result.setSuccessCount(successCount);
        result.setFailureCount(totalCount - successCount);
        result.setTotalCount(totalCount);
        result.setProviderName(providerName);
        result.setDuration(duration);
        return result;
    }

    /**
     * 创建失败结果
     *
     * @param errorMessage 错误消息
     * @param providerName 提供商名称
     * @return 失败结果
     */
    public static ErpSyncResultVo failure(String errorMessage, String providerName) {
        ErpSyncResultVo result = new ErpSyncResultVo();
        result.setSuccess(false);
        result.setSuccessCount(0);
        result.setFailureCount(0);
        result.setTotalCount(0);
        result.setErrorMessage(errorMessage);
        result.setProviderName(providerName);
        return result;
    }
}
