package com.imhuso.wholesale.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.adapter.IOverseasWarehouseProvider;
import com.imhuso.wholesale.core.adapter.OverseasWarehouseProviderFactory;
import com.imhuso.wholesale.core.domain.WhsInboundOrder;
import com.imhuso.wholesale.core.domain.WhsInboundOrderItem;
import com.imhuso.wholesale.core.domain.WhsShippingDispatchLog;
import com.imhuso.wholesale.core.domain.WhsWarehouse;
import com.imhuso.wholesale.core.domain.vo.warehouse.InboundOrderSyncResultVo;
import com.imhuso.wholesale.core.domain.vo.warehouse.InboundOrderVo;
import com.imhuso.wholesale.core.domain.vo.warehouse.WarehouseContextVo;
import com.imhuso.wholesale.core.enums.InboundOrderStatus;
import com.imhuso.wholesale.core.mapper.WhsInboundOrderItemMapper;
import com.imhuso.wholesale.core.mapper.WhsInboundOrderMapper;
import com.imhuso.wholesale.core.mapper.WhsShippingDispatchLogMapper;
import com.imhuso.wholesale.core.mapper.WhsWarehouseMapper;
import com.imhuso.wholesale.core.service.IWhsInTransitStockService;
import com.imhuso.wholesale.core.service.IWhsInboundOrderSyncService;
import com.imhuso.wholesale.core.service.IWhsOverseasWarehouseService;
import com.imhuso.wholesale.core.service.IWhsProductVariantService;
import com.imhuso.wholesale.core.utils.SkuMappingUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 入库单同步服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsInboundOrderSyncServiceImpl implements IWhsInboundOrderSyncService {

    private final WhsWarehouseMapper warehouseMapper;
    private final WhsInboundOrderMapper inboundOrderMapper;
    private final WhsInboundOrderItemMapper inboundOrderItemMapper;
    private final WhsShippingDispatchLogMapper dispatchLogMapper;
    private final IWhsOverseasWarehouseService overseasWarehouseService;
    private final OverseasWarehouseProviderFactory providerFactory;
    private final IWhsProductVariantService variantService;
    private final IWhsInTransitStockService inTransitStockService;

    /**
     * 同步所有海外仓的入库单
     *
     * @return 同步成功的仓库数量
     */
    @Override
    public int syncAllWarehouses() {
        log.info("开始同步所有海外仓入库单");
        long startTime = System.currentTimeMillis();

        // 查询所有启用的海外仓库
        List<WhsWarehouse> warehouses = getActiveOverseasWarehouses();
        if (warehouses.isEmpty()) {
            log.warn("没有找到可用的海外仓库，同步终止");
            return 0;
        }

        log.info("找到{}个可用海外仓库准备同步入库单", warehouses.size());

        // 创建线程池
        int threadCount = calculateOptimalThreadCount(warehouses.size());
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        try {
            // 并行同步每个仓库
            List<CompletableFuture<Boolean>> futures = createSyncTasks(warehouses, executor);

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            // 统计成功数量
            int successCount = countSuccessfulTasks(futures);

            long duration = System.currentTimeMillis() - startTime;
            log.info("所有仓库入库单同步完成，耗时{}ms，成功{}个，失败{}个",
                duration, successCount, warehouses.size() - successCount);

            return successCount;
        } finally {
            executor.shutdown();
        }
    }

    @Override
    public int syncAllWarehousesFull() {
        log.info("开始全量同步所有海外仓库的入库单（查询最近一年数据）");
        long startTime = System.currentTimeMillis();

        // 获取所有启用的海外仓库
        List<WhsWarehouse> warehouses = getActiveOverseasWarehouses();
        if (warehouses.isEmpty()) {
            log.warn("没有找到启用的海外仓库");
            return 0;
        }

        log.info("找到{}个可用海外仓库准备全量同步入库单", warehouses.size());

        // 创建线程池
        int threadCount = calculateOptimalThreadCount(warehouses.size());
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        log.info("创建线程池，线程数: {}", threadCount);

        try {
            // 并行全量同步每个仓库
            List<CompletableFuture<Boolean>> futures = createSyncTasksFull(warehouses, executor);

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            // 统计成功数量
            int successCount = countSuccessfulTasks(futures);

            long duration = System.currentTimeMillis() - startTime;
            log.info("所有仓库入库单全量同步完成，耗时{}ms，成功{}个，失败{}个",
                duration, successCount, warehouses.size() - successCount);

            return successCount;
        } finally {
            executor.shutdown();
        }
    }

    /**
     * 获取所有启用的海外仓库
     *
     * @return 海外仓库列表
     */
    private List<WhsWarehouse> getActiveOverseasWarehouses() {
        LambdaQueryWrapper<WhsWarehouse> query = new LambdaQueryWrapper<>();
        query.eq(WhsWarehouse::getIsOverseas, BusinessConstants.YES)
            .eq(WhsWarehouse::getStatus, BusinessConstants.NORMAL);
        return warehouseMapper.selectList(query);
    }

    /**
     * 计算最优线程数
     *
     * @param warehouseCount 仓库数量
     * @return 线程数
     */
    private int calculateOptimalThreadCount(int warehouseCount) {
        return Math.min(Runtime.getRuntime().availableProcessors(), Math.min(5, warehouseCount));
    }

    /**
     * 创建同步任务
     *
     * @param warehouses 仓库列表
     * @param executor   线程池
     * @return 任务列表
     */
    private List<CompletableFuture<Boolean>> createSyncTasks(List<WhsWarehouse> warehouses, ExecutorService executor) {
        return warehouses.stream()
            .map(warehouse -> CompletableFuture.supplyAsync(() -> {
                try {
                    log.info("开始同步仓库[{}]的入库单", warehouse.getName());
                    InboundOrderSyncResultVo result = doSyncWarehouseInboundOrders(warehouse.getId(), 60);
                    log.info("仓库[{}]入库单同步完成，结果: {}", warehouse.getName(),
                        result.isSuccess() ? "成功" : "失败: " + result.getErrorMessage());
                    return result.isSuccess();
                } catch (Exception e) {
                    log.error("仓库[{}]入库单同步出错", warehouse.getName(), e);
                    return false;
                }
            }, executor)).toList();
    }

    /**
     * 创建全量同步任务
     *
     * @param warehouses 仓库列表
     * @param executor   线程池
     * @return 任务列表
     */
    private List<CompletableFuture<Boolean>> createSyncTasksFull(List<WhsWarehouse> warehouses, ExecutorService executor) {
        return warehouses.stream()
            .map(warehouse -> CompletableFuture.supplyAsync(() -> {
                try {
                    log.info("开始全量同步仓库[{}]的入库单（查询最近一年数据）", warehouse.getName());
                    InboundOrderSyncResultVo result = doSyncWarehouseInboundOrders(warehouse.getId(), 365);
                    log.info("仓库[{}]入库单全量同步完成，结果: {}", warehouse.getName(),
                        result.isSuccess() ? "成功" : "失败: " + result.getErrorMessage());
                    return result.isSuccess();
                } catch (Exception e) {
                    log.error("仓库[{}]入库单全量同步出错", warehouse.getName(), e);
                    return false;
                }
            }, executor)).toList();
    }

    /**
     * 统计成功任务数量
     *
     * @param futures 任务列表
     * @return 成功数量
     */
    private int countSuccessfulTasks(List<CompletableFuture<Boolean>> futures) {
        return (int) futures.stream()
            .map(CompletableFuture::join)
            .filter(success -> success)
            .count();
    }

    /**
     * 同步指定仓库的入库单
     *
     * @param warehouseId 仓库ID
     * @return 同步结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public InboundOrderSyncResultVo syncWarehouseInboundOrders(Long warehouseId) {
        // 默认查询60天的数据（定时任务使用，约2个月）
        return doSyncWarehouseInboundOrders(warehouseId, 60);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InboundOrderSyncResultVo syncWarehouseInboundOrders(Long warehouseId, Integer daysBefore) {
        return doSyncWarehouseInboundOrders(warehouseId, daysBefore);
    }

    /**
     * 执行仓库入库单同步的内部方法
     *
     * @param warehouseId 仓库ID
     * @param daysBefore 查询天数
     * @return 同步结果
     */
    private InboundOrderSyncResultVo doSyncWarehouseInboundOrders(Long warehouseId, Integer daysBefore) {
        if (warehouseId == null) {
            throw new ServiceException("仓库ID不能为空");
        }

        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String warehouseName = "未知仓库";

        try {
            // 创建仓库上下文
            WarehouseContextVo context = overseasWarehouseService.createWarehouseContext(warehouseId);
            warehouseName = context.getWarehouse().getName();

            // 设置查询天数参数
            if (daysBefore != null && daysBefore > 0) {
                context.setCustomData("daysBefore", daysBefore);
                log.info("开始同步仓库[{}]的入库单，查询最近{}天的数据", warehouseName, daysBefore);
            } else {
                log.info("开始同步仓库[{}]的入库单", warehouseName);
            }

            // 获取提供商实现
            IOverseasWarehouseProvider provider = providerFactory.getProvider(context.getProvider().getProviderType());

            // 调用海外仓API同步入库单
            InboundOrderSyncResultVo result = provider.syncInboundOrders(context);

            // 记录同步操作日志
            recordSyncOperationLog(context, result);

            // 处理同步结果
            if (result.isSuccess() && result.getRecords() != null && !result.getRecords().isEmpty()) {
                // 直接在当前事务中处理数据，不再调用带有@Transactional注解的方法
                doProcessInboundOrderSyncResult(warehouseId, result);
            }

            // 记录日志
            long duration = System.currentTimeMillis() - startTime;
            log.info("仓库[{}]入库单同步完成，耗时{}ms，处理{}个入库单，成功{}个，失败{}个，跳过{}个",
                warehouseName, duration, result.getProcessedCount(),
                result.getSuccessCount(), result.getFailedCount(), result.getSkippedCount());

            return result;
        } catch (ServiceException e) {
            // 直接抛出业务异常
            log.error("同步仓库[{}]入库单失败: {}", warehouseName, e.getMessage());
            throw e;
        } catch (Exception e) {
            // 其他异常包装为业务异常
            log.error("同步仓库[{}]入库单发生异常", warehouseName, e);

            // 创建失败结果
            InboundOrderSyncResultVo result = new InboundOrderSyncResultVo();
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());

            throw new ServiceException("同步入库单失败: " + e.getMessage());
        }
    }

    /**
     * 处理入库单同步结果
     * <p>
     * 注意：此方法在带有@Transactional注解的方法中调用，不需要单独添加事务注解
     *
     * @param warehouseId 仓库ID
     * @param result      同步结果
     */
    private void doProcessInboundOrderSyncResult(Long warehouseId, InboundOrderSyncResultVo result) {
        if (result == null || !result.isSuccess() || result.getRecords() == null
            || result.getRecords().isEmpty()) {
            log.warn("同步结果为空或同步失败，不处理入库单更新");
            return;
        }

        log.info("开始处理{}个入库单数据", result.getRecords().size());

        // 查询此仓库下已存在的入库单号列表，用于判断新增或更新
        Map<String, WhsInboundOrder> existingOrderMap = getExistingInboundOrders(warehouseId);
        log.info("仓库[{}]已存在{}个入库单记录", warehouseId, existingOrderMap.size());

        List<WhsInboundOrder> ordersToInsert = new ArrayList<>();
        List<WhsInboundOrder> ordersToUpdate = new ArrayList<>();
        List<WhsInboundOrderItem> itemsToInsert = new ArrayList<>();

        // 处理每个入库单
        for (InboundOrderVo orderData : result.getRecords()) {
            try {
                // 提取基本信息
                String externalOrderNo = orderData.getAsnNo();
                if (StringUtils.isEmpty(externalOrderNo)) {
                    log.warn("入库单号为空，跳过处理");
                    continue;
                }

                // 构建入库单对象
                WhsInboundOrder inboundOrder = buildInboundOrderFromData(orderData, warehouseId);

                // 判断是新增还是更新
                WhsInboundOrder existingOrder = existingOrderMap.get(externalOrderNo);
                if (existingOrder != null) {
                    // 更新已存在的入库单
                    inboundOrder.setId(existingOrder.getId());
                    // 保留原有的创建信息
                    inboundOrder.setCreateBy(existingOrder.getCreateBy());
                    inboundOrder.setCreateTime(existingOrder.getCreateTime());
                    inboundOrder.setCreateDept(existingOrder.getCreateDept());

                    ordersToUpdate.add(inboundOrder);
                    log.debug("更新入库单: {}, ID: {}", externalOrderNo, existingOrder.getId());

                    // 处理入库单明细
                    handleOrderItems(orderData, existingOrder.getId(), itemsToInsert);
                } else {
                    // 新增入库单
                    inboundOrder.setId(cn.hutool.core.util.IdUtil.getSnowflakeNextId());
                    ordersToInsert.add(inboundOrder);
                    log.debug("新增入库单: {}, ID: {}", externalOrderNo, inboundOrder.getId());

                    // 处理入库单明细
                    handleOrderItems(orderData, inboundOrder.getId(), itemsToInsert);
                }
            } catch (Exception e) {
                log.error("处理入库单数据异常: {}", e.getMessage(), e);
            }
        }

        // 执行数据库操作
        if (!ordersToInsert.isEmpty() || !ordersToUpdate.isEmpty() || !itemsToInsert.isEmpty()) {
            log.info("准备保存数据: 新增入库单{}个, 更新入库单{}个, 新增明细{}个",
                ordersToInsert.size(), ordersToUpdate.size(), itemsToInsert.size());
            saveInboundOrderData(ordersToInsert, ordersToUpdate, itemsToInsert);
        } else {
            log.info("没有需要保存的数据");
        }
    }

    /**
     * 获取仓库已存在的入库单映射
     *
     * @param warehouseId 仓库ID
     * @return 入库单号到入库单对象的映射
     */
    private Map<String, WhsInboundOrder> getExistingInboundOrders(Long warehouseId) {
        LambdaQueryWrapper<WhsInboundOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhsInboundOrder::getWarehouseId, warehouseId);
        List<WhsInboundOrder> existingOrders = inboundOrderMapper.selectList(queryWrapper);

        return existingOrders.stream()
            .collect(Collectors.toMap(
                WhsInboundOrder::getExternalOrderNo,
                order -> order,
                (existing, replacement) -> existing // 如果有重复，保留第一个
            ));
    }

    /**
     * 从入库单视图对象构建入库单实体对象
     *
     * @param orderData   入库单视图对象
     * @param warehouseId 仓库ID
     * @return 入库单实体对象
     */
    private WhsInboundOrder buildInboundOrderFromData(InboundOrderVo orderData, Long warehouseId) {
        WhsInboundOrder inboundOrder = new WhsInboundOrder();
        inboundOrder.setWarehouseId(warehouseId);
        inboundOrder.setExternalOrderNo(orderData.getAsnNo());

        // 设置我方订单号（从物流信息中获取）
        if (orderData.getPurchaseLogistics() != null &&
            StringUtils.isNotEmpty(orderData.getPurchaseLogistics().getDeliveryNo())) {
            String deliveryNo = orderData.getPurchaseLogistics().getDeliveryNo();
            // 如果是昊通，以"ASN_PurchaseLogistics"下的"DeliveryNo"作为order_no
            log.debug("从物流信息中获取到订单号: {}", deliveryNo);
            inboundOrder.setOrderNo(deliveryNo);

        } else {
            log.warn("入库单[{}]没有物流信息或订单号为空", orderData.getAsnNo());
        }

        // 设置状态信息
        if (orderData.getStatus() != null) {
            // 设置海外仓状态编码和文本
            inboundOrder.setStatusCode(String.valueOf(orderData.getStatus()));
            inboundOrder.setStatusText(orderData.getStatusName());

            // 优先使用状态枚举
            if (orderData.getStatusEnum() != null) {
                // 直接使用枚举的值作为数字状态
                InboundOrderStatus statusEnum = orderData.getStatusEnum();
                inboundOrder.setStatus(statusEnum.getValue());
                log.debug("入库单[{}]状态枚举[{}]对应的数字状态为[{}]", orderData.getAsnNo(), statusEnum.name(), statusEnum.getValue());
            } else {
                // 查找对应的枚举
                InboundOrderStatus statusEnum = InboundOrderStatus.getByValue(orderData.getStatus());
                if (statusEnum != null) {
                    inboundOrder.setStatus(statusEnum.getValue());
                    log.debug("入库单[{}]状态值[{}]映射为系统状态枚举[{}]", orderData.getAsnNo(), orderData.getStatus(), statusEnum.name());
                } else {
                    // 直接使用海外仓状态数值
                    inboundOrder.setStatus(orderData.getStatus());
                    log.debug("入库单[{}]使用海外仓状态数值[{}]", orderData.getAsnNo(), orderData.getStatus());
                }
            }
        } else {
            // 默认为待入库
            inboundOrder.setStatus(InboundOrderStatus.PENDING_RECEIPT.getValue());
            inboundOrder.setStatusCode("0");
            inboundOrder.setStatusText("待入库");
            log.debug("入库单[{}]状态为空，使用默认状态[待入库]", orderData.getAsnNo());
        }

        inboundOrder.setExpectedArrival(orderData.getEstimatedTime());
        inboundOrder.setRemark(orderData.getRemark());

        // 保存外部数据为JSON
        inboundOrder.setExternalData(cn.hutool.json.JSONUtil.toJsonStr(orderData));
        inboundOrder.setLastSyncTime(new Date());

        return inboundOrder;
    }

    /**
     * 处理入库单明细
     *
     * @param orderData      入库单视图对象
     * @param inboundOrderId 入库单ID
     * @param itemsToInsert  待插入的明细列表
     */
    private void handleOrderItems(InboundOrderVo orderData, Long inboundOrderId, List<WhsInboundOrderItem> itemsToInsert) {
        if (orderData.getPurchaseDetails() == null || orderData.getPurchaseDetails().isEmpty()) {
            log.debug("入库单[{}]没有明细数据", orderData.getAsnNo());
            return;
        }

        log.debug("处理入库单[{}]明细，共{}条", orderData.getAsnNo(), orderData.getPurchaseDetails().size());

        // 收集所有SKU编码，用于批量查询变体ID
        List<String> skuCodes = orderData.getPurchaseDetails().stream()
            .map(InboundOrderVo.InboundOrderItemVo::getSku)
            .filter(StringUtils::isNotEmpty)
            .distinct()
            .toList();

        log.debug("入库单[{}]包含{}个不同的SKU", orderData.getAsnNo(), skuCodes.size());

        // 批量查询变体ID
        Map<String, Long> variantIdMap = batchFindVariantIdsBySkuCodes(skuCodes);
        log.debug("成功查询到{}个SKU对应的变体ID", variantIdMap.size());

        // 如果是更新操作，先删除已有明细
        try {
            int deletedCount = inboundOrderItemMapper.delete(
                Wrappers.lambdaQuery(WhsInboundOrderItem.class)
                    .eq(WhsInboundOrderItem::getInboundOrderId, inboundOrderId)
            );
            log.debug("删除入库单[{}]已有明细{}条", orderData.getAsnNo(), deletedCount);
        } catch (Exception e) {
            log.error("删除入库单[{}]已有明细异常: {}", orderData.getAsnNo(), e.getMessage(), e);
        }

        // 添加新明细
        int successCount = 0;
        for (InboundOrderVo.InboundOrderItemVo itemData : orderData.getPurchaseDetails()) {
            String skuCode = itemData.getSku();
            if (StringUtils.isEmpty(skuCode)) {
                log.warn("入库单[{}]明细SKU为空，跳过处理", orderData.getAsnNo());
                continue;
            }

            try {
                // 构建明细对象
                WhsInboundOrderItem item = new WhsInboundOrderItem();
                item.setId(cn.hutool.core.util.IdUtil.getSnowflakeNextId());
                item.setInboundOrderId(inboundOrderId);
                item.setSkuCode(skuCode);

                // 从映射中获取变体ID - 注意SKU大小写不敏感
                Long variantId = findVariantIdIgnoreCase(variantIdMap, skuCode);

                item.setVariantId(variantId != null ? variantId : 0L);

                if (variantId == null || variantId == 0L) {
                    log.warn("找不到SKU[{}]对应的变体ID，使用默认值0", skuCode);
                    // 如果找不到变体ID，尝试再次查询
                    try {
                        Map<String, Long> result = variantService.getVariantIdsBySkuCodes(Collections.singletonList(skuCode));
                        if (result != null && !result.isEmpty()) {
                            Long foundId = findVariantIdIgnoreCase(result, skuCode);
                            if (foundId != null && foundId > 0) {
                                item.setVariantId(foundId);
                                log.info("二次查询成功找到SKU[{}]对应的变体ID: {}", skuCode, foundId);
                            }
                        }
                    } catch (Exception e) {
                        log.error("二次查询SKU[{}]对应的变体ID时发生异常: {}", skuCode, e.getMessage());
                    }
                }

                // 设置数量和已接收数量
                Integer quantity = itemData.getQuantity() != null ? itemData.getQuantity() : 0;
                Integer uploadQuantity = itemData.getUploadQuantity() != null ? itemData.getUploadQuantity() : 0;

                item.setQuantity(quantity);
                item.setReceivedQuantity(uploadQuantity);

                // 添加到待插入列表
                itemsToInsert.add(item);
                successCount++;

                log.debug("添加入库单[{}]明细: SKU={}, 变体ID={}, 预约数量={}, 上架数量={}",
                    orderData.getAsnNo(), skuCode, item.getVariantId(), quantity, uploadQuantity);
            } catch (Exception e) {
                log.error("处理入库单[{}]明细异常, SKU: {}, 错误: {}", orderData.getAsnNo(), skuCode, e.getMessage(), e);
                // 继续处理下一个明细，不中断整个流程
            }
        }

        log.debug("入库单[{}]明细处理完成，成功添加{}条", orderData.getAsnNo(), successCount);
    }

    /**
     * 从映射中查找变体ID，忽略SKU大小写
     *
     * @param variantIdMap SKU到变体ID的映射
     * @param skuCode      SKU编码
     * @return 变体ID，如果找不到则返回null
     */
    private Long findVariantIdIgnoreCase(Map<String, Long> variantIdMap, String skuCode) {
        if (StringUtils.isEmpty(skuCode)) {
            return null;
        }
        return SkuMappingUtils.findVariantIdBySku(variantIdMap, skuCode);
    }

    /**
     * 保存入库单数据到数据库
     *
     * @param ordersToInsert 待插入的入库单列表
     * @param ordersToUpdate 待更新的入库单列表
     * @param itemsToInsert  待插入的明细列表
     */
    private void saveInboundOrderData(List<WhsInboundOrder> ordersToInsert, List<WhsInboundOrder> ordersToUpdate,
                                      List<WhsInboundOrderItem> itemsToInsert) {
        int insertSuccess = 0;
        int updateSuccess = 0;
        int itemInsertSuccess = 0;
        long startTime = System.currentTimeMillis();

        // 批量插入新增入库单
        if (!ordersToInsert.isEmpty()) {
            log.info("开始新增{}个入库单", ordersToInsert.size());
            try {
                // 使用分批处理，每批50条
                int batchSize = 50;
                for (int i = 0; i < ordersToInsert.size(); i += batchSize) {
                    List<WhsInboundOrder> batch = ordersToInsert.subList(i,
                        Math.min(i + batchSize, ordersToInsert.size()));

                    log.debug("批量插入入库单: {}/{}",
                        Math.min(i + batchSize, ordersToInsert.size()), ordersToInsert.size());

                    // 循环单条插入
                    for (WhsInboundOrder order : batch) {
                        try {
                            inboundOrderMapper.insert(order);
                            insertSuccess++;
                        } catch (Exception e) {
                            log.error("插入入库单异常, ID: {}, 外部单号: {}, 错误: {}",
                                order.getId(), order.getExternalOrderNo(), e.getMessage(), e);
                        }
                    }
                }
                log.info("入库单新增完成，成功{}个，失败{}个",
                    insertSuccess, ordersToInsert.size() - insertSuccess);
            } catch (Exception e) {
                log.error("批量插入入库单异常: {}", e.getMessage(), e);
            }
        }

        // 批量更新已有入库单
        if (!ordersToUpdate.isEmpty()) {
            log.info("开始更新{}个入库单", ordersToUpdate.size());
            try {
                // 使用分批处理，每批50条
                int batchSize = 50;
                for (int i = 0; i < ordersToUpdate.size(); i += batchSize) {
                    List<WhsInboundOrder> batch = ordersToUpdate.subList(i,
                        Math.min(i + batchSize, ordersToUpdate.size()));

                    log.debug("批量更新入库单: {}/{}",
                        Math.min(i + batchSize, ordersToUpdate.size()), ordersToUpdate.size());

                    // 循环单条更新
                    for (WhsInboundOrder order : batch) {
                        try {
                            inboundOrderMapper.updateById(order);
                            updateSuccess++;
                        } catch (Exception e) {
                            log.error("更新入库单异常, ID: {}, 外部单号: {}, 错误: {}",
                                order.getId(), order.getExternalOrderNo(), e.getMessage(), e);
                        }
                    }
                }
                log.info("入库单更新完成，成功{}个，失败{}个",
                    updateSuccess, ordersToUpdate.size() - updateSuccess);
            } catch (Exception e) {
                log.error("批量更新入库单异常: {}", e.getMessage(), e);
            }
        }

        // 批量插入入库单明细
        if (!itemsToInsert.isEmpty()) {
            log.info("开始新增{}个入库单明细", itemsToInsert.size());
            try {
                // 使用批量插入方法
                itemInsertSuccess = batchInsertOrderItems(itemsToInsert);
                log.info("入库单明细新增完成，成功{}个，失败{}个",
                    itemInsertSuccess, itemsToInsert.size() - itemInsertSuccess);
            } catch (Exception e) {
                log.error("批量插入入库单明细异常: {}", e.getMessage(), e);
            }
        }

        // 更新在途库存
        try {
            log.info("开始更新在途库存");
            int updatedCount = inTransitStockService.updateAllInTransitStock();
            log.info("更新在途库存完成，更新{}条记录", updatedCount);
        } catch (Exception e) {
            log.error("更新在途库存异常", e);
            // 不影响主流程，继续执行
        }

        long duration = System.currentTimeMillis() - startTime;
        log.info("入库单同步数据处理完成: 耗时{}ms, 新增{}个入库单(成功{}个), 更新{}个入库单(成功{}个), 新增{}个明细(成功{}个)",
            duration, ordersToInsert.size(), insertSuccess, ordersToUpdate.size(), updateSuccess,
            itemsToInsert.size(), itemInsertSuccess);
    }

    /**
     * 批量插入入库单明细，使用分批处理提高性能
     *
     * @param itemsToInsert 待插入的明细列表
     * @return 成功插入的记录数
     */
    private int batchInsertOrderItems(List<WhsInboundOrderItem> itemsToInsert) {
        if (itemsToInsert.isEmpty()) {
            return 0;
        }

        int batchSize = 500; // 增加批量大小，提高性能
        int successCount = 0;

        try {
            for (int i = 0; i < itemsToInsert.size(); i += batchSize) {
                List<WhsInboundOrderItem> batch = itemsToInsert.subList(i,
                    Math.min(i + batchSize, itemsToInsert.size()));

                try {
                    // 使用批量插入方法
                    int count = inboundOrderItemMapper.insertBatch(batch);
                    successCount += count;

                    log.debug("批量插入入库单明细: {}/{}, 成功{}条",
                        Math.min(i + batchSize, itemsToInsert.size()),
                        itemsToInsert.size(),
                        count);
                } catch (Exception e) {
                    log.error("批量插入入库单明细批次异常: {}", e.getMessage(), e);
                    // 如果批量插入失败，回退到单条插入
                    int fallbackCount = fallbackToSingleInsert(batch);
                    successCount += fallbackCount;
                }
            }

            return successCount;
        } catch (Exception e) {
            log.error("批量插入入库单明细整体异常: {}", e.getMessage(), e);
            // 如果整体批量插入失败，可以考虑回退到单条插入
            return fallbackToSingleInsert(itemsToInsert);
        }
    }

    /**
     * 批量插入失败时的回退方法，使用单条插入
     *
     * @param itemsToInsert 待插入的明细列表
     * @return 成功插入的记录数
     */
    private int fallbackToSingleInsert(List<WhsInboundOrderItem> itemsToInsert) {
        log.warn("批量插入失败，回退到单条插入模式，共{}条记录", itemsToInsert.size());
        int successCount = 0;

        for (WhsInboundOrderItem item : itemsToInsert) {
            try {
                inboundOrderItemMapper.insert(item);
                successCount++;

                // 每插入100条记录输出一次日志
                if (successCount % 100 == 0) {
                    log.debug("单条插入进度: {}/{}", successCount, itemsToInsert.size());
                }
            } catch (Exception e) {
                log.error("单条插入入库单明细异常, ID: {}, SKU: {}, 错误: {}",
                    item.getId(), item.getSkuCode(), e.getMessage());
            }
        }

        log.info("单条插入完成，成功{}条，失败{}条",
            successCount, itemsToInsert.size() - successCount);

        return successCount;
    }


    /**
     * 批量查找SKU编码对应的变体ID
     *
     * @param skuCodes SKU编码列表
     * @return SKU编码到变体ID的映射
     */
    private Map<String, Long> batchFindVariantIdsBySkuCodes(List<String> skuCodes) {
        if (skuCodes == null || skuCodes.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            // 调用商品变体服务查询变体ID
            Map<String, Long> variantIdMap = variantService.getVariantIdsBySkuCodes(skuCodes);

            if (variantIdMap != null && !variantIdMap.isEmpty()) {
                log.debug("成功从商品变体服务查询到{}个SKU对应的变体ID", variantIdMap.size());
                return variantIdMap;
            }

            log.warn("从商品变体服务查询变体ID返回空结果，尝试从历史数据中查询");

            // 如果商品变体服务查询失败，尝试从历史数据中查询
            LambdaQueryWrapper<WhsInboundOrderItem> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(WhsInboundOrderItem::getSkuCode, skuCodes)
                .select(WhsInboundOrderItem::getSkuCode, WhsInboundOrderItem::getVariantId)
                .groupBy(WhsInboundOrderItem::getSkuCode);

            List<WhsInboundOrderItem> items = inboundOrderItemMapper.selectList(queryWrapper);

            // 将查询结果转换为映射
            Map<String, Long> result = items.stream()
                .filter(item -> item.getVariantId() != null && item.getVariantId() > 0)
                .collect(Collectors.toMap(
                    WhsInboundOrderItem::getSkuCode,
                    WhsInboundOrderItem::getVariantId,
                    (existing, replacement) -> existing // 如果有重复，保留第一个
                ));

            log.debug("从历史数据中查询到{}个SKU对应的变体ID", result.size());
            return result;
        } catch (Exception e) {
            log.error("批量查询SKU对应的变体ID时发生异常: {}", e.getMessage());
            return Collections.emptyMap();
        }
    }

    /**
     * 记录同步操作日志
     * 将入库单同步操作记录到whs_shipping_dispatch_log表中
     *
     * @param context 仓库上下文
     * @param result  同步结果
     */
    private void recordSyncOperationLog(WarehouseContextVo context, InboundOrderSyncResultVo result) {
        try {
            WhsShippingDispatchLog dispatchLog = new WhsShippingDispatchLog();
            dispatchLog.setId(cn.hutool.core.util.IdUtil.getSnowflakeNextId());
            dispatchLog.setWarehouseId(context.getWarehouseId());
            dispatchLog.setProviderId(context.getProvider().getId());
            dispatchLog.setAccountId(context.getAccount().getId());

            // 设置操作类型为同步入库单
            dispatchLog.setOperationType("SYNC_INBOUND");

            // 设置状态
            dispatchLog.setStatus(result.isSuccess() ? "SUCCESS" : "FAILED");

            // 设置错误信息
            if (!result.isSuccess()) {
                dispatchLog.setErrorMessage(result.getErrorMessage());
            }

            // 设置请求和响应数据
            dispatchLog.setRequestData("同步入库单请求");

            // 构建响应数据摘要
            StringBuilder responseSummary = new StringBuilder();
            responseSummary.append("同步结果: ").append(result.isSuccess() ? "成功" : "失败");

            if (result.isSuccess() && result.getRecords() != null) {
                responseSummary.append(", 获取到").append(result.getRecords().size()).append("条入库单记录");

                // 添加入库单号列表（最多显示10个）
                if (!result.getRecords().isEmpty()) {
                    responseSummary.append(", 入库单号: ");
                    List<String> orderNos = result.getRecords().stream()
                        .map(InboundOrderVo::getAsnNo)
                        .limit(10)
                        .toList();
                    responseSummary.append(String.join(", ", orderNos));

                    if (result.getRecords().size() > 10) {
                        responseSummary.append("...(共").append(result.getRecords().size()).append("个)");
                    }
                }
            }

            dispatchLog.setResponseData(responseSummary.toString());

            // 保存日志
            dispatchLogMapper.insert(dispatchLog);

            log.info("记录入库单同步操作日志成功，仓库ID: {}, 日志ID: {}", context.getWarehouseId(), dispatchLog.getId());
        } catch (Exception e) {
            log.error("记录入库单同步操作日志失败: {}", e.getMessage(), e);
        }
    }
}
