package com.imhuso.wholesale.core.service.impl;

import cn.dev33.satoken.stp.parameter.SaLoginParameter;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.imhuso.common.client.domain.ClientVo;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.utils.ValidatorUtils;
import com.imhuso.common.json.utils.JsonUtils;
import com.imhuso.common.satoken.utils.LoginHelper;
import com.imhuso.wholesale.core.domain.WhsMember;
import com.imhuso.wholesale.core.domain.bo.front.WhsLoginBody;
import com.imhuso.wholesale.core.domain.vo.front.WhsLoginVo;
import com.imhuso.wholesale.core.exception.WhsException;
import com.imhuso.wholesale.core.model.WholesaleLoginUser;
import com.imhuso.wholesale.core.satoken.WholesaleLoginHelper;
import com.imhuso.wholesale.core.satoken.utils.StpWholesaleUtil;
import com.imhuso.wholesale.core.service.IWhsAuthStrategy;
import com.imhuso.wholesale.core.service.IWhsMemberService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 批发商密码认证策略
 *
 * <AUTHOR>
 */
@Slf4j
@Service("password" + IWhsAuthStrategy.BASE_NAME)
@RequiredArgsConstructor
public class WhsPasswordAuthStrategy implements IWhsAuthStrategy {

    private final IWhsMemberService memberService;

    @Override
    public WhsLoginVo login(String body, ClientVo client) {
        WhsLoginBody loginBody = JsonUtils.parseObject(body, WhsLoginBody.class);
        ValidatorUtils.validate(loginBody);

        String username = loginBody.getUsername();
        String password = loginBody.getPassword();

        // 获取会员信息
        WhsMember member = Optional.ofNullable(memberService.getMemberByUsername(username))
            .orElseThrow(() -> new WhsException("wholesale.member.not.exists"));

        // 验证密码
        if (!BCrypt.checkpw(password, member.getPassword())) {
            throw new WhsException("wholesale.member.password.not.match");
        }

        // 验证会员状态
        if (!BusinessConstants.NORMAL.equals(member.getStatus())) {
            throw new WhsException("wholesale.member.blocked");
        }

        // 更新登录信息
        memberService.updateLoginInfo(member.getId(), null, null);

        // 构建登录用户
        WholesaleLoginUser loginUser = new WholesaleLoginUser();
        loginUser.setUserId(member.getId());
        loginUser.setUsername(member.getEmail());
        loginUser.setEmail(member.getEmail());
        loginUser.setFullName(member.getFirstName() + " " + member.getLastName());
        loginUser.setAvatar(member.getAvatar());

        // 设置客户端信息
        if (ObjectUtil.isNotNull(client)) {
            loginUser.setClientId(client.getClientId());

        }

        // 生成token
        SaLoginParameter model = new SaLoginParameter();
        if (ObjectUtil.isNotNull(client)) {
            model.setTimeout(client.getTimeout());
            model.setActiveTimeout(client.getActiveTimeout());

        }
        model.setExtra(LoginHelper.CLIENT_ID, loginBody.getClientId());

        // 登录
        WholesaleLoginHelper.login(loginUser, model);

        // 返回token信息
        WhsLoginVo loginVo = new WhsLoginVo();
        loginVo.setAccessToken(StpWholesaleUtil.getTokenValue());
        loginVo.setExpireIn(StpWholesaleUtil.getTokenTimeout());
        loginVo.setClientId(loginBody.getClientId());

        log.info("批发商用户登录成功: {}, 客户端ID: {}", username, loginBody.getClientId());
        return loginVo;
    }
}
