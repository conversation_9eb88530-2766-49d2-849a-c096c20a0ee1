package com.imhuso.wholesale.core.mapper;

import com.imhuso.common.mybatis.core.mapper.BaseMapperPlus;
import com.imhuso.wholesale.core.domain.WhsOrderItem;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderItemVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单项Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface WhsOrderItemMapper extends BaseMapperPlus<WhsOrderItem, WhsOrderItemVo> {

    /**
     * 获取订单项及相关详细信息
     * 通过一次查询获取订单项和相关产品、变体信息
     *
     * @param orderId 订单ID
     * @return 包含详细信息的订单项列表
     */
    List<WhsOrderItem> selectOrderItemsWithDetails(@Param("orderId") Long orderId);
}
