package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderPendingShipmentVo;

/**
 * 订单待发货明细服务接口
 *
 * <AUTHOR>
 */
public interface IWhsOrderPendingShipmentService {

    /**
     * 获取订单待发货明细
     * 根据订单是否有发货方案，采用不同的逻辑：
     * - 如果有发货方案，则以发货方案为准
     * - 如果没有发货方案，则以订单明细为准
     *
     * @param orderId 订单ID
     * @return 待发货明细
     */
    WhsOrderPendingShipmentVo getOrderPendingShipment(Long orderId);
}
