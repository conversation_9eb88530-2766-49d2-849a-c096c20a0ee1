package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.wholesale.core.domain.WhsProductAttribute;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品属性业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@AutoMapper(target = WhsProductAttribute.class)
public class WhsProductAttributeBo extends BaseEntity {
    /**
     * 属性ID
     */
    @NotNull(message = "属性ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 属性名称(如color,material)
     */
    @NotBlank(message = "属性名称不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 50, message = "属性名称不能超过50个字符")
    private String attrName;

    /**
     * 属性显示名称(如颜色,材质)
     */
    @NotBlank(message = "属性显示名称不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 50, message = "属性显示名称不能超过50个字符")
    private String attrDisplay;

    /**
     * 是否必填
     */
    @NotNull(message = "是否必填不能为空", groups = {AddGroup.class, EditGroup.class})
    private String isRequired;

    /**
     * 排序顺序
     */
    private Integer sort;

    /**
     * 状态（0停用 1正常）
     */
    private String status;
}
