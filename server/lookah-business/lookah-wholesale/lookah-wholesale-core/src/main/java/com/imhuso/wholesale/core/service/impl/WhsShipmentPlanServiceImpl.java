package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.wholesale.core.domain.WhsShipmentPlan;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanItemVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanVo;
import com.imhuso.wholesale.core.enums.ShipmentPlanStatus;
import com.imhuso.wholesale.core.mapper.WhsShipmentPlanMapper;
import com.imhuso.wholesale.core.service.IWhsShipmentConversionService;
import com.imhuso.wholesale.core.service.IWhsShipmentPlanItemService;
import com.imhuso.wholesale.core.service.IWhsShipmentPlanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 发货方案服务实现类
 * 直接实现了发货方案的管理功能，包括查询、选择、验证和更新等操作
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsShipmentPlanServiceImpl implements IWhsShipmentPlanService {

    private final WhsShipmentPlanMapper shipmentPlanMapper;
    private final IWhsShipmentPlanItemService shipmentPlanItemService;
    private final IWhsShipmentConversionService shipmentConversionService;

    @Override
    public List<WhsShipmentPlanVo> getShipmentPlansByOrderId(Long orderId) {
        if (orderId == null) {
            return new ArrayList<>();
        }

        // 查询订单的所有发货方案
        List<WhsShipmentPlan> plans = queryPlansByOrderId(orderId);
        if (CollUtil.isEmpty(plans)) {
            return new ArrayList<>();
        }

        // 转换为VO列表并加载方案项
        return convertAndLoadPlanItems(plans);
    }

    @Override
    public WhsShipmentPlanVo getShipmentPlanById(Long planId) {
        if (planId == null) {
            return null;
        }

        // 直接通过ID查询方案
        WhsShipmentPlan plan = shipmentPlanMapper.selectById(planId);
        if (plan == null) {
            return null;
        }

        // 转换为VO并加载方案项
        List<WhsShipmentPlan> plans = new ArrayList<>();
        plans.add(plan);
        List<WhsShipmentPlanVo> planVos = convertAndLoadPlanItems(plans);

        // 返回转换后的VO (应该只有一个结果)
        return CollUtil.isEmpty(planVos) ? null : planVos.get(0);
    }

    /**
     * 根据订单ID查询方案
     */
    private List<WhsShipmentPlan> queryPlansByOrderId(Long orderId) {
        LambdaQueryWrapper<WhsShipmentPlan> query = new LambdaQueryWrapper<>();
        query.eq(WhsShipmentPlan::getOrderId, orderId).orderByDesc(WhsShipmentPlan::getCreateTime);
        return shipmentPlanMapper.selectList(query);
    }

    /**
     * 将方案实体列表转换为VO列表并加载方案项
     *
     * @param plans 方案实体列表
     * @return 方案VO列表，包含方案项
     */
    private List<WhsShipmentPlanVo> convertAndLoadPlanItems(List<WhsShipmentPlan> plans) {
        if (CollUtil.isEmpty(plans)) {
            return new ArrayList<>();
        }

        // 转换为VO列表
        List<WhsShipmentPlanVo> vos = MapstructUtils.convert(plans, WhsShipmentPlanVo.class);

        // 批量获取所有相关方案项
        List<Long> planIds = plans.stream().map(WhsShipmentPlan::getId).collect(Collectors.toList());
        List<WhsShipmentPlanItemVo> allItems = shipmentPlanItemService.getShipmentPlanItemsByPlanIds(planIds);

        // 按 planId 分组方案项
        Map<Long, List<WhsShipmentPlanItemVo>> itemsByPlanId = allItems.stream().collect(Collectors.groupingBy(WhsShipmentPlanItemVo::getPlanId));

        // 为每个方案VO设置方案项
        for (WhsShipmentPlanVo vo : vos) {
            vo.setPlanItems(itemsByPlanId.getOrDefault(vo.getId(), new ArrayList<>()));
        }

        return vos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean selectShipmentPlan(Long planId) {
        // 验证方案
        WhsShipmentPlan plan = validatePlanForSelection(planId);

        // 将其他方案标记为未选中
        markOtherPlansAsUnselected(plan.getOrderId(), planId);

        // 将当前方案标记为已选中
        markPlanAsSelected(plan);

        return true;
    }

    /**
     * 验证方案是否可选择
     */
    private WhsShipmentPlan validatePlanForSelection(Long planId) {
        if (planId == null) {
            throw new ServiceException("方案ID不能为空");
        }

        // 获取方案
        WhsShipmentPlan plan = shipmentPlanMapper.selectById(planId);
        if (plan == null) {
            throw new ServiceException("发货方案不存在");
        }

        // 检查方案状态
        // 允许选择 PENDING 或 EXECUTED 状态的方案
        Integer currentStatus = plan.getStatus();
        boolean isStatusNull = currentStatus == null;
        boolean isPendingStatus = ShipmentPlanStatus.PENDING.getValue().equals(currentStatus);
        boolean isExecutedStatus = ShipmentPlanStatus.EXECUTED.getValue().equals(currentStatus);
        boolean isValidStatus = isPendingStatus || isExecutedStatus;

        if (isStatusNull || !isValidStatus) {
            throw new ServiceException("只能选择待处理或部分执行状态的发货方案");
        }

        return plan;
    }

    /**
     * 将其他方案标记为未选中
     */
    private void markOtherPlansAsUnselected(Long orderId, Long selectedPlanId) {
        LambdaQueryWrapper<WhsShipmentPlan> query = new LambdaQueryWrapper<>();
        query.eq(WhsShipmentPlan::getOrderId, orderId).ne(WhsShipmentPlan::getId, selectedPlanId);

        // 使用批量更新替代循环单条更新，提高数据库操作效率
        // 注意：只更新 isSelected 字段，避免意外清空其他字段
        // 创建一个只包含isSelected字段的更新对象
        shipmentPlanMapper.update(null, new com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper<WhsShipmentPlan>().eq(WhsShipmentPlan::getOrderId, orderId).ne(WhsShipmentPlan::getId, selectedPlanId).set(WhsShipmentPlan::getIsSelected, false));
    }

    /**
     * 标记方案为已选中
     */
    private void markPlanAsSelected(WhsShipmentPlan plan) {
        // 使用LambdaUpdateWrapper，只更新isSelected字段，避免修改其他字段
        shipmentPlanMapper.update(null, new com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper<WhsShipmentPlan>().eq(WhsShipmentPlan::getId, plan.getId()).set(WhsShipmentPlan::getIsSelected, true));
    }

    /**
     * 将订单的其他所有计划标记为不可用
     * 在一个计划成功发货后调用此方法，防止重复发货
     *
     * @param orderId        订单ID
     * @param executedPlanId 已执行的计划ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markOtherPlansAsUnavailable(Long orderId, Long executedPlanId) {
        // 更新其他未选中的、非不可用状态的计划为不可用
        int updatedCount = shipmentPlanMapper.update(null,
            Wrappers.<WhsShipmentPlan>lambdaUpdate()
                .eq(WhsShipmentPlan::getOrderId, orderId)
                .ne(WhsShipmentPlan::getId, executedPlanId)
                .ne(WhsShipmentPlan::getStatus, ShipmentPlanStatus.UNAVAILABLE.getValue())
                .set(WhsShipmentPlan::getStatus, ShipmentPlanStatus.UNAVAILABLE.getValue())
        );

        if (updatedCount > 0) {
            log.info("订单 {} 已将 {} 个其他发货方案标记为不可用", orderId, updatedCount);
        }
    }

    /**
     * 更新单个发货方案的状态
     */
    @Override
    public void updatePlanStatus(Long planId, Integer status) {
        if (planId == null || status == null) {
            log.warn("更新发货方案状态失败：planId 或 status 为空");
            return;
        }

        int updatedRows = shipmentPlanMapper.update(null,
            Wrappers.<WhsShipmentPlan>lambdaUpdate()
                .eq(WhsShipmentPlan::getId, planId)
                .set(WhsShipmentPlan::getStatus, status)
        );

        if (updatedRows > 0) {
            log.info("成功更新发货方案 {} 的状态为 {}", planId, status);
        } else {
            log.warn("更新发货方案 {} 状态为 {} 时，没有行被影响或更新失败", planId, status);
        }
    }

    /**
     * 删除订单的所有发货方案及相关数据
     * 包括发货方案、发货方案项和发货转换记录
     *
     * @param orderId 订单ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAllShipmentPlansByOrderId(Long orderId) {
        if (orderId == null) {
            log.warn("删除发货方案失败：订单ID为空");
            return;
        }

        log.info("开始删除订单 {} 的所有发货方案及相关数据", orderId);

        try {
            // 1. 删除发货转换记录
            shipmentConversionService.deleteByOrderId(orderId);

            // 2. 删除发货方案项
            shipmentPlanItemService.deleteByOrderId(orderId);

            // 3. 删除发货方案
            LambdaQueryWrapper<WhsShipmentPlan> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WhsShipmentPlan::getOrderId, orderId);
            int deletedPlanCount = shipmentPlanMapper.delete(queryWrapper);

            if (deletedPlanCount > 0) {
                log.info("成功删除订单 {} 的发货方案，共删除 {} 个方案", orderId, deletedPlanCount);
            } else {
                log.debug("订单 {} 没有发货方案需要删除", orderId);
            }

        } catch (Exception e) {
            log.error("删除订单 {} 的发货方案时发生错误: {}", orderId, e.getMessage(), e);
            throw e;
        }
    }
}
