package com.imhuso.wholesale.core.domain.vo.admin;

import com.imhuso.wholesale.core.domain.WhsCountry;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 国家视图对象（后台）
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsCountry.class)
public class WhsCountryVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 国家ID
     */
    private Long id;

    /**
     * 国家代码
     */
    private String code;

    /**
     * 国家名称
     */
    private String name;

    /**
     * 国家中文名称
     */
    private String nameZh;

    /**
     * 是否启用（0正常 1停用）
     */
    private String status;

    /**
     * 排序
     */
    private Integer sort;
}
