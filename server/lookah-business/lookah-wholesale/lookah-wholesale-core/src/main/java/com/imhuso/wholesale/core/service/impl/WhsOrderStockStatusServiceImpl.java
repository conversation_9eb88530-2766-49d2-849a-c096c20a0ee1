package com.imhuso.wholesale.core.service.impl;

import com.imhuso.wholesale.core.domain.WhsOrderItem;
import com.imhuso.wholesale.core.enums.StockStatus;
import com.imhuso.wholesale.core.service.IWhsOrderItemService;
import com.imhuso.wholesale.core.service.IWhsOrderStockStatusService;
import com.imhuso.wholesale.core.service.IWhsStockService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单库存状态服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsOrderStockStatusServiceImpl implements IWhsOrderStockStatusService {

    private final IWhsOrderItemService orderItemService;
    private final IWhsStockService stockService;

    @Override
    public StockStatus calculateOrderStockStatus(Long orderId) {
        if (orderId == null) {
            return StockStatus.OUT_OF_STOCK;
        }

        try {
            // 获取订单项
            List<WhsOrderItem> orderItems = orderItemService.getOrderItems(orderId);
            if (orderItems.isEmpty()) {
                return StockStatus.OUT_OF_STOCK;
            }

            // 获取所有变体ID
            List<Long> variantIds = orderItems.stream()
                .map(WhsOrderItem::getVariantId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

            if (variantIds.isEmpty()) {
                return StockStatus.OUT_OF_STOCK;
            }

            // 获取可用库存
            Map<Long, Integer> availableStocks = stockService.getAvailableStocks(variantIds);

            // 检查每个订单项的库存状态
            boolean hasOutOfStock = false;
            boolean hasInsufficientStock = false;
            boolean hasSufficientStock = false;

            for (WhsOrderItem item : orderItems) {
                Long variantId = item.getVariantId();
                Integer requiredQuantity = calculateRequiredQuantity(item);
                Integer availableQuantity = availableStocks.getOrDefault(variantId, 0);

                if (availableQuantity == 0) {
                    hasOutOfStock = true;
                } else if (availableQuantity < requiredQuantity) {
                    hasInsufficientStock = true;
                } else {
                    hasSufficientStock = true;
                }
            }

            // 根据检查结果返回状态
            return determineStockStatus(hasOutOfStock, hasInsufficientStock, hasSufficientStock);

        } catch (Exception e) {
            log.error("计算订单[{}]库存状态时发生错误: {}", orderId, e.getMessage(), e);
            return StockStatus.OUT_OF_STOCK;
        }
    }

    @Override
    public Map<Long, StockStatus> batchCalculateOrderStockStatus(List<Long> orderIds) {
        Map<Long, StockStatus> result = new HashMap<>();

        if (orderIds == null || orderIds.isEmpty()) {
            return result;
        }

        try {
            // 性能优化：批量查询所有订单的订单项，避免N+1查询
            Map<Long, List<WhsOrderItem>> orderItemsMap = batchGetOrderItems(orderIds);

            // 收集所有需要查询库存的变体ID
            Set<Long> allVariantIds = new HashSet<>();
            for (List<WhsOrderItem> items : orderItemsMap.values()) {
                items.stream()
                    .map(WhsOrderItem::getVariantId)
                    .filter(Objects::nonNull)
                    .forEach(allVariantIds::add);
            }

            // 批量查询所有变体的库存信息
            Map<Long, Integer> availableStocks = allVariantIds.isEmpty() ?
                Collections.emptyMap() : stockService.getAvailableStocks(new ArrayList<>(allVariantIds));

            // 为每个订单计算库存状态
            for (Long orderId : orderIds) {
                List<WhsOrderItem> orderItems = orderItemsMap.getOrDefault(orderId, Collections.emptyList());
                StockStatus status = calculateOrderStockStatusFromItems(orderItems, availableStocks);
                result.put(orderId, status);
            }

        } catch (Exception e) {
            log.error("批量计算订单库存状态时发生错误，订单ID: {}，错误: {}", orderIds, e.getMessage());
            for (Long orderId : orderIds) {
                result.put(orderId, StockStatus.OUT_OF_STOCK);
            }
        }

        return result;
    }

    @Override
    public boolean hasInsufficientStock(Long orderId) {
        StockStatus status = calculateOrderStockStatus(orderId);
        return status == StockStatus.INSUFFICIENT ||
            status == StockStatus.OUT_OF_STOCK ||
            status == StockStatus.PARTIAL_STOCK;
    }

    @Override
    public Map<Long, Boolean> batchCheckInsufficientStock(List<Long> orderIds) {
        Map<Long, Boolean> result = new HashMap<>();

        if (orderIds == null || orderIds.isEmpty()) {
            return result;
        }

        Map<Long, StockStatus> stockStatusMap = batchCalculateOrderStockStatus(orderIds);

        for (Long orderId : orderIds) {
            StockStatus status = stockStatusMap.get(orderId);
            boolean hasInsufficient = status == StockStatus.INSUFFICIENT ||
                status == StockStatus.OUT_OF_STOCK ||
                status == StockStatus.PARTIAL_STOCK;
            result.put(orderId, hasInsufficient);
        }

        return result;
    }

    /**
     * 批量获取多个订单的订单项
     * 避免N+1查询问题
     */
    private Map<Long, List<WhsOrderItem>> batchGetOrderItems(List<Long> orderIds) {
        Map<Long, List<WhsOrderItem>> result = new HashMap<>();

        try {
            // 使用批量查询获取所有订单项（已经按订单ID分组）
            Map<Long, List<WhsOrderItem>> orderItemsMap = orderItemService.batchGetOrderItemsByOrderIds(orderIds);

            // 直接使用分组后的结果
            result.putAll(orderItemsMap);

        } catch (Exception e) {
            log.error("批量获取订单项时发生错误: {}", e.getMessage(), e);
            // 降级到单个查询
            for (Long orderId : orderIds) {
                try {
                    List<WhsOrderItem> items = orderItemService.getOrderItems(orderId);
                    result.put(orderId, items);
                } catch (Exception ex) {
                    log.warn("获取订单[{}]的订单项失败: {}", orderId, ex.getMessage());
                    result.put(orderId, Collections.emptyList());
                }
            }
        }

        return result;
    }

    /**
     * 根据订单项和库存信息计算库存状态
     */
    private StockStatus calculateOrderStockStatusFromItems(List<WhsOrderItem> orderItems, Map<Long, Integer> availableStocks) {
        if (orderItems.isEmpty()) {
            return StockStatus.OUT_OF_STOCK;
        }

        // 检查每个订单项的库存状态
        boolean hasOutOfStock = false;
        boolean hasInsufficientStock = false;
        boolean hasSufficientStock = false;

        for (WhsOrderItem item : orderItems) {
            Long variantId = item.getVariantId();
            if (variantId == null) {
                continue;
            }

            Integer requiredQuantity = calculateRequiredQuantity(item);
            Integer availableQuantity = availableStocks.getOrDefault(variantId, 0);

            if (availableQuantity == 0) {
                hasOutOfStock = true;
            } else if (availableQuantity < requiredQuantity) {
                hasInsufficientStock = true;
            } else {
                hasSufficientStock = true;
            }
        }

        // 根据检查结果返回状态
        return determineStockStatus(hasOutOfStock, hasInsufficientStock, hasSufficientStock);
    }

    private StockStatus determineStockStatus(boolean hasOutOfStock, boolean hasInsufficientStock, boolean hasSufficientStock) {
        if (!hasOutOfStock && !hasInsufficientStock && !hasSufficientStock) {
            return StockStatus.OUT_OF_STOCK;
        }

        if (hasOutOfStock) {
            if (!hasSufficientStock && !hasInsufficientStock) {
                return StockStatus.OUT_OF_STOCK;
            } else {
                return StockStatus.PARTIAL_STOCK;
            }
        } else if (hasInsufficientStock) {
            return StockStatus.INSUFFICIENT;
        } else {
            return StockStatus.SUFFICIENT;
        }
    }

    private Integer calculateRequiredQuantity(WhsOrderItem item) {
        if (item == null) {
            return 0;
        }
        return item.getQuantity() != null ? item.getQuantity() : 0;
    }
}
