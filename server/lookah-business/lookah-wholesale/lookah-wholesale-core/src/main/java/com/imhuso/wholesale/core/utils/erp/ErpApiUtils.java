package com.imhuso.wholesale.core.utils.erp;

import com.fasterxml.jackson.core.type.TypeReference;
import com.imhuso.wholesale.core.config.ErpConfig;
import com.imhuso.wholesale.core.domain.dto.ErpApiResponse;
import com.imhuso.wholesale.core.domain.dto.ErpStockBo;
import com.imhuso.wholesale.core.domain.dto.ErpStockListData;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ERP API工具类
 * 提供ERP系统的具体API调用方法
 *
 * <AUTHOR>
 */
@Slf4j
public class ErpApiUtils {

    private ErpApiUtils() {
        // 私有构造方法，防止实例化
    }

    /**
     * 获取产品库存列表
     *
     * @param erpConfig ERP配置
     * @param skuCodes  SKU编码列表
     * @return 库存数据列表
     */
    public static List<ErpStockBo> getProductStockList(ErpConfig erpConfig, List<String> skuCodes) {
        if (skuCodes == null || skuCodes.isEmpty()) {
            log.warn("SKU编码列表为空");
            return new ArrayList<>();
        }

        // 分批处理，避免URL过长
        List<List<String>> batches = ErpHttpUtils.splitIntoBatches(skuCodes, erpConfig.getBatchSize());

        try {
            long startTime = System.currentTimeMillis();

            List<ErpStockBo> allStockData = new ArrayList<>();

            log.info("ERP库存同步开始：总共{}个SKU，分{}批处理，每批最多{}个", skuCodes.size(), batches.size(), erpConfig.getBatchSize());

            int totalProcessed = 0;
            int successfulBatches = 0;

            for (int i = 0; i < batches.size(); i++) {
                List<String> batch = batches.get(i);
                log.info("处理第{}/{}批，包含{}个SKU", i + 1, batches.size(), batch.size());

                List<ErpStockBo> batchData = fetchStockDataBatch(erpConfig, batch);
                allStockData.addAll(batchData);
                totalProcessed += batch.size();

                if (!batchData.isEmpty()) {
                    successfulBatches++;
                }

                log.info("第{}/{}批完成，获取到{}个库存数据", i + 1, batches.size(), batchData.size());
            }

            long endTime = System.currentTimeMillis();
            double durationSeconds = (endTime - startTime) / 1000.0;

            // 输出友好的同步摘要
            logSyncSummary(skuCodes.size(), allStockData.size(), batches.size(),
                successfulBatches, durationSeconds, erpConfig.getBatchSize());

            return allStockData;

        } catch (Exception e) {
            log.error("获取ERP库存数据失败，SKU总数: {}, 分批数: {}, 错误: {}",
                skuCodes.size(), batches.size(), e.getMessage(), e);
            // 统一异常处理策略：对于批量库存获取，返回空列表避免中断整个同步流程
            // 记录详细上下文信息便于问题排查和监控
            return new ArrayList<>();
        }
    }

    /**
     * 分批获取库存数据
     *
     * @param erpConfig ERP配置
     * @param skuCodes  SKU编码列表
     * @return 库存数据列表
     */
    private static List<ErpStockBo> fetchStockDataBatch(ErpConfig erpConfig, List<String> skuCodes) {
        // 使用GoFrame V2标准的数组参数格式
        // Hutool会自动给数组参数添加[]，所以这里用"skus"，最终会变成"skus[]"
        Map<String, String[]> params = new HashMap<>();
        params.put("skus", skuCodes.toArray(new String[0]));

        String uri = "/api/v1/product_stock/getProductStockList";

        // 发送请求 - 使用最佳实践的签名方式
        ErpApiResponse<ErpStockListData> response = ErpHttpUtils.sendGetRequestWithV2Signature(
            erpConfig,
            uri,
            params,
            new TypeReference<ErpApiResponse<ErpStockListData>>() {
            }
        );

        // 提取list数据
        ErpStockListData stockListData = response.getData();
        if (stockListData == null || stockListData.getList() == null) {
            return new ArrayList<>();
        }

        return stockListData.getList();
    }

    /**
     * 输出ERP库存同步摘要日志
     *
     * @param totalSkus         总SKU数量
     * @param successfulSkus    成功获取库存的SKU数量
     * @param totalBatches      总批次数
     * @param successfulBatches 成功的批次数
     * @param durationSeconds   耗时（秒）
     * @param batchSize         批次大小
     */
    private static void logSyncSummary(int totalSkus, int successfulSkus, int totalBatches,
                                       int successfulBatches, double durationSeconds, int batchSize) {
        int failedSkus = totalSkus - successfulSkus;
        double successRate = totalSkus > 0 ? (successfulSkus * 100.0 / totalSkus) : 0.0;
        double avgSkusPerBatch = totalBatches > 0 ? (totalSkus * 1.0 / totalBatches) : 0.0;

        StringBuilder summary = new StringBuilder("\n");
        summary.append("========================================\n");
        summary.append("        ERP库存同步完成摘要\n");
        summary.append("========================================\n");
        summary.append(String.format("总SKU数量:      %,d\n", totalSkus));
        summary.append(String.format("成功获取:       %,d (%.1f%%)\n", successfulSkus, successRate));
        summary.append(String.format("失败/无数据:    %,d (%.1f%%)\n", failedSkus, 100.0 - successRate));
        summary.append(String.format("处理批次:       %d/%d 批\n", successfulBatches, totalBatches));
        summary.append(String.format("平均每批:       %.0f 个SKU\n", avgSkusPerBatch));
        summary.append(String.format("批次大小:       %d\n", batchSize));
        summary.append(String.format("总耗时:         %.2f 秒\n", durationSeconds));
        if (durationSeconds > 0) {
            summary.append(String.format("处理速度:       %.0f SKU/秒\n", totalSkus / durationSeconds));
        }
        summary.append("========================================");

        log.info(summary.toString());
    }

    /**
     * 健康检查
     *
     * @param erpConfig ERP配置
     * @return 是否健康
     */
    public static boolean healthCheck(ErpConfig erpConfig) {
        return ErpHttpUtils.healthCheck(erpConfig);
    }

    /**
     * 测试ERP连接
     *
     * @param erpConfig ERP配置
     * @return 连接测试结果
     */
    public static Map<String, Object> testConnection(ErpConfig erpConfig) {
        Map<String, Object> result = new HashMap<>();

        try {
            boolean isHealthy = healthCheck(erpConfig);
            result.put("success", isHealthy);
            result.put("message", isHealthy ? "ERP连接正常" : "ERP连接失败");
            result.put("timestamp", System.currentTimeMillis());
            result.put("config", Map.of(
                "baseUrl", erpConfig.getBaseUrl(),
                "appKey", erpConfig.getAppKey() != null ? "***" : null,
                "enabled", erpConfig.isEnabled()
            ));
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "ERP连接测试异常: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }

        return result;
    }

    /**
     * 获取ERP系统信息
     *
     * @param erpConfig ERP配置
     * @return 系统信息
     */
    public static Map<String, Object> getSystemInfo(ErpConfig erpConfig) {
        Map<String, Object> info = new HashMap<>();
        info.put("type", erpConfig.getType());
        info.put("baseUrl", erpConfig.getBaseUrl());
        info.put("appKey", erpConfig.getAppKey() != null ? "***" : null);
        info.put("enabled", erpConfig.isEnabled());
        info.put("internal", erpConfig.isInternal());
        info.put("connectTimeout", erpConfig.getConnectTimeout());
        info.put("readTimeout", erpConfig.getReadTimeout());
        info.put("retryCount", erpConfig.getRetryCount());
        info.put("retryInterval", erpConfig.getRetryInterval());
        info.put("batchSize", erpConfig.getBatchSize());
        info.put("webhookEnabled", erpConfig.getWebhook().isEnabled());
        info.put("signatureVerification", erpConfig.getWebhook().isSignatureVerification());
        return info;
    }

    /**
     * 验证ERP配置
     *
     * @param erpConfig ERP配置
     * @return 验证结果
     */
    public static Map<String, Object> validateConfig(ErpConfig erpConfig) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();

        if (!erpConfig.isEnabled()) {
            errors.add("ERP集成未启用");
        }

        if (erpConfig.getBaseUrl() == null || erpConfig.getBaseUrl().trim().isEmpty()) {
            errors.add("ERP基础URL未配置");
        }

        if (erpConfig.getAppKey() == null || erpConfig.getAppKey().trim().isEmpty()) {
            errors.add("ERP应用Key未配置");
        }

        if (erpConfig.getAppSecret() == null || erpConfig.getAppSecret().trim().isEmpty()) {
            errors.add("ERP应用Secret未配置");
        }

        if (erpConfig.getConnectTimeout() <= 0) {
            errors.add("连接超时时间配置无效");
        }

        if (erpConfig.getReadTimeout() <= 0) {
            errors.add("读取超时时间配置无效");
        }

        if (erpConfig.getBatchSize() <= 0) {
            errors.add("批处理大小配置无效");
        }

        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        result.put("config", getSystemInfo(erpConfig));

        return result;
    }
}
