package com.imhuso.wholesale.core.service;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.WhsStock;
import com.imhuso.wholesale.core.domain.vo.admin.WhsStockLogVo;
import com.imhuso.wholesale.core.enums.StockOperationType;

import java.util.List;

/**
 * 库存操作日志服务接口
 *
 * <AUTHOR>
 */
public interface IWhsStockLogService {
    /**
     * 创建库存操作日志
     *
     * @param stock         库存信息
     * @param operationType 操作类型
     * @param quantity      操作数量
     * @param beforeStock   操作前可用库存
     * @param afterStock    操作后可用库存
     * @param orderId       订单ID（可选）
     * @param orderItemId   订单项ID（可选）
     * @param remark        备注信息
     */
    void createStockLog(WhsStock stock,
                        StockOperationType operationType,
                        int quantity,
                        int beforeStock,
                        int afterStock,
                        Long orderId,
                        Long orderItemId,
                        String remark);

    /**
     * 根据变体ID、订单ID和操作类型查找对应的库存ID列表
     *
     * @param variantId     变体ID
     * @param orderId       订单ID
     * @param operationType 操作类型
     * @return 库存ID列表
     */
    List<Long> findStockIdsByOperationLog(Long variantId, Long orderId, Integer operationType);

    /**
     * 根据变体ID、订单ID、订单项ID和操作类型查找对应的库存ID列表
     * <p>
     * 此方法可用于精确定位某个订单项对应的库存记录，提高库存操作的准确性。
     *
     * @param variantId     变体ID
     * @param orderId       订单ID
     * @param orderItemId   订单项ID
     * @param operationType 操作类型
     * @return 库存ID列表
     */
    List<Long> findStockIdsByOperationLogWithItem(Long variantId, Long orderId, Long orderItemId,
                                                  Integer operationType);

    /**
     * 根据库存ID查询库存日志分页列表
     *
     * @param stockId   库存ID
     * @param pageQuery 分页参数
     * @return 库存日志分页列表
     */
    TableDataInfo<WhsStockLogVo> getStockLogsPageByStockId(Long stockId, PageQuery pageQuery);
}
