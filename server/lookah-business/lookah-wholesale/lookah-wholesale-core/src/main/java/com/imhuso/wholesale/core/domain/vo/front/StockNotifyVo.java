package com.imhuso.wholesale.core.domain.vo.front;

import com.imhuso.wholesale.core.domain.WhsStockNotify;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 批发到货通知视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsStockNotify.class)
public class StockNotifyVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 通知ID
     */
    private Long id;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 通知邮箱
     */
    private String email;

    /**
     * WhatsApp号码
     */
    private String whatsapp;

    /**
     * 状态
     */
    private String status;

    /**
     * 状态描述
     */
    private String statusInfo;

    /**
     * 通知时间
     */
    private Date notifyTime;

    /**
     * 创建时间
     */
    private Date createTime;
}
