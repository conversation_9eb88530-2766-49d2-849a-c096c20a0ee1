package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 订单主状态更新业务对象
 *
 * <AUTHOR>
 */
@Data
public class OrderStatusUpdateBo {

    /**
     * 订单状态
     */
    @NotNull(message = "订单状态不能为空", groups = {EditGroup.class})
    private Integer orderStatus;

    /**
     * 备注
     */
    private String remark;
}
