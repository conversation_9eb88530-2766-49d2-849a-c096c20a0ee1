package com.imhuso.wholesale.core.listener;

import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderVo;
import com.imhuso.wholesale.core.event.WhsOrderEvent;
import com.imhuso.wholesale.core.event.WhsOrderEvent.WhsOrderEventType;
import com.imhuso.wholesale.core.service.IOrderNotificationService;
import com.imhuso.wholesale.core.service.IWhsOrderQueryService;
import com.imhuso.wholesale.core.service.IWhsOrderShipmentQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.List;

/**
 * 订单通知事件监听器
 * 负责处理订单相关的通知事件，如邮件通知、短信通知等
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WhsOrderNotificationEventListener {

    private final IOrderNotificationService notificationService;
    private final IWhsOrderQueryService orderQueryService;
    private final IWhsOrderShipmentQueryService orderShipmentQueryService;

    /**
     * 处理订单通知事件
     * 异步执行，避免阻塞主流程
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleOrderEvent(WhsOrderEvent event) {
        try {
            Long orderId = event.getOrderId();
            if (orderId == null) {
                log.error("订单ID为空，无法处理通知事件");
                return;
            }

            // 获取完整的订单VO
            WhsOrderVo order = orderQueryService.getOrderDetail(orderId);
            if (order == null) {
                log.error("订单查询失败，无法处理通知: orderId={}", orderId);
                return;
            }

            // 如果是发货事件但订单没有发货记录，尝试重新查询
            WhsOrderEventType eventType = event.getEventType();
            if ((eventType == WhsOrderEventType.ORDER_SHIPPED || eventType == WhsOrderEventType.ORDER_PARTIAL_SHIPPED) &&
                (order.getShipment() == null || order.getShipment().isEmpty())) {
                    
                log.warn("订单[{}]没有发货记录但收到发货事件通知，尝试重新查询发货记录", orderId);
                try {
                    List<WhsOrderShipmentVo> shipments = orderShipmentQueryService.getOrderShipmentRecords(orderId);
                    if (shipments != null && !shipments.isEmpty()) {
                        order.setShipment(shipments);
                        log.info("订单[{}]重新查询到{}条发货记录", orderId, shipments.size());
                    } else {
                        log.error("订单[{}]重新查询后仍无发货记录，可能存在数据一致性问题", orderId);
                    }
                } catch (Exception e) {
                    log.error("订单[{}]重新查询发货记录失败: {}", orderId, e.getMessage());
                }
            }

            log.info("接收到订单通知事件：{}, 订单号：{}", event.getEventType(), order.getOrderNo());

            // 处理订单通知
            notificationService.processNotification(order, event.getEventType());
        } catch (Exception e) {
            log.error("处理订单通知事件出错：{}", e.getMessage(), e);
        }
    }
}
