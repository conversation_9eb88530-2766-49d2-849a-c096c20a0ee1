package com.imhuso.wholesale.core.service.impl;

import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.StringUtils;

import com.imhuso.wholesale.core.domain.WhsOrder;
import com.imhuso.wholesale.core.enums.InvoiceStatus;
import com.imhuso.wholesale.core.enums.OrderStatus;
import com.imhuso.wholesale.core.enums.PaymentStatus;
import com.imhuso.wholesale.core.enums.ShipmentStatus;
import com.imhuso.wholesale.core.event.WhsOrderEvent;
import com.imhuso.wholesale.core.event.WhsOrderEvent.WhsOrderEventType;
import com.imhuso.wholesale.core.mapper.WhsOrderMapper;
import com.imhuso.wholesale.core.satoken.WholesaleLoginHelper;
import com.imhuso.common.satoken.utils.LoginHelper;
import com.imhuso.wholesale.core.service.IWhsOrderStatusService;
import com.imhuso.wholesale.core.service.IWhsOrderShipmentApprovalService;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentApprovalVo;
import com.imhuso.wholesale.core.enums.ShipmentApprovalStatus;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.wholesale.core.utils.WhsEnumTranslationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.Date;
import java.util.Objects;

/**
 * 订单状态服务实现类
 * 统一使用主订单状态(OrderStatus)来管理订单生命周期
 * 职责：
 * 1. 处理订单状态变更
 * 2. 验证状态流转的合法性
 * 3. 更新相关联的状态
 * 4. 发布状态变更事件
 * 5. 作为统一的事件管理中心，负责转发订单状态事件
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsOrderStatusServiceImpl implements IWhsOrderStatusService {

    private final WhsOrderMapper orderMapper;
    private final ApplicationEventPublisher eventPublisher;
    private final IWhsOrderShipmentApprovalService approvalService;

    private static final int MAX_STATUS_VALUE = 9;

    /**
     * 智能获取当前操作用户ID
     * 优先尝试获取系统管理员用户ID，如果失败则尝试获取批发用户ID
     *
     * @return 当前操作用户ID，如果都获取失败则返回null
     */
    private Long getCurrentOperatorUserId() {
        log.info("开始获取当前操作用户ID...");

        // 首先尝试获取系统管理员用户ID（后台管理操作）
        try {
            log.info("尝试获取系统管理员用户ID...");
            boolean isLogin = LoginHelper.isLogin();
            log.info("系统管理员登录状态: {}", isLogin);

            if (isLogin) {
                Long adminUserId = LoginHelper.getUserId();
                log.info("获取到的系统管理员用户ID: {}", adminUserId);
                if (adminUserId != null && adminUserId > 0) {
                    log.info("成功获取到系统管理员用户ID: {}", adminUserId);
                    return adminUserId;
                }
            }
        } catch (Exception e) {
            log.warn("获取系统管理员用户ID失败: {}", e.getMessage(), e);
        }

        // 如果没有管理员用户，则尝试获取批发用户ID（前台操作）
        try {
            log.info("尝试获取批发用户ID...");
            boolean isLogin = WholesaleLoginHelper.isLogin();
            log.info("批发用户登录状态: {}", isLogin);

            if (isLogin) {
                Long wholesaleUserId = WholesaleLoginHelper.getUserId();
                log.info("获取到的批发用户ID: {}", wholesaleUserId);
                if (wholesaleUserId != null && wholesaleUserId > 0) {
                    log.info("成功获取到批发用户ID: {}", wholesaleUserId);
                    return wholesaleUserId;
                }
            }
        } catch (Exception e) {
            log.warn("获取批发用户ID失败: {}", e.getMessage(), e);
        }

        log.error("无法获取任何有效的用户ID，将使用默认值0");
        return null;
    }

    /**
     * 子状态变更结果
     */
    private static class SubStatusChangeResult {
        boolean anyChanged;
        boolean paymentChanged;
        boolean shipmentChanged;
        boolean invoiceChanged;

        SubStatusChangeResult() {
            this.anyChanged = false;
            this.paymentChanged = false;
            this.shipmentChanged = false;
            this.invoiceChanged = false;
        }
    }

    /**
     * 订单状态快照，用于保存更新前的状态
     */
    private static class OrderStatusSnapshot {
        final Integer orderStatus;
        final Integer paymentStatus;
        final Integer shipmentStatus;
        final Integer invoiceStatus;

        public OrderStatusSnapshot(WhsOrder order) {
            this.orderStatus = order.getOrderStatus();
            this.paymentStatus = order.getPaymentStatus();
            this.shipmentStatus = order.getShipmentStatus();
            this.invoiceStatus = order.getInvoiceStatus();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOrderStatus(Long orderId, Integer orderStatus, Integer paymentStatus, Integer shipmentStatus, Integer invoiceStatus, String remark) {
        // 1. 获取订单并验证
        WhsOrder order = getAndValidateOrder(orderId);
        if (order == null) {
            return false;
        }

        log.debug("开始更新订单状态: orderId={}, orderStatus={}, paymentStatus={}, shipmentStatus={}, invoiceStatus={}, remark={}", orderId, orderStatus, paymentStatus, shipmentStatus, invoiceStatus, remark);

        // 2. 保存当前状态用于比较
        OrderStatusSnapshot snapshot = new OrderStatusSnapshot(order);

        // 3. 更新子状态
        SubStatusChangeResult subStatusResult = updateSubStatuses(order, paymentStatus, shipmentStatus, invoiceStatus);

        // 4. 更新主状态
        boolean isOrderStatusChanged = updateMainOrderStatus(order, orderStatus, snapshot.orderStatus, subStatusResult);

        // 5. 保存更新到数据库
        saveOrderUpdate(order, orderId);

        // 6. 发布事件
        publishStatusChangeEvents(order, snapshot, subStatusResult, isOrderStatusChanged, orderStatus, remark);

        return true;
    }

    /**
     * 获取并验证订单
     */
    private WhsOrder getAndValidateOrder(Long orderId) {
        WhsOrder order = orderMapper.selectById(orderId);
        if (order == null) {
            log.error("更新订单状态失败，订单不存在: {}", orderId);
        }
        return order;
    }

    /**
     * 更新主订单状态
     */
    private boolean updateMainOrderStatus(WhsOrder order, Integer orderStatus, Integer currentOrderStatus, SubStatusChangeResult subStatusResult) {
        boolean isOrderStatusChanged = false;

        // 如果显式指定了订单状态，则设置订单状态并验证状态流转
        if (orderStatus != null && !orderStatus.equals(currentOrderStatus)) {
            // 如果要将订单设置为已完成，检查订单是否已完全发货
            if (orderStatus.equals(OrderStatus.COMPLETED.getValue())) {
                if (!ShipmentStatus.SHIPPED.getValue().equals(order.getShipmentStatus())) {
                    log.error("标记订单为已完成失败，订单未完全发货: orderId={}, shipmentStatus={}", order.getId(), order.getShipmentStatus());
                    throw new ServiceException("只有完全发货的订单才能标记为已完成");
                }
            }

            validateStatusTransition(currentOrderStatus, orderStatus);
            order.setOrderStatus(orderStatus);
            isOrderStatusChanged = true;
            log.debug("订单主状态已更新: orderId={}, 从{}变为{}", order.getId(), currentOrderStatus, orderStatus);
        }
        // 如果是支付状态或发票状态变更，且当前订单状态是待处理(PENDING)，则自动将订单状态更新为处理中(PROCESSING)
        else if (shouldAutoUpdateToProcessing(subStatusResult.paymentChanged, subStatusResult.invoiceChanged, currentOrderStatus)) {
            order.setOrderStatus(OrderStatus.PROCESSING.getValue());
            isOrderStatusChanged = true;
            log.info("由于{}状态变更，自动将订单状态从待处理更新为处理中", subStatusResult.paymentChanged ? "支付" : "发票");
        }

        return isOrderStatusChanged;
    }

    /**
     * 保存订单更新到数据库
     */
    private void saveOrderUpdate(WhsOrder order, Long orderId) {
        order.setUpdateTime(new Date());
        orderMapper.updateById(order);
        log.debug("已保存订单更新到数据库: orderId={}", orderId);
    }

    /**
     * 发布状态变更事件
     */
    private void publishStatusChangeEvents(WhsOrder order, OrderStatusSnapshot snapshot, SubStatusChangeResult subStatusResult, boolean isOrderStatusChanged, Integer orderStatus, String remark) {
        boolean anyEventPublished = false;

        // 发布主状态变更事件
        if (isOrderStatusChanged && orderStatus != null && !OrderStatus.DRAFT.getValue().equals(snapshot.orderStatus)) {
            log.debug("发布主状态变更事件: orderId={}, isOrderStatusChanged={}, orderStatus={}, previousOrderStatus={}", order.getId(), true, orderStatus, snapshot.orderStatus);
            publishOrderStatusChangedEvent(order, order.getOrderStatus(), remark);
            anyEventPublished = true;
        } else if (isOrderStatusChanged && orderStatus != null) {
            log.info("跳过主状态变更事件发布：这是从草稿状态转换: orderId={}, previousOrderStatus={}, newOrderStatus={}", order.getId(), snapshot.orderStatus, orderStatus);
        }

        // 发布子状态变更事件
        anyEventPublished |= publishSubStatusChangeEvents(order, snapshot, subStatusResult, remark);

        // 特殊处理：ORDER_CREATED事件
        handleOrderCreatedEvent(order, snapshot.orderStatus, anyEventPublished, remark);
    }

    /**
     * 发布子状态变更事件
     */
    private boolean publishSubStatusChangeEvents(WhsOrder order, OrderStatusSnapshot snapshot, SubStatusChangeResult subStatusResult, String remark) {
        boolean anyEventPublished = false;

        if (subStatusResult.paymentChanged) {
            publishPaymentStatusChangedEvent(order, snapshot.paymentStatus, order.getPaymentStatus(), remark);
            anyEventPublished = true;
        }

        if (subStatusResult.shipmentChanged) {
            publishShipmentStatusChangedEvent(order, snapshot.shipmentStatus, order.getShipmentStatus(), remark);
            anyEventPublished = true;
        }

        if (subStatusResult.invoiceChanged) {
            publishInvoiceStatusChangedEvent(order, snapshot.invoiceStatus, order.getInvoiceStatus(), remark);
            anyEventPublished = true;
        }

        return anyEventPublished;
    }

    /**
     * 处理ORDER_CREATED事件
     */
    private void handleOrderCreatedEvent(WhsOrder order, Integer previousOrderStatus, boolean anyEventPublished, String remark) {
        if (shouldPublishOrderCreatedEvent(anyEventPublished, order.getOrderStatus(), previousOrderStatus)) {
            log.info("特殊处理：检测到订单创建但未发布事件，强制发布ORDER_CREATED事件: orderId={}", order.getId());
            publishOrderStatusChangedEvent(order, OrderStatus.PENDING.getValue(), remark != null ? remark : "新订单创建");
        } else if (!anyEventPublished && OrderStatus.PENDING.getValue().equals(order.getOrderStatus())) {
            if (OrderStatus.DRAFT.getValue().equals(previousOrderStatus)) {
                log.info("跳过ORDER_CREATED事件发布：这是编辑草稿状态订单的操作: orderId={}, 从草稿状态({})转为待处理状态({})", order.getId(), previousOrderStatus, order.getOrderStatus());
            } else {
                log.info("跳过ORDER_CREATED事件发布：其他原因: orderId={}, previousOrderStatus={}, newOrderStatus={}", order.getId(), previousOrderStatus, order.getOrderStatus());
            }
        }
    }

    /**
     * 判断是否应该自动更新到处理中状态
     */
    private boolean shouldAutoUpdateToProcessing(boolean isPaymentStatusChanged, boolean isInvoiceStatusChanged, Integer currentOrderStatus) {
        return (isPaymentStatusChanged || isInvoiceStatusChanged) && Objects.equals(currentOrderStatus, OrderStatus.PENDING.getValue());
    }

    /**
     * 判断是否应该发布订单创建事件
     */
    private boolean shouldPublishOrderCreatedEvent(boolean anyEventPublished, Integer orderStatus, Integer previousOrderStatus) {
        log.debug("判断是否发布ORDER_CREATED事件: anyEventPublished={}, orderStatus={}, previousOrderStatus={}", anyEventPublished, orderStatus, previousOrderStatus);

        // 强制检查：如果之前的状态是草稿状态，绝对不发布ORDER_CREATED事件
        if (OrderStatus.DRAFT.getValue().equals(previousOrderStatus)) {
            log.info("强制跳过ORDER_CREATED事件：从草稿状态转换，previousOrderStatus={}", previousOrderStatus);
            return false;
        }

        if (anyEventPublished || !OrderStatus.PENDING.getValue().equals(orderStatus)) {
            log.debug("不发布ORDER_CREATED事件: anyEventPublished={} 或 orderStatus不是PENDING({})", anyEventPublished, orderStatus);
            return false;
        }

        // 只有真正的新订单创建（之前状态不是草稿）才发布ORDER_CREATED事件
        boolean shouldPublish = true;
        log.debug("ORDER_CREATED事件发布决定: shouldPublish={}, previousOrderStatus={}, DRAFT={}", shouldPublish, previousOrderStatus, OrderStatus.DRAFT.getValue());
        return shouldPublish;
    }

    /**
     * 验证订单状态流转是否合法
     */
    private void validateStatusTransition(Integer current, Integer target) {
        // 不能向无效的状态流转
        if (target == null) {
            throw new ServiceException("订单状态不能为空");
        }

        // 已取消的订单不能变更状态
        if (Objects.equals(current, OrderStatus.CANCELED.getValue())) {
            throw new ServiceException("订单不能变更状态：订单已取消");
        }

        // 已完成的订单不能变更状态
        if (Objects.equals(current, OrderStatus.COMPLETED.getValue())) {
            throw new ServiceException("订单不能变更状态：订单已完成");
        }

        // 如果是取消订单操作
        if (Objects.equals(target, OrderStatus.CANCELED.getValue())) {
            // 已完成不能取消订单
            return;
        }

        // 特殊情况：允许从PENDING/PROCESSING撤回到DRAFT状态
        if (Objects.equals(target, OrderStatus.DRAFT.getValue())) {
            // 只有PENDING或PROCESSING状态可以撤回到DRAFT
            if (!Objects.equals(current, OrderStatus.PENDING.getValue()) && !Objects.equals(current, OrderStatus.PROCESSING.getValue())) {
                throw new ServiceException("只有待处理或处理中的订单可以撤回到草稿状态");
            }
            return; // 允许撤回到草稿状态
        }

        // 草稿状态只能流转到待处理状态
        if (Objects.equals(current, OrderStatus.DRAFT.getValue())) {
            if (!Objects.equals(target, OrderStatus.PENDING.getValue())) {
                throw new ServiceException("草稿状态只能提交到待处理状态");
            }
            return; // 允许从草稿状态提交到待处理状态
        }

        // 其他情况：状态只能向前流转，不能回退
        if (target < current) {
            throw new ServiceException("订单状态不能回退");
        }
    }

    /**
     * 发布订单状态变更事件
     * 根据状态的不同发布对应类型的事件
     */
    private void publishOrderStatusChangedEvent(WhsOrder order, Integer statusCode, String remark) {
        if (statusCode == null) {
            return;
        }

        WhsOrderEventType eventType = getWhsOrderEventType(statusCode);

        // 获取当前用户ID
        Long userId = getCurrentOperatorUserId();

        // 发布事件，包含订单ID和操作用户ID
        eventPublisher.publishEvent(new WhsOrderEvent(this, eventType, order.getId(), remark, userId));
        log.info("已发布订单状态变更事件: orderId={}, status={}, eventType={}, userId={}", order.getId(), formatStatusText(statusCode, OrderStatus.class), eventType, userId);
    }

    @NotNull
    private static WhsOrderEventType getWhsOrderEventType(Integer statusCode) {
        WhsOrderEventType eventType;

        // 根据状态确定事件类型
        if (Objects.equals(statusCode, OrderStatus.PENDING.getValue())) {
            eventType = WhsOrderEventType.ORDER_CREATED;
        } else if (Objects.equals(statusCode, OrderStatus.PROCESSING.getValue())) {
            eventType = WhsOrderEventType.ORDER_PROCESSING;
        } else if (Objects.equals(statusCode, OrderStatus.COMPLETED.getValue())) {
            eventType = WhsOrderEventType.ORDER_COMPLETED;
        } else if (Objects.equals(statusCode, OrderStatus.CANCELED.getValue())) {
            eventType = WhsOrderEventType.ORDER_CANCELED;
        } else {
            // 对于其他状态，发布一个通用的状态变更事件
            eventType = WhsOrderEventType.ORDER_STATUS_CHANGED;
        }
        return eventType;
    }

    /**
     * 发布订单支付状态变更事件
     *
     * @param order     订单信息
     * @param oldStatus 旧支付状态
     * @param newStatus 新支付状态
     * @param remark    备注
     */
    private void publishPaymentStatusChangedEvent(WhsOrder order, Integer oldStatus, Integer newStatus, String remark) {
        if (newStatus == null) {
            return;
        }

        // 如果支付状态变为已支付，发布ORDER_PAID事件
        WhsOrderEventType eventType = WhsOrderEventType.ORDER_STATUS_CHANGED;
        if (Objects.equals(newStatus, PaymentStatus.PAID.getValue())) {
            eventType = WhsOrderEventType.ORDER_PAID;
        }

        // 获取当前用户ID
        Long userId = getCurrentOperatorUserId();

        // 发布事件
        String oldStatusText = WhsEnumTranslationUtils.translate(PaymentStatus.class, oldStatus);
        String newStatusText = WhsEnumTranslationUtils.translate(PaymentStatus.class, newStatus);
        String statusRemark = remark != null ? remark : "支付状态从 [" + oldStatusText + "] 变更为 [" + newStatusText + "]";
        eventPublisher.publishEvent(new WhsOrderEvent(this, eventType, order.getId(), statusRemark, userId));
        log.info("已发布订单支付状态变更事件: orderId={}, paymentStatus={}, eventType={}, userId={}", order.getId(), formatStatusText(newStatus, PaymentStatus.class), eventType, userId);
    }

    /**
     * 发布订单发货状态变更事件
     *
     * @param order     订单信息
     * @param oldStatus 旧发货状态
     * @param newStatus 新发货状态
     * @param remark    备注
     */
    private void publishShipmentStatusChangedEvent(WhsOrder order, Integer oldStatus, Integer newStatus, String remark) {
        if (newStatus == null) {
            return;
        }

        // 判断是否为首次发货（从未发货状态 PENDING(0)或PREPARING(1) 变为
        // 已发货SHIPPED(3)或部分发货PARTIAL_SHIPPED(2)）
        boolean isFirstShipment = (Objects.equals(oldStatus, ShipmentStatus.PENDING.getValue()) || Objects.equals(oldStatus, ShipmentStatus.PREPARING.getValue())) && (newStatus.equals(ShipmentStatus.SHIPPED.getValue()) || newStatus.equals(ShipmentStatus.PARTIAL_SHIPPED.getValue()));

        // 记录首次发货信息
        if (isFirstShipment) {
            log.info("检测到首次发货: orderId={}, 从{}({})变为{}({})", order.getId(), oldStatus, WhsEnumTranslationUtils.translate(ShipmentStatus.class, oldStatus), newStatus, WhsEnumTranslationUtils.translate(ShipmentStatus.class, newStatus));
        }

        // 注意：不再为发货状态变更发布 ORDER_SHIPPED 或 ORDER_PARTIAL_SHIPPED 事件
        // 因为这些事件已经在 WhsOrderShipmentServiceImpl 中为每个批次发布了
        // 这样可以避免重复通知

        // 只有当状态变为已送达(DELIVERED)或其他非发货状态时，才发布通用状态变更事件
        if (!newStatus.equals(ShipmentStatus.SHIPPED.getValue()) && !newStatus.equals(ShipmentStatus.PARTIAL_SHIPPED.getValue())) {

            // 获取当前用户ID
            Long userId = getCurrentOperatorUserId();

            // 发布通用状态变更事件
            String oldStatusText = WhsEnumTranslationUtils.translate(ShipmentStatus.class, oldStatus);
            String newStatusText = WhsEnumTranslationUtils.translate(ShipmentStatus.class, newStatus);
            String statusRemark = remark != null ? remark : "发货状态从 [" + oldStatusText + "] 变更为 [" + newStatusText + "]";

            eventPublisher.publishEvent(new WhsOrderEvent(this, WhsOrderEventType.ORDER_STATUS_CHANGED, order.getId(), statusRemark, userId));

            log.info("已发布订单发货状态变更事件: orderId={}, shipmentStatus={}, eventType={}, userId={}", order.getId(), formatStatusText(newStatus, ShipmentStatus.class), WhsOrderEventType.ORDER_STATUS_CHANGED, userId);
        } else {
            log.info("跳过发布发货状态变更事件(避免重复通知): orderId={}, shipmentStatus={}", order.getId(), formatStatusText(newStatus, ShipmentStatus.class));
        }
    }

    /**
     * 发布订单发票状态变更事件
     *
     * @param order     订单信息
     * @param oldStatus 旧发票状态
     * @param newStatus 新发票状态
     * @param remark    备注
     */
    private void publishInvoiceStatusChangedEvent(WhsOrder order, Integer oldStatus, Integer newStatus, String remark) {
        if (newStatus == null) {
            return;
        }

        // 如果发票状态变为已发送，发布ORDER_INVOICE_SENT事件
        WhsOrderEventType eventType = WhsOrderEventType.ORDER_STATUS_CHANGED;
        if (Objects.equals(newStatus, InvoiceStatus.SENT.getValue())) {
            eventType = WhsOrderEventType.ORDER_INVOICE_SENT;
        }

        // 获取当前用户ID
        Long userId = getCurrentOperatorUserId();

        // 发布事件
        String oldStatusText = WhsEnumTranslationUtils.translate(InvoiceStatus.class, oldStatus);
        String newStatusText = WhsEnumTranslationUtils.translate(InvoiceStatus.class, newStatus);
        String statusRemark = remark != null ? remark : "发票状态从 [" + oldStatusText + "] 变更为 [" + newStatusText + "]";
        eventPublisher.publishEvent(new WhsOrderEvent(this, eventType, order.getId(), statusRemark, userId));
        log.info("已发布订单发票状态变更事件: orderId={}, invoiceStatus={}, eventType={}, userId={}", order.getId(), formatStatusText(newStatus, InvoiceStatus.class), eventType, userId);
    }

    /**
     * 更新订单的子状态（支付状态、发货状态、发票状态）
     *
     * @param order          订单对象
     * @param paymentStatus  新的支付状态，null表示不更新
     * @param shipmentStatus 新的发货状态，null表示不更新
     * @param invoiceStatus  新的发票状态，null表示不更新
     * @return 子状态变更结果
     */
    private SubStatusChangeResult updateSubStatuses(WhsOrder order, Integer paymentStatus, Integer shipmentStatus, Integer invoiceStatus) {
        SubStatusChangeResult result = new SubStatusChangeResult();

        // 更新支付状态
        if (paymentStatus != null && !Objects.equals(paymentStatus, order.getPaymentStatus())) {
            // 确保传入的支付状态值符合数据库char(1)字段的要求
            if (paymentStatus >= 0 && paymentStatus <= MAX_STATUS_VALUE) {
                order.setPaymentStatus(paymentStatus);
                result.anyChanged = true;
                result.paymentChanged = true;

                log.debug("更新支付状态: 从{}变为{}", order.getPaymentStatus(), paymentStatus);
            } else {
                log.warn("忽略无效的支付状态值: {}", paymentStatus);
            }
        }

        // 更新发货状态
        if (shipmentStatus != null && !Objects.equals(shipmentStatus, order.getShipmentStatus())) {
            // 确保传入的发货状态值符合数据库char(1)字段的要求
            if (shipmentStatus >= 0 && shipmentStatus <= MAX_STATUS_VALUE) {
                order.setShipmentStatus(shipmentStatus);
                result.anyChanged = true;
                result.shipmentChanged = true;

                log.debug("更新发货状态: 从{}变为{}", order.getShipmentStatus(), shipmentStatus);
            } else {
                log.warn("忽略无效的发货状态值: {}", shipmentStatus);
            }
        }

        // 更新发票状态
        if (invoiceStatus != null && !Objects.equals(invoiceStatus, order.getInvoiceStatus())) {
            // 确保传入的发票状态值符合数据库char(1)字段的要求
            if (invoiceStatus >= 0 && invoiceStatus <= MAX_STATUS_VALUE) {
                order.setInvoiceStatus(invoiceStatus);
                result.anyChanged = true;
                result.invoiceChanged = true;

                log.debug("更新发票状态: 从{}变为{}", order.getInvoiceStatus(), invoiceStatus);
            } else {
                log.warn("忽略无效的发票状态值: {}", invoiceStatus);
            }
        }

        return result;
    }

    /**
     * 格式化状态文本，格式为：状态码(状态描述)
     *
     * @param statusCode  状态码
     * @param statusClass 状态枚举类
     * @return 格式化的状态文本
     */
    private <T extends Enum<T>> String formatStatusText(Integer statusCode, Class<T> statusClass) {
        String statusText = WhsEnumTranslationUtils.translate(statusClass, statusCode);
        return statusCode + "(" + statusText + ")";
    }

    /**
     * 订单状态变更事务提交后重新发布事件
     * 作为事件转发中心，确保所有事件在事务提交后被正确处理
     * 所有订单相关事件都应该通过这个方法转发，确保统一管理
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleOrderStatusChanged(WhsOrderEvent event) {
        try {
            // 详细记录事件信息
            log.info("接收到订单事件(事务已提交): orderId={}, eventType={}, source={}, operatorId={}", event.getOrderId(), event.getEventType(), event.getSource().getClass().getSimpleName(), event.getOperatorId());

            // 验证订单是否存在
            WhsOrder order = orderMapper.selectById(event.getOrderId());
            if (order == null) {
                log.error("事件转发失败：订单不存在, orderId={}", event.getOrderId());
                return;
            }

            // 创建新的事件实例并转发，避免使用原事件可能存在的潜在问题
            WhsOrderEvent newEvent = new WhsOrderEvent(this, event.getEventType(), event.getOrderId(), event.getRemark(), event.getOperatorId());

            log.info("转发订单事件: orderId={}, eventType={}, remark={}", newEvent.getOrderId(), newEvent.getEventType(), StringUtils.isNotEmpty(newEvent.getRemark()) ? (newEvent.getRemark().length() > 50 ? newEvent.getRemark().substring(0, 50) + "..." : newEvent.getRemark()) : "");

            // 发布新事件
            eventPublisher.publishEvent(newEvent);
        } catch (Exception e) {
            // 捕获并记录任何异常，但不重新抛出，以防影响主业务流程
            log.error("订单事件转发过程中发生异常: orderId={}, eventType={}, error={}", event.getOrderId(), event.getEventType(), e.getMessage(), e);
        }
    }

    /**
     * 撤回订单到草稿状态
     * 包含清理折扣金额和重置所有状态
     *
     * @param orderId 订单ID
     * @param remark  撤回原因
     * @return 操作是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean revertOrderToDraft(Long orderId, String remark) {
        log.info("撤回订单到草稿状态: orderId={}, remark={}", orderId, remark);

        // 获取订单信息
        WhsOrder order = orderMapper.selectById(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在: " + orderId);
        }

        // 清理折扣金额
        order.setDiscountAmount(java.math.BigDecimal.ZERO);

        // 重置所有状态到初始状态
        order.setOrderStatus(OrderStatus.DRAFT.getValue());
        order.setPaymentStatus(PaymentStatus.PENDING.getValue());
        order.setShipmentStatus(ShipmentStatus.PENDING.getValue());
        order.setInvoiceStatus(InvoiceStatus.PENDING.getValue());

        // 更新修改时间和修改人
        order.setUpdateTime(new Date());
        if (WholesaleLoginHelper.isLogin()) {
            order.setUpdateBy(WholesaleLoginHelper.getUserId());
        }

        // 执行更新
        int result = orderMapper.updateById(order);
        if (result <= 0) {
            log.error("撤回订单失败: orderId={}", orderId);
            return false;
        }

        // 发布订单撤回事件
        Long userId = getCurrentOperatorUserId();
        eventPublisher.publishEvent(new WhsOrderEvent(this, WhsOrderEventType.ORDER_REVERTED, orderId, remark, userId));

        log.info("订单撤回成功: orderId={}", orderId);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean forceCompleteOrder(Long orderId, String reason) {
        Long userId = LoginHelper.getUserId();
        String username = LoginHelper.getUsername();

        log.info("开始强制完成订单: orderId={}, userId={}, username={}, reason={}",
            orderId, userId, username, reason);

        // 1. 参数验证
        if (orderId == null) {
            log.error("强制完成订单失败：订单ID不能为空, userId={}, username={}", userId, username);
            return false;
        }
        if (StringUtils.isBlank(reason)) {
            log.error("强制完成订单失败：必须提供强制完成的原因, orderId={}, userId={}, username={}",
                orderId, userId, username);
            throw new ServiceException(MessageUtils.message("wholesale.order.force.complete.reason.required"));
        }

        // 2. 获取订单并验证
        WhsOrder order = getAndValidateOrder(orderId);
        if (order == null) {
            return false;
        }

        // 3. 检查订单当前状态
        log.debug("检查订单状态: orderId={}, currentStatus={}, shipmentStatus={}",
            orderId, order.getOrderStatus(), order.getShipmentStatus());

        if (OrderStatus.COMPLETED.getValue().equals(order.getOrderStatus())) {
            log.warn("订单已经是完成状态，无需重复操作: orderId={}, userId={}, username={}",
                orderId, userId, username);
            return true;
        }

        if (OrderStatus.CANCELED.getValue().equals(order.getOrderStatus())) {
            log.error("强制完成订单失败，已取消的订单不能强制完成: orderId={}, userId={}, username={}",
                orderId, userId, username);
            throw new ServiceException(MessageUtils.message("wholesale.order.force.complete.canceled.not.allowed"));
        }

        // 4. 检查订单状态限制：只有处理中的订单才能强制完成
        if (!OrderStatus.PROCESSING.getValue().equals(order.getOrderStatus())) {
            log.error("强制完成订单失败，只有处理中的订单才能强制完成: orderId={}, currentStatus={}", orderId, order.getOrderStatus());
            throw new ServiceException(MessageUtils.message("wholesale.order.force.complete.not.processing"));
        }

        // 5. 检查发货状态限制：必须已经发过货（部分发货或已发货）
        if (!ShipmentStatus.PARTIAL_SHIPPED.getValue().equals(order.getShipmentStatus())
            && !ShipmentStatus.SHIPPED.getValue().equals(order.getShipmentStatus())) {
            log.error("强制完成订单失败，只有已经发过货的订单才能强制完成: orderId={}, shipmentStatus={}", orderId, order.getShipmentStatus());
            throw new ServiceException(MessageUtils.message("wholesale.order.force.complete.not.shipped"));
        }

        log.info("开始强制完成订单: orderId={}, reason={}", orderId, reason);

        // 6. 保存当前状态用于比较
        OrderStatusSnapshot snapshot = new OrderStatusSnapshot(order);

        // 7. 处理相关的审批流程
        handleApprovalForForceComplete(orderId, reason);

        // 8. 强制设置为已完成状态（已通过前置条件检查）
        order.setOrderStatus(OrderStatus.COMPLETED.getValue());

        // 9. 保存更新到数据库
        saveOrderUpdate(order, orderId);

        // 10. 发布强制完成事件
        publishForceCompleteEvent(order, snapshot, reason);

        log.info("订单强制完成成功: orderId={}, userId={}, username={}, reason={}, " +
            "originalStatus={}, finalStatus={}, operationTime={}",
            orderId, userId, username, reason,
            snapshot.orderStatus, OrderStatus.COMPLETED.getValue(), new Date());
        return true;
    }

    /**
     * 发布强制完成事件
     */
    private void publishForceCompleteEvent(WhsOrder order, OrderStatusSnapshot snapshot, String reason) {
        // 获取当前用户ID
        Long userId = getCurrentOperatorUserId();

        // 构建强制完成的备注信息，只包含强制完成的原因
        String forceCompleteRemark = String.format("强制完成订单，原因：%s", reason);

        // 发布订单状态变更事件
        eventPublisher.publishEvent(new WhsOrderEvent(this, WhsOrderEventType.ORDER_STATUS_CHANGED, order.getId(), forceCompleteRemark, userId));
        log.info("已发布订单强制完成事件: orderId={}, userId={}, reason={}",
            order.getId(), userId, reason);
    }

    /**
     * 处理强制完成订单时的审批流程
     * 如果订单有待审批的申请，将其状态设置为已撤销
     *
     * @param orderId 订单ID
     * @param reason  强制完成的原因，将作为审批撤销的原因
     * @apiNote 此方法不会抛出异常，审批处理失败不影响订单强制完成
     */
    private void handleApprovalForForceComplete(Long orderId, String reason) {
        try {
            // 查询订单的最新审批状态
            WhsOrderShipmentApprovalVo latestApproval = approvalService.queryLatestByOrderId(orderId);

            if (latestApproval != null && ShipmentApprovalStatus.PENDING.getValue().equals(latestApproval.getApprovalStatus())) {
                // 如果有待审批的申请，撤销它
                log.info("订单强制完成时发现待审批申请，将自动撤销: orderId={}, approvalId={}", orderId, latestApproval.getId());

                // 创建撤销原因
                String cancelReason = String.format("订单强制完成，自动撤销审批申请。强制完成原因：%s", reason);

                // 调用审批服务撤销申请
                // 使用系统级别的撤销方法，不检查申请人权限
                boolean cancelResult = approvalService.cancelApprovalRequestBySystem(latestApproval.getId(), cancelReason);

                if (cancelResult) {
                    log.info("订单强制完成时成功撤销审批申请: orderId={}, approvalId={}", orderId, latestApproval.getId());
                } else {
                    log.warn("订单强制完成时撤销审批申请失败: orderId={}, approvalId={}", orderId, latestApproval.getId());
                }
            } else {
                log.debug("订单强制完成时无需处理审批流程: orderId={}, approvalStatus={}",
                    orderId, latestApproval != null ? latestApproval.getApprovalStatus() : "无审批记录");
            }
        } catch (Exception e) {
            // 审批处理失败不应该影响强制完成操作
            log.error("订单强制完成时处理审批流程失败: orderId={}, error={}", orderId, e.getMessage(), e);
        }
    }
}
