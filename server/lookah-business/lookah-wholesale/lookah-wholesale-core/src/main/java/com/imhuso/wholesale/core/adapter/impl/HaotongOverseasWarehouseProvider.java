package com.imhuso.wholesale.core.adapter.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.adapter.AbstractOverseasWarehouseProvider;
import com.imhuso.wholesale.core.constant.HaoTongApiConstants;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.vo.warehouse.*;
import com.imhuso.wholesale.core.enums.InboundOrderStatus;
import com.imhuso.wholesale.core.service.ILogisticsMethodInfoService;
import com.imhuso.wholesale.core.service.IWhsProductVariantService;
import com.imhuso.wholesale.core.utils.HaoTongApiUtils;
import com.imhuso.wholesale.core.utils.SkuMappingUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 昊天海外仓提供商实现
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HaotongOverseasWarehouseProvider extends AbstractOverseasWarehouseProvider {

    /**
     * 物流方法信息服务
     */
    private final ILogisticsMethodInfoService logisticsMethodInfoService;

    /**
     * 商品变体服务
     */
    private final IWhsProductVariantService variantService;

    // -------------------------------------------------------------------------
    // 常量定义
    // -------------------------------------------------------------------------

    private static final String PROVIDER_TYPE = "HAOTONG";

    /**
     * 每次查询的最大产品数量
     */
    private static final int MAX_BATCH_SIZE = 500;

    // 日志消息常量
    private static final String LOG_SYNC_START = "昊通海外仓同步入库单 - 查询开始日期: {}，查询天数: {}";
    private static final String LOG_PAGE_PROCESSING = "昊通API调用完成，开始处理第{}页响应数据";
    private static final String LOG_PAGE_EMPTY = "昊通入库单第{}页没有数据，这是正常情况";
    private static final String LOG_ORDER_BAGS_CREATED = "根据 pieceCount: {} 创建了 {} 个 OrderBags";
    private static final String LOG_API_ERROR = "同步昊通入库单失败 - 第{}页: {}";
    private static final String LOG_PAGE_CONTINUE = "第{}页数据获取失败，但继续处理已获取的{}页数据";
    private static final String LOG_PROCESSED_RECORDS = "已处理{}条入库单记录";
    private static final String LOG_PARSE_SUCCESS = "成功解析{}条入库单记录，成功{}条";

    /**
     * 批次号前缀
     */
    private static final String SYNC_BATCH_PREFIX = "SYNC-";

    // -------------------------------------------------------------------------
    // IOverseasWarehouseProvider接口实现
    // -------------------------------------------------------------------------

    @Override
    public String getProviderType() {
        return PROVIDER_TYPE;
    }

    // -------------------------------------------------------------------------
    // 仓库配置相关方法
    // -------------------------------------------------------------------------

    /**
     * 获取仓库ID，安全处理转换异常
     *
     * @param context 仓库上下文
     * @return 仓库ID，如果转换失败则返回null
     */
    private Integer getWarehouseId(WarehouseContextVo context) {
        String warehouseIdStr = getWarehouseConfig(context, HaoTongApiConstants.CONFIG_WAREHOUSE_ID);
        try {
            return Convert.toInt(warehouseIdStr);
        } catch (Exception e) {
            log.warn("仓库ID转换失败: {}", warehouseIdStr);
            return null;
        }
    }

    /**
     * 获取必需的仓库ID，如果无效则抛出异常
     *
     * @param context 仓库上下文
     * @return 仓库ID
     * @throws ServiceException 如果仓库ID无效
     */
    private Integer getRequiredWarehouseId(WarehouseContextVo context) {
        Integer warehouseId = getWarehouseId(context);
        if (warehouseId == null) {
            throw new ServiceException("无法解析有效的仓库ID，请检查仓库配置");
        }
        return warehouseId;
    }

    // -------------------------------------------------------------------------
    // 库存同步相关
    // -------------------------------------------------------------------------

    @Override
    public StockSyncResultVo syncStock(WarehouseContextVo context, List<WhsProductVariant> variants) {
        log.info("昊天海外仓开始同步库存，变体数量：{}", variants != null ? variants.size() : 0);

        if (variants == null || variants.isEmpty()) {
            // 返回空结果
            StockSyncResultVo result = new StockSyncResultVo();
            result.setSuccess(true);
            result.setProcessedCount(0);
            result.setSuccessCount(0);
            result.setFailedCount(0);
            result.setSkippedCount(0);
            return result;
        }

        try {
            // 从上下文中获取账号配置
            String customerCode = getRequiredAccountConfig(context, HaoTongApiConstants.CONFIG_CUSTOMER_CODE);
            String apiKey = getRequiredAccountConfig(context, HaoTongApiConstants.CONFIG_API_KEY);
            Integer warehouseId = getRequiredWarehouseId(context);

            // 获取当前应使用的产品映射策略
            String mappingStrategy = getCurrentMappingStrategy(context);
            log.info("昊天海外仓使用[{}]映射策略进行库存同步", mappingStrategy);

            // 根据映射策略提取产品编码
            List<String> externalCodes = extractProductCodes(variants, mappingStrategy);

            if (externalCodes.isEmpty()) {
                log.warn("未找到有效的外部编码({}), 无法同步库存", mappingStrategy);
                StockSyncResultVo result = new StockSyncResultVo();
                result.setSuccess(true);
                result.setProcessedCount(0);
                result.setSuccessCount(0);
                result.setFailedCount(0);
                result.setSkippedCount(0);
                return result;
            }

            // 创建结果对象
            StockSyncResultVo result = new StockSyncResultVo();
            result.setSuccess(true);
            result.getExtraData().put("syncBatchNo", SYNC_BATCH_PREFIX + System.currentTimeMillis());
            result.getExtraData().put("mappingStrategy", mappingStrategy);

            // 自行分页处理
            int totalSize = externalCodes.size();
            int totalBatches = (int) Math.ceil((double) totalSize / MAX_BATCH_SIZE);

            log.info("昊天海外仓库存同步开始，共{}个产品，分{}批处理，每批最多{}个", totalSize, totalBatches, MAX_BATCH_SIZE);

            // 记录映射策略到上下文，以便后续处理
            context.setCurrentMappingStrategy(mappingStrategy);

            for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                // 计算当前批次的起始和结束索引
                int fromIndex = batchIndex * MAX_BATCH_SIZE;
                int toIndex = Math.min(fromIndex + MAX_BATCH_SIZE, totalSize);

                // 获取当前批次的产品编码
                List<String> batchCodes = externalCodes.subList(fromIndex, toIndex);

                log.info("查询昊天海外仓库存，批次：{}/{}，查询产品数量：{}", batchIndex + 1, totalBatches, batchCodes.size());

                // 查询当前批次库存
                Map<String, Object> responseMap = HaoTongApiUtils.searchInventory(customerCode, apiKey, warehouseId,
                    batchCodes, batchCodes.size(), 1);

                // 处理查询结果
                StockSyncResultVo batchResult = parseStockResponse(responseMap, batchCodes, mappingStrategy);

                // 如果当前批次查询失败，整个同步失败
                if (!batchResult.isSuccess()) {
                    log.error("批次{}/{}查询失败：{}", batchIndex + 1, totalBatches, batchResult.getErrorMessage());
                    return batchResult; // 返回错误结果
                }

                // 合并库存更新结果
                result.getStockUpdates().putAll(batchResult.getStockUpdates());

                // 合并物品结果
                result.getItemResults().putAll(batchResult.getItemResults());

                // 累计成功和跳过的记录数
                result.setSuccessCount(result.getSuccessCount() + batchResult.getSuccessCount());
                result.setSkippedCount(result.getSkippedCount() + batchResult.getSkippedCount());

                log.debug("批次{}/{}处理完成，成功：{}，跳过：{}", batchIndex + 1, totalBatches, batchResult.getSuccessCount(),
                    batchResult.getSkippedCount());
            }

            // 设置最终处理数量
            result.setProcessedCount(externalCodes.size());

            // 记录同步结果日志
            log.info("昊天海外仓库存同步完成：总批次数={}, 总产品数={}, 成功数量={}, 跳过数量={}", totalBatches, totalSize,
                result.getSuccessCount(), result.getSkippedCount());

            return result;

        } catch (Exception e) {
            log.error("昊天海外仓同步库存异常", e);

            // 创建错误结果
            StockSyncResultVo result = new StockSyncResultVo();
            result.setSuccess(false);
            result.setErrorMessage("同步库存失败: " + e.getMessage());
            result.setProcessedCount(variants.size());
            result.setFailedCount(variants.size());
            result.setSuccessCount(0);
            result.setSkippedCount(0);

            return result;
        }
    }

    /**
     * 根据映射策略从变体列表中提取产品编码
     *
     * @param variants        变体列表
     * @param mappingStrategy 映射策略
     * @return 产品编码列表
     */
    private List<String> extractProductCodes(List<WhsProductVariant> variants, String mappingStrategy) {
        if (variants == null || variants.isEmpty()) {
            return Collections.emptyList();
        }

        if ("UPC".equals(mappingStrategy)) {
            List<String> upcCodes = variants.stream().map(WhsProductVariant::getUpc)
                .filter(code -> code != null && !code.isEmpty()).distinct().collect(Collectors.toList());

            if (!upcCodes.isEmpty()) {
                return upcCodes;
            }

            // 如果UPC为空，回退到SKU
            log.warn("未找到有效的UPC编码，回退使用SKU编码");
        }

        // 默认或回退时使用SKU
        return variants.stream().map(WhsProductVariant::getSkuCode).filter(code -> code != null && !code.isEmpty())
            .distinct().collect(Collectors.toList());
    }

    /**
     * 获取当前应使用的映射策略
     *
     * @param context 仓库上下文
     * @return 映射策略类型
     */
    private String getCurrentMappingStrategy(WarehouseContextVo context) {
        // 1. 优先使用context中已设置的当前策略
        if (context.getCurrentMappingStrategy() != null) {
            return context.getCurrentMappingStrategy();
        }

        // 2. 其次使用仓库配置中指定的映射策略
        String configStrategy = getWarehouseConfig(context, "mappingStrategyType");
        if (configStrategy != null && !configStrategy.isEmpty()) {
            if ("SKU".equals(configStrategy) || "UPC".equals(configStrategy)) {
                return configStrategy;
            }
        }

        // 3. 最后使用提供商默认策略
        return super.getMappingStrategyType();
    }

    /**
     * 解析库存同步响应
     *
     * @param responseMap     响应数据
     * @param externalCodes   请求的外部编码列表
     * @param mappingStrategy 使用的映射策略
     * @return 库存同步结果
     */
    @SuppressWarnings("unchecked")
    private StockSyncResultVo parseStockResponse(Map<String, Object> responseMap, List<String> externalCodes,
                                                 String mappingStrategy) {
        StockSyncResultVo result = new StockSyncResultVo();

        // 自定义批次号，用于日志追踪
        String syncBatchNo = SYNC_BATCH_PREFIX + System.currentTimeMillis();
        result.getExtraData().put("syncBatchNo", syncBatchNo);
        result.getExtraData().put("mappingStrategy", mappingStrategy);

        // 验证API响应是否包含Records数据
        Object recordsObj = responseMap.get(HaoTongApiConstants.RESP_RECORDS);
        List<Map<String, Object>> records = new ArrayList<>();

        if (recordsObj instanceof List<?>) {
            try {
                records = (List<Map<String, Object>>) recordsObj;
            } catch (ClassCastException e) {
                log.error("响应数据类型不匹配，无法转换为List<Map<String, Object>>: {}", e.getMessage());
            }
        }

        // 如果API返回了Records数据，则认为同步成功
        if (!records.isEmpty()) {
            result.setSuccess(true);
        } else if (HaoTongApiUtils.isSuccess(responseMap)) {
            // 如果API标记为成功但没有Records数据，也认为成功（可能是没有库存数据）
            result.setSuccess(true);
        } else {
            // 其他情况标记为失败
            result.setSuccess(false);
            result.setErrorMessage(HaoTongApiUtils.getErrorMessage(responseMap));
            return result;
        }

        // 处理返回的库存数据
        int successCount = 0;
        int skippedCount = 0;

        if (!records.isEmpty()) {
            // 确定响应中用于标识产品的字段，这是根据API的实际响应格式来决定的
            // 昊天API返回中可能包含SKU但不包含UPC，所以始终使用SKU字段解析
            // 但我们会根据映射策略来区分结果中的编码含义
            String codeField = HaoTongApiConstants.RESP_SKU;

            for (Map<String, Object> record : records) {
                String externalCode = String.valueOf(record.getOrDefault(codeField, ""));
                Integer quantity = Convert.toInt(record.get(HaoTongApiConstants.RESP_CUSTOMER_QTY));
                Integer usedQty = Convert.toInt(record.get(HaoTongApiConstants.RESP_CUSTOMER_USED_QTY));

                // 计算可用库存 = 总库存 - 占用库存
                int availableStock = quantity - usedQty;

                if (availableStock > 0) {
                    // 库存大于0，视为同步成功
                    result.addItemResult(externalCode, "success", availableStock);
                    successCount++;
                } else {
                    // 库存为0，视为跳过
                    result.addItemResult(externalCode, "skipped", 0);
                    skippedCount++;
                }
            }
        }

        // 只记录日志，显示哪些编码在海外仓中不存在
        if (externalCodes != null && log.isInfoEnabled()) {
            Set<String> returnedCodes = new HashSet<>(result.getItemResults().keySet());
            List<String> notFoundCodes = new ArrayList<>();

            for (String code : externalCodes) {
                if (!returnedCodes.contains(code)) {
                    notFoundCodes.add(code);

                    // 将未找到的SKU添加到itemResults中(使用特殊状态"not_found")，但不添加到库存更新列表
                    // 这样在日志和API响应中可以清晰看到哪些SKU不存在，但不会影响实际库存更新
                    result.addItemResult(code, "not_found", null);
                }
            }

            if (!notFoundCodes.isEmpty()) {
                log.info("海外仓库中找不到以下SKU编码 (状态: not_found): {}", String.join(", ", notFoundCodes));

                // 将未找到的SKU信息存储到结果的extraData中，但不计入处理统计或更新操作
                result.getExtraData().put("notFoundCodes", notFoundCodes);
                result.getExtraData().put("notFoundCount", notFoundCodes.size());
            }
        }

        // 设置统计信息 - 重要修改：processedCount应只计算API实际返回的记录数，不是请求的总数
        result.setProcessedCount(result.getItemResults().size());
        result.setSuccessCount(successCount);
        result.setSkippedCount(skippedCount);
        result.setFailedCount(0);

        // 日志记录库存同步结果
        if (log.isDebugEnabled()) {
            log.debug("库存同步结果({} 映射): 成功={}, 处理数量={}, 成功数量={}, 跳过数量={}", mappingStrategy, result.isSuccess(),
                result.getProcessedCount(), result.getSuccessCount(), result.getSkippedCount());
        }

        return result;
    }

    // -------------------------------------------------------------------------
    // 订单创建与管理相关
    // -------------------------------------------------------------------------

    @Override
    public CreateOrderResultVo createShipmentOrder(WarehouseContextVo context, Map<String, Object> orderData) {
        log.info("昊天海外仓开始创建订单，订单参考号: {}", orderData.get(HaoTongApiConstants.PARAM_CS_REF_NO));

        try {
            // 从上下文中获取账号配置
            String customerCode = getRequiredAccountConfig(context, HaoTongApiConstants.CONFIG_CUSTOMER_CODE);
            String apiKey = getRequiredAccountConfig(context, HaoTongApiConstants.CONFIG_API_KEY);
            Integer warehouseId = getRequiredWarehouseId(context);

            // 转换订单数据为昊通API格式
            Map<String, Object> haotongRequestMap = convertToHaotongOrderRequest(orderData);

            // 手动设置 warehouseId (HaoTongApiUtils.createOrder 不处理此字段)
            haotongRequestMap.put(HaoTongApiConstants.PARAM_WAREHOUSE_ID_2, warehouseId);

            // 调用API创建订单 (customerCode 由 HaoTongApiUtils 内部处理)
            Map<String, Object> responseMap = HaoTongApiUtils.createOrder(customerCode, apiKey, haotongRequestMap);

            // 解析响应并构建结果
            CreateOrderResultVo.CreateOrderResultVoBuilder resultBuilder = CreateOrderResultVo.builder();
            if (HaoTongApiUtils.isSuccess(responseMap)) {
                log.info("昊天海外仓订单创建成功，参考号: {}", orderData.get(HaoTongApiConstants.PARAM_CS_REF_NO));
                resultBuilder.success(true).trackingNo((String) responseMap.get(HaoTongApiConstants.RESP_TRACKING_NO))
                    .orderNo((String) responseMap.get(HaoTongApiConstants.RESP_ORDER_NO))
                    .csRefNo((String) responseMap.get(HaoTongApiConstants.RESP_CS_REF_NO));
            } else {
                String errorMessage = HaoTongApiUtils.getErrorMessage(responseMap);
                log.error("昊天海外仓订单创建失败，参考号: {}，错误信息: {}", orderData.get(HaoTongApiConstants.PARAM_CS_REF_NO),
                    errorMessage);
                resultBuilder.success(false).errorMessage(errorMessage);
            }
            return resultBuilder.build();

        } catch (Exception e) {
            // 保留详细的错误日志，用于后端排查
            log.error("昊天海外仓创建订单异常，参考号: {}", orderData.get(HaoTongApiConstants.PARAM_CS_REF_NO), e);
            // 返回用户友好的通用错误信息
            return CreateOrderResultVo.builder().success(false).errorMessage("创建昊通订单时发生内部错误，请联系技术支持。") // 修改为用户友好的消息
                // 尝试包含参考号以便追踪, 使用 Convert.toStr 确保安全
                .csRefNo(Convert.toStr(orderData.get(HaoTongApiConstants.PARAM_CS_REF_NO))).build();
        }
    }

    /**
     * 将内部订单数据转换为昊通API /AddOrder 请求体格式
     *
     * @param orderData 内部订单数据 Map (应包含 logisticsMethodId)
     * @return 昊通API请求Map
     */
    private Map<String, Object> convertToHaotongOrderRequest(Map<String, Object> orderData) {
        Map<String, Object> request = new HashMap<>();

        // Checklist[2]: 从 orderData 中获取 logisticsMethodId 并校验 - 使用常量
        Object logisticsMethodIdObj = orderData.get(HaoTongApiConstants.PARAM_LOGISTICS_METHOD_ID);
        if (logisticsMethodIdObj == null) {
            log.error("创建昊通订单请求失败：缺少物流方式ID ({})", HaoTongApiConstants.PARAM_LOGISTICS_METHOD_ID);
            throw new IllegalArgumentException("物流方式ID (" + HaoTongApiConstants.PARAM_LOGISTICS_METHOD_ID + ") 不能为空");
        }
        Long logisticsMethodId;
        try {
            logisticsMethodId = Convert.toLong(logisticsMethodIdObj);
            if (logisticsMethodId == null) {
                throw new NullPointerException(); // 触发下面的 catch
            }
        } catch (Exception e) {
            log.error("创建昊通订单请求失败：无效的物流方式ID格式 ({}): {}", HaoTongApiConstants.PARAM_LOGISTICS_METHOD_ID,
                logisticsMethodIdObj);
            throw new IllegalArgumentException(
                "无效的物流方式ID格式 (" + HaoTongApiConstants.PARAM_LOGISTICS_METHOD_ID + "): " + logisticsMethodIdObj);
        }

        // Checklist[3-6]: 查询 StoreWarehouseLogisticsMethod 并获取 channelId, 进行校验
        String channelId = logisticsMethodInfoService.getChannelId(logisticsMethodId);
        if (StrUtil.isBlank(channelId)) {
            log.error("创建昊通订单请求失败：未找到有效的物流方式配置或渠道ID无效，LogisticsMethodId: {}", logisticsMethodId);
            throw new IllegalArgumentException("未找到有效的物流方式配置或渠道ID无效，LogisticsMethodId: " + logisticsMethodId);
        }

        // 仅添加API文档定义的必填字段
        // 注意: CustomerCode 和 WarehouseID 在 createShipmentOrder 方法中添加
        // 使用 Convert.toStr() 确保 CsRefNo 是字符串，防止 ClassCastException - 使用常量
        request.put(HaoTongApiConstants.PARAM_CS_REF_NO,
            Convert.toStr(orderData.get(HaoTongApiConstants.PARAM_CS_REF_NO)));
        // 添加 SecondCsRefNo - 使用常量
        request.put(HaoTongApiConstants.PARAM_SECOND_CS_REF_NO,
            Convert.toStr(orderData.get(HaoTongApiConstants.PARAM_SECOND_CS_REF_NO)));

        // 使用查询到的 channelId 设置 ChannelID - 使用常量
        request.put(HaoTongApiConstants.PARAM_CHANNEL_ID, channelId);

        // 添加之前遗漏的收件人信息
        request.put(HaoTongApiConstants.PARAM_RECIPIENT_NAME, orderData.get(HaoTongApiConstants.PARAM_RECIPIENT_NAME));
        request.put(HaoTongApiConstants.PARAM_RECIPIENT_COMPANY_NAME, orderData.get(HaoTongApiConstants.PARAM_RECIPIENT_COMPANY_NAME));
        request.put(HaoTongApiConstants.PARAM_RECIPIENT_COUNTRY,
            orderData.get(HaoTongApiConstants.PARAM_RECIPIENT_COUNTRY));
        request.put(HaoTongApiConstants.PARAM_RECIPIENT_ADDRESS1,
            orderData.get(HaoTongApiConstants.PARAM_RECIPIENT_ADDRESS1));
        request.put(HaoTongApiConstants.PARAM_RECIPIENT_STATE,
            orderData.get(HaoTongApiConstants.PARAM_RECIPIENT_STATE));
        request.put(HaoTongApiConstants.PARAM_RECIPIENT_CITY, orderData.get(HaoTongApiConstants.PARAM_RECIPIENT_CITY));
        request.put(HaoTongApiConstants.PARAM_RECIPIENT_ZIPCODE,
            orderData.get(HaoTongApiConstants.PARAM_RECIPIENT_ZIPCODE));
        request.put(HaoTongApiConstants.PARAM_RECIPIENT_CONTACT,
            orderData.get(HaoTongApiConstants.PARAM_RECIPIENT_CONTACT));

        // 使用常量替换字符串字面量
        request.put(HaoTongApiConstants.PARAM_TOTAL_WEIGHT, orderData.get(HaoTongApiConstants.PARAM_TOTAL_WEIGHT));
        request.put(HaoTongApiConstants.PARAM_SHIPPING_WEIGHT,
            orderData.get(HaoTongApiConstants.PARAM_SHIPPING_WEIGHT));
        request.put(HaoTongApiConstants.PARAM_LENGTH, orderData.get(HaoTongApiConstants.PARAM_LENGTH));
        request.put(HaoTongApiConstants.PARAM_WIDTH, orderData.get(HaoTongApiConstants.PARAM_WIDTH));
        request.put(HaoTongApiConstants.PARAM_HEIGHT, orderData.get(HaoTongApiConstants.PARAM_HEIGHT));
        request.put(HaoTongApiConstants.PARAM_BILL_QTY, orderData.get(HaoTongApiConstants.PARAM_BILL_QTY));
        request.put(HaoTongApiConstants.PARAM_IS_BATTERY_FLAG,
            orderData.get(HaoTongApiConstants.PARAM_IS_BATTERY_FLAG));
        request.put(HaoTongApiConstants.PARAM_ORDER_STATUS, orderData.get(HaoTongApiConstants.PARAM_ORDER_STATUS));

        // 添加新增字段
        request.put(HaoTongApiConstants.PARAM_IS_SIGN_FLAG, orderData.get(HaoTongApiConstants.PARAM_IS_SIGN_FLAG));
        request.put(HaoTongApiConstants.PARAM_CUS_REMARK, orderData.get(HaoTongApiConstants.PARAM_CUS_REMARK));

        // 订单明细 (OrderDetails) - 必填 - 使用常量
        Object detailsObj = orderData.get(HaoTongApiConstants.PARAM_ORDER_DETAILS);
        if (detailsObj instanceof List) {
            @SuppressWarnings("unchecked") // 抑制 detailsObj 的强制转换警告
            List<Map<String, Object>> internalDetails = (List<Map<String, Object>>) detailsObj;
            List<Map<String, Object>> haotongDetails = internalDetails.stream().map(item -> {
                Map<String, Object> detail = new HashMap<>();
                // 仅添加 OrderDetails 内部的必填字段 - 使用常量
                detail.put(HaoTongApiConstants.PARAM_DETAIL_SKU, item.get(HaoTongApiConstants.PARAM_DETAIL_SKU));
                detail.put(HaoTongApiConstants.PARAM_DETAIL_CN_NAME,
                    item.get(HaoTongApiConstants.PARAM_DETAIL_CN_NAME));
                detail.put(HaoTongApiConstants.PARAM_DETAIL_EN_NAME,
                    item.get(HaoTongApiConstants.PARAM_DETAIL_EN_NAME));
                detail.put(HaoTongApiConstants.PARAM_DETAIL_QUANTITY,
                    item.get(HaoTongApiConstants.PARAM_DETAIL_QUANTITY));
                detail.put(HaoTongApiConstants.PARAM_DETAIL_SINGLE_WEIGHT,
                    item.get(HaoTongApiConstants.PARAM_DETAIL_SINGLE_WEIGHT));
                detail.put(HaoTongApiConstants.PARAM_DETAIL_SINGLE_PRICE,
                    item.get(HaoTongApiConstants.PARAM_DETAIL_SINGLE_PRICE));
                return detail;
            }).collect(Collectors.toList());
            request.put(HaoTongApiConstants.PARAM_ORDER_DETAILS, haotongDetails);
        } else {
            // 保持对必需字段的校验
            throw new IllegalArgumentException(
                "订单明细 (" + HaoTongApiConstants.PARAM_ORDER_DETAILS + ") 不能为空且必须是 List<Map<String, Object>> 类型");
        }

        // 创建 OrderBags
        Integer pieceCount = Convert.toInt(orderData.get(HaoTongApiConstants.PARAM_BILL_QTY));
        List<Map<String, Object>> orderBags = createOrderBags(orderData, pieceCount);
        request.put(HaoTongApiConstants.PARAM_ORDER_BAGS, orderBags);

        return request;
    }

    /**
     * 同步物流渠道
     *
     * @param context 仓库上下文
     * @return 同步结果
     */
    @Override
    @SuppressWarnings("unchecked") // 抑制方法内部处理 dataObj 时的强制转换警告
    public LogisticsChannelSyncResultVo syncLogisticsChannels(WarehouseContextVo context) {
        log.info("昊天海外仓开始同步物流渠道");
        LogisticsChannelSyncResultVo result = new LogisticsChannelSyncResultVo();

        try {
            // 从上下文中获取账号配置
            String customerCode = getRequiredAccountConfig(context, HaoTongApiConstants.CONFIG_CUSTOMER_CODE);
            String apiKey = getRequiredAccountConfig(context, HaoTongApiConstants.CONFIG_API_KEY);

            // 调用API获取渠道信息
            Map<String, Object> response = HaoTongApiUtils.getChannels(customerCode, apiKey);

            if (!HaoTongApiUtils.isSuccess(response)) {
                String errorMessage = HaoTongApiUtils.getErrorMessage(response);
                log.error("同步昊通渠道失败: {}", errorMessage);
                result.setSuccess(false);
                result.setErrorMessage(errorMessage);
                return result;
            }

            // 解析渠道信息
            Object dataObj = response.get(HaoTongApiConstants.RESP_DATA);
            if (dataObj == null) {
                log.warn("昊通渠道数据为空");
                result.setSuccess(true);
                result.setProcessedCount(0);
                return result;
            }

            // fix
            List<Map<String, Object>> channels;
            if (dataObj instanceof List) {
                channels = (List<Map<String, Object>>) dataObj;
            } else if (dataObj instanceof Map
                && ((Map<String, Object>) dataObj).containsKey(HaoTongApiConstants.RESP_CHANNELS)) {
                channels = (List<Map<String, Object>>) ((Map<String, Object>) dataObj)
                    .get(HaoTongApiConstants.RESP_CHANNELS);
            } else {
                log.error("无法解析昊通渠道数据: {}", dataObj);
                result.setSuccess(false);
                result.setErrorMessage("无法解析昊通渠道数据");
                return result;
            }

            if (CollUtil.isEmpty(channels)) {
                log.warn("昊通渠道列表为空");
                result.setSuccess(true);
                result.setProcessedCount(0);
                return result;
            }

            // 处理返回的渠道数据
            result.setSuccess(true);
            result.setProcessedCount(channels.size());
            result.setSuccessCount(channels.size());

            // 将渠道数据存储到结果中
            for (Map<String, Object> channel : channels) {
                String channelId = String.valueOf(channel.get(HaoTongApiConstants.RESP_CHANNEL_ID));
                String channelName = String.valueOf(channel.get(HaoTongApiConstants.RESP_CHANNEL_NAME));

                // 只有ID和名称都有值的记录才有效
                if (channelId != null && !channelId.isEmpty() && channelName != null && !channelName.isEmpty()) {
                    result.addChannelResult(channelId, "added");
                    result.getExtraData().put("channel_" + channelId, channel);
                } else {
                    log.warn("无效的渠道记录: {}", channel);
                    result.addWarning("发现无效的渠道记录: " + channel);
                }
            }

            log.info("昊通渠道同步完成, 共{}个渠道", result.getSuccessCount());
            return result;

        } catch (Exception e) {
            log.error("昊通渠道同步异常", e);
            result.setSuccess(false);
            result.setErrorMessage("同步渠道失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 将ShipmentInfoVo转换为昊通海外仓API所需的数据格式
     *
     * @param context      仓库上下文
     * @param shipmentInfo 发货信息
     * @param packageId    包裹ID (用于 SecondCsRefNo)
     * @param batchNumber  批次号 (用于 CsRefNo 格式化, >1 时添加后缀)
     * @return 转换后的Map
     */
    @Override
    public Map<String, Object> convertShipmentInfo(WarehouseContextVo context, ShipmentInfoVo shipmentInfo,
                                                   Long packageId, Integer batchNumber) {
        Map<String, Object> orderData = new HashMap<>();

        // 从 context 获取 warehouseId (假设 getRequiredWarehouseId 返回 Integer)
        Integer warehouseId = getRequiredWarehouseId(context);

        // 从 context 获取仓库 code
        String warehouseCode = null;
        if (context.getWarehouse() != null && StringUtils.isNotBlank(context.getWarehouse().getCode())) {
            warehouseCode = context.getWarehouse().getCode();
            log.debug("使用仓库编码 {} 作为发货单号区分", warehouseCode);
        } else {
            log.warn("仓库编码为空，将使用仓库ID {} 作为发货单号区分", warehouseId);
        }

        // 基本信息 - 使用常量 - 实现 CsRefNo 格式化
        String csRefNoValue = shipmentInfo.getOrderNo(); // 基础订单号
        // 设置RefNo, 优先使用仓库编码，如果为空则使用仓库ID
        csRefNoValue = setRefNo(warehouseId, warehouseCode, csRefNoValue, batchNumber);
        orderData.put(HaoTongApiConstants.PARAM_CS_REF_NO, csRefNoValue);

        // 从账号配置中获取客户编码 - 使用常量
        String customerCode = getRequiredAccountConfig(context, HaoTongApiConstants.CONFIG_CUSTOMER_CODE);
        orderData.put(HaoTongApiConstants.PARAM_CUSTOMER_CODE, customerCode);
        // 添加 SecondCsRefNo (包裹ID) - 使用常量
        orderData.put(HaoTongApiConstants.PARAM_SECOND_CS_REF_NO, packageId);

        // 将物流方式ID放入Map，键使用常量，以供 createShipmentOrder -> convertToHaotongOrderRequest 使用
        orderData.put(HaoTongApiConstants.PARAM_LOGISTICS_METHOD_ID, shipmentInfo.getLogisticsMethodId());

        // 收件人信息 - 从shipmentInfo中获取 - 使用常量
        orderData.put(HaoTongApiConstants.PARAM_RECIPIENT_NAME, shipmentInfo.getRecipientName());
        orderData.put(HaoTongApiConstants.PARAM_RECIPIENT_COMPANY_NAME, 
            StringUtils.defaultIfBlank(shipmentInfo.getRecipientCompanyName(), ""));
        orderData.put(HaoTongApiConstants.PARAM_RECIPIENT_PHONE,
            StringUtils.defaultIfBlank(shipmentInfo.getRecipientPhone(), ""));
        orderData.put(HaoTongApiConstants.PARAM_RECIPIENT_EMAIL,
            StringUtils.defaultIfBlank(shipmentInfo.getRecipientEmail(), ""));
        orderData.put(HaoTongApiConstants.PARAM_RECIPIENT_COUNTRY,
            StringUtils.defaultIfBlank(shipmentInfo.getCountry(), ""));
        orderData.put(HaoTongApiConstants.PARAM_RECIPIENT_STATE,
            StringUtils.defaultIfBlank(shipmentInfo.getProvince(), ""));
        orderData.put(HaoTongApiConstants.PARAM_RECIPIENT_CITY, StringUtils.defaultIfBlank(shipmentInfo.getCity(), ""));
        orderData.put(HaoTongApiConstants.PARAM_RECIPIENT_ADDRESS1,
            StringUtils.defaultIfBlank(shipmentInfo.getAddress(), ""));
        orderData.put(HaoTongApiConstants.PARAM_RECIPIENT_ZIPCODE,
            StringUtils.defaultIfBlank(shipmentInfo.getPostalCode(), ""));
        // 添加 RecipientContact
        orderData.put(HaoTongApiConstants.PARAM_RECIPIENT_CONTACT,
            StringUtils.defaultIfBlank(shipmentInfo.getRecipientPhone(), ""));

        // 包裹尺寸和重量 - 设置默认值 - 使用常量
        orderData.put(HaoTongApiConstants.PARAM_TOTAL_WEIGHT, 0.1);
        orderData.put(HaoTongApiConstants.PARAM_SHIPPING_WEIGHT, 0.1);
        orderData.put(HaoTongApiConstants.PARAM_LENGTH, 0.1);
        orderData.put(HaoTongApiConstants.PARAM_WIDTH, 0.1);
        orderData.put(HaoTongApiConstants.PARAM_HEIGHT, 0.1);

        // 必填字段 - 使用常量
        // 使用 shipmentInfo 中的 pieceCount 设置 BILL_QTY
        Integer pieceCount = shipmentInfo.getPieceCount();
        if (pieceCount != null && pieceCount > 0) {
            orderData.put(HaoTongApiConstants.PARAM_BILL_QTY, pieceCount);
            log.debug("使用 shipmentInfo 中的 pieceCount: {} 设置 BILL_QTY", pieceCount);
        } else {
            orderData.put(HaoTongApiConstants.PARAM_BILL_QTY, 1); // 默认1
            pieceCount = 1; // 确保后续使用
            log.warn("shipmentInfo 中未设置 pieceCount，使用默认值1");
        }

        orderData.put(HaoTongApiConstants.PARAM_IS_BATTERY_FLAG, "False"); // 改为 String "False"
        orderData.put(HaoTongApiConstants.PARAM_ORDER_STATUS, 1); // 改为 Integer 1

        // 设置签名服务 - 优先使用用户选择，其次考虑账号支持情况
        String isSignFlag; // 默认关闭

        // 首先检查用户是否选择了签名服务
        Boolean userSignatureChoice = shipmentInfo.getSignatureService();
        if (userSignatureChoice != null) {
            // 用户有明确选择
            if (userSignatureChoice) {
                // 用户选择启用，需要检查账号是否支持
                if (context.getAccount() != null &&
                    "1".equals(context.getAccount().getSupportSignatureService())) {
                    isSignFlag = "True";
                    log.debug("用户选择启用签名服务，且海外仓账号支持，设置为: {}", isSignFlag);
                } else {
                    isSignFlag = "False";
                    log.warn("用户选择启用签名服务，但海外仓账号不支持或账号信息为空，强制设置为: {}", isSignFlag);
                }
            } else {
                // 用户选择关闭签名服务
                isSignFlag = "False";
                log.debug("用户选择关闭签名服务，设置为: {}", isSignFlag);
            }
        } else {
            // 用户没有明确选择，使用账号默认配置
            if (context.getAccount() != null &&
                "1".equals(context.getAccount().getSupportSignatureService())) {
                isSignFlag = "True";
                log.debug("用户未选择签名服务，根据海外仓账号默认配置设置为: {}", isSignFlag);
            } else {
                isSignFlag = "False";
                log.debug("用户未选择签名服务，海外仓账号不支持或账号信息为空，设置为: {}", isSignFlag);
            }
        }
        orderData.put(HaoTongApiConstants.PARAM_IS_SIGN_FLAG, isSignFlag);
        orderData.put(HaoTongApiConstants.PARAM_CUS_REMARK, ""); // 默认空字符串

        // 处理发货项，转为HAOTONG需要的OrderDetails格式
        List<Map<String, Object>> orderDetails = new ArrayList<>();
        if (CollUtil.isNotEmpty(shipmentInfo.getItems())) {
            for (ShipmentInfoVo.ShipmentItemVo item : shipmentInfo.getItems()) {
                Map<String, Object> detailMap = new HashMap<>();
                // 使用常量
                detailMap.put(HaoTongApiConstants.PARAM_DETAIL_SKU, item.getSku());
                detailMap.put(HaoTongApiConstants.PARAM_DETAIL_CN_NAME,
                    StringUtils.defaultIfBlank(item.getProductName(), item.getSku())); // 中文名默认使用SKU
                detailMap.put(HaoTongApiConstants.PARAM_DETAIL_EN_NAME,
                    StringUtils.defaultIfBlank(item.getProductName(), item.getSku())); // 英文名默认使用SKU
                detailMap.put(HaoTongApiConstants.PARAM_DETAIL_QUANTITY, item.getQuantity());
                detailMap.put(HaoTongApiConstants.PARAM_DETAIL_SINGLE_WEIGHT, 0.1); // 默认单件重量
                detailMap.put(HaoTongApiConstants.PARAM_DETAIL_SINGLE_PRICE, 0.1); // 单价，默认0.1
                orderDetails.add(detailMap);
            }
        }

        // 确保至少有一个明细项 - 改为抛出异常
        if (orderDetails.isEmpty()) {
            log.error("订单 {} 没有明细项，无法创建发货单", shipmentInfo.getOrderId());
            throw new ServiceException("发货单必须包含至少一个商品明细，订单号: " + shipmentInfo.getOrderNo());
        }

        // 添加订单明细 - 使用常量
        orderData.put(HaoTongApiConstants.PARAM_ORDER_DETAILS, orderDetails);

        // 创建 OrderBags
        List<Map<String, Object>> orderBags = createOrderBags(orderData, pieceCount);
        orderData.put(HaoTongApiConstants.PARAM_ORDER_BAGS, orderBags);

        return orderData;
    }

    /**
     * 根据仓库信息、订单号和批次号设置RefNo，并且要排除两边空格
     *
     * @param warehouseId   仓库ID (备用，当仓库编码为空时使用)
     * @param warehouseCode 仓库编码 (优先使用)
     * @param orderNo       订单号
     * @param batchNo       批次号
     * @return 格式化后的 CsRefNo (例如: orderNo-warehouseCode 或
     * orderNo-warehouseCode-batchNo)
     */
    private String setRefNo(Integer warehouseId, String warehouseCode, String orderNo, Integer batchNo) {
        // 确定使用的仓库标识符 (优先使用仓库编码)
        String warehouseIdentifier = StringUtils.isNotBlank(warehouseCode) ? warehouseCode
            : String.valueOf(warehouseId);

        // 基础参考号格式: 订单号-仓库标识符
        String baseRefNo = orderNo + "-" + warehouseIdentifier;

        if (batchNo == null || batchNo <= 1) {
            return StringUtils.trim(baseRefNo);
        }

        // 如果有批次号，则添加批次号后缀
        return StringUtils.trim(baseRefNo + "-" + batchNo);
    }

    /**
     * 取消海外仓订单
     *
     * @param context            仓库上下文
     * @param packageId          包裹ID (用于日志记录)
     * @param externalShipmentId 外部系统发货单号 (昊天返回的OrderNo)
     * @return 取消结果
     */
    @Override
    public CancelOrderResultVo cancelOrder(WarehouseContextVo context, Long packageId, String externalShipmentId) {
        log.info("昊通海外仓开始取消订单，包裹ID: {}, 外部发货单号: {}", packageId, externalShipmentId);
        CancelOrderResultVo result = new CancelOrderResultVo();

        if (StringUtils.isBlank(externalShipmentId)) {
            log.error("无法取消昊通订单，外部发货单号(externalShipmentId)为空，包裹ID: {}", packageId);
            result.setSuccess(false);
            result.setErrorMessage("外部发货单号不能为空");
            return result;
        }

        try {
            // 从上下文中获取账号配置
            String customerCode = getRequiredAccountConfig(context, HaoTongApiConstants.CONFIG_CUSTOMER_CODE);
            String apiKey = getRequiredAccountConfig(context, HaoTongApiConstants.CONFIG_API_KEY);

            // 不再调用 getExternalOrderInfo
            // 直接使用传入的 externalShipmentId 和 OrderNoType = 1 (系统订单号)
            int orderNoType = 1;

            log.info("调用昊通取消API: customerCode={}, orderNoType={}, orderNo={}", customerCode, orderNoType,
                externalShipmentId);

            // 调用API取消订单，使用 orderNoType=1 和 externalShipmentId
            Map<String, Object> response = HaoTongApiUtils.cancelOrder(customerCode, apiKey, orderNoType,
                externalShipmentId);

            // 日志记录原始响应
            log.debug("昊通取消API响应: {}", response);

            if (!HaoTongApiUtils.isSuccess(response)) {
                String errorMessage = HaoTongApiUtils.getErrorMessage(response);
                log.error("取消昊通订单失败: {}", errorMessage);
                result.setSuccess(false);
                result.setErrorMessage(errorMessage);
                // 保留 orderNo (externalShipmentId)
                result.setOrderNo(externalShipmentId);
                return result;
            }

            // 设置取消成功的响应
            result.setSuccess(true);
            result.setOrderNo(externalShipmentId); // 使用取消时用的ID

            // 如果响应中包含csRefNo，则设置 (通常取消接口可能不返回这个)
            Object csRefNoObj = response.get(HaoTongApiConstants.RESP_CS_REF_NO);
            if (csRefNoObj != null) {
                result.setCsRefNo(csRefNoObj.toString());
            }

            log.info("昊通订单取消成功，包裹ID：{}，外部发货单号：{}", packageId, externalShipmentId);
            return result;

        } catch (Exception e) {
            log.error("昊通订单取消异常，包裹ID: {}, 外部发货单号: {}", packageId, externalShipmentId, e);
            result.setSuccess(false);
            result.setErrorMessage("取消订单时发生内部异常: " + e.getMessage());
            result.setOrderNo(externalShipmentId);
            return result;
        }
    }

    /**
     * 同步入库单
     *
     * @param context 仓库上下文
     * @return 同步结果
     */
    @Override
    public InboundOrderSyncResultVo syncInboundOrders(WarehouseContextVo context) {
        log.info("昊通海外仓开始同步入库单");
        InboundOrderSyncResultVo result = new InboundOrderSyncResultVo();
        long startTime = System.currentTimeMillis();

        try {
            // 从上下文中获取账号配置
            String customerCode = getRequiredAccountConfig(context, HaoTongApiConstants.CONFIG_CUSTOMER_CODE);
            String apiKey = getRequiredAccountConfig(context, HaoTongApiConstants.CONFIG_API_KEY);
            Integer warehouseId = getRequiredWarehouseId(context);
            log.info("昊通海外仓同步入库单 - 仓库ID: {}, 客户编码: {}", warehouseId, customerCode);

            // 从上下文获取查询天数，默认60天（定时任务约2个月），手动同步为365天（约1年）
            Integer daysBefore = context.getCustomData("daysBefore");
            if (daysBefore == null || daysBefore <= 0) {
                daysBefore = 60; // 默认查询60天（约2个月）
            }

            // 获取开始日期，使用yyyy-MM-dd格式
            String startDate = cn.hutool.core.date.DateUtil
                .format(cn.hutool.core.date.DateUtil.offsetDay(new Date(), -daysBefore), "yyyy-MM-dd");
            log.info(LOG_SYNC_START, startDate, daysBefore);

            // 初始化分页参数
            int pageIndex = 1;
            int pageSize = 50; // 每页最大记录数，平衡性能和稳定性
            int totalPages = 1;
            int totalRecords = 0;
            boolean hasMorePages = true;

            // 用于存储所有页的记录
            List<InboundOrderVo> allRecords = new ArrayList<>();

            // 分页查询所有入库单
            while (hasMorePages) {
                log.info("开始查询昊通入库单 - 第{}页，每页{}条记录", pageIndex, pageSize);

                // 调用API获取入库单信息
                log.debug("开始调用昊通API获取入库单数据 - 第{}页", pageIndex);
                Map<String, Object> response = HaoTongApiUtils.getInboundOrders(
                    customerCode,
                    apiKey,
                    warehouseId,
                    startDate,
                    null, // 结束日期设为null，获取更完整的数据
                    null, // 状态设为null，获取所有状态的入库单
                    pageSize,
                    pageIndex
                );
                log.debug(LOG_PAGE_PROCESSING, pageIndex);

                // 检查API调用是否成功
                if (!HaoTongApiUtils.isSuccess(response)) {
                    if (handleApiError(response, pageIndex, result)) {
                        return result; // 第一页失败，直接返回
                    } else {
                        break; // 非第一页失败，跳出循环
                    }
                }

                // 解析当前页数据
                InboundOrderSyncResultVo pageResult = new InboundOrderSyncResultVo();
                parseHaotongInboundOrderResponse(response, pageResult);

                // API调用成功，检查是否有数据
                if (pageResult.getRecords().isEmpty()) {
                    // 解析成功但没有记录
                    log.info(LOG_PAGE_EMPTY, pageIndex);

                    // 处理空结果
                    if (handleEmptyResult(result, pageIndex)) {
                        return result; // 如果是第一页，返回空结果
                    } else {
                        break; // 如果不是第一页，跳出循环
                    }
                }

                // 更新分页信息
                if (pageIndex == 1) {
                    // 第一页时初始化总页数和总记录数
                    totalPages = pageResult.getPageCount();
                    totalRecords = pageResult.getTotalCount();
                    log.info("昊通入库单总页数: {}, 总记录数: {}", totalPages, totalRecords);
                }

                // 添加当前页记录到总记录列表
                allRecords.addAll(pageResult.getRecords());
                log.info("已获取昊通入库单 - 第{}页，本页记录数: {}, 累计记录数: {}",
                    pageIndex, pageResult.getRecords().size(), allRecords.size());

                // 判断是否还有下一页
                hasMorePages = pageIndex < totalPages;
                pageIndex++;

                // 防止无限循环，设置最大页数限制
                if (pageIndex > 100) {
                    log.warn("达到最大页数限制(100页)，停止获取更多数据");
                    break;
                }
            }

            // 设置最终结果
            result.setSuccess(true);
            result.setPagination(1, pageSize, totalPages, totalRecords);
            result.getRecords().addAll(allRecords);
            result.setStatistics(allRecords.size(), allRecords.size(), 0, 0);

            // 记录处理结果
            long endTime = System.currentTimeMillis();
            log.info("昊通入库单同步完成, 共处理{}个入库单, 成功{}个, 失败{}个, 耗时{}毫秒",
                result.getProcessedCount(), result.getSuccessCount(), result.getFailedCount(),
                (endTime - startTime));
            return result;

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("昊通入库单同步异常, 耗时{}毫秒: {}", (endTime - startTime), e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage("同步入库单失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 解析昊通API入库单响应数据
     *
     * @param responseData 昊通API响应数据
     * @param result       入库单同步结果对象
     */
    @SuppressWarnings("unchecked")
    private void parseHaotongInboundOrderResponse(Map<String, Object> responseData, InboundOrderSyncResultVo result) {
        if (responseData == null) {
            result.setResult(false, "响应数据为空");
            return;
        }

        try {
            // 解析分页信息
            int pageIndex = Convert.toInt(responseData.get("PageIndex"), 1);
            int pageSize = Convert.toInt(responseData.get("PageSize"), 20);
            int pageCount = Convert.toInt(responseData.get("PageCount"), 0);
            int totalCount = Convert.toInt(responseData.get("TotalCount"), 0);

            // 设置分页信息
            result.setPagination(pageIndex, pageSize, pageCount, totalCount);

            log.info("解析昊通入库单响应 - 页码: {}/{}, 每页记录数: {}, 总记录数: {}",
                pageIndex, pageCount, pageSize, totalCount);

            // 解析入库单记录
            Object recordsObj = responseData.get("Records");
            if (recordsObj == null) {
                // Records为null，表示没有数据，这是正常情况
                log.info("昊通入库单响应中没有Records数据，表示没有入库单记录");
                // 设置空结果但标记为成功
                setEmptyResult(result);
            } else if (recordsObj instanceof List<?> records) {
                // 检查是否为空列表
                if (records.isEmpty()) {
                    log.info("昊通入库单响应中Records是空列表，表示没有入库单记录");
                    // 设置空结果但标记为成功
                    setEmptyResult(result);
                    return;
                }

                // 转换为具体类型
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> recordsList = (List<Map<String, Object>>) records;
                int successCount = 0;

                log.info("开始解析{}条入库单记录", recordsList.size());

                for (Map<String, Object> recordMap : recordsList) {
                    // 创建通用入库单对象
                    InboundOrderVo inboundOrder = new InboundOrderVo();

                    // 提取核心字段 - 基本信息
                    String asnNo = String.valueOf(recordMap.getOrDefault("AsnNo", ""));
                    if (StringUtils.isEmpty(asnNo)) {
                        log.warn("入库单号为空，跳过处理");
                        continue;
                    }

                    inboundOrder.setAsnNo(asnNo);
                    // 设置仓库ID
                    inboundOrder.setWarehouseId(Convert.toInt(recordMap.get("WareHouseID"), 0));
                    // 设置状态码
                    Integer statusCode = Convert.toInt(recordMap.get("Status"), null);
                    inboundOrder.setStatus(statusCode);

                    // 设置状态枚举
                    if (statusCode != null) {
                        InboundOrderStatus statusEnum = mapHaotongStatusToEnum(statusCode);
                        inboundOrder.setStatusEnum(statusEnum);
                        log.debug("入库单[{}]状态码[{}]映射为系统状态枚举[{}]", asnNo, statusCode, statusEnum.name());
                    } else {
                        log.warn("入库单[{}]状态码为空", asnNo);
                        // 默认为待入库
                        inboundOrder.setStatusEnum(InboundOrderStatus.PENDING_RECEIPT);
                    }
                    inboundOrder.setBags(Convert.toInt(recordMap.get("Bags"), 0));

                    // 处理日期字段
                    Object estimatedTimeObj = recordMap.get("EstimatedTime");
                    if (estimatedTimeObj != null) {
                        try {
                            inboundOrder.setEstimatedTime(
                                cn.hutool.core.date.DateUtil.parse(String.valueOf(estimatedTimeObj)));
                        } catch (Exception e) {
                            // 记录解析异常，但不中断处理流程
                            log.warn("解析预计到达时间异常, 值: {}, 错误: {}", estimatedTimeObj, e.getMessage());
                        }
                    }

                    Object createTimeObj = recordMap.get("CreateTime");
                    if (createTimeObj != null) {
                        try {
                            inboundOrder
                                .setCreateTime(cn.hutool.core.date.DateUtil.parse(String.valueOf(createTimeObj)));
                        } catch (Exception e) {
                            // 记录解析异常，但不中断处理流程
                            log.warn("解析创建时间异常, 值: {}, 错误: {}", createTimeObj, e.getMessage());
                        }
                    }

                    Object receiveTimeObj = recordMap.get("ReceiveTime");
                    if (receiveTimeObj != null) {
                        try {
                            inboundOrder
                                .setReceiveTime(cn.hutool.core.date.DateUtil.parse(String.valueOf(receiveTimeObj)));
                        } catch (Exception e) {
                            // 记录解析异常，但不中断处理流程
                            log.warn("解析接收时间异常, 值: {}, 错误: {}", receiveTimeObj, e.getMessage());
                        }
                    }

                    // 提取核心字段 - 备注和第三方系统信息
                    inboundOrder.setRemark(String.valueOf(recordMap.getOrDefault("Remark", "")));
                    inboundOrder.setThirdSystemNo(String.valueOf(recordMap.getOrDefault("ThirdSystemNO", "")));
                    inboundOrder.setThirdSystemUrl(String.valueOf(recordMap.getOrDefault("ThirdSystemUrl", "")));
                    inboundOrder.setThirdSystemRefNo(String.valueOf(recordMap.getOrDefault("ThirdSystemRefNo", "")));

                    // 处理入库单明细 - 提取核心字段
                    Object purchaseDetailsObj = recordMap.get("ASN_PurchaseDetails");
                    if (purchaseDetailsObj instanceof List) {
                        List<Map<String, Object>> detailsList = (List<Map<String, Object>>) purchaseDetailsObj;
                        log.debug("处理入库单[{}]明细，共{}条", asnNo, detailsList.size());

                        for (Map<String, Object> detailMap : detailsList) {
                            String sku = String.valueOf(detailMap.getOrDefault("SKU", ""));
                            if (StringUtils.isEmpty(sku)) {
                                log.warn("入库单[{}]明细SKU为空，跳过处理", asnNo);
                                continue;
                            }

                            InboundOrderVo.InboundOrderItemVo itemVo = new InboundOrderVo.InboundOrderItemVo();
                            itemVo.setSku(sku);

                            // 查询变体ID
                            try {
                                Long variantId = findVariantIdBySkuCode(sku);
                                if (variantId != null && variantId > 0) {
                                    itemVo.setVariantId(variantId);
                                    log.debug("入库单明细SKU[{}]对应的变体ID为: {}", sku, variantId);
                                } else {
                                    log.warn("未找到入库单明细SKU[{}]对应的变体ID", sku);
                                }
                            } catch (Exception e) {
                                log.error("查询SKU[{}]对应的变体ID时发生异常: {}", sku, e.getMessage());
                            }

                            // 提取核心数量字段
                            Integer quantity = Convert.toInt(detailMap.get("Quantity"), 0);
                            Integer checkQuantity = Convert.toInt(detailMap.get("CheckQuantity"),
                                0);
                            Integer breakQuantity = Convert.toInt(detailMap.get("BreakQuantity"),
                                0);
                            Integer uploadQuantity = Convert
                                .toInt(detailMap.get("UploadQuantity"), 0);

                            itemVo.setQuantity(quantity);
                            itemVo.setCheckQuantity(checkQuantity);
                            itemVo.setBreakQuantity(breakQuantity);
                            itemVo.setUploadQuantity(uploadQuantity);
                            itemVo.setRemark(String.valueOf(detailMap.getOrDefault("Remark", "")));

                            inboundOrder.getPurchaseDetails().add(itemVo);

                            log.debug("添加入库单[{}]明细: SKU={}, 预约数量={}, 上架数量={}",
                                asnNo, sku, quantity, uploadQuantity);
                        }
                    } else {
                        log.debug("入库单[{}]没有明细数据", asnNo);
                    }

                    // 处理物流信息 - 提取核心字段
                    Object logisticsObj = recordMap.get("ASN_PurchaseLogistics");
                    InboundOrderVo.LogisticsInfoVo logisticsVo = new InboundOrderVo.LogisticsInfoVo();
                    boolean hasLogisticsInfo;

                    hasLogisticsInfo = parseLogisticsInfo(logisticsObj, logisticsVo, asnNo);

                    // 只有在成功获取到物流信息时才设置
                    if (hasLogisticsInfo) {
                        inboundOrder.setPurchaseLogistics(logisticsVo);
                    }

                    // 添加到结果中
                    result.getRecords().add(inboundOrder);
                    successCount++;

                    if (successCount % 10 == 0) {
                        log.debug(LOG_PROCESSED_RECORDS, successCount);
                    }
                }

                // 设置处理统计
                result.setStatistics(recordsList.size(), successCount, 0, 0);
                result.setResult(true, null);
                log.info(LOG_PARSE_SUCCESS, recordsList.size(), successCount);
            } else {
                result.setResult(false, "响应数据格式错误：Records不是列表");
            }
        } catch (Exception e) {
            log.error("解析响应数据异常", e);
            result.setResult(false, "解析响应数据异常：" + e.getMessage());
        }
    }

    /**
     * 设置空结果
     *
     * @param result 结果对象
     */
    private void setEmptyResult(InboundOrderSyncResultVo result) {
        result.setPagination(1, 0, 0, 0);
        result.setStatistics(0, 0, 0, 0);
        result.setResult(true, null);
    }

    /**
     * 处理空结果
     *
     * @param result    结果对象
     * @param pageIndex 当前页码
     * @return 如果是第一页返回true，否则返回false
     */
    private boolean handleEmptyResult(InboundOrderSyncResultVo result, int pageIndex) {
        if (pageIndex == 1) {
            // 如果是第一页，设置空结果
            result.setSuccess(true);
            setEmptyResult(result);
            return true;
        } else {
            // 如果不是第一页，表示已经获取了所有数据
            log.info("已获取所有昊通入库单数据，共{}页", pageIndex - 1);
            return false;
        }
    }

    /**
     * 将昊通状态码映射到系统状态枚举
     *
     * @param statusCode 昊通状态码
     * @return 系统状态枚举
     */
    private InboundOrderStatus mapHaotongStatusToEnum(Integer statusCode) {
        if (statusCode == null) {
            return InboundOrderStatus.PENDING_RECEIPT; // 默认为待入库
        }

        // 直接使用 getByValue 方法获取对应的枚举值
        InboundOrderStatus status = InboundOrderStatus.getByValue(statusCode);
        if (status != null) {
            return status;
        }

        // 根据昊通状态码映射到系统状态枚举
        return switch (statusCode) {
            case 0 -> InboundOrderStatus.PENDING_RECEIPT; // 待入库
            case 1 -> InboundOrderStatus.PARTIALLY_RECEIVED; // 部分入库
            case 2 -> InboundOrderStatus.RECEIVED; // 已入库
            case 3 -> InboundOrderStatus.PARTIALLY_SHELVED; // 部分上架
            case 4 -> InboundOrderStatus.SHELVED; // 已上架
            case 10 -> InboundOrderStatus.CANCELLED; // 已作废
            default -> InboundOrderStatus.PENDING_RECEIPT; // 默认为待入库
        };
    }

    /**
     * 根据SKU编码查找对应的变体ID
     *
     * @param skuCode SKU编码
     * @return 变体ID，如果未找到则返回null
     */
    private Long findVariantIdBySkuCode(String skuCode) {
        if (StringUtils.isEmpty(skuCode)) {
            return null;
        }

        try {
            // 调用商品变体服务查询变体ID
            Map<String, Long> result = variantService.getVariantIdsBySkuCodes(Collections.singletonList(skuCode));
            if (result == null || result.isEmpty()) {
                return null;
            }

            // 使用工具类查找变体ID
            return SkuMappingUtils.findVariantIdBySku(result, skuCode);
        } catch (Exception e) {
            log.error("查询SKU[{}]对应的变体ID时发生异常: {}", skuCode, e.getMessage());
            return null;
        }
    }

    /**
     * 创建 OrderBags 列表
     *
     * @param orderData  订单数据
     * @param pieceCount 包裹数量
     * @return OrderBags 列表
     */
    private List<Map<String, Object>> createOrderBags(Map<String, Object> orderData, Integer pieceCount) {
        List<Map<String, Object>> orderBags = new ArrayList<>();

        if (pieceCount == null || pieceCount <= 0) {
            pieceCount = 1; // 默认至少一个包裹
        }

        for (int i = 0; i < pieceCount; i++) {
            Map<String, Object> bag = new HashMap<>();
            bag.put(HaoTongApiConstants.PARAM_BAGS_WEIGHT, orderData.get(HaoTongApiConstants.PARAM_TOTAL_WEIGHT));
            bag.put(HaoTongApiConstants.PARAM_BAGS_LENGTH, orderData.get(HaoTongApiConstants.PARAM_LENGTH));
            bag.put(HaoTongApiConstants.PARAM_BAGS_WIDTH, orderData.get(HaoTongApiConstants.PARAM_WIDTH));
            bag.put(HaoTongApiConstants.PARAM_BAGS_HEIGHT, orderData.get(HaoTongApiConstants.PARAM_HEIGHT));
            orderBags.add(bag);
        }

        log.debug(LOG_ORDER_BAGS_CREATED, pieceCount, orderBags.size());
        return orderBags;
    }

    /**
     * 处理API错误
     *
     * @param response  API响应
     * @param pageIndex 页码
     * @param result    同步结果
     * @return true表示第一页失败需要返回，false表示非第一页失败可以继续
     */
    private boolean handleApiError(Map<String, Object> response, int pageIndex, InboundOrderSyncResultVo result) {
        String errorMessage = HaoTongApiUtils.getErrorMessage(response);
        log.error(LOG_API_ERROR, pageIndex, errorMessage);

        // 如果是第一页就失败，则整个同步失败
        if (pageIndex == 1) {
            result.setSuccess(false);
            result.setErrorMessage(errorMessage);
            return true; // 需要返回
        } else {
            // 如果不是第一页，记录错误但继续处理已获取的数据
            log.warn(LOG_PAGE_CONTINUE, pageIndex, pageIndex - 1);
            return false; // 不需要返回，跳出循环即可
        }
    }

    /**
     * 解析物流信息
     *
     * @param logisticsObj 物流信息对象
     * @param logisticsVo  物流信息VO
     * @param asnNo        ASN号
     * @return 是否成功解析到物流信息
     */
    @SuppressWarnings("unchecked")
    private boolean parseLogisticsInfo(Object logisticsObj, InboundOrderVo.LogisticsInfoVo logisticsVo, String asnNo) {
        if (logisticsObj instanceof Map) {
            // 单个物流信息对象
            return parseLogisticsMap((Map<String, Object>) logisticsObj, logisticsVo, asnNo, "");
        } else if (logisticsObj instanceof List<?> logisticsList && !logisticsList.isEmpty()) {
            // 物流信息是数组的情况，使用第一个元素
            Object firstLogistics = logisticsList.get(0);
            if (firstLogistics instanceof Map) {
                return parseLogisticsMap((Map<String, Object>) firstLogistics, logisticsVo, asnNo, "(数组)");
            } else {
                log.warn("入库单[{}]物流信息数组中的元素不是Map类型，无法解析", asnNo);
                return false;
            }
        } else if (log.isDebugEnabled()) {
            log.debug("入库单[{}]没有物流信息或物流信息格式不正确", asnNo);
        }
        return false;
    }

    /**
     * 解析物流信息Map
     *
     * @param logisticsMap 物流信息Map
     * @param logisticsVo  物流信息VO
     * @param asnNo        ASN号
     * @param suffix       日志后缀
     * @return 是否成功解析
     */
    private boolean parseLogisticsMap(Map<String, Object> logisticsMap, InboundOrderVo.LogisticsInfoVo logisticsVo,
                                      String asnNo, String suffix) {
        Integer deliveryType = Convert.toInt(logisticsMap.get("DeliveryType"), null);
        String deliveryNo = String.valueOf(logisticsMap.getOrDefault("DeliveryNo", ""));

        logisticsVo.setDeliveryType(deliveryType);
        logisticsVo.setDeliveryNo(deliveryNo);

        log.debug("添加入库单[{}]物流信息{}: 单号={}", asnNo, suffix, deliveryNo);
        return true;
    }

    /**
     * 查询昊通订单信息
     * 使用 /SearchOrder API 查询订单详细信息，包括运费
     *
     * @param context     仓库上下文
     * @param queryParams 查询参数
     * @return 查询结果
     */
    public Map<String, Object> queryOrders(WarehouseContextVo context, Map<String, Object> queryParams) {
        log.info("昊通海外仓开始查询订单信息，参数: {}", queryParams);

        try {
            // 从上下文中获取账号配置
            String customerCode = getRequiredAccountConfig(context, HaoTongApiConstants.CONFIG_CUSTOMER_CODE);
            String apiKey = getRequiredAccountConfig(context, HaoTongApiConstants.CONFIG_API_KEY);

            // 提取查询参数
            String orderNosStr = (String) queryParams.get("OrderNoList");
            Integer pageSize = Convert.toInt(queryParams.get("PageSize"));
            Integer pageIndex = Convert.toInt(queryParams.get("PageIndex"));

            // 将逗号分隔的订单号字符串转换为数组
            List<String> orderNoList = null;
            if (StringUtils.isNotBlank(orderNosStr)) {
                orderNoList = Arrays.asList(orderNosStr.split(","));
            }

            // 调用昊通SearchOrder API查询订单（不传递OrderStatus，获取所有状态）
            Map<String, Object> response = HaoTongApiUtils.searchOrders(
                customerCode,
                apiKey,
                orderNoList,
                null, // 不传递OrderStatus，获取所有状态的订单
                pageSize,
                pageIndex);

            // 检查响应状态
            if (!HaoTongApiUtils.isSuccess(response)) {
                String errorMsg = HaoTongApiUtils.getErrorMessage(response);
                log.error("昊通订单查询失败: {}", errorMsg);
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("errorMessage", errorMsg);
                return result;
            }

            // 解析订单数据 - SearchOrder API返回的数据在Records字段中
            Object recordsObj = response.get("Records");
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", recordsObj);

            log.info("昊通订单查询成功，返回数据条数: {}",
                recordsObj instanceof List ? ((List<?>) recordsObj).size() : "未知");

            return result;

        } catch (Exception e) {
            log.error("昊通订单查询异常，参数: {}", queryParams, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("errorMessage", "查询订单信息时发生内部异常: " + e.getMessage());
            return result;
        }
    }

}
