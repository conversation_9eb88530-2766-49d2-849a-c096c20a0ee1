package com.imhuso.wholesale.core.enums;

import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.translation.core.EnumTranslatableInterface;
import lombok.Getter;

import java.util.Arrays;
import java.util.Locale;

/**
 * 订单状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum OrderStatus implements EnumTranslatableInterface {

    /**
     * 草稿状态
     */
    DRAFT(-1),

    /**
     * 待处理
     */
    PENDING(0),

    /**
     * 处理中
     */
    PROCESSING(1),

    /**
     * 已完成
     */
    COMPLETED(2),

    /**
     * 已取消
     */
    CANCELED(3);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 构造函数
     */
    OrderStatus(int value) {
        this.value = value;
    }

    /**
     * 获取指定语言环境下的状态国际化消息
     *
     * @param code   状态码
     * @param locale 语言环境
     * @return 国际化消息
     */
    public static String getMessageWithLocale(Integer code, Locale locale) {
        // 通过值查找对应的枚举实例
        OrderStatus status = Arrays.stream(OrderStatus.values())
            .filter(s -> s.getValue().equals(code))
            .findFirst()
            .orElse(null);

        if (status == null) {
            return null;
        }

        // 获取消息前缀 - 使用实例的方法获取前缀
        String messagePrefix = status.getMessagePrefix();

        // 从多语言文件中获取对应的翻译
        // Locale标准化处理已在MessageUtils内部完成
        return MessageUtils.messageWithLocale(messagePrefix + status.name().toLowerCase(), locale);
    }
}
