package com.imhuso.wholesale.core.utils.erp;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import com.fasterxml.jackson.core.type.TypeReference;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.json.utils.JsonUtils;
import com.imhuso.wholesale.core.config.ErpConfig;
import com.imhuso.wholesale.core.domain.dto.ErpApiResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * ERP HTTP请求工具类
 * 基于Hutool实现的ERP API调用工具
 *
 * <AUTHOR>
 */
@Slf4j
public class ErpHttpUtils {

    private ErpHttpUtils() {
        // 私有构造方法，防止实例化
    }


    /**
     * 发送GET请求到ERP系统（使用V2最佳实践签名）
     *
     * @param erpConfig    ERP配置
     * @param uri          请求URI
     * @param params       请求参数（支持String[]数组类型）
     * @param responseType 响应类型
     * @return 响应数据
     */
    public static <T> ErpApiResponse<T> sendGetRequestWithV2Signature(ErpConfig erpConfig, String uri,
                                                                      Map<String, String[]> params,
                                                                      TypeReference<ErpApiResponse<T>> responseType) {
        return sendRequestWithV2Signature(erpConfig, "GET", uri, params, null, responseType);
    }



    /**
     * 健康检查
     *
     * @param erpConfig ERP配置
     * @return 是否健康
     */
    public static boolean healthCheck(ErpConfig erpConfig) {
        if (!erpConfig.isEnabled()) {
            log.debug("ERP集成未启用");
            return false;
        }

        try {
            String url = erpConfig.getBaseUrl() + "/api/v1/health";
            String uri = "/api/v1/health";

            // 使用V2签名算法生成签名
            ErpSignatureUtils.SignatureResult signatureResult = ErpSignatureUtils.generateSignature(
                    "GET", uri, "", erpConfig.getAppSecret());

            // 创建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("X-App-Key", erpConfig.getAppKey());
            headers.put("X-Signature", signatureResult.getSignature());
            headers.put("X-Timestamp", signatureResult.getTimestamp());
            headers.put("X-Nonce", signatureResult.getNonce());

            log.info("ERP健康检查 - URL: {}", url);

            HttpRequest request = HttpRequest.get(url);
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                request.header(entry.getKey(), entry.getValue());
            }
            request.timeout(erpConfig.getConnectTimeout());

            try (HttpResponse response = request.execute()) {

                boolean isHealthy = response.getStatus() == HttpStatus.HTTP_OK;
                log.info("ERP健康检查结果: {}", isHealthy);
                return isHealthy;
            }
        } catch (Exception e) {
            log.error("ERP健康检查失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 构建查询字符串
     * 
     * @param params 请求参数
     * @return 查询字符串
     */
    private static String buildQueryString(Map<String, String[]> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }
        
        StringBuilder queryBuilder = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, String[]> entry : params.entrySet()) {
            String key = entry.getKey();
            String[] values = entry.getValue();
            for (String value : values) {
                if (!first) {
                    queryBuilder.append("&");
                }
                // 使用GoFrame V2标准的数组参数格式：key[]=value
                queryBuilder.append(key).append("[]=").append(value);
                first = false;
            }
        }
        return queryBuilder.toString();
    }

    /**
     * 将列表分批处理
     *
     * @param list      原始列表
     * @param batchSize 批次大小
     * @return 分批后的列表
     */
    public static <T> java.util.List<java.util.List<T>> splitIntoBatches(java.util.List<T> list, int batchSize) {
        java.util.List<java.util.List<T>> batches = new java.util.ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            int end = Math.min(i + batchSize, list.size());
            batches.add(list.subList(i, end));
        }
        return batches;
    }

    /**
     * 发送请求到ERP系统（使用V2最佳实践签名）
     *
     * @param erpConfig    ERP配置
     * @param method       HTTP方法
     * @param uri          请求URI
     * @param params       请求参数（支持String[]数组类型）
     * @param body         请求体（可选）
     * @param responseType 响应类型
     * @return 响应数据
     */
    private static <T> ErpApiResponse<T> sendRequestWithV2Signature(ErpConfig erpConfig, String method, String uri,
                                                                    Map<String, String[]> params, Object body,
                                                                    TypeReference<ErpApiResponse<T>> responseType) {
        if (!erpConfig.isEnabled()) {
            throw new ServiceException("ERP集成未启用");
        }

        try {
            // 构建完整URL
            String url = erpConfig.getBaseUrl() + uri;

            log.info("ERP请求V2 - URL: {}, Method: {}, Params: {}", url, method, params);

            // 发送请求
            HttpRequest request;
            String queryString = buildQueryString(params);
            
            if ("GET".equalsIgnoreCase(method)) {
                request = HttpRequest.get(url);
                if (!queryString.isEmpty()) {
                    log.debug("构建的查询字符串（V2）: {}", queryString);
                    request.body(queryString, "application/x-www-form-urlencoded");
                }
            } else if ("POST".equalsIgnoreCase(method)) {
                request = HttpRequest.post(url);
                if (body != null) {
                    String jsonBody = JsonUtils.toJsonString(body);
                    request.body(jsonBody);
                    log.debug("ERP请求体V2: {}", jsonBody);
                }
            } else {
                throw new ServiceException("不支持的HTTP方法: " + method);
            }

            // 使用V2签名算法生成签名
            ErpSignatureUtils.SignatureResult signatureResult = ErpSignatureUtils.generateSignature(
                    method, uri, queryString, erpConfig.getAppSecret());

            // 创建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("X-App-Key", erpConfig.getAppKey());
            headers.put("X-Signature", signatureResult.getSignature());
            headers.put("X-Timestamp", signatureResult.getTimestamp());
            headers.put("X-Nonce", signatureResult.getNonce());

            log.info("ERP请求头V2: {}", headers);

            // 设置请求头
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                request.header(entry.getKey(), entry.getValue());
            }
            request.timeout(erpConfig.getConnectTimeout());

            // 执行请求
            try (HttpResponse response = request.execute()) {
                // 检查响应状态
                if (response.getStatus() != HttpStatus.HTTP_OK) {
                    log.error("ERP API调用失败，状态码: {}", response.getStatus());
                    throw new ServiceException("ERP API调用失败，状态码: " + response.getStatus());
                }

                // 获取响应内容
                String responseBody = response.body();
                log.info("ERP API响应内容V2: {}", responseBody);

                // 解析响应
                ErpApiResponse<T> apiResponse = JsonUtils.parseObject(responseBody, responseType);

                // 检查业务状态码
                if (apiResponse != null && apiResponse.getCode() != 1000) {
                    log.error("ERP API返回错误: code={}, message={}", apiResponse.getCode(), apiResponse.getMessage());
                    throw new ServiceException("ERP API返回错误: " + apiResponse.getMessage());
                }

                return apiResponse;
            }

        } catch (Exception e) {
            log.error("ERP API请求异常V2: {}", e.getMessage(), e);
            if (e instanceof ServiceException) {
                throw (ServiceException) e;
            }
            throw new ServiceException("ERP API请求异常: " + e.getMessage());
        }
    }
}
