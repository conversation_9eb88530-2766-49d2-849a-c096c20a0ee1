package com.imhuso.wholesale.core.listener;

import com.imhuso.wholesale.core.event.WhsStockNotifyEvent;
import com.imhuso.wholesale.core.service.IStockNotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 缺货通知事件监听器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WhsStockNotifyEventListener {

    private final IStockNotificationService notificationService;

    /**
     * 处理缺货通知事件
     * 异步执行，避免阻塞主流程
     */
    @Async
    @EventListener
    public void handleStockNotifyEvent(WhsStockNotifyEvent event) {
        log.info("接收到缺货通知事件：{}, 通知ID：{}", event.getEventType(), event.getStockNotify().getId());
        try {
            notificationService.processNotification(event.getStockNotify(), event.getEventType());
        } catch (Exception e) {
            log.error("处理缺货通知事件异常", e);
        }
    }
}
