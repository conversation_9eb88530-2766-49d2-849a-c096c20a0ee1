package com.imhuso.wholesale.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发货审批操作类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ShipmentApprovalOperationType {

    /**
     * 提交申请
     */
    SUBMIT("SUBMIT", "提交申请"),

    /**
     * 审批通过
     */
    APPROVE("APPROVE", "审批通过"),

    /**
     * 审批拒绝
     */
    REJECT("REJECT", "审批拒绝"),

    /**
     * 撤销申请
     */
    CANCEL("CANCEL", "撤销申请");

    /**
     * 类型值
     */
    private final String value;

    /**
     * 类型描述
     */
    private final String description;
}
