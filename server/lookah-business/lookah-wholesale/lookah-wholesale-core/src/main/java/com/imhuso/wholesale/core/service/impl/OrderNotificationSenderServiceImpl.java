package com.imhuso.wholesale.core.service.impl;

import com.aizuda.snailjob.client.core.annotation.Retryable;
import com.aizuda.snailjob.client.core.retryer.RetryType;
import com.imhuso.wholesale.core.domain.bo.front.OrderNotifyTemplateBo;
import com.imhuso.wholesale.core.service.IWhsMailService;
import com.imhuso.wholesale.core.service.IOrderNotificationSenderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * 订单通知发送服务实现类
 * <p>
 * 专注于订单通知的发送功能，提供重试机制确保通知可靠送达。
 * 数据处理应由调用方完成。
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderNotificationSenderServiceImpl implements IOrderNotificationSenderService {

    private final IWhsMailService mailService;

    /**
     * 发送订单客户通知
     * <p>
     * 发送客户邮件通知。支持重试机制确保通知可靠送达。
     * 调用方需确保提供完整的模板数据。
     * </p>
     *
     * @param templateBo 订单通知模板对象，包含订单及通知相关信息
     */
    @Override
    @Retryable(scene = "客户订单通知", retryStrategy = RetryType.LOCAL_REMOTE)
    public void sendOrderCustomerNotification(OrderNotifyTemplateBo templateBo) {
        log.info("发送订单客户通知：{}, 订单号：{}", templateBo.getEventType(), templateBo.getOrder().getCustomerOrderNo());

        // 获取通知收件人
        String toEmail = templateBo.getToEmail();

        // 验证邮箱是否有效
        if (toEmail == null || toEmail.isEmpty()) {
            log.warn("客户邮箱为空，无法发送通知：订单号={}", templateBo.getOrder().getCustomerOrderNo());
            return;
        }

        // 发送邮件通知
        if (templateBo.getAttachments() != null && templateBo.getAttachments().length > 0) {
            // 使用提供的附件数组
            mailService.sendCustomerTemplateEmail(
                templateBo.getTemplateName(),
                templateBo,
                toEmail,
                templateBo.getAttachments());
        } else if (templateBo.getInvoiceFile() != null) {
            // 兼容旧版本，使用发票文件作为附件
            mailService.sendCustomerTemplateEmail(
                templateBo.getTemplateName(),
                templateBo,
                toEmail,
                new File[]{templateBo.getInvoiceFile()});
        } else {
            // 不带附件发送
            mailService.sendCustomerTemplateEmail(
                templateBo.getTemplateName(),
                templateBo,
                toEmail);
        }

        log.info("订单客户通知发送成功：{}, 订单号：{}, 收件人：{}",
            templateBo.getEventType(),
            templateBo.getOrder().getCustomerOrderNo(),
            toEmail);
    }

    /**
     * 发送订单管理员通知
     * <p>
     * 发送管理员邮件通知。支持重试机制确保通知可靠送达。
     * 调用方需确保提供完整的模板数据。
     * </p>
     *
     * @param templateBo 订单通知模板对象，包含订单及通知相关信息
     */
    @Override
    @Retryable(scene = "管理员订单通知", retryStrategy = RetryType.LOCAL_REMOTE)
    public void sendOrderAdminNotification(OrderNotifyTemplateBo templateBo) {
        log.info("发送订单管理员通知：{}, 系统订单号：{}, 客户订单号：{}",
            templateBo.getEventType(),
            templateBo.getOrder().getOrderNo(),
            templateBo.getOrder().getCustomerOrderNo());

        // 获取通知收件人
        String toEmail = templateBo.getToEmail();

        // 验证邮箱是否有效
        if (toEmail == null || toEmail.isEmpty()) {
            log.warn("管理员邮箱为空，无法发送通知：系统订单号={}, 客户订单号={}",
                templateBo.getOrder().getOrderNo(),
                templateBo.getOrder().getCustomerOrderNo());
            return;
        }

        // 发送邮件通知
        if (templateBo.getAttachments() != null && templateBo.getAttachments().length > 0) {
            // 使用提供的附件数组
            mailService.sendAdminTemplateEmail(
                templateBo.getTemplateName(),
                templateBo,
                toEmail,
                templateBo.getAttachments());
        } else if (templateBo.getInvoiceFile() != null) {
            // 兼容旧版本，使用发票文件作为附件
            mailService.sendAdminTemplateEmail(
                templateBo.getTemplateName(),
                templateBo,
                toEmail,
                new File[]{templateBo.getInvoiceFile()});
        } else {
            // 不带附件发送
            mailService.sendAdminTemplateEmail(
                templateBo.getTemplateName(),
                templateBo,
                toEmail);
        }

        log.info("订单管理员通知发送成功：{}, 系统订单号：{}, 客户订单号：{}, 收件人：{}",
            templateBo.getEventType(),
            templateBo.getOrder().getOrderNo(),
            templateBo.getOrder().getCustomerOrderNo(),
            toEmail);
    }
}
