package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品变体属性关联对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_product_variant_attribute")
public class WhsProductVariantAttribute extends BaseEntity {

    /**
     * 关联ID
     */
    @TableId
    private Long id;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * 属性ID
     */
    private Long attributeId;

    /**
     * 属性值ID
     */
    private Long attributeValueId;
}
