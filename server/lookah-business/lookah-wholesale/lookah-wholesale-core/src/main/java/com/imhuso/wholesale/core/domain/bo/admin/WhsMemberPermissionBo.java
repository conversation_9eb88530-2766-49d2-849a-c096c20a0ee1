package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.wholesale.core.domain.WhsMemberPermission;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户权限业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WhsMemberPermission.class)
public class WhsMemberPermissionBo extends BaseEntity {
    /**
     * 权限ID
     */
    @NotNull(message = "权限ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 客户ID
     */
    @NotNull(message = "客户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long memberId;

    /**
     * 权限键
     */
    @NotBlank(message = "权限键不能为空", groups = {AddGroup.class, EditGroup.class})
    private String permissionKey;

    /**
     * 权限值
     */
    @NotBlank(message = "权限值不能为空", groups = {AddGroup.class, EditGroup.class})
    private String permissionValue;

    /**
     * 帐号状态（0停用 1正常）
     */
    private String status;
}
