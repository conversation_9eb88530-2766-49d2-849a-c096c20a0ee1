package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 订单支付状态更新业务对象
 *
 * <AUTHOR>
 */
@Data
public class OrderPaymentStatusUpdateBo {

    /**
     * 支付状态
     */
    @NotNull(message = "支付状态不能为空", groups = {EditGroup.class})
    private Integer paymentStatus;

    /**
     * 备注
     */
    private String remark;
}
