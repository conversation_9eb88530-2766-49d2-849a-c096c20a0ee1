package com.imhuso.wholesale.core.domain.bo.front;

import com.imhuso.wholesale.core.domain.WhsProduct;
import com.imhuso.wholesale.core.domain.vo.front.CartVo;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 购物车校验结果业务对象
 *
 * <AUTHOR>
 */
@Data
public class CartCheckResultBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 购物车商品列表
     */
    private List<CartVo> cartItems;

    /**
     * 商品映射，key为商品ID，value为商品信息
     */
    private Map<Long, WhsProduct> productMap;
}
