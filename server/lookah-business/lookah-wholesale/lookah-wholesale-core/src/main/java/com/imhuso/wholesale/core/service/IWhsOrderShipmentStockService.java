package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentStockResponseVo;

/**
 * 订单发货库存查询服务接口 (查询实际变体库存)
 *
 * <AUTHOR>
 */
public interface IWhsOrderShipmentStockService {
    /**
     * 根据发货方案ID获取相关变体的库存信息
     * 返回方案中变体在各仓库的有效可用库存
     *
     * @param planId 发货方案ID
     * @return 实际变体库存响应对象
     * @throws com.imhuso.common.core.exception.ServiceException 如果方案ID无效
     */
    WhsShipmentStockResponseVo getStocksByPlanId(Long planId);
}
