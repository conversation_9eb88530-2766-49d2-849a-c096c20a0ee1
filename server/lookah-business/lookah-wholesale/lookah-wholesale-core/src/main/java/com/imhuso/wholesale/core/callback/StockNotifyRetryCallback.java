package com.imhuso.wholesale.core.callback;

import com.aizuda.snailjob.client.core.callback.RetryCompleteCallback;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 库存通知重试回调
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class StockNotifyRetryCallback implements RetryCompleteCallback {
    @Override
    public void doSuccessCallback(String sceneName, String executorName, Object[] params) {
        log.info("库存通知重试成功 - 场景: {}, 执行器: {}, 参数: {}", sceneName, executorName, params);
    }

    @Override
    public void doMaxRetryCallback(String sceneName, String executorName, Object[] params) {
        log.error("库存通知重试达到最大次数 - 场景: {}, 执行器: {}, 参数: {}", sceneName, executorName, params);
    }
}
