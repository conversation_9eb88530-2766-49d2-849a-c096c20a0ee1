package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_order_status_log")
public class WhsOrderStatusLog extends BaseEntity {

    /**
     * 日志ID
     */
    @TableId
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 支付状态
     */
    private Integer paymentStatus;

    /**
     * 发票状态
     */
    private Integer invoiceStatus;

    /**
     * 发货状态
     */
    private Integer shipmentStatus;

    /**
     * 变更前状态
     */
    private Integer beforeStatus;

    /**
     * 操作类型（0创建 1支付 2取消 3发货 4完成）
     */
    private Integer operationType;

    /**
     * 备注
     */
    private String remark;
}
