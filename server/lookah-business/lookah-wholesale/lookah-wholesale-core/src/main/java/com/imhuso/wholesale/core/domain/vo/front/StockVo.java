package com.imhuso.wholesale.core.domain.vo.front;

import com.imhuso.wholesale.core.domain.WhsStock;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 前台库存视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsStock.class)
public class StockVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 库存ID
     */
    private Long id;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 可用库存
     */
    private Integer availableStock;

    /**
     * 状态（0停用 1正常）
     */
    private String status;
}
