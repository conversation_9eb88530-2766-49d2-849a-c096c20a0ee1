package com.imhuso.wholesale.core.domain.vo.admin;

import java.time.LocalDateTime;

import com.imhuso.common.translation.annotation.Translation;
import com.imhuso.wholesale.core.constant.TransConstant;
import com.imhuso.wholesale.core.domain.WhsStockAllocation;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 库存分配视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AutoMapper(target = WhsStockAllocation.class)
public class WhsStockAllocationVo {

    /**
     * 分配ID
     */
    private Long id;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * 变体SKU
     */
    private String skuCode;

    /**
     * 分配数量
     */
    private Integer quantity;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 发票ID
     */
    private Long invoiceId;

    /**
     * 订单项ID
     */
    private Long orderItemId;

    /**
     * 状态：0-待处理 1-已分配 2-已释放 3-已发货
     */
    private Integer status;

    /**
     * 状态名称
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, mapper = "status")
    private String statusName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
