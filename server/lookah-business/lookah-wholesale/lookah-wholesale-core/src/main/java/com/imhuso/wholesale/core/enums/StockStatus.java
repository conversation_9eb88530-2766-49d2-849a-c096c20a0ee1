package com.imhuso.wholesale.core.enums;

import com.imhuso.common.translation.core.EnumTranslatableInterface;
import lombok.Getter;

/**
 * 库存状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum StockStatus implements EnumTranslatableInterface {

    /**
     * 库存充足
     */
    SUFFICIENT(0),

    /**
     * 库存不足（部分商品库存不足）
     */
    INSUFFICIENT(1),

    /**
     * 无库存（所有商品都无库存）
     */
    OUT_OF_STOCK(2),

    /**
     * 部分库存（部分商品有库存，部分无库存）
     */
    PARTIAL_STOCK(3);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 构造函数
     */
    StockStatus(int value) {
        this.value = value;
    }
}
