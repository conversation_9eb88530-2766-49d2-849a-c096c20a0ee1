package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.wholesale.core.domain.WhsProductAttributeRelation;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品属性关联业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WhsProductAttributeRelation.class)
public class WhsProductAttributeRelationBo extends BaseEntity {

    /**
     * 关联ID
     */
    @NotNull(message = "关联ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productId;

    /**
     * 属性ID
     */
    @NotNull(message = "属性ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long attributeId;

    /**
     * 排序
     */
    private Integer sortOrder;
}
