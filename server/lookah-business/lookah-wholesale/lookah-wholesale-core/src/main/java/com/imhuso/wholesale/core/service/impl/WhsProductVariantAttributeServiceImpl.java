package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.imhuso.wholesale.core.domain.WhsProductVariantAttribute;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVariantAttributeVo;
import com.imhuso.wholesale.core.service.IWhsProductVariantAttributeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 产品变体属性关联Service实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsProductVariantAttributeServiceImpl implements IWhsProductVariantAttributeService {

    private final WhsProductVariantAttributeHelper attributeHelper;
    private final WhsProductVariantAttributeValidator attributeValidator;

    @Override
    public void deleteByVariantId(Long variantId) {
        attributeHelper.deleteVariantAttributes(variantId);
    }

    @Override
    public void batchInsert(List<WhsProductVariantAttribute> attributes) {
        attributeHelper.batchInsertAttributes(attributes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processVariantAttributes(Long productId, Long variantId, Map<Long, Long> newAttributeMap) {
        if (variantId == null) {
            log.warn("处理变体属性关联时变体ID为空");
            return;
        }

        // 1. 验证属性数据
        attributeValidator.validateVariantAttributes(productId, variantId, newAttributeMap);

        // 2. 处理属性为空的情况
        if (CollUtil.isEmpty(newAttributeMap)) {
            attributeHelper.deleteVariantAttributes(variantId);
            attributeHelper.updateVariantSpecs(variantId, newAttributeMap);
            return;
        }

        // 3. 处理属性变更并更新规格
        attributeHelper.processAttributeChanges(variantId, newAttributeMap);
        attributeHelper.updateVariantSpecs(variantId, newAttributeMap);
    }

    @Override
    public List<WhsProductVariantAttributeVo> getVariantAttributes(Long variantId) {
        return attributeHelper.getVariantAttributes(variantId);
    }

    @Override
    public Map<Long, List<WhsProductVariantAttributeVo>> getVariantAttributesMap(List<Long> variantIds) {
        return attributeHelper.getVariantAttributesMap(variantIds);
    }
}
