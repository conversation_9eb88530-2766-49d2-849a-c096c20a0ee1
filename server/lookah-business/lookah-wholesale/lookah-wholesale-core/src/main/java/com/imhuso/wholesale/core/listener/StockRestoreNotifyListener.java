package com.imhuso.wholesale.core.listener;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.wholesale.core.config.WholesaleBusinessConfig;
import com.imhuso.wholesale.core.domain.WhsStock;
import com.imhuso.wholesale.core.domain.WhsStockNotify;
import com.imhuso.wholesale.core.enums.NotifyStatus;
import com.imhuso.wholesale.core.event.StockChangeEvent;
import com.imhuso.wholesale.core.mapper.WhsStockMapper;
import com.imhuso.wholesale.core.mapper.WhsStockNotifyMapper;
import com.imhuso.wholesale.core.service.IWhsStockNotifyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 库存恢复通知监听器
 * <p>
 * 监听库存变更事件，当库存从无到有时，自动发送到货通知
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StockRestoreNotifyListener {

    private final WhsStockMapper stockMapper;
    private final WhsStockNotifyMapper stockNotifyMapper;
    private final IWhsStockNotifyService stockNotifyService;
    private final WholesaleBusinessConfig businessConfig;

    /**
     * 防抖时间阈值（毫秒）
     */
    private static final long THRESHOLD_TIME_MS = 2000;

    /**
     * 每个变体的上次处理时间（用于防抖）
     */
    private final ConcurrentHashMap<Long, AtomicLong> lastProcessTimeMap = new ConcurrentHashMap<>();

    /**
     * 监听库存变更事件，处理库存恢复通知
     * <p>
     * 当库存从无到有（可用库存大于阈值）时，查找所有待通知的记录并发送通知
     * </p>
     *
     * @param event 库存变更事件
     */
    @Async
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void handleStockChangeEvent(StockChangeEvent event) {
        try {
            List<Long> variantIds = event.getVariantIds();
            if (CollUtil.isEmpty(variantIds)) {
                return;
            }

            log.debug("处理库存恢复通知，变体数量: {}", variantIds.size());

            // 处理每个变体的库存恢复通知
            for (Long variantId : variantIds) {
                // 每个变体单独进行防抖处理
                processVariantStockRestore(variantId);
            }
        } catch (Exception e) {
            log.error("处理库存恢复通知失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理单个变体的库存恢复通知
     *
     * @param variantId 变体ID
     */
    private void processVariantStockRestore(Long variantId) {
        try {
            // 防抖处理: 在指定时间内的多次事件，只处理一次
            AtomicLong lastTime = lastProcessTimeMap.computeIfAbsent(variantId, k -> new AtomicLong(0));
            long currentTime = System.currentTimeMillis();
            long previousTime = lastTime.get();

            if ((currentTime - previousTime) < THRESHOLD_TIME_MS) {
                log.debug("变体防抖：距离上次处理时间小于{}ms，跳过处理: variantId={}", THRESHOLD_TIME_MS, variantId);
                return;
            }

            // 更新上次处理时间 (CAS操作确保线程安全)
            if (!lastTime.compareAndSet(previousTime, currentTime)) {
                log.debug("变体防抖：CAS更新失败，跳过处理: variantId={}", variantId);
                return;
            }

            // 查询变体当前库存状态
            List<WhsStock> stockList = stockMapper.selectList(
                new LambdaQueryWrapper<WhsStock>()
                    .eq(WhsStock::getVariantId, variantId));

            if (CollUtil.isEmpty(stockList)) {
                log.debug("变体无库存记录，跳过处理: variantId={}", variantId);
                return;
            }

            // 计算总可用库存
            int totalAvailableStock = stockList.stream()
                .mapToInt(WhsStock::getAvailableStock)
                .sum();

            // 检查库存是否满足通知条件（从无到有且大于阈值）
            int threshold = businessConfig.getStockNotifyThreshold();
            if (totalAvailableStock < threshold) {
                log.debug("变体库存未达到通知阈值，跳过处理: variantId={}, availableStock={}, threshold={}",
                    variantId, totalAvailableStock, threshold);
                return;
            }

            log.info("变体库存已恢复，准备发送到货通知: variantId={}, availableStock={}", variantId, totalAvailableStock);

            // 查询所有待通知的记录
            List<WhsStockNotify> notifyList = stockNotifyMapper.selectList(
                new LambdaQueryWrapper<WhsStockNotify>()
                    .eq(WhsStockNotify::getVariantId, variantId)
                    .eq(WhsStockNotify::getStatus, NotifyStatus.PENDING.getValue()));

            if (CollUtil.isEmpty(notifyList)) {
                log.debug("变体无待通知记录，跳过处理: variantId={}", variantId);
                return;
            }

            log.info("发送变体到货通知，变体ID: {}, 通知数量: {}", variantId, notifyList.size());

            // 更新通知状态并发送通知
            for (WhsStockNotify notify : notifyList) {
                stockNotifyService.updateNotifyStatus(notify.getId(), NotifyStatus.NOTIFIED.getValue());
                // 注意：updateNotifyStatus方法内部会发布NOTIFY_SENT事件，触发通知发送
            }
        } catch (Exception e) {
            log.error("处理变体库存恢复通知失败: variantId={}, error={}", variantId, e.getMessage(), e);
        }
    }
}
