package com.imhuso.wholesale.core.domain.vo.admin;

import com.imhuso.wholesale.core.domain.WhsProductSeries;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

/**
 * 产品系列视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsProductSeries.class)
public class WhsProductSeriesVo {

    /**
     * 系列ID
     */
    private Long id;

    /**
     * 系列名称
     */
    private String name;

    /**
     * 状态（0停用 1正常）
     */
    private String status;

    /**
     * 显示顺序
     */
    private Integer sort;
}
