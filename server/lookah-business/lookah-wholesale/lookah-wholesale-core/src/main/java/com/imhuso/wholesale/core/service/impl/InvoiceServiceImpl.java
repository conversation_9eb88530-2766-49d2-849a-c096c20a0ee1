package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderItemVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderVo;
import com.imhuso.wholesale.core.enums.PackagingType;
import com.imhuso.wholesale.core.service.IInvoiceService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单发票服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InvoiceServiceImpl implements IInvoiceService {
    private static final String EXCEL_TEMPLATE_PATH = "templates/excel/invoice.xlsx";

    @Override
    public File generateExcelInvoice(WhsOrderVo orderVo) throws Exception {
        if (ObjectUtil.isNull(orderVo)) {
            throw new RuntimeException("订单不存在");
        }

        // 发票号
        String orderNo = orderVo.getOrderNo();

        // 创建临时文件
        File tempFile = File.createTempFile("INVOICE_" + orderNo + "_", ".xlsx");

        // 获取模板文件
        try (InputStream templateStream = new ClassPathResource(EXCEL_TEMPLATE_PATH).getInputStream()) {
            // 准备主数据（订单基本信息）
            Map<String, Object> mainData = new HashMap<>();
            // 下单日期
            mainData.put("date", new SimpleDateFormat("MM/dd/yyyy").format(orderVo.getCreateTime()));
            // 发票号
            mainData.put("invoiceId", orderNo);
            // 总金额
            mainData.put("totalAmount", orderVo.getTotalAmount().doubleValue());
            // 收货地址
            mainData.put("shippingAddress", orderVo.getFullShippingAddress());
            // 付款方式
            mainData.put("paymentMethod", null);
            // 发货时间
            mainData.put("shippingDate", null);
            // 发货方式
            mainData.put("shippingMethod", null);
            // 追踪号
            mainData.put("trackingNo", null);

            // 处理订单项数据
            List<InvoiceItemData> itemDataList = new ArrayList<>();

            // 转换订单项数据
            if (orderVo.getItems() != null) {
                for (WhsOrderItemVo item : orderVo.getItems()) {
                    // 构造表格数据
                    InvoiceItemData invoiceItemData = getInvoiceItemData(item);

                    // 添加到列表
                    itemDataList.add(invoiceItemData);
                }
            }

            // 添加汇总信息
            mainData.put("credits", 0.00);

            // 使用EasyExcel填充模板
            try (ExcelWriter excelWriter = EasyExcel.write(tempFile).withTemplate(templateStream).excelType(ExcelTypeEnum.XLSX).build()) {

                WriteSheet writeSheet = EasyExcel.writerSheet().build();

                // 配置填充选项
                FillConfig fillConfig = FillConfig.builder().autoStyle(Boolean.TRUE).forceNewRow(Boolean.TRUE).build();

                // 填充数据
                excelWriter.fill(itemDataList, fillConfig, writeSheet);
                excelWriter.fill(mainData, writeSheet);
            }

            log.info("已成功生成订单Excel发票 - 订单号: LO#{}, 格式化订单号: {}", orderVo.getCustomerOrderNo(), orderNo);
            return tempFile;
        }
    }

    @NotNull
    private static InvoiceItemData getInvoiceItemData(WhsOrderItemVo item) {
        InvoiceItemData invoiceItemData = new InvoiceItemData();

        invoiceItemData.setCode(item.getProductName());
        invoiceItemData.setSku(item.getSkuCode());
        invoiceItemData.setQuantity(item.getQuantity());
        invoiceItemData.setCost(item.getPrice());
        invoiceItemData.setAmount(item.getAmount());
        invoiceItemData.setSpecs(item.getSpecsSnapshotText());

        // 如果是箱装，则设置每箱数量
        if (item.getPackagingType() != null && item.getPackagingType().equals(PackagingType.CASE.getValue())) {
            invoiceItemData.setCases(item.getQuantity());
        }
        invoiceItemData.setBrand("LOOKAH");
        return invoiceItemData;
    }

    /**
     * 发票项数据对象 - 只包含需要动态填充的字段
     */
    @Data
    private static class InvoiceItemData {
        /**
         * 产品编码
         */
        private String code;

        /**
         * SKU
         */
        private String sku;

        /**
         * 规格字符串
         * 例如："红色-大尺寸"
         */
        private String specs;

        /**
         * 数量
         */
        private Integer quantity;

        /**
         * 单价
         */
        private BigDecimal cost;

        /**
         * 总价
         */
        private BigDecimal amount;

        /**
         * 每箱数量
         */
        private Integer cases;

        /**
         * 品牌
         */
        private String brand;
    }
}
