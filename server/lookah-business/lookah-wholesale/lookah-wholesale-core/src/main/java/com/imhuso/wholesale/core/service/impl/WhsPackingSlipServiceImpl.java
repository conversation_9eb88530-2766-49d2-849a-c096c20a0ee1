package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.imhuso.wholesale.core.domain.WhsOrder;
import com.imhuso.wholesale.core.domain.WhsOrderItem;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderProgressVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanItemVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentItemVo;
import com.imhuso.wholesale.core.domain.vo.admin.PackingSlipAvailabilityVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsLogisticsPackageVo;
import com.imhuso.wholesale.core.enums.OrderStatus;
import com.imhuso.wholesale.core.service.IPackingSlipService;
import com.imhuso.wholesale.core.service.IWhsOrderProgressService;
import com.imhuso.wholesale.core.service.IWhsOrderBaseService;
import com.imhuso.wholesale.core.service.IWhsOrderShipmentQueryService;
import com.imhuso.wholesale.core.service.IWhsOrderItemService;
import com.imhuso.wholesale.core.service.IWhsOrderNoFormatService;
import com.imhuso.wholesale.core.utils.WhsEnumTranslationUtils;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 装箱单服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsPackingSlipServiceImpl implements IPackingSlipService {
    private static final String EXCEL_TEMPLATE_PATH = "templates/excel/packing-slip.xlsx";

    private final IWhsOrderBaseService orderBaseService;
    private final IWhsOrderShipmentQueryService shipmentQueryService;
    private final IWhsOrderItemService orderItemService;
    private final IWhsOrderProgressService orderProgressService;
    private final IWhsOrderNoFormatService orderNoFormatService;

    /**
     * 装箱单生成异常类
     */
    public static class PackingSlipException extends Exception {
        public PackingSlipException(String message) {
            super(message);
        }

        public PackingSlipException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    @Override
    public File generatePackingSlipExcel(WhsOrderVo orderVo) throws Exception {
        if (ObjectUtil.isNull(orderVo)) {
            throw new PackingSlipException("订单不存在");
        }

        // 检查订单是否有发货记录
        if (CollUtil.isEmpty(orderVo.getShipment())) {
            log.warn("订单VO[{}]中没有发货记录，无法生成装箱单", orderVo.getId());
            throw new PackingSlipException("订单没有发货记录，无法生成装箱单");
        }

        // 创建临时文件
        String orderNo = orderVo.getCustomerOrderNo();
        File tempFile = File.createTempFile("PACKING-SLIP-" + orderNo, ".xlsx");
        try {

            // 使用最新的发货日期
            Date shipDate = getLatestShipmentDate(orderVo.getShipment());

            // 如果shipDate为null（例如，所有发货记录都没有日期），则使用当前日期
            if (shipDate == null) {
                shipDate = new Date();
            }
            LocalDate localDate = shipDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            String formattedDate = localDate.format(DateTimeFormatter.ofPattern("MM/dd/yyyy"));

            // 处理发货项数据
            List<PackingSlipItemData> itemDataList = generatePackingSlipItems(orderVo);

            // 获取所有物流包裹信息，过滤掉没有有效追踪号的包裹
            List<WhsLogisticsPackageVo> allPackages = new ArrayList<>();
            for (WhsOrderShipmentVo shipment : orderVo.getShipment()) {
                if (shipment.getLogisticsPackages() != null && !shipment.getLogisticsPackages().isEmpty()) {
                    for (WhsLogisticsPackageVo pkg : shipment.getLogisticsPackages()) {
                        // 只添加有有效追踪号的包裹
                        if (pkg.getTrackingNumber() != null && !pkg.getTrackingNumber().trim().isEmpty()) {
                            allPackages.add(pkg);
                        }
                    }
                }
            }

            // 使用动态生成方式创建Excel文件
            return generateDynamicPackingSlipExcel(tempFile, orderVo.getCustomerOrderNo(), formattedDate,
                allPackages, itemDataList);
        } catch (Exception e) {
            // 如果发生异常，确保清理临时文件
            if (tempFile.exists()) {
                try {
                    boolean deleted = tempFile.delete();
                    if (!deleted) {
                        log.warn("删除临时装箱单文件失败: {}", tempFile.getAbsolutePath());
                    }
                } catch (Exception deleteEx) {
                    log.warn("清理临时装箱单文件时发生异常: {}", tempFile.getAbsolutePath(), deleteEx);
                }
            }
            // 重新抛出异常
            throw e;
        }
    }

    /**
     * 动态生成装箱单Excel文件
     *
     * @param outputFile        输出文件
     * @param poNumber          PO号
     * @param shipDate          发货日期
     * @param logisticsPackages 物流包裹列表
     * @param itemDataList      产品数据列表
     * @return 生成的Excel文件
     * @throws Exception 如果生成过程中发生错误
     */
    private File generateDynamicPackingSlipExcel(File outputFile, String poNumber, String shipDate,
                                                 List<WhsLogisticsPackageVo> logisticsPackages,
                                                 List<PackingSlipItemData> itemDataList) throws Exception {
        log.info("开始动态生成装箱单Excel文件 - PO号: {}, 发货日期: {}", poNumber, shipDate);

        // 从模板文件创建工作簿
        try (InputStream templateInputStream = new ClassPathResource(EXCEL_TEMPLATE_PATH).getInputStream();
             Workbook workbook = WorkbookFactory.create(templateInputStream)) {

            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);

            // 创建基础单元格样式 - 添加居中对齐
            CellStyle dataCellStyle = workbook.createCellStyle();
            dataCellStyle.setBorderTop(BorderStyle.THIN);
            dataCellStyle.setBorderBottom(BorderStyle.THIN);
            dataCellStyle.setBorderLeft(BorderStyle.THIN);
            dataCellStyle.setBorderRight(BorderStyle.THIN);
            dataCellStyle.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中
            dataCellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直居中

            // 创建已完成行的样式 - 绿色背景，白色字体
            CellStyle completedRowStyle = workbook.createCellStyle();
            completedRowStyle.cloneStyleFrom(dataCellStyle); // 复制基础样式
            completedRowStyle.setFillForegroundColor(IndexedColors.GREEN.getIndex());
            completedRowStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font whiteFont = workbook.createFont();
            whiteFont.setColor(IndexedColors.WHITE.getIndex());
            completedRowStyle.setFont(whiteFont);

            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);

            // 填充PO号和发货日期
            // 查找并替换{poNumber}单元格 - 位于G3
            Row poRow = sheet.getRow(2);
            if (poRow != null) {
                Cell poCell = poRow.getCell(6); // G列对应索引6
                if (poCell != null) {
                    poCell.setCellValue(poNumber);
                }
            }

            // 查找并替换{shipDate}单元格 - 位于G4
            Row shipDateRow = sheet.getRow(3);
            if (shipDateRow != null) {
                Cell shipDateCell = shipDateRow.getCell(6); // G列对应索引6
                if (shipDateCell != null) {
                    shipDateCell.setCellValue(shipDate);
                }
            }

            // 当前行索引，用于跟踪下一行的位置
            int currentRowIndex = 6;

            // 添加物流包裹信息（如果有）
            if (!logisticsPackages.isEmpty()) {
                // 创建物流包裹表头
                Row packageHeaderRow = sheet.createRow(currentRowIndex++);
                Cell trackingHeaderCell = packageHeaderRow.createCell(0);
                trackingHeaderCell.setCellValue("Tracking Number");
                trackingHeaderCell.setCellStyle(headerStyle);

                Cell carrierHeaderCell = packageHeaderRow.createCell(1);
                carrierHeaderCell.setCellValue("Carrier");
                carrierHeaderCell.setCellStyle(headerStyle);

                Cell trackingUrlHeaderCell = packageHeaderRow.createCell(2);
                trackingUrlHeaderCell.setCellValue("Tracking URL");
                trackingUrlHeaderCell.setCellStyle(headerStyle);

                // 确保表头行的其余单元格也有边框
                for (int i = 3; i < 7; i++) {
                    Cell emptyCell = packageHeaderRow.createCell(i);
                    emptyCell.setCellStyle(headerStyle);
                }

                // 添加物流包裹数据
                for (WhsLogisticsPackageVo packageVo : logisticsPackages) {
                    Row packageRow = sheet.createRow(currentRowIndex++);

                    // 追踪号
                    Cell trackingCell = packageRow.createCell(0);
                    trackingCell.setCellValue(packageVo.getTrackingNumber());
                    trackingCell.setCellStyle(dataCellStyle);

                    // 承运商
                    Cell carrierCell = packageRow.createCell(1);
                    carrierCell.setCellValue(packageVo.getCarrier());
                    carrierCell.setCellStyle(dataCellStyle);

                    // 追踪URL（如果有）
                    Cell trackingUrlCell = packageRow.createCell(2);
                    if (packageVo.getTrackingUrl() != null && !packageVo.getTrackingUrl().isEmpty()) {
                        trackingUrlCell.setCellValue(packageVo.getTrackingUrl());

                        // 添加超链接
                        Hyperlink link = workbook.getCreationHelper().createHyperlink(HyperlinkType.URL);
                        link.setAddress(packageVo.getTrackingUrl());
                        trackingUrlCell.setHyperlink(link);
                    } else {
                        trackingUrlCell.setCellValue("");
                    }
                    trackingUrlCell.setCellStyle(dataCellStyle);

                    // 确保数据行的其余单元格也有边框
                    for (int i = 3; i < 7; i++) {
                        Cell emptyCell = packageRow.createCell(i);
                        emptyCell.setCellStyle(dataCellStyle);
                    }
                }

                // 添加一个空行作为间隔
                ++currentRowIndex;
            }

            // 添加产品表格表头
            Row productHeaderRow = sheet.createRow(currentRowIndex++);
            String[] headers = {"Code#", "SKU", "Colors", "Order Qty", "Shipped Qty", "Remaining Qty", "Qty Per Case"};

            for (int i = 0; i < headers.length; i++) {
                Cell headerCell = productHeaderRow.createCell(i);
                headerCell.setCellValue(headers[i]);
                headerCell.setCellStyle(headerStyle);
            }

            // 添加产品数据
            for (PackingSlipItemData item : itemDataList) {
                Row dataRow = sheet.createRow(currentRowIndex++);

                // 判断是否已完成发货（剩余数量为0）
                boolean isCompleted = item.getRemainingQty() == 0;
                // 选择合适的样式
                CellStyle rowStyle = isCompleted ? completedRowStyle : dataCellStyle;

                // 产品编码
                Cell codeCell = dataRow.createCell(0);
                codeCell.setCellValue(item.getProductName() != null ? item.getProductName() : "");
                codeCell.setCellStyle(rowStyle);

                // SKU
                Cell skuCell = dataRow.createCell(1);
                skuCell.setCellValue(item.getSku());
                skuCell.setCellStyle(rowStyle);

                // 颜色/规格
                Cell colorsCell = dataRow.createCell(2);
                colorsCell.setCellValue(item.getColors());
                colorsCell.setCellStyle(rowStyle);

                // 订单数量
                Cell orderQtyCell = dataRow.createCell(3);
                orderQtyCell.setCellValue(item.getOrderQty());
                orderQtyCell.setCellStyle(rowStyle);

                // 已发货数量
                Cell shippedQtyCell = dataRow.createCell(4);
                shippedQtyCell.setCellValue(item.getShippedQty());
                shippedQtyCell.setCellStyle(rowStyle);

                // 剩余数量
                Cell remainingQtyCell = dataRow.createCell(5);
                remainingQtyCell.setCellValue(item.getRemainingQty());
                remainingQtyCell.setCellStyle(rowStyle);

                // 每箱数量（如果是箱装）
                Cell qtyPerCaseCell = dataRow.createCell(6);
                if (item.getIsCase() && item.getQtyPerCase() != null) {
                    qtyPerCaseCell.setCellValue(item.getQtyPerCase());
                } else {
                    qtyPerCaseCell.setCellValue("");
                }
                qtyPerCaseCell.setCellStyle(rowStyle);
            }

            // 添加汇总行（直接添加，不空行）
            Row totalRow = sheet.createRow(currentRowIndex);
            ++currentRowIndex;

            // 添加"Total"标签
            Cell totalLabelCell = totalRow.createCell(0);
            totalLabelCell.setCellValue("Total");
            totalLabelCell.setCellStyle(headerStyle);

            // 创建第1和第2列的单元格，并设置样式（虽然会被合并，但需要设置边框）
            Cell totalCell1 = totalRow.createCell(1);
            totalCell1.setCellStyle(headerStyle);
            Cell totalCell2 = totalRow.createCell(2);
            totalCell2.setCellStyle(headerStyle);

            // 合并前三列作为标签
            sheet.addMergedRegion(new CellRangeAddress(
                totalRow.getRowNum(), totalRow.getRowNum(), 0, 2
            ));

            // 计算各列的总和
            int totalOrderQty = 0;
            int totalShippedQty = 0;
            int totalRemainingQty = 0;

            for (PackingSlipItemData item : itemDataList) {
                totalOrderQty += item.getOrderQty() != null ? item.getOrderQty() : 0;
                totalShippedQty += item.getShippedQty() != null ? item.getShippedQty() : 0;
                totalRemainingQty += item.getRemainingQty();
            }

            // 订单总数量
            Cell totalOrderQtyCell = totalRow.createCell(3);
            totalOrderQtyCell.setCellValue(totalOrderQty);
            totalOrderQtyCell.setCellStyle(headerStyle);

            // 已发货总数量
            Cell totalShippedQtyCell = totalRow.createCell(4);
            totalShippedQtyCell.setCellValue(totalShippedQty);
            totalShippedQtyCell.setCellStyle(headerStyle);

            // 剩余总数量
            Cell totalRemainingQtyCell = totalRow.createCell(5);
            totalRemainingQtyCell.setCellValue(totalRemainingQty);
            totalRemainingQtyCell.setCellStyle(headerStyle);

            // 每箱数量列不需要汇总
            Cell totalQtyPerCaseCell = totalRow.createCell(6);
            totalQtyPerCaseCell.setCellStyle(headerStyle);

            // 写入文件
            try (FileOutputStream outputStream = new FileOutputStream(outputFile)) {
                workbook.write(outputStream);
            }

            log.info("成功动态生成装箱单Excel文件 - PO号: {}", poNumber);
            return outputFile;
        } catch (Exception e) {
            log.error("动态生成装箱单Excel时出错 - PO号: {}: {}", poNumber, e.getMessage(), e);
            if (outputFile != null && outputFile.exists()) {
                boolean deleted = outputFile.delete(); // 清理失败时产生的临时文件
                if (!deleted) {
                    log.warn("删除动态生成的临时Excel文件失败: {}", outputFile.getAbsolutePath());
                }
            }
            throw new PackingSlipException("动态生成装箱单Excel失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取最新的发货日期
     */
    private Date getLatestShipmentDate(List<WhsOrderShipmentVo> shipments) {
        if (CollUtil.isEmpty(shipments)) {
            return null;
        }

        return shipments.stream()
            .map(WhsOrderShipmentVo::getShippedDate)
            .filter(ObjectUtil::isNotNull)
            .max(Date::compareTo)
            .orElse(null);
    }

    @Override
    public File generatePackingSlipExcelById(Long orderId) throws Exception {
        // 1. 获取订单基础信息
        WhsOrder order = orderBaseService.getOrderById(orderId);
        if (order == null) {
            throw new PackingSlipException("订单不存在");
        }

        // 2. 获取发货记录
        List<WhsOrderShipmentVo> shipments = shipmentQueryService.getOrderShipmentRecords(orderId);
        if (shipments == null || shipments.isEmpty()) {
            log.warn("查询订单[{}]的发货记录为空，无法生成装箱单", orderId);
            throw new PackingSlipException("订单没有发货记录，无法生成装箱单");
        }

        // 3. 确保所有发货记录包含包裹和发货项信息
        for (int i = 0; i < shipments.size(); i++) {
            WhsOrderShipmentVo shipment = shipments.get(i);
            if (shipment.getLogisticsPackages() == null || shipment.getLogisticsPackages().isEmpty()) {
                log.info("发货记录[{}]的包裹信息为空，尝试重新加载", shipment.getId());
                // 重新加载发货记录的详细信息
                WhsOrderShipmentVo refreshedShipment = shipmentQueryService.getShipmentById(shipment.getId());
                if (refreshedShipment != null && refreshedShipment.getLogisticsPackages() != null
                    && !refreshedShipment.getLogisticsPackages().isEmpty()) {
                    // 更新列表中的发货记录
                    shipments.set(i, refreshedShipment);
                    log.info("成功重新加载发货记录[{}]的详细信息，包含{}个包裹",
                        refreshedShipment.getId(), refreshedShipment.getLogisticsPackages().size());
                }
            }
        }

        // 4. 构建简单的订单VO用于生成装箱单
        WhsOrderVo orderVo = new WhsOrderVo();
        orderVo.setId(order.getId());
        orderVo.setCustomerOrderNo(orderNoFormatService.getCustomerOrderNoByOrder(order));
        orderVo.setShipment(shipments);

        // 5. 生成装箱单
        return generatePackingSlipExcel(orderVo);
    }

    /**
     * 生成装箱单项目数据
     * 优先使用发货计划数据，如果没有则返回空列表
     *
     * @param orderVo 订单VO对象
     * @return 装箱单项目数据列表
     */
    private List<PackingSlipItemData> generatePackingSlipItems(WhsOrderVo orderVo) {
        log.info("开始为订单[{}]生成装箱单数据", orderVo.getId());

        // 获取订单进度信息，包含发货计划项
        WhsOrderProgressVo progressVo = orderProgressService.getOrderProgress(orderVo.getId());

        // 如果有发货计划项，则使用计划项数据生成装箱单
        if (progressVo != null && progressVo.getPlanItems() != null && !progressVo.getPlanItems().isEmpty()) {
            log.info("订单[{}]有{}个发货计划项，将使用计划项数据生成装箱单",
                orderVo.getId(), progressVo.getPlanItems().size());

            // 使用发货计划项生成装箱单数据，完全基于发货计划数据，不依赖原始订单数据
            return generatePackingSlipItemsFromPlanItems(progressVo.getPlanItems(), orderVo.getShipment());
        } else {
            log.warn("订单[{}]没有发货计划项，无法生成装箱单数据", orderVo.getId());
            return new ArrayList<>();
        }
    }

    /**
     * 使用发货计划项生成装箱单数据
     * 完全基于发货计划数据，不依赖原始订单数据
     *
     * @param planItems 发货计划项列表
     * @param shipments 订单的所有发货记录
     * @return 装箱单项目数据列表
     */
    private List<PackingSlipItemData> generatePackingSlipItemsFromPlanItems(List<WhsShipmentPlanItemVo> planItems,
                                                                           List<WhsOrderShipmentVo> shipments) {
        List<PackingSlipItemData> result = new ArrayList<>();
        if (planItems == null || planItems.isEmpty()) {
            return result;
        }

        // 获取所有发货记录中的追踪号信息
        Map<String, String> trackingNumberMap = buildTrackingNumberMap(shipments);

        // 提前获取所有需要的订单项数据，避免循环中重复查询
        Map<Long, Map<Long, WhsOrderItem>> orderItemsCache = new HashMap<>();
        Set<Long> orderIds = new HashSet<>();

        // 收集所有需要查询的订单ID
        for (WhsShipmentPlanItemVo planItem : planItems) {
            if (planItem.getOrderId() != null) {
                orderIds.add(planItem.getOrderId());
            }
        }

        // 批量查询所有订单的订单项
        for (Long orderId : orderIds) {
            List<WhsOrderItem> orderItems = orderItemService.getOrderItems(orderId);
            if (orderItems != null && !orderItems.isEmpty()) {
                Map<Long, WhsOrderItem> variantToItemMap = new HashMap<>();
                for (WhsOrderItem item : orderItems) {
                    if (item.getVariantId() != null) {
                        variantToItemMap.put(item.getVariantId(), item);
                    }
                }
                orderItemsCache.put(orderId, variantToItemMap);
            }
        }

        // 处理每个发货计划项
        for (WhsShipmentPlanItemVo planItem : planItems) {
            String skuCode = planItem.getSkuCode();
            if (skuCode == null || skuCode.isEmpty()) {
                log.warn("Skipping plan item with null or empty SKU code.");
                continue;
            }

            // 创建装箱单项
            PackingSlipItemData packingItem = new PackingSlipItemData();

            // 设置基本信息
            packingItem.setCode(planItem.getSkuCode()); // 使用SKU作为Code#

            // 获取产品名称 - 确保不为空
            String productName = planItem.getProductName();
            if (productName == null || productName.isEmpty()) {
                // 如果计划项中没有产品名称，尝试通过变体ID查询产品名称
                if (planItem.getVariantId() != null && planItem.getOrderId() != null) {
                    // 从缓存中获取订单项
                    Map<Long, WhsOrderItem> variantToItemMap = orderItemsCache.get(planItem.getOrderId());
                    if (variantToItemMap != null) {
                        WhsOrderItem item = variantToItemMap.get(planItem.getVariantId());
                        if (item != null) {
                            productName = item.getProductName();
                        }
                    }
                }
                if (productName == null || productName.isEmpty()) {
                    productName = planItem.getSkuCode(); // 使用SKU作为备选
                }
            }
            packingItem.setProductName(productName);

            packingItem.setSku(skuCode);

            // 获取规格文本 - 确保不为空
            String specsText = planItem.getSpecsText();
            if (specsText == null || specsText.isEmpty()) {
                // 尝试从订单项中获取规格信息
                if (planItem.getVariantId() != null && planItem.getOrderId() != null) {
                    // 从缓存中获取订单项
                    Map<Long, WhsOrderItem> variantToItemMap = orderItemsCache.get(planItem.getOrderId());
                    if (variantToItemMap != null) {
                        WhsOrderItem item = variantToItemMap.get(planItem.getVariantId());
                        if (item != null && item.getSpecsSnapshot() != null) {
                            specsText = orderItemService.parseSpecsSnapshotToText(item.getSpecsSnapshot());
                        }
                    }
                }
                if (specsText == null || specsText.isEmpty()) {
                    specsText = ""; // 如果仍然为空，使用空字符串
                }
            }
            packingItem.setColors(specsText);

            // 设置订单数量（使用计划项的数量）
            packingItem.setOrderQty(planItem.getQuantity());

            // 设置已发货数量
            packingItem.setShippedQty(planItem.getShippedQuantity() != null ? planItem.getShippedQuantity() : 0);

            // 设置当前发货数量（使用所有发货记录）
            int currentQty = calculateCurrentShippedQty(skuCode, shipments);
            packingItem.setCurrentShippedQty(currentQty);

            // 设置包装类型
            packingItem.setPackagingType(planItem.getPackagingType());
            packingItem.setPackagingTypeText(planItem.getPackagingTypeText());

            // 根据包装类型设置每箱数量和发货箱数
            if (planItem.getPackagingType() != null && planItem.getPackagingType() == 2) {
                // 设置每箱数量
                packingItem.setQtyPerCase(planItem.getPackagingQuantity());

                // 计算发货箱数
                if (planItem.getShippedQuantity() != null && planItem.getPackagingQuantity() != null
                    && planItem.getPackagingQuantity() > 0) {
                    int shippedCases = (int) Math.ceil((double) planItem.getShippedQuantity() / planItem.getPackagingQuantity());
                    packingItem.setShippedCases(shippedCases);
                } else {
                    packingItem.setShippedCases(0);
                }
            } else {
                // 非箱装产品，将这些字段设为null
                packingItem.setQtyPerCase(null);
                packingItem.setShippedCases(null);
            }

            // 设置追踪号
            packingItem.setTrackingNumber(trackingNumberMap.get(skuCode));

            // 添加到结果列表
            result.add(packingItem);
        }

        return result;
    }

    /**
     * 装箱单项数据对象
     * 注意：属性名必须与Excel模板中的变量名完全匹配
     */
    @Data
    private static class PackingSlipItemData {
        /**
         * 产品编码 - 对应模板中的 {code}
         */
        private String code;

        /**
         * 产品名称 - 对应模板中的 {productName}
         * 在Excel中显示为Code#列
         */
        private String productName;

        /**
         * SKU编码 - 对应模板中的 {sku}
         */
        private String sku;

        /**
         * 颜色/规格 - 对应模板中的 {colors}
         */
        private String colors;

        /**
         * 订单数量 - 对应模板中的 {orderQty}
         */
        private Integer orderQty;

        /**
         * 当前发货数量 - 对应模板中的 {currentShippedQty}
         */
        private Integer currentShippedQty = 0;

        /**
         * 已发货总数量 - 对应模板中的 {shippedQty}
         */
        private Integer shippedQty = 0;

        /**
         * 每箱数量 - 对应模板中的 {qtyPerCase}
         * 注意：如果不是箱装，则为null
         */
        private Integer qtyPerCase;

        /**
         * 发货箱数 - 对应模板中的 {shippedCases}
         * 注意：如果不是箱装，则为null
         */
        private Integer shippedCases = 0;

        /**
         * 追踪号 - 对应模板中的 {trackingNumber}
         */
        private String trackingNumber;

        /**
         * 包装类型 - 对应模板中的 {packagingType}
         * 0-单品 1-展示盒 2-整箱
         */
        private Integer packagingType;

        /**
         * 包装类型文本 - 对应模板中的 {packagingTypeText}
         */
        private String packagingTypeText;

        /**
         * 获取剩余数量 - 对应模板中的 {remainingQty}
         */
        public Integer getRemainingQty() {
            // 动态计算剩余数量
            return orderQty - shippedQty;
        }

        /**
         * 判断是否为箱装 - 对应模板中的 {isCase}
         * 用于条件判断是否显示箱装相关字段
         */
        public Boolean getIsCase() {
            // 包装类型为2表示整箱
            return packagingType != null && packagingType == 2;
        }
    }

    @Override
    public PackingSlipAvailabilityVo checkPackingSlipAvailability(Long orderId) {
        if (orderId == null) {
            log.warn("订单ID不能为空");
            return PackingSlipAvailabilityVo.unavailable("订单ID不能为空");
        }
        
        try {
            // 1. 检查订单是否存在
            WhsOrder order = orderBaseService.getOrderById(orderId);
            if (order == null) {
                log.warn("订单[{}]不存在，无法下载装箱单", orderId);
                return PackingSlipAvailabilityVo.orderNotFound();
            }

            // 2. 检查订单是否有发货记录
            List<WhsOrderShipmentVo> shipments = shipmentQueryService.getOrderShipmentRecords(orderId);
            int shipmentCount = shipments != null ? shipments.size() : 0;

            if (shipmentCount == 0) {
                log.warn("订单[{}]没有发货记录，无法下载装箱单", orderId);
                PackingSlipAvailabilityVo vo = PackingSlipAvailabilityVo.noShipment();
                setOrderStatusInfo(vo, order);
                return vo;
            }

            log.info("订单[{}]可以下载装箱单，共有{}条发货记录", orderId, shipmentCount);
            PackingSlipAvailabilityVo vo = PackingSlipAvailabilityVo.available(shipmentCount);
            setOrderStatusInfo(vo, order);
            return vo;
        } catch (Exception e) {
            log.error("检查订单[{}]装箱单可用性时发生错误: {}", orderId, e.getMessage(), e);
            return PackingSlipAvailabilityVo.unavailable("检查装箱单可用性时发生错误: " + e.getMessage());
        }
    }

    /**
     * 设置订单状态信息到装箱单可用性VO中
     * 包括订单状态码、订单状态文本描述
     *
     * @param vo    装箱单可用性VO对象
     * @param order 订单实体对象
     */
    private void setOrderStatusInfo(PackingSlipAvailabilityVo vo, WhsOrder order) {
        // 设置订单状态信息
        vo.setOrderStatus(order.getOrderStatus() != null ? order.getOrderStatus().toString() : null);
        // 设置订单状态文本
        vo.setOrderStatusText(order.getOrderStatus() != null ?
            WhsEnumTranslationUtils.translate(OrderStatus.class, order.getOrderStatus()) : null);
    }

    /**
     * 构建SKU到追踪号的映射
     *
     * @param shipments 发货记录列表
     * @return SKU到追踪号的映射
     */
    private Map<String, String> buildTrackingNumberMap(List<WhsOrderShipmentVo> shipments) {
        Map<String, String> trackingNumberMap = new HashMap<>();
        if (shipments != null && !shipments.isEmpty()) {
            for (WhsOrderShipmentVo shipment : shipments) {
                if (shipment.getLogisticsPackages() != null) {
                    for (WhsLogisticsPackageVo pkg : shipment.getLogisticsPackages()) {
                        if (pkg.getShipmentItems() != null) {
                            String trackingNumber = pkg.getTrackingNumber();
                            if (trackingNumber != null && !trackingNumber.isEmpty()) {
                                for (WhsOrderShipmentItemVo item : pkg.getShipmentItems()) {
                                    if (item.getSkuCode() != null) {
                                        trackingNumberMap.put(item.getSkuCode(), trackingNumber);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return trackingNumberMap;
    }

    /**
     * 计算指定SKU的当前发货数量
     *
     * @param skuCode   SKU编码
     * @param shipments 发货记录列表
     * @return 当前发货数量
     */
    private int calculateCurrentShippedQty(String skuCode, List<WhsOrderShipmentVo> shipments) {
        int currentQty = 0;
        if (shipments != null && !shipments.isEmpty()) {
            for (WhsOrderShipmentVo shipment : shipments) {
                if (shipment.getLogisticsPackages() != null) {
                    for (WhsLogisticsPackageVo pkg : shipment.getLogisticsPackages()) {
                        if (pkg.getShipmentItems() != null) {
                            for (WhsOrderShipmentItemVo item : pkg.getShipmentItems()) {
                                if (skuCode.equals(item.getSkuCode())) {
                                    currentQty += item.getQuantity();
                                }
                            }
                        }
                    }
                }
            }
        }
        return currentQty;
    }
}
