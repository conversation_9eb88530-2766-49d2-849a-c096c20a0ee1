package com.imhuso.wholesale.core.constant;

/**
 * 运费同步相关常量
 *
 * <AUTHOR>
 */
public class FreightSyncConstants {

    /**
     * 昊通订单状态
     * 根据API文档：0：草稿；1：确认, 3：待发货，4：已发货，5:，问题件，6：缺货，10：已作废 7处理中
     */
    public static class HaotongOrderStatus {
        /** 草稿 */
        public static final int DRAFT = 0;
        /** 确认 */
        public static final int CONFIRMED = 1;
        /** 待发货 */
        public static final int PENDING_SHIPMENT = 3;
        /** 已发货 */
        public static final int SHIPPED = 4;
        /** 问题件 */
        public static final int PROBLEM = 5;
        /** 缺货 */
        public static final int OUT_OF_STOCK = 6;
        /** 处理中 */
        public static final int PROCESSING = 7;
        /** 已作废 */
        public static final int CANCELLED = 10;
    }

    /**
     * 运费同步配置
     */
    public static class SyncConfig {
        /** 批量查询的批次大小 */
        public static final int BATCH_SIZE = 50;
        /** 默认货币 */
        public static final String DEFAULT_CURRENCY = "USD";
        /** 昊通API分页大小 */
        public static final int API_PAGE_SIZE = 100;
        /** 默认页码 */
        public static final int DEFAULT_PAGE_INDEX = 1;
    }

    /**
     * 提供商类型
     */
    public static class ProviderType {
        /** 昊通 */
        public static final String HAOTONG = "haotong";
    }

    /**
     * 同步状态
     */
    public static class SyncStatus {
        /** 未同步 */
        public static final String NOT_SYNCED = "0";
        /** 已同步 */
        public static final String SYNCED = "1";
    }

    /**
     * API路径
     */
    public static class ApiPath {
        /** 昊通订单查询接口 */
        public static final String HAOTONG_ORDER_LIST = "/GetOrderList";
    }
}
