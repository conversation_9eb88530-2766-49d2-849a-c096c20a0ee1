package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.constant.CarrierConstants;
import com.imhuso.wholesale.core.domain.WhsLogisticsPackage;
import com.imhuso.wholesale.core.domain.WhsWarehouse;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderShipmentItemBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderShipmentPlanItemBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanItemVo;
import com.imhuso.wholesale.core.domain.vo.warehouse.ShipmentInfoVo;
import com.imhuso.wholesale.core.domain.vo.warehouse.ShippingResultVo;
import com.imhuso.wholesale.core.domain.vo.warehouse.WarehouseShipmentRecord;
import com.imhuso.wholesale.core.enums.PackageStatus;
import com.imhuso.wholesale.core.mapper.WhsLogisticsPackageMapper;
import com.imhuso.wholesale.core.mapper.WhsWarehouseMapper;
import com.imhuso.wholesale.core.service.IWhsOverseasShipmentProcessingService;
import com.imhuso.wholesale.core.service.IWhsOverseasWarehouseService;
import com.imhuso.wholesale.core.utils.carrier.TrackingNumberUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单海外仓发货处理服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsOverseasShipmentProcessingServiceImpl implements IWhsOverseasShipmentProcessingService {

    private final IWhsOverseasWarehouseService overseasWarehouseService;
    private final WhsLogisticsPackageMapper logisticsPackageMapper;
    private final WhsWarehouseMapper warehouseMapper;

    /**
     * 处理订单的海外仓发货流程
     * 在库存处理完成后调用，确保库存操作成功后才触发海外仓发货
     * 修改：接收列表参数并直接修改，返回void
     *
     * @param successfulShipments 用于收集成功发货记录的列表 (In/Out Param)
     * @param orderId             订单ID
     * @param order               订单视图对象 (包含收货地址等信息)
     * @param packageIdMap        包裹ID映射 (物流方式ID-仓库ID -> 包裹ID)
     * @param shipmentItems       此次发货的发货项列表 (Bo)
     * @param planItemMap         计划项映射 (planItemId -> planItemVo)
     * @param batchNumber         批次号
     */
    @Override
    public void processShipments(List<WarehouseShipmentRecord> successfulShipments,
                                 Long orderId, WhsOrderVo order,
                                 Map<String, Long> packageIdMap,
                                 List<WhsOrderShipmentItemBo> shipmentItems,
                                 Map<Long, WhsShipmentPlanItemVo> planItemMap,
                                 Integer batchNumber) {

        if (packageIdMap == null || packageIdMap.isEmpty()) {
            log.info("订单 {} 没有物流包裹，跳过海外仓发货处理", orderId);
            return; // 直接返回
        }

        log.info("开始处理订单 {} 的海外仓发货，共 {} 个包裹，批次号: {}", orderId, packageIdMap.size(), batchNumber);

        // 获取所有物流包裹ID
        Collection<Long> packageIds = packageIdMap.values();

        // 查询物流包裹详情
        List<WhsLogisticsPackage> packages = logisticsPackageMapper.selectByIds(packageIds);
        if (CollUtil.isEmpty(packages)) {
            // 注意：虽然 packageIdMap 有值，但可能因为某些原因（如事务问题）导致包裹未成功创建或查询失败
            log.warn("订单 {} 存在包裹ID映射，但未能查询到任何物流包裹记录 (Package IDs: {})，跳过海外仓发货处理", orderId, packageIds);
            return; // 直接返回
        }

        // 创建一个副本，避免并发修改问题
        List<WhsLogisticsPackage> packagesToProcess = new ArrayList<>(packages);

        // 为快速查找包裹对应的发货项，构建包裹ID到发货项列表的映射
        Map<Long, List<WhsOrderShipmentItemBo>> packageToShipmentItemsMap = new HashMap<>();
        for (WhsOrderShipmentItemBo shipmentItem : shipmentItems) {
            if (shipmentItem.getLogisticsMethodId() != null && shipmentItem.getWarehouseId() != null) {
                // 根据仓库ID和物流方式ID查找对应的包裹
                String packageKey = shipmentItem.getLogisticsMethodId() + "-" + shipmentItem.getWarehouseId();
                Long packageId = packageIdMap.get(packageKey);
                if (packageId != null) {
                    packageToShipmentItemsMap.computeIfAbsent(packageId, k -> new ArrayList<>()).add(shipmentItem);
                }
            }
        }

        // 处理每个物流包裹
        for (WhsLogisticsPackage logisticsPackage : packagesToProcess) {
            Long warehouseId = logisticsPackage.getWarehouseId();
            if (warehouseId == null) {
                log.warn("物流包裹 {} 没有关联仓库ID，跳过海外仓发货处理", logisticsPackage.getId());
                continue;
            }

            // 查询仓库信息，判断是否为海外仓
            WhsWarehouse warehouse = warehouseMapper.selectById(warehouseId);
            if (warehouse == null) {
                log.warn("未找到仓库信息 (ID: {})，跳过海外仓发货处理", warehouseId);
                continue;
            }

            // 如果不是海外仓，跳过处理
            if (!BusinessConstants.YES.equals(warehouse.getIsOverseas())) {
                log.debug("仓库 {} ({}) 不是海外仓，跳过海外仓发货处理", warehouseId, warehouse.getName());
                continue;
            }

            log.info("准备处理海外仓 {} 的包裹 {} 发货", warehouse.getName(), logisticsPackage.getId());

            // 查找该包裹关联的发货项列表
            List<WhsOrderShipmentItemBo> warehouseShipmentItems = packageToShipmentItemsMap.get(logisticsPackage.getId());
            if (warehouseShipmentItems == null || warehouseShipmentItems.isEmpty()) {
                log.warn("物流包裹 {} 没有找到关联的发货项，跳过海外仓发货处理", logisticsPackage.getId());
                continue;
            }

            // 如果有多个发货项，记录日志以便跟踪
            if (warehouseShipmentItems.size() > 1) {
                log.info("物流包裹 {} 包含 {} 个发货项，将合并所有发货项的planItems进行海外仓发货",
                    logisticsPackage.getId(), warehouseShipmentItems.size());
            }

            // 逐个处理每个海外仓包裹，确保准确记录成功发货的仓库
            try {
                // 构建海外仓发货信息 - 传入所有发货项以合并处理planItems
                ShipmentInfoVo shipmentInfo = buildOverseasWarehouseShipmentInfo(
                    orderId, order, logisticsPackage.getId(), warehouseShipmentItems, planItemMap);

                // 调用海外仓服务发货 - 直接使用传入的 batchNumber
                log.info("开始调用海外仓 {} 的发货API，包裹ID: {}，批次号: {}", warehouse.getName(), logisticsPackage.getId(),
                    batchNumber);
                ShippingResultVo shippingResult = overseasWarehouseService.createShipment(
                    warehouseId, shipmentInfo, logisticsPackage.getId(), batchNumber);

                // 处理发货结果，更新物流包裹状态
                handleOverseasWarehouseShippingResult(logisticsPackage, shippingResult);

                // 发货成功，添加到传入的列表
                WarehouseShipmentRecord successRecord = new WarehouseShipmentRecord(
                    warehouseId, logisticsPackage.getId(), order.getOrderNo(), shippingResult.getExternalShipmentId());
                successfulShipments.add(successRecord); // 直接修改传入的列表
                log.info("海外仓 {} 包裹 {} 发货成功，添加到成功发货记录", warehouse.getName(), logisticsPackage.getId());
            } catch (Exception e) {
                // 捕获构建信息、调用API或处理结果中可能出现的任何异常
                log.error("处理订单 {} 海外仓包裹 {} (仓库 {}) 发货时发生异常: {}",
                    orderId, logisticsPackage.getId(), warehouse.getName(), e.getMessage(), e);

                // 直接向上抛出异常，以便外层事务回滚
                // 由于是逐个包裹处理，如果有一个包裹失败，前面已成功的包裹记录已经添加到 successfulShipments 列表
                // 这样外层调用方就可以对已成功的包裹进行回滚处理
                // 抛出原始异常或包装后的 ServiceException，确保事务能感知
                if (e instanceof ServiceException) {
                    throw (ServiceException) e;
                } else {
                    throw new ServiceException(String.format("发货失败: %s", e.getMessage()));
                }
            }
        }

        log.info("订单 {} 的海外仓发货处理完成，成功发货 {} 个包裹", orderId, successfulShipments.size());
    }

    /**
     * 构建海外仓发货信息 (私有方法)
     *
     * @param orderId                订单ID
     * @param order                  订单信息 (包含真实地址)
     * @param packageId              物流包裹ID
     * @param warehouseShipmentItems 仓库发货项列表（包含该仓库的所有配置和商品项）
     * @param planItemMap            计划项映射
     * @return 海外仓发货信息
     * @throws ServiceException 如果关键地址信息缺失
     */
    private ShipmentInfoVo buildOverseasWarehouseShipmentInfo(Long orderId, WhsOrderVo order, Long packageId,
                                                              List<WhsOrderShipmentItemBo> warehouseShipmentItems,
                                                              Map<Long, WhsShipmentPlanItemVo> planItemMap) {
        // 创建发货信息对象
        ShipmentInfoVo shipmentInfo = new ShipmentInfoVo();

        // 取第一个发货项作为主要配置（通常同一包裹的发货项配置应该相同）
        WhsOrderShipmentItemBo primaryShipmentItem = warehouseShipmentItems.get(0);

        // 设置订单基本信息
        shipmentInfo.setOrderId(orderId);
        shipmentInfo.setOrderNo(order.getOrderNo()); // 使用订单号

        // --- 使用真实的收货地址信息 ---
        String recipientName = StringUtils.trimToNull(order.getShippingName());
        String phone = StringUtils.trimToNull(order.getShippingPhone());
        String country = StringUtils.trimToNull(order.getShippingCountry());
        String state = StringUtils.trimToNull(order.getShippingState());
        String city = StringUtils.trimToNull(order.getShippingCity());
        String address1 = StringUtils.trimToNull(order.getShippingAddress());
        String address2 = StringUtils.trimToNull(order.getShippingAddress2());
        String postalCode = StringUtils.trimToNull(order.getShippingZip());
        String email = StringUtils.trimToNull(order.getShippingEmail()); // Email 通常可选
        String companyName = StringUtils.trimToNull(order.getShippingCompanyName()); // 收件人公司名称

        // 校验必填字段 (根据实际海外仓接口要求调整)
        if (recipientName == null)
            throw new ServiceException("收货人姓名不能为空，订单: " + orderId);
        if (phone == null)
            throw new ServiceException("收货人电话不能为空，订单: " + orderId);
        if (country == null)
            throw new ServiceException("收货国家不能为空，订单: " + orderId);
        // 省/州可能不是所有国家都强制要求，但美国/加拿大等是需要的，这里假设必填
        if (state == null)
            throw new ServiceException("收货州/省不能为空，订单: " + orderId);
        if (city == null)
            throw new ServiceException("收货城市不能为空，订单: " + orderId);
        if (address1 == null)
            throw new ServiceException("收货地址不能为空，订单: " + orderId); // 地址1通常必填
        if (postalCode == null)
            throw new ServiceException("收货邮编不能为空，订单: " + orderId);

        shipmentInfo.setRecipientName(recipientName);
        shipmentInfo.setRecipientPhone(phone);
        shipmentInfo.setRecipientEmail(email != null ? email : ""); // 邮箱提供默认空字符串
        shipmentInfo.setRecipientCompanyName(companyName != null ? companyName : ""); // 公司名称提供默认空字符串

        shipmentInfo.setCountry(country);
        shipmentInfo.setProvince(state);
        shipmentInfo.setCity(city);
        // 合并地址1和地址2
        shipmentInfo.setAddress(address1 + (address2 != null ? " " + address2 : ""));
        shipmentInfo.setPostalCode(postalCode);
        // --------------------------

        // 获取物流方式ID、pieceCount和签名服务配置
        Long logisticsMethodId = primaryShipmentItem.getLogisticsMethodId();
        if (logisticsMethodId == null) {
            log.warn("包裹 {} 的发货项缺少物流方式ID，无法设置到发货信息中", packageId);
            throw new ServiceException("发货项缺少物流方式ID，无法创建海外仓发货单，包裹ID: " + packageId);
        }
        shipmentInfo.setLogisticsMethodId(logisticsMethodId);

        // 设置pieceCount - 从仓库发货项中获取
        Integer pieceCount = primaryShipmentItem.getPieceCount();
        if (pieceCount != null && pieceCount > 0) {
            shipmentInfo.setPieceCount(pieceCount);
            log.debug("包裹 {} 设置pieceCount: {}", packageId, pieceCount);
        } else {
            // 如果没有设置pieceCount，默认为1
            shipmentInfo.setPieceCount(1);
            log.warn("包裹 {} 未设置pieceCount，使用默认值1", packageId);
        }

        // 设置签名服务 - 需要考虑用户选择和账号支持情况
        Boolean userSignatureChoice = primaryShipmentItem.getSignatureService();
        Boolean finalSignatureService;

        if (userSignatureChoice != null) {
            // 用户有明确选择，直接使用用户选择
            finalSignatureService = userSignatureChoice;
            log.debug("包裹 {} 使用用户明确选择的签名服务设置: {}", packageId, finalSignatureService);
        } else {
            // 用户没有明确选择，默认关闭签名服务，让海外仓Provider根据账号配置决定
            finalSignatureService = false;
            log.debug("包裹 {} 用户未选择签名服务，设置为false让Provider根据账号配置决定", packageId);
        }

        shipmentInfo.setSignatureService(finalSignatureService);

        // 创建发货项列表 - 遍历所有仓库发货项中的商品项
        List<ShipmentInfoVo.ShipmentItemVo> items = new ArrayList<>();
        for (WhsOrderShipmentItemBo warehouseShipmentItem : warehouseShipmentItems) {
            for (WhsOrderShipmentPlanItemBo planItemBo : warehouseShipmentItem.getPlanItems()) {
                WhsShipmentPlanItemVo planItem = planItemMap.get(planItemBo.getPlanItemId());
                if (planItem == null) {
                    log.warn("发货项 (PlanItemId: {}) 没有找到对应的计划项，无法添加到海外仓发货单中，包裹ID: {}",
                        planItemBo.getPlanItemId(), packageId);
                    // 决定是跳过此项还是抛出异常
                    continue; // 暂时跳过
                }

                ShipmentInfoVo.ShipmentItemVo item = ShipmentInfoVo.ShipmentItemVo.builder()
                    .sku(planItem.getSkuCode() != null ? planItem.getSkuCode() : "UNKNOWN_SKU") // 提供默认SKU
                    .productName(planItem.getSkuCode() != null ? planItem.getSkuCode() : "Unknown Product") // 临时使用SKU或提供默认名称
                    .quantity(planItemBo.getQuantity())
                    .unitPrice(BigDecimal.ZERO) // 暂时设为0
                    .weightInGrams(0) // 临时设为0
                    .build();

                items.add(item);
            }
        }

        if (items.isEmpty()) {
            throw new ServiceException("无法为包裹 " + packageId + " 构建有效的发货项列表");
        }
        shipmentInfo.setItems(items);

        log.debug("为包裹 {} 构建的海外仓发货信息: {}", packageId, shipmentInfo);
        return shipmentInfo;
    }

    /**
     * 处理海外仓发货结果 (私有方法)
     *
     * @param logisticsPackage 物流包裹
     * @param shippingResult   发货结果
     * @throws ServiceException 如果发货失败或更新包裹状态失败
     */
    private void handleOverseasWarehouseShippingResult(WhsLogisticsPackage logisticsPackage,
                                                       ShippingResultVo shippingResult) {
        if (shippingResult == null) {
            throw new ServiceException("海外仓发货结果为空，物流包裹ID: " + logisticsPackage.getId());
        }

        log.info("包裹 {} 海外仓发货结果: success={}, errorMessage={}, trackingNumber={}, externalShipmentId={}",
            logisticsPackage.getId(), shippingResult.isSuccess(), shippingResult.getErrorMessage(),
            shippingResult.getTrackingNumber(), shippingResult.getExternalShipmentId());

        if (!shippingResult.isSuccess()) {
            // 如果海外仓API调用本身就失败了，直接抛出异常，外层事务会回滚
            // 无需更新包裹状态为失败，因为发货操作未在海外仓成功创建
            log.error("海外仓发货API调用失败，包裹ID: {}, 错误: {}", logisticsPackage.getId(), shippingResult.getErrorMessage());
            throw new ServiceException("海外仓发货失败: " + shippingResult.getErrorMessage());
        }

        // --- 发货成功，更新物流包裹信息 ---
        try {
            WhsLogisticsPackage updatePackage = new WhsLogisticsPackage();
            updatePackage.setId(logisticsPackage.getId());

            // 发货成功，更新状态为"已发货"
            updatePackage.setPackageStatus(PackageStatus.SHIPPED.getValue());

            // 获取追踪号
            String trackingNumber = shippingResult.getTrackingNumber();
            updatePackage.setTrackingNumber(trackingNumber);

            // 获取海外仓订单号并保存
            String externalOrderNo = shippingResult.getExternalShipmentId();
            if (StringUtils.isNotBlank(externalOrderNo)) {
                updatePackage.setExternalOrderNo(externalOrderNo);
                log.info("保存海外仓订单号: {}, 包裹ID: {}", externalOrderNo, logisticsPackage.getId());
            } else {
                log.warn("海外仓返回的订单号为空，包裹ID: {}", logisticsPackage.getId());
            }

            // 识别并设置承运商
            String detectedCarrier = CarrierConstants.UNKNOWN;
            if (StringUtils.isNotBlank(trackingNumber)) {
                detectedCarrier = TrackingNumberUtils.detectCarrier(trackingNumber);
                log.info("包裹 {} (跟踪号: {}) 本地识别承运商为: {}", logisticsPackage.getId(), trackingNumber, detectedCarrier);
            } else {
                log.warn("包裹 {} 海外仓发货成功，但未返回跟踪号，无法识别承运商", logisticsPackage.getId());
            }
            // 将carrier统一转为小写
            updatePackage.setCarrier(detectedCarrier.toLowerCase());

            updatePackage.setExpectedDelivery(shippingResult.getEstimatedDeliveryDate());

            log.info("海外仓发货成功，更新物流包裹状态，ID: {}, 跟踪号: {}, 承运商: {}, 海外仓订单号: {}",
                logisticsPackage.getId(), trackingNumber, detectedCarrier, externalOrderNo);

            // 更新物流包裹
            int updatedRows = logisticsPackageMapper.updateById(updatePackage);
            if (updatedRows == 0) {
                // 更新失败，可能包裹已被删除或并发修改
                log.error("更新物流包裹 {} 状态失败 (未找到记录或更新0行)", logisticsPackage.getId());
                // 抛出异常以回滚事务，因为发货已在海外仓创建，但本地状态更新失败
                throw new ServiceException("更新物流包裹状态失败，包裹ID: " + logisticsPackage.getId());
            }
        } catch (Exception e) {
            log.error("更新物流包裹 {} 状态时发生异常: {}", logisticsPackage.getId(), e.getMessage(), e);
            // 向上抛出，确保事务回滚
            throw new ServiceException("更新物流包裹状态时发生异常: " + e.getMessage());
        }
    }
}
