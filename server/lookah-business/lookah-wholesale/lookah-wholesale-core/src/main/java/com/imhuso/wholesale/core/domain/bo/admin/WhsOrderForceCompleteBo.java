package com.imhuso.wholesale.core.domain.bo.admin;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 订单强制完成业务对象
 *
 * <AUTHOR>
 */
@Data
public class WhsOrderForceCompleteBo {

    /**
     * 强制完成原因（必填）
     */
    @NotBlank(message = "强制完成原因不能为空")
    @Size(min = 2, max = 500, message = "强制完成原因长度必须在2-500字符之间")
    private String reason;
}
