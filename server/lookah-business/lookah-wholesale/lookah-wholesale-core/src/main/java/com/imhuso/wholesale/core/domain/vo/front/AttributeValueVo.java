package com.imhuso.wholesale.core.domain.vo.front;

import java.io.Serial;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 属性值视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AttributeValueVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 属性值ID
     */
    private Long id;

    /**
     * 属性ID
     */
    private Long attributeId;

    /**
     * 属性值键(如black,plastic)
     */
    private String valueKey;

    /**
     * 属性值显示名(如黑色,塑料)
     */
    private String valueDisplay;

    /**
     * 排序顺序
     */
    private Integer sort;
}
