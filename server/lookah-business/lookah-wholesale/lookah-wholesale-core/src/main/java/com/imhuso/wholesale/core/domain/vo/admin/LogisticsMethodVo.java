package com.imhuso.wholesale.core.domain.vo.admin;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 可用物流方式视图对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsMethodVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 物流方式代码 (例如: FEDEX_GROUND)
     */
    private String code;

    /**
     * 物流方式显示名称 (例如: FedEx Ground)
     */
    private String name;

    /**
     * 预估费用 (以字符串表示，保持灵活性)
     */
    private String estimatedCost;

    // 可以根据需要添加其他字段，如预计送达时间等
}
