package com.imhuso.wholesale.core.domain.bo.admin;

import java.util.Date;

import com.imhuso.common.mybatis.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 库存日志查询业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WhsStockLogBo extends BaseEntity {

    /**
     * 库存ID
     */
    private Long stockId;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 操作类型（1初始化 2更新 3锁定 4释放 5扣减 6退回）
     */
    private Integer operationType;

    /**
     * 开始时间
     */
    private Date beginTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 关联订单ID
     */
    private Long orderId;

    /**
     * 关联订单号
     */
    private String orderNo;
}
