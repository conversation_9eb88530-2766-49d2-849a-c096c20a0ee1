package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 入库单列表查询业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WhsInboundOrderListBo extends BaseEntity {

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 单号
     */
    private String orderNo;

    /**
     * 状态
     */
    private String status;

    /**
     * SKU编码
     */
    private String skuCode;
}
