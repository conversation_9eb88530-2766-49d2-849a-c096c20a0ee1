package com.imhuso.wholesale.core.service.impl;

import com.imhuso.wholesale.core.domain.bo.IMailVariables;
import com.imhuso.wholesale.core.domain.bo.front.MailSenderBo;
import com.imhuso.wholesale.core.service.IMailSenderService;
import com.imhuso.wholesale.core.service.IWhsMailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

import java.io.File;
import java.util.Locale;

/**
 * 批发邮件服务实现类
 * <p>
 * 采用模板组合设计模式：
 * 1. 所有邮件共享主模板 base.html
 * 2. 基于用途有不同的中间模板：order_base.html, stock_base.html 等
 * 3. 具体的模板内容（如 order_created.html）以 th:fragment="content" 形式提供
 * 4. 此设计符合前端关注点分离原则，避免在Java代码中处理模板组合逻辑
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsMailServiceImpl implements IWhsMailService {

    private final SpringTemplateEngine templateEngine;
    private final IMailSenderService mailSenderService;

    /**
     * 邮件模板路径前缀
     */
    private static final String TEMPLATE_PREFIX = "mail/";

    /**
     * 邮件主模板
     */
    private static final String MAIN_TEMPLATE = "base";

    /**
     * 内容片段变量名
     */
    private static final String CONTENT_VARIABLE = "content";

    @Override
    public void sendCustomerTemplateEmail(String templateName, IMailVariables variables, String to) {
        sendTemplateEmail(templateName, variables, to, Locale.ENGLISH);
    }

    @Override
    public void sendCustomerTemplateEmail(String templateName, IMailVariables variables, String to,
                                          File[] attachments) {
        sendTemplateEmail(templateName, variables, to, Locale.ENGLISH, attachments);
    }

    @Override
    public void sendAdminTemplateEmail(String templateName, IMailVariables variables, String to) {
        sendTemplateEmail(templateName, variables, to, Locale.CHINESE);
    }

    @Override
    public void sendAdminTemplateEmail(String templateName, IMailVariables variables, String to, File[] attachments) {
        sendTemplateEmail(templateName, variables, to, Locale.CHINESE, attachments);
    }

    @Override
    public void sendTemplateEmail(String templateName, IMailVariables variables, String to, Locale locale) {
        sendTemplateEmail(templateName, variables, to, locale, null);
    }

    /**
     * 发送模板邮件
     * <p>
     * 实现模板组合模式：
     * 1. 将模板名称(如"order_created")转换为内容片段路径
     * 2. 将内容片段路径作为"content"变量传递给模板引擎
     * 3. 根据模板类型使用相应的主模板(base.html, order_base.html, stock_base.html)
     * 4. 主模板通过Thymeleaf插入指定的内容片段
     * </p>
     *
     * @param templateName 模板名称，用于确定内容片段路径
     * @param variables    模板变量
     * @param to           收件人
     * @param locale       语言环境
     * @param attachments  附件
     */
    @Override
    public void sendTemplateEmail(String templateName, IMailVariables variables, String to, Locale locale,
                                  File[] attachments) {
        // 确保模板名称格式正确
        String contentPath = getFragmentPath(templateName);

        // 选择适当的主模板
        String baseTemplate = determineBaseTemplate(templateName);
        String mailTemplatePath = TEMPLATE_PREFIX + baseTemplate;

        log.info("准备发送邮件 - 收件人: {}, 标题: {}, 内容片段: {}, 主模板: {}, 附件: {}", to, variables.getTitle(), contentPath,
            baseTemplate, attachments != null ? attachments.length : 0);

        Context context = new Context(locale);
        context.setVariables(variables.getVariables());

        // 添加内容片段路径到上下文
        context.setVariable(CONTENT_VARIABLE, contentPath);

        // 使用选择的主模板进行渲染
        String emailContent = templateEngine.process(mailTemplatePath, context);

        if (emailContent == null || emailContent.isEmpty()) {
            log.warn("模板处理结果为空，可能是模板不存在或配置有误 - 模板: {}, 收件人: {}", mailTemplatePath, to);
            mailSenderService.sendMail(MailSenderBo.builder()
                .to(to)
                .title(variables.getTitle())
                .content(String.valueOf(variables.getVariables().get(CONTENT_VARIABLE)))
                .isHtml(false)
                .attachments(attachments)
                .build());
            return;
        }

        mailSenderService.sendMail(MailSenderBo.builder()
            .to(to)
            .title(variables.getTitle())
            .content(emailContent)
            .isHtml(true)
            .attachments(attachments)
            .build());
    }

    /**
     * 确定应该使用的主模板
     * <p>
     * 直接从模板名称中提取前缀并构建主模板名称
     * 例如：
     * - order_xxx -> order_base
     * - stock_xxx -> stock_base
     * - 其他情况使用默认主模板
     * </p>
     *
     * @param templateName 模板名称
     * @return 主模板名称
     */
    private String determineBaseTemplate(String templateName) {
        String cleanName = getCleanTemplateName(templateName);

        // 查找第一个下划线的位置
        int underscoreIndex = cleanName.indexOf('_');
        if (underscoreIndex > 0) {
            // 提取前缀（如"order"或"stock"）
            String prefix = cleanName.substring(0, underscoreIndex + 1);
            // 直接构建基础模板名（如"order_base"）
            return prefix + "base";
        }

        // 没有下划线或格式不符合预期，使用默认主模板
        return MAIN_TEMPLATE;
    }

    /**
     * 获取干净的模板名称（不包含路径前缀）
     *
     * @param templateName 模板名称
     * @return 干净的模板名称
     */
    private String getCleanTemplateName(String templateName) {
        if (templateName.startsWith(TEMPLATE_PREFIX)) {
            return templateName.substring(TEMPLATE_PREFIX.length());
        }
        return templateName;
    }

    /**
     * 获取内容片段路径
     * <p>
     * 根据模板名称生成对应的内容片段路径
     * 例如：输入"order_created" 输出"mail/order_created"
     * </p>
     *
     * @param templateName 模板名称
     * @return 内容片段路径
     */
    private String getFragmentPath(String templateName) {
        // 确保模板名有正确的前缀
        String cleanName = getCleanTemplateName(templateName);
        return TEMPLATE_PREFIX + cleanName;
    }
}
