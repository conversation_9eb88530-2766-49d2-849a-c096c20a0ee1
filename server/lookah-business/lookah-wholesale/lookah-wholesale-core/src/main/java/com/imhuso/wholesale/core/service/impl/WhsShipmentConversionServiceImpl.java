package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.wholesale.core.domain.WhsShipmentConversion;
import com.imhuso.wholesale.core.domain.bo.admin.WhsShipmentConversionBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentConversionVo;
import com.imhuso.wholesale.core.mapper.WhsShipmentConversionMapper;
import com.imhuso.wholesale.core.service.IWhsShipmentConversionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 发货转换记录服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsShipmentConversionServiceImpl implements IWhsShipmentConversionService {

    private final WhsShipmentConversionMapper conversionMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveFromPlanItems(List<WhsShipmentConversionBo> conversionBos) {
        if (CollUtil.isEmpty(conversionBos)) {
            return;
        }

        // 转换Bo列表为实体列表
        List<WhsShipmentConversion> conversions = MapstructUtils.convert(conversionBos,
            WhsShipmentConversion.class);

        if (conversions.isEmpty()) {
            log.warn("无有效转换记录需要保存");
            return;
        }

        // 使用MyBatisPlus的批量保存方法，指定批处理大小提高性能
        conversionMapper.insertBatch(conversions, 100);
    }

    @Override
    public List<WhsShipmentConversionVo> getConversionsByPlanId(Long planId) {
        if (planId == null) {
            return new ArrayList<>();
        }

        // 查询指定计划ID的转换记录
        LambdaQueryWrapper<WhsShipmentConversion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhsShipmentConversion::getPlanId, planId);
        List<WhsShipmentConversion> conversions = conversionMapper.selectList(queryWrapper);

        if (CollUtil.isEmpty(conversions)) {
            return new ArrayList<>();
        }

        // 转换为VO列表并返回
        return MapstructUtils.convert(conversions, WhsShipmentConversionVo.class);
    }

    @Override
    public boolean hasConversionForOriginalItem(Long orderId, Long originalVariantId) {
        if (orderId == null || originalVariantId == null) {
            return false;
        }
        LambdaQueryWrapper<WhsShipmentConversion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhsShipmentConversion::getOrderId, orderId)
            .eq(WhsShipmentConversion::getOriginalVariantId, originalVariantId)
            .last("LIMIT 1"); // 只需要判断是否存在，不需要获取数据

        return conversionMapper.exists(queryWrapper);
    }

    /**
     * 根据订单ID删除所有发货转换记录
     *
     * @param orderId 订单ID
     */
    @Override
    public void deleteByOrderId(Long orderId) {
        if (orderId == null) {
            return;
        }

        LambdaQueryWrapper<WhsShipmentConversion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhsShipmentConversion::getOrderId, orderId);
        int deletedCount = conversionMapper.delete(queryWrapper);

        if (deletedCount > 0) {
            log.info("删除订单 {} 的发货转换记录，共删除 {} 条记录", orderId, deletedCount);
        }
    }
}
