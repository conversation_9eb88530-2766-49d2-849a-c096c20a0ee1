package com.imhuso.wholesale.core.listener;

import com.imhuso.wholesale.core.constant.WholesaleConstants;
import com.imhuso.wholesale.core.domain.WhsOrder;
import com.imhuso.wholesale.core.event.WhsOrderEvent;
import com.imhuso.wholesale.core.event.WhsOrderEvent.WhsOrderEventType;
import com.imhuso.wholesale.core.service.IWhsOrderBaseService;
import com.imhuso.wholesale.core.service.IWhsOrderShipmentApprovalService;
import com.imhuso.wholesale.core.service.IApprovalConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * 订单发货审批事件监听器
 * 负责监听订单状态变更事件，在订单进入处理中状态时自动发起审核申请
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WhsOrderShipmentApprovalEventListener {

    private final IWhsOrderShipmentApprovalService shipmentApprovalService;
    private final IWhsOrderBaseService orderBaseService;
    private final IApprovalConfigService approvalConfigService;

    /**
     * 处理订单处理中状态事件
     * 当订单状态变为处理中时，根据配置自动发起发货审批申请
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleOrderProcessingEvent(WhsOrderEvent event) {
        // 只处理ORDER_PROCESSING事件
        if (event.getEventType() != WhsOrderEventType.ORDER_PROCESSING) {
            return;
        }

        try {
            Long orderId = event.getOrderId();
            if (orderId == null) {
                log.error("订单ID为空，无法处理发货审批事件");
                return;
            }

            // 检查是否启用发货审批功能
            if (!approvalConfigService.isApprovalEnabled()) {
                log.debug("订单[{}]发货审批功能未启用，跳过自动申请", orderId);
                return;
            }

            // 检查是否启用自动发起审核申请
            if (!approvalConfigService.isAutoRequest()) {
                log.debug("订单[{}]未启用自动发起审核申请，跳过自动申请", orderId);
                return;
            }

            // 查询订单信息
            WhsOrder order = orderBaseService.getOrderById(orderId);
            if (order == null) {
                log.error("订单不存在, orderId={}", orderId);
                return;
            }

            log.info("订单[{}]状态变为处理中，开始自动发起发货审批申请", order.getInternalOrderNo());

            // 检查是否已有审核申请
            var existingApproval = shipmentApprovalService.queryLatestByOrderId(orderId);
            if (existingApproval != null) {
                log.info("订单[{}]已存在审核申请，跳过自动申请", order.getInternalOrderNo());
                return;
            }

            // 自动发起审核申请（使用系统身份）
            String applicationReason = WholesaleConstants.Approval.SYSTEM_AUTO_REQUEST_REASON;
            Long approvalId = shipmentApprovalService.submitApprovalRequestBySystem(orderId, applicationReason);

            log.info("订单[{}]自动发起发货审批申请成功，审核ID: {}", order.getInternalOrderNo(), approvalId);

            // 检查是否需要自动审批
            if (approvalConfigService.isAutoApprove()) {
                log.info("订单[{}]配置了自动审批，开始自动审批通过", order.getInternalOrderNo());
                boolean success = shipmentApprovalService.approveShipmentRequest(approvalId, true, WholesaleConstants.Approval.SYSTEM_AUTO_APPROVE_COMMENT);
                if (success) {
                    log.info("订单[{}]自动审批成功", order.getInternalOrderNo());
                } else {
                    log.error("订单[{}]自动审批失败", order.getInternalOrderNo());
                }
            }

        } catch (Exception e) {
            log.error("处理订单发货审批自动申请出错: orderId={}, error={}", event.getOrderId(), e.getMessage(), e);
        }
    }
}
