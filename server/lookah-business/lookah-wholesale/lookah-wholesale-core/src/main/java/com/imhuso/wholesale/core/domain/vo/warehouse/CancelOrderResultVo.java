package com.imhuso.wholesale.core.domain.vo.warehouse;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 海外仓取消订单结果VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CancelOrderResultVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误消息 (如果 success 为 false)
     */
    private String errorMessage;

    /**
     * 海外仓返回的订单号
     */
    private String orderNo;

    /**
     * 海外仓返回的客户参考号
     */
    private String csRefNo;
}
