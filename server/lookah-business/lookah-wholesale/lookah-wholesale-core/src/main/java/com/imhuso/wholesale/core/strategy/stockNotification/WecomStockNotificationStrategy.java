package com.imhuso.wholesale.core.strategy.stockNotification;

import com.imhuso.common.core.config.WecomConfig;
import com.imhuso.wholesale.core.domain.WhsProduct;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.WhsStockNotify;
import com.imhuso.wholesale.core.event.WhsStockNotifyEvent.StockNotifyEventType;
import com.imhuso.wholesale.core.mapper.WhsProductMapper;
import com.imhuso.wholesale.core.mapper.WhsProductVariantMapper;
import com.imhuso.wholesale.core.service.IWecomService;
import com.imhuso.wholesale.core.service.INotificationConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.EnumMap;
import java.util.Map;

import static com.imhuso.wholesale.core.event.WhsStockNotifyEvent.StockNotifyEventType.NOTIFY_REGISTERED;
import static com.imhuso.wholesale.core.event.WhsStockNotifyEvent.StockNotifyEventType.NOTIFY_SENT;

/**
 * 企业微信库存通知策略实现
 * <p>
 * 使用企业微信方式发送库存到货通知
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WecomStockNotificationStrategy implements IStockNotificationStrategy {

    private final IWecomService wecomService;
    private final WhsProductVariantMapper variantMapper;
    private final WhsProductMapper productMapper;
    private final WecomConfig wecomConfig;
    private final INotificationConfigService notificationConfigService;

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    // Emoji常量定义
    private static final String EMOJI_BELL = "🔔";
    private static final String EMOJI_PACKAGE = "📦";
    private static final String EMOJI_CALENDAR = "📅";
    private static final String EMOJI_EMAIL = "📧";
    private static final String EMOJI_PHONE = "📱";
    private static final String EMOJI_TAG = "🏷️";
    private static final String EMOJI_NEW = "🆕";
    private static final String EMOJI_CHECK = "✅";
    private static final String EMOJI_ALERT = "⚠️";

    // 标题映射
    private static final Map<StockNotifyEventType, String> TITLE_MAP = Map.of(
        NOTIFY_REGISTERED, "<font color=\"#1E88E5\">" + EMOJI_BELL + " 缺货通知订阅提醒</font>",
        NOTIFY_SENT, "<font color=\"#4CAF50\">" + EMOJI_NEW + " 商品到货通知已发送</font>"
    );

    /**
     * 通知配置映射
     */
    private final Map<StockNotifyEventType, NotificationConfig> configMap = createConfigMap();

    /**
     * 创建通知配置映射
     */
    private Map<StockNotifyEventType, NotificationConfig> createConfigMap() {
        Map<StockNotifyEventType, NotificationConfig> map = new EnumMap<>(StockNotifyEventType.class);

        // 注册通知：仅通知管理员
        map.put(NOTIFY_REGISTERED, new NotificationConfig(true, false));

        // 发送通知：只通知管理员（企业微信通常只发给管理员）
        map.put(NOTIFY_SENT, new NotificationConfig(true, false));

        return map;
    }

    @Override
    public Map<StockNotifyEventType, NotificationConfig> getConfigMap() {
        return configMap;
    }

    @Override
    public String getName() {
        return "企业微信";
    }

    @Override
    public boolean sendAdminNotification(WhsStockNotify stockNotify, StockNotifyEventType eventType) {
        if (!isAdminNotificationEnabled(eventType) || !isWecomEnabled()) {
            log.debug("企业微信管理员库存通知未启用: eventType={}, enabled={}", eventType, isWecomEnabled());
            return false;
        }

        try {
            // 获取变体信息
            WhsProductVariant variant = variantMapper.selectById(stockNotify.getVariantId());
            if (variant == null) {
                log.error("变体不存在，无法发送通知: variantId={}", stockNotify.getVariantId());
                return false;
            }

            // 获取产品信息
            WhsProduct product = productMapper.selectById(variant.getProductId());
            if (product == null) {
                log.error("产品不存在，无法发送通知: productId={}", variant.getProductId());
                return false;
            }

            // 获取标题
            String title = TITLE_MAP.getOrDefault(eventType, "库存通知");

            // 构造消息内容
            StringBuilder content = buildNotificationContent(stockNotify, variant, product, eventType);

            // 发送企业微信消息
            wecomService.sendMarkdownMessage(title, content.toString());
            return true;
        } catch (Exception e) {
            log.error("发送企业微信管理员库存通知失败: notifyId={}, eventType={}, error={}",
                stockNotify.getId(), eventType, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean sendCustomerNotification(WhsStockNotify stockNotify, StockNotifyEventType eventType) {
        // 企业微信不直接发送客户通知
        return false;
    }

    /**
     * 构建通知内容
     */
    private StringBuilder buildNotificationContent(WhsStockNotify stockNotify, WhsProductVariant variant,
                                                   WhsProduct product, StockNotifyEventType eventType) {
        StringBuilder content = new StringBuilder();

        // 添加标题和分隔线 - 移除emoji，因为标题中已经有了
        content.append("# ").append(TITLE_MAP.getOrDefault(eventType, "库存通知")).append("\n\n");
        content.append("<font color=\"#1E88E5\">").append("—".repeat(20)).append("</font>\n\n");

        if (eventType == NOTIFY_REGISTERED) {
            buildRegisteredNotificationContent(content, stockNotify, variant, product);
        } else if (eventType == NOTIFY_SENT) {
            buildSentNotificationContent(content, stockNotify, variant, product);
        }

        return content;
    }

    /**
     * 构建注册通知内容
     */
    private void buildRegisteredNotificationContent(StringBuilder content, WhsStockNotify stockNotify,
                                                    WhsProductVariant variant, WhsProduct product) {
        content.append("> **客户订阅了产品到货通知**\n\n");

        content.append("<font color=\"#546E7A\">").append(EMOJI_CALENDAR).append(" **时间：**</font> ")
            .append(DATE_FORMAT.format(stockNotify.getCreateTime())).append("\n\n");

        content.append("<font color=\"#43A047\">").append(EMOJI_PACKAGE).append(" **产品：**</font> ")
            .append("<font color=\"#FF5722\">**").append(product.getItemName()).append("**</font>\n\n");

        content.append("<font color=\"#0288D1\">").append(EMOJI_EMAIL).append(" **客户邮箱：**</font> ")
            .append(stockNotify.getEmail()).append("\n\n");

        if (stockNotify.getWhatsapp() != null && !stockNotify.getWhatsapp().isEmpty()) {
            content.append("<font color=\"#00897B\">").append(EMOJI_PHONE).append(" **WhatsApp：**</font> ")
                .append(stockNotify.getWhatsapp()).append("\n\n");
        }

        content.append("<font color=\"#757575\">").append(EMOJI_TAG).append(" **SKU：**</font> ")
            .append("`").append(variant.getSkuCode()).append("`\n\n");

        // 添加提示信息
        content.append("<font color=\"#FB8C00\">").append(EMOJI_ALERT).append(" 请及时关注此产品库存状态").append("</font>");
    }

    /**
     * 构建已发送通知内容
     */
    private void buildSentNotificationContent(StringBuilder content, WhsStockNotify stockNotify,
                                              WhsProductVariant variant, WhsProduct product) {
        content.append("> **系统已向客户发送产品到货通知**\n\n");

        content.append("<font color=\"#546E7A\">").append(EMOJI_CALENDAR).append(" **通知时间：**</font> ")
            .append(DATE_FORMAT.format(stockNotify.getNotifyTime())).append("\n\n");

        content.append("<font color=\"#43A047\">").append(EMOJI_PACKAGE).append(" **产品：**</font> ")
            .append("<font color=\"#FF5722\">**").append(product.getItemName()).append("**</font>\n\n");

        content.append("<font color=\"#0288D1\">").append(EMOJI_EMAIL).append(" **客户邮箱：**</font> ")
            .append(stockNotify.getEmail()).append("\n\n");

        content.append("<font color=\"#8BC34A\">").append(EMOJI_CHECK).append(" **通知状态：**</font> ")
            .append("<font color=\"#4CAF50\">已发送</font>\n\n");

        content.append("<font color=\"#757575\">").append(EMOJI_TAG).append(" **SKU：**</font> ")
            .append("`").append(variant.getSkuCode()).append("`\n\n");
    }

    /**
     * 检查企业微信是否启用
     */
    private boolean isWecomEnabled() {
        // 使用数据库配置检查企业微信库存通知是否启用
        boolean notificationEnabled = notificationConfigService.isStockWecomEnabled();
        boolean configEnabled = wecomConfig.isAvailable();
        boolean enabled = configEnabled && notificationEnabled;

        log.debug("企业微信库存通知状态: {}, configEnabled={}, notificationEnabled={}",
            enabled ? "已启用" : "未启用", configEnabled, notificationEnabled);
        return enabled;
    }
}
