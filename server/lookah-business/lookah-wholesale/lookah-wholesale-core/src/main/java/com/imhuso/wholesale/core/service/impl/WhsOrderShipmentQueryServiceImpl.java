package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.wholesale.core.domain.WhsOrderShipment;
import com.imhuso.wholesale.core.domain.vo.admin.WhsLogisticsPackageVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentItemVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentVo;
import com.imhuso.wholesale.core.mapper.WhsLogisticsPackageMapper;
import com.imhuso.wholesale.core.mapper.WhsOrderShipmentItemMapper;
import com.imhuso.wholesale.core.mapper.WhsOrderShipmentMapper;
import com.imhuso.wholesale.core.service.IWhsOrderShipmentQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单发货查询Service实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsOrderShipmentQueryServiceImpl implements IWhsOrderShipmentQueryService {

    private final WhsOrderShipmentMapper orderShipmentMapper;
    private final WhsOrderShipmentItemMapper orderShipmentItemMapper;
    private final WhsLogisticsPackageMapper logisticsPackageMapper;

    @Override
    public List<WhsOrderShipmentVo> getOrderShipmentRecords(Long orderId) {
        // 1. 查询指定订单的所有发货记录
        LambdaQueryWrapper<WhsOrderShipment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhsOrderShipment::getOrderId, orderId);
        queryWrapper.orderByDesc(WhsOrderShipment::getCreateTime);

        // 2. 转换为VO并获取相关的发货项和物流包裹信息
        List<WhsOrderShipmentVo> shipmentVos = orderShipmentMapper.selectVoList(queryWrapper);

        // 3. 为每个发货记录加载其关联的包裹和发货项信息
        for (WhsOrderShipmentVo shipmentVo : shipmentVos) {
            loadShipmentDetails(shipmentVo);
        }

        return shipmentVos;
    }

    @Override
    public WhsOrderShipmentVo getShipmentById(Long shipmentId) {
        if (shipmentId == null) {
            log.warn("查询发货记录时，发货记录ID不能为空");
            return null;
        }

        // 1. 查询指定ID的发货记录
        WhsOrderShipmentVo shipmentVo = orderShipmentMapper.selectVoById(shipmentId);
        if (shipmentVo == null) {
            log.warn("未找到ID为{}的发货记录", shipmentId);
            return null;
        }

        // 2. 加载发货记录的详细信息
        loadShipmentDetails(shipmentVo);

        return shipmentVo;
    }

    /**
     * 加载发货记录的详细信息（包裹和发货项）
     *
     * @param shipmentVo 发货记录VO对象
     */
    private void loadShipmentDetails(WhsOrderShipmentVo shipmentVo) {
        // 1. 加载发货项信息
        List<WhsOrderShipmentItemVo> shipmentItems = orderShipmentItemMapper.selectItemsByShipmentId(shipmentVo.getId());

        // 2. 将物流包裹信息加载到发货记录中
        List<WhsLogisticsPackageVo> packages = logisticsPackageMapper.selectPackagesByShipmentId(shipmentVo.getId());

        // 3. 初始化每个包裹的追踪链接
        if (packages != null) {
            for (WhsLogisticsPackageVo pkg : packages) {
                pkg.getTrackingUrl(); // 初始化追踪链接
            }
        }

        // 4. 按包裹ID对发货项进行分组
        Map<Long, List<WhsOrderShipmentItemVo>> itemsByPackage = shipmentItems.stream()
            .filter(item -> item.getPackageId() != null)
            .collect(Collectors.groupingBy(WhsOrderShipmentItemVo::getPackageId));

        // 5. 将发货项关联到对应的包裹
        if (ObjectUtil.isNotNull(packages)) {
            for (WhsLogisticsPackageVo pkg : packages) {
                List<WhsOrderShipmentItemVo> packageItems = itemsByPackage.get(pkg.getId());
                if (packageItems != null) {
                    pkg.setShipmentItems(packageItems);
                }
            }
        }

        // 6. 设置包裹列表到发货记录中
        shipmentVo.setLogisticsPackages(packages);

        // 7. 计算并设置发货汇总信息
        String shipmentSummary = calculateShipmentSummary(shipmentItems);
        shipmentVo.setShipmentSummary(shipmentSummary);
    }

    /**
     * 批量查询多个订单的发货记录（仅基本信息，不包含详细的发货项和包裹信息）
     * 用于订单列表页面的仓库信息展示，避免N+1查询问题
     */
    @Override
    public Map<Long, List<WhsOrderShipmentVo>> batchGetOrderShipmentRecords(List<Long> orderIds) {
        if (orderIds == null || orderIds.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            // 使用IN查询批量获取所有订单的发货记录
            LambdaQueryWrapper<WhsOrderShipment> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(WhsOrderShipment::getOrderId, orderIds);
            queryWrapper.orderByDesc(WhsOrderShipment::getCreateTime);

            List<WhsOrderShipmentVo> allShipments = orderShipmentMapper.selectVoList(queryWrapper);

            // 按订单ID分组
            return allShipments.stream()
                .collect(Collectors.groupingBy(WhsOrderShipmentVo::getOrderId));

        } catch (Exception e) {
            log.error("批量查询订单发货记录时发生错误: {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    /**
     * 计算发货汇总信息
     * 格式：箱装数量 展示盒数量 单品数量
     *
     * @param shipmentItems 发货项列表
     * @return 发货汇总字符串，格式如："5 3 10"
     */
    private String calculateShipmentSummary(List<WhsOrderShipmentItemVo> shipmentItems) {
        if (shipmentItems == null || shipmentItems.isEmpty()) {
            return "0 0 0";
        }

        int caseCount = 0;      // 箱装数量
        int displayCount = 0;   // 展示盒数量
        int individualCount = 0; // 单品数量

        for (WhsOrderShipmentItemVo item : shipmentItems) {
            Integer packagingType = item.getPackagingType();
            Integer quantity = item.getQuantity();

            if (quantity == null || quantity <= 0) {
                continue;
            }

            if (packagingType == null) {
                // 如果包装类型为空，默认为单品
                individualCount += quantity;
            } else {
                switch (packagingType) {
                    case 0: // 单品
                        individualCount += quantity;
                        break;
                    case 1: // 展示盒
                        displayCount += quantity;
                        break;
                    case 2: // 箱装
                        caseCount += quantity;
                        break;
                    default:
                        // 未知类型默认为单品
                        individualCount += quantity;
                        break;
                }
            }
        }

        return String.format("%d %d %d", caseCount, displayCount, individualCount);
    }
}
