package com.imhuso.wholesale.core.domain.vo.admin;

import com.imhuso.common.translation.annotation.Translation;
import com.imhuso.wholesale.core.constant.TransConstant;
import com.imhuso.wholesale.core.domain.WhsOrder;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 发货管理视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMappers({
    @AutoMapper(target = WhsOrder.class),
    @AutoMapper(target = WhsOrderVo.class)
})
public class WhsShipmentManagementVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long id;

    /**
     * 系统订单号
     */
    private String orderNo;

    /**
     * 客户订单号
     */
    private String customerOrderNo;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 收货人姓名
     */
    private String shippingName;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 发货状态
     */
    private Integer shipmentStatus;

    /**
     * 发货状态文本
     */
    private String shipmentStatusText;

    /**
     * 商品总PCS数量
     */
    private Integer totalPcs;

    /**
     * 已发货PCS数量
     */
    private Integer shippedPcs;

    /**
     * 订单总箱数
     */
    private Integer totalCases;

    /**
     * 已发货箱数
     */
    private Integer shippedCases;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 发货仓库名称
     */
    private String shippingWarehouses;

    /**
     * 库存状态
     * 0-库存充足, 1-库存不足, 2-无库存, 3-部分库存
     */
    private Integer stockStatus;

    /**
     * 审批状态
     * 0-待审核, 1-审核通过, 2-审核拒绝, 3-已撤销, null-无需审批
     */
    private Integer approvalStatus;

    /**
     * 审批状态文本
     */
    @Translation(type = TransConstant.WHOLESALE_SHIPMENT_APPROVAL_STATUS, other = "approvalStatus", mapper = "approvalStatus")
    private String approvalStatusText;

    /**
     * 审批意见（审批通过时的意见）
     */
    private String approvalComment;
}
