package com.imhuso.wholesale.core.utils;

import java.util.Map;

/**
 * SKU映射工具类
 * 
 * <AUTHOR>
 */
public class SkuMappingUtils {

    private SkuMappingUtils() {
        // 私有构造函数，防止实例化
    }

    /**
     * 从映射中查找变体ID，支持忽略大小写匹配
     * 
     * @param variantIdMap SKU到变体ID的映射
     * @param skuCode SKU编码
     * @return 变体ID，如果未找到则返回null
     */
    public static Long findVariantIdBySku(Map<String, Long> variantIdMap, String skuCode) {
        if (variantIdMap == null || skuCode == null) {
            return null;
        }

        // 首先尝试精确匹配
        Long variantId = variantIdMap.get(skuCode);
        if (variantId != null) {
            return variantId;
        }

        // 遍历映射，忽略大小写比较
        for (Map.Entry<String, Long> entry : variantIdMap.entrySet()) {
            if (entry.getKey() != null && entry.getKey().equalsIgnoreCase(skuCode)) {
                return entry.getValue();
            }
        }

        return null;
    }
}
