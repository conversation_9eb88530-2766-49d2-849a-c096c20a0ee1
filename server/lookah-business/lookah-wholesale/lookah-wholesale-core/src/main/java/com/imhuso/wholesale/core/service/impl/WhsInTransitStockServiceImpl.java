package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.common.core.utils.SpringUtils;
import com.imhuso.wholesale.core.domain.WhsInTransitStock;
import com.imhuso.wholesale.core.domain.WhsInboundOrderItem;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.mapper.WhsInTransitStockMapper;
import com.imhuso.wholesale.core.mapper.WhsInboundOrderItemMapper;
import com.imhuso.wholesale.core.mapper.WhsProductVariantMapper;
import com.imhuso.wholesale.core.service.IWhsInTransitStockService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 在途库存服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsInTransitStockServiceImpl implements IWhsInTransitStockService {

    private final WhsInboundOrderItemMapper inboundOrderItemMapper;
    private final WhsInTransitStockMapper inTransitStockMapper;

    /**
     * 更新所有变体的在途库存
     * 从入库单中计算并更新在途库存表
     *
     * @return 更新的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateAllInTransitStock() {
        log.info("开始更新所有变体的在途库存");
        long startTime = System.currentTimeMillis();

        try {
            // 查询所有变体ID
            List<Long> allVariantIds = getAllVariantIdsFromInboundOrders();
            if (CollUtil.isEmpty(allVariantIds)) {
                log.info("没有找到需要更新在途库存的变体");
                return 0;
            }

            log.info("找到{}个需要更新在途库存的变体", allVariantIds.size());

            // 批量计算在途库存
            List<WhsInboundOrderItem> inTransitStocks = inboundOrderItemMapper.calculateInTransitStockBatch(allVariantIds);
            if (CollUtil.isEmpty(inTransitStocks)) {
                log.info("没有在途库存数据需要更新");
                return 0;
            }

            log.info("计算得到{}个变体的在途库存数据", inTransitStocks.size());

            // 转换为在途库存对象
            List<WhsInTransitStock> stocksToUpdate = convertToInTransitStocks(inTransitStocks);

            // 批量更新在途库存
            int updatedCount = batchUpdateInTransitStocks(stocksToUpdate);

            long duration = System.currentTimeMillis() - startTime;
            log.info("更新在途库存完成，耗时{}ms，更新{}条记录", duration, updatedCount);

            return updatedCount;
        } catch (Exception e) {
            log.error("更新在途库存异常", e);
            throw e;
        }
    }

    /**
     * 获取变体的在途库存
     *
     * @param variantId 变体ID
     * @return 在途库存数量
     */
    @Override
    public int getInTransitStock(Long variantId) {
        if (variantId == null) {
            return 0;
        }

        try {
            WhsInTransitStock stock = inTransitStockMapper.selectOne(
                new LambdaQueryWrapper<WhsInTransitStock>()
                    .eq(WhsInTransitStock::getVariantId, variantId)
            );

            return stock != null ? stock.getQuantity() : 0;
        } catch (Exception e) {
            log.error("获取变体ID={}的在途库存异常", variantId, e);
            return 0;
        }
    }

    /**
     * 批量获取变体的在途库存
     *
     * @param variantIds 变体ID列表
     * @return 变体ID到在途库存数量的映射
     */
    @Override
    public Map<Long, Integer> getInTransitStocks(List<Long> variantIds) {
        if (CollUtil.isEmpty(variantIds)) {
            return Collections.emptyMap();
        }

        try {
            List<WhsInTransitStock> stocks = inTransitStockMapper.selectList(
                new LambdaQueryWrapper<WhsInTransitStock>()
                    .in(WhsInTransitStock::getVariantId, variantIds)
            );

            if (CollUtil.isEmpty(stocks)) {
                return Collections.emptyMap();
            }

            return stocks.stream()
                .collect(Collectors.toMap(WhsInTransitStock::getVariantId, WhsInTransitStock::getQuantity));
        } catch (Exception e) {
            log.error("批量获取变体在途库存异常", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 获取所有需要更新在途库存的变体ID
     *
     * @return 变体ID列表
     */
    private List<Long> getAllVariantIdsFromInboundOrders() {
        try {
            // 查询所有在途状态的入库单明细中的变体ID
            List<WhsInboundOrderItem> items = inboundOrderItemMapper.selectList(
                new LambdaQueryWrapper<WhsInboundOrderItem>()
                    .select(WhsInboundOrderItem::getVariantId)
                    .groupBy(WhsInboundOrderItem::getVariantId)
            );

            if (CollUtil.isEmpty(items)) {
                return Collections.emptyList();
            }

            return items.stream()
                .map(WhsInboundOrderItem::getVariantId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取所有变体ID异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 将入库单明细转换为在途库存对象
     *
     * @param inTransitStocks 入库单明细列表
     * @return 在途库存对象列表
     */
    private List<WhsInTransitStock> convertToInTransitStocks(List<WhsInboundOrderItem> inTransitStocks) {
        if (CollUtil.isEmpty(inTransitStocks)) {
            return Collections.emptyList();
        }

        // 收集所有需要查询skuCode的变体ID
        List<Long> variantIdsNeedSkuCode = inTransitStocks.stream()
            .filter(item -> item.getVariantId() != null && (item.getSkuCode() == null || item.getSkuCode().isEmpty()))
            .map(WhsInboundOrderItem::getVariantId)
            .collect(Collectors.toList());

        // 存储变体ID到skuCode的映射
        Map<Long, String> variantIdToSkuCodeMap = new HashMap<>();

        // 如果有需要查询skuCode的变体ID，则从数据库中查询
        if (!CollUtil.isEmpty(variantIdsNeedSkuCode)) {
            try {
                // 查询变体信息
                LambdaQueryWrapper<WhsProductVariant> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.select(WhsProductVariant::getId, WhsProductVariant::getSkuCode)
                    .in(WhsProductVariant::getId, variantIdsNeedSkuCode);

                List<WhsProductVariant> variants = SpringUtils.getBean(WhsProductVariantMapper.class).selectList(queryWrapper);

                // 构建变体ID到skuCode的映射
                if (!CollUtil.isEmpty(variants)) {
                    variantIdToSkuCodeMap = variants.stream()
                        .collect(Collectors.toMap(
                            WhsProductVariant::getId,
                            variant -> variant.getSkuCode() != null ? variant.getSkuCode() : "",
                            (existing, replacement) -> existing
                        ));
                }

                log.debug("为{}个变体查询到skuCode信息", variantIdToSkuCodeMap.size());
            } catch (Exception e) {
                log.error("查询变体skuCode信息异常", e);
            }
        }

        List<WhsInTransitStock> result = new ArrayList<>(inTransitStocks.size());
        for (WhsInboundOrderItem item : inTransitStocks) {
            if (item.getVariantId() == null || item.getQuantity() == null) {
                continue;
            }

            WhsInTransitStock stock = new WhsInTransitStock();
            stock.setId(cn.hutool.core.util.IdUtil.getSnowflakeNextId());
            stock.setVariantId(item.getVariantId());

            // 如果item中的skuCode为空，则从映射中获取
            String skuCode = item.getSkuCode();
            if (skuCode == null || skuCode.isEmpty()) {
                skuCode = variantIdToSkuCodeMap.get(item.getVariantId());
                if (skuCode == null || skuCode.isEmpty()) {
                    log.warn("变体ID={}的skuCode为空，跳过该记录", item.getVariantId());
                    continue; // 如果仍然为空，则跳过该记录
                }
            }

            stock.setSkuCode(skuCode);
            stock.setQuantity(item.getQuantity());

            result.add(stock);
        }

        return result;
    }

    /**
     * 批量更新在途库存
     *
     * @param stocks 在途库存列表
     * @return 更新的记录数
     */
    private int batchUpdateInTransitStocks(List<WhsInTransitStock> stocks) {
        if (CollUtil.isEmpty(stocks)) {
            return 0;
        }

        try {
            // 先清空在途库存表
            inTransitStockMapper.delete(null);

            // 批量插入新的在途库存数据
            return inTransitStockMapper.batchInsertOrUpdate(stocks);
        } catch (Exception e) {
            log.error("批量更新在途库存异常", e);
            return 0;
        }
    }
}
