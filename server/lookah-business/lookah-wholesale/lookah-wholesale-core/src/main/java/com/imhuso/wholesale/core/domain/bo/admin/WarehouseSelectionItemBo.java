package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 仓库选择项业务对象
 *
 * <AUTHOR>
 */
@Data
public class WarehouseSelectionItemBo {

    /**
     * 发货方案项ID
     */
    @NotNull(message = "发货方案项ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long planItemId;

    /**
     * 仓库ID
     */
    @NotNull(message = "仓库ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long warehouseId;

    /**
     * 是否选中
     */
    @NotNull(message = "选中状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private Boolean selected;

    /**
     * 发货数量
     */
    @NotNull(message = "发货数量不能为空", groups = {AddGroup.class, EditGroup.class})
    @Min(value = 0, message = "发货数量不能小于0", groups = {AddGroup.class, EditGroup.class})
    private Integer quantity;
}
