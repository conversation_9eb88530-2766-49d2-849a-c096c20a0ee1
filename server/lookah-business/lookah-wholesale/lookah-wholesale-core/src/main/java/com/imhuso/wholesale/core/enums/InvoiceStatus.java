package com.imhuso.wholesale.core.enums;

import com.imhuso.common.translation.core.EnumTranslatableInterface;
import lombok.Getter;

/**
 * 发票状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum InvoiceStatus implements EnumTranslatableInterface {

    /**
     * 待处理
     */
    PENDING(0),

    /**
     * 已发送
     */
    SENT(1);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 构造函数
     */
    InvoiceStatus(int value) {
        this.value = value;
    }
}
