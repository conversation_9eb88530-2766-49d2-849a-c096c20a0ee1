package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.vo.warehouse.InboundOrderSyncResultVo;

/**
 * 入库单同步服务接口
 *
 * <AUTHOR>
 */
public interface IWhsInboundOrderSyncService {

    /**
     * 同步所有海外仓的入库单
     *
     * @return 同步成功的仓库数量
     */
    int syncAllWarehouses();

    /**
     * 全量同步所有海外仓的入库单（查询最近一年的数据）
     *
     * @return 同步成功的仓库数量
     */
    int syncAllWarehousesFull();

    /**
     * 同步指定仓库的入库单
     *
     * @param warehouseId 仓库ID
     * @return 同步结果
     */
    InboundOrderSyncResultVo syncWarehouseInboundOrders(Long warehouseId);

    /**
     * 同步指定仓库的入库单（支持自定义时间范围）
     *
     * @param warehouseId 仓库ID
     * @param daysBefore 查询多少天前的数据（默认60天，全量同步可设置为365天）
     * @return 同步结果
     */
    InboundOrderSyncResultVo syncWarehouseInboundOrders(Long warehouseId, Integer daysBefore);
}
