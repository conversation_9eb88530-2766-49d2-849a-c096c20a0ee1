package com.imhuso.wholesale.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单文件类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrderFileType {

    /**
     * 发票文件
     */
    INVOICE("INVOICE", "发票文件"),

    /**
     * 合同文件
     */
    CONTRACT("CONTRACT", "合同文件"),

    /**
     * 其他文件
     */
    OTHER("OTHER", "其他文件");

    /**
     * 类型代码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     *
     * @param code 类型代码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static OrderFileType fromCode(String code) {
        for (OrderFileType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 类型代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return fromCode(code) != null;
    }
}
