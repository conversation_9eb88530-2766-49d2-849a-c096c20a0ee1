package com.imhuso.wholesale.core.domain.vo.admin;

import com.imhuso.wholesale.core.domain.WhsInboundOrderItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 入库单明细视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsInboundOrderItem.class)
public class WhsInboundOrderItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 明细ID
     */
    private Long id;

    /**
     * 入库单ID
     */
    private Long inboundOrderId;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 已接收数量
     */
    private Integer receivedQuantity;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
