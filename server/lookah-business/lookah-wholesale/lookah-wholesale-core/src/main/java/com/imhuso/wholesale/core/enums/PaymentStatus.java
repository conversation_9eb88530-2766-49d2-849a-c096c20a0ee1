package com.imhuso.wholesale.core.enums;

import com.imhuso.common.translation.core.EnumTranslatableInterface;
import lombok.Getter;

/**
 * 支付状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum PaymentStatus implements EnumTranslatableInterface {

    /**
     * 待支付
     */
    PENDING(0),

    /**
     * 部分支付
     */
    PARTIAL_PAID(1),

    /**
     * 已支付
     */
    PAID(2);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 构造函数
     */
    PaymentStatus(int value) {
        this.value = value;
    }
}
