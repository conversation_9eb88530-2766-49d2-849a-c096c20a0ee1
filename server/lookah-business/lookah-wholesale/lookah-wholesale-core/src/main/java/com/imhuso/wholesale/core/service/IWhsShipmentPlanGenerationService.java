package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.bo.admin.WhsShipmentPlanGenerationBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanGenerationVo;

/**
 * 发货方案生成服务接口
 * 负责发货方案的生成相关操作
 *
 * <AUTHOR>
 */
public interface IWhsShipmentPlanGenerationService {

    /**
     * 为订单生成发货方案
     *
     * @param orderId 订单ID
     */
    void generateShipmentPlans(Long orderId);

    /**
     * 准备订单数据用于生成发货方案
     *
     * @param orderId 订单ID
     * @return 准备好的订单数据
     */
    WhsShipmentPlanGenerationVo prepareOrderDataForPlan(Long orderId);

    /**
     * 提交自定义发货方案
     *
     * @param planBo 自定义发货方案
     */
    void submitCustomShipmentPlan(WhsShipmentPlanGenerationBo planBo);
}
