package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.vo.admin.ErpProviderStatusVo;

import java.util.List;

/**
 * ERP配置管理服务接口
 *
 * <AUTHOR>
 */
public interface IWhsErpConfigService {

    /**
     * 获取当前配置的ERP提供商类型
     *
     * @return ERP提供商类型
     */
    String getCurrentErpProviderType();

    /**
     * 设置ERP提供商类型
     *
     * @param providerType 提供商类型
     * @return 设置是否成功
     */
    boolean setErpProviderType(String providerType);

    /**
     * 获取所有ERP提供商状态
     *
     * @return 提供商状态列表
     */
    List<ErpProviderStatusVo> getAllProviderStatus();

    /**
     * 测试ERP提供商连接
     *
     * @param providerType 提供商类型
     * @return 连接测试结果
     */
    boolean testErpConnection(String providerType);

    /**
     * 获取ERP系统配置信息
     *
     * @return 配置信息
     */
    String getErpConfigInfo();

    /**
     * 刷新ERP提供商状态
     */
    void refreshProviderStatus();
}
