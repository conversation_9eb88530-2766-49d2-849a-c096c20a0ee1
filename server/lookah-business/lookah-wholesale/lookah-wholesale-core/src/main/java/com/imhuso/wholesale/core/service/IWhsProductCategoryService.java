package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.WhsProductCategory;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductCategoryBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductCategoryVo;

import java.util.List;
import java.util.Map;

/**
 * 批发产品分类Service接口
 *
 * <AUTHOR>
 */
public interface IWhsProductCategoryService {

    /**
     * 查询产品分类列表
     *
     * @return 产品分类列表
     */
    List<WhsProductCategoryVo> selectCategoryList(WhsProductCategoryBo bo);

    /**
     * 根据分类ID查询信息
     *
     * @param categoryId 分类ID
     * @return 分类信息
     */
    WhsProductCategoryVo selectCategoryById(Long categoryId);

    /**
     * 是否存在子分类
     *
     * @param categoryId 分类ID
     * @return 结果 true 存在 false 不存在
     */
    boolean hasChildByCategoryId(Long categoryId);

    /**
     * 查询分类是否存在商品
     *
     * @param categoryId 分类ID
     * @return 结果 true 存在 false 不存在
     */
    boolean hasProductByCategoryId(Long categoryId);

    /**
     * 校验分类名称是否唯一
     *
     * @param category 分类信息
     * @return 结果
     */
    boolean checkCategoryNameUnique(WhsProductCategory category);

    /**
     * 新增分类
     *
     * @param category 分类信息
     * @return 结果
     */
    int insertCategory(WhsProductCategory category);

    /**
     * 修改分类
     *
     * @param bo 分类业务对象
     * @return 结果
     */
    int updateCategory(WhsProductCategoryBo bo);

    /**
     * 删除分类信息
     *
     * @param categoryId 分类ID
     * @return 结果
     */
    int deleteCategory(Long categoryId);

    /**
     * 批量获取分类名称
     *
     * @param categoryIds 分类ID列表
     * @return 分类ID到名称的映射
     */
    Map<Long, String> getCategoryNames(List<Long> categoryIds);
}
