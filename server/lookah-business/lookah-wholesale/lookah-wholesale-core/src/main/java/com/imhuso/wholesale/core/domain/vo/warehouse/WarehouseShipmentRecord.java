package com.imhuso.wholesale.core.domain.vo.warehouse;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用于跟踪已成功发货的海外仓订单
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseShipmentRecord {
    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 包裹ID
     */
    private Long packageId;

    /**
     * 订单号（仅用于记录日志）
     */
    private String orderNo;

    /**
     * 外部系统（如海外仓）返回的发货单号
     * 用于取消操作
     */
    private String externalShipmentId;
}
