package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.vo.admin.WhsStateVo;

import java.util.List;

/**
 * 州/省服务接口
 *
 * <AUTHOR>
 */
public interface IWhsStateService {

    /**
     * 根据国家ID和州/省代码获取州/省信息
     *
     * @param countryId 国家ID
     * @param code      州/省代码
     * @return 州/省信息
     */
    WhsStateVo getStateByCountryIdAndCode(Long countryId, String code);

    /**
     * 根据国家ID和州/省代码获取州/省名称
     *
     * @param countryId 国家ID
     * @param code      州/省代码
     * @return 州/省名称
     */
    String getStateNameByCountryIdAndCode(Long countryId, String code);

    /**
     * 根据国家代码和州/省代码获取州/省名称
     *
     * @param countryCode 国家代码
     * @param stateCode   州/省代码
     * @return 州/省名称
     */
    String getStateNameByCountryCodeAndStateCode(String countryCode, String stateCode);

    /**
     * 根据国家ID获取州/省列表
     *
     * @param countryId 国家ID
     * @return 州/省列表
     */
    List<WhsStateVo> getStatesByCountryId(Long countryId);

    /**
     * 根据国家ID获取州/省列表（支持指定返回类型）
     *
     * @param countryId 国家ID
     * @param clazz 返回类型的Class
     * @param <T> 返回类型
     * @return 州/省列表
     */
    <T> List<T> getStatesByCountryId(Long countryId, Class<T> clazz);
}
