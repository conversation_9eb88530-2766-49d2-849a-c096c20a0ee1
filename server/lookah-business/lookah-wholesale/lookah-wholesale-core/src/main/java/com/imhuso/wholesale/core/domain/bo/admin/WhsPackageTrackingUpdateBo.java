package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 包裹跟踪信息更新业务对象
 *
 * <AUTHOR>
 */
@Data
public class WhsPackageTrackingUpdateBo {

    /**
     * 跟踪号
     */
    @NotBlank(message = "跟踪号不能为空", groups = EditGroup.class)
    private String trackingNumber;

    /**
     * 承运商
     */
    private String carrier;

    /**
     * 备注
     */
    private String remark;
}
