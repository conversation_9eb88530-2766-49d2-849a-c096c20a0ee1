package com.imhuso.wholesale.core.service;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsShippingAddressBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShippingAddressVo;

import java.util.List;

/**
 * 会员地址管理Service接口
 *
 * <AUTHOR>
 */
public interface IWhsShippingAddressAdminService {

    /**
     * 查询会员地址列表
     *
     * @param bo        地址查询参数
     * @param pageQuery 分页参数
     * @return 地址分页列表
     */
    TableDataInfo<WhsShippingAddressVo> queryPageList(WhsShippingAddressBo bo, PageQuery pageQuery);

    /**
     * 查询会员地址详情
     *
     * @param addressId 地址ID
     * @return 地址详情
     */
    WhsShippingAddressVo getAddressDetail(Long addressId);

    /**
     * 查询会员的地址列表
     *
     * @param memberId 会员ID
     * @return 地址列表
     */
    List<WhsShippingAddressVo> getAddressByMemberId(Long memberId);

    /**
     * 新增会员地址
     *
     * @param bo 地址信息
     * @return 新增结果
     */
    int insertAddress(WhsShippingAddressBo bo);

    /**
     * 更新会员地址
     *
     * @param bo 地址信息
     * @return 更新结果
     */
    int updateAddress(WhsShippingAddressBo bo);

    /**
     * 删除会员地址
     *
     * @param addressId 地址ID
     * @return 删除结果
     */
    int deleteAddress(Long addressId);
}
