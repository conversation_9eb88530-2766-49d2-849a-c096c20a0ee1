package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 产品属性值对象 whs_product_attribute_value
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_product_attribute_value")
public class WhsProductAttributeValue extends BaseEntity {

    /**
     * 属性值ID
     */
    @TableId
    private Long id;

    /**
     * 属性ID
     */
    private Long attributeId;

    /**
     * 属性值键(如black,plastic)
     */
    private String valueKey;

    /**
     * 属性值显示名(如黑色,塑料)
     */
    private String valueDisplay;

    /**
     * 显示顺序
     */
    private Integer sort;

    /**
     * 状态（0停用 1正常）
     */
    private String status;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;
}
