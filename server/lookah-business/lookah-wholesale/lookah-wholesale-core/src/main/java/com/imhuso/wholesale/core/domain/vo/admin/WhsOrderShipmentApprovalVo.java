package com.imhuso.wholesale.core.domain.vo.admin;

import com.imhuso.common.translation.annotation.Translation;
import com.imhuso.wholesale.core.constant.TransConstant;
import io.github.linpeilie.annotations.AutoMapper;
import com.imhuso.wholesale.core.domain.WhsOrderShipmentApproval;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单发货审批视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsOrderShipmentApproval.class)
public class WhsOrderShipmentApprovalVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 审核ID
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 申请人ID
     */
    private Long applicantId;

    /**
     * 申请人姓名
     */
    private String applicantName;

    /**
     * 申请理由
     */
    private String applicationReason;

    /**
     * 审核状态
     */
    private Integer approvalStatus;

    /**
     * 审核状态文本
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, other = "approvalStatus", mapper = "approvalStatus")
    private String approvalStatusText;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 操作类型文本
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, other = "operationType", mapper = "operationType")
    private String operationTypeText;

    /**
     * 操作序号，同一订单的操作按时间顺序递增
     */
    private Integer operationSequence;

    /**
     * 记录状态
     */
    private String recordStatus;

    /**
     * 记录状态文本
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, other = "recordStatus", mapper = "recordStatus")
    private String recordStatusText;

    /**
     * 审核人ID
     */
    private Long approverId;

    /**
     * 审核人姓名
     */
    private String approverName;

    /**
     * 审核时间
     */
    private Date approvalTime;

    /**
     * 审核意见
     */
    private String approvalComment;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
