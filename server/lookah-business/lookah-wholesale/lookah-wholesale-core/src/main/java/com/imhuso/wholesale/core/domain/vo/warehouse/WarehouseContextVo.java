package com.imhuso.wholesale.core.domain.vo.warehouse;

import com.imhuso.wholesale.core.domain.WhsOverseasWarehouseAccount;
import com.imhuso.wholesale.core.domain.WhsOverseasWarehouseProvider;
import com.imhuso.wholesale.core.domain.WhsWarehouse;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 仓库上下文视图对象
 *
 * <AUTHOR>
 */
@Data
public class WarehouseContextVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 仓库信息
     */
    private WhsWarehouse warehouse;

    /**
     * 提供商信息
     */
    private WhsOverseasWarehouseProvider provider;

    /**
     * 账号信息
     */
    private WhsOverseasWarehouseAccount account;

    /**
     * 仓库配置
     */
    private Map<String, String> warehouseConfig = new HashMap<>();

    /**
     * 账号配置
     */
    private Map<String, String> accountConfig = new HashMap<>();

    /**
     * 提供商类型（冗余，便于直接获取）
     */
    private String providerType;

    /**
     * 仓库ID（冗余，便于直接获取）
     */
    private Long warehouseId;

    /**
     * 支持的映射策略类型
     */
    private String mappingStrategyType;

    /**
     * 当前使用的映射策略类型
     */
    private String currentMappingStrategy;

    /**
     * 自定义数据，用于在上下文中传递各种临时数据
     */
    private Map<String, Object> customData = new HashMap<>();

    /**
     * 获取自定义数据
     *
     * @param key 键名
     * @param <T> 返回类型
     * @return 数据值
     */
    @SuppressWarnings("unchecked")
    public <T> T getCustomData(String key) {
        return (T) customData.get(key);
    }

    /**
     * 设置自定义数据
     *
     * @param key 键名
     * @param value 数据值
     */
    public void setCustomData(String key, Object value) {
        customData.put(key, value);
    }
}
