package com.imhuso.wholesale.core.service.impl;

import com.imhuso.wholesale.core.domain.WhsStockNotify;
import com.imhuso.wholesale.core.event.WhsStockNotifyEvent.StockNotifyEventType;
import com.imhuso.wholesale.core.service.IStockNotificationService;
import com.imhuso.wholesale.core.strategy.stockNotification.IStockNotificationStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * 库存通知服务实现类
 * <p>
 * 基于策略模式实现，支持多种通知方式
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StockNotificationServiceImpl implements IStockNotificationService {

    /**
     * 注入所有通知策略实现
     * Spring会自动查找所有实现了IStockNotificationStrategy接口的Bean并注入
     */
    private final List<IStockNotificationStrategy> notificationStrategies;

    // 使用线程池异步发送通知，避免相互阻塞
    private final Executor notificationExecutor = Executors.newFixedThreadPool(5);

    @Override
    public void processNotification(WhsStockNotify stockNotify, StockNotifyEventType eventType) {
        if (stockNotify == null || eventType == null) {
            log.warn("通知参数无效: stockNotify={}, eventType={}", stockNotify, eventType);
            return;
        }

        log.info("处理库存通知: notifyId={}, eventType={}", stockNotify.getId(), eventType);

        // 并行发送所有策略的通知，避免相互影响
        for (IStockNotificationStrategy strategy : notificationStrategies) {
            final String strategyName = strategy.getName();

            // 异步发送管理员通知
            if (strategy.isAdminNotificationEnabled(eventType)) {
                CompletableFuture.runAsync(() -> {
                    try {
                        boolean result = strategy.sendAdminNotification(stockNotify, eventType);
                        log.debug("发送{}管理员通知{}: notifyId={}, eventType={}",
                                strategyName, result ? "成功" : "失败", stockNotify.getId(), eventType);
                    } catch (Exception e) {
                        log.error("发送{}管理员通知异常: notifyId={}, eventType={}, error={}",
                                strategyName, stockNotify.getId(), eventType, e.getMessage(), e);
                    }
                }, notificationExecutor);
            }

            // 异步发送客户通知
            if (strategy.isCustomerNotificationEnabled(eventType)) {
                CompletableFuture.runAsync(() -> {
                    try {
                        boolean result = strategy.sendCustomerNotification(stockNotify, eventType);
                        log.debug("发送{}客户通知{}: notifyId={}, eventType={}",
                                strategyName, result ? "成功" : "失败", stockNotify.getId(), eventType);
                    } catch (Exception e) {
                        log.error("发送{}客户通知异常: notifyId={}, eventType={}, error={}",
                                strategyName, stockNotify.getId(), eventType, e.getMessage(), e);
                    }
                }, notificationExecutor);
            }
        }
    }
}
