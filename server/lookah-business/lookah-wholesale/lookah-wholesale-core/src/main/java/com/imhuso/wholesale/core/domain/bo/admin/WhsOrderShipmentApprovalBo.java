package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.wholesale.core.domain.WhsOrderShipmentApproval;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 订单发货审批业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WhsOrderShipmentApproval.class)
public class WhsOrderShipmentApprovalBo extends BaseEntity {

    /**
     * 审核ID
     */
    @NotNull(message = "审核ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 申请人ID
     */
    private Long applicantId;

    /**
     * 申请人姓名
     */
    private String applicantName;

    /**
     * 申请理由
     */
    @NotBlank(message = "申请理由不能为空", groups = {AddGroup.class})
    private String applicationReason;

    /**
     * 审核状态
     */
    private Integer approvalStatus;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 操作序号，同一订单的操作按时间顺序递增
     */
    private Integer operationSequence;

    /**
     * 记录状态
     */
    private String recordStatus;

    /**
     * 审核人ID
     */
    private Long approverId;

    /**
     * 审核人姓名
     */
    private String approverName;

    /**
     * 审核时间
     */
    private Date approvalTime;

    /**
     * 审核意见
     */
    private String approvalComment;

    /**
     * 备注
     */
    private String remark;
}
