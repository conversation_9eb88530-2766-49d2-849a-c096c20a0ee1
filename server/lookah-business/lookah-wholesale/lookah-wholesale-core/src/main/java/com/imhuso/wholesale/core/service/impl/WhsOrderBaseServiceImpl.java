package com.imhuso.wholesale.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.wholesale.core.domain.WhsOrder;
import com.imhuso.wholesale.core.mapper.WhsOrderMapper;
import com.imhuso.wholesale.core.service.IWhsOrderBaseService;
import com.imhuso.wholesale.core.satoken.WholesaleLoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 批发订单基础服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsOrderBaseServiceImpl implements IWhsOrderBaseService {

    private final WhsOrderMapper baseMapper;

    /**
     * 通过订单ID获取订单
     */
    @Override
    public WhsOrder getOrderById(Long orderId) {
        return baseMapper.selectById(orderId);
    }

    /**
     * 验证订单所有权，确保订单存在且属于当前登录会员
     */
    @Override
    public WhsOrder checkOrderOwnership(Long orderId) {
        Long memberId = WholesaleLoginHelper.getUserId();

        LambdaQueryWrapper<WhsOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(WhsOrder::getId, orderId);
        lqw.eq(WhsOrder::getMemberId, memberId);

        WhsOrder order = baseMapper.selectOne(lqw);

        if (order == null) {
            throw new ServiceException(MessageUtils.message("wholesale.order.not.exists"));
        }
        return order;
    }

    /**
     * 验证订单是否存在
     */
    @Override
    public WhsOrder checkOrderExists(Long orderId) {
        WhsOrder order = baseMapper.selectById(orderId);
        if (order == null) {
            throw new ServiceException(MessageUtils.message("wholesale.order.not.exists"));
        }
        return order;
    }
}
