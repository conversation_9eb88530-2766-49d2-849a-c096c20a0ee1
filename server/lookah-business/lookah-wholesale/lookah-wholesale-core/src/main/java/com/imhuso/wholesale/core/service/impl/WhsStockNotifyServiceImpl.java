package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.WhsStockNotify;
import com.imhuso.wholesale.core.domain.bo.front.StockNotifyBo;
import com.imhuso.wholesale.core.domain.bo.front.StockNotifyCreateBo;
import com.imhuso.wholesale.core.enums.NotifyStatus;
import com.imhuso.wholesale.core.event.WhsStockNotifyEvent;
import com.imhuso.wholesale.core.event.WhsStockNotifyEvent.StockNotifyEventType;
import com.imhuso.wholesale.core.mapper.WhsProductVariantMapper;
import com.imhuso.wholesale.core.mapper.WhsStockNotifyMapper;
import com.imhuso.wholesale.core.service.IWhsStockNotifyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;

/**
 * 批发到货通知Service实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsStockNotifyServiceImpl implements IWhsStockNotifyService {

    private final WhsStockNotifyMapper baseMapper;
    private final WhsProductVariantMapper variantMapper;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 创建到货通知（前端用户创建）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createNotify(StockNotifyCreateBo bo) {
        // 校验变体是否存在
        checkVariant(bo.getVariantId());

        // 检查是否可以订阅
        if (!canSubscribe(bo.getVariantId(), bo.getEmail())) {
            log.debug("用户已订阅变体到货通知 variantId: {}, email: {}", bo.getVariantId(), bo.getEmail());
            throw new ServiceException(MessageUtils.message("wholesale.notify.already.subscribed"));
        }

        // 创建通知记录（状态默认为待通知）
        WhsStockNotify notify = MapstructUtils.convert(bo, WhsStockNotify.class);
        notify.setStatus(NotifyStatus.PENDING.getValue());
        baseMapper.insert(notify);

        // 发布通知注册事件
        publishEvent(notify, StockNotifyEventType.NOTIFY_REGISTERED);
    }

    /**
     * 更新通知状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateNotifyStatus(Long notifyId, Integer status) {
        WhsStockNotify notify = baseMapper.selectById(notifyId);
        if (ObjectUtil.isNull(notify)) {
            throw new ServiceException(MessageUtils.message("wholesale.notify.not.exists"));
        }

        Integer oldStatus = notify.getStatus();

        // 如果状态没有变化，则无需更新
        if (Objects.equals(status, oldStatus)) {
            return;
        }

        notify.setStatus(status);

        // 如果状态变更为已通知，设置通知时间
        if (Objects.equals(status, NotifyStatus.NOTIFIED.getValue())) {
            notify.setNotifyTime(new Date());
        }

        // updateBy 会自动填充为管理员ID
        baseMapper.updateById(notify);

        // 如果状态变更为已通知，发布已通知事件
        if (Objects.equals(status, NotifyStatus.NOTIFIED.getValue())) {
            publishEvent(notify, StockNotifyEventType.NOTIFY_SENT);
        }
    }

    /**
     * 发布库存通知事件
     */
    private void publishEvent(WhsStockNotify notify, StockNotifyEventType eventType) {
        eventPublisher.publishEvent(new WhsStockNotifyEvent(this, eventType, notify));
    }

    /**
     * 校验变体是否存在
     */
    private void checkVariant(Long variantId) {
        WhsProductVariant variant = variantMapper.selectById(variantId);
        if (ObjectUtil.isNull(variant)) {
            throw new ServiceException(MessageUtils.message("wholesale.product.variant.not.exists"));
        }
    }

    /**
     * 校验是否已经订阅
     *
     * @return true 如果可以继续订阅，false 如果已经订阅
     */
    private boolean canSubscribe(Long variantId, String email) {
        StockNotifyBo bo = new StockNotifyBo();
        bo.setVariantId(variantId);
        bo.setEmail(email);
        bo.setStatus(NotifyStatus.PENDING.getValue());
        return !baseMapper.exists(buildQueryWrapper(bo));
    }

    /**
     * 构建查询条件
     *
     * @param bo 查询参数
     * @return 查询条件
     */
    private LambdaQueryWrapper<WhsStockNotify> buildQueryWrapper(StockNotifyBo bo) {
        LambdaQueryWrapper<WhsStockNotify> lqw = new LambdaQueryWrapper<>();

        if (bo == null) {
            return lqw;
        }

        // 按ID查询
        if (bo.getId() != null) {
            lqw.eq(WhsStockNotify::getId, bo.getId());
        }

        // 按变体ID查询
        if (bo.getVariantId() != null) {
            lqw.eq(WhsStockNotify::getVariantId, bo.getVariantId());
        }

        // 按邮箱查询
        if (StringUtils.isNotBlank(bo.getEmail())) {
            lqw.eq(WhsStockNotify::getEmail, bo.getEmail());
        }

        // 按状态查询
        if (bo.getStatus() != null) {
            lqw.eq(WhsStockNotify::getStatus, bo.getStatus());
        }

        return lqw;
    }
}
