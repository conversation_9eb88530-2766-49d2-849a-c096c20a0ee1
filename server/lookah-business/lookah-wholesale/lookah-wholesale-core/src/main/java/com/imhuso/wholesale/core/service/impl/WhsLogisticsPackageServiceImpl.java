package com.imhuso.wholesale.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.domain.WhsLogisticsPackage;
import com.imhuso.wholesale.core.domain.WhsOrder;
import com.imhuso.wholesale.core.domain.WhsOrderShipment;
import com.imhuso.wholesale.core.domain.vo.admin.WhsLogisticsPackageVo;
import com.imhuso.wholesale.core.enums.PackageStatus;
import com.imhuso.wholesale.core.event.WhsOrderEvent;
import com.imhuso.wholesale.core.event.WhsOrderEvent.WhsOrderEventType;
import com.imhuso.wholesale.core.mapper.WhsLogisticsPackageMapper;
import com.imhuso.wholesale.core.mapper.WhsOrderMapper;
import com.imhuso.wholesale.core.mapper.WhsOrderShipmentMapper;
import com.imhuso.wholesale.core.satoken.WholesaleLoginHelper;
import com.imhuso.wholesale.core.service.IWhsLogisticsPackageService;
import com.imhuso.wholesale.core.service.IWhsOrderStatusLogService;
import com.imhuso.wholesale.core.utils.WhsEnumTranslationUtils;
import com.imhuso.wholesale.core.utils.carrier.TrackingNumberUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 物流包裹 服务层实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsLogisticsPackageServiceImpl extends ServiceImpl<WhsLogisticsPackageMapper, WhsLogisticsPackage> implements IWhsLogisticsPackageService {

    private final WhsOrderMapper orderMapper;
    private final IWhsOrderStatusLogService orderStatusLogService;
    private final WhsOrderShipmentMapper shipmentMapper;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 根据发货单ID查询物流包裹信息
     *
     * @param shipmentId 发货单ID
     * @return 物流包裹信息列表
     */
    @Override
    public List<WhsLogisticsPackageVo> getLogisticsPackagesByShipmentId(Long shipmentId) {
        LambdaQueryWrapper<WhsLogisticsPackage> queryWrapper = Wrappers.lambdaQuery(WhsLogisticsPackage.class).eq(shipmentId != null, WhsLogisticsPackage::getShipmentId, shipmentId);

        List<WhsLogisticsPackage> list = this.list(queryWrapper);
        return MapstructUtils.convert(list, WhsLogisticsPackageVo.class);
    }

    /**
     * 更新包裹状态
     *
     * @param packageId 包裹ID
     * @param status    包裹状态
     * @param remark    备注
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePackageStatus(Long packageId, Integer status, String remark) {
        // 查询包裹信息
        WhsLogisticsPackage pkg = this.getById(packageId);
        if (pkg == null) {
            throw new ServiceException("包裹不存在");
        }

        // 如果状态相同，则直接返回成功
        if (pkg.getPackageStatus().equals(status)) {
            return true;
        }

        // 保存旧状态，用于记录日志
        Integer oldStatus = pkg.getPackageStatus();

        // 更新包裹状态
        WhsLogisticsPackage updatePackage = new WhsLogisticsPackage();
        updatePackage.setId(packageId);
        updatePackage.setPackageStatus(status);

        // 更新状态
        boolean success = this.updateById(updatePackage);
        if (!success) {
            throw new ServiceException("更新包裹状态失败");
        }

        // 如果是送达状态，则更新实际送达时间
        if (PackageStatus.DELIVERED.getValue().equals(status)) {
            WhsLogisticsPackage deliveredPackage = new WhsLogisticsPackage();
            deliveredPackage.setId(packageId);
            deliveredPackage.setActualDelivery(new Date());
            this.updateById(deliveredPackage);

            // 检查订单是否所有包裹都已送达，如果是则可以更新订单状态为已送达
            checkAndUpdateOrderDeliveryStatus(pkg.getOrderId());
        }

        // 获取状态文本描述
        String oldStatusText = WhsEnumTranslationUtils.translate(PackageStatus.class, oldStatus);
        String newStatusText = WhsEnumTranslationUtils.translate(PackageStatus.class, status);

        // 获取批次号
        Integer batchNumber = getShipmentBatchNumber(pkg.getShipmentId());
        String batchInfo = batchNumber != null ? String.format("批次 %d ", batchNumber) : "";

        // 记录状态变更日志
        log.info("包裹 {} {}状态由 [{}] 变更为 [{}], 备注: {}",
            packageId,
            batchInfo,
            oldStatusText,
            newStatusText,
            StringUtils.isNotBlank(remark) ? remark : "无");

        // 记录订单状态日志
        WhsOrder order = getOrderById(pkg.getOrderId());
        if (order != null) {
            String logRemark = String.format("包裹%s状态更新: [%s] → [%s]",
                batchInfo,
                oldStatusText,
                newStatusText);

            if (StringUtils.isNotBlank(remark)) {
                logRemark += ", 备注: " + remark;
            }

            orderStatusLogService.createStatusLog(
                order,
                order.getOrderStatus(),
                null,
                logRemark,
                null,
                WhsOrderEventType.ORDER_STATUS_CHANGED
            );

            // 判断是否是状态从小到大的变化（排除异常状态）
            boolean isStatusIncreasing = isStatusIncreasing(oldStatus, status);

            // 如果是状态从小到大的变化，则发布包裹状态变更事件
            if (isStatusIncreasing) {
                log.info("包裹状态从小到大变化，发布包裹状态变更通知事件: 包裹ID={}, 订单ID={}, 状态: {} -> {}",
                    packageId, order.getId(), oldStatusText, newStatusText);

                // 获取当前用户ID
                Long userId = WholesaleLoginHelper.getUserId();

                // 发布包裹状态变更事件
                eventPublisher.publishEvent(new WhsOrderEvent(
                    this,
                    WhsOrderEventType.PACKAGE_STATUS_CHANGED,
                    order.getId(),
                    logRemark,
                    userId
                ));
            }
        }

        return true;
    }

    /**
     * 更新包裹跟踪信息
     *
     * @param packageId      包裹ID
     * @param trackingNumber 跟踪号
     * @param carrier        承运商
     * @param remark         备注
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePackageTracking(Long packageId, String trackingNumber, String carrier, String remark) {
        // 查询包裹信息
        WhsLogisticsPackage pkg = this.getById(packageId);
        if (pkg == null) {
            throw new ServiceException("包裹不存在");
        }

        // 保存旧的跟踪信息以便日志记录
        String oldTrackingNumber = pkg.getTrackingNumber();
        String oldCarrier = pkg.getCarrier();

        // 如果跟踪号相同且承运商相同，则直接返回成功
        if (trackingNumber.equals(oldTrackingNumber) &&
            (carrier == null && oldCarrier == null ||
                carrier != null && carrier.equals(oldCarrier))) {
            return true;
        }

        // 更新包裹跟踪信息
        WhsLogisticsPackage updatePackage = new WhsLogisticsPackage();
        updatePackage.setId(packageId);
        updatePackage.setTrackingNumber(trackingNumber);

        // 如果未提供承运商，则尝试自动识别
        if (StringUtils.isBlank(carrier)) {
            carrier = TrackingNumberUtils.detectCarrier(trackingNumber);
        }
        // 将carrier统一转为小写
        updatePackage.setCarrier(carrier.toLowerCase());

        // 更新跟踪信息
        boolean success = this.updateById(updatePackage);
        if (!success) {
            throw new ServiceException("更新包裹跟踪信息失败");
        }

        // 获取批次号
        Integer batchNumber = getShipmentBatchNumber(pkg.getShipmentId());
        String batchInfo = batchNumber != null ? String.format("批次 %d ", batchNumber) : "";

        // 记录日志
        log.info("包裹 {} {}跟踪信息更新: 跟踪号 [{}] → [{}], 承运商 [{}] → [{}], 备注: {}",
            packageId,
            batchInfo,
            oldTrackingNumber != null ? oldTrackingNumber : "无",
            trackingNumber,
            oldCarrier != null ? oldCarrier : "无",
            carrier,
            StringUtils.isNotBlank(remark) ? remark : "无");

        // 记录订单状态日志
        WhsOrder order = getOrderById(pkg.getOrderId());
        if (order != null) {
            String logRemark = String.format("包裹%s跟踪信息更新: [%s] → [%s]",
                batchInfo,
                oldTrackingNumber != null ? oldTrackingNumber : "无",
                trackingNumber);

            if (StringUtils.isNotBlank(carrier)) {
                logRemark += String.format(", 承运商: [%s]", carrier);
            }

            if (StringUtils.isNotBlank(remark)) {
                logRemark += ", 备注: " + remark;
            }

            orderStatusLogService.createStatusLog(
                order,
                order.getOrderStatus(),
                null,
                logRemark,
                null,
                WhsOrderEventType.ORDER_STATUS_CHANGED
            );
        }

        return true;
    }

    /**
     * 检查并更新订单发货状态
     *
     * @param orderId 订单ID
     */
    private void checkAndUpdateOrderDeliveryStatus(Long orderId) {
        // 查询订单所有包裹
        List<WhsLogisticsPackage> packages = this.lambdaQuery().eq(WhsLogisticsPackage::getOrderId, orderId).list();

        // 检查是否所有包裹都已送达
        boolean allDelivered = packages.stream().allMatch(pkg -> PackageStatus.DELIVERED.getValue().equals(pkg.getPackageStatus()));

        // 如果所有包裹都已送达，则更新订单状态
        if (allDelivered && !packages.isEmpty()) {
            log.info("订单 {} 所有包裹已送达，可更新订单状态", orderId);
            // 如需更新订单状态，可通过依赖注入订单服务实现
        }
    }

    /**
     * 获取包裹关联的订单
     *
     * @param orderId 订单ID
     * @return 订单实体，如果未找到返回null
     */
    private WhsOrder getOrderById(Long orderId) {
        if (orderId == null) {
            return null;
        }
        return orderMapper.selectById(orderId);
    }

    /**
     * 获取包裹关联的发货批次号
     *
     * @param shipmentId 发货单ID
     * @return 批次号，如果未找到返回null
     */
    private Integer getShipmentBatchNumber(Long shipmentId) {
        if (shipmentId == null) {
            return null;
        }
        WhsOrderShipment shipment = shipmentMapper.selectById(shipmentId);
        return shipment != null ? shipment.getBatchNumber() : null;
    }

    /**
     * 判断包裹状态是否从小到大变化（排除异常状态）
     *
     * @param oldStatus 旧状态
     * @param newStatus 新状态
     * @return 是否是从小到大的变化
     */
    private boolean isStatusIncreasing(Integer oldStatus, Integer newStatus) {
        // 如果新状态是异常状态，则不视为从小到大的变化
        if (PackageStatus.EXCEPTION.getValue().equals(newStatus)) {
            return false;
        }

        // 如果旧状态是异常状态，新状态不是异常状态，则视为从小到大的变化
        if (PackageStatus.EXCEPTION.getValue().equals(oldStatus) && !PackageStatus.EXCEPTION.getValue().equals(newStatus)) {
            return true;
        }

        // 正常情况下，判断状态值是否增加
        return newStatus > oldStatus;
    }
}
