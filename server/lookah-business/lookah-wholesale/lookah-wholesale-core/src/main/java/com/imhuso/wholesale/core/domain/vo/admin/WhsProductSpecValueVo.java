package com.imhuso.wholesale.core.domain.vo.admin;

import com.imhuso.wholesale.core.domain.WhsProductAttributeValue;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 产品规格值视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AutoMapper(target = WhsProductAttributeValue.class)
public class WhsProductSpecValueVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 值标识
     */
    private String code;

    /**
     * 值名称
     */
    private String name;
}
