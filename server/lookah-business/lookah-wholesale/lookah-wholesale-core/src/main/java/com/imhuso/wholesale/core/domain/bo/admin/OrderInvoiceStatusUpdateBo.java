package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 订单发票状态更新业务对象
 *
 * <AUTHOR>
 */
@Data
public class OrderInvoiceStatusUpdateBo {

    /**
     * 发票状态
     */
    @NotNull(message = "发票状态不能为空", groups = {EditGroup.class})
    private Integer invoiceStatus;

    /**
     * 备注
     */
    private String remark;
}
