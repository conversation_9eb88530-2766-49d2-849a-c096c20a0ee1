package com.imhuso.wholesale.core.domain.bo.admin;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

/**
 * 物流包裹业务对象
 *
 * <AUTHOR>
 */
@Data
public class WhsLogisticsPackageBo {

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    /**
     * 关联的发货单ID
     */
    @NotNull(message = "发货单ID不能为空")
    private Long shipmentId;

    /**
     * 跟踪号
     */
    @NotBlank(message = "跟踪号不能为空")
    private String trackingNumber;

    /**
     * 承运商(如：FedEx、DHL、UPS)
     */
    @NotBlank(message = "承运商不能为空")
    private String carrier;

    /**
     * 物流方式ID
     */
    @NotNull(message = "物流方式不能为空")
    private Long logisticsMethodId;

    /**
     * 包裹状态(0-待处理 1-已发货 2-运输中 3-已送达 4-异常)
     */
    private Integer packageStatus;

    /**
     * 发货仓库ID
     */
    @NotNull(message = "发货仓库不能为空")
    private Long warehouseId;

    /**
     * 发货日期
     */
    private Date shippedDate;

    /**
     * 海外仓订单号
     */
    private String externalOrderNo;
}
