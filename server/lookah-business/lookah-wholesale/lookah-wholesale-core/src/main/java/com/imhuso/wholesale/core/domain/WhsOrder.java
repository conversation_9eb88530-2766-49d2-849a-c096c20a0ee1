package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * 批发订单对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("whs_order")
public class WhsOrder extends BaseEntity {

    /**
     * 订单ID
     */
    @TableId
    private Long id;

    /**
     * 发票编号
     */
    private Long invoiceId;

    /**
     * 内部订单号
     */
    private String internalOrderNo;

    /**
     * 客户订单号
     */
    private String customerOrderNo;

    /**
     * 参考号
     */
    private String referenceNo;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 销售代表ID，关联sys_user表
     */
    private Long salespersonId;

    /**
     * 是否为手动下单
     */
    private Boolean isManualOrder;

    /**
     * 收货人姓名
     */
    private String shippingName;

    /**
     * 收货人电话
     */
    private String shippingPhone;

    /**
     * 收货人邮箱
     */
    private String shippingEmail;

    /**
     * 收货人国家
     */
    private String shippingCountry;

    /**
     * 收货人州/省
     */
    private String shippingState;

    /**
     * 收货人城市
     */
    private String shippingCity;

    /**
     * 收货人详细地址
     */
    private String shippingAddress;

    /**
     * 收货人详细地址2
     */
    private String shippingAddress2;

    /**
     * 收货人邮编
     */
    private String shippingZip;

    /**
     * 收货公司名称
     */
    private String shippingCompanyName;

    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmount;

    /**
     * 订单项总数量
     */
    private Integer totalItems;

    /**
     * 商品总PCS数量
     */
    private Integer totalPcs;

    /**
     * 支付状态
     *
     * @see com.imhuso.wholesale.core.enums.PaymentStatus
     */
    private Integer paymentStatus;

    /**
     * 订单状态
     *
     * @see com.imhuso.wholesale.core.enums.OrderStatus
     */
    private Integer orderStatus;

    /**
     * 是否为补货订单
     * 0-否，1-是
     */
    private Boolean isReplenishment;

    /**
     * 发货状态
     *
     * @see com.imhuso.wholesale.core.enums.ShipmentStatus
     */
    private Integer shipmentStatus;

    /**
     * 发票状态
     *
     * @see com.imhuso.wholesale.core.enums.InvoiceStatus
     */
    private Integer invoiceStatus;

    /**
     * 发货审批状态
     * 0-无需审批, 1-待审批, 2-审批通过, 3-审批拒绝, 4-已撤销
     *
     * @see com.imhuso.wholesale.core.enums.ShipmentApprovalStatus
     */
    private Integer shipmentApprovalStatus;

    /**
     * 备注
     */
    private String remark;
}
