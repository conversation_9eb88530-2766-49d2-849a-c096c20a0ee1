package com.imhuso.wholesale.core.mapper;

import com.imhuso.common.mybatis.annotation.DataColumn;
import com.imhuso.common.mybatis.annotation.DataPermission;
import com.imhuso.common.mybatis.core.mapper.BaseMapperPlus;
import com.imhuso.wholesale.core.domain.WhsOrder;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 后台订单Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
@DataPermission(value = {
    @DataColumn(key = "deptName", value = "create_dept"),
    @DataColumn(key = "userName", value = "create_by"),
    @DataColumn(key = "salespersonName", value = "salesperson_id")
}, joinStr = "OR")
public interface WhsOrderMapper extends BaseMapperPlus<WhsOrder, WhsOrderVo> {

    /**
     * 获取订单表中最大的发票号
     *
     * @return 最大发票号，如果没有记录则返回null
     */
    default Long selectMaxInvoiceId() {
        LambdaQueryWrapper<WhsOrder> queryWrapper = new LambdaQueryWrapper<WhsOrder>()
            .select(WhsOrder::getInvoiceId)
            .orderByDesc(WhsOrder::getInvoiceId)
            .last("LIMIT 1");
        WhsOrder order = this.selectOne(queryWrapper);
        return order != null ? order.getInvoiceId() : null;
    }
}
