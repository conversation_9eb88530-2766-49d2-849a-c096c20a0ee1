package com.imhuso.wholesale.core.service.impl;

import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.domain.vo.admin.WhsWarehouseLogisticsMethodVo;
import com.imhuso.wholesale.core.mapper.WhsWarehouseLogisticsMethodMapper;
import com.imhuso.wholesale.core.service.ILogisticsMethodInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 物流方法信息查询服务实现
 * 直接使用Mapper访问数据库，避免循环依赖
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LogisticsMethodInfoServiceImpl implements ILogisticsMethodInfoService {

    private final WhsWarehouseLogisticsMethodMapper methodMapper;

    /**
     * 根据ID获取物流方法信息
     *
     * @param id 物流方法ID
     * @return 物流方法信息
     */
    @Override
    public WhsWarehouseLogisticsMethodVo getById(Long id) {
        if (id == null) {
            return null;
        }

        try {
            return methodMapper.selectVoById(id);
        } catch (Exception e) {
            log.error("获取物流方法信息失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取物流方法的渠道ID
     *
     * @param id 物流方法ID
     * @return 渠道ID，如果不存在则返回null
     */
    @Override
    public String getChannelId(Long id) {
        WhsWarehouseLogisticsMethodVo method = getById(id);
        if (method == null) {
            return null;
        }

        String channelId = method.getChannelId();
        return StringUtils.isNotBlank(channelId) ? channelId : null;
    }
}
