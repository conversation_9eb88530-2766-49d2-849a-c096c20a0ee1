package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.WhsProduct;
import com.imhuso.wholesale.core.domain.WhsProductVariant;

import java.util.List;

/**
 * 商品特征服务接口
 *
 * <AUTHOR>
 */
public interface IWhsProductFeatureService {

    /**
     * 判断两个产品是否有相同的规格属性（除包装类型外）
     *
     * @param p1 产品1
     * @param p2 产品2
     * @return 是否有相同规格
     */
    boolean hasSameSkuSpecs(WhsProduct p1, WhsProduct p2);

    /**
     * 比较特定规格属性
     *
     * @param p1 产品1
     * @param p2 产品2
     * @param specKey 规格键名
     * @return 是否相同
     */
    boolean hasSameSkuSpec(WhsProduct p1, WhsProduct p2, String specKey);

    /**
     * 判断是否存在指定包装类型且规格相同的产品
     *
     * @param variants 变体列表
     * @param packagingType 包装类型
     * @param targetProduct 目标产品
     * @return 是否不存在
     */
    boolean hasNoPackagingTypeWithSameSpecs(List<WhsProduct> variants, Integer packagingType, WhsProduct targetProduct);

    /**
     * 生成产品特征键
     *
     * @param variant 产品变体
     * @return 特征键
     */
    String generateFeatureKey(WhsProductVariant variant);

    /**
     * 判断是否应在变体中显示
     *
     * @param product 产品
     * @param variants 同类产品列表
     * @return 是否应显示
     */
    boolean shouldShowInVariants(WhsProduct product, List<WhsProduct> variants);

    /**
     * 判断是否是最大包装规格
     *
     * @param product 产品
     * @param sameFeatureProducts 同特征产品列表
     * @return 是否是最大包装
     */
    boolean isLargestPackaging(WhsProduct product, List<WhsProduct> sameFeatureProducts);

    /**
     * 根据包装类型计算数量
     *
     * @param parentType 父包装类型
     * @param childType 子包装类型
     * @return 计算结果
     */
    int calculateQuantityByPackagingType(int parentType, int childType);

    /**
     * 计算产品件数
     *
     * @param variant 产品变体
     * @return 件数
     */
    Integer calculatePcs(WhsProductVariant variant);
}
