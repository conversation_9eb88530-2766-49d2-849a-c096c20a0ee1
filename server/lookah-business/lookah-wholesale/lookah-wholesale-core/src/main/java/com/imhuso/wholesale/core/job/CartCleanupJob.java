package com.imhuso.wholesale.core.job;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import com.aizuda.snailjob.common.log.SnailJobLog;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.wholesale.core.domain.WhsCart;
import com.imhuso.wholesale.core.mapper.WhsCartMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 购物车清理任务
 * 每天凌晨3点执行一次，清理过期的购物车数据
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@SuppressWarnings("unused") // SnailJob框架调用，IDE检测不到使用
public class CartCleanupJob {

    private final WhsCartMapper cartMapper;

    // 清理过期购物车数据
    @JobExecutor(name = "cleanCartOverdue")
    public ExecuteResult jobExecute(JobArgs jobArgs) {
        SnailJobLog.LOCAL.info("开始执行购物车清理任务");
        try {
            // 删除过期的购物车数据
            int count = cartMapper.delete(new LambdaQueryWrapper<WhsCart>()
                    .le(WhsCart::getExpireTime, new Date()));

            String message = String.format("购物车清理任务完成，共清理 %d 条过期数据", count);
            SnailJobLog.LOCAL.info(message);
            return ExecuteResult.success(message);
        } catch (Exception e) {
            String errorMessage = "购物车清理任务执行失败: " + e.getMessage();
            SnailJobLog.LOCAL.error(errorMessage, e);
            return ExecuteResult.failure(errorMessage);
        }
    }
}
