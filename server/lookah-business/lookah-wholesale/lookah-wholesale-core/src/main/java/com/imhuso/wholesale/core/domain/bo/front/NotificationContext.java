package com.imhuso.wholesale.core.domain.bo.front;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通知上下文对象，封装通知发送所需的所有参数
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationContext {

    /**
     * 通知记录ID
     */
    private Long notifyId;

    /**
     * 客户邮箱
     */
    private String customerEmail;

    /**
     * 管理员邮箱
     */
    private String adminEmail;

    /**
     * 客户通知模板名称
     */
    private String customerTemplate;

    /**
     * 管理员通知模板名称
     */
    private String adminTemplate;

    /**
     * 客户通知标题（英文）
     */
    private String customerTitle;

    /**
     * 管理员通知标题（中文）
     */
    private String adminTitle;
}
