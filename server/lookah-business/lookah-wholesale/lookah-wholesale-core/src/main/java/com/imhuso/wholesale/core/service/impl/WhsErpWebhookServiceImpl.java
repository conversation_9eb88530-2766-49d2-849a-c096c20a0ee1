package com.imhuso.wholesale.core.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.imhuso.wholesale.core.config.ErpConfig;
import com.imhuso.wholesale.core.domain.bo.admin.ErpStockSyncBo;
import com.imhuso.wholesale.core.service.IWhsErpStockSyncService;
import com.imhuso.wholesale.core.service.IWhsErpWebhookService;
import com.imhuso.wholesale.core.utils.erp.ErpSignatureUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * ERP Webhook事件处理服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsErpWebhookServiceImpl implements IWhsErpWebhookService {

    private final ErpConfig erpConfig;
    private final IWhsErpStockSyncService erpStockSyncService;
    private final ObjectMapper objectMapper;

    @Override
    public boolean handleWebhookEvent(String eventType, String payload) {
        try {
            log.info("开始处理ERP Webhook事件: {}", eventType);

            switch (eventType) {
                case "stock-changed":
                    return handleStockChangedEvent(payload);
                case "production-stock-changed":
                    return handleProductionStockChangedEvent(payload);
                case "wait-inbound-changed":
                    return handleWaitInboundStockChangedEvent(payload);
                case "low-inventory":
                    return handleLowInventoryEvent(payload);
                case "out-of-stock":
                    return handleOutOfStockEvent(payload);
                default:
                    log.warn("不支持的ERP事件类型: {}", eventType);
                    return false;
            }

        } catch (Exception e) {
            log.error("处理ERP Webhook事件失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean verifyWebhookSignature(String payload, String signature) {
        if (!erpConfig.getWebhook().isSignatureVerification()) {
            log.debug("Webhook签名验证已禁用");
            return true;
        }

        if (signature == null || signature.isEmpty()) {
            log.warn("ERP Webhook签名为空");
            return false;
        }

        return ErpSignatureUtils.verifyWebhookSignature(payload, signature, erpConfig.getAppSecret());
    }

    /**
     * 处理库存变化事件
     */
    private boolean handleStockChangedEvent(String payload) {
        try {
            log.info("处理库存变化事件");

            // 解析Webhook数据
            Map<String, Object> webhookData = objectMapper.readValue(payload, Map.class);

            // 提取变化的SKU列表
            @SuppressWarnings("unchecked")
            List<String> changedSkus = (List<String>) webhookData.get("changed_skus");

            if (changedSkus != null && !changedSkus.isEmpty()) {
                log.info("检测到{}个SKU的库存发生变化，开始同步", changedSkus.size());

                // 获取最新的库存数据
                List<ErpStockSyncBo> stockData = erpStockSyncService.fetchErpStockData(changedSkus);

                if (!stockData.isEmpty()) {
                    // 更新本地库存
                    int updatedCount = erpStockSyncService.updateVariantErpStock(stockData);
                    log.info("Webhook触发的库存同步完成，更新了{}个产品变体", updatedCount);
                    return updatedCount > 0;
                }
            }

            return true;

        } catch (Exception e) {
            log.error("处理库存变化事件失败", e);
            return false;
        }
    }

    /**
     * 处理生产库存变化事件
     */
    private boolean handleProductionStockChangedEvent(String payload) {
        log.info("处理生产库存变化事件");
        // 可以根据需要实现特定的生产库存处理逻辑
        return handleStockChangedEvent(payload);
    }

    /**
     * 处理待入库库存变化事件
     */
    private boolean handleWaitInboundStockChangedEvent(String payload) {
        log.info("处理待入库库存变化事件");
        // 可以根据需要实现特定的待入库库存处理逻辑
        return handleStockChangedEvent(payload);
    }

    /**
     * 处理低库存预警事件
     */
    private boolean handleLowInventoryEvent(String payload) {
        try {
            log.warn("收到低库存预警事件");

            Map<String, Object> webhookData = objectMapper.readValue(payload, Map.class);

            // 可以在这里实现低库存预警的处理逻辑
            // 例如：发送通知、创建采购订单等
            log.info("低库存预警数据: {}", webhookData);

            return true;

        } catch (Exception e) {
            log.error("处理低库存预警事件失败", e);
            return false;
        }
    }

    /**
     * 处理缺货预警事件
     */
    private boolean handleOutOfStockEvent(String payload) {
        try {
            log.error("收到缺货预警事件");

            Map<String, Object> webhookData = objectMapper.readValue(payload, Map.class);

            // 可以在这里实现缺货预警的处理逻辑
            // 例如：发送紧急通知、暂停相关产品销售等
            log.error("缺货预警数据: {}", webhookData);

            return true;

        } catch (Exception e) {
            log.error("处理缺货预警事件失败", e);
            return false;
        }
    }
}
