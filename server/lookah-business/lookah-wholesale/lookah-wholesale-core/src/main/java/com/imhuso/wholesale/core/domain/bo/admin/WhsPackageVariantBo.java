package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.core.validate.enumd.EnumPattern;
import com.imhuso.wholesale.core.enums.PackagingType;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 创建包装类型变体的业务对象
 * 用于从单品变体创建对应的包装类型变体
 *
 * <AUTHOR>
 */
@Data
public class WhsPackageVariantBo {

    /**
     * 变体ID（更新时必填）
     */
    @NotNull(message = "变体ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 源变体ID(单品变体)
     */
    @NotNull(message = "源变体ID不能为空", groups = {AddGroup.class})
    private Long sourceVariantId;

    /**
     * SKU编码
     */
    @NotBlank(message = "SKU编码不能为空", groups = {AddGroup.class})
    @Size(max = 50, message = "SKU编码不能超过50个字符", groups = {AddGroup.class})
    private String skuCode;

    /**
     * UPC编码
     */
    @Size(max = 50, message = "UPC编码不能超过50个字符", groups = {AddGroup.class})
    private String upc;

    /**
     * 包装类型（1展示盒 2箱装）
     */
    @NotNull(message = "包装类型不能为空", groups = {AddGroup.class})
    @EnumPattern(type = PackagingType.class, fieldName = "value", message = "包装类型必须是有效的值(1:展示盒, 2:箱装)", groups = {
        AddGroup.class})
    private Integer packagingType;

    /**
     * 包含的单品数量
     */
    @NotNull(message = "包含的单品数量不能为空", groups = {AddGroup.class})
    @Positive(message = "包含的单品数量必须大于0", groups = {AddGroup.class})
    private Integer packageQuantity;

    /**
     * 批发价格(W-Sale)
     */
    @DecimalMin(value = "0.00", message = "批发价格不能小于0", groups = {AddGroup.class})
    @DecimalMax(value = "99999999.99", message = "批发价格不能超过99999999.99", groups = {AddGroup.class})
    @Digits(integer = 8, fraction = 2, message = "批发价格最多8位整数，2位小数", groups = {AddGroup.class})
    private BigDecimal wholesalePrice;

    /**
     * 建议零售价(MSRP)
     */
    @DecimalMin(value = "0.00", message = "建议零售价不能小于0", groups = {AddGroup.class})
    @DecimalMax(value = "99999999.99", message = "建议零售价不能超过99999999.99", groups = {AddGroup.class})
    @Digits(integer = 8, fraction = 2, message = "建议零售价最多8位整数，2位小数", groups = {AddGroup.class})
    private BigDecimal msrp;

    /**
     * 门店价格
     */
    @DecimalMin(value = "0.00", message = "门店价格不能小于0", groups = {AddGroup.class})
    @DecimalMax(value = "99999999.99", message = "门店价格不能超过99999999.99", groups = {AddGroup.class})
    @Digits(integer = 8, fraction = 2, message = "门店价格最多8位整数，2位小数", groups = {AddGroup.class})
    private BigDecimal storePrice;

    /**
     * 库存预警值（0表示使用仓库预警库存）
     */
    @Min(value = 0, message = "预警库存不能小于0", groups = {AddGroup.class, EditGroup.class})
    @Max(value = 999999, message = "预警库存不能超过999999", groups = {AddGroup.class, EditGroup.class})
    private Integer alertStock;

    /**
     * 状态（0停用 1正常）
     */
    private String status;
}
