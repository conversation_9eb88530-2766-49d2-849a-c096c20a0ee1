package com.imhuso.wholesale.core.service;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.WhsStock;
import com.imhuso.wholesale.core.domain.bo.admin.WhsStockBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsStockSummaryBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsStockSummaryVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsStockVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsVariantStockByWarehouseVo;

import java.util.List;
import java.util.Map;

/**
 * 库存管理服务接口
 *
 * <AUTHOR>
 */
public interface IWhsStockService {
    /**
     * 【后台】查询库存分页列表
     *
     * @param bo        查询参数
     * @param pageQuery 分页参数
     * @return 库存分页数据
     */
    TableDataInfo<WhsStockVo> queryPageList(WhsStockBo bo, PageQuery pageQuery);

    /**
     * 获取商品变体的可用库存数量
     * 如果传入单个ID，返回该ID对应的库存数量
     * 如果传入ID列表，返回ID与库存数量的映射关系
     *
     * @param variantIds 变体ID或变体ID列表
     * @return 如果参数是单个ID，返回库存数量；如果是列表，返回ID到库存数量的映射
     */
    Map<Long, Integer> getAvailableStocks(List<Long> variantIds);

    /**
     * 获取商品变体在各仓库的可用库存数量
     * 返回变体ID到仓库库存映射的嵌套Map
     *
     * @param variantIds 变体ID列表
     * @return Map<变体ID, Map<仓库ID, 可用库存数量>>
     */
    Map<Long, Map<Long, Integer>> getAvailableStocksByWarehouse(List<Long> variantIds);

    /**
     * 根据变体ID和仓库ID获取库存对象
     * 如果不存在返回null
     *
     * @param variantId 变体ID
     * @param warehouseId 仓库ID
     * @return 库存对象
     */
    WhsStock getStockByVariantAndWarehouse(Long variantId, Long warehouseId);

    /**
     * 【后台】查询库存汇总列表（不分页）
     *
     * @param bo 查询参数
     * @return 库存汇总列表
     */
    List<WhsStockSummaryVo> queryStockSummaryList(WhsStockSummaryBo bo);

    /**
     * 【后台】查询库存汇总列表带统计信息
     *
     * @param bo 查询参数
     * @return 库存汇总响应数据，包含列表和统计信息
     */
    WhsStockSummaryVo.StockSummaryResponse queryStockSummaryWithStatistics(WhsStockSummaryBo bo);

    /**
     * 获取变体在各仓库的库存分布
     * 包含可用库存和在途库存
     *
     * @param variantId 变体ID
     * @return 该变体在各仓库的库存分布列表
     */
    List<WhsVariantStockByWarehouseVo> getVariantStockByWarehouses(Long variantId);
}
