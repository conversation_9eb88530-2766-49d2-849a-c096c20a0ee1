package com.imhuso.wholesale.core.domain.vo.admin;

import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * 海外仓账号视图对象
 *
 * <AUTHOR>
 */
@Data
public class OverseasWarehouseAccountVo {

    /**
     * 账号ID
     */
    private Long id;

    /**
     * 账号名称
     */
    private String name;

    /**
     * 账号编码
     */
    private String code;

    /**
     * 提供商ID
     */
    private Long providerId;

    /**
     * 提供商名称
     */
    private String providerName;

    /**
     * 提供商类型
     */
    private String providerType;

    /**
     * 账号配置（已解密）
     */
    private Map<String, String> accountConfig;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 回调URL
     */
    private String callbackUrl;

    /**
     * 最后同步时间
     */
    private Date lastSyncTime;

    /**
     * 是否默认账号
     */
    private String isDefault;

    /**
     * 备注
     */
    private String remark;

    /**
     * 仓库数量
     */
    private Integer warehouseCount;
}
