package com.imhuso.wholesale.core.domain.vo.admin;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 包装方案视图对象
 * 包含订单项和可用包装选项信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WhsShipmentPlanGenerationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单项列表
     */
    private List<WhsOrderItemVo> orderItems;

    /**
     * 订单总PCS
     */
    private Integer totalPcs;
    /**
     * 可用包装选项列表，按产品分组
     * 包含单品、展示盒、整箱等可用的包装类型
     */
    private List<WhsPackagingOptionVo> packagingOptions;
}
