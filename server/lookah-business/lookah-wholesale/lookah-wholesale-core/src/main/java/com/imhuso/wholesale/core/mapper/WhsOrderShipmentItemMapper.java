package com.imhuso.wholesale.core.mapper;

import com.imhuso.common.mybatis.core.mapper.BaseMapperPlus;
import com.imhuso.wholesale.core.domain.WhsOrderShipmentItem;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentItemVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 批发订单发货项Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface WhsOrderShipmentItemMapper extends BaseMapperPlus<WhsOrderShipmentItem, WhsOrderShipmentItemVo> {

    /**
     * 根据发货ID查询发货项列表（包含产品名称、包装类型等扩展信息）
     *
     * @param shipmentId 发货ID
     * @return 发货项列表
     */
    List<WhsOrderShipmentItemVo> selectItemsByShipmentId(@Param("shipmentId") Long shipmentId);
}
