package com.imhuso.wholesale.core.listener;

import cn.dev33.satoken.SaManager;
import cn.dev33.satoken.config.SaTokenConfig;
import cn.dev33.satoken.listener.SaTokenListener;
import cn.dev33.satoken.stp.StpLogic;
import cn.dev33.satoken.stp.parameter.SaLoginParameter;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.imhuso.common.core.constant.CacheConstants;
import com.imhuso.common.core.constant.Constants;
import com.imhuso.common.core.domain.dto.UserOnlineDTO;
import com.imhuso.common.core.utils.DeviceTypeUtils;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.core.utils.ServletUtils;
import com.imhuso.common.core.utils.SpringUtils;
import com.imhuso.common.core.utils.ip.AddressUtils;
import com.imhuso.common.log.event.LoginInformEvent;
import com.imhuso.common.redis.utils.RedisUtils;
import com.imhuso.wholesale.core.constant.WholesaleConstants;
import com.imhuso.wholesale.core.satoken.WholesaleLoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Objects;

/**
 * 批发用户行为监听器
 * 监听批发用户的登录、登出等行为，统一处理在线用户信息
 * 与后台管理模块保持一致的处理风格
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
@Slf4j
public class WhsUserActionListener implements SaTokenListener {

    private final SaTokenConfig tokenConfig;

    /**
     * 每次登录时触发 (Sa-Token 1.44.0 新签名)
     */
    @Override
    public void doLogin(String loginType, Object loginId, String tokenValue, SaLoginParameter loginParameter) {
        // 只处理批发模块的登录
        if (!WholesaleConstants.SaToken.LOGIN_TYPE.equals(loginType)) {
            return;
        }

        UserAgent userAgent = UserAgentUtil.parse(Objects.requireNonNull(ServletUtils.getRequest()).getHeader("User-Agent"));
        String ip = ServletUtils.getClientIP();
        UserOnlineDTO dto = new UserOnlineDTO();
        dto.setIpaddr(ip);

        // 使用优化后的 AddressUtils 获取位置信息
        String location = "未知位置";
        try {
            location = AddressUtils.getRealAddressByIP(ip);
        } catch (Exception e) {
            log.warn("获取IP位置信息失败: ip={}, error={}", ip, e.getMessage());
        }
        dto.setLoginLocation(location);
        dto.setBrowser(userAgent.getBrowser().getName());
        dto.setOs(userAgent.getOs().getName());
        dto.setLoginTime(System.currentTimeMillis());
        dto.setTokenId(tokenValue);
        dto.setLoginType(loginType);

        // 从登录参数中获取用户信息
        String username = (String) loginParameter.getExtra(WholesaleLoginHelper.USER_NAME_KEY);
        dto.setUserName(username);
        dto.setClientId((String) loginParameter.getExtra(WholesaleLoginHelper.CLIENT_ID));

        // 根据User-Agent动态判断设备类型，而不是使用loginParameter的默认值
        String deviceType = DeviceTypeUtils.determineDeviceType(userAgent);
        dto.setDeviceType(deviceType);

        // 批发用户没有部门概念，设置为空
        dto.setDeptName("");

        // 将在线用户信息存入Redis
        if (tokenConfig.getTimeout() == -1) {
            RedisUtils.setCacheObject(CacheConstants.ONLINE_TOKEN_KEY + tokenValue, dto);
        } else {
            RedisUtils.setCacheObject(CacheConstants.ONLINE_TOKEN_KEY + tokenValue, dto, Duration.ofSeconds(tokenConfig.getTimeout()));
        }

        // 记录登录日志
        LoginInformEvent loginInformEvent = new LoginInformEvent();
        loginInformEvent.setUsername(username);
        loginInformEvent.setStatus(Constants.LOGIN_SUCCESS);
        loginInformEvent.setMessage(MessageUtils.message("user.login.success"));
        loginInformEvent.setRequest(ServletUtils.getRequest());
        SpringUtils.context().publishEvent(loginInformEvent);

        log.info("wholesale user login: userId={}, token={}", loginId, tokenValue);
    }

    /**
     * 每次注销时触发
     */
    @Override
    public void doLogout(String loginType, Object loginId, String tokenValue) {
        clearOnlineTokenInfo(loginType, tokenValue, "logout");
    }

    /**
     * 每次被踢下线时触发
     */
    @Override
    public void doKickout(String loginType, Object loginId, String tokenValue) {
        clearOnlineTokenInfo(loginType, tokenValue, "kickout");
    }

    /**
     * 每次被顶下线时触发
     */
    @Override
    public void doReplaced(String loginType, Object loginId, String tokenValue) {
        clearOnlineTokenInfo(loginType, tokenValue, "replaced");
    }

    /**
     * 每次被封禁时触发
     */
    @Override
    public void doDisable(String loginType, Object loginId, String service, int level, long disableTime) {
        // 批发用户被封禁时的处理逻辑
        if (WholesaleConstants.SaToken.LOGIN_TYPE.equals(loginType)) {
            log.info("wholesale user disabled: userId={}, service={}, level={}, disableTime={}", loginId, service, level, disableTime);
        }
    }

    /**
     * 每次被解封时触发
     */
    @Override
    public void doUntieDisable(String loginType, Object loginId, String service) {
        // 批发用户被解封时的处理逻辑
        if (WholesaleConstants.SaToken.LOGIN_TYPE.equals(loginType)) {
            log.info("wholesale user untie disable: userId={}, service={}", loginId, service);
        }
    }

    /**
     * 每次二级认证时触发
     */
    @Override
    public void doOpenSafe(String loginType, String tokenValue, String service, long safeTime) {
        // 批发用户二级认证时的处理逻辑
        if (WholesaleConstants.SaToken.LOGIN_TYPE.equals(loginType)) {
            log.info("wholesale user open safe: tokenValue={}, service={}, safeTime={}", tokenValue, service, safeTime);
        }
    }

    /**
     * 每次退出二级认证时触发
     */
    @Override
    public void doCloseSafe(String loginType, String tokenValue, String service) {
        // 批发用户退出二级认证时的处理逻辑
        if (WholesaleConstants.SaToken.LOGIN_TYPE.equals(loginType)) {
            log.info("wholesale user close safe: tokenValue={}, service={}", tokenValue, service);
        }
    }

    /**
     * 每次创建Session时触发
     */
    @Override
    public void doCreateSession(String id) {
        // 创建Session时的处理逻辑
        log.debug("wholesale create session: id={}", id);
    }

    /**
     * 每次注销Session时触发
     */
    @Override
    public void doLogoutSession(String id) {
        // 注销Session时的处理逻辑
        log.debug("wholesale logout session: id={}", id);
    }

    /**
     * 每次Token续期时触发 (Sa-Token 1.44.0)
     */
    @Override
    public void doRenewTimeout(String loginType, Object loginId, String tokenValue, long timeout) {
        // Token续期时的处理逻辑
        if (log.isDebugEnabled()) {
            log.debug("wholesale renew timeout: loginType={}, loginId={}, tokenValue={}, timeout={}", loginType, loginId, tokenValue, timeout);
        }
    }

    /**
     * 清理在线用户信息
     *
     * @param loginType  登录类型
     * @param tokenValue token值
     * @param action     行为描述 (logout, kickout, replaced)
     */
    private void clearOnlineTokenInfo(String loginType, String tokenValue, String action) {
        // 只处理批发模块的登出
        if (!WholesaleConstants.SaToken.LOGIN_TYPE.equals(loginType)) {
            return;
        }

        try {
            StpLogic stpLogic = SaManager.getStpLogic(loginType);
            if (stpLogic == null) {
                log.warn("Cannot find StpLogic for loginType: {}", loginType);
                return;
            }
            Object loginIdObj = stpLogic.getLoginIdByToken(tokenValue);
            // 记录登出日志并清理在线信息
            RedisUtils.deleteObject(CacheConstants.ONLINE_TOKEN_KEY + tokenValue);
            log.info("{} user {}: userId={}, token={}", loginType, action, loginIdObj != null ? loginIdObj : "unknown", tokenValue);
        } catch (Exception e) {
            log.error("Error clearing online token info for token {}: {}", tokenValue, e.getMessage(), e);
        }
    }
}
