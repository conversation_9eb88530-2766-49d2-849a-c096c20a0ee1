package com.imhuso.wholesale.core.constant;

/**
 * 昊天海外仓API常量接口
 *
 * <AUTHOR>
 */
public interface HaoTongApiConstants {

    /**
     * API基础URL
     */
    String API_BASE_URL = "https://sys.httlogistics.com/Api/HTYWMS";

    /**
     * API路径常量
     */
    String API_SEARCH_INVENTORY = "/SearchInventory";
    String API_ADD_ORDER = "/AddOrder";
    String API_VOID_ORDER = "/VoidOrder";
    String API_SEARCH_ORDER = "/SearchOrder";
    String API_GET_CHANNEL = "/GetChannel";
    String API_GET_INBOUND_ORDERS = "/SearchASNList";

    /**
     * 配置字段定义
     */
    String CONFIG_CUSTOMER_CODE = "customerCode";
    String CONFIG_API_KEY = "apiKey";
    String CONFIG_WAREHOUSE_ID = "WarehouseID";

    /**
     * 请求头常量
     */
    String HEADER_CUSTOMER_CODE = "CustomerCode";
    String HEADER_TIMESTAMP = "Timestamp";
    String HEADER_SIGN = "Sign";

    /**
     * 库存查询请求参数
     */
    String PARAM_WAREHOUSE_ID = "WareHousesID";
    String PARAM_SKU_OR_PRODUCT_CODE = "SKUOrProductCode";
    String PARAM_PAGE_SIZE = "PageSize";
    String PARAM_PAGE_INDEX = "PageIndex";

    /**
     * 库存查询响应字段
     */
    String RESP_RECORDS = "Records";
    String RESP_CUSTOMER_QTY = "CustomerQty";
    String RESP_CUSTOMER_USED_QTY = "CustomerUsedQty";
    String RESP_SKU = "SKU";

    /**
     * 创建订单请求参数
     */
    String PARAM_WAREHOUSE_ID_2 = "WarehouseID";
    String PARAM_CUSTOMER_CODE = "CustomerCode";
    String PARAM_CS_REF_NO = "CsRefNo";
    String PARAM_ORDER_NO = "OrderNo";

    /**
     * AddOrder 请求参数 ChannelID
     */
    String PARAM_CHANNEL_ID = "ChannelID";

    /**
     * AddOrder 请求参数 收件人信息
     */
    String PARAM_RECIPIENT_NAME = "RecipientName";
    String PARAM_RECIPIENT_COMPANY_NAME = "RecipientCompanyName";
    String PARAM_RECIPIENT_PHONE = "RecipientPhone";
    String PARAM_RECIPIENT_EMAIL = "RecipientEmail";
    String PARAM_RECIPIENT_COUNTRY = "RecipientCountry";
    String PARAM_RECIPIENT_STATE = "RecipientState";
    String PARAM_RECIPIENT_CITY = "RecipientCity";
    String PARAM_RECIPIENT_ADDRESS1 = "RecipientAddress1";
    String PARAM_RECIPIENT_ZIPCODE = "RecipientZipCode";

    /**
     * AddOrder 请求参数 包裹/订单属性
     */
    String PARAM_TOTAL_WEIGHT = "TotalWeight";
    String PARAM_SHIPPING_WEIGHT = "ShippingWeight";
    String PARAM_LENGTH = "Length";
    String PARAM_WIDTH = "Width";
    String PARAM_HEIGHT = "Height";
    String PARAM_BILL_QTY = "BillQty";
    String PARAM_IS_BATTERY_FLAG = "IsBatteryFlag";
    String PARAM_ORDER_STATUS = "OrderStatus";

    /**
     * AddOrder 请求参数 订单明细
     */
    String PARAM_ORDER_DETAILS = "OrderDetails";
    String PARAM_DETAIL_SKU = "SKU";
    String PARAM_DETAIL_CN_NAME = "CnName";
    String PARAM_DETAIL_EN_NAME = "EnName";
    String PARAM_DETAIL_QUANTITY = "Quantity";
    String PARAM_DETAIL_SINGLE_WEIGHT = "SingleWeight";
    String PARAM_DETAIL_SINGLE_PRICE = "SinglePrice";

    /**
     * AddOrder 请求参数 其他
     */
    String PARAM_RECIPIENT_CONTACT = "RecipientContact";
    String PARAM_IS_SIGN_FLAG = "IsSignFlag";
    String PARAM_CUS_REMARK = "CusRemark";

    /**
     * AddOrder 请求参数 OrderBags
     */
    String PARAM_ORDER_BAGS = "OrderBags";
    String PARAM_BAGS_WEIGHT = "BagsWeight";
    String PARAM_BAGS_LENGTH = "BagsLenght";
    String PARAM_BAGS_WIDTH = "BagsWidth";
    String PARAM_BAGS_HEIGHT = "BagsHeight";

    /**
     * AddOrder 请求参数 SecondCsRefNo (包裹ID)
     */
    String PARAM_SECOND_CS_REF_NO = "SecondCsRefNo";

    /**
     * 内部传递参数
     */
    String PARAM_LOGISTICS_METHOD_ID = "logisticsMethodId";

    /**
     * 订单响应字段
     */
    String RESP_SUCCESS = "Success";
    String RESP_MESSAGE = "Message";
    String RESP_TRACKING_NO = "TrackingNo";
    String RESP_ORDER_NO = "OrderNo";
    String RESP_CS_REF_NO = "CsRefNo";
    String RESP_DATA = "Data";

    /**
     * 取消订单请求参数
     */
    String PARAM_ORDER_NO_TYPE = "OrderNoType";

    /**
     * 渠道相关参数和响应
     */
    String RESP_CHANNEL_ID = "ChannelID";
    String RESP_CHANNEL_NAME = "ChannelName";
    String RESP_CHANNELS = "Channels";

    /**
     * 入库单查询参数
     */
    String PARAM_START_CREATE_TIME = "StartCreateTime";
    String PARAM_END_CREATE_TIME = "EndCreateTime";
    String PARAM_STATUS = "Status";
    String PARAM_ASN_NO = "AsnNo";

    /**
     * 入库单响应字段
     */
    String RESP_INBOUND_ORDERS = "InboundOrders";
    String RESP_INBOUND_ORDER_NO = "OrderNo";
    String RESP_INBOUND_STATUS = "Status";
    String RESP_INBOUND_STATUS_CODE = "StatusCode";
}
