package com.imhuso.wholesale.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * ERP系统配置类
 * 
 * 管理ERP系统集成的所有配置参数，包括连接信息、认证凭据、超时设置等。
 * 使用Spring Boot的@ConfigurationProperties自动绑定配置文件中的属性。
 * 
 * 配置文件示例（application.yml）：
 * <pre>
 * lookah:
 *   erp:
 *     enabled: true
 *     type: LOOKAH
 *     base-url: https://erp.example.com
 *     app-key: your-app-key
 *     app-secret: your-app-secret
 *     connect-timeout: 30000
 *     read-timeout: 60000
 *     batch-size: 500
 * </pre>
 * 
 * 安全注意事项：
 * - appKey和appSecret等敏感信息不应有默认值
 * - 生产环境中应使用环境变量或加密配置
 * - 日志输出时敏感信息应脱敏处理
 *
 * <AUTHOR>
 * @since 1.6.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "lookah.erp")
public class ErpConfig {

    /**
     * 是否启用ERP集成
     */
    private boolean enabled = false;

    /**
     * ERP系统类型
     */
    private String type = "LOOKAH";

    /**
     * API基础地址
     */
    private String baseUrl;

    /**
     * 应用标识符
     */
    private String appKey;

    /**
     * 应用密钥
     */
    private String appSecret;

    /**
     * 是否为内部系统
     */
    private boolean internal = true;

    /**
     * 连接超时时间(毫秒)
     */
    private int connectTimeout = 30000;

    /**
     * 读取超时时间(毫秒)
     */
    private int readTimeout = 60000;

    /**
     * 重试次数
     */
    private int retryCount = 3;

    /**
     * 重试间隔(毫秒)
     */
    private int retryInterval = 1000;

    /**
     * 批量查询最大数量
     */
    private int batchSize = 500;

    /**
     * Webhook配置
     */
    private Webhook webhook = new Webhook();

    @Data
    public static class Webhook {
        /**
         * 是否启用Webhook
         */
        private boolean enabled = false;



        /**
         * 签名验证是否启用
         */
        private boolean signatureVerification = true;
    }
}
