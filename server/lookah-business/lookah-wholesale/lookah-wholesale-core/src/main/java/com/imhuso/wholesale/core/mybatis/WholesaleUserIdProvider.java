package com.imhuso.wholesale.core.mybatis;

import com.imhuso.common.mybatis.handler.UserIdProvider;
import com.imhuso.wholesale.core.satoken.WholesaleLoginHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 批发模块用户ID提供者
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WholesaleUserIdProvider implements UserIdProvider {

    @Override
    public Long getCurrentUserId() {
        try {
            if (isLogin()) {
                Long userId = WholesaleLoginHelper.getUserId();
                log.debug("WholesaleUserIdProvider获取到用户ID: {}", userId);
                return userId;
            }
        } catch (Exception e) {
            log.debug("WholesaleUserIdProvider获取用户ID失败: {}", e.getMessage());
        }
        return null;
    }

    @Override
    public boolean isLogin() {
        try {
            return WholesaleLoginHelper.isLogin();
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public int getPriority() {
        // 批发用户优先级次之
        return 2;
    }

    @Override
    public boolean isApplicableForCurrentContext() {
        // 直接尝试获取批发用户登录状态，如果能获取到则说明适用于当前上下文
        try {
            return WholesaleLoginHelper.isLogin();
        } catch (cn.dev33.satoken.exception.SaTokenContextException e) {
            // 在异步环境中SaToken上下文未初始化，返回false
            log.debug("异步环境中SaToken上下文未初始化: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            log.debug("检查批发用户登录状态失败: {}", e.getMessage());
            return false;
        }
    }
}
