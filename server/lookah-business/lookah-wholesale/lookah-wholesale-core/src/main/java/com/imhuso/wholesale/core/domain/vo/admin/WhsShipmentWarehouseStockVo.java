package com.imhuso.wholesale.core.domain.vo.admin;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 仓库有效库存视图对象 (发货查询简化版)
 * 只包含仓库基本信息和有效可用库存
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WhsShipmentWarehouseStockVo {

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 有效可用库存 (物理可用 + 当前订单锁定)
     */
    private Integer effectiveAvailableStock;

}
