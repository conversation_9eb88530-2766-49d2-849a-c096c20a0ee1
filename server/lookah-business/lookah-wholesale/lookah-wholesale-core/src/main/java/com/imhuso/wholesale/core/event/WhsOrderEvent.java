package com.imhuso.wholesale.core.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 订单事件
 *
 * <AUTHOR>
 */
@Getter
public class WhsOrderEvent extends ApplicationEvent {

    /**
     * 事件类型
     */
    private final WhsOrderEventType eventType;

    /**
     * 订单ID
     */
    private final Long orderId;

    /**
     * 备注
     */
    private final String remark;

    /**
     * 操作用户ID
     */
    private final Long operatorId;

    /**
     * 完整构造方法
     *
     * @param source    事件源
     * @param eventType 事件类型
     * @param orderId   订单ID
     * @param remark    备注信息
     * @param operatorId 操作用户ID
     */
    public WhsOrderEvent(Object source, WhsOrderEventType eventType, Long orderId, String remark, Long operatorId) {
        super(source);
        this.eventType = eventType;
        this.orderId = orderId;
        this.remark = remark;
        this.operatorId = operatorId;
    }

    /**
     * 订单事件类型枚举
     */
    public enum WhsOrderEventType {
        /**
         * 订单创建
         */
        ORDER_CREATED,

        /**
         * 订单处理中
         */
        ORDER_PROCESSING,

        /**
         * 订单取消
         */
        ORDER_CANCELED,

        /**
         * 订单支付
         */
        ORDER_PAID,

        /**
         * 订单部分发货
         */
        ORDER_PARTIAL_SHIPPED,

        /**
         * 订单发货
         */
        ORDER_SHIPPED,

        /**
         * 订单发票已发送
         */
        ORDER_INVOICE_SENT,

        /**
         * 订单完成
         */
        ORDER_COMPLETED,

        /**
         * 订单状态变更（用于其他未指定具体事件类型的状态变更）
         */
        ORDER_STATUS_CHANGED,

        /**
         * 订单撤回
         */
        ORDER_REVERTED,

        /**
         * 包裹状态变更
         */
        PACKAGE_STATUS_CHANGED
    }
}
