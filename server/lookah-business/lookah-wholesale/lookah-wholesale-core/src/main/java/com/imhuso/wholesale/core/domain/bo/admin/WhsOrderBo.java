package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 批发订单业务对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class WhsOrderBo extends BaseEntity {

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 系统订单号
     */
    private String orderNo;

    /**
     * 客户订单号
     */
    private String customerOrderNo;

    /**
     * 参考号
     */
    private String referenceNo;

    /**
     * 收货人姓名
     */
    private String shippingName;

    /**
     * 收货人电话
     */
    private String shippingPhone;

    /**
     * 收货人邮箱
     */
    private String shippingEmail;

    /**
     * 收货国家
     */
    private String shippingCountry;

    /**
     * 销售代表ID
     */
    private Long salespersonId;

    /**
     * 订单状态（支持多选）
     */
    private Integer[] orderStatus;

    /**
     * 支付状态
     */
    private Integer paymentStatus;

    /**
     * 发货状态
     */
    private Integer shipmentStatus;

    /**
     * 发货状态（支持多选查询）
     */
    private Integer[] shipmentStatusArray;

    /**
     * 发票状态
     */
    private Integer invoiceStatus;

    /**
     * 发货审批状态
     * 0-待审核, 1-审核通过, 2-审核拒绝, 3-已撤销, null-无需审批
     */
    private Integer shipmentApprovalStatus;

    /**
     * 订单总金额（起始）
     */
    private BigDecimal minAmount;

    /**
     * 订单总金额（结束）
     */
    private BigDecimal maxAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 客户注册邮箱（通过memberId关联查询）
     */
    private String memberEmail;
}
