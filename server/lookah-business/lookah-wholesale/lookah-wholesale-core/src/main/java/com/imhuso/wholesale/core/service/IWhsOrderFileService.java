package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderFileCreateBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderFileVo;

import java.util.List;

/**
 * 批发订单文件关联服务接口
 *
 * <AUTHOR>
 */
public interface IWhsOrderFileService {

    /**
     * 添加订单文件关联
     *
     * @param orderId 订单ID
     * @param fileBo  文件信息
     * @return 是否成功
     */
    boolean addOrderFile(Long orderId, WhsOrderFileCreateBo fileBo);

    /**
     * 批量添加订单文件关联
     *
     * @param orderId 订单ID
     * @param files   文件列表
     * @return 成功添加的数量
     */
    int addOrderFiles(Long orderId, List<WhsOrderFileCreateBo> files);

    /**
     * 删除订单文件关联
     *
     * @param orderId 订单ID
     * @param fileId  文件关联ID
     * @return 是否成功
     */
    boolean removeOrderFile(Long orderId, Long fileId);

    /**
     * 根据OSS ID删除订单文件关联
     *
     * @param orderId 订单ID
     * @param ossId   OSS文件ID
     * @return 是否成功
     */
    boolean removeOrderFileByOssId(Long orderId, Long ossId);

    /**
     * 获取订单的所有文件
     *
     * @param orderId 订单ID
     * @return 文件列表
     */
    List<WhsOrderFileVo> getOrderFiles(Long orderId);

    /**
     * 获取订单指定类型的文件
     *
     * @param orderId  订单ID
     * @param fileType 文件类型
     * @return 文件列表
     */
    List<WhsOrderFileVo> getOrderFilesByType(Long orderId, String fileType);

    /**
     * 获取订单的INVOICE文件
     *
     * @param orderId 订单ID
     * @return INVOICE文件列表
     */
    List<WhsOrderFileVo> getOrderInvoiceFiles(Long orderId);

    /**
     * 清空订单的所有文件关联
     *
     * @param orderId 订单ID
     * @return 删除的数量
     */
    int clearOrderFiles(Long orderId);

    /**
     * 清空订单指定类型的文件关联
     *
     * @param orderId  订单ID
     * @param fileType 文件类型
     * @return 删除的数量
     */
    int clearOrderFilesByType(Long orderId, String fileType);
}
