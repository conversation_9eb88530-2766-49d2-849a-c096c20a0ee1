package com.imhuso.wholesale.core.domain.bo.admin;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * ERP库存同步业务对象
 * 用于从ERP系统同步成品库存和待产库存数据
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ErpStockSyncBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品变体ID
     */
    private Long variantId;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 成品库存数量
     */
    private Integer finishedStock;

    /**
     * 待产库存数量
     */
    private Integer pendingStock;

    /**
     * 同步时间戳
     */
    private Long syncTimestamp;

    /**
     * ERP系统来源标识
     */
    private String erpSource;

    public ErpStockSyncBo(String skuCode, Integer finishedStock, Integer pendingStock) {
        this.skuCode = skuCode;
        this.finishedStock = finishedStock;
        this.pendingStock = pendingStock;
        this.syncTimestamp = System.currentTimeMillis();
    }
}
