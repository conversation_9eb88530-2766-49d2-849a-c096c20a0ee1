package com.imhuso.wholesale.core.enums;

import com.imhuso.common.translation.core.EnumTranslatableInterface;
import lombok.Getter;

/**
 * 库存操作类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum StockOperationType implements EnumTranslatableInterface {

    /**
     * 初始化库存
     */
    CREATE(1),

    /**
     * 调整库存
     */
    ADJUST(2),

    /**
     * 锁定库存
     */
    LOCK(3),

    /**
     * 释放库存
     */
    RELEASE(4),

    /**
     * 扣减库存
     */
    DEDUCT(5),

    /**
     * 同步库存
     */
    SYNC(6),

    /**
     * 发货出库
     */
    OUTBOUND(7);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 构造函数
     */
    StockOperationType(int value) {
        this.value = value;
    }
}
