package com.imhuso.wholesale.core.security;

import cn.dev33.satoken.exception.NotLoginException;
import com.imhuso.common.core.utils.ServletUtils;
import com.imhuso.common.core.utils.StringUtils;

import com.imhuso.wholesale.core.satoken.WholesaleLoginHelper;
import com.imhuso.wholesale.core.satoken.utils.StpWholesaleUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 批发客户端验证器
 * 验证客户端ID与Token的一致性
 *
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class WholesaleClientValidator {

    /**
     * 验证客户端ID与Token的一致性
     *
     * @param request HTTP请求对象
     * @throws NotLoginException 验证失败时抛出
     */
    public static void validateConsistency(HttpServletRequest request) {
        // 检查 header 与 param 里的 clientId 与 token 里的是否一致
        String headerCid = request.getHeader(WholesaleLoginHelper.CLIENT_ID);
        String paramCid = ServletUtils.getParameter(WholesaleLoginHelper.CLIENT_ID);
        String clientId = StpWholesaleUtil.getExtra(WholesaleLoginHelper.CLIENT_ID).toString();

        if (!StringUtils.equalsAny(clientId, headerCid, paramCid)) {
            // token 无效
            throw NotLoginException.newInstance(StpWholesaleUtil.getLoginType(),
                "-100", "客户端ID与Token不匹配",
                StpWholesaleUtil.getTokenValue());
        }
    }
}
