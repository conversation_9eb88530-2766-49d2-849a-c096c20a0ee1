package com.imhuso.wholesale.core.adapter.impl;

import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.wholesale.core.adapter.AbstractErpProvider;
import com.imhuso.wholesale.core.client.LookahErpApiClient;
import com.imhuso.wholesale.core.domain.dto.ErpStockBo;
import com.imhuso.wholesale.core.config.ErpConfig;
import com.imhuso.wholesale.core.domain.bo.admin.ErpStockSyncBo;
import com.imhuso.wholesale.core.domain.vo.admin.ErpSyncResultVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Lookah ERP系统提供商实现
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LookahErpProvider extends AbstractErpProvider {

    private static final String PROVIDER_NAME = "Lookah ERP";
    private static final String PROVIDER_TYPE = "LOOKAH";

    private final ErpConfig erpConfig;
    private final LookahErpApiClient apiClient;

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public String getProviderType() {
        return PROVIDER_TYPE;
    }

    @Override
    protected boolean checkConnection() {
        log.info("开始检查 Lookah ERP 连接配置");

        boolean enabled = erpConfig.isEnabled();
        log.info("ERP 启用状态: {}", enabled);
        if (!enabled) {
            log.warn("ERP集成未启用");
            return false;
        }

        String baseUrl = erpConfig.getBaseUrl();
        String appKey = erpConfig.getAppKey();
        String appSecret = erpConfig.getAppSecret();

        log.info("ERP 配置检查 - baseUrl: {}, appKey: {}, appSecret: {}",
                baseUrl != null ? baseUrl : "null",
                appKey != null ? appKey : "null",
                appSecret != null ? "已配置" : "null");

        // 移除健康检查逻辑，直接检查基本配置
        boolean isValid = baseUrl != null &&
                         !baseUrl.trim().isEmpty() &&
                         appKey != null &&
                         !appKey.trim().isEmpty() &&
                         appSecret != null &&
                         !appSecret.trim().isEmpty();

        log.info("Lookah ERP连接配置检查结果: {}", isValid);
        return isValid;
    }

    @Override
    public List<ErpStockSyncBo> fetchStockData(List<String> skuCodes) {
        if (!validateSkuCodes(skuCodes)) {
            return createEmptyStockList();
        }

        if (!isAvailable()) {
            log.warn("Lookah ERP不可用，无法获取库存数据");
            return createEmptyStockList();
        }

        logSyncStart("获取库存数据", skuCodes.size());

        try {
            // 调用Lookah ERP API获取库存数据
            List<ErpStockBo> erpStockDataList = apiClient.getProductStockList(skuCodes);

            // 转换为标准格式
            List<ErpStockSyncBo> result = erpStockDataList.stream()
                    .map(this::convertToErpStockSyncBo)
                    .collect(Collectors.toList());

            log.info("成功从Lookah ERP获取{}个SKU的库存数据", result.size());
            return result;

        } catch (Exception e) {
            log.error("从Lookah ERP获取库存数据失败，SKU数量: {}, 错误: {}", skuCodes.size(), e.getMessage(), e);
            // 统一异常处理策略：对于批量库存获取，返回空列表避免中断整个同步流程
            // 但会记录详细的错误日志用于问题排查
            return createEmptyStockList();
        }
    }

    @Override
    public ErpSyncResultVo syncStockData(List<ErpStockSyncBo> stockDataList) {
        if (!validateStockData(stockDataList)) {
            return ErpSyncResultVo.failure(
                MessageUtils.message("erp.stock.sync.invalid.data"),
                getProviderName()
            );
        }

        if (!isAvailable()) {
            return ErpSyncResultVo.failure(
                MessageUtils.message("lookah.erp.unavailable"),
                getProviderName()
            );
        }

        long startTime = System.currentTimeMillis();
        logSyncStart("同步库存数据", stockDataList.size());

        try {
            // 注意：Lookah ERP系统目前只提供库存查询API，不提供库存更新API
            // 根据ERP对接文档，我们只能从ERP获取库存数据，不能向ERP推送库存变更
            // 因此这个方法主要用于记录同步尝试，实际的库存同步是单向的（ERP -> 系统）

            log.info("Lookah ERP系统不支持库存数据推送，跳过向ERP同步{}个SKU的库存数据", stockDataList.size());

            long duration = System.currentTimeMillis() - startTime;
            ErpSyncResultVo result = ErpSyncResultVo.success(
                0, // 实际同步数量为0，因为ERP不支持推送
                stockDataList.size(), // 尝试同步的数量
                getProviderName(),
                duration
            );

            logSyncComplete("同步库存数据", result);
            return result;

        } catch (Exception e) {
            ErpSyncResultVo result = handleException("同步库存数据", e);
            logSyncComplete("同步库存数据", result);
            return result;
        }
    }

    /**
     * 转换ERP库存数据为标准格式
     *
     * @param erpStockBo ERP库存业务对象
     * @return 标准库存同步对象
     */
    private ErpStockSyncBo convertToErpStockSyncBo(ErpStockBo erpStockBo) {
        ErpStockSyncBo syncBo = new ErpStockSyncBo();
        syncBo.setSkuCode(erpStockBo.getSku());
        syncBo.setFinishedStock(erpStockBo.getFinishedStock());
        syncBo.setPendingStock(erpStockBo.getPendingStock());
        syncBo.setErpSource(PROVIDER_TYPE);
        syncBo.setSyncTimestamp(System.currentTimeMillis());
        return syncBo;
    }
}
