package com.imhuso.wholesale.core.domain.vo.admin;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.imhuso.common.excel.annotation.ExcelDictFormat;
import com.imhuso.common.excel.convert.ExcelDictConvert;
import com.imhuso.common.translation.annotation.Translation;
import com.imhuso.wholesale.core.constant.TransConstant;
import com.imhuso.wholesale.core.domain.WhsShipmentConversion;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 发货转换记录视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AutoMapper(target = WhsShipmentConversion.class)
@ExcelIgnoreUnannotated
public class WhsShipmentConversionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 转换记录ID
     */
    @ExcelProperty(value = "转换记录ID")
    private Long id;

    /**
     * 原始订单ID
     */
    @ExcelProperty(value = "原始订单ID")
    private Long orderId;

    /**
     * 原始订单项ID
     */
    @ExcelProperty(value = "原始订单项ID")
    private Long orderItemId;

    /**
     * 原始变体ID
     */
    @ExcelProperty(value = "原始变体ID")
    private Long originalVariantId;

    /**
     * 原始变体SKU编码
     */
    @ExcelProperty(value = "原始变体SKU编码")
    private String originalSkuCode;

    /**
     * 参与转换的原始订单项数量
     */
    @ExcelProperty(value = "参与转换的原始订单项数量")
    private Integer originalQuantity;

    /**
     * 原始包装类型: 0-单品 1-展示盒 2-整箱
     */
    @ExcelProperty(value = "原始包装类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=单品,1=展示盒,2=整箱")
    private Integer originalPackagingType;

    /**
     * 原始包装类型名称
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, mapper = "originalPackagingType", other = "packagingType")
    private String originalPackagingTypeText;

    /**
     * 关联的发货计划ID
     */
    @ExcelProperty(value = "关联的发货计划ID")
    private Long planId;

    /**
     * 转换后生成的发货计划项ID
     */
    @ExcelProperty(value = "转换后生成的发货计划项ID")
    private Long planItemId;

    /**
     * 实际发货变体ID
     */
    @ExcelProperty(value = "实际发货变体ID")
    private Long actualVariantId;

    /**
     * 实际发货变体SKU编码
     */
    @ExcelProperty(value = "实际发货变体SKU编码")
    private String actualSkuCode;

    /**
     * 转换后的数量
     */
    @ExcelProperty(value = "转换后的数量")
    private Integer actualQuantity;

    /**
     * 转换后包装类型: 0-单品 1-展示盒 2-整箱
     */
    @ExcelProperty(value = "转换后包装类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=单品,1=展示盒,2=整箱")
    private Integer actualPackagingType;

    /**
     * 转换后包装类型名称
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, mapper = "actualPackagingType", other = "packagingType")
    private String actualPackagingTypeText;

    /**
     * 转换说明 (新增)
     */
    @ExcelProperty(value = "转换说明")
    private String remark;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
