package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * 国家对象
 *
 * <AUTHOR>
 */
@Data
@TableName("whs_country")
public class WhsCountry {

    /**
     * 国家ID
     */
    @TableId
    private Long id;

    /**
     * 国家代码
     */
    private String code;

    /**
     * 国家名称
     */
    private String name;

    /**
     * 国家中文名称
     */
    @JsonIgnore
    private String nameZh;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 排序
     */
    private Integer sort;
}
