package com.imhuso.wholesale.core.domain.bo.admin;

import java.math.BigDecimal;
import java.util.List;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.wholesale.core.domain.WhsShipmentPlan;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanVo;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 发货方案业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMappers({
    @AutoMapper(target = WhsShipmentPlan.class),
    @AutoMapper(target = WhsShipmentPlanVo.class)
})
public class WhsShipmentPlanBo extends BaseEntity {

    /**
     * 方案ID
     */
    @NotNull(message = "方案ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空", groups = {AddGroup.class})
    private Long orderId;

    /**
     * 方案名称
     */
    @NotBlank(message = "方案名称不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 100, message = "方案名称长度不能超过100个字符")
    private String planName;

    /**
     * 方案评分
     */
    private BigDecimal score;

    /**
     * 是否系统推荐
     */
    private Boolean isRecommended;

    /**
     * 是否已选择
     */
    private Boolean isSelected;

    /**
     * 是否为自定义方案
     */
    private Boolean isCustom;

    /**
     * 包裹数量
     */
    private Integer packageCount;

    /**
     * 总PCS数量
     */
    private Integer totalPcs;

    /**
     * 状态：0-待确认 1-已确认 2-已执行 3-不可用
     */
    private Integer status;

    /**
     * 方案项列表
     */
    @Valid
    private List<WhsShipmentPlanItemBo> planItems;
}
