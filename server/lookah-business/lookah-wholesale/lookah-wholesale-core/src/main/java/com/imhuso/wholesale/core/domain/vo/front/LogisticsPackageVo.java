package com.imhuso.wholesale.core.domain.vo.front;

import com.imhuso.common.translation.annotation.Translation;
import com.imhuso.wholesale.core.constant.TransConstant;
import com.imhuso.wholesale.core.domain.WhsLogisticsPackage;
import com.imhuso.wholesale.core.utils.carrier.TrackingNumberUtils;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 物流包裹视图对象（前端用户）
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsLogisticsPackage.class)
public class LogisticsPackageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 包裹ID
     */
    private Long id;

    /**
     * 跟踪号
     */
    private String trackingNumber;

    /**
     * 承运商(如：FedEx、DHL、UPS)
     */
    private String carrier;

    /**
     * 包裹状态(0-待处理 1-已发货 2-运输中 3-已送达 4-异常)
     */
    private Integer packageStatus;

    /**
     * 包裹状态文本
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, other = "packageStatus", mapper = "packageStatus")
    private String packageStatusText;

    /**
     * 发货日期
     */
    private Date shippedDate;

    /**
     * 海外仓订单号
     */
    private String externalOrderNo;

    /**
     * 物流追踪链接
     */
    private String trackingUrl;

    /**
     * 获取物流追踪链接
     */
    public String getTrackingUrl() {
        if (trackingUrl == null && trackingNumber != null && !trackingNumber.isEmpty()) {
            trackingUrl = TrackingNumberUtils.getTrackingUrl(trackingNumber, carrier);
        }
        return trackingUrl;
    }
}
