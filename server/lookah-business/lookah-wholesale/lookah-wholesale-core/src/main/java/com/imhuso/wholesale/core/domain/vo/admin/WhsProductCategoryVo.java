package com.imhuso.wholesale.core.domain.vo.admin;

import java.io.Serial;
import java.io.Serializable;

import com.imhuso.wholesale.core.domain.WhsProductCategory;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

/**
 * 批发产品分类视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsProductCategory.class)
public class WhsProductCategoryVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分类ID
     */
    private Long id;

    /**
     * 父分类ID
     */
    private Long parentId;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 显示顺序
     */
    private Integer sort;

    /**
     * 状态（0停用 1正常）
     */
    private String status;
}
