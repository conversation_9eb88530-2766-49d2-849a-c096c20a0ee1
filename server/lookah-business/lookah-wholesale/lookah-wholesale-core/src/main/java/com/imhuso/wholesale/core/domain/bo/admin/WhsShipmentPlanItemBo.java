package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.wholesale.core.domain.WhsShipmentPlanItem;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanItemVo;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 发货方案项业务对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@AutoMappers({
    @AutoMapper(target = WhsShipmentPlanItem.class),
    @AutoMapper(target = WhsShipmentPlanItemVo.class)
})
public class WhsShipmentPlanItemBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    private Long id;

    /**
     * 方案ID
     */
    private Long planId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * SKU编码 (对应数据库字段，通常为实际发货SKU)
     */
    private String skuCode;

    /**
     * 计划项数量 (例如: 1箱, 5个散件)
     */
    private Integer quantity;

    /**
     * 包装类型: 0-单品 1-展示盒 2-整箱
     */
    private Integer packagingType;

    /**
     * 包装数量 (每计划单位包含的原始物品数)
     */
    private Integer packagingQuantity;

    /**
     * 已发货数量
     */
    private Integer shippedQuantity;
}
