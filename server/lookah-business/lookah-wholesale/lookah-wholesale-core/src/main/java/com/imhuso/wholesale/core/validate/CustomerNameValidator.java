package com.imhuso.wholesale.core.validate;

import com.imhuso.common.core.utils.MessageUtils;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.lang.reflect.Field;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * 客户姓名长度验证器
 * 验证firstName + " " + lastName的总长度不超过指定限制
 * 
 * <AUTHOR>
 */
@Slf4j
public class CustomerNameValidator implements ConstraintValidator<ValidCustomerName, Object> {
    
    private int maxLength;
    
    // 缓存字段反射结果以提高性能
    private static final Map<String, Field> FIELD_CACHE = new ConcurrentHashMap<>();
    
    public CustomerNameValidator() {
        log.debug("CustomerNameValidator instantiated");
    }
    
    @Override
    public void initialize(ValidCustomerName constraintAnnotation) {
        this.maxLength = constraintAnnotation.maxLength();
        log.debug("CustomerNameValidator initialized with maxLength: {}", maxLength);
    }
    
    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        log.debug("CustomerNameValidator.isValid called with value: {}", value);
        
        if (value == null) {
            log.debug("Value is null, returning true");
            return true;
        }
        
        try {
            // 使用反射获取firstName和lastName字段的值（使用缓存提高性能）
            Field firstNameField = getCachedField(value.getClass(), "firstName");
            Field lastNameField = getCachedField(value.getClass(), "lastName");
            
            log.debug("Found fields - firstName: {}, lastName: {}", firstNameField != null, lastNameField != null);
            
            if (firstNameField == null || lastNameField == null) {
                return true;
            }
            
            // 字段在缓存时已经设置为可访问，不需要重复设置
            
            String firstName = (String) firstNameField.get(value);
            String lastName = (String) lastNameField.get(value);
            
            log.debug("Extracted values - firstName: '{}', lastName: '{}'", firstName, lastName);
            
            // 如果firstName或lastName为空，交给@NotBlank注解处理
            if (firstName == null || lastName == null || firstName.isEmpty() || lastName.isEmpty()) {
                log.debug("First name or last name is null/empty, returning true");
                return true;
            }
            
            // 计算CustomerName的长度: firstName + " " + lastName
            int totalLength = firstName.length() + 1 + lastName.length();
            
            log.debug("CustomerName validation - firstName: '{}' ({}), lastName: '{}' ({}), total: {}/{}", 
                firstName, firstName.length(), lastName, lastName.length(), totalLength, maxLength);
            
            if (totalLength > maxLength) {
                // 使用国际化消息，提供更详细的信息
                String errorMessage = MessageUtils.message("wholesale.admin.member.customer.name.too.long", 
                    maxLength, totalLength, firstName.length(), lastName.length());
                log.warn("Validation failed: {}", errorMessage);
                
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(errorMessage).addConstraintViolation();
                return false;
            }
            
            log.debug("Validation passed: total length {} <= {}", totalLength, maxLength);
            return true;
            
        } catch (IllegalAccessException e) {
            // 记录详细的异常信息，便于问题排查
            log.warn("Failed to access fields for customer name validation on class {}: {}", 
                    value.getClass().getSimpleName(), e.getMessage());
            log.debug("Full exception details for customer name validation", e);
            
            // 如果无法获取字段值，返回true（不阻塞验证，但已记录问题）
            return true;
        } catch (Exception e) {
            // 捕获其他可能的异常，确保验证过程不会崩溃
            log.error("Unexpected error during customer name validation on class {}: {}", 
                     value.getClass().getSimpleName(), e.getMessage());
            log.debug("Full exception details for unexpected validation error", e);
            
            // 出现未预期的异常时，为了安全起见返回false（防止无效数据通过验证）
            return false;
        }
    }
    
    /**
     * 获取缓存的字段，提高性能
     */
    private Field getCachedField(Class<?> clazz, String fieldName) {
        String key = clazz.getName() + "." + fieldName;
        return FIELD_CACHE.computeIfAbsent(key, k -> findField(clazz, fieldName));
    }
    
    /**
     * 递归查找字段，包括父类中的字段
     */
    private Field findField(Class<?> clazz, String fieldName) {
        Class<?> searchType = clazz;
        while (searchType != null && searchType != Object.class) {
            try {
                Field field = searchType.getDeclaredField(fieldName);
                field.setAccessible(true); // 在缓存时就设置可访问
                return field;
            } catch (NoSuchFieldException e) {
                // 继续在父类中查找
                searchType = searchType.getSuperclass();
            }
        }
        return null;
    }
}