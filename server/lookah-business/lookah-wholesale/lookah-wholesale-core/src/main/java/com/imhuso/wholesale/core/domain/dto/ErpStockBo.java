package com.imhuso.wholesale.core.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * ERP库存业务对象
 * <p>
 * 用于封装从ERP系统获取的库存数据，与ERP API返回的数据结构完全匹配。
 * 支持成品库存（inStock）和待产库存（productionStock）的数据映射。
 * <p>
 * 使用场景：
 * - ERP库存数据同步
 * - 库存信息展示
 * - 库存统计分析
 *
 * <AUTHOR>
 * @since 1.6.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ErpStockBo {

    /**
     * SKU编码
     */
    private String sku;

    /**
     * 成品库存数量（在库库存）
     */
    @JsonProperty("inStock")
    private Integer inStock;

    /**
     * 待产库存数量（生产库存）
     */
    @JsonProperty("productionStock")
    private Integer productionStock;

    /**
     * 获取成品库存数量
     */
    public Integer getFinishedStock() {
        return inStock != null ? inStock : 0;
    }

    /**
     * 获取待产库存数量
     */
    public Integer getPendingStock() {
        return productionStock != null ? productionStock : 0;
    }
}
