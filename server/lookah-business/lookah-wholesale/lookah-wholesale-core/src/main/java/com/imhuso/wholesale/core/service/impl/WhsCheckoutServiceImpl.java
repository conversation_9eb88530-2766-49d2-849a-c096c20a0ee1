package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.imhuso.wholesale.core.domain.bo.front.CartCheckResultBo;
import com.imhuso.wholesale.core.domain.vo.front.CheckoutSummaryVo;
import com.imhuso.wholesale.core.domain.vo.front.ShippingAddressVo;
import com.imhuso.wholesale.core.service.IWhsCartService;
import com.imhuso.wholesale.core.service.IWhsCheckoutService;
import com.imhuso.wholesale.core.service.IWhsShippingAddressService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 批发结账Service实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsCheckoutServiceImpl implements IWhsCheckoutService {

    private final IWhsCartService cartService;
    private final IWhsShippingAddressService addressService;

    @Override
    public CheckoutSummaryVo getCheckoutSummary() {
        CheckoutSummaryVo summary = new CheckoutSummaryVo();

        // 获取默认收货地址
        ShippingAddressVo defaultAddress = addressService.getDefaultAddress();
        if (!ObjectUtil.isNull(defaultAddress)) {
            summary.setAddressId(defaultAddress.getId());
        }

        // 获取购物车商品并校验
        CartCheckResultBo cartResult = cartService.getAndCheckCartItems();
        summary.setCartItems(cartResult.getCartItems());

        return summary;
    }
}
