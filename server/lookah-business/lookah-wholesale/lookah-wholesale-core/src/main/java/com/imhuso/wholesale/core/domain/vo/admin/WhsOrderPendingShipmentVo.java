package com.imhuso.wholesale.core.domain.vo.admin;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 订单待发货明细视图对象
 *
 * <AUTHOR>
 */
@Data
public class WhsOrderPendingShipmentVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 是否有发货方案
     */
    private Boolean hasShipmentPlan;

    /**
     * 发货方案名称
     */
    private String planName;

    /**
     * 待发货明细列表
     */
    private List<PendingShipmentItem> pendingItems;

    /**
     * 待发货明细项
     */
    @Data
    public static class PendingShipmentItem implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 变体ID
         */
        private Long variantId;

        /**
         * SKU编码
         */
        private String skuCode;

        /**
         * 产品名称
         */
        private String productName;

        /**
         * 规格信息
         */
        private String specs;

        /**
         * 包装类型: 0-单品 1-展示盒 2-整箱
         */
        private Integer packagingType;

        /**
         * 包装数量 (每计划单位包含的原始物品数)
         */
        private Integer packagingQuantity;

        /**
         * 待发货数量
         */
        private Integer pendingQuantity;

        /**
         * 已发货数量
         */
        private Integer shippedQuantity;

        /**
         * 总订单数量
         */
        private Integer totalQuantity;

        /**
         * 在途库存数量
         */
        private Integer inTransitStock;

        /**
         * 可用库存数量
         */
        private Integer availableStock;
    }
}
