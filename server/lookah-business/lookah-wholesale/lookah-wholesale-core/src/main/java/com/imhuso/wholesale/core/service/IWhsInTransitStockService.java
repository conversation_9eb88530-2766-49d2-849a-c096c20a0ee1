package com.imhuso.wholesale.core.service;

import java.util.List;
import java.util.Map;

/**
 * 在途库存服务接口
 *
 * <AUTHOR>
 */
public interface IWhsInTransitStockService {

    /**
     * 更新所有变体的在途库存
     * 从入库单中计算并更新在途库存表
     *
     * @return 更新的记录数
     */
    int updateAllInTransitStock();

    /**
     * 获取变体的在途库存
     *
     * @param variantId 变体ID
     * @return 在途库存数量
     */
    int getInTransitStock(Long variantId);

    /**
     * 批量获取变体的在途库存
     *
     * @param variantIds 变体ID列表
     * @return 变体ID到在途库存数量的映射
     */
    Map<Long, Integer> getInTransitStocks(List<Long> variantIds);
}
