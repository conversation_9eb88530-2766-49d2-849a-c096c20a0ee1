package com.imhuso.wholesale.core.adapter;

import com.imhuso.wholesale.core.domain.bo.admin.ErpStockSyncBo;
import com.imhuso.wholesale.core.domain.vo.admin.ErpSyncResultVo;

import java.util.List;

/**
 * ERP系统提供商接口
 * 定义了与ERP系统交互的标准接口
 *
 * <AUTHOR>
 */
public interface IErpProvider {

    /**
     * 获取ERP提供商名称
     *
     * @return ERP提供商名称
     */
    String getProviderName();

    /**
     * 获取ERP提供商类型
     *
     * @return ERP提供商类型
     */
    String getProviderType();

    /**
     * 检查ERP连接是否可用
     *
     * @return 连接状态
     */
    boolean isAvailable();

    /**
     * 从ERP系统获取库存数据
     *
     * @param skuCodes SKU编码列表
     * @return ERP库存数据列表
     */
    List<ErpStockSyncBo> fetchStockData(List<String> skuCodes);

    /**
     * 批量同步库存数据到ERP系统
     *
     * @param stockDataList 库存数据列表
     * @return 同步结果
     */
    ErpSyncResultVo syncStockData(List<ErpStockSyncBo> stockDataList);

    /**
     * 获取ERP系统配置信息
     *
     * @return 配置信息
     */
    String getConfigInfo();
}
