package com.imhuso.wholesale.core.domain.vo.admin;

import cn.dev33.satoken.stp.StpUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.imhuso.common.translation.annotation.Translation;
import com.imhuso.wholesale.core.constant.TransConstant;
import com.imhuso.wholesale.core.domain.WhsOrder;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 批发订单后台视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsOrder.class)
public class WhsOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long id;

    /**
     * 系统订单号
     */
    private String orderNo;

    /**
     * 客户订单号
     */
    private String customerOrderNo;

    /**
     * 参考号
     */
    private String referenceNo;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 销售代表ID，关联sys_user表
     */
    private Long salespersonId;

    /**
     * 销售代表姓名（关联字段）
     */
    @Translation(type = TransConstant.USER_ID_TO_NICKNAME, mapper = "salespersonId")
    private String salespersonName;

    /**
     * 会员名称（冗余）
     */
    private String memberName;

    /**
     * 客户邮箱账号
     */
    private String memberEmail;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 收货人姓名
     */
    private String shippingName;

    /**
     * 收货人电话
     */
    private String shippingPhone;

    /**
     * 收货人邮箱
     */
    private String shippingEmail;

    /**
     * 收货国家
     */
    private String shippingCountry;

    /**
     * 收货州/省
     */
    private String shippingState;

    /**
     * 收货人城市
     */
    private String shippingCity;

    /**
     * 收货人地址
     */
    private String shippingAddress;

    /**
     * 收货人详细地址2
     */
    private String shippingAddress2;

    /**
     * 收货人邮编
     */
    private String shippingZip;

    /**
     * 收货人公司名称
     */
    private String shippingCompanyName;

    /**
     * 完整收货地址（格式化后的地址，用于直接展示）
     */
    private String fullShippingAddress;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 订单状态描述
     */
    private String orderStatusText;

    /**
     * 支付状态
     */
    private String paymentStatus;

    /**
     * 支付状态描述
     */
    private String paymentStatusText;

    /**
     * 发货状态
     */
    private String shipmentStatus;

    /**
     * 发货状态文本
     */
    private String shipmentStatusText;

    /**
     * 发票状态
     */
    private String invoiceStatus;

    /**
     * 发票状态文本
     */
    private String invoiceStatusText;

    /**
     * 发货审批状态
     * 0-待审核, 1-审核通过, 2-审核拒绝, 3-已撤销, null-无需审批
     */
    private Integer shipmentApprovalStatus;

    /**
     * 发货审批状态文本
     */
    @Translation(type = TransConstant.WHOLESALE_SHIPMENT_APPROVAL_STATUS, other = "shipmentApprovalStatus", mapper = "shipmentApprovalStatus")
    private String shipmentApprovalStatusText;

    /**
     * 审批意见（审批通过时的意见）
     */
    private String approvalComment;

    /**
     * 订单项总数量
     */
    private Integer totalItems;

    /**
     * 商品总PCS数量
     */
    private Integer totalPcs;

    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmount;

    /**
     * 订单关联的文件列表（仅在详情时返回）
     */
    private List<WhsOrderFileVo> files;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 订单项列表（仅在详情时返回）
     */
    private List<WhsOrderItemVo> items;

    /**
     * 物流信息列表（仅在详情时返回）
     */
    private List<WhsOrderShipmentVo> shipment;

    /**
     * 状态变更日志（仅在详情时返回）
     */
    private List<WhsOrderStatusLogVo> statusLogs;

    /**
     * 发货仓库名称（聚合显示）
     * 多个仓库时使用顿号分隔，例如："CHI、LA、Factory（国内）"
     */
    private String shippingWarehouses;

    /**
     * 库存状态
     * 0-库存充足 1-库存不足 2-无库存
     */
    private Integer stockStatus;

    /**
     * 库存状态文本
     */
    private String stockStatusText;

    /**
     * 是否有库存不足的商品
     */
    private Boolean hasInsufficientStock;

    /**
     * 订单总箱数
     */
    private Integer totalCases;

    /**
     * 已发货箱数
     */
    private Integer shippedCases;

    /**
     * 已发货PCS数量
     */
    private Integer shippedPcs;

    /**
     * 发货时间文本（多次发货用逗号分隔）
     */
    private String shipmentDatesText;

    /**
     * 实际运费（所有包裹运费之和）
     */
    private BigDecimal actualFreight;

    /**
     * 运费币种
     */
    private String freightCurrency;

    /**
     * 是否显示发货审批申请按钮
     * true-显示审核申请按钮，false-不显示
     */
    private Boolean showApprovalButton;

    /**
     * 是否显示发货按钮
     * true-显示发货按钮，false-不显示
     */
    private Boolean showShipmentButton;

    /**
     * 是否显示撤回审批按钮
     * true-显示撤回审批按钮，false-不显示
     */
    private Boolean showCancelApprovalButton;

    /**
     * 是否显示审批操作按钮（通过/拒绝）
     * true-显示审批操作按钮，false-不显示
     */
    private Boolean showApproveButton;

    /**
     * 发货按钮文本（发货/继续发货）
     */
    private String shipmentButtonText;

    /**
     * 装箱清单可用性信息
     */
    private PackingSlipAvailabilityVo packingSlipAvailability;

    /**
     * 检查是否有查看敏感信息的权限
     */
    @JsonIgnore
    public boolean hasViewSensitivePermission() {
        try {
            return StpUtil.hasPermission("wholesale:order:sensitive:view");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 过滤敏感信息（如果没有权限）
     * 注意：订单敏感字段暂时留空，不进行过滤
     */
    public void filterSensitiveData() {
        if (!hasViewSensitivePermission()) {
            // 订单敏感字段暂时留空，不进行任何过滤
            // 如需启用敏感信息过滤，可以在此添加字段过滤逻辑
        }
    }
}
