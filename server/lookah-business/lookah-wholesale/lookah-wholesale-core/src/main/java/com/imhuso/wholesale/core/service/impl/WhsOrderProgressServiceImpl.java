package com.imhuso.wholesale.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.wholesale.core.domain.WhsShipmentPlan;
import com.imhuso.wholesale.core.domain.WhsShipmentPlanItem;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderProgressVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanItemVo;
import com.imhuso.wholesale.core.enums.ShipmentPlanStatus;
import com.imhuso.wholesale.core.mapper.WhsShipmentPlanItemMapper;
import com.imhuso.wholesale.core.mapper.WhsShipmentPlanMapper;
import com.imhuso.wholesale.core.service.IWhsOrderProgressService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 订单进度查询Service实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsOrderProgressServiceImpl implements IWhsOrderProgressService {

    private final WhsShipmentPlanMapper shipmentPlanMapper;
    private final WhsShipmentPlanItemMapper shipmentPlanItemMapper;

    @Override
    public WhsOrderProgressVo getOrderProgress(Long id) {
        // 1. 查询订单已确定的计划
        LambdaQueryWrapper<WhsShipmentPlan> planQuery = new LambdaQueryWrapper<>();
        planQuery.eq(WhsShipmentPlan::getOrderId, id)
            .eq(WhsShipmentPlan::getStatus, ShipmentPlanStatus.EXECUTED.getValue());

        WhsShipmentPlan plan = shipmentPlanMapper.selectOne(planQuery);

        // 2. 如果没有直接返回空
        if (plan == null) {
            return null;
        }

        // 3. 查询计划项
        LambdaQueryWrapper<WhsShipmentPlanItem> itemQuery = new LambdaQueryWrapper<>();
        itemQuery.eq(WhsShipmentPlanItem::getPlanId, plan.getId())
            .orderByAsc(WhsShipmentPlanItem::getId);

        List<WhsShipmentPlanItem> planItems = shipmentPlanItemMapper.selectList(itemQuery);

        // 4. 将计划和计划项的内容转换为 WhsOrderProgressVo
        WhsOrderProgressVo progressVo = new WhsOrderProgressVo();
        progressVo.setPlanName(plan.getPlanName());

        // 转换计划项
        if (!planItems.isEmpty()) {
            List<WhsShipmentPlanItemVo> planItemVos = MapstructUtils.convert(planItems, WhsShipmentPlanItemVo.class);
            progressVo.setPlanItems(planItemVos);
        }

        return progressVo;
    }
}
