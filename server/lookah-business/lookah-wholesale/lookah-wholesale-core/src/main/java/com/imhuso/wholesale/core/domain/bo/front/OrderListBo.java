package com.imhuso.wholesale.core.domain.bo.front;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import lombok.Data;

/**
 * 订单列表查询参数
 */
@Data
public class OrderListBo {
    /**
     * 状态数组
     */
    private Integer[] status;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private Date from;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private Date to;

    /**
     * 订单号搜索（支持系统订单号和客户订单号）
     */
    private String orderNo;

    /**
     * 收货人邮箱
     */
    private String shippingEmail;

    /**
     * 收货人姓名
     */
    private String shippingName;

    /**
     * 收货人电话
     */
    private String shippingPhone;
}
