package com.imhuso.wholesale.core.service.impl;

import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.constant.MemberPermissionConstants;
import com.imhuso.wholesale.core.domain.WhsMember;
import com.imhuso.wholesale.core.domain.bo.front.MemberBo;
import com.imhuso.wholesale.core.domain.bo.front.MemberPasswordBo;
import com.imhuso.wholesale.core.domain.bo.front.MemberUpdateBo;
import com.imhuso.wholesale.core.domain.vo.front.MemberVo;
import com.imhuso.wholesale.core.exception.WhsException;
import com.imhuso.wholesale.core.satoken.WholesaleLoginHelper;
import com.imhuso.wholesale.core.utils.MemberPermissionHelper;
import com.imhuso.wholesale.core.mapper.WhsMemberMapper;
import com.imhuso.wholesale.core.service.IWhsMemberService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Optional;

/**
 * 商店客户(whs_member)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-11 16:58:30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsMemberServiceImpl implements IWhsMemberService {

    private final WhsMemberMapper baseMapper;
    private final MemberPermissionHelper permissionHelper;

    /**
     * 通过用户名查询会员
     */
    @Override
    public WhsMember getMemberByUsername(String username) {
        MemberBo bo = new MemberBo();
        bo.setEmail(username);
        return baseMapper.selectOne(buildQueryWrapper(bo));
    }

    /**
     * 获取会员信息
     */
    @Override
    public MemberVo getMemberInfo(Long userId) {
        MemberVo memberVo = baseMapper.selectVoById(userId, MemberVo.class);
        if (memberVo != null) {
            // 填充权限信息
            memberVo.setShowPrice(permissionHelper.hasPermission(userId, MemberPermissionConstants.SHOW_PRICE));
            memberVo.setShowStock(permissionHelper.hasPermission(userId, MemberPermissionConstants.SHOW_STOCK));
            memberVo.setAllowOrder(permissionHelper.hasPermission(userId, MemberPermissionConstants.ALLOW_ORDER));
        }
        return memberVo;
    }

    /**
     * 修改密码
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePassword(MemberPasswordBo bo) {
        Long userId = WholesaleLoginHelper.getUserId();

        // 只查询需要的字段，提高性能
        WhsMember member = Optional.ofNullable(baseMapper.selectOne(new LambdaQueryWrapper<WhsMember>().select(WhsMember::getPassword).eq(WhsMember::getId, userId))).orElseThrow(() -> new WhsException("wholesale.member.not.exists"));

        if (!BCrypt.checkpw(bo.getOldPassword(), member.getPassword())) {
            throw new WhsException("wholesale.member.password.not.match");
        }

        // 直接更新密码字段，避免不必要的对象转换
        WhsMember updateEntity = new WhsMember();
        updateEntity.setId(userId);
        updateEntity.setPassword(BCrypt.hashpw(bo.getNewPassword()));

        if (baseMapper.updateById(updateEntity) <= 0) {
            throw new WhsException("wholesale.member.password.change.error");
        }

        // 记录操作日志
        log.info("用户[{}]修改了密码", userId);
    }

    @Override
    public void updateLoginInfo(Long userId, String ip, String location) {
        WhsMember member = new WhsMember();
        member.setId(userId);
        member.setLoginIp(ip);
        member.setLoginDate(new Date());
        member.setLoginLocation(location);
        baseMapper.updateById(member);
    }

    /**
     * 更新基本资料
     */
    @Override
    public void updateProfile(MemberUpdateBo bo) {
        Long userId = WholesaleLoginHelper.getUserId();

        WhsMember updateMember = MapstructUtils.convert(bo, WhsMember.class);
        updateMember.setId(userId);

        if (baseMapper.updateById(updateMember) <= 0) {
            throw new WhsException("wholesale.member.profile.update.error");
        }
    }

    /**
     * 构建查询条件
     *
     * @param bo 查询参数
     * @return 查询条件
     */
    private LambdaQueryWrapper<WhsMember> buildQueryWrapper(MemberBo bo) {
        LambdaQueryWrapper<WhsMember> lqw = new LambdaQueryWrapper<>();

        if (bo == null) {
            return lqw;
        }

        // 按ID查询
        if (bo.getId() != null) {
            lqw.eq(WhsMember::getId, bo.getId());
        }

        // 按邮箱查询
        if (StringUtils.isNotBlank(bo.getEmail())) {
            lqw.eq(WhsMember::getEmail, bo.getEmail());
        }

        // 按状态查询
        if (StringUtils.isNotBlank(bo.getStatus())) {
            lqw.eq(WhsMember::getStatus, bo.getStatus());
        }

        return lqw;
    }
}
