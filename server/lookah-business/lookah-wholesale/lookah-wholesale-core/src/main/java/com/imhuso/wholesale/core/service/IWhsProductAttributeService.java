package com.imhuso.wholesale.core.service;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductAttributeBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductAttributeVo;

import java.util.List;
import java.util.Map;

/**
 * 产品属性Service接口
 *
 * <AUTHOR>
 */
public interface IWhsProductAttributeService {

    /**
     * 查询产品属性列表
     *
     * @param bo 查询条件
     * @return 产品属性列表
     */
    List<WhsProductAttributeVo> queryList(WhsProductAttributeBo bo);

    /**
     * 分页查询产品属性列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品属性分页列表
     */
    TableDataInfo<WhsProductAttributeVo> queryPage(WhsProductAttributeBo bo, PageQuery pageQuery);

    /**
     * 根据ID查询产品属性
     *
     * @param id 属性ID
     * @return 产品属性
     */
    WhsProductAttributeVo getById(Long id);

    /**
     * 新增产品属性
     *
     * @param bo 产品属性信息
     * @return 结果
     */
    int insertAttribute(WhsProductAttributeBo bo);

    /**
     * 修改产品属性
     *
     * @param bo 产品属性信息
     * @return 结果
     */
    int updateAttribute(WhsProductAttributeBo bo);

    /**
     * 删除产品属性
     *
     * @param id 需要删除的属性ID数组
     * @return 结果
     */
    int deleteAttributeById(Long id);

    /**
     * 根据ID列表获取属性映射，可选择是否填充属性值
     *
     * @param ids        属性ID列表
     * @param fillValues 是否填充属性值
     * @return 属性ID到属性的映射
     */
    Map<Long, WhsProductAttributeVo> getAttributeMap(List<Long> ids, boolean fillValues);
}
