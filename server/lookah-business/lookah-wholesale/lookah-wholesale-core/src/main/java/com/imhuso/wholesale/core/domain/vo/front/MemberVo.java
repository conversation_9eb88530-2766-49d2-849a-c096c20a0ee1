package com.imhuso.wholesale.core.domain.vo.front;

import com.imhuso.wholesale.core.domain.WhsMember;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 商店会员信息视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsMember.class)
public class MemberVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 476358114768657083L;

    /**
     * 会员ID
     */
    private Long id;

    /**
     * 会员邮箱
     */
    private String email;

    /**
     * 名字
     */
    private String firstName;

    /**
     * 姓氏
     */
    private String lastName;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 是否有显示价格权限
     */
    private Boolean showPrice;

    /**
     * 是否有显示库存权限
     */
    private Boolean showStock;

    /**
     * 是否有下单权限
     */
    private Boolean allowOrder;

    /**
     * 公司类型（wholesale,retail,chain,cash_carry）
     */
    private String companyType;

    /**
     * 店面数量
     */
    private Integer storeCount;

    /**
     * 客户来源
     */
    private String customerSource;

    /**
     * 公司名称
     */
    private String companyName;
}
