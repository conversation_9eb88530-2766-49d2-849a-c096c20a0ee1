package com.imhuso.wholesale.core.listener;

import com.imhuso.wholesale.core.service.IWhsStockCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 库存缓存初始化监听器
 * <p>
 * 在应用启动完成后自动预加载所有商品库存到Redis缓存
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Order(100)
@RequiredArgsConstructor
public class StockCacheInitListener implements ApplicationRunner {

    private final IWhsStockCacheService whsStockCacheService;

    @Override
    public void run(ApplicationArguments args) {
        log.info("应用启动完成，开始预加载所有商品库存到缓存...");
        try {
            // 直接调用Service层方法，避免通过Job间接调用
            whsStockCacheService.reloadAllProductStocksCache();
            log.info("商品库存预加载完成");
        } catch (Exception e) {
            // 记录错误但不阻止应用启动
            log.error("商品库存预加载失败，将在首次访问时加载: {}", e.getMessage(), e);
        }
    }
}
