package com.imhuso.wholesale.core.service;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsWarehouseLogisticsMethodBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsWarehouseLogisticsMethodVo;

import java.util.List;

/**
 * 仓库物流方式服务接口
 *
 * <AUTHOR>
 */
public interface IWhsWarehouseLogisticsMethodService {

    /**
     * 查询仓库物流方式分页列表
     *
     * @param bo        查询参数
     * @param pageQuery 分页参数
     * @return 分页列表
     */
    TableDataInfo<WhsWarehouseLogisticsMethodVo> queryPageList(WhsWarehouseLogisticsMethodBo bo, PageQuery pageQuery);

    /**
     * 查询仓库物流方式列表
     *
     * @param bo 查询参数
     * @return 列表
     */
    List<WhsWarehouseLogisticsMethodVo> queryList(WhsWarehouseLogisticsMethodBo bo);

    /**
     * 根据ID获取详情
     *
     * @param id ID
     * @return 详情
     */
    WhsWarehouseLogisticsMethodVo getById(Long id);

    /**
     * 根据仓库ID查询物流方式
     *
     * @param warehouseId 仓库ID
     * @return 物流方式列表
     */
    List<WhsWarehouseLogisticsMethodVo> queryByWarehouseId(Long warehouseId);

    /**
     * 同步海外仓物流渠道
     *
     * @param warehouseId 仓库ID
     * @return 同步结果
     */
    Boolean syncLogisticsChannels(Long warehouseId);
}
