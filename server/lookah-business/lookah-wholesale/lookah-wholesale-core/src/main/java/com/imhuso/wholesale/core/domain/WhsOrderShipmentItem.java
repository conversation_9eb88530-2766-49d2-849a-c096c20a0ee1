package com.imhuso.wholesale.core.domain;

import java.io.Serial;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.common.mybatis.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单发货项对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_order_shipment_item")
public class WhsOrderShipmentItem extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    @TableId
    private Long id;

    /**
     * 发货ID
     */
    private Long shipmentId;

    /**
     * 物流包裹ID
     */
    private Long packageId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 关联的发货方案项ID
     */
    private Long planItemId;

    /**
     * 发货数量
     */
    private Integer quantity;

    /**
     * 仓库ID
     */
    private Long warehouseId;
}
