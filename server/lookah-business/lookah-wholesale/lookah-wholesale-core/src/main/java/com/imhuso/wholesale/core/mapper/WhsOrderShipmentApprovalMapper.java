package com.imhuso.wholesale.core.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.mybatis.core.mapper.BaseMapperPlus;
import com.imhuso.wholesale.core.domain.WhsOrderShipmentApproval;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentApprovalVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单发货审批Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface WhsOrderShipmentApprovalMapper extends BaseMapperPlus<WhsOrderShipmentApproval, WhsOrderShipmentApprovalVo> {

    /**
     * 分页查询审批记录，包含订单号
     */
    Page<WhsOrderShipmentApprovalVo> selectVoPageWithOrderNo(Page<WhsOrderShipmentApprovalVo> page, @Param("ew") Wrapper<WhsOrderShipmentApproval> queryWrapper);

    /**
     * 单条查询审批记录，包含订单号
     */
    WhsOrderShipmentApprovalVo selectVoOneWithOrderNo(@Param("ew") Wrapper<WhsOrderShipmentApproval> queryWrapper);

    /**
     * 列表查询审批记录，包含订单号
     */
    List<WhsOrderShipmentApprovalVo> selectVoListWithOrderNo(@Param("ew") Wrapper<WhsOrderShipmentApproval> queryWrapper);

}
