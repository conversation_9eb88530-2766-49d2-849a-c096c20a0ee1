package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.wholesale.core.domain.WhsMember;
import com.imhuso.wholesale.core.validate.ValidCustomerName;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会员管理业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WhsMember.class)
@ValidCustomerName(groups = {AddGroup.class, EditGroup.class})
public class WhsMemberBo extends BaseEntity {
    /**
     * 会员ID
     */
    @NotNull(message = "wholesale.admin.member.id.not.null", groups = {EditGroup.class})
    private Long id;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 销售代表ID，关联sys_user表
     */
    private Long salespersonId;

    /**
     * 会员邮箱
     */
    @NotBlank(message = "wholesale.admin.member.email.not.blank", groups = {AddGroup.class, EditGroup.class})
    @Email(message = "wholesale.admin.member.email.format", groups = {AddGroup.class, EditGroup.class})
    private String email;

    /**
     * 电话号码
     */
    @Pattern(regexp = "^\\+?[0-9\\-\\s()]+$", message = "wholesale.admin.member.phone.format", groups = {AddGroup.class, EditGroup.class})
    private String phone;

    /**
     * EIN税号
     */
    private String ein;

    /**
     * 会员密码（创建客户时由后端自动生成，可为空）
     */
    private String password;

    /**
     * 名字
     */
    @NotBlank(message = "wholesale.admin.member.first.name.not.blank", groups = {AddGroup.class, EditGroup.class})
    private String firstName;

    /**
     * 姓氏
     */
    @NotBlank(message = "wholesale.admin.member.last.name.not.blank", groups = {AddGroup.class, EditGroup.class})
    private String lastName;

    /**
     * 帐号状态（0停用 1正常）
     */
    private String status;

    /**
     * 公司类型（wholesale,retail,chain,cash_carry）
     */
    private String companyType;

    /**
     * 店面数量
     */
    private Integer storeCount;

    /**
     * 客户来源
     */
    private String customerSource;

    /**
     * 自定义客户来源（当customerSource为others时使用）
     */
    private String customSource;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 自定义公司类型（当companyType为others时使用）
     */
    private String customCompanyType;

    /**
     * 备注
     */
    private String remark;

    // ========== 收货地址相关字段 ==========

    /**
     * 收货地址 - 公司名称
     */
    private String shippingCompanyName;

    /**
     * 收货地址 - 名字
     */
    private String shippingFirstName;

    /**
     * 收货地址 - 姓氏
     */
    private String shippingLastName;

    /**
     * 收货地址 - 电话
     */
    private String shippingPhone;

    /**
     * 收货地址 - 邮箱
     */
    private String shippingEmail;

    /**
     * 收货地址 - 国家
     */
    private String shippingCountry;

    /**
     * 收货地址 - 州/省
     */
    private String shippingState;

    /**
     * 收货地址 - 城市
     */
    private String shippingCity;

    /**
     * 收货地址 - 地址行1
     */
    private String shippingAddressLine1;

    /**
     * 收货地址 - 地址行2
     */
    private String shippingAddressLine2;

    /**
     * 收货地址 - 邮政编码
     */
    private String shippingZipCode;
}
