package com.imhuso.wholesale.core.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * ERP API响应基础结构
 *
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ErpApiResponse<T> {

    /**
     * 响应码，1000表示成功
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return code != null && code == 1000;
    }
}
