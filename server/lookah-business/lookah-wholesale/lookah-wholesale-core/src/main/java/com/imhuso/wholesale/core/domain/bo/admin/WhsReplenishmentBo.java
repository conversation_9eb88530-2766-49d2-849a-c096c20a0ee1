package com.imhuso.wholesale.core.domain.bo.admin;

import lombok.Data;

/**
 * 补货单查询业务对象
 *
 * <AUTHOR>
 */
@Data
public class WhsReplenishmentBo {

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * SKU编码（模糊查询）
     */
    private String skuCode;

    /**
     * 产品名称（模糊查询）
     */
    private String productName;

    /**
     * 是否只显示需要补货的商品
     * true: 只显示可用库存小于告警库存的商品
     * false: 显示所有商品
     */
    private Boolean onlyNeedReplenishment;
}
