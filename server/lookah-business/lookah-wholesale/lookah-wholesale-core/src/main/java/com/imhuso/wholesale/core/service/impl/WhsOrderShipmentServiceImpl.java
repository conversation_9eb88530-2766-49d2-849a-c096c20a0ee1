package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.satoken.utils.LoginHelper;
import com.imhuso.wholesale.core.constant.WholesaleConstants;
import com.imhuso.wholesale.core.domain.*;
import com.imhuso.wholesale.core.domain.bo.admin.WarehouseSelectionBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderShipmentBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderShipmentItemBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderShipmentPlanItemBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentConversionVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanItemVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanVo;
import com.imhuso.wholesale.core.domain.vo.warehouse.WarehouseShipmentRecord;
import com.imhuso.wholesale.core.enums.OrderStatus;
import com.imhuso.wholesale.core.enums.PackageStatus;
import com.imhuso.wholesale.core.enums.ShipmentPlanStatus;
import com.imhuso.wholesale.core.enums.ShipmentStatus;
import com.imhuso.wholesale.core.mapper.*;
import com.imhuso.wholesale.core.service.*;
import com.imhuso.wholesale.core.event.WhsOrderEvent;
import com.imhuso.wholesale.core.event.WhsOrderEvent.WhsOrderEventType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单发货Service实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsOrderShipmentServiceImpl implements IWhsOrderShipmentService {

    private final IWhsShipmentPlanGenerationService shipmentPlanGenerationService;
    private final IWhsShipmentPlanService shipmentPlanService;
    private final IShipmentValidationService shipmentValidationService;
    private final IWhsOrderService orderService;
    private final IWhsOrderStatusService orderStatusService;
    private final IWhsShipmentPlanItemService shipmentPlanItemService;
    private final WhsOrderShipmentMapper shipmentMapper;
    private final WhsOrderShipmentItemMapper shipmentItemMapper;
    private final WhsShipmentPlanItemMapper shipmentPlanItemMapper;
    private final WhsLogisticsPackageMapper logisticsPackageMapper;
    private final IWhsOverseasShipmentProcessingService overseasShipmentProcessingService;
    private final IWhsShipmentStockProcessingService shipmentStockProcessingService;
    private final IOverseasShipmentCancellationService overseasShipmentCancellationService;
    private final WhsShipmentPlanMapper shipmentPlanMapper;
    private final IWhsShipmentConversionService shipmentConversionService;
    private final ApplicationEventPublisher eventPublisher;
    private final WhsWarehouseMapper warehouseMapper;
    private final WhsOverseasWarehouseAccountMapper overseasWarehouseAccountMapper;
    private final IWhsOrderShipmentApprovalService shipmentApprovalService;

    /**
     * 获取或生成订单发货方案列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<WhsShipmentPlanVo> getOrGenerateShipmentPlans(Long orderId, Boolean forceRenew) {
        if (orderId == null) {
            return new ArrayList<>();
        }

        // 1. 如果不是强制重新生成，先查询现有方案
        if (!Boolean.TRUE.equals(forceRenew)) {
            // 使用优化后的服务查询现有方案
            List<WhsShipmentPlanVo> existingPlans = shipmentPlanService.getShipmentPlansByOrderId(orderId);
            if (CollUtil.isNotEmpty(existingPlans)) {
                log.debug("订单 {} 已存在发货方案，直接返回", orderId);

                // 检查并自动选择执行中的方案
                autoSelectExecutedPlanIfNeeded(orderId, existingPlans);

                // 对方案进行排序: 已选择 > 自定义 > 推荐 > 分数
                sortShipmentPlans(existingPlans);
                return existingPlans;
            }
        } else {
            log.info("强制重新生成订单 {} 的发货方案，同时将重新记录包装转换", orderId);
        }

        // 2. 调用优化后的方案生成服务生成新方案
        shipmentPlanGenerationService.generateShipmentPlans(orderId);

        // 3. 获取生成的方案列表
        List<WhsShipmentPlanVo> generatedPlans = shipmentPlanService.getShipmentPlansByOrderId(orderId);

        // 生成的方案也需要检查自动选择和排序
        if (CollUtil.isNotEmpty(generatedPlans)) {
            // 检查并自动选择执行中的方案
            autoSelectExecutedPlanIfNeeded(orderId, generatedPlans);

            sortShipmentPlans(generatedPlans);
        }

        return generatedPlans;
    }

    /**
     * 对发货方案进行排序
     * 排序规则: 已选择方案 > 自定义方案 > 推荐方案 > 按分数降序
     */
    private void sortShipmentPlans(List<WhsShipmentPlanVo> plans) {
        plans.sort((p1, p2) -> {
            // 1. 已选择方案排在第一位
            boolean p1IsSelected = Boolean.TRUE.equals(p1.getIsSelected());
            boolean p2IsSelected = Boolean.TRUE.equals(p2.getIsSelected());

            if (p1IsSelected && !p2IsSelected) {
                return -1;
            }
            if (!p1IsSelected && p2IsSelected) {
                return 1;
            }

            // 2. 自定义方案排在第二位（通过固定规则判断）
            boolean p1IsCustom = isCustomPlan(p1);
            boolean p2IsCustom = isCustomPlan(p2);

            if (p1IsCustom && !p2IsCustom) {
                return -1;
            }
            if (!p1IsCustom && p2IsCustom) {
                return 1;
            }

            // 3. 推荐方案排在第三位
            if (Boolean.TRUE.equals(p1.getIsRecommended()) && !Boolean.TRUE.equals(p2.getIsRecommended())) {
                return -1;
            }
            if (!Boolean.TRUE.equals(p1.getIsRecommended()) && Boolean.TRUE.equals(p2.getIsRecommended())) {
                return 1;
            }

            // 4. 最后按分数降序排列
            BigDecimal score1 = p1.getScore() != null ? p1.getScore() : BigDecimal.ZERO;
            BigDecimal score2 = p2.getScore() != null ? p2.getScore() : BigDecimal.ZERO;

            return score2.compareTo(score1); // 降序排列
        });
    }

    /**
     * 判断方案是否为自定义方案
     */
    private boolean isCustomPlan(WhsShipmentPlanVo plan) {
        if (plan == null) {
            return false;
        }
        return Boolean.TRUE.equals(plan.getIsCustom());
    }

    /**
     * 检查并自动选择执行中的方案
     * 如果只有一个方案且状态为执行中，则自动选择该方案
     */
    private void autoSelectExecutedPlanIfNeeded(Long orderId, List<WhsShipmentPlanVo> plans) {
        if (CollUtil.isEmpty(plans)) {
            return;
        }

        // 检查是否已有选中的方案
        boolean hasSelectedPlan = plans.stream().anyMatch(plan -> Boolean.TRUE.equals(plan.getIsSelected()));
        if (hasSelectedPlan) {
            log.debug("订单 {} 已有选中的发货方案，跳过自动选择", orderId);
            return;
        }

        // 筛选出执行中的方案
        List<WhsShipmentPlanVo> executedPlans = plans.stream()
            .filter(plan -> ShipmentPlanStatus.EXECUTED.getValue().equals(plan.getStatus()))
            .toList();

        // 如果只有一个执行中的方案，自动选择它
        if (executedPlans.size() == 1) {
            WhsShipmentPlanVo executedPlan = executedPlans.get(0);
            try {
                shipmentPlanService.selectShipmentPlan(executedPlan.getId());
                // 更新内存中的状态，避免重新查询数据库
                executedPlan.setIsSelected(true);
                log.info("订单 {} 自动选择了唯一的执行中发货方案 {}", orderId, executedPlan.getId());
            } catch (Exception e) {
                log.error("订单 {} 自动选择执行中发货方案 {} 失败: {}", orderId, executedPlan.getId(), e.getMessage());
            }
        } else if (executedPlans.size() > 1) {
            log.debug("订单 {} 有多个执行中的发货方案，需要手动选择", orderId);
        }
    }

    /**
     * 选择发货方案
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean selectShipmentPlan(Long planId) {
        if (planId == null) {
            throw new ServiceException("发货方案ID不能为空");
        }
        return shipmentPlanService.selectShipmentPlan(planId);
    }

    /**
     * 预览发货仓库分配
     */
    @Override
    public Boolean previewWarehouseAssignment(WarehouseSelectionBo selectionBo) {
        return shipmentValidationService.previewAndValidateWarehouseAssignment(selectionBo);
    }

    /**
     * 确认发货
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean confirmShipment(WhsOrderShipmentBo shipmentBo) {
        // 1. 参数验证与上下文准备
        ShipmentContext context = validateShipmentParameters(shipmentBo);
        Long orderId = context.getOrderId();
        Long planId = context.getPlanId();
        List<WhsOrderShipmentItemBo> shipmentItems = context.getShipmentItems();

        // 跟踪已成功创建的海外仓订单，以便在失败时尝试取消
        List<WarehouseShipmentRecord> successfulWarehouseShipments = new ArrayList<>();

        try {
            // 2. 验证订单状态
            WhsOrderVo order = validateOrderStatus(orderId);

            // 2.5. 验证发货审批状态
            validateShipmentApproval(orderId);

            // 3. 验证发货方案状态和内容
            ShipmentPlanContext planContext = validateShipmentPlan(orderId, planId, shipmentItems);
            List<WhsShipmentPlanItemVo> planItems = planContext.getPlanItems();
            Map<Long, WhsShipmentPlanItemVo> planItemMap = planContext.getPlanItemMap();

            // 4. 创建发货主记录 (需要在处理库存和海外仓之前创建以获取 shipmentId)
            WhsOrderShipment shipmentRecord = createShipmentRecord(orderId, shipmentItems);
            Long shipmentId = shipmentRecord.getId();
            Integer currentBatchNumber = shipmentRecord.getBatchNumber(); // 获取批次号

            // 5. 创建物流包裹
            Map<String, Long> packageIdMap = createLogisticsPackages(orderId, shipmentItems, shipmentId);

            // 6. 创建发货项记录
            List<WhsOrderShipmentItem> shipmentItemEntities = createShipmentItems(shipmentId, orderId, shipmentItems,
                packageIdMap);

            // 7. 更新发货方案项的已发货数量
            updatePlanItemStatus(shipmentItems, planItemMap);

            // 获取发货转换记录，用于处理原始变体和转换后变体的库存
            List<WhsShipmentConversionVo> conversions = shipmentConversionService.getConversionsByPlanId(planId);

            // 根据发货项分类处理
            // 区分需要处理库存的项和本地仓发货项
            List<WhsOrderShipmentItem> nonLocalShipmentItems = shipmentItemEntities.stream()
                .filter(item -> !WholesaleConstants.LOCAL_WAREHOUSE_ID.equals(item.getWarehouseId()))
                .collect(Collectors.toList());

            // 8. 处理库存扣减和释放
            // 注意：无论是否为本地仓，都需要处理锁定库存的释放，但本地仓跳过库存校验和海外仓处理
            if (CollUtil.isNotEmpty(nonLocalShipmentItems)) {
                log.info("订单 {} 处理 {} 个非本地仓发货项的库存", orderId, nonLocalShipmentItems.size());
                // 对于非本地仓项目，进行完整的库存处理
                shipmentStockProcessingService.processStockForShipment(orderId, shipmentItemEntities, planItemMap,
                    planItems, conversions);
            } else {
                log.info("订单 {} 全部采用本地仓发货，仅处理库存释放", orderId);
                // 对于全部本地仓项目，仅处理库存释放
                shipmentStockProcessingService.processStockForShipment(orderId, shipmentItemEntities, planItemMap,
                    planItems, conversions);
            }

            // 9. 处理海外仓发货 - 只处理非本地仓的发货
            if (CollUtil.isNotEmpty(nonLocalShipmentItems)) {
                // 要过滤出非本地仓的发货项来处理海外仓
                List<WhsOrderShipmentItemBo> nonLocalBos = shipmentItems.stream()
                    .filter(item -> !WholesaleConstants.LOCAL_WAREHOUSE_ID.equals(item.getWarehouseId()))
                    .collect(Collectors.toList());

                if (CollUtil.isNotEmpty(nonLocalBos)) {
                    log.info("订单 {} 处理 {} 个非本地仓发货项的海外仓发货", orderId, nonLocalBos.size());
                    processWarehouseRecords(successfulWarehouseShipments, orderId, order, packageIdMap, nonLocalBos,
                        planItemMap, currentBatchNumber);
                }
            } else {
                log.info("订单 {} 全部采用本地仓发货，跳过海外仓处理", orderId);
            }

            // 10. 更新订单和方案状态
            updateOrderStatus(orderId, planId, shipmentId, planItems, shipmentItems, currentBatchNumber);

            // 11. 标记其他方案为不可用 (需要 shipmentPlanService)
            shipmentPlanService.markOtherPlansAsUnavailable(orderId, planId);

            log.info("订单 {} 发货确认成功，发货单ID: {}", orderId, shipmentId);
            return true;

        } catch (Exception e) {
            log.error("订单[{}]发货确认过程中发生异常，尝试回滚海外仓操作", orderId, e);
            tryRollbackOverseasShipments(successfulWarehouseShipments);
            if (e instanceof ServiceException) {
                throw (ServiceException) e;
            } else {
                throw new ServiceException("确认发货失败: " + e.getMessage());
            }
        }
    }

    /**
     * 验证发货请求参数
     */
    private ShipmentContext validateShipmentParameters(WhsOrderShipmentBo shipmentBo) {
        if (shipmentBo == null) {
            throw new ServiceException("发货请求不能为空");
        }
        if (shipmentBo.getOrderId() == null) {
            throw new ServiceException("订单ID不能为空");
        }
        if (shipmentBo.getPlanId() == null) {
            throw new ServiceException("方案ID不能为空");
        }
        if (CollUtil.isEmpty(shipmentBo.getItems())) {
            throw new ServiceException("发货项列表不能为空");
        }

        Long orderId = shipmentBo.getOrderId();
        Long planId = shipmentBo.getPlanId();
        List<WhsOrderShipmentItemBo> shipmentItems = shipmentBo.getItems();

        log.info("开始处理订单 {} 的确认发货请求，发货方案ID: {}", orderId, planId);

        return new ShipmentContext(orderId, planId, shipmentItems);
    }

    /**
     * 验证订单状态是否允许发货
     */
    private WhsOrderVo validateOrderStatus(Long orderId) {
        WhsOrderVo order = orderService.getOrderById(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        Integer orderStatusValue = null;
        try {
            if (order.getOrderStatus() != null) {
                orderStatusValue = Integer.parseInt(order.getOrderStatus());
            }
        } catch (NumberFormatException e) {
            log.warn("无法解析订单VO中的订单状态: {}", order.getOrderStatus());
            throw new ServiceException("订单状态格式错误");
        }

        if (orderStatusValue == null || !(orderStatusValue.equals(OrderStatus.PENDING.getValue())
            || orderStatusValue.equals(OrderStatus.PROCESSING.getValue()))) {
            String statusText = order.getOrderStatusText() != null ? order.getOrderStatusText()
                : String.valueOf(orderStatusValue);
            throw new ServiceException("订单状态不允许发货: " + statusText);
        }

        Integer shipmentStatusValue = null;
        try {
            if (order.getShipmentStatus() != null) {
                shipmentStatusValue = Integer.parseInt(order.getShipmentStatus());
            }
        } catch (NumberFormatException e) {
            log.warn("无法解析订单VO中的发货状态: {}", order.getShipmentStatus());
        }

        if (shipmentStatusValue != null && (shipmentStatusValue.equals(ShipmentStatus.SHIPPED.getValue())
            || shipmentStatusValue.equals(ShipmentStatus.DELIVERED.getValue()))) {
            String statusText = order.getShipmentStatusText() != null ? order.getShipmentStatusText()
                : String.valueOf(shipmentStatusValue);
            throw new ServiceException("订单已发货或已送达，不能再次操作: " + statusText);
        }

        return order;
    }

    /**
     * 验证发货方案状态和内容
     */
    private ShipmentPlanContext validateShipmentPlan(Long orderId, Long planId,
                                                     List<WhsOrderShipmentItemBo> shipmentItems) {
        WhsShipmentPlan plan = shipmentPlanMapper.selectById(planId);
        if (plan == null || !plan.getOrderId().equals(orderId)) {
            throw new ServiceException("发货方案不存在或不属于该订单");
        }

        // 发货方案只要不是失效即可发货
        if (plan.getStatus().equals(ShipmentPlanStatus.UNAVAILABLE.getValue())) {
            throw new ServiceException("发货已失效无法发货");
        }

        List<WhsShipmentPlanItemVo> planItems = shipmentPlanItemService
            .getShipmentPlanItemsByPlanIds(Collections.singletonList(planId));
        if (CollUtil.isEmpty(planItems)) {
            throw new ServiceException("发货方案项为空，无法确认发货");
        }

        Map<Long, WhsShipmentPlanItemVo> planItemMap = planItems.stream()
            .collect(Collectors.toMap(WhsShipmentPlanItemVo::getId, item -> item));

        // 验证新的按仓库汇总的数据结构
        for (WhsOrderShipmentItemBo warehouseItem : shipmentItems) {
            // 验证仓库级别的数据
            if (warehouseItem.getWarehouseId() == null) {
                throw new ServiceException("仓库ID不能为空");
            }
            if (warehouseItem.getLogisticsMethodId() == null) {
                throw new ServiceException("物流方式ID不能为空");
            }
            if (warehouseItem.getPieceCount() == null || warehouseItem.getPieceCount() <= 0) {
                throw new ServiceException("包裹数量必须大于0");
            }
            if (CollUtil.isEmpty(warehouseItem.getPlanItems())) {
                throw new ServiceException("发货方案项列表不能为空");
            }

            // 验证每个方案项
            for (WhsOrderShipmentPlanItemBo planItemBo : warehouseItem.getPlanItems()) {
                if (planItemBo.getPlanItemId() == null) {
                    throw new ServiceException("发货方案项ID不能为空");
                }
                if (planItemBo.getQuantity() == null || planItemBo.getQuantity() <= 0) {
                    throw new ServiceException("发货数量必须大于0");
                }

                WhsShipmentPlanItemVo planItem = planItemMap.get(planItemBo.getPlanItemId());
                if (planItem == null) {
                    throw new ServiceException("发货请求项对应的方案项不存在: ID " + planItemBo.getPlanItemId());
                }

                int remainingQuantity = planItem.getQuantity()
                    - (planItem.getShippedQuantity() != null ? planItem.getShippedQuantity() : 0);
                if (planItemBo.getQuantity() > remainingQuantity) {
                    throw new ServiceException(String.format("SKU [%s] 发货数量 [%d] 超过方案剩余可发数量 [%d]",
                        planItem.getSkuCode(), planItemBo.getQuantity(), remainingQuantity));
                }
            }
        }

        return new ShipmentPlanContext(planItems, planItemMap);
    }

    @lombok.Data
    @lombok.AllArgsConstructor
    private static class ShipmentContext {
        private Long orderId;
        private Long planId;
        private List<WhsOrderShipmentItemBo> shipmentItems;
    }

    @lombok.Data
    @lombok.AllArgsConstructor
    private static class ShipmentPlanContext {
        private List<WhsShipmentPlanItemVo> planItems;
        private Map<Long, WhsShipmentPlanItemVo> planItemMap;
    }

    /**
     * 更新订单状态、方案状态和相关记录
     */
    private void updateOrderStatus(Long orderId, Long planId, Long shipmentId, List<WhsShipmentPlanItemVo> planItems,
                                   List<WhsOrderShipmentItemBo> shipmentItems, Integer batchNumber) {
        boolean allItemsShipped = checkAllPlanItemsShipped(planItems, shipmentItems);

        // 不再将订单状态设置为完成，只设为处理中
        OrderStatus newOrderStatus = OrderStatus.PROCESSING;

        // 获取当前订单状态，用于比较
        WhsOrderVo existingOrder = orderService.getOrderById(orderId);
        if (existingOrder != null) {
            log.info("订单当前状态: orderStatus={}, shipmentStatus={}, paymentStatus={}, invoiceStatus={}",
                existingOrder.getOrderStatus(), existingOrder.getShipmentStatus(), existingOrder.getPaymentStatus(),
                existingOrder.getInvoiceStatus());
        }

        // 判断是否为首次发货
        boolean isFirstShipment = existingOrder != null &&
            (ShipmentStatus.PENDING.getValue().equals(Integer.parseInt(existingOrder.getShipmentStatus())) ||
                ShipmentStatus.PREPARING.getValue()
                    .equals(Integer.parseInt(existingOrder.getShipmentStatus())));

        // 根据是否全部发货设置不同的发货状态
        ShipmentStatus newShipmentStatus = allItemsShipped ? ShipmentStatus.SHIPPED : ShipmentStatus.PARTIAL_SHIPPED;

        // 构建详细的日志信息，包含发货明细
        StringBuilder logBuilder = new StringBuilder();
        if (isFirstShipment) {
            logBuilder.append("[首次发货] ");
        }

        if (allItemsShipped) {
            logBuilder.append("订单全部发货");
        } else {
            logBuilder.append("订单部分发货");
        }

        // 添加发货批次号、发货数量等信息到备注中
        logBuilder.append("，批次: ").append(batchNumber);
        logBuilder.append("，共发货 ").append(shipmentItems.size()).append(" 个项目");

        // 如果有仓库信息，添加到日志中, 如果仓库ID是0代表未指定仓库
        if (!shipmentItems.isEmpty() && shipmentItems.get(0).getWarehouseId() != null) {
            if (WholesaleConstants.LOCAL_WAREHOUSE_ID.equals(shipmentItems.get(0).getWarehouseId())) {
                logBuilder.append("，未指定仓库");
            } else {
                logBuilder.append("，仓库ID: ").append(shipmentItems.get(0).getWarehouseId());
            }
        }

        String logMessage = logBuilder.toString();

        log.info("即将更新订单状态: orderId={}, orderStatus={}, shipmentStatus={}, 日志消息={}",
            orderId, newOrderStatus.getValue(), newShipmentStatus.getValue(), logMessage);

        // 更新订单状态并记录日志，同时更新订单的发货状态
        orderStatusService.updateOrderStatus(orderId, newOrderStatus.getValue(), null, newShipmentStatus.getValue(),
            null, logMessage);

        // 获取更新后的订单状态，用于比较
        WhsOrderVo updatedOrder = orderService.getOrderById(orderId);
        if (updatedOrder != null) {
            log.info("订单更新后状态: orderStatus={}, shipmentStatus={}, paymentStatus={}, invoiceStatus={}",
                updatedOrder.getOrderStatus(), updatedOrder.getShipmentStatus(), updatedOrder.getPaymentStatus(),
                updatedOrder.getInvoiceStatus());
        }

        // 无论是否全部发货完成，都将当前方案状态设置为"已执行"
        shipmentPlanService.updatePlanStatus(planId, ShipmentPlanStatus.EXECUTED.getValue());

        // 根据是否全部发货记录不同的日志
        if (allItemsShipped) {
            log.info("订单[{}]的发货方案[{}]已全部执行完毕", orderId, planId);
        } else {
            log.info("订单[{}]的发货方案[{}]已开始执行（部分发货）", orderId, planId);
        }

        // 更新发货单的状态
        WhsOrderShipment shipmentUpdate = new WhsOrderShipment();
        shipmentUpdate.setId(shipmentId);
        shipmentUpdate.setShipmentStatus(newShipmentStatus.getValue());
        shipmentMapper.updateById(shipmentUpdate);

        // 记录发货状态更新
        log.info("订单[{}]发货状态已更新为: {}", orderId, newShipmentStatus.name());

        // 发布发货批次完成通知事件 - 确保每个批次都发送通知
        try {
            // 获取当前管理员用户ID
            Long userId = LoginHelper.getUserId();

            // 构建通知消息
            String notificationMessage = String.format("订单[%s]发货批次[%d]已完成发货",
                orderId, batchNumber);

            // 发布批次完成通知事件
            log.info("发布发货批次完成通知事件: {}", notificationMessage);

            // 根据是否全部发货发布不同的事件类型
            WhsOrderEventType eventType = allItemsShipped ?
                WhsOrderEventType.ORDER_SHIPPED :
                WhsOrderEventType.ORDER_PARTIAL_SHIPPED;

            log.info("订单[{}]即将发布{}事件，发货单ID: {}, 批次号: {}", 
                orderId, eventType, shipmentId, batchNumber);

            eventPublisher.publishEvent(new WhsOrderEvent(
                this,
                eventType,
                orderId,
                notificationMessage,
                userId
            ));
            
            log.info("订单[{}]{}事件发布成功", orderId, eventType);
        } catch (Exception e) {
            // 通知发送失败不影响主流程
            log.error("发布发货批次完成通知事件失败: {}", e.getMessage(), e);
        }

        // 注意：除了上面的批次通知外，WhsOrderStatusServiceImpl 也会在订单状态变更时发布事件
        // 这样可以确保每个批次都有通知，同时订单状态变更时也有通知
    }

    private void tryRollbackOverseasShipments(List<WarehouseShipmentRecord> successfulShipments) {
        rollbackSuccessfulWarehouseShipments(successfulShipments);
    }

    private void rollbackSuccessfulWarehouseShipments(List<WarehouseShipmentRecord> successfulShipments) {
        if (CollUtil.isEmpty(successfulShipments)) {
            return;
        }

        log.warn("检测到 {} 条已成功的海外仓发货记录，将为每条记录分派取消任务 (带重试)...", successfulShipments.size());

        for (WarehouseShipmentRecord record : successfulShipments) {
            if (record.getPackageId() == null) {
                log.error("无法分派取消任务，包裹ID为空，仓库ID: {}, 订单号: {}", record.getWarehouseId(), record.getOrderNo());
                continue;
            }
            if (StrUtil.isBlank(record.getExternalShipmentId())) {
                log.error("无法分派取消任务，外部发货单号(externalShipmentId)为空，仓库ID: {}, 包裹ID: {}, 订单号: {}", record.getWarehouseId(),
                    record.getPackageId(), record.getOrderNo());
                continue;
            }

            try {
                log.info("分派海外仓发货取消任务 - 仓库ID: {}, 包裹ID: {}, 外部单号: {}", record.getWarehouseId(), record.getPackageId(),
                    record.getExternalShipmentId());
                overseasShipmentCancellationService.attemptCancellation(record);
            } catch (Exception dispatchEx) {
                log.error("分派海外仓取消任务时发生意外异常 (非预期，可能 SnailJob AOP 未生效?)，仓库ID: {}, 包裹ID: {}, 错误: {}",
                    record.getWarehouseId(), record.getPackageId(), dispatchEx.getMessage(), dispatchEx);
            }
        }

        log.info("所有需要回滚的海外仓记录 ({} 条) 已全部分派取消任务 (最终结果请关注回调日志和企业微信通知).", successfulShipments.size());
    }

    private boolean checkAllPlanItemsShipped(List<WhsShipmentPlanItemVo> planItems,
                                             List<WhsOrderShipmentItemBo> shipmentItems) {
        if (CollUtil.isEmpty(planItems)) {
            return true; // 没有计划项，视为全部完成
        }

        // 从新的数据结构中计算当前发货数量
        Map<Long, Integer> currentShipmentQuantities = new HashMap<>();
        for (WhsOrderShipmentItemBo warehouseItem : shipmentItems) {
            if (CollUtil.isNotEmpty(warehouseItem.getPlanItems())) {
                for (WhsOrderShipmentPlanItemBo planItemBo : warehouseItem.getPlanItems()) {
                    if (planItemBo.getPlanItemId() != null && planItemBo.getQuantity() != null) {
                        currentShipmentQuantities.merge(planItemBo.getPlanItemId(), planItemBo.getQuantity(), Integer::sum);
                    }
                }
            }
        }

        return planItems.stream()
            .filter(planItem -> planItem != null && planItem.getId() != null && planItem.getQuantity() != null)
            .noneMatch(planItem -> {
                int required = planItem.getQuantity();
                int alreadyShipped = planItem.getShippedQuantity() != null ? planItem.getShippedQuantity() : 0;
                int currentlyShipping = currentShipmentQuantities.getOrDefault(planItem.getId(), 0);
                return alreadyShipped + currentlyShipping < required;
            });
    }

    private Integer calculateNextBatchNumber(Long orderId) {
        try {
            LambdaQueryWrapper<WhsOrderShipment> queryWrapper = Wrappers.lambdaQuery(WhsOrderShipment.class)
                .eq(WhsOrderShipment::getOrderId, orderId).select(WhsOrderShipment::getBatchNumber)
                .orderByDesc(WhsOrderShipment::getBatchNumber).last("LIMIT 1");

            WhsOrderShipment latestShipment = shipmentMapper.selectOne(queryWrapper);

            if (latestShipment != null && latestShipment.getBatchNumber() != null) {
                return latestShipment.getBatchNumber() + 1;
            }
        } catch (Exception e) {
            log.error("查询订单 {} 最大批次号时发生错误: {}", orderId, e.getMessage());
        }

        return 1; // 如果没有找到记录或发生错误，返回初始批次号1
    }

    private WhsOrderShipment createShipmentRecord(Long orderId, List<WhsOrderShipmentItemBo> shipmentItems) {
        WhsOrderShipment shipment = new WhsOrderShipment();
        shipment.setOrderId(orderId);
        shipment.setShipmentStatus(ShipmentStatus.PREPARING.getValue());
        shipment.setShippedDate(new Date());
        shipment.setBatchNumber(calculateNextBatchNumber(orderId));
        shipment.setWarehouseId(shipmentItems.isEmpty() || shipmentItems.get(0).getWarehouseId() == null
            ? WholesaleConstants.LOCAL_WAREHOUSE_ID
            : shipmentItems.get(0).getWarehouseId());

        int result = shipmentMapper.insert(shipment);
        if (result <= 0 || shipment.getId() == null) {
            throw new ServiceException("创建发货记录失败，未能获取到发货ID");
        }

        log.debug("成功创建发货记录：ID={}, 批次号={}", shipment.getId(), shipment.getBatchNumber());
        return shipment;
    }

    private Map<String, Long> createLogisticsPackages(Long orderId, List<WhsOrderShipmentItemBo> shipmentItems,
                                                      Long shipmentId) {
        Map<String, Long> packageIdMap = new HashMap<>();

        for (WhsOrderShipmentItemBo warehouseItem : shipmentItems) {
            if (warehouseItem.getLogisticsMethodId() == null || warehouseItem.getWarehouseId() == null) {
                continue;
            }

            String key = warehouseItem.getLogisticsMethodId() + "-" + warehouseItem.getWarehouseId();

            // 检查是否已经为这个仓库+物流方式组合创建过包裹
            if (packageIdMap.containsKey(key)) {
                continue;
            }

            try {
                WhsLogisticsPackage logisticsPackage = new WhsLogisticsPackage();
                logisticsPackage.setOrderId(orderId);
                logisticsPackage.setShipmentId(shipmentId);
                logisticsPackage.setLogisticsMethodId(warehouseItem.getLogisticsMethodId());
                logisticsPackage.setWarehouseId(warehouseItem.getWarehouseId());
                logisticsPackage.setPackageStatus(PackageStatus.PENDING.getValue());
                logisticsPackage.setShippedDate(new Date());

                int result = logisticsPackageMapper.insert(logisticsPackage);
                Long packageId = logisticsPackage.getId();

                if (result > 0 && packageId != null) {
                    packageIdMap.put(key, packageId);
                    log.info("为订单 {} 创建了物流包裹，ID: {}, 物流方式: {}, 仓库: {}, pieceCount: {}",
                        orderId, packageId, warehouseItem.getLogisticsMethodId(),
                        warehouseItem.getWarehouseId(), warehouseItem.getPieceCount());
                } else {
                    throw new ServiceException("创建物流包裹失败");
                }
            } catch (Exception e) {
                log.error("创建物流包裹时发生异常，订单ID: {}, key={}, 错误: {}",
                    orderId, key, e.getMessage(), e);
                throw new ServiceException("创建物流包裹时发生异常: " + e.getMessage());
            }
        }

        return packageIdMap;
    }

    private List<WhsOrderShipmentItem> createShipmentItems(Long shipmentId, Long orderId,
                                                           List<WhsOrderShipmentItemBo> shipmentItems, Map<String, Long> packageIdMap) {

        List<WhsOrderShipmentItem> shipmentItemEntities = new ArrayList<>();

        for (WhsOrderShipmentItemBo warehouseItem : shipmentItems) {
            if (warehouseItem.getLogisticsMethodId() == null || warehouseItem.getWarehouseId() == null
                || CollUtil.isEmpty(warehouseItem.getPlanItems())) {
                log.error("仓库发货项数据不完整: {}", warehouseItem);
                throw new ServiceException("仓库发货项数据不完整");
            }

            // 遍历该仓库下的每个方案项
            for (WhsOrderShipmentPlanItemBo planItemBo : warehouseItem.getPlanItems()) {
                if (planItemBo.getPlanItemId() == null || planItemBo.getQuantity() == null) {
                    log.error("发货方案项数据不完整: {}", planItemBo);
                    throw new ServiceException("发货方案项数据不完整");
                }

                WhsOrderShipmentItem shipmentItem = new WhsOrderShipmentItem();
                shipmentItem.setShipmentId(shipmentId);
                shipmentItem.setOrderId(orderId);
                shipmentItem.setPlanItemId(planItemBo.getPlanItemId());
                shipmentItem.setQuantity(planItemBo.getQuantity());
                shipmentItem.setWarehouseId(warehouseItem.getWarehouseId());

                // 查找对应的物流包裹ID
                String packageKey = warehouseItem.getLogisticsMethodId() + "-" + warehouseItem.getWarehouseId();
                Long packageId = packageIdMap.get(packageKey);

                if (packageId != null) {
                    shipmentItem.setPackageId(packageId);
                } else {
                    log.error("未找到发货项的对应物流包裹，物流方式ID: {}, 仓库ID: {}, PlanItemId: {}",
                        warehouseItem.getLogisticsMethodId(), warehouseItem.getWarehouseId(), planItemBo.getPlanItemId());
                    throw new ServiceException("系统错误：未能关联到发货项的物流包裹");
                }

                shipmentItemEntities.add(shipmentItem);
            }
        }

        if (!shipmentItemEntities.isEmpty()) {
            try {
                shipmentItemMapper.insertBatch(shipmentItemEntities);
                log.debug("成功创建 {} 个发货项记录", shipmentItemEntities.size());
            } catch (Exception e) {
                log.error("批量保存发货项失败，订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
                throw new ServiceException("保存发货项失败: " + e.getMessage());
            }
        }

        return shipmentItemEntities;
    }

    private void updatePlanItemStatus(List<WhsOrderShipmentItemBo> shipmentItems,
                                      Map<Long, WhsShipmentPlanItemVo> planItemMap) {
        for (WhsOrderShipmentItemBo warehouseItem : shipmentItems) {
            if (CollUtil.isEmpty(warehouseItem.getPlanItems())) {
                continue;
            }

            // 遍历该仓库下的每个方案项
            for (WhsOrderShipmentPlanItemBo planItemBo : warehouseItem.getPlanItems()) {
                if (planItemBo.getPlanItemId() == null || planItemBo.getQuantity() == null) {
                    continue;
                }

                WhsShipmentPlanItemVo planItem = planItemMap.get(planItemBo.getPlanItemId());
                if (planItem == null) {
                    log.error("在planItemMap中找不到planItemId: {}", planItemBo.getPlanItemId());
                    continue;
                }

                WhsShipmentPlanItem updatePlanItem = new WhsShipmentPlanItem();
                updatePlanItem.setId(planItemBo.getPlanItemId());
                updatePlanItem.setShippedQuantity(
                    (planItem.getShippedQuantity() != null ? planItem.getShippedQuantity() : 0) + planItemBo.getQuantity());

                try {
                    shipmentPlanItemMapper.updateById(updatePlanItem);
                    log.debug("更新方案项发货数量：planItemId={}, 新数量={}", planItemBo.getPlanItemId(), updatePlanItem.getShippedQuantity());
                } catch (Exception e) {
                    log.error("更新发货方案项状态失败，PlanItemId: {}, 错误: {}", planItemBo.getPlanItemId(), e.getMessage(), e);
                    throw new ServiceException("更新发货方案项状态失败: " + e.getMessage());
                }
            }
        }
    }

    private void processWarehouseRecords(List<WarehouseShipmentRecord> successfulShipments, Long orderId,
                                         WhsOrderVo order, Map<String, Long> packageIdMap, List<WhsOrderShipmentItemBo> shipmentItems,
                                         Map<Long, WhsShipmentPlanItemVo> planItemMap, Integer batchNumber) {

        try {
            log.info("订单 {} 开始处理海外仓发货，批次号: {}, 包裹数量: {}", orderId, batchNumber,
                packageIdMap != null ? packageIdMap.size() : 0);

            overseasShipmentProcessingService.processShipments(successfulShipments, orderId, order, packageIdMap,
                shipmentItems, planItemMap, batchNumber);

            if (successfulShipments.isEmpty()) {
                log.info("订单 {} 海外仓发货处理完成，但没有成功发货任何包裹", orderId);
            } else {
                log.info("订单 {} 海外仓发货处理完成，成功发货 {} 个包裹", orderId, successfulShipments.size());
            }
        } catch (Exception e) {
            log.error("海外仓发货处理失败，订单ID: {}, 批次号: {}, 错误类型: {}, 错误详情: {}", orderId, batchNumber,
                e.getClass().getSimpleName(), e.getMessage());
            throw e;
        }
    }

    /**
     * 获取仓库签名服务支持状态
     *
     * @param warehouseIds 仓库ID列表
     * @return 仓库ID到签名服务支持状态的映射
     */
    @Override
    public Map<String, Boolean> getWarehouseSignatureSupport(List<Long> warehouseIds) {
        Map<String, Boolean> result = new HashMap<>();

        if (CollUtil.isEmpty(warehouseIds)) {
            return result;
        }

        for (Long warehouseId : warehouseIds) {
            try {
                // 查询仓库信息
                WhsWarehouse warehouse = warehouseMapper.selectById(warehouseId);
                if (warehouse == null || !BusinessConstants.YES.equals(warehouse.getIsOverseas())) {
                    // 非海外仓默认不支持签名服务
                    result.put(String.valueOf(warehouseId), false);
                    continue;
                }

                // 获取该仓库的海外仓账号配置
                WhsOverseasWarehouseAccount account = getWarehouseOverseasAccount(warehouseId);
                if (account != null && account.getSupportSignatureService() != null) {
                    boolean supportSignature = "1".equals(account.getSupportSignatureService());
                    result.put(String.valueOf(warehouseId), supportSignature);
                    log.debug("仓库 {} 签名服务支持状态: {}", warehouseId, supportSignature);
                } else {
                    // 默认支持签名服务
                    result.put(String.valueOf(warehouseId), true);
                    log.debug("仓库 {} 未配置签名服务支持状态，使用默认值: true", warehouseId);
                }
            } catch (Exception e) {
                log.error("获取仓库 {} 签名服务支持状态失败: {}", warehouseId, e.getMessage(), e);
                // 出错时默认支持签名服务
                result.put(String.valueOf(warehouseId), true);
            }
        }

        return result;
    }

    /**
     * 根据仓库ID获取海外仓账号配置
     *
     * @param warehouseId 仓库ID
     * @return 海外仓账号配置，如果未找到返回null
     */
    private WhsOverseasWarehouseAccount getWarehouseOverseasAccount(Long warehouseId) {
        if (warehouseId == null) {
            log.warn("仓库ID为空，无法查询海外仓账号配置");
            return null;
        }

        try {
            WhsOverseasWarehouseAccount account = overseasWarehouseAccountMapper.selectByWarehouseId(warehouseId);
            if (account == null) {
                log.debug("未找到仓库ID[{}]对应的海外仓账号配置", warehouseId);
            } else {
                log.debug("成功查询到仓库ID[{}]对应的海外仓账号配置，账号ID: {}", warehouseId, account.getId());
            }
            return account;
        } catch (Exception e) {
            log.error("查询仓库ID[{}]对应的海外仓账号配置时发生异常", warehouseId, e);
            return null;
        }
    }

    /**
     * 验证发货审批状态
     *
     * @param orderId 订单ID
     */
    private void validateShipmentApproval(Long orderId) {
        // 检查是否需要发货审批
        if (!shipmentApprovalService.isShipmentApprovalRequired(orderId)) {
            log.debug("订单[{}]无需发货审批", orderId);
            return;
        }

        // 检查是否已通过审核
        if (!shipmentApprovalService.isShipmentApproved(orderId)) {
            throw new ServiceException("订单尚未通过发货审批，无法执行发货操作");
        }

        log.debug("订单[{}]已通过发货审批验证", orderId);
    }
}
