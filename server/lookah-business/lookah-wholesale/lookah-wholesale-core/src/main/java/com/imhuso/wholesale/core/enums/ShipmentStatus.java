package com.imhuso.wholesale.core.enums;

import com.imhuso.common.translation.core.EnumTranslatableInterface;
import lombok.Getter;

/**
 * 配送状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ShipmentStatus implements EnumTranslatableInterface {

    /**
     * 待发货
     */
    PENDING(0),

    /**
     * 备货中
     */
    PREPARING(1),

    /**
     * 部分发货
     */
    PARTIAL_SHIPPED(2),

    /**
     * 已发货
     */
    SHIPPED(3),

    /**
     * 已送达
     */
    DELIVERED(4);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 构造函数
     */
    ShipmentStatus(int value) {
        this.value = value;
    }
}
