package com.imhuso.wholesale.core.service;

/**
 * 海外仓运费同步服务接口
 *
 * <AUTHOR>
 */
public interface IWhsOverseasFreightSyncService {

    /**
     * 同步所有海外仓的运费信息
     * 查询昊通系统中状态为3（待发货）和4（已发货）的订单，同步运费信息
     *
     * @return 成功同步的订单数量
     */
    int syncAllWarehousesFreight();

    /**
     * 同步指定海外仓的运费信息
     *
     * @param warehouseId 仓库ID
     * @return 成功同步的订单数量
     */
    int syncWarehouseFreight(Long warehouseId);

    /**
     * 异步同步所有海外仓的运费信息
     * 该方法在后台异步执行，避免前端请求超时
     */
    void asyncSyncAllWarehousesFreight();
}
