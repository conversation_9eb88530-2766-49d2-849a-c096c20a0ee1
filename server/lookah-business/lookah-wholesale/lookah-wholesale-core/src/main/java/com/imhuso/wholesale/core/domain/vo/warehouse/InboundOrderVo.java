package com.imhuso.wholesale.core.domain.vo.warehouse;

import com.imhuso.wholesale.core.enums.InboundOrderStatus;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.*;

/**
 * 入库单视图对象
 * 通用的入库单视图对象，适用于所有海外仓提供商
 *
 * <AUTHOR>
 */
@Data
public class InboundOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 入库单号
     */
    private String asnNo;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 状态码
     * 0:待入库,1:部分入库,2:已入库,3:部分上架,4:已上架,10:已作废
     */
    private Integer status;

    /**
     * 状态枚举
     * 使用系统统一的入库单状态枚举
     */
    private InboundOrderStatus statusEnum;

    /**
     * 总箱数
     */
    private Integer bags;

    /**
     * 预计到达时间
     */
    private Date estimatedTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 收货时间
     */
    private Date receiveTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 第三方系统编号
     */
    private String thirdSystemNo;

    /**
     * 第三方系统URL
     */
    private String thirdSystemUrl;

    /**
     * 第三方系统参考号
     */
    private String thirdSystemRefNo;

    /**
     * 入库单明细列表
     */
    private List<InboundOrderItemVo> purchaseDetails = new ArrayList<>();

    /**
     * 物流信息
     */
    private LogisticsInfoVo purchaseLogistics;

    /**
     * 入库单明细视图对象
     */
    @Data
    public static class InboundOrderItemVo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * SKU编码
         */
        private String sku;

        /**
         * 变体ID
         * 系统内部产品变体ID，通过SKU映射获取
         */
        private Long variantId;

        /**
         * 预约入库数量
         */
        private Integer quantity;

        /**
         * 质检合格数量
         */
        private Integer checkQuantity;

        /**
         * 质检破损数量
         */
        private Integer breakQuantity;

        /**
         * 上架数量
         */
        private Integer uploadQuantity;

        /**
         * 备注
         */
        private String remark;
    }

    /**
     * 物流信息视图对象
     */
    @Data
    public static class LogisticsInfoVo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 物流类型
         * 0:快递,1:空运,2:自行送货,3:其他,4:海运货柜
         */
        private Integer deliveryType;

        /**
         * 物流单号或者其他单号
         */
        private String deliveryNo;
    }

    /**
     * 状态码到状态名称的映射
     */
    private static final Map<Integer, String> STATUS_NAME_MAP = new HashMap<>();

    static {
        STATUS_NAME_MAP.put(0, "待入库");
        STATUS_NAME_MAP.put(1, "部分入库");
        STATUS_NAME_MAP.put(2, "已入库");
        STATUS_NAME_MAP.put(3, "部分上架");
        STATUS_NAME_MAP.put(4, "已上架");
        STATUS_NAME_MAP.put(10, "已作废");
    }

    /**
     * 获取状态名称
     *
     * @return 状态名称
     */
    public String getStatusName() {
        if (status == null) {
            return "未知";
        }

        return STATUS_NAME_MAP.getOrDefault(status, "未知状态(" + status + ")");
    }
}
