package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.wholesale.core.domain.WhsProductSeries;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品系列业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WhsProductSeries.class)
public class WhsProductSeriesBo extends BaseEntity {

    /**
     * 系列ID
     */
    @NotNull(message = "系列ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 系列名称
     */
    @NotBlank(message = "系列名称不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 50, message = "系列名称长度不能超过50个字符")
    private String name;

    /**
     * 显示顺序
     */
    private Integer sort;

    /**
     * 状态（0停用 1正常）
     */
    private String status;
}
