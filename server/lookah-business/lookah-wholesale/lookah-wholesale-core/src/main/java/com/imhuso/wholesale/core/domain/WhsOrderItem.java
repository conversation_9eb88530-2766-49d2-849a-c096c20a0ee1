package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 批发订单项对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_order_item")
public class WhsOrderItem extends BaseEntity {

    /**
     * 订单项ID
     */
    @TableId
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 变体ID(SKU)
     */
    private Long variantId;

    /**
     * 产品名称（冗余）
     */
    private String productName;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 采购单价
     */
    private BigDecimal purchasePrice;

    /**
     * 销售单价（手动填入）
     */
    private BigDecimal salesPrice;

    /**
     * 小计金额
     */
    private BigDecimal amount;

    /**
     * 包装类型（0单品 1展示盒 2箱装）
     */
    private Integer packagingType;

    /**
     * 产品包装数量（每个单位内含的单品数量）
     * 例如：单品=1，展示盒=40，整箱=240
     * 作为冗余字段，用于存储产品包装数量
     */
    @JsonIgnore
    private Integer packagingQuantity;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 已发货数量
     */
    private Integer shippedQuantity;

    /**
     * 规格快照
     */
    private String specsSnapshot;
}
