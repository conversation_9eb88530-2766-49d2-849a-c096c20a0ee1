package com.imhuso.wholesale.core.domain.vo.admin;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 产品变体视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AutoMapper(target = WhsProductVariant.class)
public class WhsProductVariantVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 变体ID
     */
    private Long id;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * UPC编码
     */
    private String upc;

    /**
     * 批发价格(Wholesale)
     */
    private BigDecimal wholesalePrice;

    /**
     * 建议零售价(MSRP)
     */
    private BigDecimal msrp;

    /**
     * 门店价格
     */
    private BigDecimal storePrice;

    /**
     * 主图URL
     */
    private String mainImage;

    /**
     * 规格属性JSON(冗余,用于快速访问)
     */
    private String specs;

    /**
     * 状态（0停用 1正常）
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 可用库存
     */
    private Integer availableStock;

    /**
     * 库存预警值（变体自身的预警库存）
     */
    private Integer alertStock;

    /**
     * 成品库存数量
     */
    private Integer finishedStock;

    /**
     * 待产库存数量
     */
    private Integer pendingStock;

    /**
     * 有效预警库存（考虑多仓库逻辑后的最终预警库存）
     */
    private Integer effectiveAlertStock;

    /**
     * 包装类型
     *
     * @see com.imhuso.wholesale.core.enums.PackagingType
     */
    private Integer packagingType;

    /**
     * 包装数量
     */
    private Integer packageQuantity;

    /**
     * 规格属性列表
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<WhsProductVariantAttributeVo> attributes;

    /**
     * 包装变体列表
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<WhsProductVariantVo> packageVariants;
}
