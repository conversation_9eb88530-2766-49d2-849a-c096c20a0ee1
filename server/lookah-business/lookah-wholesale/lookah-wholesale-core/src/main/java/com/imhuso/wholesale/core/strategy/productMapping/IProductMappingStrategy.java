package com.imhuso.wholesale.core.strategy.productMapping;

import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.vo.warehouse.WarehouseContextVo;

import java.util.List;
import java.util.Map;

/**
 * 产品映射策略接口
 * 定义不同海外仓如何将本地产品变体映射到外部系统
 *
 * <AUTHOR>
 */
public interface IProductMappingStrategy {

    /**
     * 获取策略类型标识
     *
     * @return 策略类型
     */
    String getStrategyType();

    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    String getStrategyName();

    /**
     * 检查策略是否适用于指定仓库上下文
     *
     * @param context 仓库上下文
     * @return 是否适用
     */
    boolean isApplicable(WarehouseContextVo context);

    /**
     * 批量获取变体在海外仓中对应的外部编码
     *
     * @param context  仓库上下文
     * @param variants 产品变体列表
     * @return 变体ID到外部编码的映射
     */
    Map<Long, String> getExternalCodes(WarehouseContextVo context, List<WhsProductVariant> variants);

    /**
     * 批量根据外部编码获取对应的变体ID
     *
     * @param context       仓库上下文
     * @param externalCodes 外部编码列表
     * @return 外部编码到变体ID的映射
     */
    Map<String, Long> getVariantIdsByExternalCodes(WarehouseContextVo context, List<String> externalCodes);
}
