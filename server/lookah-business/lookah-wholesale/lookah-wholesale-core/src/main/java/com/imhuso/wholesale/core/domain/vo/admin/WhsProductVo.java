package com.imhuso.wholesale.core.domain.vo.admin;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.imhuso.wholesale.core.domain.WhsProduct;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 批发产品视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AutoMapper(target = WhsProduct.class)
public class WhsProductVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品ID
     */
    private Long id;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 系列ID
     */
    private Long seriesId;

    /**
     * 系列名称
     */
    private String seriesName;

    /**
     * 产品名称
     */
    private String itemName;

    /**
     * 默认变体ID
     */
    private Long defaultVariantId;

    /**
     * 产品主图URL(来源于默认变体或第一个变体的主图)
     */
    private String mainImage;

    /**
     * 变体数量
     */
    private Integer variantCount;

    /**
     * 最低批发价格
     */
    private BigDecimal minWholesalePrice;

    /**
     * 最高批发价格
     */
    private BigDecimal maxWholesalePrice;

    /**
     * 最低建议零售价(MSRP)
     */
    private BigDecimal minMsrp;

    /**
     * 最高建议零售价(MSRP)
     */
    private BigDecimal maxMsrp;

    /**
     * 最低门店价格
     */
    private BigDecimal minStorePrice;

    /**
     * 最高门店价格
     */
    private BigDecimal maxStorePrice;

    /**
     * 总库存
     */
    private Integer totalStock;

    /**
     * 可用库存
     */
    private Integer availableStock;

    /**
     * 状态（0停用 1正常）
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 产品属性列表
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<WhsProductAttributeVo> attributes;
}
