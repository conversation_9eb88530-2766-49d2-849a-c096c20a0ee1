package com.imhuso.wholesale.core.domain.vo.front;

import com.imhuso.wholesale.core.domain.WhsWarehouse;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 前台仓库视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsWarehouse.class)
public class WarehouseVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 仓库ID
     */
    private Long id;

    /**
     * 仓库名称
     */
    private String name;

    /**
     * 仓库编码
     */
    private String code;

    /**
     * 仓库地址
     */
    private String address;

    /**
     * 状态（0停用 1正常）
     */
    private String status;

    /**
     * 是否默认（0否 1是）
     */
    private String isDefault;
}
