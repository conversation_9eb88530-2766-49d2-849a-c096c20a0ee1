package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.bo.admin.ErpStockSyncBo;

import java.util.List;

/**
 * ERP库存同步服务接口
 * 用于同步成品库存和待产库存数据
 *
 * <AUTHOR>
 */
public interface IWhsErpStockSyncService {

    /**
     * 同步所有产品变体的ERP库存数据
     * 包括成品库存和待产库存
     *
     * @return 同步成功的变体数量
     */
    int syncAllVariantErpStock();

    /**
     * 同步指定产品变体的ERP库存数据
     *
     * @param variantIds 产品变体ID列表
     * @return 同步成功的变体数量
     */
    int syncVariantErpStock(List<Long> variantIds);

    /**
     * 从ERP系统获取库存数据
     * TODO: 预留接口，待ERP系统对接时实现
     *
     * @param skuCodes SKU编码列表
     * @return ERP库存数据列表
     */
    List<ErpStockSyncBo> fetchErpStockData(List<String> skuCodes);

    /**
     * 更新产品变体的ERP库存数据
     *
     * @param stockDataList ERP库存数据列表
     * @return 更新成功的记录数
     */
    int updateVariantErpStock(List<ErpStockSyncBo> stockDataList);
}
