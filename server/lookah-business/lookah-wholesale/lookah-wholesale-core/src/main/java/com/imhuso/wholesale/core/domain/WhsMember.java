package com.imhuso.wholesale.core.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商店客户(WhsMember)实体类
 *
 * <AUTHOR>
 * @since 2025-02-11 16:56:55
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "whs_member")
public class WhsMember extends BaseEntity {

    /**
     * 客户ID
     */
    private Long id;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 销售代表ID，关联sys_user表
     */
    private Long salespersonId;

    /**
     * 客户邮箱
     */
    private String email;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * EIN税号
     */
    private String ein;

    /**
     * 密码
     */
    private String password;

    /**
     * 名字
     */
    private String firstName;

    /**
     * 姓氏
     */
    private String lastName;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 帐号状态（0停用 1正常）
     */
    private String status;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 最后登录位置
     */
    private String loginLocation;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 公司类型（wholesale,retail,chain,cash_carry）
     */
    private String companyType;

    /**
     * 店面数量
     */
    private Integer storeCount;

    /**
     * 客户来源
     */
    private String customerSource;

    /**
     * 自定义客户来源（当customerSource为others时使用）
     */
    private String customSource;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 自定义公司类型（当companyType为others时使用）
     */
    private String customCompanyType;

    /**
     * 备注
     */
    private String remark;

}
