package com.imhuso.wholesale.core.domain.vo.front;

import com.imhuso.wholesale.core.domain.WhsCart;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 批发购物车视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsCart.class)
public class CartVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 购物车ID
     */
    private Long id;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 产品名称
     */
    private String itemName;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 规格信息（JSON格式）
     */
    private String specs;

    /**
     * UPC
     */
    private String upc;

    /**
     * 批发价格(Wholesale)
     */
    private BigDecimal wholesalePrice;

    /**
     * 建议零售价(MSRP)
     */
    private BigDecimal msrp;

    /**
     * 可用库存
     */
    private Integer availableStock;

    /**
     * 产品图片
     */
    private String mainImage;

    /**
     * 包装类型（0单品 1展示盒 2箱装）
     */
    private Integer packagingType;
}
