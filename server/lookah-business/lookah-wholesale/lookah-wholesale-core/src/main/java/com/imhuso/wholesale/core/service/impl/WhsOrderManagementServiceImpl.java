package com.imhuso.wholesale.core.service.impl;

import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.wholesale.core.domain.WhsOrder;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderNoEditBo;
import com.imhuso.wholesale.core.enums.OrderStatus;
import com.imhuso.wholesale.core.mapper.WhsOrderMapper;
import com.imhuso.wholesale.core.service.*;
import com.imhuso.wholesale.enums.OrderFileType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 批发订单管理服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsOrderManagementServiceImpl implements IWhsOrderManagementService {

    private final WhsOrderMapper baseMapper;
    private final IWhsOrderStatusService orderStatusService;
    private final IWhsStockAllocationService stockAllocationService;
    private final IWhsOrderBaseService orderBaseService;
    private final IWhsOrderFileService orderFileService;
    private final IWhsOrderItemService orderItemService;
    private final IWhsOrderShipmentApprovalService orderShipmentApprovalService;

    /**
     * 取消订单
     * 【后台】
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder(Long orderId, String cancelReason) {
        // 1. 查询订单是否存在
        WhsOrder order = baseMapper.selectById(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        // 2. 判断订单状态是否允许取消
        int orderStatusCode = order.getOrderStatus();
        // 只有待处理和处理中的订单可以取消
        if (orderStatusCode > OrderStatus.PROCESSING.getValue()) {
            throw new ServiceException("当前订单状态不支持取消操作");
        }

        // 3. 幂等性检查：避免重复取消
        if (OrderStatus.CANCELED.getValue() == orderStatusCode) {
            log.warn("订单已处于取消状态，无需重复操作。订单ID: {}", orderId);
            return;
        }

        // 4. 释放库存
        releaseOrderStock(orderId);

        // 5. 更新订单状态为已取消，记录取消原因
        Integer newStatus = OrderStatus.CANCELED.getValue();
        String remark = String.format("后台管理取消订单，取消原因：%s", cancelReason);
        orderStatusService.updateOrderStatus(orderId, newStatus, null, null, null, remark);
    }

    /**
     * 释放订单库存
     */
    private void releaseOrderStock(Long orderId) {
        try {
            // 使用库存分配服务释放库存
            boolean success = stockAllocationService.releaseStockByOrderId(orderId);
            if (!success) {
                throw new ServiceException("库存释放失败");
            }
        } catch (Exception e) {
            log.error("释放订单库存失败，订单ID: {}, 错误: {}", orderId, e.getMessage());
            throw new ServiceException("库存释放失败，请手动检查库存");
        }
    }

    /**
     * 更新订单编号
     * 【后台】
     *
     * @param orderId 订单ID
     * @param bo 订单编号信息
     * @return 成功与否
     */
    @Override
    public boolean updateOrderNo(Long orderId, WhsOrderNoEditBo bo) {
        log.info("更新订单号: orderId={}, internalOrderNo={}, customerOrderNo={}, referenceNo={}",
            orderId, bo.getInternalOrderNo(), bo.getCustomerOrderNo(), bo.getReferenceNo());

        // 验证订单是否存在
        WhsOrder order = orderBaseService.checkOrderExists(orderId);

        // 只更新非空的字段
        if (bo.getInternalOrderNo() != null) {
            order.setInternalOrderNo(bo.getInternalOrderNo());
        }

        if (bo.getCustomerOrderNo() != null) {
            order.setCustomerOrderNo(bo.getCustomerOrderNo());
        }

        if (bo.getReferenceNo() != null) {
            order.setReferenceNo(bo.getReferenceNo());
        }

        // 更新订单
        return baseMapper.updateById(order) > 0;
    }

    /**
     * 撤回订单到草稿状态
     * 【后台】
     *
     * @param orderId 订单ID
     * @param remark  撤回原因
     * @return 成功与否
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean revertOrderToDraft(Long orderId, String remark) {
        log.info("撤回订单到草稿状态: orderId={}, remark={}", orderId, remark);

        // 验证订单是否存在
        orderBaseService.checkOrderExists(orderId);

        // 1. 清理 INVOICE 文件
        orderFileService.clearOrderFilesByType(orderId, OrderFileType.INVOICE.getCode());

        // 2. 重置订单项销售单价
        orderItemService.resetSalesPrice(orderId);

        // 3. 重置审核状态和清理审核记录
        orderShipmentApprovalService.resetOrderApprovalData(orderId);

        // 4. 调用订单状态服务撤回到草稿状态，包含清理折扣和重置所有状态
        return orderStatusService.revertOrderToDraft(orderId, remark);
    }
}
