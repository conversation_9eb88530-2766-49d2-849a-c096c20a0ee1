package com.imhuso.wholesale.core.service.impl;

import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.json.utils.JsonUtils;
import com.imhuso.wholesale.core.adapter.IOverseasWarehouseProvider;
import com.imhuso.wholesale.core.adapter.OverseasWarehouseProviderFactory;
import com.imhuso.wholesale.core.domain.WhsOverseasWarehouseAccount;
import com.imhuso.wholesale.core.domain.WhsOverseasWarehouseProvider;
import com.imhuso.wholesale.core.domain.WhsWarehouse;
import com.imhuso.wholesale.core.domain.vo.warehouse.LogisticsChannelSyncResultVo;
import com.imhuso.wholesale.core.domain.vo.warehouse.WarehouseContextVo;
import com.imhuso.wholesale.core.mapper.WhsOverseasWarehouseAccountMapper;
import com.imhuso.wholesale.core.mapper.WhsOverseasWarehouseProviderMapper;
import com.imhuso.wholesale.core.mapper.WhsWarehouseMapper;
import com.imhuso.wholesale.core.service.IWhsLogisticsChannelSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * 物流渠道同步服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsLogisticsChannelSyncServiceImpl implements IWhsLogisticsChannelSyncService {

    private final OverseasWarehouseProviderFactory providerFactory;
    private final WhsWarehouseMapper warehouseMapper;
    private final WhsOverseasWarehouseProviderMapper providerMapper;
    private final WhsOverseasWarehouseAccountMapper accountMapper;

    /**
     * 同步物流渠道
     *
     * @param warehouseId 仓库ID
     * @return 同步结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public LogisticsChannelSyncResultVo syncLogisticsChannels(Long warehouseId) {
        log.info("开始同步海外仓物流渠道: warehouseId={}", warehouseId);

        if (warehouseId == null) {
            throw new ServiceException("仓库ID不能为空");
        }

        try {
            // 调用物流渠道同步方法
            WarehouseContextVo context = createWarehouseContextProxy(warehouseId);
            return syncLogisticsChannels(context);
        } catch (Exception e) {
            log.error("同步海外仓物流渠道异常", e);
            throw new ServiceException("同步海外仓物流渠道失败: " + e.getMessage());
        }
    }

    /**
     * 同步物流渠道（指定上下文）
     *
     * @param context 仓库上下文
     * @return 同步结果
     */
    @Transactional(rollbackFor = Exception.class)
    protected LogisticsChannelSyncResultVo syncLogisticsChannels(WarehouseContextVo context) {
        if (context == null) {
            throw new ServiceException("仓库上下文不能为空");
        }

        log.info("开始同步海外仓物流渠道: warehouseId={}, providerType={}", context.getWarehouseId(), context.getProviderType());

        try {
            // 获取仓库和提供商信息
            String providerType = context.getProviderType();
            IOverseasWarehouseProvider provider = providerFactory.getProvider(providerType);

            // 调用海外仓提供商实现同步物流渠道
            LogisticsChannelSyncResultVo result = provider.syncLogisticsChannels(context);

            // 记录同步操作
            logSyncOperation(context, result);

            return result;
        } catch (Exception e) {
            log.error("同步海外仓物流渠道异常", e);
            throw new ServiceException("同步海外仓物流渠道失败: " + e.getMessage());
        }
    }

    /**
     * 创建仓库上下文
     *
     * @param warehouseId 仓库ID
     * @return 仓库上下文
     */
    private WarehouseContextVo createWarehouseContextProxy(Long warehouseId) {
        log.info("创建仓库上下文: warehouseId={}", warehouseId);

        // 1. 查询仓库信息
        WhsWarehouse warehouse = warehouseMapper.selectById(warehouseId);
        if (warehouse == null) {
            throw new ServiceException("仓库不存在: " + warehouseId);
        }

        // 2. 查询提供商信息
        WhsOverseasWarehouseProvider provider = providerMapper.selectById(warehouse.getProviderId());
        if (provider == null) {
            throw new ServiceException("提供商不存在: " + warehouse.getProviderId());
        }

        // 3. 创建上下文对象
        WarehouseContextVo context = new WarehouseContextVo();
        context.setWarehouse(warehouse);
        context.setProvider(provider);
        context.setWarehouseId(warehouse.getId());
        context.setProviderType(provider.getProviderType());

        // 4. 解析仓库配置
        if (warehouse.getWarehouseConfig() != null && !warehouse.getWarehouseConfig().isEmpty()) {
            try {
                Map<String, String> warehouseConfig = JsonUtils.parseObject(warehouse.getWarehouseConfig(), HashMap.class);
                context.setWarehouseConfig(warehouseConfig);
            } catch (Exception e) {
                log.warn("解析仓库配置异常: {}", e.getMessage());
            }
        }

        // 5. 设置账号信息
        if (warehouse.getAccountId() != null) {
            WhsOverseasWarehouseAccount account = accountMapper.selectById(warehouse.getAccountId());
            if (account != null) {
                context.setAccount(account);

                // 6. 解析账号配置
                if (account.getAccountConfig() != null && !account.getAccountConfig().isEmpty()) {
                    try {
                        Map<String, String> accountConfig = JsonUtils.parseObject(account.getAccountConfig(), HashMap.class);
                        context.setAccountConfig(accountConfig);
                        log.info("成功加载账号配置，账号ID={}, 配置项数量={}", account.getId(), accountConfig.size());
                    } catch (Exception e) {
                        log.error("解析账号配置异常: {}", e.getMessage(), e);
                        throw new ServiceException("无法解析账号配置信息: " + e.getMessage());
                    }
                } else {
                    log.warn("账号配置为空，账号ID={}", account.getId());
                    throw new ServiceException("账号配置为空，无法进行同步操作");
                }
            } else {
                log.warn("仓库关联的账号不存在，仓库ID={}, 账号ID={}", warehouse.getId(), warehouse.getAccountId());
                throw new ServiceException("仓库关联的账号不存在");
            }
        } else {
            log.warn("仓库未关联账号，仓库ID={}", warehouse.getId());
            throw new ServiceException("仓库未关联账号，无法进行同步操作");
        }

        return context;
    }

    /**
     * 记录同步操作日志
     *
     * @param context 仓库上下文
     * @param result  同步结果
     */
    private void logSyncOperation(WarehouseContextVo context, LogisticsChannelSyncResultVo result) {
        // 简化的日志记录逻辑
        if (result.isSuccess()) {
            log.info("同步物流渠道成功, 仓库ID={}, 提供商类型={}, 处理数量={}, 成功数量={}",
                context.getWarehouseId(), context.getProviderType(),
                result.getProcessedCount(), result.getSuccessCount());
        } else {
            log.error("同步物流渠道失败, 仓库ID={}, 提供商类型={}, 错误消息={}",
                context.getWarehouseId(), context.getProviderType(), result.getErrorMessage());
        }

        // 可选：在这里添加更详细的日志记录，例如将操作记录保存到数据库
        // 这部分逻辑可以从原始的服务实现中移植过来
    }
}
