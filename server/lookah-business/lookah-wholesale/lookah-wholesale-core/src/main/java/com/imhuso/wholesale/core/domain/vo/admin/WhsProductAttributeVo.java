package com.imhuso.wholesale.core.domain.vo.admin;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.imhuso.wholesale.core.domain.WhsProductAttribute;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 产品属性视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AutoMapper(target = WhsProductAttribute.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WhsProductAttributeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 属性ID
     */
    private Long id;

    /**
     * 属性名称(如color,material)
     */
    private String attrName;

    /**
     * 属性显示名称(如颜色,材质)
     */
    private String attrDisplay;

    /**
     * 是否必填
     */
    private String isRequired;

    /**
     * 排序顺序
     */
    private Integer sort;

    /**
     * 状态（0停用 1正常）
     */
    private String status;

    /**
     * 属性值列表
     */
    private List<WhsProductAttributeValueVo> values;

    /**
     * 是否被变体使用
     */
    private Boolean isUsedByVariants;
}
