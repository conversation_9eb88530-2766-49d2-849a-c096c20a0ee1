package com.imhuso.wholesale.core.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.wholesale.core.domain.WhsStock;
import com.imhuso.wholesale.core.domain.WhsWarehouse;
import com.imhuso.wholesale.core.domain.vo.warehouse.StockSyncResultVo;
import com.imhuso.wholesale.core.domain.vo.warehouse.WarehouseContextVo;
import com.imhuso.wholesale.core.enums.StockOperationType;
import com.imhuso.wholesale.core.mapper.WhsStockMapper;
import com.imhuso.wholesale.core.mapper.WhsWarehouseMapper;
import com.imhuso.wholesale.core.strategy.productMapping.IProductMappingStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 库存同步结果处理器
 * 负责将同步结果转化为具体的库存更新操作
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StockSyncProcessor {

    private final WhsStockMapper stockMapper;
    private final WhsWarehouseMapper warehouseMapper;
    private final IWhsStockLogService stockLogService;
    private final IWhsStockCacheService stockCacheService;

    /**
     * 处理同步结果，基于仓库上下文和当前使用的映射策略
     * 该方法包含在事务中执行
     *
     * @param result          同步结果
     * @param context         仓库上下文
     * @param mappingStrategy 当前使用的映射策略
     */
    @Transactional(rollbackFor = Exception.class)
    public void processStockSyncResult(StockSyncResultVo result, WarehouseContextVo context, IProductMappingStrategy mappingStrategy) {
        if (result == null || !result.isSuccess()) {
            log.warn("同步结果为空或同步失败，不处理库存更新");
            return;
        }

        if (context == null) {
            log.warn("仓库上下文为空，无法处理同步结果");
            return;
        }

        if (mappingStrategy == null) {
            log.warn("映射策略为空，无法处理同步结果");
            return;
        }

        Long warehouseId = context.getWarehouseId();
        log.info("处理仓库[{}]的同步结果，使用映射策略[{}]，需更新{}个商品的库存", warehouseId, mappingStrategy.getStrategyName(), result.getStockUpdates().size());

        // 记录海外仓中未找到的SKU编码
        // 从itemResults中提取not_found状态的SKU
        List<String> notFoundSkus = new ArrayList<>();
        if (result.getItemResults() != null && !result.getItemResults().isEmpty()) {
            for (Map.Entry<String, String> entry : result.getItemResults().entrySet()) {
                if ("not_found".equals(entry.getValue())) {
                    notFoundSkus.add(entry.getKey());
                }
            }
        }

        // 如果存在未找到的SKU，记录详细日志
        if (!notFoundSkus.isEmpty()) {
            log.info("仓库[{}]同步时，有{}个SKU在海外仓中未找到(状态: not_found): {}",
                warehouseId, notFoundSkus.size(), String.join(", ", notFoundSkus));

            // 附加信息，记录映射关系，帮助排查问题
            Map<Long, String> externalCodes = context.getCustomData("externalCodes");
            if (externalCodes != null && !externalCodes.isEmpty()) {
                // 反向查找，找出这些未找到的SKU对应的变体ID
                Map<String, Long> reverseMapping = new HashMap<>();
                for (Map.Entry<Long, String> entry : externalCodes.entrySet()) {
                    reverseMapping.put(entry.getValue(), entry.getKey());
                }

                StringBuilder mappingInfo = new StringBuilder();
                for (String sku : notFoundSkus) {
                    Long variantId = reverseMapping.get(sku);
                    if (variantId != null) {
                        mappingInfo.append(sku).append("->").append(variantId).append(", ");
                    }
                }

                if (!mappingInfo.isEmpty()) {
                    log.debug("未找到SKU的映射关系: {}", mappingInfo.substring(0, mappingInfo.length() - 2));
                }
            }
        }

        if (result.getStockUpdates().isEmpty()) {
            log.info("没有需要更新的库存数据");
            return;
        }

        // 获取外部编码到变体ID的映射关系
        Map<String, Long> externalCodeToIdMap = null;

        // 尝试从上下文中获取已准备好的映射关系
        Map<Long, String> idToExternalCodeMap = context.getCustomData("externalCodes");
        if (idToExternalCodeMap != null && !idToExternalCodeMap.isEmpty()) {
            // 反转映射关系：从变体ID到外部编码 转换为 从外部编码到变体ID
            externalCodeToIdMap = new HashMap<>();
            for (Map.Entry<Long, String> entry : idToExternalCodeMap.entrySet()) {
                externalCodeToIdMap.put(entry.getValue(), entry.getKey());
            }
            log.debug("从上下文中获取到{}个外部编码映射关系", externalCodeToIdMap.size());
        }

        // 如果上下文中没有映射关系，则根据映射策略获取
        if (externalCodeToIdMap == null || externalCodeToIdMap.isEmpty()) {
            // 获取同步结果中所有外部编码
            List<String> externalCodes = new ArrayList<>(result.getStockUpdates().keySet());
            if (!externalCodes.isEmpty()) {
                // 使用映射策略获取外部编码到变体ID的映射
                externalCodeToIdMap = mappingStrategy.getVariantIdsByExternalCodes(context, externalCodes);
                log.debug("通过映射策略获取到{}个外部编码映射关系", externalCodeToIdMap.size());
            }
        }

        // 如果仍然没有映射关系，则无法处理
        if (externalCodeToIdMap == null || externalCodeToIdMap.isEmpty()) {
            log.warn("无法获取外部编码到变体ID的映射关系，无法处理同步结果");
            return;
        }

        // 执行库存更新
        updateStockWithMappings(result, warehouseId, externalCodeToIdMap);
    }

    /**
     * 使用外部编码映射更新库存
     *
     * @param result              同步结果
     * @param warehouseId         仓库ID
     * @param externalCodeToIdMap 外部编码到变体ID的映射关系
     */
    private void updateStockWithMappings(StockSyncResultVo result, Long warehouseId, Map<String, Long> externalCodeToIdMap) {
        if (result == null || !result.isSuccess()) {
            log.warn("同步结果为空或同步失败，不处理库存更新");
            return;
        }

        Map<String, Integer> stockUpdates = result.getStockUpdates();
        int updateSize = stockUpdates.size();

        log.info("处理仓库[{}]的同步结果，需更新{}个商品的库存", warehouseId, updateSize);
        log.info("仓库[{}]同步策略: 同步海外仓返回的所有SKU，对本地不存在库存记录的产品将自动创建库存记录", warehouseId);

        if (stockUpdates.isEmpty()) {
            log.info("没有需要更新的库存数据");
            return;
        }

        // 确保外部编码到ID映射有效
        if (externalCodeToIdMap == null || externalCodeToIdMap.isEmpty()) {
            log.warn("没有找到有效的外部编码映射，无法更新库存");
            return;
        }

        // 获取仓库信息
        WhsWarehouse warehouse = warehouseMapper.selectById(warehouseId);
        if (warehouse == null) {
            log.error("找不到仓库信息: warehouseId={}", warehouseId);
            return;
        }

        // 重要：记录当前同步处理的仓库和SKU列表，方便排查问题
        if (log.isDebugEnabled()) {
            log.debug("仓库[{}]当前同步的SKU列表: {}", warehouseId, String.join(", ", stockUpdates.keySet()));
        }

        // 重点修复: 只收集海外仓API返回的SKU对应的变体ID
        // 这里的stockUpdates是海外仓API返回的库存数据，而不是请求的所有SKU
        Set<Long> variantIds = new HashSet<>();
        Map<String, Long> validExternalCodeToIdMap = new HashMap<>();
        List<String> invalidExternalCodes = new ArrayList<>();

        // 首先过滤出有效的外部编码和对应的变体ID
        for (String externalCode : stockUpdates.keySet()) {
            Long variantId = externalCodeToIdMap.get(externalCode);
            if (variantId != null) {
                variantIds.add(variantId);
                validExternalCodeToIdMap.put(externalCode, variantId);
            } else {
                invalidExternalCodes.add(externalCode);
                log.warn("找不到外部编码[{}]对应的变体ID，跳过", externalCode);
            }
        }

        // 记录未被处理的外部编码
        if (!invalidExternalCodes.isEmpty()) {
            log.warn("海外仓返回了{}个无法匹配的外部编码: {}", invalidExternalCodes.size(), String.join(", ", invalidExternalCodes));
        }

        // 如果没有有效的变体ID，直接返回
        if (variantIds.isEmpty()) {
            log.warn("没有找到有效的变体ID映射，无法更新库存");
            return;
        }

        log.info("海外仓返回的{}个SKU中，有{}个可以匹配到系统中的变体", stockUpdates.size(), variantIds.size());

        // 获取这些变体ID的当前库存信息 - 严格限制只获取当前海外仓返回的SKU对应的变体ID
        Map<Long, WhsStock> variantStockDetails = getVariantStockDetails(variantIds, warehouseId);
        log.debug("为{}个变体ID获取到{}个库存记录", variantIds.size(), variantStockDetails.size());

        int successCount = 0;
        int failCount = 0;
        int skippedCount = 0;

        // 关键修复：确保只遍历海外仓实际返回的SKU（stockUpdates中的key）
        // 不要使用请求中包含的所有SKU列表
        for (String externalCode : stockUpdates.keySet()) {
            Integer newStock = stockUpdates.get(externalCode);

            // 处理库存值：如果为null或负数，设置为0
            if (newStock == null || newStock < 0) {
                log.debug("外部编码[{}]的同步库存小于0，将其设置为0", externalCode);
                newStock = 0;
            }

            // 获取变体ID，如果不存在就跳过
            Long variantId = validExternalCodeToIdMap.get(externalCode);
            if (variantId == null) {
                log.warn("外部编码[{}]无法映射到变体ID，跳过更新", externalCode);
                skippedCount++;
                continue;
            }

            try {
                // 获取当前库存信息
                WhsStock currentStockInfo = variantStockDetails.get(variantId);
                // 如果没有找到库存记录，直接跳过，不创建新记录
                if (currentStockInfo == null) {
                    log.debug("变体ID[{}]没有找到库存记录，创建新库存记录", variantId);
                    // 创建新的库存记录
                    currentStockInfo = new WhsStock();
                    currentStockInfo.setVariantId(variantId);
                    currentStockInfo.setWarehouseId(warehouseId);
                    currentStockInfo.setAvailableStock(0); // 初始可用库存为0
                    currentStockInfo.setLockedStock(0);    // 初始锁定库存为0
                    currentStockInfo.setTotalStock(0);     // 初始总库存为0

                    // 插入新记录
                    stockMapper.insert(currentStockInfo);
                    log.info("为变体ID[{}]创建了新的库存记录", variantId);
                }

                int currentAvailableStock = currentStockInfo.getAvailableStock();
                int lockedStock = currentStockInfo.getLockedStock();
                int currentTotalStock = currentStockInfo.getTotalStock();

                // 确保当前库存不为负数
                if (currentTotalStock < 0) {
                    log.debug("变体ID[{}]的当前库存为负数({}), 将其视为0", variantId, currentTotalStock);
                }

                // 计算库存差值
                // 海外仓返回的newStock是可用库存，我们需要更新本地的可用库存
                // 但锁定库存不变化，总库存 = 可用库存 + 锁定库存
                int difference = newStock - currentAvailableStock;

                if (difference == 0) {
                    log.debug("外部编码[{}]的可用库存无变化(当前={})，跳过更新", externalCode, currentAvailableStock);
                    skippedCount++;
                    continue;
                }

                // 使用库存操作服务更新库存
                String remark = "海外仓库同步更新: 可用库存" + (difference > 0 ? "增加" : "减少") + Math.abs(difference);

                // 创建自定义的库存操作逻辑
                int updatedQuantity = updateStockForSync(variantId, warehouseId, newStock, lockedStock, remark);

                if (updatedQuantity != 0) {
                    log.info("成功更新外部编码[{}]的库存，变体ID[{}]，可用库存变化量: {}, 新可用库存: {}, 锁定库存: {}",
                        externalCode, variantId, difference, newStock, lockedStock);
                    successCount++;
                } else {
                    log.warn("更新外部编码[{}]的库存失败，变体ID[{}]", externalCode, variantId);
                    failCount++;
                }

            } catch (Exception e) {
                log.error("处理外部编码[{}]的库存更新时发生异常: {}", externalCode, e.getMessage());
                if (log.isDebugEnabled()) {
                    log.debug("库存更新异常详情", e);
                }
                failCount++;
            }
        }

        // 明确记录本次处理的仓库和SKU列表
        log.info("仓库[{}]库存更新完成: 成功{}个，失败{}个，跳过{}个", warehouseId, successCount, failCount, skippedCount);

        // 记录详细处理结果
        if (log.isDebugEnabled()) {
            log.debug("库存更新明细: 总数={}, 成功={}, 失败={}, 跳过={}, 成功率={}%",
                updateSize, successCount, failCount, skippedCount,
                updateSize > 0 ? (successCount * 100.0 / updateSize) : 0);
        }
    }

    /**
     * 为库存同步创建特殊的库存更新方法
     *
     * @param variantId         变体ID
     * @param warehouseId       仓库ID
     * @param newAvailableStock 新的可用库存
     * @param lockedStock       当前锁定库存
     * @param remark            备注
     * @return 影响的行数
     */
    private int updateStockForSync(Long variantId, Long warehouseId, int newAvailableStock,
                                   int lockedStock, String remark) {
        try {
            // 查询现有库存记录，不创建新记录
            LambdaQueryWrapper<WhsStock> query = new LambdaQueryWrapper<>();
            query.eq(WhsStock::getVariantId, variantId)
                .eq(WhsStock::getWarehouseId, warehouseId);

            WhsStock stock = stockMapper.selectOne(query);

            // 如果找不到库存记录，则跳过(不应该发生，因为在上层已经过滤)
            if (stock == null) {
                log.warn("库存同步时找不到变体ID[{}]的库存记录，跳过更新", variantId);
                return 0;
            }

            // 记录更新前的库存值
            int beforeAvailable = stock.getAvailableStock();

            // 计算新的总库存 = 新的可用库存 + 现有锁定库存
            int newTotalStock = newAvailableStock + lockedStock;

            // 更新库存值
            stock.setAvailableStock(newAvailableStock);
            stock.setLockedStock(lockedStock);
            stock.setTotalStock(newTotalStock);

            // 使用MyBatis-Plus的updateById进行更新，它会使用version字段作为乐观锁
            int rows = stockMapper.updateById(stock);

            if (rows > 0) {
                // 记录库存操作日志
                stockLogService.createStockLog(
                    stock,
                    StockOperationType.SYNC,
                    newAvailableStock - beforeAvailable, // 变化量
                    beforeAvailable,
                    newAvailableStock,
                    null, // 订单ID
                    null, // 订单项ID
                    remark
                );

                // 更新缓存
                if (org.springframework.transaction.support.TransactionSynchronizationManager.isSynchronizationActive()) {
                    org.springframework.transaction.support.TransactionSynchronizationManager.registerSynchronization(
                        new org.springframework.transaction.support.TransactionSynchronization() {
                            @Override
                            public void afterCommit() {
                                try {
                                    stockCacheService.updateStockCache(stock);
                                } catch (Exception e) {
                                    log.error("事务提交后更新缓存失败: stockId={}, error={}",
                                        stock.getId(), e.getMessage());
                                }
                            }
                        });
                } else {
                    stockCacheService.updateStockCache(stock);
                }

                log.info("库存同步直接更新成功: id={}, 可用库存={}, 锁定库存={}, 总库存={}",
                    stock.getId(), newAvailableStock, lockedStock, newTotalStock);
            } else {
                log.warn("库存同步直接更新失败，可能是并发冲突: stockId={}", stock.getId());
            }

            return rows;
        } catch (Exception e) {
            log.error("同步库存时发生异常: variantId={}, warehouseId={}, error={}",
                variantId, warehouseId, e.getMessage());
            if (log.isDebugEnabled()) {
                log.debug("同步库存异常详情", e);
            }
            return 0;
        }
    }

    /**
     * 批量获取多个变体的详细库存信息
     *
     * @param variantIds  变体ID集合
     * @param warehouseId 仓库ID
     * @return 变体ID到详细库存信息的映射关系
     */
    private Map<Long, WhsStock> getVariantStockDetails(Set<Long> variantIds, Long warehouseId) {
        if (variantIds == null || variantIds.isEmpty() || warehouseId == null) {
            return Collections.emptyMap();
        }

        // 构造查询，一次性获取所有库存记录的详细信息
        LambdaQueryWrapper<WhsStock> stockQuery = new LambdaQueryWrapper<>();
        stockQuery.in(WhsStock::getVariantId, variantIds)
            .eq(WhsStock::getWarehouseId, warehouseId)
            .select(WhsStock::getId, WhsStock::getVariantId, WhsStock::getTotalStock,
                WhsStock::getAvailableStock, WhsStock::getLockedStock);

        List<WhsStock> stocks = stockMapper.selectList(stockQuery);

        // 转换为Map结构，方便快速查找
        Map<Long, WhsStock> result = new HashMap<>(stocks.size());
        for (WhsStock stock : stocks) {
            result.put(stock.getVariantId(), stock);
        }

        return result;
    }
}
