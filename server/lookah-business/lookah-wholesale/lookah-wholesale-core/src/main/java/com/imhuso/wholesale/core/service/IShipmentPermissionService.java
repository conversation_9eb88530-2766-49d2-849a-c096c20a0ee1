package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.WhsOrder;
import com.imhuso.wholesale.core.service.impl.ApprovalConfigServiceImpl;

/**
 * 发货权限检查服务
 * 统一管理发货相关的权限判断逻辑
 *
 * <AUTHOR>
 */
public interface IShipmentPermissionService {

    /**
     * 检查订单发货权限
     * 综合考虑系统配置、订单状态、审批状态等因素
     *
     * @param order 订单信息
     * @return 权限检查结果
     */
    ApprovalConfigServiceImpl.PermissionResult checkShipmentPermission(WhsOrder order);

    /**
     * 获取发货按钮文本
     *
     * @param order 订单信息
     * @return 按钮文本
     */
    String getShipmentButtonText(WhsOrder order);

    /**
     * 检查是否需要显示审批按钮
     *
     * @param order 订单信息
     * @return true-显示审批按钮，false-不显示
     */
    boolean shouldShowApprovalButton(WhsOrder order);

    /**
     * 检查是否需要显示发货按钮
     *
     * @param order 订单信息
     * @return true-显示发货按钮，false-不显示
     */
    boolean shouldShowShipmentButton(WhsOrder order);

    /**
     * 检查是否需要显示撤回审批按钮
     *
     * @param order 订单信息
     * @return true-显示撤回审批按钮，false-不显示
     */
    boolean shouldShowCancelApprovalButton(WhsOrder order);

    /**
     * 检查是否需要显示审批操作按钮（通过/拒绝）
     *
     * @param order 订单信息
     * @return true-显示审批操作按钮，false-不显示
     */
    boolean shouldShowApproveButton(WhsOrder order);
}
