package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.WhsStock;
import com.imhuso.wholesale.core.enums.StockOperationType;

/**
 * 库存操作服务接口
 * 专门处理库存更新的核心逻辑，确保操作安全和事务正确传播
 *
 * <AUTHOR>
 */
public interface IWhsStockOperationService {

    /**
     * 统一库存操作方法
     * <p>
     * 所有库存更新操作的统一入口，使用乐观锁确保并发安全
     *
     * @param stock         库存对象
     * @param operationType 操作类型
     * @param quantity      数量（正数增加，负数减少）
     * @param orderId       订单ID（可选）
     * @param orderItemId   订单项ID（可选）
     * @param remark        备注
     * @return 实际操作的数量
     */
    int updateStock(WhsStock stock, StockOperationType operationType, int quantity,
                    Long orderId, Long orderItemId, String remark);
}
