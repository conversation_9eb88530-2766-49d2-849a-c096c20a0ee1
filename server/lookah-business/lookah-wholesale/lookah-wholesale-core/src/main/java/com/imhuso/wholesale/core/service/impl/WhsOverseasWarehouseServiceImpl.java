package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.json.utils.JsonUtils;
import com.imhuso.wholesale.core.adapter.IOverseasWarehouseProvider;
import com.imhuso.wholesale.core.adapter.OverseasWarehouseProviderFactory;
import com.imhuso.wholesale.core.domain.*;
import com.imhuso.wholesale.core.domain.vo.warehouse.*;
import com.imhuso.wholesale.core.mapper.*;
import com.imhuso.wholesale.core.service.IWhsOverseasWarehouseService;
import com.imhuso.wholesale.core.service.StockSyncProcessor;
import com.imhuso.wholesale.core.strategy.productMapping.IProductMappingStrategy;
import com.imhuso.wholesale.core.strategy.productMapping.ProductMappingStrategyFactory;
import com.imhuso.wholesale.core.utils.EncryptUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 海外仓服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsOverseasWarehouseServiceImpl implements IWhsOverseasWarehouseService {

    private final WhsWarehouseMapper warehouseMapper;
    private final WhsOverseasWarehouseProviderMapper providerMapper;
    private final WhsOverseasWarehouseAccountMapper accountMapper;
    private final WhsShippingDispatchLogMapper dispatchLogMapper;
    private final OverseasWarehouseProviderFactory providerFactory;
    private final ProductMappingStrategyFactory productMappingStrategyFactory;
    private final StockSyncProcessor stockSyncProcessor;
    private final WhsLogisticsPackageMapper logisticsPackageMapper;

    /**
     * 创建仓库上下文
     *
     * @param warehouseId 仓库ID
     * @return 仓库上下文
     */
    @Override
    public WarehouseContextVo createWarehouseContext(Long warehouseId) {
        // 查询仓库信息
        WhsWarehouse warehouse = warehouseMapper.selectById(warehouseId);
        if (warehouse == null) {
            throw new ServiceException("仓库不存在");
        }

        // 检查是否为海外仓
        if (!BusinessConstants.YES.equals(warehouse.getIsOverseas())) {
            throw new ServiceException("非海外仓，无法执行此操作");
        }

        // 检查仓库状态
        if (!BusinessConstants.NORMAL.equals(warehouse.getStatus())) {
            throw new ServiceException("仓库已停用，无法执行此操作");
        }

        // 检查提供商ID
        if (warehouse.getProviderId() == null) {
            throw new ServiceException("仓库未配置提供商，无法执行此操作");
        }

        // 查询提供商信息
        WhsOverseasWarehouseProvider provider = providerMapper.selectById(warehouse.getProviderId());
        if (provider == null) {
            throw new ServiceException("提供商不存在");
        }

        // 检查提供商状态
        if (!BusinessConstants.NORMAL.equals(provider.getStatus())) {
            throw new ServiceException("提供商已停用，无法执行此操作");
        }

        // 创建上下文对象并设置基本信息
        WarehouseContextVo context = new WarehouseContextVo();
        context.setWarehouse(warehouse);
        context.setProvider(provider);

        // 解析仓库配置
        Map<String, String> warehouseConfig = parseWarehouseConfig(warehouse);
        context.setWarehouseConfig(warehouseConfig);

        // 设置账号信息
        if (warehouse.getAccountId() != null) {
            WhsOverseasWarehouseAccount account = accountMapper.selectById(warehouse.getAccountId());
            if (account != null) {
                context.setAccount(account);

                // 解析账号配置
                Map<String, String> accountConfig = parseAccountConfig(account);
                context.setAccountConfig(accountConfig);
            }
        }

        // 设置冗余信息
        context.setProviderType(provider.getProviderType());
        context.setWarehouseId(warehouse.getId());

        // 设置映射策略信息
        IOverseasWarehouseProvider providerImpl = providerFactory.getProvider(provider.getProviderType());
        if (providerImpl != null) {
            // 获取提供商支持的映射策略类型
            context.setMappingStrategyType(providerImpl.getMappingStrategyType());

            // 从仓库配置中获取已配置的映射策略
            String configuredStrategyType = getConfiguredStrategyType(context);
            if (configuredStrategyType != null) {
                // 设置为当前映射策略
                context.setCurrentMappingStrategy(configuredStrategyType);
            } else {
                // 使用提供商默认映射策略
                context.setCurrentMappingStrategy(providerImpl.getMappingStrategyType());
            }
        }

        return context;
    }

    @Override
    public StockSyncResultVo syncStock(Long warehouseId, List<WhsProductVariant> variants) {
        // 创建仓库上下文
        WarehouseContextVo context = createWarehouseContext(warehouseId);

        // 获取提供商实现
        IOverseasWarehouseProvider provider = providerFactory.getProvider(context.getProvider().getProviderType());

        // 根据仓库配置获取对应的产品映射策略
        IProductMappingStrategy mappingStrategy = selectMappingStrategy(context);
        log.info("仓库[{}]使用映射策略: {}", warehouseId, mappingStrategy.getStrategyName());

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 同步库存
        try {
            // 初始化变量
            final Map<Long, String> externalCodes;
            List<WhsProductVariant> filteredVariants = variants;

            // 为变体列表映射外部编码
            if (variants != null && !variants.isEmpty()) {
                // 获取所有变体的外部编码映射
                externalCodes = mappingStrategy.getExternalCodes(context, variants);
                log.debug("变体ID到外部编码映射: {}", externalCodes);

                // 重要优化：只使用有外部编码的变体，避免发送无关的SKU到海外仓API
                if (externalCodes != null && !externalCodes.isEmpty()) {
                    // 记录被过滤掉的变体信息，便于排查问题
                    List<String> filteredSkus = variants.stream().filter(v -> !externalCodes.containsKey(v.getId())).map(WhsProductVariant::getSkuCode).collect(Collectors.toList());

                    if (!filteredSkus.isEmpty()) {
                        log.info("以下SKU没有外部编码映射，将被过滤: {}", String.join(", ", filteredSkus));
                    }

                    // 只保留有外部编码的变体
                    filteredVariants = variants.stream().filter(v -> externalCodes.containsKey(v.getId())).collect(Collectors.toList());

                    log.info("原始变体数量:{}, 筛选后变体数量:{}, 只使用有外部编码的变体与海外仓API通信", variants.size(), filteredVariants.size());
                } else {
                    log.warn("没有找到任何变体的外部编码映射，同步可能会失败");
                    // 如果没有任何外部编码映射，则使用空列表避免不必要的API调用
                    filteredVariants = Collections.emptyList();
                }

                // 记录映射信息，以便后续处理结果时使用
                context.setCustomData("externalCodes", externalCodes);
            }

            // 使用过滤后的变体列表调用海外仓API
            StockSyncResultVo result = provider.syncStock(context, filteredVariants);

            // 记录日志
            logSyncOperation(context, filteredVariants, result, startTime);

            // 处理同步结果，更新库存
            if (result.isSuccess() && stockSyncProcessor != null) {
                try {
                    // 使用新方法，传递上下文和映射策略
                    stockSyncProcessor.processStockSyncResult(result, context, mappingStrategy);
                } catch (Exception e) {
                    log.error("处理库存同步结果时发生异常", e);
                    // 重新抛出异常，表明内部处理失败
                    throw new ServiceException("处理库存同步结果失败: " + e.getMessage());
                }
            }

            return result;
        } catch (Exception e) {
            log.error("同步库存失败", e);

            // 创建失败结果
            StockSyncResultVo result = new StockSyncResultVo();
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());

            // 记录日志
            logSyncOperation(context, variants, result, startTime);

            throw new ServiceException("同步库存失败: " + e.getMessage());
        }
    }

    /**
     * 根据仓库上下文选择合适的产品映射策略
     *
     * @param context 仓库上下文
     * @return 选择的映射策略
     */
    private IProductMappingStrategy selectMappingStrategy(WarehouseContextVo context) {
        // 获取context中已经设置好的当前映射策略类型
        String strategyType = context.getCurrentMappingStrategy();

        if (strategyType != null && !strategyType.isEmpty()) {
            // 根据映射策略类型获取对应的策略实现
            IProductMappingStrategy mappingStrategy = productMappingStrategyFactory.getStrategy(strategyType);
            if (mappingStrategy != null) {
                log.debug("使用已配置的映射策略: {}", strategyType);
                return mappingStrategy;
            }
        }

        // 如果没有找到策略或没有配置，使用默认策略
        log.debug("使用默认的SKU映射策略");
        return productMappingStrategyFactory.getDefaultStrategy();
    }

    /**
     * 从仓库配置中获取指定的策略类型
     *
     * @param context 仓库上下文
     * @return 策略类型，如果未配置则返回null
     */
    private String getConfiguredStrategyType(WarehouseContextVo context) {
        try {
            Map<String, String> config = context.getWarehouseConfig();
            if (config != null) {
                return config.get("mappingStrategyType");
            }
        } catch (Exception e) {
            log.warn("解析仓库配置失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 记录同步操作日志
     *
     * @param context   仓库上下文
     * @param variants  变体列表
     * @param result    同步结果
     * @param startTime 开始时间
     */
    private void logSyncOperation(WarehouseContextVo context, List<WhsProductVariant> variants, StockSyncResultVo result, long startTime) {
        // 如果变体列表过大，记录日志可能会很消耗资源，这里做一些限制
        int variantCount = variants != null ? variants.size() : 0;

        try {
            WhsShippingDispatchLog dispatchLog = new WhsShippingDispatchLog();
            dispatchLog.setWarehouseId(context.getWarehouse().getId());
            dispatchLog.setProviderId(context.getProvider().getId());
            if (context.getAccount() != null) {
                dispatchLog.setAccountId(context.getAccount().getId());
            }
            // 设置订单ID为0，因为库存同步操作不关联具体订单
            dispatchLog.setOrderId(0L);
            dispatchLog.setOperationType("SYNC");
            dispatchLog.setStatus(result.isSuccess() ? "SUCCESS" : "FAILED");

            // 构建请求数据，避免冗余信息
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("warehouseId", context.getWarehouseId());
            requestData.put("providerType", context.getProviderType());

            // 限制变体数量，如果超过100个只记录数量信息
            if (variantCount <= 100) {
                // 只记录实际传递给提供商的关键信息
                Map<Long, String> externalCodes = context.getCustomData("externalCodes");
                List<Map<String, Object>> variantList = null;
                if (variants != null) {
                    variantList = variants.stream().map(v -> {
                        Map<String, Object> info = new HashMap<>(3);
                        info.put("id", v.getId());

                        // 根据映射策略记录相关的编码
                        if (externalCodes != null && externalCodes.containsKey(v.getId())) {
                            info.put("externalCode", externalCodes.get(v.getId()));
                        } else {
                            // 如果没有映射关系，记录原始SKU和UPC，便于问题排查
                            info.put("sku", v.getSkuCode());
                            info.put("upc", v.getUpc());
                        }
                        return info;
                    }).collect(Collectors.toList());
                }
                requestData.put("variants", variantList);
            } else {
                // 对于大量变体，只记录数量和ID列表，避免生成过大的JSON
                requestData.put("variantCount", variantCount);
                requestData.put("variantIds", variants.stream().map(WhsProductVariant::getId).collect(Collectors.toList()));
            }

            // 记录同步参数信息
            if (result.getExtraData() != null && !result.getExtraData().isEmpty()) {
                requestData.put("syncParams", result.getExtraData());
            }

            dispatchLog.setRequestData(JsonUtils.toJsonString(requestData));

            // 限制响应数据大小，避免记录过大的JSON
            // 深拷贝结果以避免修改原始对象
            StockSyncResultVo logResult = new StockSyncResultVo();
            logResult.setSuccess(result.isSuccess());
            logResult.setErrorMessage(result.getErrorMessage());
            logResult.setWarnings(result.getWarnings());
            logResult.setProcessedCount(result.getProcessedCount());
            logResult.setSuccessCount(result.getSuccessCount());
            logResult.setFailedCount(result.getFailedCount());
            logResult.setSkippedCount(result.getSkippedCount());

            // 记录不超过20个未识别的编码
            if (result.getUnrecognizedCodes() != null && !result.getUnrecognizedCodes().isEmpty()) {
                List<String> codes = result.getUnrecognizedCodes();
                if (codes.size() > 20) {
                    logResult.setUnrecognizedCodes(codes.subList(0, 20));
                    logResult.getExtraData().put("moreUnrecognizedCodes", codes.size() - 20);
                } else {
                    logResult.setUnrecognizedCodes(codes);
                }
            }

            // 对大量更新项，只记录数量信息
            if (result.getStockUpdates() != null && !result.getStockUpdates().isEmpty()) {
                int updateCount = result.getStockUpdates().size();
                if (updateCount > 50) {
                    logResult.getExtraData().put("stockUpdateCount", updateCount);
                } else {
                    logResult.setStockUpdates(result.getStockUpdates());
                }
            }

            dispatchLog.setResponseData(JsonUtils.toJsonString(logResult));

            dispatchLog.setErrorMessage(result.getErrorMessage());
            dispatchLog.setProcessTime((int) (System.currentTimeMillis() - startTime));
            dispatchLog.setItemsCount(variantCount);

            // 插入日志记录
            dispatchLogMapper.insert(dispatchLog);
        } catch (Exception e) {
            // 记录日志失败不应影响主业务流程
            log.error("记录库存同步日志失败: warehouseId={}, variantCount={}, errorMessage={}", context.getWarehouseId(), variantCount, e.getMessage());

            // 在DEBUG级别记录详细堆栈，避免在生产环境产生过多日志
            if (log.isDebugEnabled()) {
                log.debug("记录库存同步日志失败详细信息", e);
            }
        }
    }

    /**
     * 解析仓库配置
     *
     * @param warehouse 仓库
     * @return 解析后的配置Map
     */
    private Map<String, String> parseWarehouseConfig(WhsWarehouse warehouse) {
        Map<String, String> warehouseConfig = new HashMap<>();
        if (StringUtils.isNotEmpty(warehouse.getWarehouseConfig())) {
            try {
                String configContent = warehouse.getWarehouseConfig();
                // 检查配置是否已经是明文JSON（以 { 开头）
                String decryptedConfig;
                if (configContent.startsWith("{")) {
                    // 配置已经是明文，不需要解密
                    log.debug("仓库配置已是明文JSON，无需解密");
                    decryptedConfig = configContent;
                } else {
                    // 尝试解密
                    decryptedConfig = EncryptUtils.decrypt(configContent);
                }

                Map<String, Object> dict = JsonUtils.parseMap(decryptedConfig);
                if (dict != null) {
                    // 将Dict转换为Map<String, String>
                    for (String key : dict.keySet()) {
                        Object value = dict.get(key);
                        warehouseConfig.put(key, value != null ? value.toString() : null);
                    }
                }
                log.debug("解析仓库配置成功: {}", warehouseConfig);
            } catch (Exception e) {
                log.error("解析仓库配置失败", e);
                throw new ServiceException("仓库配置解析失败: " + e.getMessage());
            }
        } else {
            log.warn("仓库未配置warehouseConfig");
        }
        return warehouseConfig;
    }

    /**
     * 解析账号配置
     *
     * @param account 账号
     * @return 解析后的配置Map
     */
    private Map<String, String> parseAccountConfig(WhsOverseasWarehouseAccount account) {
        Map<String, String> accountConfig = new HashMap<>();
        if (StringUtils.isNotEmpty(account.getAccountConfig())) {
            try {
                String configContent = account.getAccountConfig();
                // 检查配置是否已经是明文JSON（以 { 开头）
                String decryptedConfig;
                if (configContent.startsWith("{")) {
                    // 配置已经是明文，不需要解密
                    log.debug("账号配置已是明文JSON，无需解密");
                    decryptedConfig = configContent;
                } else {
                    // 尝试解密
                    decryptedConfig = EncryptUtils.decrypt(configContent);
                }

                var dict = JsonUtils.parseMap(decryptedConfig);
                if (dict != null) {
                    // 将Dict转换为Map<String, String>
                    for (String key : dict.keySet()) {
                        Object value = dict.get(key);
                        accountConfig.put(key, value != null ? value.toString() : null);
                    }
                }
                log.debug("解析账号配置成功");
            } catch (Exception e) {
                log.error("解析账号配置失败", e);
                throw new ServiceException("账号配置解析失败: " + e.getMessage());
            }
        }
        return accountConfig;
    }

    /**
     * 创建发货单
     *
     * @param warehouseId  仓库ID
     * @param shipmentInfo 发货信息
     * @param packageId    包裹ID
     * @param batchNumber  批次号
     * @return 发货结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShippingResultVo createShipment(Long warehouseId, ShipmentInfoVo shipmentInfo, Long packageId, Integer batchNumber) {
        log.info("开始创建海外仓发货单，仓库ID: {}, 订单ID: {}, 包裹ID: {}, 批次号: {}", warehouseId, shipmentInfo.getOrderId(), packageId, batchNumber);

        // 创建仓库上下文
        WarehouseContextVo context;
        try {
            context = createWarehouseContext(warehouseId);
        } catch (Exception e) {
            log.error("创建仓库上下文失败，仓库ID: {}, 错误: {}", warehouseId, e.getMessage(), e);
            throw new ServiceException("获取仓库信息失败: " + e.getMessage());
        }

        String providerType = context.getProvider().getProviderType();

        try {
            // 获取提供商实现
            IOverseasWarehouseProvider provider = providerFactory.getProvider(providerType);

            // 使用提供商自己的方法转换订单数据 - 传递 packageId 和 batchNumber
            Map<String, Object> orderData = provider.convertShipmentInfo(context, shipmentInfo, packageId, batchNumber);

            // 调用提供商创建发货订单方法
            CreateOrderResultVo createResult = provider.createShipmentOrder(context, orderData);

            // 记录发货操作日志
            logShipmentOperation(context, shipmentInfo, createResult);

            // 如果发货失败，抛出异常使事务回滚
            if (!createResult.isSuccess()) {
                String errorMessage = createResult.getErrorMessage();
                if (errorMessage != null && errorMessage.contains("$")) {
                    // 去除无关信息，直接提取核心错误消息
                    throw new ServiceException(errorMessage);
                } else {
                    throw new ServiceException("创建发货单失败：" + errorMessage);
                }
            }

            // 构建并返回结果
            return ShippingResultVo.builder().success(true).orderNo(shipmentInfo.getOrderNo()).externalShipmentId(createResult.getOrderNo()).trackingNumber(createResult.getTrackingNo()).build();

        } catch (ServiceException se) {
            // ServiceException 直接抛出，保持原始错误消息
            throw se;
        } catch (Exception e) {
            log.error("创建海外仓发货单失败，仓库ID: {}, 订单ID: {}, 错误: {}", warehouseId, shipmentInfo.getOrderId(), e.getMessage(), e);

            // 检查错误消息，规范化异常信息
            String errorMessage = e.getMessage();
            if (errorMessage != null && errorMessage.contains("$")) {
                // 提取关键错误信息
                throw new ServiceException(errorMessage);
            } else {
                // 抛出异常以触发事务回滚
                throw new ServiceException("创建发货单失败：" + errorMessage);
            }
        }
    }

    /**
     * 记录发货操作日志
     *
     * @param context      仓库上下文
     * @param shipmentInfo 发货信息
     * @param result       创建结果
     */
    private void logShipmentOperation(WarehouseContextVo context, ShipmentInfoVo shipmentInfo, CreateOrderResultVo result) {
        try {
            WhsShippingDispatchLog log = new WhsShippingDispatchLog();
            log.setOrderId(shipmentInfo.getOrderId());
            log.setWarehouseId(context.getWarehouseId());
            log.setProviderId(context.getProvider().getId()); // 设置提供商ID
            log.setStatus(result.isSuccess() ? "SUCCESS" : "FAILED");
            log.setErrorMessage(result.getErrorMessage());
            // 使用昊通返回的 OrderNo 作为 externalRefId
            log.setExternalRefId(result.getOrderNo());
            // 使用昊通返回的 TrackingNo
            log.setTrackingNumber(result.getTrackingNo());

            // 设置操作类型
            log.setOperationType("CREATE_SHIPMENT");

            // 保存日志
            dispatchLogMapper.insert(log);
        } catch (Exception e) {
            // 日志记录失败记录错误，但不影响主流程
            log.error("记录发货操作日志失败，订单ID: {}, 错误: {}", shipmentInfo.getOrderId(), e.getMessage());
        }
    }

    /**
     * 取消海外仓订单
     *
     * @param warehouseId        仓库ID
     * @param packageId          包裹ID
     * @param externalShipmentId 外部系统（如海外仓）返回的发货单号
     * @return 取消结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class) // 通常取消操作也应在一个事务中，以记录日志等
    public CancelOrderResultVo cancelOrder(Long warehouseId, Long packageId, String externalShipmentId) {
        // 校验参数
        if (warehouseId == null || packageId == null || StringUtils.isBlank(externalShipmentId)) {
            log.error("取消海外仓订单参数无效: warehouseId={}, packageId={}, externalShipmentId={}", warehouseId, packageId, externalShipmentId);
            throw new ServiceException("取消订单参数无效，缺少仓库ID、包裹ID或外部发货单号");
        }

        log.info("开始取消海外仓订单，仓库ID: {}, 包裹ID: {}, 外部发货单号: {}", warehouseId, packageId, externalShipmentId);

        // 创建仓库上下文
        WarehouseContextVo context;
        try {
            context = createWarehouseContext(warehouseId);
        } catch (Exception e) {
            log.error("创建仓库上下文失败，仓库ID: {}, 错误: {}", warehouseId, e.getMessage(), e);
            // 记录取消日志（尝试）
            logCancelOperation(warehouseId, null, packageId, externalShipmentId, CancelOrderResultVo.builder().success(false).errorMessage("获取仓库信息失败").build());
            throw new ServiceException("获取仓库信息失败: " + e.getMessage());
        }

        String providerType = context.getProvider().getProviderType();
        Long orderId = null; // 尝试获取订单ID用于日志记录
        try {
            WhsLogisticsPackage pkg = logisticsPackageMapper.selectById(packageId);
            if (pkg != null) {
                orderId = pkg.getOrderId();
            }
        } catch (Exception logEx) {
            log.warn("获取订单ID用于取消日志记录时出错: {}", logEx.getMessage());
        }

        CancelOrderResultVo result;
        try {
            // 获取提供商实现
            IOverseasWarehouseProvider provider = providerFactory.getProvider(providerType);

            // 调用提供商的取消方法，传入 externalShipmentId
            result = provider.cancelOrder(context, packageId, externalShipmentId);

        } catch (Exception e) {
            log.error("调用海外仓提供商取消订单接口时发生异常，仓库ID: {}, 包裹ID: {}, 外部发货单号: {}, 错误: {}", warehouseId, packageId, externalShipmentId, e.getMessage(), e);
            // 构建失败结果
            result = CancelOrderResultVo.builder().success(false).orderNo(externalShipmentId) // 使用外部ID作为参考
                .errorMessage("取消订单时发生内部错误: " + e.getMessage()).build();
            // 注意：即使这里失败，下面仍然会尝试记录日志
        }

        // 记录取消操作日志
        logCancelOperation(warehouseId, orderId, packageId, externalShipmentId, result);

        // 如果取消失败，则抛出异常，允许调用者（如回滚逻辑）知道失败
        if (!result.isSuccess()) {
            log.error("取消海外仓订单失败: {}", result.getErrorMessage());
            throw new ServiceException("取消海外仓订单失败: " + result.getErrorMessage());
        }

        log.info("海外仓订单取消请求成功，仓库ID: {}, 包裹ID: {}, 外部发货单号: {}", warehouseId, packageId, externalShipmentId);
        return result;
    }

    /**
     * 记录取消操作日志
     *
     * @param warehouseId        仓库ID
     * @param orderId            订单ID（可能为空）
     * @param packageId          包裹ID
     * @param externalShipmentId 外部发货单号
     * @param result             取消结果
     */
    private void logCancelOperation(Long warehouseId, Long orderId, Long packageId, String externalShipmentId, CancelOrderResultVo result) {
        long startTime = System.currentTimeMillis();
        try {
            // 创建日志对象
            WhsShippingDispatchLog dispatchLog = new WhsShippingDispatchLog();

            // 设置基础信息
            dispatchLog.setWarehouseId(warehouseId);
            dispatchLog.setShippingId(packageId); // 使用 packageId 作为 shipping_id
            dispatchLog.setOrderId(ObjectUtil.defaultIfNull(orderId, 0L));

            // 尝试获取Provider和Account ID
            try {
                WarehouseContextVo context = createWarehouseContext(warehouseId);
                dispatchLog.setProviderId(context.getProvider().getId());
                if (context.getAccount() != null) {
                    dispatchLog.setAccountId(context.getAccount().getId());
                }
            } catch (Exception contextEx) {
                log.warn("记录取消日志时无法获取仓库上下文: {}", contextEx.getMessage());
            }

            // 设置状态信息
            dispatchLog.setStatus(result.isSuccess() ? "SUCCESS" : "FAILED");
            dispatchLog.setErrorMessage(result.getErrorMessage());
            dispatchLog.setExternalRefId(externalShipmentId); // 使用外部发货单号作为外部引用ID

            // 设置操作类型
            dispatchLog.setOperationType("CANCEL_ORDER");

            // 设置处理时间
            dispatchLog.setProcessTime((int) (System.currentTimeMillis() - startTime));

            // 构建请求数据（关键参数）
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("warehouseId", warehouseId);
            requestData.put("packageId", packageId);
            requestData.put("externalShipmentId", externalShipmentId);
            // 如果 result 中有 csRefNo，也记录一下
            if (StringUtils.isNotBlank(result.getCsRefNo())) {
                requestData.put("csRefNo", result.getCsRefNo());
            }

            // 序列化请求数据
            try {
                dispatchLog.setRequestData(JsonUtils.toJsonString(requestData));
            } catch (Exception e) {
                log.warn("请求数据序列化失败: {}", e.getMessage());
                dispatchLog.setRequestData("{\"error\":\"序列化失败\"}");
            }

            // 构建响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("success", result.isSuccess());
            responseData.put("externalOrderNo", result.getOrderNo());
            if (StringUtils.isNotBlank(result.getErrorMessage())) {
                responseData.put("errorMessage", result.getErrorMessage());
            }
            if (StringUtils.isNotBlank(result.getCsRefNo())) {
                responseData.put("csRefNo", result.getCsRefNo());
            }

            // 序列化响应数据
            try {
                dispatchLog.setResponseData(JsonUtils.toJsonString(responseData));
            } catch (Exception e) {
                log.warn("响应数据序列化失败: {}", e.getMessage());
                dispatchLog.setResponseData("{\"error\":\"序列化失败\"}");
            }

            // 保存日志
            dispatchLogMapper.insert(dispatchLog);

            if (dispatchLog.getId() != null) {
                log.debug("成功记录取消订单操作日志，日志ID: {}, 外部发货单号: {}", dispatchLog.getId(), externalShipmentId);
            }
        } catch (Exception e) {
            // 确保日志记录失败不影响主流程
            log.error("记录取消订单操作日志失败，外部发货单号: {}, 错误: {}", externalShipmentId, e.getMessage());
            if (log.isDebugEnabled()) {
                log.debug("日志记录异常详情", e);
            }
        }
    }
}
