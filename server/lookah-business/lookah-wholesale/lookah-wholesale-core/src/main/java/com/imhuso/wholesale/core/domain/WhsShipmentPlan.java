package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.*;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 发货方案对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("whs_shipment_plan")
public class WhsShipmentPlan extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 方案ID
     */
    @TableId
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 方案名称
     */
    private String planName;

    /**
     * 方案评分
     */
    private BigDecimal score;

    /**
     * 是否系统推荐
     */
    @Builder.Default
    private Boolean isRecommended = false;

    /**
     * 是否已选择
     */
    @Builder.Default
    private Boolean isSelected = false;

    /**
     * 是否为自定义方案
     */
    @Builder.Default
    private Boolean isCustom = false;

    /**
     * 包裹数量
     */
    @Builder.Default
    private Integer packageCount = 0;

    /**
     * 总PCS数量
     */
    @Builder.Default
    private Integer totalPcs = 0;

    /**
     * 状态：0-待确认 1-已执行 3-不可用
     *
     * @see com.imhuso.wholesale.core.enums.ShipmentPlanStatus
     */
    private Integer status;

    /**
     * 方案项列表，仅在内存中使用，不持久化
     * -- GETTER --
     * 获取方案项列表
     * <p>
     * <p>
     * -- SETTER --
     * 设置方案项列表
     */
    @Setter
    @Getter
    @TableField(exist = false)
    @Builder.Default
    private List<WhsShipmentPlanItem> planItems = new ArrayList<>();
}
