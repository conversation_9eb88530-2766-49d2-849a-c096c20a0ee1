package com.imhuso.wholesale.core.job;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import com.aizuda.snailjob.common.log.SnailJobLog;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.service.IReplenishmentNotificationService;
import com.imhuso.wholesale.core.service.IWhsInboundOrderSyncService;
import com.imhuso.wholesale.core.service.IWhsStockSyncService;
import com.imhuso.wholesale.core.domain.vo.warehouse.StockSyncSummary;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 补货通知定时任务
 * <p>
 * 每日检查库存补货需求并发送通知
 * 配置为每天凌晨0:10执行一次
 * <p>
 * 执行流程：
 * 1. 先同步所有仓库库存
 * 2. 同步入库单和在途库存
 * 3. 检查补货需求并发送通知
 * <p>
 * 补货逻辑：(可用库存 + 在途库存) < 预警库存
 * 通知方式：邮件 + 企业微信 + Excel附件
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@SuppressWarnings("unused") // SnailJob框架调用，IDE检测不到使用
public class ReplenishmentNotificationJob {

    private final IReplenishmentNotificationService replenishmentNotificationService;
    private final IWhsStockSyncService stockSyncService;
    private final IWhsInboundOrderSyncService inboundOrderSyncService;

    /**
     * 执行补货通知任务
     * <p>
     * 执行流程：
     * 1. 先同步所有仓库库存，确保数据最新
     * 2. 同步入库单和在途库存
     * 3. 检查补货需求并发送通知
     *
     * @param jobArgs 任务参数
     * @return 执行结果
     */
    @JobExecutor(name = "sendReplenishmentNotification")
    public ExecuteResult sendReplenishmentNotification(JobArgs jobArgs) {
        SnailJobLog.LOCAL.info("开始执行补货通知任务");
        try {
            // 1. 先同步库存，确保数据最新
            SnailJobLog.LOCAL.info("步骤1：开始同步所有库存（海外仓 + ERP）");
            StockSyncSummary syncSummary = stockSyncService.syncAllStocks();
            SnailJobLog.LOCAL.info("库存同步完成，海外仓成功: {}, ERP成功: {}",
                    syncSummary.getWarehouseSuccessCount(), syncSummary.getErpSuccessCount());

            // 2. 同步入库单和在途库存
            SnailJobLog.LOCAL.info("步骤2：开始同步入库单和在途库存");
            int syncedInboundOrderCount = inboundOrderSyncService.syncAllWarehouses();
            SnailJobLog.LOCAL.info("入库单同步完成，成功同步{}个仓库的入库单", syncedInboundOrderCount);

            // 3. 检查补货需求并发送通知
            SnailJobLog.LOCAL.info("步骤3：开始检查补货需求并发送通知");
            int notificationCount = replenishmentNotificationService.checkAndSendReplenishmentNotifications();

            String message = String.format("补货通知任务执行完成，同步了海外仓%d个/ERP%d个，%d个仓库入库单，发送了%d条通知",
                syncSummary.getWarehouseSuccessCount(), syncSummary.getErpSuccessCount(),
                syncedInboundOrderCount, notificationCount);
            SnailJobLog.LOCAL.info(message);
            return ExecuteResult.success(message);
        } catch (Exception e) {
            String errorMessage = "补货通知任务执行失败: " +
                StringUtils.defaultIfEmpty(e.getMessage(), e.getClass().getName());
            SnailJobLog.LOCAL.error(errorMessage, e);
            return ExecuteResult.failure(errorMessage);
        }
    }
}
