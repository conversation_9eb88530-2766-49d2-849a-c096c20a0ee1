package com.imhuso.wholesale.core.adapter;

import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.vo.warehouse.StockSyncResultVo;
import com.imhuso.wholesale.core.domain.vo.warehouse.WarehouseContextVo;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 海外仓提供商抽象基类，实现了提供商的通用逻辑
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractOverseasWarehouseProvider implements IOverseasWarehouseProvider {

    //-------------------------------------------------------------------------
    // 配置获取相关方法
    //-------------------------------------------------------------------------

    /**
     * 获取配置值，安全处理可能为空的情况
     *
     * @param config 配置信息
     * @param key    配置键
     * @return 配置值，如果不存在则返回null
     */
    protected String getConfigValue(Map<String, String> config, String key) {
        if (config == null || !config.containsKey(key)) {
            return null;
        }
        return config.get(key);
    }

    //-------------------------------------------------------------------------
    // 账号配置相关方法
    //-------------------------------------------------------------------------

    /**
     * 从仓库上下文中获取账号配置值
     *
     * @param context 仓库上下文
     * @param key     配置键
     * @return 配置值，如果不存在则返回null
     */
    protected String getAccountConfig(WarehouseContextVo context, String key) {
        if (context == null || context.getAccountConfig() == null) {
            log.warn("仓库上下文或账号配置为空，无法获取账号配置: {}", key);
            return null;
        }
        return getConfigValue(context.getAccountConfig(), key);
    }

    /**
     * 从仓库上下文中获取必需的账号配置值，如果不存在则抛出异常
     *
     * @param context 仓库上下文
     * @param key     配置键
     * @return 配置值
     * @throws ServiceException 如果配置值不存在或为空
     */
    protected String getRequiredAccountConfig(WarehouseContextVo context, String key) {
        String value = getAccountConfig(context, key);
        if (value == null || value.trim().isEmpty()) {
            throw new ServiceException(String.format("海外仓提供商 [%s] 缺少必要账号配置: %s", this.getProviderType(), key));
        }
        return value;
    }

    //-------------------------------------------------------------------------
    // 仓库配置相关方法
    //-------------------------------------------------------------------------

    /**
     * 从仓库上下文中获取仓库配置值
     *
     * @param context 仓库上下文
     * @param key     配置键
     * @return 配置值，如果不存在则返回null
     */
    protected String getWarehouseConfig(WarehouseContextVo context, String key) {
        if (context == null || context.getWarehouseConfig() == null) {
            log.warn("仓库上下文或仓库配置为空，无法获取仓库配置: {}", key);
            return null;
        }
        return getConfigValue(context.getWarehouseConfig(), key);
    }

    //-------------------------------------------------------------------------
    // 默认实现 - 这些方法可被子类覆盖
    //-------------------------------------------------------------------------

    /**
     * 同步库存 - 默认实现抛出未实现异常
     * 子类应当覆盖此方法提供具体实现
     *
     * @param context  仓库上下文
     * @param variants 变体列表
     * @return 同步结果
     * @throws ServiceException 未实现异常
     */
    @Override
    public StockSyncResultVo syncStock(WarehouseContextVo context, List<WhsProductVariant> variants) {
        throw new ServiceException(String.format("海外仓提供商 [%s] 未实现库存同步功能", this.getProviderType()));
    }
}
