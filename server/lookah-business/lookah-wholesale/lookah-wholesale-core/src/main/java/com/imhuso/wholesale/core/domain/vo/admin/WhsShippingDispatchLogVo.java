package com.imhuso.wholesale.core.domain.vo.admin;

import com.imhuso.wholesale.core.domain.WhsShippingDispatchLog;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 海外仓发货日志视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsShippingDispatchLog.class)
public class WhsShippingDispatchLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    private Long id;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 提供商ID
     */
    private Long providerId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 物流ID
     */
    private Long shippingId;

    /**
     * 外部系统参考ID
     */
    private String externalRefId;

    /**
     * 操作类型(CREATE/CANCEL/TRACK/SYNC)
     */
    private String operationType;

    /**
     * 状态(SUCCESS/FAILED/PARTIAL)
     */
    private String status;

    /**
     * 请求数据
     */
    private String requestData;

    /**
     * 响应数据
     */
    private String responseData;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 处理耗时(毫秒)
     */
    private Integer processTime;

    /**
     * 商品项数量
     */
    private Integer itemsCount;

    /**
     * 总数量
     */
    private Integer totalQuantity;

    /**
     * 跟踪单号
     */
    private String trackingNumber;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
