package com.imhuso.wholesale.core.config;

import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Wholesale模块业务配置类
 * 统一管理wholesale模块的所有业务配置，替换分散的@Value注解
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "wholesale")
public class WholesaleBusinessConfig {

    /**
     * 发票配置
     */
    private InvoiceConfig invoice = new InvoiceConfig();

    /**
     * 库存配置
     */
    private StockConfig stock = new StockConfig();

    /**
     * 发票配置
     */
    @Data
    public static class InvoiceConfig {
        /**
         * 发票号初始值
         * YAML中配置为initial-id，Spring Boot会自动转换为initialId
         */
        private Integer initialId = 30000;
    }

    /**
     * 库存配置
     */
    @Data
    public static class StockConfig {
        /**
         * 库存通知配置
         */
        private NotifyConfig notify = new NotifyConfig();
    }

    /**
     * 库存通知配置
     */
    @Data
    public static class NotifyConfig {
        /**
         * 库存到货通知阈值
         */
        private Integer threshold = 10;
    }

    /**
     * 配置初始化后的日志输出
     */
    @PostConstruct
    public void init() {
        log.info("Wholesale业务配置已加载:");
        log.info("  - 发票配置: initialId={}", invoice.initialId);
        log.info("  - 库存通知阈值: {}", stock.notify.threshold);
    }

    /**
     * 获取发票号初始值
     */
    public Integer getInvoiceInitialId() {
        return invoice.initialId;
    }

    /**
     * 获取库存通知阈值
     */
    public Integer getStockNotifyThreshold() {
        return stock.notify.threshold;
    }
}
