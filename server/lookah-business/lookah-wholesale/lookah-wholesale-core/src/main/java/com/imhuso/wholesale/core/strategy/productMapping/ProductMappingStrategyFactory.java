package com.imhuso.wholesale.core.strategy.productMapping;

import com.imhuso.common.core.exception.ServiceException;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 产品映射策略工厂
 * 负责管理和提供产品映射策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ProductMappingStrategyFactory {

    /**
     * 注入所有策略实现
     */
    private final List<IProductMappingStrategy> strategies;

    /**
     * 策略类型到策略实例的映射
     */
    private final Map<String, IProductMappingStrategy> strategyMap = new HashMap<>();

    /**
     * 默认策略类型
     */
    private static final String DEFAULT_STRATEGY_TYPE = "SKU";

    /**
     * 初始化，将所有策略注册到映射表中
     */
    @PostConstruct
    public void init() {
        for (IProductMappingStrategy strategy : strategies) {
            strategyMap.put(strategy.getStrategyType(), strategy);
            log.info("注册产品映射策略: {}-{}", strategy.getStrategyType(), strategy.getStrategyName());
        }
    }

    /**
     * 根据策略类型获取策略
     *
     * @param strategyType 策略类型
     * @return 对应的策略实例
     */
    public IProductMappingStrategy getStrategy(String strategyType) {
        if (strategyType == null || strategyType.isEmpty()) {
            return getDefaultStrategy();
        }

        IProductMappingStrategy strategy = strategyMap.get(strategyType);
        if (strategy == null) {
            log.warn("未找到类型为{}的映射策略，将使用默认策略", strategyType);
            return getDefaultStrategy();
        }

        return strategy;
    }

    /**
     * 获取默认的映射策略
     *
     * @return 默认策略
     */
    public IProductMappingStrategy getDefaultStrategy() {
        IProductMappingStrategy defaultStrategy = strategyMap.get(DEFAULT_STRATEGY_TYPE);
        if (defaultStrategy == null) {
            throw new ServiceException("系统错误: 未找到默认的SKU映射策略");
        }
        return defaultStrategy;
    }

    /**
     * 获取所有已注册的策略
     *
     * @return 所有策略实例列表
     */
    public List<IProductMappingStrategy> getAllStrategies() {
        return strategies;
    }
}
