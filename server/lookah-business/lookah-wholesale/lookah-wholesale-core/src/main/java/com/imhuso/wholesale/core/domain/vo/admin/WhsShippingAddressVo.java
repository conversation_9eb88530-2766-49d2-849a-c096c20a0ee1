package com.imhuso.wholesale.core.domain.vo.admin;

import java.io.Serial;
import java.io.Serializable;

import com.imhuso.wholesale.core.domain.WhsShippingAddress;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

/**
 * 会员地址管理视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsShippingAddress.class)
public class WhsShippingAddressVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 地址ID
     */
    private Long id;

    /**
     * 名
     */
    private String firstName;

    /**
     * 姓
     */
    private String lastName;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 联系邮箱
     */
    private String email;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 国家
     */
    private String country;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 州
     */
    private String state;

    /**
     * 州名称
     */
    private String stateName;

    /**
     * 城市
     */
    private String city;

    /**
     * 地址行1
     */
    private String addressLine1;

    /**
     * 地址行2（公寓、套房、单元等）
     */
    private String addressLine2;

    /**
     * 邮政编码
     */
    private String zipCode;

    /**
     * 送货说明
     */
    private String deliveryNotes;

    /**
     * 是否默认地址（0否 1是）
     */
    private String isDefault;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 会员邮箱
     */
    private String memberEmail;

    /**
     * 会员姓名
     */
    private String memberName;
}
