package com.imhuso.wholesale.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.utils.DateUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.constant.WholesaleConstants;
import com.imhuso.wholesale.core.domain.WhsOrder;
import com.imhuso.wholesale.core.domain.WhsOrderShipmentApproval;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderShipmentApprovalBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentApprovalVo;
import com.imhuso.wholesale.core.enums.ShipmentApprovalRecordStatus;
import com.imhuso.wholesale.core.enums.ShipmentApprovalStatus;
import com.imhuso.wholesale.core.mapper.WhsOrderMapper;
import com.imhuso.wholesale.core.mapper.WhsOrderShipmentApprovalMapper;
import com.imhuso.wholesale.core.service.IApprovalConfigService;
import com.imhuso.wholesale.core.service.IWecomService;
import com.imhuso.wholesale.core.service.IWhsOrderShipmentApprovalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 订单发货审批服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsOrderShipmentApprovalServiceImpl implements IWhsOrderShipmentApprovalService {

    private final WhsOrderShipmentApprovalMapper approvalMapper;
    private final WhsOrderMapper orderMapper;
    private final IWecomService wecomService;
    private final IApprovalConfigService approvalConfigService;
    private final WhsOrderShipmentApprovalTransactionService transactionService;

    @Override
    public TableDataInfo<WhsOrderShipmentApprovalVo> queryPageList(WhsOrderShipmentApprovalBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WhsOrderShipmentApproval> lqw = buildQueryWrapper(bo);
        Page<WhsOrderShipmentApprovalVo> result = approvalMapper.selectVoPageWithOrderNo(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public WhsOrderShipmentApprovalVo queryById(Long id) {
        return approvalMapper.selectVoById(id);
    }

    @Override
    public WhsOrderShipmentApprovalVo queryLatestByOrderId(Long orderId) {
        LambdaQueryWrapper<WhsOrderShipmentApproval> lqw = Wrappers.lambdaQuery();
        lqw.eq(WhsOrderShipmentApproval::getOrderId, orderId)
            .eq(WhsOrderShipmentApproval::getRecordStatus, ShipmentApprovalRecordStatus.ACTIVE.getValue())
            .orderByDesc(WhsOrderShipmentApproval::getOperationSequence)
            .orderByDesc(WhsOrderShipmentApproval::getCreateTime)
            .last("LIMIT 1");
        return approvalMapper.selectVoOneWithOrderNo(lqw);
    }

    @Override
    public List<WhsOrderShipmentApprovalVo> queryHistoryByOrderId(Long orderId) {
        LambdaQueryWrapper<WhsOrderShipmentApproval> lqw = Wrappers.lambdaQuery();
        lqw.eq(WhsOrderShipmentApproval::getOrderId, orderId)
            .orderByDesc(WhsOrderShipmentApproval::getOperationSequence)
            .orderByDesc(WhsOrderShipmentApproval::getCreateTime);
        return approvalMapper.selectVoListWithOrderNo(lqw);
    }

    @Override
    public Long submitApprovalRequest(Long orderId, String applicationReason) {
        // 使用独立的事务服务处理数据库操作
        Long approvalId = transactionService.submitApprovalRequestInTransaction(orderId, applicationReason);

        // 事务提交后执行外部服务调用
        executePostTransactionOperations(approvalId, orderId);

        return approvalId;
    }


    /**
     * 执行事务后的外部服务调用操作
     */
    private void executePostTransactionOperations(Long approvalId, Long orderId) {
        try {
            // 获取审批记录和订单信息用于发送通知
            WhsOrderShipmentApproval approval = approvalMapper.selectById(approvalId);
            WhsOrder order = orderMapper.selectById(orderId);

            if (approval != null && order != null) {
                // 发送企业微信通知（外部服务调用）
                sendApprovalNotification(approval, order);
            }

            // 检查是否需要自动审批
            if (approvalConfigService.isAutoApprove()) {
                boolean success = approveShipmentRequest(approvalId, true, WholesaleConstants.Approval.SYSTEM_AUTO_APPROVE_COMMENT);
                if (success) {
                    log.info("订单[{}]自动审批成功", orderId);
                } else {
                    log.error("订单[{}]自动审批失败", orderId);
                }
            }
        } catch (Exception e) {
            // 外部服务调用失败不影响主流程，记录日志即可
            log.error("执行审批申请后续操作失败: approvalId={}, orderId={}, error={}", approvalId, orderId, e.getMessage(), e);
        }
    }

    @Override
    public Long submitApprovalRequestBySystem(Long orderId, String applicationReason) {
        // 使用事务服务处理数据库操作
        Long approvalId = transactionService.submitApprovalRequestBySystem(orderId, applicationReason);

        // 事务提交后的后续操作
        try {
            // 获取审批记录和订单信息用于发送通知
            WhsOrderShipmentApproval approval = approvalMapper.selectById(approvalId);
            WhsOrder order = orderMapper.selectById(orderId);

            if (approval != null && order != null) {
                // 发送企业微信通知
                sendApprovalNotification(approval, order);
            }

            // 检查是否需要自动审批
            if (approvalConfigService.isAutoApprove()) {
                log.info("订单[{}]配置了自动审批，开始自动审批通过", orderId);
                boolean success = approveShipmentRequest(approvalId, true, WholesaleConstants.Approval.SYSTEM_AUTO_APPROVE_COMMENT);
                if (success) {
                    log.info("订单[{}]系统自动审批成功", orderId);
                } else {
                    log.error("订单[{}]系统自动审批失败", orderId);
                }
            }
        } catch (Exception e) {
            log.error("系统自动申请后续操作失败: approvalId={}, orderId={}, error={}", approvalId, orderId, e.getMessage(), e);
        }

        return approvalId;
    }

    @Override
    public boolean approveShipmentRequest(Long approvalId, boolean approved, String approvalComment) {
        // 使用事务服务处理数据库操作
        boolean result = transactionService.approveShipmentRequest(approvalId, approved, approvalComment);

        if (result) {
            // 事务提交后发送通知
            try {
                WhsOrderShipmentApproval originalApproval = approvalMapper.selectById(approvalId);
                if (originalApproval != null) {
                    WhsOrder order = orderMapper.selectById(originalApproval.getOrderId());
                    WhsOrderShipmentApprovalVo latestApproval = queryLatestByOrderId(originalApproval.getOrderId());

                    if (order != null && latestApproval != null) {
                        WhsOrderShipmentApproval newApproval = approvalMapper.selectById(latestApproval.getId());
                        if (newApproval != null) {
                            // 发送审批结果通知
                            sendApprovalResultNotification(newApproval, order, approved);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("发送审批结果通知失败: approvalId={}, approved={}, error={}", approvalId, approved, e.getMessage(), e);
            }
        }

        return result;
    }

    @Override
    public boolean cancelApprovalRequest(Long approvalId) {
        // 使用事务服务处理数据库操作
        boolean result = transactionService.cancelApprovalRequest(approvalId);

        if (result) {
            // 事务提交后发送通知
            sendCancellationNotificationAfterCancel(approvalId, "发送撤销通知失败");
        }

        return result;
    }

    @Override
    public boolean cancelApprovalRequestBySystem(Long approvalId, String reason) {
        // 使用事务服务处理数据库操作
        boolean result = transactionService.cancelApprovalRequestBySystem(approvalId, reason);

        if (result) {
            // 事务提交后发送通知
            sendCancellationNotificationAfterCancel(approvalId, "系统撤销审批申请后发送通知失败");
        }

        return result;
    }

    /**
     * 撤销审批后发送通知的公共方法
     *
     * @param approvalId 审批ID
     * @param errorMessage 错误日志消息
     */
    private void sendCancellationNotificationAfterCancel(Long approvalId, String errorMessage) {
        try {
            WhsOrderShipmentApproval originalApproval = approvalMapper.selectById(approvalId);
            if (originalApproval != null) {
                WhsOrder order = orderMapper.selectById(originalApproval.getOrderId());
                WhsOrderShipmentApprovalVo latestApproval = queryLatestByOrderId(originalApproval.getOrderId());

                if (order != null && latestApproval != null) {
                    WhsOrderShipmentApproval cancelApproval = approvalMapper.selectById(latestApproval.getId());
                    if (cancelApproval != null) {
                        // 发送撤销通知
                        sendCancellationNotification(cancelApproval, order);
                    }
                }
            }
        } catch (Exception e) {
            log.error("{}: approvalId={}, error={}", errorMessage, approvalId, e.getMessage(), e);
        }
    }

    @Override
    public boolean isShipmentApprovalRequired(Long orderId) {
        // 从系统配置中检查是否启用发货审批
        return approvalConfigService.isApprovalEnabled();
    }

    @Override
    public boolean isShipmentApproved(Long orderId) {
        if (!isShipmentApprovalRequired(orderId)) {
            // 如果不需要审核，直接返回true
            return true;
        }

        // 直接从订单表获取审批状态，不查询审批记录表
        WhsOrder order = orderMapper.selectById(orderId);
        if (order == null) {
            log.warn("订单不存在: orderId={}", orderId);
            return false;
        }

        Integer approvalStatus = order.getShipmentApprovalStatus();
        return ShipmentApprovalStatus.APPROVED.getValue().equals(approvalStatus);
    }


    /**
     * 构建查询条件
     * 已优化：使用JOIN查询避免N+1问题
     * 优化：只显示ACTIVE状态的记录，避免显示重复的历史记录
     */
    private LambdaQueryWrapper<WhsOrderShipmentApproval> buildQueryWrapper(WhsOrderShipmentApprovalBo bo) {
        LambdaQueryWrapper<WhsOrderShipmentApproval> lqw = Wrappers.lambdaQuery();

        // 订单号查询：利用XML中的JOIN查询，避免N+1问题
        if (StringUtils.isNotBlank(bo.getOrderNo())) {
            // 使用参数化查询避免SQL注入风险
            lqw.apply("wo.internal_order_no LIKE CONCAT('%', {0}, '%')", bo.getOrderNo());
        }

        // 只显示ACTIVE状态的记录，每个订单只显示最新的可操作记录
        lqw.eq(WhsOrderShipmentApproval::getRecordStatus, ShipmentApprovalRecordStatus.ACTIVE.getValue());
        log.info("发货审批查询过滤条件：只显示ACTIVE状态记录，过滤值={}", ShipmentApprovalRecordStatus.ACTIVE.getValue());

        lqw.eq(bo.getOrderId() != null, WhsOrderShipmentApproval::getOrderId, bo.getOrderId());
        lqw.eq(bo.getApprovalStatus() != null, WhsOrderShipmentApproval::getApprovalStatus, bo.getApprovalStatus());
        lqw.like(StringUtils.isNotBlank(bo.getApplicantName()), WhsOrderShipmentApproval::getApplicantName, bo.getApplicantName());
        lqw.like(StringUtils.isNotBlank(bo.getApproverName()), WhsOrderShipmentApproval::getApproverName, bo.getApproverName());
        lqw.orderByDesc(WhsOrderShipmentApproval::getCreateTime);
        return lqw;
    }

    /**
     * 发送审核通知
     */
    private void sendApprovalNotification(WhsOrderShipmentApproval approval, WhsOrder order) {
        try {
            if (!wecomService.isEnabled()) {
                log.debug("企业微信服务未启用，跳过发货审批通知");
                return;
            }

            String title = "🔔 发货审批通知";

            // 添加标题

            String content = "# " + title + "\n\n" +

                // 添加分隔线
                "<font color=\"#1E88E5\">" + "—".repeat(16) + "</font>\n\n" +

                // 添加通知摘要
                "> " + "📋" + " 有新的发货审批申请需要处理\n\n" +

                // 添加订单基本信息
                "<font color=\"#546E7A\">" + "🏷️" + " 订单号：</font> " + "`" + order.getInternalOrderNo() + "`\n\n" +
                "<font color=\"#43A047\">" + "👤" + " 申请人：</font> " + "<font color=\"#FF5722\">" + approval.getApplicantName() + "</font>\n\n" +
                "<font color=\"#9C27B0\">" + "📝" + " 申请理由：</font> " + "<font color=\"#7B1FA2\">" + approval.getApplicationReason() + "</font>\n\n" +
                "<font color=\"#546E7A\">" + "📅" + " 申请时间：</font> " + DateUtils.formatDateTime(approval.getCreateTime()) + "\n\n" +

                // 添加操作提示
                "<font color=\"#1E88E5\">" + "⏰" + " 请及时处理发货审批申请</font>\n";

            wecomService.sendMarkdownMessage(title, content);
            log.info("发货审批企业微信通知发送成功: approvalId={}", approval.getId());
        } catch (Exception e) {
            log.error("发送发货审批企业微信通知失败: approvalId={}, error={}", approval.getId(), e.getMessage(), e);
        }
    }

    /**
     * 发送审批结果通知
     */
    private void sendApprovalResultNotification(WhsOrderShipmentApproval approval, WhsOrder order, boolean approved) {
        try {
            if (!wecomService.isEnabled()) {
                log.debug("企业微信服务未启用，跳过发货审批结果通知");
                return;
            }

            String title = approved ? "✅ 发货审批通过" : "❌ 发货审批拒绝";
            StringBuilder content = new StringBuilder();

            // 添加标题
            content.append("# ").append(title).append("\n\n");

            // 添加分隔线
            content.append("<font color=\"").append(approved ? "#4CAF50" : "#F44336").append("\">")
                .append("—".repeat(16)).append("</font>\n\n");

            // 添加通知摘要
            String statusEmoji = approved ? "🎉" : "⚠️";
            String statusText = approved ? "发货审批已通过" : "发货审批被拒绝";
            content.append("> ").append(statusEmoji).append(" ").append(statusText).append("\n\n");

            // 添加订单基本信息
            content.append("<font color=\"#546E7A\">").append("🏷️").append(" 订单号：</font> ").append("`").append(order.getInternalOrderNo()).append("`\n\n");

            content.append("<font color=\"#43A047\">").append("👤").append(" 申请人：</font> ").append("<font color=\"#FF5722\">").append(approval.getApplicantName()).append("</font>\n\n");

            content.append("<font color=\"#2196F3\">").append("👨‍💼").append(" 审批人：</font> ").append("<font color=\"#FF5722\">").append(approval.getApproverName()).append("</font>\n\n");

            content.append("<font color=\"#546E7A\">").append("📅").append(" 审批时间：</font> ").append(DateUtils.formatDateTime(approval.getApprovalTime())).append("\n\n");

            if (StringUtils.isNotBlank(approval.getApprovalComment())) {
                String commentColor = approved ? "#4CAF50" : "#F44336";
                content.append("<font color=\"").append(commentColor).append("\">")
                    .append("💬").append(" 审批意见：</font> ")
                    .append("<font color=\"").append(commentColor).append("\">")
                    .append(approval.getApprovalComment()).append("</font>\n\n");
            }

            // 添加后续提示
            if (approved) {
                content.append("<font color=\"#4CAF50\">").append("🚚").append(" 订单已可以进行发货操作</font>\n");
            } else {
                content.append("<font color=\"#F44336\">").append("🔄").append(" 如需重新申请，请修改后重新提交</font>\n");
            }

            wecomService.sendMarkdownMessage(title, content.toString());
            log.info("发货审批结果企业微信通知发送成功: approvalId={}, approved={}", approval.getId(), approved);
        } catch (Exception e) {
            log.error("发送发货审批结果企业微信通知失败: approvalId={}, approved={}, error={}", approval.getId(), approved, e.getMessage(), e);
        }
    }

    /**
     * 发送撤销通知
     */
    private void sendCancellationNotification(WhsOrderShipmentApproval approval, WhsOrder order) {
        try {
            if (!wecomService.isEnabled()) {
                log.debug("企业微信服务未启用，跳过发货审批撤销通知");
                return;
            }

            String title = "🔄 发货审批申请已撤销";

            // 添加标题

            String content = "# " + title + "\n\n" +

                // 添加分隔线
                "<font color=\"#FF9800\">" + "—".repeat(16) + "</font>\n\n" +

                // 添加通知摘要
                "> " + "📝" + " 发货审批申请已被撤销\n\n" +

                // 添加订单基本信息
                "<font color=\"#546E7A\">" + "🏷️" + " 订单号：</font> " + "`" + order.getInternalOrderNo() + "`\n\n" +
                "<font color=\"#43A047\">" + "👤" + " 申请人：</font> " + "<font color=\"#FF5722\">" + approval.getApplicantName() + "</font>\n\n" +
                "<font color=\"#9C27B0\">" + "📝" + " 原申请理由：</font> " + "<font color=\"#7B1FA2\">" + approval.getApplicationReason() + "</font>\n\n" +
                "<font color=\"#546E7A\">" + "📅" + " 撤销时间：</font> " + DateUtils.formatDateTime(new Date()) + "\n\n" +

                // 添加后续提示
                "<font color=\"#FF9800\">" + "💡" + " 如需发货审批，请重新提交申请</font>\n";

            wecomService.sendMarkdownMessage(title, content);
            log.info("发货审批撤销企业微信通知发送成功: approvalId={}", approval.getId());
        } catch (Exception e) {
            log.error("发送发货审批撤销企业微信通知失败: approvalId={}, error={}", approval.getId(), e.getMessage(), e);
        }
    }


    /**
     * 重置订单的审核数据（状态和记录）
     * 用于订单撤回时清理审核相关数据
     *
     * @param orderId 订单ID
     */
    @Override
    public void resetOrderApprovalData(Long orderId) {
        // 使用事务服务处理数据库操作
        transactionService.resetOrderApprovalData(orderId);
    }

}
