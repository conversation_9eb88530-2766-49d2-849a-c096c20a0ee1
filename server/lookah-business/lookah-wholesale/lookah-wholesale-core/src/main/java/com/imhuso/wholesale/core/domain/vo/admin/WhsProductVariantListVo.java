package com.imhuso.wholesale.core.domain.vo.admin;

import com.imhuso.wholesale.core.domain.WhsProductVariant;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品变体列表视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AutoMapper(target = WhsProductVariant.class)
public class WhsProductVariantListVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 变体ID
     */
    private Long id;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * UPC编码
     */
    private String upc;

    /**
     * 批发价格(Wholesale)
     */
    private BigDecimal wholesalePrice;

    /**
     * 建议零售价(MSRP)
     */
    private BigDecimal msrp;

    /**
     * 门店价格
     */
    private BigDecimal storePrice;

    /**
     * 主图URL
     */
    private String mainImage;

    /**
     * 规格属性JSON(冗余,用于快速访问)
     */
    private String specs;

    /**
     * 状态（0停用 1正常）
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 总库存
     */
    private Integer totalStock;

    /**
     * 可用库存
     */
    private Integer availableStock;

    /**
     * 锁定库存
     */
    private Integer lockedStock;

    /**
     * 库存预警值（变体自身的预警库存）
     */
    private Integer alertStock;

    /**
     * 成品库存数量
     */
    private Integer finishedStock;

    /**
     * 待产库存数量
     */
    private Integer pendingStock;

    /**
     * 有效预警库存（考虑多仓库逻辑后的最终预警库存）
     */
    private Integer effectiveAlertStock;

    /**
     * 包装类型
     */
    private Integer packagingType;

    /**
     * 在途库存
     */
    private Integer inTransitStock;
}
