package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品变体列表查询业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WhsProductVariantListBo extends BaseEntity {

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品名称（模糊查询）
     */
    private String productName;

    /**
     * SKU编码（模糊查询）
     */
    private String skuCode;

    /**
     * UPC编码（模糊查询）
     */
    private String upc;

    /**
     * 包装类型（0单品 1展示盒 2整箱）
     */
    private Integer packagingType;

    /**
     * 状态（0停用 1正常）
     */
    private String status;

    /**
     * 库存状态（1有库存 0无库存 2库存预警）
     */
    private Integer stockStatus;

    /**
     * 变体ID
     */
    private Long id;
}
