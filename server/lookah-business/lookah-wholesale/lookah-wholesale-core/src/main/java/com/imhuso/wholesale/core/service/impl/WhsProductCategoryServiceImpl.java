package com.imhuso.wholesale.core.service.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.core.utils.ObjectUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.domain.WhsProduct;
import com.imhuso.wholesale.core.domain.WhsProductCategory;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductCategoryBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductCategoryVo;
import com.imhuso.wholesale.core.mapper.WhsProductCategoryMapper;
import com.imhuso.wholesale.core.mapper.WhsProductMapper;
import com.imhuso.wholesale.core.service.IWhsProductCategoryService;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 批发产品分类Service业务层处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class WhsProductCategoryServiceImpl implements IWhsProductCategoryService {

    private final WhsProductCategoryMapper categoryMapper;
    private final WhsProductMapper productMapper;

    /**
     * 查询产品分类列表
     */
    @Override
    public List<WhsProductCategoryVo> selectCategoryList(WhsProductCategoryBo bo) {
        LambdaQueryWrapper<WhsProductCategory> lqw = buildQueryWrapper(bo);
        return categoryMapper.selectVoList(lqw);
    }

    /**
     * 根据分类ID查询信息
     */
    @Override
    public WhsProductCategoryVo selectCategoryById(Long categoryId) {
        return categoryMapper.selectVoById(categoryId);
    }

    /**
     * 检查分类是否存在子分类
     */
    @Override
    public boolean hasChildByCategoryId(Long categoryId) {
        LambdaQueryWrapper<WhsProductCategory> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WhsProductCategory::getParentId, categoryId);
        return categoryMapper.exists(lqw);
    }

    /**
     * 检查分类是否存在关联商品
     */
    @Override
    public boolean hasProductByCategoryId(Long categoryId) {
        LambdaQueryWrapper<WhsProduct> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WhsProduct::getCategoryId, categoryId);
        return productMapper.exists(lqw);
    }

    /**
     * 检查分类名称是否唯一
     */
    @Override
    public boolean checkCategoryNameUnique(WhsProductCategory category) {
        LambdaQueryWrapper<WhsProductCategory> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WhsProductCategory::getName, category.getName())
            .eq(WhsProductCategory::getParentId, category.getParentId())
            .ne(category.getId() != null, WhsProductCategory::getId, category.getId());
        return !categoryMapper.exists(lqw);
    }

    /**
     * 新增分类
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertCategory(WhsProductCategory category) {
        // 检查分类名称是否唯一
        if (!checkCategoryNameUnique(category)) {
            throw new ServiceException(MessageUtils.message("wholesale.product.category.name.duplicate"));
        }
        return categoryMapper.insert(category);
    }

    /**
     * 修改分类
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateCategory(WhsProductCategoryBo bo) {
        WhsProductCategory category = MapstructUtils.convert(bo, WhsProductCategory.class);
        // 检查分类名称是否唯一
        if (ObjectUtil.isNotNull(category) && !checkCategoryNameUnique(category)) {
            throw new ServiceException(MessageUtils.message("wholesale.product.category.name.duplicate"));
        }
        return categoryMapper.updateById(category);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteCategory(Long categoryId) {
        // 检查是否存在子分类
        if (hasChildByCategoryId(categoryId)) {
            throw new ServiceException(MessageUtils.message("wholesale.product.category.delete.has.child"));
        }
        // 检查是否存在关联商品
        if (hasProductByCategoryId(categoryId)) {
            throw new ServiceException(MessageUtils.message("wholesale.product.category.delete.has.product"));
        }
        return categoryMapper.deleteById(categoryId);
    }

    /**
     * 批量获取分类名称
     */
    @Override
    public Map<Long, String> getCategoryNames(List<Long> categoryIds) {
        if (ObjectUtils.isEmpty(categoryIds)) {
            return Map.of();
        }
        LambdaQueryWrapper<WhsProductCategory> lqw = new LambdaQueryWrapper<>();
        lqw.in(WhsProductCategory::getId, categoryIds)
            .select(WhsProductCategory::getId, WhsProductCategory::getName);
        return categoryMapper.selectList(lqw).stream()
            .collect(Collectors.toMap(WhsProductCategory::getId, WhsProductCategory::getName));
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<WhsProductCategory> buildQueryWrapper(WhsProductCategoryBo bo) {
        LambdaQueryWrapper<WhsProductCategory> lqw = new LambdaQueryWrapper<>();
        if (bo == null) {
            return lqw;
        }
        lqw.eq(bo.getId() != null, WhsProductCategory::getId, bo.getId())
            .eq(bo.getParentId() != null, WhsProductCategory::getParentId, bo.getParentId())
            .like(StringUtils.isNotBlank(bo.getName()), WhsProductCategory::getName, bo.getName())
            .eq(StringUtils.isNotBlank(bo.getStatus()), WhsProductCategory::getStatus, bo.getStatus());
        return lqw;
    }
}
