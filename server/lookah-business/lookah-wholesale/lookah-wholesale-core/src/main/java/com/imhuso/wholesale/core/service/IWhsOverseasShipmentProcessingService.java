package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderShipmentItemBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanItemVo;
import com.imhuso.wholesale.core.domain.vo.warehouse.WarehouseShipmentRecord;

import java.util.List;
import java.util.Map;

/**
 * 订单海外仓发货处理服务接口
 *
 * <AUTHOR>
 */
public interface IWhsOverseasShipmentProcessingService {

    /**
     * 处理订单的海外仓发货流程
     * 修改：接收列表参数并直接修改，返回void
     *
     * @param successfulShipments 用于收集成功发货记录的列表 (In/Out Param)
     * @param orderId             订单ID
     * @param order               订单视图对象 (包含收货地址等信息)
     * @param packageIdMap        包裹ID映射 (物流方式ID-仓库ID -> 包裹ID)
     * @param shipmentItems       此次发货的发货项列表 (Bo)
     * @param planItemMap         计划项映射 (planItemId -> planItemVo)
     * @param batchNumber         本次发货操作的批次号
     */
    void processShipments(List<WarehouseShipmentRecord> successfulShipments,
                          Long orderId, WhsOrderVo order, Map<String, Long> packageIdMap,
                          List<WhsOrderShipmentItemBo> shipmentItems,
                          Map<Long, WhsShipmentPlanItemVo> planItemMap,
                          Integer batchNumber);

}
