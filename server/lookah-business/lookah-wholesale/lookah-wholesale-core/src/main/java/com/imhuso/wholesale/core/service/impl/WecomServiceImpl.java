package com.imhuso.wholesale.core.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.imhuso.common.core.config.WecomConfig;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.service.IWecomService;
import com.imhuso.wholesale.core.service.INotificationConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 企业微信服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WecomServiceImpl implements IWecomService {

    private static final String WEBHOOK_BASE_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send";
    private static final String UPLOAD_BASE_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media";

    private final WecomConfig wecomConfig;
    private final INotificationConfigService notificationConfigService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    private RestTemplate restTemplate;

    /**
     * 初始化RestTemplate with timeout配置
     */
    private RestTemplate initRestTemplate() {
        if (restTemplate == null) {
            org.springframework.http.client.SimpleClientHttpRequestFactory factory =
                new org.springframework.http.client.SimpleClientHttpRequestFactory();
            factory.setConnectTimeout(wecomConfig.getConnectTimeout());
            factory.setReadTimeout(wecomConfig.getReadTimeout());
            restTemplate = new RestTemplate(factory);
        }
        return restTemplate;
    }


    @Override
    public void sendMarkdownMessage(String title, String content) {
        // 从数据库获取默认的企业微信配置
        List<String> defaultKeys = notificationConfigService.getDefaultWecomKeys();

        if (defaultKeys.isEmpty()) {
            log.error("企业微信通知失败: 未配置默认webhook密钥 (wholesale.notification.wecom.list)，标题: {}", title);
            return;
        }

        // 使用第一个配置的密钥作为默认密钥
        String defaultKey = defaultKeys.get(0);
        log.debug("使用默认企业微信配置发送消息，密钥: {}..., 标题: {}",
            defaultKey.substring(0, Math.min(8, defaultKey.length())), title);

        sendMarkdownMessage(title, content, defaultKey);
    }

    @Override
    public void sendMarkdownMessage(String title, String content, String webhookKey) {
        if (!isEnabled() || StringUtils.isEmpty(content)) {
            log.debug("企业微信消息未发送 - 原因: {}, 标题: {}", !isEnabled() ? "服务未启用" : "内容为空", title);
            return;
        }

        if (StringUtils.isEmpty(webhookKey)) {
            log.warn("未配置企业微信 webhook key，无法发送消息: {}", title);
            return;
        }

        try {
            String webhookUrl = WEBHOOK_BASE_URL + "?key=" + webhookKey;
            log.debug("准备发送企业微信消息 - 标题: {}, Webhook URL: {}", title, webhookUrl);

            Map<String, Object> messageMap = new HashMap<>();
            messageMap.put("msgtype", "markdown");

            Map<String, String> markdownMap = new HashMap<>();
            markdownMap.put("content", content);
            messageMap.put("markdown", markdownMap);

            ResponseEntity<String> response = sendHttpRequest(webhookUrl, messageMap);

            boolean success = response.getStatusCode().is2xxSuccessful();
            if (success) {
                log.debug("企业微信消息发送成功 - 标题: {}, 响应状态: {}, 响应内容: {}", title, response.getStatusCode(), response.getBody());
                log.info("企业微信消息发送成功: {}", title);
            } else {
                log.debug("企业微信消息发送失败 - 标题: {}, 响应状态: {}, 响应内容: {}", title, response.getStatusCode(), response.getBody());
                log.error("企业微信消息发送失败: {}, 状态码: {}", title, response.getStatusCode());
            }
        } catch (Exception e) {
            log.debug("企业微信消息发送异常 - 标题: {}, 异常类型: {}, 异常消息: {}", title, e.getClass().getName(), e.getMessage());
            log.error("企业微信消息发送失败: {}, 错误: {}", title, e.getMessage(), e);
        }
    }

    @Override
    public boolean sendFileMessage(File file) {
        // 从数据库获取默认的企业微信配置
        List<String> defaultKeys = notificationConfigService.getDefaultWecomKeys();

        if (defaultKeys.isEmpty()) {
            log.error("企业微信文件发送失败: 未配置默认webhook密钥 (wholesale.notification.wecom.list)，文件: {}",
                file.getName());
            return false;
        }

        // 使用第一个配置的密钥作为默认密钥
        String defaultKey = defaultKeys.get(0);
        log.debug("使用默认企业微信配置发送文件，密钥: {}..., 文件: {}",
            defaultKey.substring(0, Math.min(8, defaultKey.length())),
            file.getName());

        return sendFileMessage(file, defaultKey);
    }

    @Override
    public boolean sendFileMessage(File file, String webhookKey) {
        if (!isEnabled() || file == null || !file.exists()) {
            log.debug("企业微信文件消息未发送 - 原因: {}, 文件: {}",
                !isEnabled() ? "服务未启用" : "文件不存在",
                file != null ? file.getName() : "null");
            return false;
        }

        if (StringUtils.isEmpty(webhookKey)) {
            log.warn("未配置企业微信 webhook key，无法发送文件消息: {}", file.getName());
            return false;
        }

        try {
            // 1. 上传文件获取media_id
            String mediaId = uploadFile(file, webhookKey);
            if (StringUtils.isEmpty(mediaId)) {
                log.error("文件上传失败，无法发送企业微信文件消息: {}", file.getName());
                return false;
            }

            // 2. 发送文件消息
            return sendFileMessageByMediaId(mediaId, webhookKey);
        } catch (Exception e) {
            log.error("企业微信文件消息发送失败: {}, 错误: {}", file.getName(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void sendMarkdownMessageWithFile(String title, String content, File file) {
        // 从数据库获取默认的企业微信配置
        List<String> defaultKeys = notificationConfigService.getDefaultWecomKeys();

        if (defaultKeys.isEmpty()) {
            log.error("企业微信消息+文件发送失败: 未配置默认webhook密钥 (wholesale.notification.wecom.list)，标题: {}", title);
            return;
        }

        // 使用第一个配置的密钥作为默认密钥
        String defaultKey = defaultKeys.get(0);
        log.debug("使用默认企业微信配置发送消息+文件，密钥: {}..., 标题: {}",
            defaultKey.substring(0, Math.min(8, defaultKey.length())), title);

        sendMarkdownMessageWithFile(title, content, file, defaultKey);
    }

    @Override
    public void sendMarkdownMessageWithFile(String title, String content, File file, String webhookKey) {
        if (!isEnabled()) {
            log.debug("企业微信消息未发送 - 服务未启用, 标题: {}", title);
            return;
        }

        if (StringUtils.isEmpty(webhookKey)) {
            log.warn("未配置企业微信 webhook key，无法发送消息: {}", title);
            return;
        }

        // 先发送Markdown消息
        sendMarkdownMessage(title, content, webhookKey);

        // 再发送文件（如果文件存在）
        if (file != null && file.exists()) {
            boolean fileSuccess = sendFileMessage(file, webhookKey);
            if (fileSuccess) {
                log.info("企业微信消息和文件发送成功: {}, 文件: {}", title, file.getName());
            } else {
                log.warn("企业微信消息发送成功但文件发送失败: {}, 文件: {}", title, file.getName());
            }
        } else {
            log.info("企业微信消息发送成功: {}", title);
        }
    }

    @Override
    public boolean isEnabled() {
        // 使用 WecomConfig 配置
        boolean configured = wecomConfig.isAvailable();
        log.debug("企业微信服务状态: {}, configured={}",
            configured ? "已启用" : "未启用", configured);
        return configured;
    }

    /**
     * 上传文件到企业微信服务器
     *
     * @param file       要上传的文件
     * @param webhookKey 企业微信webhook密钥
     * @return media_id，上传失败返回null
     */
    private String uploadFile(File file, String webhookKey) {
        try {
            if (StringUtils.isEmpty(webhookKey)) {
                log.warn("未配置企业微信 webhook key，无法上传文件: {}", file.getName());
                return null;
            }

            String uploadUrl = UPLOAD_BASE_URL + "?key=" + webhookKey + "&type=file";
            log.debug("准备上传文件到企业微信 - 文件: {}, 上传URL: {}", file.getName(), uploadUrl);

            // 构建multipart/form-data请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            FileSystemResource fileResource = new FileSystemResource(file);
            body.add("media", fileResource);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
            ResponseEntity<String> response = initRestTemplate().postForEntity(uploadUrl, requestEntity, String.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                int errCode = jsonNode.get("errcode").asInt();

                if (errCode == 0) {
                    String mediaId = jsonNode.get("media_id").asText();
                    log.info("文件上传成功: {}, media_id: {}", file.getName(), mediaId);
                    return mediaId;
                } else {
                    log.error("文件上传失败: {}, 错误码: {}, 响应: {}", file.getName(), errCode, response.getBody());
                    return null;
                }
            } else {
                log.error("文件上传失败: {}, 状态码: {}, 响应: {}", file.getName(), response.getStatusCode(), response.getBody());
                return null;
            }
        } catch (Exception e) {
            log.error("文件上传异常: {}, 错误: {}", file.getName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 通过media_id发送文件消息
     *
     * @param mediaId    文件的media_id
     * @param webhookKey 企业微信webhook密钥
     * @return 是否发送成功
     */
    private boolean sendFileMessageByMediaId(String mediaId, String webhookKey) {
        try {
            if (StringUtils.isEmpty(webhookKey)) {
                log.warn("未配置企业微信 webhook key，无法发送文件消息");
                return false;
            }

            String webhookUrl = WEBHOOK_BASE_URL + "?key=" + webhookKey;
            log.debug("准备发送企业微信文件消息 - media_id: {}, Webhook URL: {}", mediaId, webhookUrl);

            Map<String, Object> messageMap = new HashMap<>();
            messageMap.put("msgtype", "file");

            Map<String, String> fileMap = new HashMap<>();
            fileMap.put("media_id", mediaId);
            messageMap.put("file", fileMap);

            ResponseEntity<String> response = sendHttpRequest(webhookUrl, messageMap);

            boolean success = response.getStatusCode().is2xxSuccessful();
            if (success) {
                log.debug("企业微信文件消息发送成功 - media_id: {}, 响应状态: {}, 响应内容: {}", mediaId, response.getStatusCode(), response.getBody());
                log.info("企业微信文件消息发送成功: media_id={}", mediaId);
                return true;
            } else {
                log.debug("企业微信文件消息发送失败 - media_id: {}, 响应状态: {}, 响应内容: {}", mediaId, response.getStatusCode(), response.getBody());
                log.error("企业微信文件消息发送失败: media_id={}, 状态码: {}", mediaId, response.getStatusCode());
                return false;
            }
        } catch (Exception e) {
            log.debug("企业微信文件消息发送异常 - media_id: {}, 异常类型: {}, 异常消息: {}", mediaId, e.getClass().getName(), e.getMessage());
            log.error("企业微信文件消息发送失败: media_id={}, 错误: {}", mediaId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送 HTTP 请求的公共方法
     *
     * @param webhookUrl 企业微信 webhook URL
     * @param messageMap 消息内容映射
     * @return HTTP 响应
     */
    private ResponseEntity<String> sendHttpRequest(String webhookUrl, Map<String, Object> messageMap) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(messageMap, headers);
        return initRestTemplate().postForEntity(webhookUrl, entity, String.class);
    }
}
