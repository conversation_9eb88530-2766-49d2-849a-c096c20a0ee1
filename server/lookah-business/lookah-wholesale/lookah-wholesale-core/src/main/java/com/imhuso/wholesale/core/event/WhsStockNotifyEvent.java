package com.imhuso.wholesale.core.event;

import com.imhuso.wholesale.core.domain.WhsStockNotify;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 缺货通知事件
 *
 * <AUTHOR>
 */
@Getter
public class WhsStockNotifyEvent extends ApplicationEvent {

    /**
     * 事件类型
     */
    private final StockNotifyEventType eventType;

    /**
     * 缺货通知对象
     */
    private final WhsStockNotify stockNotify;

    public WhsStockNotifyEvent(Object source, StockNotifyEventType eventType, WhsStockNotify stockNotify) {
        super(source);
        this.eventType = eventType;
        this.stockNotify = stockNotify;
    }

    /**
     * 缺货通知事件类型枚举
     */
    public enum StockNotifyEventType {
        /**
         * 注册通知
         */
        NOTIFY_REGISTERED,

        /**
         * 发送通知
         */
        NOTIFY_SENT
    }
}
