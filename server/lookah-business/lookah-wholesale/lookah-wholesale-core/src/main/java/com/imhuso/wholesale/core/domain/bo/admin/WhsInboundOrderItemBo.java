package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.wholesale.core.domain.WhsInboundOrderItem;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 入库单明细业务对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsInboundOrderItem.class)
public class WhsInboundOrderItemBo {

    /**
     * 明细ID
     */
    @NotNull(message = "明细ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 入库单ID
     */
    private Long inboundOrderId;

    /**
     * 变体ID
     */
    @NotNull(message = "变体ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long variantId;

    /**
     * SKU编码
     */
    @NotBlank(message = "SKU编码不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 50, message = "SKU编码长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String skuCode;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空", groups = {AddGroup.class, EditGroup.class})
    @Min(value = 1, message = "数量必须大于0", groups = {AddGroup.class, EditGroup.class})
    private Integer quantity;

    /**
     * 已接收数量
     */
    @Min(value = 0, message = "已接收数量不能小于0", groups = {AddGroup.class, EditGroup.class})
    private Integer receivedQuantity;
}
