package com.imhuso.wholesale.core.satoken;

import com.imhuso.wholesale.core.model.WholesaleLoginUser;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import com.imhuso.wholesale.core.constant.WholesaleConstants;
import com.imhuso.wholesale.core.satoken.utils.StpWholesaleUtil;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpLogic;
import cn.dev33.satoken.stp.parameter.SaLoginParameter;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * Wholesale 模块专用登录鉴权助手
 * 参考 LoginHelper 的设计风格，专门处理批发用户的登录、权限验证等操作
 * 与系统管理员登录完全隔离
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@DependsOn("stpWholesaleUtil")
public class WholesaleLoginHelper {

    public static final String LOGIN_USER_KEY = "loginUser";
    public static final String USER_KEY = "userId";
    public static final String USER_NAME_KEY = "userName";
    public static final String CLIENT_ID = WholesaleConstants.Client.ID_KEY;

    private static StpLogic stpLogic;

    /**
     * 获取 StpLogic 实例，如果为空则尝试重新获取
     */
    private static StpLogic getStpLogic() {
        if (stpLogic == null) {
            stpLogic = StpWholesaleUtil.getStpLogic();
        }
        return stpLogic;
    }

    /**
     * Wholesale 用户登录
     *
     * @param loginUser 登录用户信息
     * @param model     配置参数
     */
    public static void login(WholesaleLoginUser loginUser, SaLoginParameter model) {
        model = ObjectUtil.defaultIfNull(model, new SaLoginParameter());

        // 设置用户类型为 WHOLESALE_USER
        loginUser.setUserType(WholesaleConstants.SaToken.USER_TYPE);

        getStpLogic().login(loginUser.getLoginId(), model
            .setExtra(USER_KEY, loginUser.getUserId())
            .setExtra(USER_NAME_KEY, loginUser.getUsername()));

        getStpLogic().getTokenSession().set(LOGIN_USER_KEY, loginUser);

        log.debug("Wholesale 用户登录成功，用户ID: {}, 用户名: {}", loginUser.getUserId(), loginUser.getUsername());
    }

    /**
     * Wholesale 用户登录（简化版本）
     */
    public static void login(WholesaleLoginUser loginUser) {
        login(loginUser, new SaLoginParameter());
    }

    /**
     * 获取当前登录的 Wholesale 用户
     */
    public static WholesaleLoginUser getLoginUser() {
        try {
            SaSession session = getStpLogic().getTokenSession();
            if (ObjectUtil.isNull(session)) {
                return null;
            }
            return (WholesaleLoginUser) session.get(LOGIN_USER_KEY);
        } catch (Exception e) {
            // 在异步环境中，SaToken上下文未初始化是正常情况，静默处理
            if (e instanceof cn.dev33.satoken.exception.SaTokenContextException) {
                log.debug("SaToken上下文未初始化，无法获取登录用户: {}", e.getMessage());
            } else {
                log.debug("获取登录用户时发生异常: {}", e.getMessage());
            }
            return null;
        }
    }

    /**
     * 获取当前用户ID
     */
    public static Long getUserId() {
        Object userId = getExtra();
        if (ObjectUtil.isNull(userId)) {
            return null;
        }
        return Long.valueOf(userId.toString());
    }

    /**
     * 获取当前 Token
     */
    public static String getToken() {
        return getStpLogic().getTokenValue();
    }

    /**
     * 检查当前用户是否已登录
     */
    public static boolean isLogin() {
        try {
            return getStpLogic().isLogin() && getLoginUser() != null;
        } catch (Exception e) {
            // 在异步环境（如定时任务）中，SaToken上下文未初始化是正常情况，使用debug级别
            if (e instanceof cn.dev33.satoken.exception.SaTokenContextException) {
                log.debug("SaToken上下文未初始化（可能在异步环境中）: {}", e.getMessage());
            } else {
                log.warn("检查登录状态时发生异常", e);
            }
            return false;
        }
    }

    /**
     * 当前用户注销登录
     */
    public static void logout() {
        try {
            getStpLogic().logout();
            log.debug("Wholesale 用户注销成功");
        } catch (Exception e) {
            log.warn("Wholesale 用户注销时发生异常", e);
        }
    }

    /**
     * 获取当前 Token 的扩展信息
     */
    private static Object getExtra() {
        try {
            return getStpLogic().getExtra(WholesaleLoginHelper.USER_KEY);
        } catch (Exception e) {
            // 在异步环境中，SaToken上下文未初始化是正常情况，静默处理
            if (e instanceof cn.dev33.satoken.exception.SaTokenContextException) {
                log.debug("SaToken上下文未初始化，无法获取扩展信息: {}", e.getMessage());
            } else {
                log.debug("获取Token扩展信息时发生异常: {}", e.getMessage());
            }
            return null;
        }
    }
}
