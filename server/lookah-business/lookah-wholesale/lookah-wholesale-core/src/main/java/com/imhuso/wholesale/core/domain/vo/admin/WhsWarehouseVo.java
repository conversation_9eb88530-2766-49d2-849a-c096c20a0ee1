package com.imhuso.wholesale.core.domain.vo.admin;

import com.imhuso.wholesale.core.domain.WhsWarehouse;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 仓库视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsWarehouse.class)
public class WhsWarehouseVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 仓库ID
     */
    private Long id;

    /**
     * 仓库名称
     */
    private String name;

    /**
     * 仓库编码
     */
    private String code;

    /**
     * 仓库地址
     */
    private String address;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 状态（0停用 1正常）
     */
    private String status;

    /**
     * 是否默认（0否 1是）
     */
    private String isDefault;

    /**
     * 仓库级别告警库存阈值
     * 默认为0表示使用变体级别告警库存
     */
    private Integer alertStock;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
