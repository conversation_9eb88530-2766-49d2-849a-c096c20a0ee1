package com.imhuso.wholesale.core.adapter;

import com.imhuso.common.core.utils.MessageUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ERP提供商工厂
 * 负责管理和创建ERP提供商实例
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ErpProviderFactory {

    private final List<IErpProvider> erpProviders;

    /**
     * 根据提供商类型获取ERP提供商
     *
     * @param providerType 提供商类型
     * @return ERP提供商实例
     */
    public Optional<IErpProvider> getProvider(String providerType) {
        if (providerType == null || providerType.trim().isEmpty()) {
            log.warn(MessageUtils.message("erp.provider.type.empty"));
            return Optional.empty();
        }

        return erpProviders.stream()
                .filter(provider -> providerType.equalsIgnoreCase(provider.getProviderType()))
                .findFirst();
    }

    /**
     * 获取默认的ERP提供商（Lookah ERP）
     *
     * @return 默认ERP提供商
     */
    public Optional<IErpProvider> getDefaultProvider() {
        return getProvider("LOOKAH");
    }

    /**
     * 获取第一个可用的ERP提供商
     *
     * @return 可用的ERP提供商
     */
    public Optional<IErpProvider> getAvailableProvider() {
        log.info("查找可用的ERP提供商，总共有{}个提供商", erpProviders.size());

        for (IErpProvider provider : erpProviders) {
            log.info("检查ERP提供商: {}, 类型: {}", provider.getProviderName(), provider.getProviderType());
            boolean available = provider.isAvailable();
            log.info("ERP提供商 {} 可用性检查结果: {}", provider.getProviderName(), available);
            if (available) {
                log.info("找到可用的ERP提供商: {}", provider.getProviderName());
                return Optional.of(provider);
            }
        }

        log.warn("没有找到可用的ERP提供商");
        return Optional.empty();
    }

    /**
     * 获取所有ERP提供商
     *
     * @return 所有ERP提供商列表
     */
    public List<IErpProvider> getAllProviders() {
        return erpProviders;
    }

    /**
     * 获取所有可用的ERP提供商
     *
     * @return 可用的ERP提供商列表
     */
    public List<IErpProvider> getAvailableProviders() {
        return erpProviders.stream()
                .filter(IErpProvider::isAvailable)
                .collect(Collectors.toList());
    }

    /**
     * 获取ERP提供商映射表
     *
     * @return 提供商类型到提供商实例的映射
     */
    public Map<String, IErpProvider> getProviderMap() {
        return erpProviders.stream()
                .collect(Collectors.toMap(
                        IErpProvider::getProviderType,
                        Function.identity(),
                        (existing, replacement) -> {
                            log.warn(MessageUtils.message("erp.provider.duplicate.type"),
                                    replacement.getProviderType());
                            return existing;
                        }
                ));
    }

    /**
     * 检查是否存在指定类型的提供商
     *
     * @param providerType 提供商类型
     * @return 是否存在
     */
    public boolean hasProvider(String providerType) {
        return getProvider(providerType).isPresent();
    }

    /**
     * 获取ERP提供商状态信息
     *
     * @return 状态信息
     */
    public String getProvidersStatus() {
        if (erpProviders.isEmpty()) {
            return MessageUtils.message("erp.provider.none.available");
        }

        StringBuilder status = new StringBuilder();
        status.append(MessageUtils.message("erp.provider.status.header")).append("\n");

        for (IErpProvider provider : erpProviders) {
            status.append(String.format("- %s (%s): %s\n",
                    provider.getProviderName(),
                    provider.getProviderType(),
                    provider.isAvailable() ?
                            MessageUtils.message("erp.provider.status.available") :
                            MessageUtils.message("erp.provider.status.unavailable")
            ));
        }

        return status.toString();
    }

    /**
     * 初始化后检查提供商状态
     */
    public void checkProvidersOnStartup() {
        log.info(MessageUtils.message("erp.provider.startup.check"), erpProviders.size());

        for (IErpProvider provider : erpProviders) {
            boolean available = provider.isAvailable();
            log.info(MessageUtils.message("erp.provider.startup.status"),
                    provider.getProviderName(),
                    provider.getProviderType(),
                    available ? "可用" : "不可用");
        }

        long availableCount = erpProviders.stream()
                .mapToLong(provider -> provider.isAvailable() ? 1 : 0)
                .sum();

        log.info(MessageUtils.message("erp.provider.startup.summary"),
                availableCount, erpProviders.size());
    }
}
