package com.imhuso.wholesale.core.domain.vo.admin;

import java.io.Serial;
import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发货方案生成项视图对象
 * 用于自定义方案准备时提供可用的包装类型信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WhsPackagingOptionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 包装类型
     */
    private Integer packagingType;

    /**
     * 包装类型文本
     */
    private String packagingTypeText;

    /**
     * 包装数量 (每计划单位包含的原始物品数)
     */
    private Integer packagingQuantity;

    /**
     * 包含变体ID
     */
    private Long childVariantId;
}
