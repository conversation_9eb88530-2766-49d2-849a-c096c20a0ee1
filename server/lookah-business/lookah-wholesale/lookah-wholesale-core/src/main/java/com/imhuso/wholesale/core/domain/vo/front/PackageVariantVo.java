package com.imhuso.wholesale.core.domain.vo.front;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 包装变体视图对象 (优化后的前端显示版本)
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PackageVariantVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 变体ID
     */
    private Long id;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * UPC编码
     */
    private String upc;

    /**
     * 批发价格(Wholesale)
     */
    private BigDecimal wholesalePrice;

    /**
     * 建议零售价(MSRP)
     */
    private BigDecimal msrp;

    /**
     * 主图URL
     */
    private String mainImage;

    /**
     * 标题（产品名称 + 变体属性，如：xxx - 黑色 - 陶瓷）
     */
    private String title;

    /**
     * 包装类型（1展示盒 2箱装）
     */
    private Integer packagingType;

    /**
     * 可用库存
     */
    private Integer availableStock;

    /**
     * 包含的单品件数
     */
    private Integer pcs;

    /**
     * 变体规格属性，JSON格式，包含属性名和属性值
     */
    private String specs;
}
