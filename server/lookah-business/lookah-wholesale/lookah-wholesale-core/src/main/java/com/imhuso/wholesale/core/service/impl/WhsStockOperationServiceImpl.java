package com.imhuso.wholesale.core.service.impl;

import com.baomidou.lock.annotation.Lock4j;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.wholesale.core.constant.WhsCacheConstants;
import com.imhuso.wholesale.core.domain.WhsStock;
import com.imhuso.wholesale.core.enums.StockOperationType;
import com.imhuso.wholesale.core.mapper.WhsStockMapper;
import com.imhuso.wholesale.core.service.IWhsStockCacheService;
import com.imhuso.wholesale.core.service.IWhsStockLogService;
import com.imhuso.wholesale.core.service.IWhsStockOperationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.OptimisticLockingFailureException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * 库存操作服务实现类
 * 专门处理库存更新的核心逻辑，确保事务正确传播
 * 使用Lock4j分布式锁确保高并发下的数据一致性
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsStockOperationServiceImpl implements IWhsStockOperationService {

    private final WhsStockMapper stockMapper;
    private final IWhsStockLogService stockLogService;
    private final IWhsStockCacheService stockCacheService;

    /**
     * 统一库存操作方法
     * <p>
     * 所有库存更新操作的统一入口，使用Lock4j分布式锁实现高效的并发控制
     * 1. 通过精细粒度的锁控制提高并发性能
     * 2. 使用缓存服务减少数据库访问
     *
     * @param stock         库存对象
     * @param operationType 操作类型
     * @param quantity      数量（正数增加，负数减少）
     * @param orderId       订单ID（可选）
     * @param orderItemId   订单项ID（可选）
     * @param remark        备注
     * @return 实际操作的数量
     */
    @Override
    @Lock4j(name = WhsCacheConstants.STOCK_LOCK_PREFIX + "operation:#{#stock.id}", acquireTimeout = 10000, expire = 15000)
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int updateStock(WhsStock stock, StockOperationType operationType, int quantity,
                           Long orderId, Long orderItemId, String remark) {
        // 参数校验
        if (stock == null || stock.getId() == null) {
            throw new ServiceException("库存对象不能为空");
        }
        if (quantity == 0) {
            return 0;
        }

        log.debug("库存操作开始: stockId={}, operationType={}, quantity={}, orderId={}, orderItemId={}",
            stock.getId(), operationType, quantity, orderId, orderItemId);

        try {
            // 获取最新库存状态
            WhsStock currentStock = stockMapper.selectById(stock.getId());
            if (currentStock == null) {
                throw new ServiceException("库存记录不存在");
            }
            log.debug("获取当前库存状态: {}", currentStock);

            // 记录更新前的库存值
            int beforeAvailable = currentStock.getAvailableStock();
            int beforeLocked = currentStock.getLockedStock();
            int beforeTotal = currentStock.getTotalStock();

            // 计算新的库存值并验证 (异常将在 calculateNewStockValues 内部抛出)
            StockUpdateResult result = calculateNewStockValues(
                currentStock, operationType, quantity);

            // 更新库存（添加乐观锁逻辑）
            log.debug("准备更新库存: currentStock={}, newValues={}", currentStock, result.values());
            boolean updated = updateStockWithOptimisticLock(currentStock, result.values());
            if (!updated) {
                log.warn("库存更新失败，数据已被并发修改: stockId={}, 操作类型={}",
                    stock.getId(), operationType);
                return 0;
            }

            // 根据操作类型选择记录的库存类型的before/after值
            int beforeStock;
            int afterStock;
            String stockTypeName;

            switch (operationType) {
                case LOCK, RELEASE -> {
                    // 记录锁定库存的变化
                    beforeStock = beforeLocked;
                    afterStock = result.values().lockedStock();
                    stockTypeName = "锁定库存";
                }
                case OUTBOUND, DEDUCT, ADJUST, SYNC, CREATE -> {
                    // 记录可用库存的变化
                    beforeStock = beforeAvailable;
                    afterStock = result.values().availableStock();
                    stockTypeName = "可用库存";
                }
                default -> {
                    // 默认记录可用库存的变化
                    beforeStock = beforeAvailable;
                    afterStock = result.values().availableStock();
                    stockTypeName = "可用库存";
                }
            }

            // 生成包含库存类型信息的备注
            String typeInfoRemark = remark;
            if (!typeInfoRemark.contains("库存类型:")) {
                typeInfoRemark = String.format("%s (库存类型: %s)", remark, stockTypeName);
            }

            // 记录库存操作日志
            stockLogService.createStockLog(
                currentStock,
                operationType,
                result.actualQuantity(),
                beforeStock,  // 根据操作类型传入相应的库存类型值
                afterStock,   // 根据操作类型传入相应的库存类型值
                orderId,
                orderItemId,
                typeInfoRemark);

            // 在事务提交后更新缓存
            registerCacheUpdateAfterCommit(currentStock);

            // 更新传入对象状态
            stock.setAvailableStock(result.values().availableStock());
            stock.setLockedStock(result.values().lockedStock());
            stock.setTotalStock(result.values().totalStock());

            log.info("库存操作成功: id={}, 类型={}, 数量={}, 可用库存:{}→{}, 锁定库存:{}→{}, 总库存:{}→{}",
                stock.getId(), operationType, result.actualQuantity(),
                beforeAvailable, result.values().availableStock(),
                beforeLocked, result.values().lockedStock(),
                beforeTotal, result.values().totalStock());

            return result.actualQuantity();
        } catch (OptimisticLockingFailureException e) {
            log.warn("库存并发更新冲突: stockId={}, 操作类型={}", stock.getId(), operationType);
            return 0;
        } catch (Exception e) {
            log.error("库存操作异常: stockId={}, 操作类型={}, 异常={}",
                stock.getId(), operationType, e.getMessage());
            throw new ServiceException("库存操作失败: " + e.getMessage());
        }
    }

    /**
     * 使用乐观锁更新库存数据
     *
     * @param currentStock 当前库存对象
     * @param newValues    新库存值
     * @return 是否更新成功
     */
    private boolean updateStockWithOptimisticLock(WhsStock currentStock, StockUpdateValues newValues) {
        // 设置新的库存值
        currentStock.setAvailableStock(newValues.availableStock());
        currentStock.setLockedStock(newValues.lockedStock());
        currentStock.setTotalStock(newValues.totalStock());

        // 利用MyBatis-Plus的updateById进行更新，它会使用version字段作为乐观锁
        // 如果stock表有version字段，会自动处理乐观锁逻辑
        log.debug("执行乐观锁更新: stock={}", currentStock);
        int rows = stockMapper.updateById(currentStock);
        log.debug("乐观锁更新结果: rows={}", rows);
        return rows > 0;
    }

    /**
     * 注册事务提交后的缓存更新
     */
    private void registerCacheUpdateAfterCommit(final WhsStock stock) {
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    try {
                        stockCacheService.updateStockCache(stock);
                    } catch (Exception e) {
                        log.error("事务提交后更新缓存失败: stockId={}, error={}",
                            stock.getId(), e.getMessage());
                    }
                }
            });
        } else {
            try {
                stockCacheService.updateStockCache(stock);
            } catch (Exception e) {
                log.error("非事务环境下更新缓存失败: stockId={}, error={}",
                    stock.getId(), e.getMessage());
            }
        }
    }

    /**
     * 计算新的库存值并验证是否满足库存条件
     */
    private StockUpdateResult calculateNewStockValues(
        WhsStock stock, StockOperationType operationType, int quantity) {

        log.debug("计算库存变化值: stock={}, operationType={}, quantity={}", stock, operationType, quantity);

        int beforeAvailable = stock.getAvailableStock();
        int beforeLocked = stock.getLockedStock();
        int beforeTotal = stock.getTotalStock();

        int newAvailable = beforeAvailable;
        int newLocked = beforeLocked;
        int newTotal = beforeTotal;
        int actualQuantity = quantity; // 实际使用的数量可能与请求数量不同

        switch (operationType) {
            case LOCK -> {
                if (quantity > beforeAvailable) {
                    String errorMsg = String.format("可用库存不足，无法锁定: stockId=%d, 当前可用=%d, 需要锁定=%d",
                        stock.getId(), beforeAvailable, quantity);
                    log.warn(errorMsg);
                    throw new ServiceException(errorMsg);
                } else {
                    newAvailable = beforeAvailable - quantity;
                    newLocked = beforeLocked + quantity;
                }
            }
            case RELEASE -> {
                if (quantity > beforeLocked) {
                    // 如果要释放的数量超过锁定库存，只释放锁定的部分
                    log.warn("要释放的数量超过锁定库存，将只释放全部锁定库存: stockId={}, 当前锁定={}, 请求释放={}",
                        stock.getId(), beforeLocked, quantity);

                    // 使用实际锁定的量进行计算
                    actualQuantity = beforeLocked;
                    newAvailable = beforeAvailable + actualQuantity;
                    newLocked = 0; // 全部释放
                } else {
                    newAvailable = beforeAvailable + quantity;
                    newLocked = beforeLocked - quantity;
                }
            }
            case OUTBOUND, DEDUCT -> {
                int deductAmount = Math.abs(quantity);

                if (deductAmount == 0) {
                    log.debug("OUTBOUND/DEDUCT 操作数量为 0，无需变更: stockId={}", stock.getId());
                    actualQuantity = 0;
                    break;
                }

                if (deductAmount > beforeAvailable) {
                    String errorMsg = String.format("可用库存不足，无法出库/扣减: stockId=%d, 当前可用=%d, 需要=%d",
                        stock.getId(), beforeAvailable, deductAmount);
                    log.warn(errorMsg);
                    throw new ServiceException(errorMsg);
                } else {
                    newAvailable = beforeAvailable - deductAmount;
                    newTotal = beforeTotal - deductAmount;
                    actualQuantity = quantity < 0 ? -deductAmount : deductAmount; // 保持原始正负号

                    log.debug("OUTBOUND/DEDUCT操作计算: 原始quantity={}, 处理后数量={}, 可用库存:{}→{}, 总库存:{}→{}",
                        quantity, deductAmount, beforeAvailable, newAvailable, beforeTotal, newTotal);
                }
            }
            case ADJUST, SYNC -> {

                if (quantity == 0) {
                    log.debug("ADJUST/SYNC 操作数量为 0，无需变更: stockId={}", stock.getId());
                    actualQuantity = 0;
                    break;
                }

                if (quantity < 0 && Math.abs(quantity) > beforeAvailable) {
                    String errorMsg = String.format("可用库存不足，无法减少: stockId=%d, 当前可用=%d, 需要减少=%d",
                        stock.getId(), beforeAvailable, Math.abs(quantity));
                    log.warn(errorMsg);
                    throw new ServiceException(errorMsg);
                } else {
                    newAvailable = beforeAvailable + quantity;
                    newTotal = beforeTotal + quantity;
                    log.debug("ADJUST/SYNC操作计算: 调整数量={}, 可用库存:{}→{}, 总库存:{}→{}",
                        quantity, beforeAvailable, newAvailable, beforeTotal, newTotal);
                }
            }
            default -> throw new ServiceException("未支持的库存操作类型: " + operationType);
        }

        StockUpdateValues values = new StockUpdateValues(newAvailable, newLocked, newTotal);
        StockUpdateResult result = new StockUpdateResult(values, actualQuantity);
        log.debug("库存计算结果: {}", result);
        return result;
    }

    /**
     * 库存更新值对象
     */
    private record StockUpdateValues(int availableStock, int lockedStock, int totalStock) {
    }

    /**
     * 库存更新结果对象，包含更新后的库存值和实际操作的数量
     */
    private record StockUpdateResult(StockUpdateValues values, int actualQuantity) {
    }
}
