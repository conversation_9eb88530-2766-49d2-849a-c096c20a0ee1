package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 库存分配对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_stock_allocation")
public class WhsStockAllocation extends BaseEntity {

    /**
     * 分配ID
     */
    @TableId
    private Long id;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * 分配数量
     */
    private Integer quantity;

    /**
     * 已释放数量
     */
    private Integer releasedQuantity;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单项ID
     */
    private Long orderItemId;

    /**
     * 状态：0-待处理 1-已分配 2-已释放 3-已发货
     *
     * @see com.imhuso.wholesale.enums.StockAllocationStatus
     */
    private Integer status;
}
