package com.imhuso.wholesale.core.constant;

import com.imhuso.wholesale.core.constant.WholesaleConstants.Module;

/**
 * 批发缓存常量
 *
 * <AUTHOR>
 */
public interface WhsCacheConstants {
    /**
     * 批发缓存前缀
     */
    String WHOLESALE_CACHE_PREFIX = Module.NAME;

    /**
     * 产品层级缓存key前缀（用于按产品ID缓存）
     */
    String PRODUCT_HIERARCHY_CACHE_PREFIX = WHOLESALE_CACHE_PREFIX + ":product:hierarchy";

    /**
     * 产品层级缓存key（当前用于缓存所有产品）
     */
    String PRODUCT_HIERARCHY_CACHE_KEY = PRODUCT_HIERARCHY_CACHE_PREFIX + ":all";

    /**
     * 库存缓存前缀
     */
    String STOCK_VARIANT_CACHE_PREFIX = WHOLESALE_CACHE_PREFIX + ":stock:variant:";

    /**
     * 国家数据缓存名称（带过期时间）
     */
    String COUNTRY_CACHE = WHOLESALE_CACHE_PREFIX + ":country#7d";


    /**
     * 州/省数据缓存名称（带过期时间）
     */
    String STATE_CACHE = WHOLESALE_CACHE_PREFIX + ":state#7d";

    /**
     * 库存锁定前缀
     */
    String STOCK_LOCK_PREFIX = WHOLESALE_CACHE_PREFIX + ":stock:lock:";

    /**
     * 发票号计数器缓存键
     */
    String INVOICE_ID_COUNTER_KEY = WHOLESALE_CACHE_PREFIX + ":invoice:counter";

    /**
     * 仓库物流方式缓存名称
     */
    String WAREHOUSE_LOGISTICS_METHOD_CACHE = WHOLESALE_CACHE_PREFIX + ":warehouse:logistics:method";

    /**
     * 在途库存缓存前缀
     */
    String WHOLESALE_INTRANSIT_STOCK_KEY = WHOLESALE_CACHE_PREFIX + ":intransit:stock";

    /**
     * 在途库存缓存时间（秒）
     */
    int WHOLESALE_INTRANSIT_STOCK_EXPIRE = 3600;

    // ==================== 订单相关缓存常量 ====================

    /**
     * 订单项缓存 - 考虑订单编辑场景，缓存时间设为30分钟
     * 格式：whs:order_items#30m
     * 注意：订单项修改时需要清除此缓存
     */
    String WHS_ORDER_ITEMS = WHOLESALE_CACHE_PREFIX + ":order_items#30m";

    /**
     * 订单项批量查询缓存 - 用于批量查询多个订单的订单项，缓存30分钟
     * 格式：whs:order_items_batch#30m#10m#1000
     * 注意：任何订单项变更都需要清除此缓存
     * 优化：增加空闲时间和最大条目数限制，提高缓存命中率
     */
    String WHS_ORDER_ITEMS_BATCH = WHOLESALE_CACHE_PREFIX + ":order_items_batch#30m#10m#1000";

    /**
     * 订单库存状态缓存 - 基于订单项计算，缓存10分钟（平衡实时性和性能）
     * 格式：whs:order_stock_status#10m#5m#2000
     * 注意：订单项变更或库存变更时都需要清除此缓存
     * 优化：增加空闲时间和最大条目数，提高缓存命中率
     */
    String WHS_ORDER_STOCK_STATUS = WHOLESALE_CACHE_PREFIX + ":order_stock_status#10m#5m#2000";

    // ==================== 仓库相关缓存常量 ====================

    /**
     * 仓库ID到编码映射缓存 - 缓存4小时，仓库信息变更不频繁
     * 格式：whs:warehouse_id_code_map#4h
     * 注意：仓库变更时需要清除此缓存
     */
    String WHS_WAREHOUSE_ID_CODE_MAP = WHOLESALE_CACHE_PREFIX + ":warehouse_id_code_map#4h";

    /**
     * 仓库ID到名称映射缓存 - 缓存4小时，仓库信息变更不频繁
     * 格式：whs:warehouse_id_name_map#4h
     * 注意：仓库变更时需要清除此缓存
     */
    String WHS_WAREHOUSE_ID_NAME_MAP = WHOLESALE_CACHE_PREFIX + ":warehouse_id_name_map#4h";
}
