package com.imhuso.wholesale.core.domain.vo.admin;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 发货库存查询响应视图对象 (简化版)
 * 直接展示实际变体及其在各仓库的有效可用库存
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WhsShipmentStockResponseVo {

    /**
     * 订单ID (如果按订单查询)
     */
    private Long orderId;

    /**
     * 计划ID (如果按计划查询)
     */
    private Long planId;

    /**
     * 实际变体仓库有效库存信息
     * key: 实际变体ID (actualVariantId)
     * value: 该实际变体在各仓库的有效库存列表
     */
    private Map<Long, List<WhsShipmentWarehouseStockVo>> variantWarehouseStocks;

}
