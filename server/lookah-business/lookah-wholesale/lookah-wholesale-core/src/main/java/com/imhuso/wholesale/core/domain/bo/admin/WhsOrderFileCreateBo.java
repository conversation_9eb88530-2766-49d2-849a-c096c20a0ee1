package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.wholesale.core.domain.WhsOrderFile;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 批发订单文件创建业务对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsOrderFile.class)
public class WhsOrderFileCreateBo {

    /**
     * OSS文件ID
     */
    @NotNull(message = "文件ID不能为空")
    private Long ossId;

    /**
     * 文件类型
     */
    @NotBlank(message = "文件类型不能为空")
    private String fileType;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件MIME类型
     */
    private String contentType;

    /**
     * 备注
     */
    private String remark;
}
