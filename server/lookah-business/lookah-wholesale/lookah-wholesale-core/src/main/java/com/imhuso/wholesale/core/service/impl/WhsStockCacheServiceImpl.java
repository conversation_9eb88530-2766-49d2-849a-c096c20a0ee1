package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.common.redis.utils.RedisUtils;
import com.imhuso.wholesale.core.constant.WhsCacheConstants;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.WhsStock;
import com.imhuso.wholesale.core.event.StockChangeEvent;
import com.imhuso.wholesale.core.mapper.WhsProductVariantMapper;
import com.imhuso.wholesale.core.mapper.WhsStockMapper;
import com.imhuso.wholesale.core.service.IWhsStockCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 库存缓存服务实现类
 * 只维护一个缓存：变体库存列表缓存
 * 通过事件机制与产品缓存解耦
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsStockCacheServiceImpl implements IWhsStockCacheService {

    private final WhsStockMapper stockMapper;
    private final WhsProductVariantMapper productVariantMapper;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 基础缓存过期时间（分钟）
     */
    private static final long BASE_CACHE_EXPIRE_MINUTES = 5;

    /**
     * 热点数据缓存过期时间（分钟）
     */
    private static final long HOT_CACHE_EXPIRE_MINUTES = 30;

    /**
     * 空值缓存过期时间（分钟）
     */
    private static final long NULL_CACHE_EXPIRE_MINUTES = 1;

    /**
     * 批处理大小
     */
    private static final int BATCH_SIZE = 1000;

    /**
     * 空值占位符
     */
    private static final String NULL_PLACEHOLDER = "NULL";

    // 公开方法 -----------------------------------------
    @Override
    public Integer getVariantAvailableStockFromCache(Long variantId) {
        if (variantId == null) {
            return null;
        }

        List<WhsStock> stocks = getVariantStocksFromCache(variantId);
        return stocks == null ? null : calculateTotalAvailableStock(stocks);
    }

    @Override
    public Map<Long, Integer> getVariantAvailableStocksFromCache(List<Long> variantIds) {
        if (CollUtil.isEmpty(variantIds)) {
            return Collections.emptyMap();
        }

        Map<Long, Integer> result = new HashMap<>(variantIds.size());

        // 过滤空值并去重
        List<Long> validIds = variantIds.stream().filter(Objects::nonNull).distinct().toList();

        for (Long variantId : validIds) {
            Integer availableStock = getVariantAvailableStockFromCache(variantId);
            if (availableStock != null) {
                result.put(variantId, availableStock);
            }
        }

        return result;
    }

    @Override
    public void updateStockCache(WhsStock stock) {
        if (stock == null || stock.getId() == null || stock.getVariantId() == null) {
            return;
        }

        try {
            // 直接刷新变体库存缓存
            refreshVariantsStockCache(Collections.singletonList(stock.getVariantId()));

            // 获取关联的产品ID
            List<Long> productIds = getProductIdsByVariantIds(Collections.singletonList(stock.getVariantId()));

            // 发布库存变更事件，包含产品ID信息
            publishStockChangeEvent(Collections.singletonList(stock.getVariantId()), productIds);

            log.debug("更新库存缓存成功: stockId={}, variantId={}, available={}, locked={}, total={}", stock.getId(), stock.getVariantId(), stock.getAvailableStock(), stock.getLockedStock(), stock.getTotalStock());
        } catch (Exception e) {
            log.error("更新库存缓存失败: stockId={}, variantId={}, error={}", stock.getId(), stock.getVariantId(), e.getMessage());
        }
    }

    @Override
    @Lock4j(name = WhsCacheConstants.STOCK_VARIANT_CACHE_PREFIX + "refresh:batch", acquireTimeout = 3000, expire = 5000)
    public void refreshVariantsStockCache(List<Long> variantIds) {
        if (variantIds == null || variantIds.isEmpty()) {
            return;
        }

        try {
            // 去重处理
            List<Long> distinctIds = variantIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());

            // 分批处理
            int totalSize = distinctIds.size();
            int processedCount = 0;

            for (int i = 0; i < totalSize; i += BATCH_SIZE) {
                List<Long> batchIds = distinctIds.subList(i, Math.min(i + BATCH_SIZE, totalSize));
                for (Long variantId : batchIds) {
                    refreshSingleVariantCache(variantId);
                }
                processedCount += batchIds.size();
                log.debug("已批量刷新库存缓存: {}/{}", processedCount, totalSize);
            }

            // 获取关联的产品ID
            List<Long> productIds = getProductIdsByVariantIds(distinctIds);

            // 发布批量库存变更事件，包含产品ID信息
            publishStockChangeEvent(distinctIds, productIds);

            log.info("批量刷新库存缓存完成, 共处理数量: {}", processedCount);
        } catch (Exception e) {
            log.error("批量刷新变体库存缓存失败: error={}", e.getMessage());
        }
    }

    @Override
    public void checkAndRepairStockCache() {
        try {
            log.info("开始检查和修复库存缓存");

            // 获取所有有库存的商品变体
            List<Long> variantIds = stockMapper.selectList(new LambdaQueryWrapper<WhsStock>().select(WhsStock::getVariantId).groupBy(WhsStock::getVariantId)).stream().map(WhsStock::getVariantId).collect(Collectors.toList());

            // 批量刷新
            refreshVariantsStockCache(variantIds);

            log.info("库存缓存检查和修复完成，共处理{}个变体", variantIds.size());
        } catch (Exception e) {
            log.error("库存缓存检查和修复失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void reloadAllProductStocksCache() {
        try {
            log.info("开始预加载商品库存到缓存");

            // 获取所有变体ID，直接查询变体表效率更高
            List<Long> variantIds = productVariantMapper.selectList(new LambdaQueryWrapper<WhsProductVariant>().select(WhsProductVariant::getId)).stream().map(WhsProductVariant::getId).collect(Collectors.toList());

            // 批量刷新
            refreshVariantsStockCache(variantIds);

            log.info("商品库存预加载完成，共处理{}个变体", variantIds.size());
        } catch (Exception e) {
            log.error("预加载商品库存失败: {}", e.getMessage(), e);
        }
    }

    // 私有辅助方法 -----------------------------------------

    /**
     * 发布库存变更事件（带产品ID信息）
     *
     * @param variantIds 发生变更的变体ID列表
     * @param productIds 关联的产品ID列表
     */
    private void publishStockChangeEvent(List<Long> variantIds, List<Long> productIds) {
        try {
            if (CollUtil.isEmpty(variantIds)) {
                return;
            }
            // 创建并发布库存变更事件
            StockChangeEvent event = new StockChangeEvent(this, variantIds, productIds);
            eventPublisher.publishEvent(event);
            log.debug("已发布库存变更事件，变体数量: {}, 产品数量: {}", variantIds.size(), productIds != null ? productIds.size() : 0);
        } catch (Exception e) {
            log.error("发布库存变更事件失败: {}", e.getMessage());
        }
    }

    /**
     * 根据变体ID列表获取关联的产品ID列表
     *
     * @param variantIds 变体ID列表
     * @return 产品ID列表
     */
    private List<Long> getProductIdsByVariantIds(List<Long> variantIds) {
        if (CollUtil.isEmpty(variantIds)) {
            return Collections.emptyList();
        }

        try {
            // 查询变体对应的产品ID
            LambdaQueryWrapper<WhsProductVariant> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(WhsProductVariant::getProductId).in(WhsProductVariant::getId, variantIds);

            return productVariantMapper.selectList(queryWrapper).stream().map(WhsProductVariant::getProductId).distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取变体关联产品ID失败: {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    /**
     * 从缓存获取变体所有库存记录
     */
    @Lock4j(name = WhsCacheConstants.STOCK_VARIANT_CACHE_PREFIX + "get:#{#variantId}", acquireTimeout = 1000, expire = 3000)
    private List<WhsStock> getVariantStocksFromCache(Long variantId) {
        if (variantId == null) {
            return null;
        }

        String cacheKey = getVariantStockListCacheKey(variantId);
        Object cacheValue = RedisUtils.getCacheObject(cacheKey);

        if (NULL_PLACEHOLDER.equals(cacheValue)) {
            return null;
        }

        if (cacheValue instanceof List<?> list && !list.isEmpty() && list.get(0) instanceof WhsStock) {
            @SuppressWarnings("unchecked") List<WhsStock> stocks = (List<WhsStock>) list;
            return stocks;
        }

        // 缓存未命中，从数据库获取
        List<WhsStock> stocks = getVariantStocksFromDatabase(variantId);
        if (CollUtil.isEmpty(stocks)) {
            cacheNullValue(cacheKey, variantId);
            return null;
        } else {
            RedisUtils.setCacheObject(cacheKey, stocks, Duration.ofMinutes(calculateExpireMinutes(variantId)));
            return stocks;
        }
    }

    /**
     * 计算缓存过期时间，加入随机值防止同时过期
     */
    private long calculateExpireMinutes(Long variantId) {
        long baseExpire = isHotProduct(variantId) ? HOT_CACHE_EXPIRE_MINUTES : BASE_CACHE_EXPIRE_MINUTES;
        // 在基础时间上增加随机值，范围是±2分钟
        return baseExpire + new Random().nextInt(4) - 2;
    }

    /**
     * 判断是否为热点商品（可以根据实际业务逻辑实现）
     */
    private boolean isHotProduct(Long variantId) {
        // 实现热点商品的判断逻辑
        // 可以基于:
        // 1. 访问频率统计
        // 2. 商品标记（如促销商品）
        // 3. 历史销量数据
        // 4. 实时监控数据
        return false;
    }

    /**
     * 从数据库获取变体的所有库存记录
     */
    private List<WhsStock> getVariantStocksFromDatabase(Long variantId) {
        if (variantId == null) {
            return Collections.emptyList();
        }

        try {
            LambdaQueryWrapper<WhsStock> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WhsStock::getVariantId, variantId);
            return stockMapper.selectList(wrapper);
        } catch (Exception e) {
            log.error("获取变体库存失败: variantId={}, error={}", variantId, e.getMessage());
            return Collections.emptyList();
        }
    }

    /**
     * 缓存空值，防止缓存穿透
     */
    private void cacheNullValue(String cacheKey, Object objectId) {
        RedisUtils.setCacheObject(cacheKey, NULL_PLACEHOLDER, Duration.ofMinutes(NULL_CACHE_EXPIRE_MINUTES));
        log.debug("已缓存空值 {}={}, key={}", "变体库存", objectId, cacheKey);
    }

    /**
     * 计算总可用库存
     */
    private Integer calculateTotalAvailableStock(List<WhsStock> stocks) {
        if (CollUtil.isEmpty(stocks)) {
            return 0;
        }
        return stocks.stream().mapToInt(stock -> stock.getAvailableStock() != null ? stock.getAvailableStock() : 0).sum();
    }

    private String getVariantStockListCacheKey(Long variantId) {
        return WhsCacheConstants.STOCK_VARIANT_CACHE_PREFIX + variantId;
    }

    /**
     * 刷新单个变体库存缓存
     *
     * @param variantId 变体ID
     */
    private void refreshSingleVariantCache(Long variantId) {
        if (variantId == null) {
            return;
        }

        try {
            // 从数据库获取最新库存
            List<WhsStock> stocks = getVariantStocksFromDatabase(variantId);
            String cacheKey = getVariantStockListCacheKey(variantId);

            if (CollUtil.isEmpty(stocks)) {
                // 缓存空值
                cacheNullValue(cacheKey, variantId);
            } else {
                // 更新缓存
                RedisUtils.setCacheObject(cacheKey, stocks, Duration.ofMinutes(calculateExpireMinutes(variantId)));
            }
        } catch (Exception e) {
            log.error("刷新单个变体库存缓存失败: variantId={}, error={}", variantId, e.getMessage());
        }
    }
}
