package com.imhuso.wholesale.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.wholesale.core.domain.WhsOrder;
import com.imhuso.wholesale.core.domain.WhsOrderItem;
import com.imhuso.wholesale.core.domain.WhsProduct;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.WhsShipmentPlan;
import com.imhuso.wholesale.core.domain.WhsShipmentPlanItem;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderPendingShipmentVo;
import com.imhuso.wholesale.core.enums.OrderStatus;
import com.imhuso.wholesale.core.enums.ShipmentPlanStatus;
import com.imhuso.wholesale.core.mapper.WhsOrderItemMapper;
import com.imhuso.wholesale.core.mapper.WhsOrderMapper;
import com.imhuso.wholesale.core.mapper.WhsProductMapper;
import com.imhuso.wholesale.core.mapper.WhsProductVariantMapper;
import com.imhuso.wholesale.core.mapper.WhsShipmentPlanItemMapper;
import com.imhuso.wholesale.core.mapper.WhsShipmentPlanMapper;
import com.imhuso.wholesale.core.service.IWhsInTransitStockService;
import com.imhuso.wholesale.core.service.IWhsOrderPendingShipmentService;
import com.imhuso.wholesale.core.service.IWhsStockService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单待发货明细服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsOrderPendingShipmentServiceImpl implements IWhsOrderPendingShipmentService {

    private final WhsOrderMapper orderMapper;
    private final WhsOrderItemMapper orderItemMapper;
    private final WhsShipmentPlanMapper shipmentPlanMapper;
    private final WhsShipmentPlanItemMapper shipmentPlanItemMapper;
    private final WhsProductVariantMapper productVariantMapper;
    private final WhsProductMapper productMapper;
    private final IWhsStockService stockService;
    private final IWhsInTransitStockService inTransitStockService;

    @Override
    public WhsOrderPendingShipmentVo getOrderPendingShipment(Long orderId) {
        log.debug("获取订单待发货明细: orderId={}", orderId);

        // 0. 参数验证
        if (orderId == null) {
            log.warn("订单ID不能为空");
            throw new ServiceException(MessageUtils.message("wholesale.order.id.required"));
        }

        // 1. 查询订单基本信息
        WhsOrder order = orderMapper.selectById(orderId);
        if (order == null) {
            log.warn("订单不存在: orderId={}", orderId);
            throw new ServiceException(MessageUtils.message("wholesale.order.not.found"));
        }

        // 2. 检查订单状态，只有处理中的订单才有待发货明细
        if (!OrderStatus.PROCESSING.getValue().equals(order.getOrderStatus())) {
            log.warn("订单状态不是处理中，无待发货明细: orderId={}, orderStatus={}", orderId, order.getOrderStatus());
            throw new ServiceException(MessageUtils.message("wholesale.order.status.not.processing"));
        }

        WhsOrderPendingShipmentVo result = new WhsOrderPendingShipmentVo();
        result.setOrderId(orderId);
        result.setOrderNo(order.getInternalOrderNo());

        // 3. 查询是否有已执行的发货方案
        LambdaQueryWrapper<WhsShipmentPlan> planQuery = new LambdaQueryWrapper<>();
        planQuery.eq(WhsShipmentPlan::getOrderId, orderId)
                .eq(WhsShipmentPlan::getStatus, ShipmentPlanStatus.EXECUTED.getValue());

        WhsShipmentPlan executedPlan = shipmentPlanMapper.selectOne(planQuery);

        if (executedPlan != null) {
            // 4a. 有发货方案，以发货方案为准
            result.setHasShipmentPlan(true);
            result.setPlanName(executedPlan.getPlanName());
            result.setPendingItems(getPendingItemsFromPlan(executedPlan.getId()));
        } else {
            // 4b. 没有发货方案，以订单明细为准
            result.setHasShipmentPlan(false);
            result.setPendingItems(getPendingItemsFromOrder(orderId));
        }

        return result;
    }

    /**
     * 从发货方案获取待发货明细
     */
    private List<WhsOrderPendingShipmentVo.PendingShipmentItem> getPendingItemsFromPlan(Long planId) {
        // 查询发货方案项
        LambdaQueryWrapper<WhsShipmentPlanItem> itemQuery = new LambdaQueryWrapper<>();
        itemQuery.eq(WhsShipmentPlanItem::getPlanId, planId)
                .orderByAsc(WhsShipmentPlanItem::getId);

        List<WhsShipmentPlanItem> planItems = shipmentPlanItemMapper.selectList(itemQuery);
        if (planItems.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取变体ID列表
        List<Long> variantIds = planItems.stream()
                .map(WhsShipmentPlanItem::getVariantId)
                .collect(Collectors.toList());

        // 批量获取库存和在途库存信息
        Map<Long, Integer> availableStocks = stockService.getAvailableStocks(variantIds);
        Map<Long, Integer> inTransitStocks = inTransitStockService.getInTransitStocks(variantIds);

        // 批量获取产品变体信息
        List<WhsProductVariant> variants = productVariantMapper.selectList(
                new LambdaQueryWrapper<WhsProductVariant>()
                        .in(WhsProductVariant::getId, variantIds)
        );
        Map<Long, WhsProductVariant> variantMap = variants.stream()
                .collect(Collectors.toMap(WhsProductVariant::getId, v -> v));

        // 批量获取产品信息
        List<Long> productIds = variants.stream()
                .map(WhsProductVariant::getProductId)
                .distinct()
                .collect(Collectors.toList());
        List<WhsProduct> products = productMapper.selectList(
                new LambdaQueryWrapper<WhsProduct>()
                        .in(WhsProduct::getId, productIds)
        );
        Map<Long, WhsProduct> productMap = products.stream()
                .collect(Collectors.toMap(WhsProduct::getId, p -> p));

        // 转换为待发货明细，只返回有待发货数量的商品
        return planItems.stream().map(planItem -> {
            WhsOrderPendingShipmentVo.PendingShipmentItem item = new WhsOrderPendingShipmentVo.PendingShipmentItem();
            item.setVariantId(planItem.getVariantId());
            item.setSkuCode(planItem.getSkuCode());
            item.setPackagingType(planItem.getPackagingType());
            item.setPackagingQuantity(planItem.getPackagingQuantity());
            item.setTotalQuantity(planItem.getQuantity());
            item.setShippedQuantity(planItem.getShippedQuantity() != null ? planItem.getShippedQuantity() : 0);
            item.setPendingQuantity(planItem.getQuantity() - item.getShippedQuantity());
            item.setAvailableStock(availableStocks.getOrDefault(planItem.getVariantId(), 0));
            item.setInTransitStock(inTransitStocks.getOrDefault(planItem.getVariantId(), 0));

            // 获取产品名称和规格信息
            WhsProductVariant variant = variantMap.get(planItem.getVariantId());
            if (variant != null) {
                WhsProduct product = productMap.get(variant.getProductId());
                if (product != null) {
                    item.setProductName(product.getItemName());
                } else {
                    item.setProductName(planItem.getSkuCode());
                }
                item.setSpecs(variant.getSpecs());
            } else {
                item.setProductName(planItem.getSkuCode());
                item.setSpecs("");
            }

            return item;
        }).filter(item -> item.getPendingQuantity() > 0) // 只返回有待发货数量的商品
          .collect(Collectors.toList());
    }

    /**
     * 从订单明细获取待发货明细
     */
    private List<WhsOrderPendingShipmentVo.PendingShipmentItem> getPendingItemsFromOrder(Long orderId) {
        // 查询订单项
        LambdaQueryWrapper<WhsOrderItem> itemQuery = new LambdaQueryWrapper<>();
        itemQuery.eq(WhsOrderItem::getOrderId, orderId)
                .orderByAsc(WhsOrderItem::getId);

        List<WhsOrderItem> orderItems = orderItemMapper.selectList(itemQuery);
        if (orderItems.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取变体ID列表
        List<Long> variantIds = orderItems.stream()
                .map(WhsOrderItem::getVariantId)
                .collect(Collectors.toList());

        // 批量获取库存和在途库存信息
        Map<Long, Integer> availableStocks = stockService.getAvailableStocks(variantIds);
        Map<Long, Integer> inTransitStocks = inTransitStockService.getInTransitStocks(variantIds);

        // 转换为待发货明细，只返回有待发货数量的商品
        return orderItems.stream().map(orderItem -> {
            WhsOrderPendingShipmentVo.PendingShipmentItem item = new WhsOrderPendingShipmentVo.PendingShipmentItem();
            item.setVariantId(orderItem.getVariantId());
            item.setSkuCode(orderItem.getSkuCode());
            item.setProductName(orderItem.getProductName());
            item.setSpecs(orderItem.getSpecsSnapshot());
            item.setPackagingType(orderItem.getPackagingType());
            item.setPackagingQuantity(orderItem.getPackagingQuantity());
            item.setTotalQuantity(orderItem.getQuantity());
            item.setShippedQuantity(0); // 没有发货方案时，已发货数量为0
            item.setPendingQuantity(orderItem.getQuantity());
            item.setAvailableStock(availableStocks.getOrDefault(orderItem.getVariantId(), 0));
            item.setInTransitStock(inTransitStocks.getOrDefault(orderItem.getVariantId(), 0));

            return item;
        }).filter(item -> item.getPendingQuantity() > 0) // 只返回有待发货数量的商品
          .collect(Collectors.toList());
    }
}
