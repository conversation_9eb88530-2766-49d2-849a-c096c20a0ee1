package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 在途库存对象 whs_in_transit_stock
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_in_transit_stock")
public class WhsInTransitStock extends BaseEntity {

    /**
     * 在途库存ID
     */
    @TableId
    private Long id;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 在途数量
     */
    private Integer quantity;
}
