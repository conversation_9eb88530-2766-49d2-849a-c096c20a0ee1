package com.imhuso.wholesale.core.enums;

import lombok.Getter;

/**
 * 发货审批记录状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ShipmentApprovalRecordStatus {

    /**
     * 有效记录
     */
    ACTIVE("ACTIVE"),

    /**
     * 已被覆盖
     */
    SUPERSEDED("SUPERSEDED"),

    /**
     * 已归档
     */
    ARCHIVED("ARCHIVED");

    /**
     * 枚举值
     */
    private final String value;

    /**
     * 构造函数
     *
     * @param value 枚举值
     */
    ShipmentApprovalRecordStatus(String value) {
        this.value = value;
    }
}
