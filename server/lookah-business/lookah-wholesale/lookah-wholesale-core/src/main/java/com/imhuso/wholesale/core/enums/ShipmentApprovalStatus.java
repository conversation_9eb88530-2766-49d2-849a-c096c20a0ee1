package com.imhuso.wholesale.core.enums;

import com.imhuso.common.translation.core.EnumTranslatableInterface;
import lombok.Getter;

/**
 * 发货审批状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ShipmentApprovalStatus implements EnumTranslatableInterface {

    /**
     * 无需审批
     */
    NO_APPROVAL(0),

    /**
     * 待审批
     */
    PENDING(1),

    /**
     * 审批通过
     */
    APPROVED(2),

    /**
     * 审批拒绝
     */
    REJECTED(3),

    /**
     * 已撤销
     */
    CANCELLED(4);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 构造函数
     *
     * @param value 枚举值
     */
    ShipmentApprovalStatus(int value) {
        this.value = value;
    }
}
