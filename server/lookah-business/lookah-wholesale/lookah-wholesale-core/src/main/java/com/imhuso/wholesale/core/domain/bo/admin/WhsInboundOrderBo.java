package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.wholesale.core.domain.WhsInboundOrder;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 入库单业务对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsInboundOrder.class)
public class WhsInboundOrderBo {

    /**
     * 入库单ID
     */
    @NotNull(message = "入库单ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 仓库ID
     */
    @NotNull(message = "仓库ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long warehouseId;

    /**
     * 外部入库单号
     */
    @NotBlank(message = "外部入库单号不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 100, message = "外部入库单号长度不能超过100个字符", groups = {AddGroup.class, EditGroup.class})
    private String externalOrderNo;

    /**
     * 状态
     */
    @NotBlank(message = "状态不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 50, message = "状态长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String status;

    /**
     * 状态编码
     */
    @NotBlank(message = "状态编码不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 50, message = "状态编码长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String statusCode;

    /**
     * 跟踪号
     */
    @Size(max = 100, message = "跟踪号长度不能超过100个字符", groups = {AddGroup.class, EditGroup.class})
    private String trackingNumber;

    /**
     * 承运商
     */
    @Size(max = 100, message = "承运商长度不能超过100个字符", groups = {AddGroup.class, EditGroup.class})
    private String carrier;

    /**
     * 预计到达时间
     */
    private Date expectedArrival;

    /**
     * 实际到达时间
     */
    private Date actualArrival;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符", groups = {AddGroup.class, EditGroup.class})
    private String remark;

    /**
     * 入库单明细列表
     */
    private List<WhsInboundOrderItemBo> items;
}
