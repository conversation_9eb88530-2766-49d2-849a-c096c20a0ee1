package com.imhuso.wholesale.core.domain.bo.admin;

import java.io.Serial;
import java.io.Serializable;

import com.imhuso.wholesale.core.domain.WhsShipmentConversion;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentConversionVo;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发货转换记录业务对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@AutoMappers({@AutoMapper(target = WhsShipmentConversion.class), @AutoMapper(target = WhsShipmentConversionVo.class)})
public class WhsShipmentConversionBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 转换记录ID
     */
    private Long id;

    /**
     * 原始订单ID
     */
    private Long orderId;

    /**
     * 原始变体ID
     */
    private Long originalVariantId;

    /**
     * 原始变体SKU编码
     */
    private String originalSkuCode;

    /**
     * 参与转换的原始订单项数量
     */
    private Integer originalQuantity;

    /**
     * 原始包装类型: 0-单品 1-展示盒 2-整箱
     *
     * @see com.imhuso.wholesale.enums.PackagingType
     */
    private Integer originalPackagingType;

    /**
     * 关联的发货计划ID
     */
    private Long planId;

    /**
     * 转换后生成的发货计划项ID
     */
    private Long planItemId;

    /**
     * 实际发货变体ID
     */
    private Long actualVariantId;

    /**
     * 实际发货变体SKU编码
     */
    private String actualSkuCode;

    /**
     * 转换后的数量
     */
    private Integer actualQuantity;

    /**
     * 转换后包装类型: 0-单品 1-展示盒 2-整箱
     *
     * @see com.imhuso.wholesale.enums.PackagingType
     */
    private Integer actualPackagingType;

    /**
     * 转换说明
     */
    private String remark;
}
