package com.imhuso.wholesale.core.domain.vo.warehouse;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * LTL追踪API响应VO
 *
 * <AUTHOR>
 */
@Data
public class LtlTrackingResponseVo {
    
    /**
     * 请求状态
     */
    private Boolean success;
    
    /**
     * 响应信息
     */
    private String msg;
    
    /**
     * BOL单号
     */
    private String shipmentBOL;
    
    /**
     * 系统单号
     */
    private String customerPO;
    
    /**
     * 客户单号
     */
    private String shipperReference;
    
    /**
     * 卡车公司单号
     */
    private String carrierPRO;
    
    /**
     * 卡车公司官网追踪链接
     */
    @JsonProperty("CarrierTrackUrl")
    private String carrierTrackUrl;
    
    /**
     * 签收单
     */
    private String pod;
    
    /**
     * 追踪详情
     */
    private List<LtlTrackingDetailVo> detail;
    
    /**
     * 追踪详情VO
     */
    @Data
    public static class LtlTrackingDetailVo {
        /**
         * 状态
         */
        private String shipmentStatus;
        
        /**
         * 活动时间
         */
        private String activityDate;
        
        /**
         * 活动描述
         */
        private String activityDescription;
    }
}