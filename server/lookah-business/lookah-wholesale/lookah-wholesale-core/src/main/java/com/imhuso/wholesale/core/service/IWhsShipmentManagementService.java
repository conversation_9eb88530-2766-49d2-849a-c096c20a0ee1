package com.imhuso.wholesale.core.service;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsShipmentManagementBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentManagementVo;

/**
 * 发货管理Service接口
 *
 * <AUTHOR>
 */
public interface IWhsShipmentManagementService {

    /**
     * 分页查询待发货订单列表
     * 查询条件：
     * 1. 订单状态为处理中(1)
     * 2. 发货状态为待发货(0)或部分发货(2)
     * 3. 排除已发货(3)和已送达(4)状态
     *
     * @param bo        查询条件
     * @param pageQuery 分页查询条件
     * @return 待发货订单列表
     */
    TableDataInfo<WhsShipmentManagementVo> queryPendingShipmentOrders(WhsShipmentManagementBo bo, PageQuery pageQuery);
}
