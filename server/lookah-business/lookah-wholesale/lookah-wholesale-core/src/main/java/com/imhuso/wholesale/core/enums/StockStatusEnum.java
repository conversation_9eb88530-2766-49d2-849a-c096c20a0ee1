package com.imhuso.wholesale.core.enums;

import com.imhuso.common.translation.core.EnumTranslatableInterface;

import lombok.Getter;

/**
 * 库存状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum StockStatusEnum implements EnumTranslatableInterface {
    NORMAL(0, "正常"), WARNING(1, "预警"), OUT_OF_STOCK(2, "紧缺");

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 构造函数
     */
    StockStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 根据可用库存和预警阈值判断库存状态
     *
     * @param availableStock 可用库存
     * @param alertThreshold 预警阈值
     * @return 库存状态枚举
     */
    public static StockStatusEnum getStockStatus(Integer availableStock, Integer alertThreshold) {
        if (availableStock == null || alertThreshold == null) {
            return NORMAL;
        }

        if (availableStock <= 0) {
            return OUT_OF_STOCK;
        } else if (availableStock <= alertThreshold) {
            return WARNING;
        } else {
            return NORMAL;
        }
    }
}
