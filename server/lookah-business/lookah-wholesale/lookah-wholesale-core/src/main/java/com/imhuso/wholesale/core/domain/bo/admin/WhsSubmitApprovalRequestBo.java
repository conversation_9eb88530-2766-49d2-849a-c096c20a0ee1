package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * 提交审核申请业务对象
 *
 * <AUTHOR>
 */
@Data
@Validated(AddGroup.class)
public class WhsSubmitApprovalRequestBo {

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    /**
     * 申请理由
     */
    @NotBlank(message = "申请理由不能为空")
    private String applicationReason;
}