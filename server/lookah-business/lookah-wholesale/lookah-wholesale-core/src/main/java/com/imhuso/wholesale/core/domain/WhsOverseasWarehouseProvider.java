package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 海外仓提供商对象 whs_overseas_warehouse_provider
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_overseas_warehouse_provider")
public class WhsOverseasWarehouseProvider extends BaseEntity {

    /**
     * 提供商ID
     */
    @TableId
    private Long id;

    /**
     * 提供商名称
     */
    private String name;

    /**
     * 提供商编码
     */
    private String code;

    /**
     * 提供商类型
     */
    private String providerType;

    /**
     * 状态（0停用 1启用）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;
}
