package com.imhuso.wholesale.core.strategy.shipmentPlan.impl;

import com.imhuso.wholesale.core.domain.bo.admin.WhsShipmentPlanGenerationBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShipmentPlanGenerationVo;
import com.imhuso.wholesale.core.strategy.shipmentPlan.IWhsShipmentPlanStrategy;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
public class StandWhsShipmentPlanStrategyImpl implements IWhsShipmentPlanStrategy {

    @Override
    public String getStrategyType() {
        return "stand";
    }

    @Override
    public String getStrategyName() {
        return "标准方案";
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public WhsShipmentPlanGenerationBo generate(WhsShipmentPlanGenerationVo generationVo) {
        // 将原始订单项构造成所需要的结果
        return WhsShipmentPlanGenerationBo.builder().planName(getStrategyName()).orderId(generationVo.getOrderId()).planItems(generationVo.getOrderItems().stream().map(item -> WhsShipmentPlanGenerationBo.PlanItem.builder().variantId(item.getVariantId()).quantity(item.getQuantity()).build()).collect(Collectors.toList())).build();
    }
}
