package com.imhuso.wholesale.core.domain.vo.warehouse;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 海外仓发货信息VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 收件人信息
     */
    private String recipientName;
    private String recipientPhone;
    private String recipientEmail;
    private String recipientCompanyName;

    /**
     * 地址信息
     */
    private String country;
    private String province;
    private String city;
    private String address;
    private String postalCode;

    /**
     * 发货方式
     */
    private String shippingMethod;

    /**
     * 物流方式ID
     */
    private Long logisticsMethodId;

    /**
     * 包裹数量（pieceCount）
     * 代表昊通仓库中发货接口中所需要的 BillQty
     */
    private Integer pieceCount;

    /**
     * 是否启用签名服务
     */
    private Boolean signatureService;

    /**
     * 发货项目列表
     */
    private List<ShipmentItemVo> items;

    /**
     * 发货项详细信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShipmentItemVo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * SKU编码
         */
        private String sku;

        /**
         * 商品名称
         */
        private String productName;

        /**
         * 数量
         */
        private int quantity;

        /**
         * 单价
         */
        private BigDecimal unitPrice;

        /**
         * 重量(克)
         */
        private Integer weightInGrams;
    }
}
