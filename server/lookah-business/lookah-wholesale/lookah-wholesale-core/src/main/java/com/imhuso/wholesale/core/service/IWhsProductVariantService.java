package com.imhuso.wholesale.core.service;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductVariantBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductVariantListBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsGetSkusInfoVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVariantListVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVariantVo;

import java.util.List;
import java.util.Map;

/**
 * 商品变体服务接口
 *
 * <AUTHOR>
 */
public interface IWhsProductVariantService {
    /**
     * 添加产品变体
     *
     * @param variant 变体信息
     * @return 结果
     */
    int insertVariant(WhsProductVariantBo variant);

    /**
     * 修改产品变体
     *
     * @param variant 变体信息
     * @return 结果
     */
    int updateVariant(WhsProductVariantBo variant);

    /**
     * 批量删除产品变体
     *
     * @param id 需要删除的变体ID
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 获取产品变体
     *
     * @param id         变体ID
     * @param needDetail 是否需要填充详细信息（库存和属性）
     * @return 变体信息
     */
    WhsProductVariantVo getVariant(Long id, boolean needDetail);

    /**
     * 批量获取产品变体
     *
     * @param ids 变体ID列表
     * @return 变体信息列表
     */
    List<WhsProductVariant> selectByIds(List<Long> ids);

    /**
     * 批量获取变体
     *
     * @param variantIds 变体ID列表
     * @param onlyBase   是否只查询基本信息（true:不查询包装关系，false:查询包装关系）
     * @return 变体列表
     */
    List<WhsProductVariantVo> getVariantsByIds(List<Long> variantIds, boolean onlyBase);

    /**
     * 根据产品ID列表批量获取变体
     *
     * @param productIds 产品ID列表
     * @return 产品ID到变体列表的映射
     */
    Map<Long, List<WhsProductVariant>> getVariantsByProductIds(List<Long> productIds);

    /**
     * 将实体列表转换为VO列表
     *
     * @param variants 变体实体列表
     * @return 变体VO列表
     */
    List<WhsProductVariantVo> convertToVoList(List<WhsProductVariant> variants);

    /**
     * 设置默认变体
     *
     * @param id 变体ID
     * @return 结果
     */
    int setDefaultVariant(Long id);

    /**
     * 根据产品ID分页查询变体列表
     *
     * @param productId 产品ID
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    TableDataInfo<WhsProductVariantVo> queryPageByProductId(Long productId, PageQuery pageQuery);

    /**
     * 检查SKU编码唯一性
     *
     * @param skuCode   要检查的SKU编码
     * @param excludeId 需要排除的变体ID（用于更新场景）
     * @return 是否唯一
     */
    boolean isSkuCodeUnique(String skuCode, Long excludeId);

    /**
     * 复制变体及其关联数据（不保存到数据库）
     *
     * @param sourceVariantId 源变体ID
     * @param newSkuCode      新的SKU编码
     * @param newUpc          新的UPC编码（可为空）
     * @return 复制的新变体（未保存到数据库）及其属性关联
     */
    Map<String, Object> copyVariantWithRelations(Long sourceVariantId, String newSkuCode, String newUpc);

    /**
     * 批量获取SKU编码到变体ID的映射
     *
     * @param skuCodes SKU编码列表
     * @return SKU到变体ID的映射
     */
    Map<String, Long> getVariantIdsBySkuCodes(List<String> skuCodes);

    /**
     * 获取所有有效的变体列表
     *
     * @return 变体列表
     */
    List<WhsProductVariant> getAllActiveVariants();

    /**
     * 获取所有变体列表，包含详细信息
     *
     * @param bo        查询参数
     * @param pageQuery 分页参数
     * @return 变体列表分页数据
     */
    TableDataInfo<WhsProductVariantListVo> queryVariantList(WhsProductVariantListBo bo, PageQuery pageQuery);

    /**
     * 批量获取SKU信息
     *
     * @param skuCodes SKU编码列表
     * @return SKU信息响应，包含SKU详细信息和不存在的SKU列表
     */
    List<WhsGetSkusInfoVo> getSkuInfoBySkuCodes(List<String> skuCodes);
}
