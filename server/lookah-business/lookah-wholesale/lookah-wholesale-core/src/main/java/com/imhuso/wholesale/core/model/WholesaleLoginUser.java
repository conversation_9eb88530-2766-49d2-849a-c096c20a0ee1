package com.imhuso.wholesale.core.model;

import com.imhuso.common.core.domain.model.LoginUser;
import com.imhuso.wholesale.core.constant.WholesaleConstants;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 批发登录用户身份权限
 * <p>
 * Wholesale模块专用的登录用户模型，继承自通用LoginUser
 * 提供Wholesale模块特有的用户信息和登录标识
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class WholesaleLoginUser extends LoginUser {
    /**
     * 用户全名
     */
    private String fullName;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 获取登录id
     * Wholesale模块使用特定的前缀来区分不同模块的用户
     *
     * @return 格式为 "whs_user:{userId}" 的登录ID
     */
    @Override
    public String getLoginId() {
        if (getUserId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return WholesaleConstants.SaToken.USER_TYPE + ":" + getUserId();
    }
}
