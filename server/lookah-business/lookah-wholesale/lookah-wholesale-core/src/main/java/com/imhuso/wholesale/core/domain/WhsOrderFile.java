package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批发订单文件关联对象 whs_order_file
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_order_file")
public class WhsOrderFile extends BaseEntity {

    /**
     * 文件关联ID
     */
    @TableId
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * OSS文件ID
     */
    private Long ossId;

    /**
     * 文件类型
     * INVOICE-发票文件, CONTRACT-合同文件, OTHER-其他文件
     */
    private String fileType;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件MIME类型
     */
    private String contentType;

    /**
     * 备注
     */
    private String remark;
}
