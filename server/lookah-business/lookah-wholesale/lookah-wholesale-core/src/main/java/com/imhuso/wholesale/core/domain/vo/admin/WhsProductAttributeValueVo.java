package com.imhuso.wholesale.core.domain.vo.admin;

import java.io.Serial;
import java.io.Serializable;

import com.imhuso.wholesale.core.domain.WhsProductAttributeValue;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 产品属性值视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AutoMapper(target = WhsProductAttributeValue.class)
public class WhsProductAttributeValueVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 属性值ID
     */
    private Long id;

    /**
     * 属性ID
     */
    private Long attributeId;

    /**
     * 属性值键
     */
    private String valueKey;

    /**
     * 属性值显示名
     */
    private String valueDisplay;

    /**
     * 显示顺序
     */
    private Integer sort;

    /**
     * 状态（0停用 1正常）
     */
    private String status;
}
