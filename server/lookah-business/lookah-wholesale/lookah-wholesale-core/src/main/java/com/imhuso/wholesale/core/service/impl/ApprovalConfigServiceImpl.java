package com.imhuso.wholesale.core.service.impl;

import com.imhuso.common.core.service.ConfigService;
import com.imhuso.wholesale.core.domain.WhsOrder;
import com.imhuso.wholesale.core.enums.OrderStatus;
import com.imhuso.wholesale.core.enums.ShipmentApprovalStatus;
import com.imhuso.wholesale.core.enums.ShipmentStatus;
import com.imhuso.wholesale.core.service.IApprovalConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 发货审批配置服务实现
 * 统一管理审批相关的配置读取和权限判断逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApprovalConfigServiceImpl implements IApprovalConfigService {

    private final ConfigService configService;

    /**
     * 审批配置键名常量
     */
    public static final class Keys {
        /**
         * 审批功能启用状态
         */
        public static final String APPROVAL_ENABLED = "wholesale.shipment.approval.enabled";
        /**
         * 自动发起审批申请
         */
        public static final String AUTO_REQUEST = "wholesale.shipment.approval.auto_request";
        /**
         * 自动审批通过
         */
        public static final String AUTO_APPROVE = "wholesale.shipment.approval.auto_approve";

        private Keys() {
        }
    }


    /**
     * 权限检查结果枚举
     */
    public enum PermissionResult {
        /**
         * 可以直接发货
         */
        CAN_SHIP,
        /**
         * 需要申请审批
         */
        NEED_REQUEST,
        /**
         * 等待审批结果
         */
        WAITING_APPROVAL,
        /**
         * 审批被拒绝，需要重新申请
         */
        NEED_REAPPLY,
        /**
         * 系统正在自动处理
         */
        SYSTEM_PROCESSING,
        /**
         * 订单状态不允许发货
         */
        ORDER_STATUS_INVALID,
        /**
         * 可以撤回审批申请
         */
        CAN_CANCEL_APPROVAL,
        /**
         * 可以进行审批操作（通过/拒绝）
         */
        CAN_APPROVE
    }

    // ===== 配置获取方法 =====

    @Override
    public boolean isApprovalEnabled() {
        return parseBoolean(configService.getConfigValue(Keys.APPROVAL_ENABLED));
    }

    @Override
    public boolean isAutoRequest() {
        return parseBoolean(configService.getConfigValue(Keys.AUTO_REQUEST));
    }

    @Override
    public boolean isAutoApprove() {
        return parseBoolean(configService.getConfigValue(Keys.AUTO_APPROVE));
    }

    @Override
    public boolean hasApprovalPermission() {
        try {
            // 使用 SaToken 检查权限
            return cn.dev33.satoken.stp.StpUtil.hasPermission("wholesale:shipment:approve");
        } catch (Exception e) {
            log.error("权限检查异常: {}", e.getMessage(), e);
            // 权限检查失败时抛出异常，不返回false
            throw new com.imhuso.common.core.exception.ServiceException("权限检查失败，请重新登录");
        }
    }

    // ===== 权限检查核心方法 =====

    @Override
    public boolean shouldShowCancelApprovalButton(WhsOrder order) {
        if (isInvalidForShipmentOperation(order)) {
            return false;
        }

        Integer approvalStatus = getApprovalStatus(order);
        // 只有待审批状态才能撤回
        return approvalStatus.equals(ShipmentApprovalStatus.PENDING.getValue());
    }

    @Override
    public boolean shouldShowApproveButton(WhsOrder order) {
        if (isInvalidForShipmentOperation(order)) {
            return false;
        }

        // 检查当前用户是否有审批权限
        if (!hasApprovalPermission()) {
            return false;
        }

        Integer approvalStatus = getApprovalStatus(order);
        // 只有待审批状态才能进行审批操作
        return approvalStatus.equals(ShipmentApprovalStatus.PENDING.getValue());
    }

    @Override
    public List<Integer> getShippableApprovalStatuses() {
        // 如果审批功能未启用，所有状态的订单都可以发货
        if (!isApprovalEnabled()) {
            return null; // 返回null表示不限制审批状态
        }

        // 审批功能已启用，需要根据配置判断
        boolean isAutoRequest = isAutoRequest();
        boolean isAutoApprove = isAutoApprove();

        if (isAutoRequest && isAutoApprove) {
            // 自动申请 + 自动审批：所有订单都能自动处理
            return null; // 返回null表示不限制审批状态
        } else {
            // 其他情况：只有已审批通过的订单才能发货
            return List.of(ShipmentApprovalStatus.APPROVED.getValue());
        }
    }

    // ===== 私有辅助方法 =====

    /**
     * 检查是否无法进行发货相关操作
     * 封装了订单状态检查和审批功能启用状态检查
     */
    private boolean isInvalidForShipmentOperation(WhsOrder order) {
        return !isValidOrderForShipment(order) || !isApprovalEnabled();
    }

    /**
     * 检查订单是否可以进行发货相关操作
     */
    private boolean isValidOrderForShipment(WhsOrder order) {
        if (order == null) {
            return false;
        }

        // 订单必须是处理中状态
        if (!OrderStatus.PROCESSING.getValue().equals(order.getOrderStatus())) {
            return false;
        }

        // 发货状态必须是待发货或部分发货
        Integer shipmentStatus = order.getShipmentStatus();
        return ShipmentStatus.PENDING.getValue().equals(shipmentStatus) || ShipmentStatus.PARTIAL_SHIPPED.getValue().equals(shipmentStatus);
    }

    /**
     * 获取审批状态（兼容null值）
     */
    private Integer getApprovalStatus(WhsOrder order) {
        Integer status = order.getShipmentApprovalStatus();
        return status != null ? status : ShipmentApprovalStatus.NO_APPROVAL.getValue();
    }

    /**
     * 解析布尔值
     */
    private boolean parseBoolean(String value) {
        if (!StringUtils.hasText(value)) {
            return false;
        }
        return "true".equalsIgnoreCase(value.trim());
    }
}
