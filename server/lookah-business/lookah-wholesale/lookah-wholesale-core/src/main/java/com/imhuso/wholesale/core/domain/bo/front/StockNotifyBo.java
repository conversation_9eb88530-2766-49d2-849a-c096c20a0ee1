package com.imhuso.wholesale.core.domain.bo.front;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 批发到货通知业务对象
 *
 * <AUTHOR>
 */
@Data
public class StockNotifyBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 通知ID
     */
    private Long id;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * 通知邮箱
     */
    private String email;

    /**
     * 通知状态
     */
    private Integer status;
}
