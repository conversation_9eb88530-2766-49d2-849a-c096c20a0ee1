package com.imhuso.wholesale.core.domain.vo.front;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 产品系列层级结构视图对象 (优化后的前端显示版本)
 * 包含产品系列 > 产品 > 变体 > 包装变体的完整层级
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductSeriesHierarchyVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 系列ID
     */
    private Long id;

    /**
     * 系列名称
     */
    private String name;

    /**
     * 系列描述
     */
    private String description;

    /**
     * 系列图片
     */
    private String image;

    /**
     * 产品列表
     */
    private List<ProductHierarchyVo> products;
}
