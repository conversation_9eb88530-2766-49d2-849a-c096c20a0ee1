package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 海外仓发货日志对象 whs_shipping_dispatch_log
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_shipping_dispatch_log")
public class WhsShippingDispatchLog extends BaseEntity {

    /**
     * 日志ID
     */
    @TableId
    private Long id;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 提供商ID
     */
    private Long providerId;

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 物流ID
     */
    private Long shippingId;

    /**
     * 外部系统参考ID
     */
    private String externalRefId;

    /**
     * 操作类型(CREATE/CANCEL/TRACK)
     */
    private String operationType;

    /**
     * 状态(SUCCESS/FAILED/PARTIAL)
     */
    private String status;

    /**
     * 请求数据
     */
    private String requestData;

    /**
     * 响应数据
     */
    private String responseData;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 处理耗时(毫秒)
     */
    private Integer processTime;

    /**
     * 商品项数量
     */
    private Integer itemsCount;

    /**
     * 总数量
     */
    private Integer totalQuantity;

    /**
     * 跟踪单号
     */
    private String trackingNumber;

    /**
     * 物流公司
     */
    private String logisticsCompany;
}
