package com.imhuso.wholesale.core.strategy.orderNotification;

import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderVo;
import com.imhuso.wholesale.core.event.WhsOrderEvent.WhsOrderEventType;

/**
 * 订单通知策略接口
 * <p>
 * 定义不同通知渠道的通用行为，各种渠道（如邮件、短信、应用推送等）需要实现此接口
 * </p>
 *
 * <AUTHOR>
 */
public interface IOrderNotificationStrategy {

    /**
     * 获取策略名称
     *
     * @return 通知策略名称
     */
    String getName();

    /**
     * 发送管理员通知
     *
     * @param order     订单对象
     * @param eventType 事件类型
     * @return 发送结果
     */
    boolean sendAdminNotification(WhsOrderVo order, WhsOrderEventType eventType);

    /**
     * 发送客户通知
     *
     * @param order     订单对象
     * @param eventType 事件类型
     * @return 发送结果
     */
    boolean sendCustomerNotification(WhsOrderVo order, WhsOrderEventType eventType);

    /**
     * 判断是否启用管理员通知
     *
     * @param eventType 事件类型
     * @return 是否启用
     */
    boolean isAdminNotificationEnabled(WhsOrderEventType eventType);

    /**
     * 判断是否启用客户通知
     *
     * @param eventType 事件类型
     * @return 是否启用
     */
    boolean isCustomerNotificationEnabled(WhsOrderEventType eventType);
}
