package com.imhuso.wholesale.core.enums;

import com.imhuso.common.translation.core.EnumTranslatableInterface;
import lombok.Getter;

/**
 * 到货通知状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum NotifyStatus implements EnumTranslatableInterface {

    /**
     * 待通知
     */
    PENDING(0),

    /**
     * 已通知
     */
    NOTIFIED(1);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 构造函数
     */
    NotifyStatus(int value) {
        this.value = value;
    }
}
