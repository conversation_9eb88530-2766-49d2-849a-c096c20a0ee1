package com.imhuso.wholesale.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.wholesale.core.domain.*;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderCreateBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderCreateItemBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsReplenishmentBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsReplenishmentVo;
import com.imhuso.wholesale.core.enums.OrderStatus;
import com.imhuso.wholesale.core.enums.PackagingType;
import com.imhuso.wholesale.core.mapper.*;
import com.imhuso.wholesale.core.service.IWhsOrderCreationService;
import com.imhuso.wholesale.core.service.IWhsReplenishmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 补货单服务实现类
 * <p>
 * 核心逻辑：
 * 1. 有库存数据
 * 2. 库存 < 仓库预警库存
 * 3. 包装类型 != 1
 * 4. 补货订单生成：洛杉矶仓缺货单品 → 芝加哥仓箱装补货
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsReplenishmentServiceImpl implements IWhsReplenishmentService {

    // 常量定义
    private static final Long LOS_ANGELES_WAREHOUSE_ID = 2001L; // 洛杉矶仓ID
    private static final Long CHICAGO_WAREHOUSE_ID = 2000L; // 芝加哥仓ID
    private static final Long REPLENISHMENT_CUSTOMER_ID = 1913538317361569794L; // 补货订单客户ID
    private static final Long REPLENISHMENT_PERSON_ID = 1913538317361569794L; // 补货订单指定人ID（用于获取默认地址）
    
    // 补货订单号生成相关常量
    private static final String ORDER_DATE_FORMAT = "yyMMdd"; // 订单号日期格式
    private static final String ORDER_PREFIX = "LO#"; // 订单号前缀
    private static final String REPLENISHMENT_IDENTIFIER = "(#2363)"; // 补货订单标识符

    private final WhsStockMapper stockMapper;
    private final WhsProductVariantMapper productVariantMapper;
    private final WhsShippingAddressMapper shippingAddressMapper;
    private final WhsOrderMapper orderMapper;
    private final WhsOrderItemMapper orderItemMapper;
    private final IWhsOrderCreationService orderCreationService;

    @Override
    public Long generateReplenishmentOrder() {
        log.info("开始生成补货订单");

        // 1. 查找需要补货的候选商品
        List<WhsReplenishmentVo> candidates = findReplenishmentCandidates();
        if (candidates.isEmpty()) {
            log.info("没有找到需要补货的商品");
            return null;
        }

        log.info("找到 {} 个需要补货的商品", candidates.size());

        // 2. 获取指定人的默认地址
        WhsShippingAddress address = getReplenishmentPersonDefaultAddress();
        if (address == null) {
            throw new ServiceException("未找到指定人的默认地址，无法生成补货订单");
        }

        // 3. 构建订单创建参数
        WhsOrderCreateBo orderBo = buildReplenishmentOrderBo(candidates, address);

        // 4. 创建订单
        try {
            boolean success = orderCreationService.manualOrder(orderBo);
            if (success) {
                log.info("补货订单创建成功，订单号: {}", orderBo.getOrderNo());

                // 5. 查询创建的订单并设置为草稿状态
                WhsOrder createdOrder = findOrderByOrderNo(orderBo.getOrderNo());
                if (createdOrder != null) {
                    // 设置为草稿状态
                    createdOrder.setOrderStatus(OrderStatus.DRAFT.getValue());
                    // 设置备注
                    createdOrder.setRemark("系统自动生成的补货订单（草稿状态，需人工确认）");
                    // 更新订单
                    orderMapper.updateById(createdOrder);
                    log.info("补货订单状态已设置为草稿，订单ID: {}", createdOrder.getId());
                    return createdOrder.getId();
                } else {
                    log.warn("无法找到刚创建的补货订单，订单号: {}", orderBo.getOrderNo());
                    return 1L; // 临时返回值，表示创建成功但无法获取ID
                }
            } else {
                throw new ServiceException("补货订单创建失败");
            }
        } catch (Exception e) {
            log.error("创建补货订单时发生异常", e);
            throw new ServiceException("创建补货订单失败：" + e.getMessage());
        }
    }

    @Override
    public List<WhsReplenishmentVo> findReplenishmentCandidates() {
        log.info("查找补货候选商品：洛杉矶仓缺货单品 → 芝加哥仓箱装库存");

        // 1. 查询洛杉矶仓缺货预警的单品
        WhsReplenishmentBo laQueryBo = new WhsReplenishmentBo();
        laQueryBo.setWarehouseId(LOS_ANGELES_WAREHOUSE_ID);
        laQueryBo.setOnlyNeedReplenishment(true);

        List<WhsReplenishmentVo> laShortageItems = stockMapper.selectReplenishmentList(laQueryBo);

        // 过滤出单品类型的商品
        List<WhsReplenishmentVo> laIndividualItems = laShortageItems.stream()
            .filter(item -> {
                // 通过变体ID查询包装类型
                WhsProductVariant variant = productVariantMapper.selectById(item.getVariantId());
                return variant != null && PackagingType.INDIVIDUAL.getValue().equals(variant.getPackagingType());
            })
            .toList();

        if (laIndividualItems.isEmpty()) {
            log.info("洛杉矶仓没有缺货的单品");
            return new ArrayList<>();
        }

        log.info("洛杉矶仓找到 {} 个缺货的单品", laIndividualItems.size());

        // 2. 排除已有草稿状态补货订单的商品
        Set<Long> excludeProductIds = getProductIdsWithPendingReplenishment();
        List<WhsReplenishmentVo> filteredItems = laIndividualItems.stream()
            .filter(item -> {
                WhsProductVariant variant = productVariantMapper.selectById(item.getVariantId());
                return variant != null && !excludeProductIds.contains(variant.getProductId());
            })
            .toList();

        if (filteredItems.isEmpty()) {
            log.info("所有缺货商品都已有待处理的补货订单");
            return new ArrayList<>();
        }

        log.info("排除已有补货订单后，剩余 {} 个缺货单品", filteredItems.size());

        // 3. 查找这些单品对应的箱装变体在芝加哥仓的库存
        List<WhsReplenishmentVo> replenishmentCandidates = new ArrayList<>();

        for (WhsReplenishmentVo laItem : filteredItems) {
            // 查找同产品的箱装变体
            WhsProductVariant individualVariant = productVariantMapper.selectById(laItem.getVariantId());
            if (individualVariant == null) {
                continue;
            }

            // 查找同产品的箱装变体
            LambdaQueryWrapper<WhsProductVariant> variantQuery = new LambdaQueryWrapper<>();
            variantQuery.eq(WhsProductVariant::getProductId, individualVariant.getProductId())
                .eq(WhsProductVariant::getPackagingType, PackagingType.CASE.getValue())
                .eq(WhsProductVariant::getStatus, BusinessConstants.NORMAL)
                .eq(WhsProductVariant::getDelFlag, BusinessConstants.LOGIC_NOT_DELETE);

            List<WhsProductVariant> caseVariants = productVariantMapper.selectList(variantQuery);

            for (WhsProductVariant caseVariant : caseVariants) {
                // 查询芝加哥仓该箱装变体的库存
                LambdaQueryWrapper<WhsStock> stockQuery = new LambdaQueryWrapper<>();
                stockQuery.eq(WhsStock::getVariantId, caseVariant.getId())
                    .eq(WhsStock::getWarehouseId, CHICAGO_WAREHOUSE_ID);

                WhsStock chicagoStock = stockMapper.selectOne(stockQuery);

                if (chicagoStock != null && chicagoStock.getAvailableStock() != null && chicagoStock.getAvailableStock() > 0) {
                    // 找到有库存的箱装变体，添加到候选列表
                    WhsReplenishmentVo candidate = WhsReplenishmentVo.builder()
                        .warehouseId(CHICAGO_WAREHOUSE_ID)
                        .warehouseName("芝加哥仓")
                        .variantId(caseVariant.getId())
                        .skuCode(caseVariant.getSkuCode())
                        .productName(laItem.getProductName())
                        .availableStock(chicagoStock.getAvailableStock())
                        .totalStock(chicagoStock.getTotalStock())
                        .lockedStock(chicagoStock.getLockedStock())
                        .recommendedQuantity(1) // 推荐补货1箱
                        .needReplenishment(true)
                        .build();

                    replenishmentCandidates.add(candidate);
                    log.info("找到补货候选：产品[{}] 洛杉矶单品缺货 → 芝加哥箱装[{}]有库存{}",
                        laItem.getProductName(), caseVariant.getSkuCode(), chicagoStock.getAvailableStock());
                    break; // 每个产品只选择一个箱装变体
                }
            }
        }

        log.info("最终找到 {} 个补货候选商品", replenishmentCandidates.size());
        return replenishmentCandidates;
    }

    @Override
    public List<WhsReplenishmentVo> exportReplenishmentList(WhsReplenishmentBo bo) {
        // 获取所有需要补货的数据（SQL已过滤）
        List<WhsReplenishmentVo> list = stockMapper.selectReplenishmentList(bo);

        // 设置补货标记和推荐数量
        for (WhsReplenishmentVo vo : list) {
            // SQL已过滤，都是需要补货的
            vo.setNeedReplenishment(true);

            // 计算有效预警库存：变体优先，否则使用仓库预警库存
            Integer effectiveAlertStock = getEffectiveAlertStock(vo.getAlertStock(), vo.getWarehouseAlertStock());
            vo.setAlertStock(effectiveAlertStock);

            // 计算推荐补货数量
            int recommendedQuantity = effectiveAlertStock - vo.getAvailableStock();
            vo.setRecommendedQuantity(Math.max(recommendedQuantity, 0));
        }

        return list;
    }

    @Override
    public Integer getEffectiveAlertStock(Integer variantAlertStock, Integer warehouseAlertStock) {
        // 变体级别优先，如果变体告警库存大于0，则使用变体级别
        if (variantAlertStock != null && variantAlertStock > 0) {
            return variantAlertStock;
        }

        // 否则使用仓库级别，如果仓库告警库存也为0或null，则返回0（不告警）
        if (warehouseAlertStock != null && warehouseAlertStock > 0) {
            return warehouseAlertStock;
        }

        // 如果都为0或null，返回0表示不需要告警
        return 0;
    }

    @Override
    public Integer getEffectiveAlertStockForVariant(Long variantId, Integer variantAlertStock) {
        // 简化版本：只返回变体本身的预警库存
        return variantAlertStock != null ? variantAlertStock : 0;
    }

    /**
     * 获取指定人的默认地址
     * 查找指定人的默认地址作为补货订单地址
     */
    private WhsShippingAddress getReplenishmentPersonDefaultAddress() {
        log.info("查找指定人的默认地址，人员ID: {}", REPLENISHMENT_PERSON_ID);

        // 查询指定人的默认地址
        LambdaQueryWrapper<WhsShippingAddress> query = new LambdaQueryWrapper<>();
        query.eq(WhsShippingAddress::getMemberId, REPLENISHMENT_PERSON_ID)
            .eq(WhsShippingAddress::getIsDefault, BusinessConstants.YES)
            .eq(WhsShippingAddress::getDelFlag, BusinessConstants.LOGIC_NOT_DELETE)
            .orderByDesc(WhsShippingAddress::getCreateTime)
            .last("LIMIT 1");

        WhsShippingAddress address = shippingAddressMapper.selectOne(query);

        if (address != null) {
            log.info("找到指定人的默认地址：{} {}", address.getFirstName(), address.getLastName());
        } else {
            log.warn("未找到指定人的默认地址");
        }

        return address;
    }

    /**
     * 根据订单号查找订单
     *
     * @param orderNo 订单号
     * @return 订单信息
     */
    private WhsOrder findOrderByOrderNo(String orderNo) {
        LambdaQueryWrapper<WhsOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WhsOrder::getInternalOrderNo, orderNo)
               .orderByDesc(WhsOrder::getCreateTime)
               .last("LIMIT 1");

        return orderMapper.selectOne(wrapper);
    }

    /**
     * 构建补货订单创建参数
     */
    private WhsOrderCreateBo buildReplenishmentOrderBo(List<WhsReplenishmentVo> candidates, WhsShippingAddress address) {
        log.info("构建补货订单参数，商品数量: {}", candidates.size());

        // 生成补货订单号
        String replenishmentOrderNo = generateReplenishmentOrderNo();

        // 使用Builder模式创建订单BO，确保字段正确设置
        WhsOrderCreateBo orderBo = WhsOrderCreateBo.builder()
                .orderNo(replenishmentOrderNo)                    // 内部订单号
                .customerOrderNo(null)                            // 客户订单号：补货订单无客户订单号
                .referenceNo(replenishmentOrderNo)                // 参考号：使用与订单号一致的格式
                .customerId(REPLENISHMENT_CUSTOMER_ID)            // 客户ID
                .customerAddressId(address.getId())               // 地址ID
                .discountAmount(BigDecimal.ZERO)                  // 折扣金额
                .isReplenishment(true)                            // 补货订单标识
                .build();

        // 构建订单项
        List<WhsOrderCreateItemBo> orderItems = new ArrayList<>();
        for (WhsReplenishmentVo candidate : candidates) {
            WhsOrderCreateItemBo item = WhsOrderCreateItemBo.builder()
                .variantId(candidate.getVariantId())
                .quantity(candidate.getRecommendedQuantity())
                .purchasePrice(BigDecimal.ZERO) // 补货订单采购价格为0
                .salesPrice(BigDecimal.ZERO) // 补货订单销售价格为0
                .build();

            orderItems.add(item);
        }

        orderBo.setItems(orderItems);

        log.info("补货订单参数构建完成，订单项数量: {}", orderItems.size());
        return orderBo;
    }

    /**
     * 生成补货订单号
     * 格式: LO#yyMMdd-[N]-(#2363)
     * 其中第一个不带 -1，从第二个开始才带序号
     *
     * @return 补货订单号
     */
    private String generateReplenishmentOrderNo() {
        // 获取当前日期
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(ORDER_DATE_FORMAT));

        // 查询当天已有的补货订单数量
        LambdaQueryWrapper<WhsOrder> query = new LambdaQueryWrapper<>();
        query.like(WhsOrder::getInternalOrderNo, ORDER_PREFIX + dateStr)
             .eq(WhsOrder::getIsReplenishment, true)
             .orderByDesc(WhsOrder::getCreateTime);

        List<WhsOrder> todayReplenishmentOrders = orderMapper.selectList(query);

        // 计算序号
        int sequence = todayReplenishmentOrders.size() + 1;

        // 生成订单号
        if (sequence == 1) {
            // 第一个不带序号
            return ORDER_PREFIX + dateStr + "-" + REPLENISHMENT_IDENTIFIER;
        } else {
            // 从第二个开始带序号
            return ORDER_PREFIX + dateStr + "-" + sequence + "-" + REPLENISHMENT_IDENTIFIER;
        }
    }

    /**
     * 获取已有待处理补货订单的商品ID集合
     * 防止重复生成补货订单
     *
     * @return 已有补货订单的商品ID集合
     */
    private Set<Long> getProductIdsWithPendingReplenishment() {
        // 查询待处理状态的补货订单（使用is_replenishment标识）
        // 排除状态：草稿、待处理、处理中（只有已完成和已取消的不排除）
        List<Integer> excludeStatuses = Arrays.asList(
            OrderStatus.DRAFT.getValue(),      // 草稿
            OrderStatus.PENDING.getValue(),    // 待处理
            OrderStatus.PROCESSING.getValue()  // 处理中
        );

        LambdaQueryWrapper<WhsOrder> orderQuery = new LambdaQueryWrapper<>();
        orderQuery.eq(WhsOrder::getIsReplenishment, true)
                  .in(WhsOrder::getOrderStatus, excludeStatuses);

        List<WhsOrder> pendingReplenishmentOrders = orderMapper.selectList(orderQuery);

        if (pendingReplenishmentOrders.isEmpty()) {
            return Collections.emptySet();
        }

        // 获取这些订单的商品ID
        Set<Long> productIds = new HashSet<>();
        for (WhsOrder order : pendingReplenishmentOrders) {
            // 查询订单项获取商品ID
            LambdaQueryWrapper<WhsOrderItem> itemQuery = new LambdaQueryWrapper<>();
            itemQuery.eq(WhsOrderItem::getOrderId, order.getId());

            List<WhsOrderItem> orderItems = orderItemMapper.selectList(itemQuery);
            for (WhsOrderItem item : orderItems) {
                // 通过变体ID获取商品ID
                WhsProductVariant variant = productVariantMapper.selectById(item.getVariantId());
                if (variant != null) {
                    productIds.add(variant.getProductId());
                }
            }
        }

        log.info("找到 {} 个待处理补货订单，涉及 {} 个商品",
                pendingReplenishmentOrders.size(), productIds.size());

        return productIds;
    }
}
