package com.imhuso.wholesale.core.service;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsShippingAddressBo;
import com.imhuso.wholesale.core.domain.bo.front.ShippingAddressBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsShippingAddressVo;
import com.imhuso.wholesale.core.domain.vo.front.ShippingAddressVo;

import java.util.List;

/**
 * 收货地址服务接口
 *
 * <AUTHOR>
 */
public interface IWhsShippingAddressService {

    /**
     * 查询收货地址列表
     *
     * @return 收货地址列表
     */
    List<ShippingAddressVo> selectAddressList();

    /**
     * 查询收货地址列表（分页）
     *
     * @param bo        地址查询参数
     * @param pageQuery 分页参数
     * @return 地址分页列表
     */
    TableDataInfo<WhsShippingAddressVo> queryPageList(WhsShippingAddressBo bo, PageQuery pageQuery);

    /**
     * 查询收货地址详情
     *
     * @param addressId 地址ID
     * @return 收货地址详情
     */
    ShippingAddressVo selectAddressById(Long addressId);

    /**
     * 查询收货地址详情（后台）
     *
     * @param addressId 地址ID
     * @return 地址详情
     */
    WhsShippingAddressVo selectAddressDetailById(Long addressId);

    /**
     * 获取默认收货地址
     *
     * @return 默认收货地址
     */
    ShippingAddressVo getDefaultAddress();

    /**
     * 按会员ID查询地址列表
     *
     * @param memberId 会员ID
     * @return 地址列表
     */
    List<WhsShippingAddressVo> getAddressesByMemberId(Long memberId);

    /**
     * 按会员ID分页查询地址列表
     *
     * @param memberId  会员ID
     * @param pageQuery 分页参数
     * @return 地址分页列表
     */
    TableDataInfo<WhsShippingAddressVo> getAddressesByMemberIdPage(Long memberId, PageQuery pageQuery);

    /**
     * 新增收货地址（前台）
     *
     * @param bo 收货地址信息
     * @return 地址ID
     */
    Long createAddress(ShippingAddressBo bo);

    /**
     * 新增收货地址（后台）
     *
     * @param bo 地址信息
     * @return 新增结果
     */
    int createAddress(WhsShippingAddressBo bo);

    /**
     * 修改收货地址（前台）
     *
     * @param bo 收货地址信息
     */
    void updateAddress(ShippingAddressBo bo);

    /**
     * 修改收货地址（后台）
     *
     * @param bo 地址信息
     * @return 更新结果
     */
    int updateAddress(WhsShippingAddressBo bo);

    /**
     * 删除收货地址（前台）
     *
     * @param addressId 地址ID
     */
    void deleteAddress(Long addressId);

    /**
     * 删除收货地址（后台）
     *
     * @param addressId 地址ID
     * @return 删除结果
     */
    int deleteAddressById(Long addressId);

    /**
     * 设置默认收货地址
     *
     * @param addressId 地址ID
     */
    void setDefaultAddress(Long addressId);
}
