package com.imhuso.wholesale.core.domain.bo.front;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;

/**
 * 批发商登录对象
 *
 * <AUTHOR>
 */
@Data
public class WhsLoginBody implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户名（邮箱）
     */
    @NotBlank(message = "{user.username.not.blank}")
    @Length(min = 2, max = 50, message = "{user.username.length.valid}")
    private String username;

    /**
     * 用户密码
     */
    @NotBlank(message = "{user.password.not.blank}")
    @Length(min = 5, max = 50, message = "{user.password.length.valid}")
    private String password;

    /**
     * 客户端id（可选，从请求头获取）
     */
    private String clientId;

    /**
     * 授权类型（可选，默认password）
     */
    private String grantType;

    /**
     * 验证码
     */
    private String code;

    /**
     * 唯一标识
     */
    private String uuid;

}
