package com.imhuso.wholesale.core.constant;

/**
 * Wholesale 模块常量定义
 *
 * <AUTHOR>
 */
public class WholesaleConstants {

    public static final Long LOCAL_WAREHOUSE_ID = 0L;

    // 模块命名
    public static class Module {
        /**
         * 模块名称
         */
        public static final String NAME = "wholesale";
    }

    public static class Client {
        /**
         * 客户端ID
         */
        public static final String ID_KEY = "clientId";
    }

    /**
     * Sa-Token 相关常量
     */
    public static class SaToken {
        /**
         * Wholesale 模块的 loginType
         */
        public static final String LOGIN_TYPE = Module.NAME;

        /**
         * User Type
         */
        public static final String USER_TYPE = Module.NAME + "_user";
    }

    /**
     * 系统相关常量
     */
    public static class System {
        /**
         * 系统管理员用户ID
         */
        public static final Long ADMIN_USER_ID = 1L;

        /**
         * 系统自动操作用户名
         */
        public static final String AUTO_USER_NAME = "系统自动";
    }

    /**
     * 审批相关常量
     */
    public static class Approval {
        /**
         * 系统自动申请的默认理由
         */
        public static final String SYSTEM_AUTO_REQUEST_REASON = "系统自动发起：订单状态变更为处理中";

        /**
         * 系统自动审批的默认意见
         */
        public static final String SYSTEM_AUTO_APPROVE_COMMENT = "系统自动审批通过";
    }
}
