package com.imhuso.wholesale.core.enums;

import com.imhuso.common.translation.core.EnumTranslatableInterface;
import lombok.Getter;

/**
 * 库存分配状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum StockAllocationStatus implements EnumTranslatableInterface {

    /**
     * 待处理
     */
    PENDING(0),

    /**
     * 已分配
     */
    ALLOCATED(1),

    /**
     * 已释放
     */
    RELEASED(2),

    /**
     * 已发货
     */
    SHIPPED(3);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 构造函数
     */
    StockAllocationStatus(int value) {
        this.value = value;
    }
}
