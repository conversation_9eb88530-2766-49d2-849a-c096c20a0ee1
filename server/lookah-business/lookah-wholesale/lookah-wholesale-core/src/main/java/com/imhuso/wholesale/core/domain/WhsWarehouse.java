package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 店铺仓库对象 whs_warehouse
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_warehouse")
public class WhsWarehouse extends BaseEntity {
    /**
     * 仓库ID
     */
    @TableId
    private Long id;

    /**
     * 仓库名称
     */
    private String name;

    /**
     * 仓库编码
     */
    private String code;

    /**
     * 仓库地址
     */
    private String address;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 状态（0停用 1启用）
     */
    private String status;

    /**
     * 是否默认（N否 Y是）
     */
    private String isDefault;

    /**
     * 提供商ID
     */
    private Long providerId;

    /**
     * 发货优先级(1-10，数字越小优先级越高)
     */
    private Integer priority;

    /**
     * 是否海外仓(Y/N)
     */
    private String isOverseas;

    /**
     * 发货规则配置(JSON)
     */
    private String shippingRules;

    /**
     * 仓库配置信息(JSON)
     */
    private String warehouseConfig;

    /**
     * 关联的账号ID
     * 关联到whs_overseas_warehouse_account表
     */
    private Long accountId;

    /**
     * 仓库级别告警库存阈值
     * 默认为0表示使用变体级别告警库存
     */
    private Integer alertStock;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;
}
