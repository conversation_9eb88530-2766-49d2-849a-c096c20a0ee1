package com.imhuso.wholesale.core.domain.bo.admin;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 库存操作聚合BO
 *
 * <AUTHOR>
 */
public class StockOperationBo {

    /**
     * 库存释放聚合项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StockReleaseItem {
        private Long variantId;
        private Long warehouseId;
        private Long orderItemId;
        private int quantity;
        private Integer packagingType;
        private Integer packagingQuantity;

        /**
         * 获取聚合键
         */
        public String getKey() {
            return variantId + ":" + warehouseId;
        }
    }

    /**
     * 转换释放聚合项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConversionReleaseItem {
        private Long actualVariantId;
        private Long originalVariantId;
        private Long orderItemId;
        private int actualQuantity;
        private int packagingQuantity;
    }

    /**
     * 出库操作聚合项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OutboundItem {
        private Long variantId;
        private Long warehouseId;
        private Long orderItemId;
        private int quantity;
        private Integer packagingType;
        private Integer packagingQuantity;

        /**
         * 获取聚合键
         */
        public String getKey() {
            return variantId + ":" + warehouseId;
        }
    }
}
