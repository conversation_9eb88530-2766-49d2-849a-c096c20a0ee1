package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.WhsProductCategory;
import com.imhuso.wholesale.core.domain.WhsProductSeries;

import java.util.List;
import java.util.Map;

/**
 * 批发产品缓存Service接口
 * 用于解决@Cacheable自调用问题
 *
 * <AUTHOR>
 */
public interface IWhsProductCacheService {

    /**
     * 获取分类映射
     *
     * @param categoryId 分类ID，为null时获取所有分类
     * @return 分类映射，key为分类ID，value为分类信息
     */
    Map<Long, WhsProductCategory> getCategoryMap(Long categoryId);

    /**
     * 获取所有启用的系列
     *
     * @return 系列列表
     */
    List<WhsProductSeries> getEnabledSeries();
}
