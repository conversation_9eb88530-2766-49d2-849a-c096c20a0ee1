package com.imhuso.wholesale.core.adapter;

import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.vo.warehouse.*;

import java.util.List;
import java.util.Map;

/**
 * 海外仓提供商接口
 *
 * <AUTHOR>
 */
public interface IOverseasWarehouseProvider {

    /**
     * 获取提供商类型
     *
     * @return 提供商类型编码
     */
    String getProviderType();

    /**
     * 获取支持的产品映射策略类型
     * 例如："SKU"、"UPC"、"EXTERNAL"
     * 默认返回null，表示使用系统默认策略
     *
     * @return 支持的产品映射策略类型
     */
    default String getMappingStrategyType() {
        return "SKU"; // 默认使用SKU映射策略
    }

    /**
     * 同步库存
     *
     * @param context  仓库上下文
     * @param variants 变体列表
     * @return 同步结果
     */
    StockSyncResultVo syncStock(WarehouseContextVo context, List<WhsProductVariant> variants);

    /**
     * 同步物流渠道
     *
     * @param context 仓库上下文
     * @return 同步结果
     */
    LogisticsChannelSyncResultVo syncLogisticsChannels(WarehouseContextVo context);

    /**
     * 将ShipmentInfoVo转换为提供商所需的格式
     *
     * @param context 仓库上下文
     * @param shipmentInfo 发货信息
     * @param packageId 包裹ID (用于 SecondCsRefNo)
     * @param batchNumber 批次号 (用于 CsRefNo 格式化, >1 时添加后缀)
     * @return 转换后的Map
     */
    Map<String, Object> convertShipmentInfo(WarehouseContextVo context, ShipmentInfoVo shipmentInfo, Long packageId, Integer batchNumber);

    /**
     * 创建发货订单到海外仓
     *
     * @param context 仓库上下文，包含配置信息
     * @param orderData 发货订单数据 (暂用 Map<String, Object> 替代, 后续可优化为具体BO)
     * @return 创建结果
     */
    CreateOrderResultVo createShipmentOrder(WarehouseContextVo context, Map<String, Object> orderData);

    /**
     * 取消海外仓订单
     *
     * @param context 仓库上下文
     * @param packageId 包裹ID (用于可能的内部查找或日志记录)
     * @param externalShipmentId 用于取消的外部系统发货单号
     * @return 取消结果
     */
    CancelOrderResultVo cancelOrder(WarehouseContextVo context, Long packageId, String externalShipmentId);

    /**
     * 同步入库单
     *
     * @param context 仓库上下文
     * @return 同步结果
     */
    InboundOrderSyncResultVo syncInboundOrders(WarehouseContextVo context);
}
