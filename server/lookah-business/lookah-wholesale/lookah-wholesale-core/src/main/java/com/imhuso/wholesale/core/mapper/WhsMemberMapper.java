package com.imhuso.wholesale.core.mapper;

import com.imhuso.common.mybatis.annotation.DataColumn;
import com.imhuso.common.mybatis.annotation.DataPermission;
import com.imhuso.common.mybatis.core.mapper.BaseMapperPlus;
import com.imhuso.wholesale.core.domain.WhsMember;
import com.imhuso.wholesale.core.domain.vo.admin.WhsMemberVo;
import org.apache.ibatis.annotations.Mapper;

/**
 * 商店客户(WhsMember)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-02-11 16:56:54
 */
@Mapper
@DataPermission(value = {
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by"),
        @DataColumn(key = "salespersonName", value = "salesperson_id")
}, joinStr = "OR")
public interface WhsMemberMapper extends BaseMapperPlus<WhsMember, WhsMemberVo> {
}
