package com.imhuso.wholesale.core.service;

import java.util.List;

/**
 * 通知配置服务接口
 * 提供通知相关配置的读取功能
 *
 * <AUTHOR>
 */
public interface INotificationConfigService {

    // ===== 订单通知配置 =====

    /**
     * 获取订单邮件通知收件人列表
     */
    List<String> getOrderEmailList();

    /**
     * 获取订单企业微信通知是否启用
     */
    boolean isOrderWecomEnabled();

    /**
     * 获取订单企业微信 webhook key 列表
     */
    List<String> getOrderWecomKeys();

    /**
     * 获取订单SSE站内信通知管理员用户ID列表
     */
    List<String> getOrderSseAdminUserIds();

    // ===== 补货通知配置 =====

    /**
     * 获取补货邮件通知是否启用
     */
    boolean isStockEmailEnabled();

    /**
     * 获取补货邮件通知收件人列表
     */
    List<String> getStockEmailList();

    /**
     * 获取补货企业微信通知是否启用
     */
    boolean isStockWecomEnabled();

    /**
     * 获取补货企业微信 webhook key 列表
     */
    List<String> getStockWecomKeys();

    // ===== 企业微信配置 =====

    /**
     * 获取默认企业微信 webhook key 列表
     */
    List<String> getDefaultWecomKeys();
}