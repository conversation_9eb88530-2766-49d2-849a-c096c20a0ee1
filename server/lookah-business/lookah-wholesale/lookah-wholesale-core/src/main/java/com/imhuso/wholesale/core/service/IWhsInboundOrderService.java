package com.imhuso.wholesale.core.service;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsInboundOrderListBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsInboundOrderVo;

/**
 * 入库单服务接口
 *
 * 注意：入库单数据由海外仓同步服务自动同步，后台API仅提供只读操作
 *
 * <AUTHOR>
 */
public interface IWhsInboundOrderService {

    /**
     * 查询入库单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 入库单列表
     */
    TableDataInfo<WhsInboundOrderVo> queryInboundOrderList(WhsInboundOrderListBo bo, PageQuery pageQuery);

    /**
     * 查询入库单详情
     *
     * @param id 入库单ID
     * @return 入库单详情
     */
    WhsInboundOrderVo getInboundOrderById(Long id);

    /**
     * 根据产品ID查询相关的在途入库单
     *
     * @param productId 产品ID
     * @param pageQuery 分页参数
     * @return 入库单列表
     */
    TableDataInfo<WhsInboundOrderVo> queryInboundOrdersByProductId(Long productId, PageQuery pageQuery);

    /**
     * 根据变体ID查询相关的在途入库单
     *
     * @param variantId 变体ID
     * @param pageQuery 分页参数
     * @return 入库单列表
     */
    TableDataInfo<WhsInboundOrderVo> queryInboundOrdersByVariantId(Long variantId, PageQuery pageQuery);
}
