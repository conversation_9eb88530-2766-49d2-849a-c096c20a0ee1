package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.wholesale.core.domain.WhsOrderItem;
import com.imhuso.wholesale.core.domain.WhsShipmentPlan;
import com.imhuso.wholesale.core.domain.WhsShipmentPlanItem;
import com.imhuso.wholesale.core.domain.bo.admin.WhsShipmentConversionBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsShipmentPlanBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsShipmentPlanGenerationBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsShipmentPlanItemBo;
import com.imhuso.wholesale.core.domain.vo.admin.*;
import com.imhuso.wholesale.core.enums.PackagingType;
import com.imhuso.wholesale.core.enums.ShipmentPlanStatus;
import com.imhuso.wholesale.core.mapper.WhsShipmentPlanItemMapper;
import com.imhuso.wholesale.core.mapper.WhsShipmentPlanMapper;
import com.imhuso.wholesale.core.service.*;
import com.imhuso.wholesale.core.strategy.shipmentPlan.IWhsShipmentPlanStrategy;
import com.imhuso.wholesale.core.strategy.shipmentPlan.WhsShipmentPlanStrategyFactory;
import com.imhuso.wholesale.core.utils.ShipmentPlanUtils;
import com.imhuso.wholesale.core.utils.WhsEnumTranslationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 发货方案生成服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsShipmentPlanGenerationServiceImpl implements IWhsShipmentPlanGenerationService {
    // 订单项服务
    private final IWhsOrderItemService orderItemService;
    // 包装项服务
    private final IWhsPackageItemService packageItemService;
    // 变体服务
    private final IWhsProductVariantService productVariantService;
    // 发货方案Mapper
    private final WhsShipmentPlanMapper shipmentPlanMapper;
    // 发货方案项Mapper
    private final WhsShipmentPlanItemMapper shipmentPlanItemMapper;
    // 发货方案转换服务
    private final IWhsShipmentConversionService shipmentConversionService;
    // 订单服务
    private final IWhsOrderService orderService;

    private final WhsShipmentPlanStrategyFactory strategyFactory;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateShipmentPlans(Long orderId) {
        // 1. 根据订单准备数据
        WhsShipmentPlanGenerationVo generationVo = prepareOrderDataForPlan(orderId);
        if (CollUtil.isEmpty(generationVo.getPackagingOptions())) {
            throw new ServiceException("没有可用的包装选项");
        }

        // 2. 根据策略生成方案
        // 2.1 获取所有启用的策略
        List<IWhsShipmentPlanStrategy> enabledStrategies = strategyFactory.getAllEnabledStrategies();
        if (CollUtil.isEmpty(enabledStrategies)) {
            log.warn("没有可用的发货方案策略，请确保至少有一个策略被标记为启用");
            throw new ServiceException("没有可用的发货方案策略");
        }

        log.info("为订单[{}]生成发货方案，发现{}个可用策略", orderId, enabledStrategies.size());

        // 2.2 遍历每一个策略，调用方案生成
        for (IWhsShipmentPlanStrategy strategy : enabledStrategies) {
            try {
                log.info("使用策略[{}]为订单[{}]生成发货方案", strategy.getStrategyName(), orderId);
                WhsShipmentPlanGenerationBo planBo = strategy.generate(generationVo);
                // 只要是系统方案，则设置为false
                planBo.setCustom(false);

                // 2.3 处理并保存方案
                if (handleAndSaveShipmentPlan(planBo) && planBo.getPlanId() != null) {
                    log.info("策略[{}]成功生成方案ID: {}", strategy.getStrategyName(), planBo.getPlanId());
                }
            } catch (Exception e) {
                log.error("使用策略[{}]生成方案时发生错误: {}", strategy.getStrategyName(), e.getMessage(), e);
            }
        }
    }

    /**
     * 准备计划数据用于生成方案
     */
    @Override
    public WhsShipmentPlanGenerationVo prepareOrderDataForPlan(Long orderId) {
        // 1. 获取订单
        WhsOrderVo order = orderService.getOrderById(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        // 1. 获取订单项信息
        List<WhsOrderItem> orderItems = orderItemService.getOrderItems(orderId);
        if (CollUtil.isEmpty(orderItems)) {
            throw new ServiceException("订单项不存在");
        }

        // 2. 转换为VO
        List<WhsOrderItemVo> orderItemVos = orderItemService.getOrderItemsWithDetails(orderId);

        // 3. 收集所有变体ID
        List<Long> variantIds = orderItems.stream().map(WhsOrderItem::getVariantId).filter(Objects::nonNull)
            .distinct().collect(Collectors.toList());

        if (CollUtil.isEmpty(variantIds)) {
            throw new ServiceException("订单项中没有有效的变体ID");
        }

        // 4. 获取变体之间的关系信息
        Map<Long, Map<Long, Integer>> packagingRelations = packageItemService.getAllPackageRelationships(variantIds);

        // 5. 为每个订单项提取包装选项
        List<WhsPackagingOptionVo> packagingOptions = new ArrayList<>();
        for (WhsOrderItem item : orderItems) {
            Long variantId = item.getVariantId();
            if (variantId == null) {
                continue;
            }

            // 获取变体详情
            WhsProductVariantVo variant = productVariantService.getVariant(variantId, false);
            if (variant == null) {
                log.warn("无法获取订单项 {} 的变体详情 (VariantId: {})", item.getId(), variantId);
                continue;
            }

            WhsPackagingOptionVo option = buildBasePackageOption(item, variant, packagingRelations);
            packagingOptions.add(option);
        }

        // 6. 构建并返回结果
        return WhsShipmentPlanGenerationVo.builder().orderId(orderId).orderItems(orderItemVos)
            .totalPcs(order.getTotalPcs()).packagingOptions(packagingOptions).build();

    }

    @Override
    public void submitCustomShipmentPlan(WhsShipmentPlanGenerationBo planBo) {
        // 设置为自定义方案
        planBo.setCustom(true);
        handleAndSaveShipmentPlan(planBo);
    }

    /**
     * 处理并保存方案
     */
    public boolean handleAndSaveShipmentPlan(WhsShipmentPlanGenerationBo generateBo) {
        Long orderId = generateBo.getOrderId();
        if (orderId == null) {
            throw new ServiceException("订单ID不能为空");
        }

        // 1. 验证是否可以创建方案
        validateCanCreatePlan(orderId);

        // 2. 获取订单中的所有订单项
        List<WhsOrderItem> orderItems = orderItemService.getOrderItems(orderId);
        if (CollUtil.isEmpty(orderItems)) {
            throw new ServiceException("订单没有商品项，无法创建方案");
        }

        // 3. 将策略或用户输入的方案转换为方案BO
        WhsShipmentPlanBo planBo = convertToPlanBo(generateBo, orderItems);

        // 4. 验证方案配置, 对方案进行验证
        validatePlanBasicData(planBo, orderItems);

        // 5. 创建并保存方案和方案项
        Long planId = createAndSaveShipmentPlan(planBo);

        // 6. 计算转换记录
        List<WhsShipmentConversionBo> conversions = calculateShipmentPlanConversions(orderItems, planBo);

        // 7. 保存转换记录
        saveShipmentPlanConversions(planId, conversions);

        log.info("为订单[{}]创建自定义方案[{}]成功", orderId, planId);

        return true;
    }

    /**
     * 验证方案基本数据
     * 分离自validateAndSetPlanData方法，用于在计算转换记录前验证基本数据
     */
    private void validatePlanBasicData(WhsShipmentPlanBo planBo, List<WhsOrderItem> orderItems) {
        List<Long> variantIds = planBo.getPlanItems().stream().map(WhsShipmentPlanItemBo::getVariantId)
            .filter(Objects::nonNull).distinct().collect(Collectors.toList());

        Map<Long, WhsProductVariantVo> variantMap = productVariantService.getVariantsByIds(variantIds, false).stream()
            .collect(Collectors.toMap(WhsProductVariantVo::getId, v -> v));

        // 1. 验证方案项
        validatePlanItems(planBo.getPlanItems(), variantMap);

        // 2. 如果订单的总数量和方案的总数量不一致，抛出异常
        int orderTotalPcs = orderItemService.calculateTotalPcs(orderItems);
        if (planBo.getTotalPcs() != orderTotalPcs) {
            throw new ServiceException("订单项数量和方案项数量不一致");
        }
    }

    /**
     * 根据订单项和方案项计算转换记录
     * 识别原始订单中的变体如何转换成方案中的变体，进行库存转换追踪
     * 注意：
     * - 此方法在方案和方案项保存后调用
     * - 返回的转换记录将在 saveShipmentPlanConversions 中设置 planId 和 planItemId
     */
    private List<WhsShipmentConversionBo> calculateShipmentPlanConversions(List<WhsOrderItem> orderItems,
                                                                             WhsShipmentPlanBo planBo) {
        // 按照变体ID构造订单Map
        Map<Long, WhsOrderItem> orderItemMap = orderItems.stream()
            .collect(Collectors.toMap(WhsOrderItem::getVariantId, v -> v));

        List<WhsShipmentConversionBo> conversions = new ArrayList<>();

        // 1. 检查是否存在不允许的拆箱行为
        for (WhsShipmentPlanItemBo planItem : planBo.getPlanItems()) {
            WhsOrderItem orderItem = orderItemMap.get(planItem.getVariantId());
            if (orderItem == null) {
                continue;
            }

            // 如果是箱装，则不允许拆箱
            if (Objects.equals(orderItem.getPackagingType(), PackagingType.CASE.getValue())) {
                // 比如原始订单中有1个，则计划中至少有1个
                if (orderItem.getQuantity() > planItem.getQuantity()) {
                    log.error("箱装订单项不允许拆箱，订单变体 {} 的订单数量为 {}，计划数量为 {}",
                        orderItem.getVariantId(), orderItem.getQuantity(), planItem.getQuantity());
                    throw new ServiceException("箱装订单项不允许拆箱");
                }
            }
        }

        // 2. 创建PCS追踪地图：记录每个变体ID的原始PCS和方案PCS
        Map<Long, Integer> originalPcsMap = new HashMap<>(); // 变体ID -> 原始订单中的总PCS
        Map<Long, Integer> planPcsMap = new HashMap<>(); // 变体ID -> 方案中的总PCS

        // 计算原始订单中每个变体的总PCS
        for (WhsOrderItem orderItem : orderItems) {
            Long variantId = orderItem.getVariantId();
            calculateAndAddPcs(originalPcsMap, variantId, orderItem.getQuantity(), orderItem.getPackagingQuantity());
        }

        // 计算方案中每个变体的总PCS
        for (WhsShipmentPlanItemBo planItem : planBo.getPlanItems()) {
            Long variantId = planItem.getVariantId();
            calculateAndAddPcs(planPcsMap, variantId, planItem.getQuantity(), planItem.getPackagingQuantity());
        }

        // 3. 检查PCS变化，找出转换关系
        Set<Long> allVariantIds = new HashSet<>();
        allVariantIds.addAll(originalPcsMap.keySet());
        allVariantIds.addAll(planPcsMap.keySet());

        // 3.1 找出减少PCS的变体（源变体）和增加PCS的变体（目标变体）
        Map<Long, Integer> pcsReduction = new HashMap<>(); // 变体ID -> 减少的PCS
        Map<Long, Integer> pcsIncrease = new HashMap<>(); // 变体ID -> 增加的PCS

        for (Long variantId : allVariantIds) {
            int originalPcs = originalPcsMap.getOrDefault(variantId, 0);
            int planPcs = planPcsMap.getOrDefault(variantId, 0);
            int diff = planPcs - originalPcs;

            if (diff < 0) {
                // 计划中此变体的PCS减少了，记录减少量
                pcsReduction.put(variantId, -diff);
            } else if (diff > 0) {
                // 计划中此变体的PCS增加了，记录增加量
                pcsIncrease.put(variantId, diff);
            }
            // diff = 0 的情况表示没有变化，不需要处理
        }

        // 4. 创建转换记录
        // 遍历所有减少PCS的变体（源变体）
        for (Map.Entry<Long, Integer> srcEntry : pcsReduction.entrySet()) {
            Long srcVariantId = srcEntry.getKey();
            int reductionPcs = srcEntry.getValue();

            WhsOrderItem srcOrderItem = orderItemMap.get(srcVariantId);
            if (srcOrderItem == null) {
                log.warn("源变体ID {}在订单项中未找到", srcVariantId);
                continue;
            }

            // 在增加PCS的变体中寻找转换目标
            for (Map.Entry<Long, Integer> destEntry : pcsIncrease.entrySet()) {
                Long destVariantId = destEntry.getKey();
                int increasePcs = destEntry.getValue();

                // 计算能转换的PCS数量（取两者最小值）
                int conversionPcs = Math.min(reductionPcs, increasePcs);
                if (conversionPcs <= 0) {
                    continue;
                }

                // 获取目标变体的方案项信息
                Optional<WhsShipmentPlanItemBo> destPlanItemOpt = planBo.getPlanItems().stream()
                    .filter(item -> Objects.equals(item.getVariantId(), destVariantId))
                    .findFirst();

                if (destPlanItemOpt.isEmpty()) {
                    log.warn("目标变体ID {}在方案项中未找到", destVariantId);
                    continue;
                }

                WhsShipmentPlanItemBo destPlanItem = destPlanItemOpt.get();
                int destPackagingQuantity = destPlanItem.getPackagingQuantity() != null
                    && destPlanItem.getPackagingQuantity() > 0 ? destPlanItem.getPackagingQuantity() : 1;

                // 计算转换后的数量：根据目标包装数量计算
                int actualQuantity = Math.max(1, conversionPcs / destPackagingQuantity); // 保证至少有1个单位
                if (actualQuantity == 1 && destPackagingQuantity > 1) {
                    log.warn(
                        "转换后数量计算为1，转换PCS={}, destPackagingQuantity={}: srcVariantId={}, destVariantId={}",
                        conversionPcs, destPackagingQuantity, srcVariantId, destVariantId);
                }

                // 获取原始变体的包装数量
                int srcPackagingQuantity = srcOrderItem.getPackagingQuantity() != null
                    && srcOrderItem.getPackagingQuantity() > 0
                    ? srcOrderItem.getPackagingQuantity() : 1;

                // 计算原始数量
                int originalQuantity = conversionPcs / srcPackagingQuantity;

                // 创建转换记录
                WhsShipmentConversionBo conversion = WhsShipmentConversionBo.builder()
                    .orderId(planBo.getOrderId())
                    .originalVariantId(srcVariantId)
                    .originalSkuCode(srcOrderItem.getSkuCode())
                    .originalQuantity(originalQuantity)
                    .originalPackagingType(srcOrderItem.getPackagingType())
                    .actualVariantId(destVariantId)
                    .actualSkuCode(destPlanItem.getSkuCode())
                    .actualQuantity(actualQuantity)
                    .actualPackagingType(destPlanItem.getPackagingType())
                    .remark(String.format("%s个%s(SKU:%s) → %s个%s(SKU:%s), 共转换%s件",
                        originalQuantity,
                        WhsEnumTranslationUtils.translate(PackagingType.class,
                            srcOrderItem.getPackagingType()),
                        srcOrderItem.getSkuCode(),
                        actualQuantity,
                        WhsEnumTranslationUtils.translate(PackagingType.class,
                            destPlanItem.getPackagingType()),
                        destPlanItem.getSkuCode(),
                        conversionPcs))
                    .build();

                conversions.add(conversion);

                // 更新剩余需要处理的PCS数量
                reductionPcs -= conversionPcs;
                increasePcs -= conversionPcs;
                destEntry.setValue(increasePcs);

                if (reductionPcs <= 0) {
                    break; // 当前源变体已完全转换
                }
            }

            // 更新剩余减少量
            srcEntry.setValue(reductionPcs);

            // 检查是否还有未处理完的减少量
            if (reductionPcs > 0) {
                log.warn("变体ID {}的PCS减少量{}未能完全匹配到转换目标", srcVariantId, reductionPcs);
            }
        }

        // 检查是否还有未处理完的增加量
        for (Map.Entry<Long, Integer> entry : pcsIncrease.entrySet()) {
            if (entry.getValue() > 0) {
                log.warn("变体ID {}的PCS增加量{}未能完全匹配到转换源", entry.getKey(), entry.getValue());
            }
        }

        return conversions;
    }

    /**
     * 将策略或用户输入的方案转换为方案BO
     * 配合订单项，可以设置更多信息
     */
    private WhsShipmentPlanBo convertToPlanBo(WhsShipmentPlanGenerationBo generateBo,
                                                List<WhsOrderItem> orderItems) {
        // 根据订单项，构造一个Map，用来获取包装类型
        Map<Long, WhsOrderItem> orderItemMap = orderItems.stream()
            .collect(Collectors.toMap(WhsOrderItem::getVariantId, v -> v));

        // 构造方案项
        List<WhsShipmentPlanItemBo> planItems = generateBo.getPlanItems().stream()
            .map(item -> WhsShipmentPlanItemBo.builder().orderId(generateBo.getOrderId())
                .variantId(item.getVariantId()).quantity(item.getQuantity())
                .skuCode(orderItemMap.get(item.getVariantId()).getSkuCode())
                .packagingType(orderItemMap.get(item.getVariantId()).getPackagingType())
                .packagingQuantity(orderItemMap.get(item.getVariantId()).getPackagingQuantity()).build())
            .collect(Collectors.toList());

        // 构造方案
        WhsShipmentPlanBo planBo = new WhsShipmentPlanBo();
        planBo.setOrderId(generateBo.getOrderId());
        planBo.setPlanName(generateBo.getPlanName());
        planBo.setPackageCount(generateBo.getPlanItems().size());
        planBo.setTotalPcs(ShipmentPlanUtils.calculateTotalPcs(planItems));
        planBo.setScore(ShipmentPlanUtils.calculateLargeUnitScore(planItems));
        planBo.setIsCustom(generateBo.isCustom());
        planBo.setPlanItems(planItems);

        return planBo;
    }

    /**
     * 保存转换记录
     */
    private void saveShipmentPlanConversions(Long planId, List<WhsShipmentConversionBo> conversions) {
        if (CollUtil.isNotEmpty(conversions)) {
            // 查询该计划下的方案项，用于设置 planItemId
            List<WhsShipmentPlanItem> planItems = shipmentPlanItemMapper.selectList(
                Wrappers.<WhsShipmentPlanItem>lambdaQuery()
                    .eq(WhsShipmentPlanItem::getPlanId, planId));

            // 按变体ID建立方案项ID的映射
            Map<Long, Long> variantToPlanItemIdMap = planItems.stream()
                .collect(Collectors.toMap(WhsShipmentPlanItem::getVariantId, WhsShipmentPlanItem::getId,
                    (a, b) -> a));

            // 设置每个转换记录的planId和planItemId
            for (WhsShipmentConversionBo conversion : conversions) {
                conversion.setPlanId(planId);

                // 根据actualVariantId查找对应的planItemId
                Long actualVariantId = conversion.getActualVariantId();
                if (actualVariantId != null && variantToPlanItemIdMap.containsKey(actualVariantId)) {
                    conversion.setPlanItemId(variantToPlanItemIdMap.get(actualVariantId));
                } else {
                    log.warn("无法为转换记录找到对应的planItemId，actualVariantId: {}", actualVariantId);
                }
            }

            shipmentConversionService.batchSaveFromPlanItems(conversions);
        }
    }

    /**
     * 验证方案项正确性
     */
    private void validatePlanItems(List<WhsShipmentPlanItemBo> planItems,
                                   Map<Long, WhsProductVariantVo> variantMap) {
        for (WhsShipmentPlanItemBo planItem : planItems) {
            Long variantId = planItem.getVariantId();

            // 验证变体是否存在于订单中
            if (!variantMap.containsKey(variantId)) {
                throw new ServiceException("自定义方案中包含订单中不存在的变体，ID: " + variantId);
            }

            // 验证变体信息是否有效
            WhsProductVariantVo variant = variantMap.get(variantId);
            if (variant == null) {
                throw new ServiceException("无法获取变体信息，变体ID: " + variantId);
            }

            // 验证数量不能为负或零
            if (planItem.getQuantity() == null || planItem.getQuantity() <= 0) {
                throw new ServiceException("自定义方案中变体数量必须大于0");
            }
        }
    }

    /**
     * 验证订单是否允许创建新的发货方案
     * （不允许在有已执行的方案后创建）
     */
    private void validateCanCreatePlan(Long orderId) {
        long count = shipmentPlanMapper
            .selectCount(Wrappers.<WhsShipmentPlan>lambdaQuery().eq(WhsShipmentPlan::getOrderId, orderId)
                .eq(WhsShipmentPlan::getStatus, ShipmentPlanStatus.EXECUTED.getValue()));

        if (count > 0) {
            throw new ServiceException("订单已有已执行的发货方案，无法创建新的自定义方案");
        }
    }

    /**
     * 获取包装选项
     */
    private WhsPackagingOptionVo buildBasePackageOption(WhsOrderItem item, WhsProductVariantVo variant,
                                                          Map<Long, Map<Long, Integer>> packagingRelations) {
        Integer packagingType = variant.getPackagingType();
        if (packagingType == null) {
            packagingType = PackagingType.INDIVIDUAL.getValue();
        }

        String packagingTypeText = WhsEnumTranslationUtils.translate(PackagingType.class, packagingType);

        Integer packagingQuantity = variant.getPackageQuantity();
        if (packagingQuantity == null || packagingQuantity <= 0) {
            packagingQuantity = item.getPackagingQuantity();
            if (packagingQuantity == null || packagingQuantity <= 0) {
                packagingQuantity = 1;
            }
        }

        if (!packagingType.equals(PackagingType.INDIVIDUAL.getValue()) && (packagingQuantity <= 1)) { // Simplified
            // check
            Map<Long, Integer> childRelations = packagingRelations.get(variant.getId());
            if (childRelations != null && !childRelations.isEmpty()) {
                packagingQuantity = childRelations.values().iterator().next();
                log.info("从包装关系中获取包装数量: {}", packagingQuantity);
            }
        }

        Long childVariantId = null;
        if (packagingRelations.containsKey(variant.getId())) {
            Set<Long> childIds = packagingRelations.get(variant.getId()).keySet();
            if (!childIds.isEmpty()) {
                childVariantId = childIds.iterator().next();
                log.info("变体ID: {}, 包装类型: {}, 包装数量: {}, 子变体ID: {}", variant.getId(), packagingType, packagingQuantity,
                    childVariantId);
            } else {
                log.warn("父变体 {} 没有找到子变体关系", variant.getId());
            }
        }

        return WhsPackagingOptionVo.builder().variantId(variant.getId()).productId(variant.getProductId())
            .productName(item.getProductName()).skuCode(variant.getSkuCode()).packagingType(packagingType)
            .packagingTypeText(packagingTypeText).packagingQuantity(packagingQuantity)
            .childVariantId(childVariantId).build();
    }

    /**
     * 创建并保存方案
     *
     * @param planBo 方案BO
     * @return 方案ID
     */
    private Long createAndSaveShipmentPlan(WhsShipmentPlanBo planBo) {
        // 转换为实体
        WhsShipmentPlan plan = MapstructUtils.convert(planBo, WhsShipmentPlan.class);
        // 保存方案
        shipmentPlanMapper.insert(plan);
        if (plan.getId() == null) {
            throw new ServiceException("创建方案失败");
        }

        // 遍历方案项
        List<WhsShipmentPlanItem> planItems = new ArrayList<>();
        for (WhsShipmentPlanItemBo item : planBo.getPlanItems()) {
            WhsShipmentPlanItem planItem = MapstructUtils.convert(item, WhsShipmentPlanItem.class);
            planItem.setPlanId(plan.getId());
            planItems.add(planItem);
        }

        // 批量保存方案项
        shipmentPlanItemMapper.insertBatch(planItems);

        return plan.getId();
    }

    /**
     * 计算并添加PCS到Map中
     * 用于消除重复代码
     */
    private void calculateAndAddPcs(Map<Long, Integer> pcsMap, Long variantId, Integer quantity, Integer packagingQuantity) {
        Integer qty = quantity != null ? quantity : 0;
        Integer pkgQty = packagingQuantity != null ? packagingQuantity : 1;
        int pcs = qty * pkgQty;
        pcsMap.put(variantId, pcsMap.getOrDefault(variantId, 0) + pcs);
    }
}
