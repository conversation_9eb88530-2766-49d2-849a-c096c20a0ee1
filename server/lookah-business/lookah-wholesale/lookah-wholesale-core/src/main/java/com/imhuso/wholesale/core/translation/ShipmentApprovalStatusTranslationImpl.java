package com.imhuso.wholesale.core.translation;

import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.translation.annotation.TranslationType;
import com.imhuso.common.translation.core.TranslationInterface;
import com.imhuso.wholesale.core.constant.TransConstant;
import com.imhuso.wholesale.core.enums.ShipmentApprovalStatus;
import com.imhuso.wholesale.core.service.IApprovalConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 发货审批状态翻译实现
 * 专门处理发货审批状态的国际化翻译
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@TranslationType(type = TransConstant.WHOLESALE_SHIPMENT_APPROVAL_STATUS)
public class ShipmentApprovalStatusTranslationImpl implements TranslationInterface<String> {

    private final IApprovalConfigService approvalConfigService;

    @Override
    public String translation(Object key, String other) {
        if (key == null) {
            // 对于null值，默认返回无需审批状态的翻译
            String messageKey = "wholesale.shipment.approval.status.no_approval";
            return MessageUtils.message(messageKey);
        }

        try {
            // 将输入转换为整数
            Integer statusValue;
            if (key instanceof String) {
                statusValue = Integer.valueOf((String) key);
            } else if (key instanceof Integer) {
                statusValue = (Integer) key;
            } else {
                log.warn("无法处理的审批状态类型: {}", key.getClass());
                return "未知状态";
            }

            // 查找对应的枚举值
            for (ShipmentApprovalStatus status : ShipmentApprovalStatus.values()) {
                if (status.getValue().equals(statusValue)) {
                    // 特殊处理NO_APPROVAL状态：根据系统配置决定显示文本
                    if (status == ShipmentApprovalStatus.NO_APPROVAL) {
                        return getNoApprovalStatusText();
                    }

                    // 其他状态使用标准翻译
                    String messageKey = "wholesale.shipment.approval.status." + status.name().toLowerCase();
                    String result = MessageUtils.message(messageKey);

                    log.debug("发货审批状态翻译: {} -> {} (key: {})", statusValue, result, messageKey);
                    return result;
                }
            }

            log.debug("未找到匹配的发货审批状态: {}", statusValue);
            return "未知状态";
        } catch (Exception e) {
            log.error("发货审批状态翻译出错: key={}, error={}", key, e.getMessage());
            return "翻译失败";
        }
    }

    /**
     * 获取NO_APPROVAL状态的文本
     * 根据系统配置决定显示"无需审批"还是"待申请"
     */
    private String getNoApprovalStatusText() {
        try {
            boolean isApprovalEnabled = approvalConfigService.isApprovalEnabled();

            String messageKey;
            if (isApprovalEnabled) {
                // 启用了审批功能，显示"待申请"
                messageKey = "wholesale.shipment.approval.status.no_approval_enabled";
            } else {
                // 未启用审批功能，显示"无需审批"
                messageKey = "wholesale.shipment.approval.status.no_approval";
            }
            return MessageUtils.message(messageKey);
        } catch (Exception e) {
            log.error("获取NO_APPROVAL状态文本时出错: {}", e.getMessage());
            return MessageUtils.message("wholesale.shipment.approval.status.no_approval");
        }
    }
}
