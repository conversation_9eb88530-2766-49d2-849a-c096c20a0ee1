package com.imhuso.wholesale.core.domain.vo.admin;

import com.imhuso.common.translation.annotation.Translation;
import com.imhuso.wholesale.core.constant.TransConstant;
import com.imhuso.wholesale.core.domain.WhsOrderShipmentItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 批发订单发货项后台视图对象
 * 仅包含产品相关信息，物流信息由物流包裹对象提供
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsOrderShipmentItem.class)
public class WhsOrderShipmentItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 发货项ID
     */
    private Long id;

    /**
     * 发货ID
     */
    private Long shipmentId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 物流包裹ID
     */
    private Long packageId;

    /**
     * 关联的发货方案项ID
     */
    private Long planItemId;

    /**
     * 产品名称（冗余展示字段，不在数据库中存储）
     */
    private String productName;

    /**
     * SKU编码（冗余展示字段，不在数据库中存储）
     */
    private String skuCode;

    /**
     * 发货数量
     */
    private Integer quantity;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 包装类型: 0-单品 1-展示盒 2-整箱（冗余展示字段，不在数据库中存储）
     *
     * @see com.imhuso.wholesale.enums.PackagingType
     */
    private Integer packagingType;

    /**
     * 包装类型文本（冗余展示字段，不在数据库中存储）
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, other = "packagingType", mapper = "packagingType")
    private String packagingTypeText;

    /**
     * 包装数量 (每计划单位包含的原始物品数)（冗余展示字段，不在数据库中存储）
     */
    private Integer packagingQuantity;
}
