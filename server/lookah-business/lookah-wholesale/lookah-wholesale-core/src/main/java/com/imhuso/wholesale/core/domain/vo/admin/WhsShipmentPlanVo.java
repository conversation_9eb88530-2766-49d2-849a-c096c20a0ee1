package com.imhuso.wholesale.core.domain.vo.admin;

import com.imhuso.common.translation.annotation.Translation;
import com.imhuso.wholesale.core.constant.TransConstant;
import com.imhuso.wholesale.core.domain.WhsShipmentPlan;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 发货方案视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AutoMapper(target = WhsShipmentPlan.class)
public class WhsShipmentPlanVo {

    /**
     * 方案ID
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 总PCS数量
     */
    private Integer totalPcs;

    /**
     * 方案名称
     */
    private String planName;

    /**
     * 方案评分
     */
    private BigDecimal score;

    /**
     * 是否系统推荐
     */
    private Boolean isRecommended;

    /**
     * 是否为自定义方案
     */
    private Boolean isCustom;

    /**
     * 是否已选择
     */
    private Boolean isSelected;

    /**
     * 包裹数量
     */
    private Integer packageCount;

    /**
     * 状态：0-待确认 1-已确认 2-已执行 3-不可用
     */
    private Integer status;

    /**
     * 状态名称
     *
     * @see com.imhuso.wholesale.enums.ShipmentPlanStatus
     */
    @Translation(type = TransConstant.WHOLESALE_ENUM_TO_TEXT, mapper = "status", other = "shipmentPlanStatus")
    private String statusText;

    /**
     * 方案项列表
     */
    private List<WhsShipmentPlanItemVo> planItems;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
