package com.imhuso.wholesale.core.enums;

import com.imhuso.common.translation.core.EnumTranslatableInterface;
import lombok.Getter;

/**
 * 操作者类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum OperatorType implements EnumTranslatableInterface {

    /**
     * 会员
     */
    MEMBER(1),

    /**
     * 管理员
     */
    ADMIN(2);

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 构造函数
     */
    OperatorType(int value) {
        this.value = value;
    }
}
