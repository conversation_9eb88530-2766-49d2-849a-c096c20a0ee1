package com.imhuso.wholesale.core.domain.bo.front;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.imhuso.wholesale.core.domain.bo.IMailVariables;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderVo;
import com.imhuso.wholesale.core.event.WhsOrderEvent.WhsOrderEventType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 订单通知模板参数对象
 *
 * <AUTHOR>
 */
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class OrderNotifyTemplateBo implements IMailVariables, Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    // 收件人邮箱
    private String toEmail;

    // 是否发送给管理员
    private boolean isAdmin;

    // 订单信息
    private WhsOrderVo order;

    // 事件类型
    private WhsOrderEventType eventType;

    // 标题
    private String title;

    // 模板名称
    private String templateName;

    // 发票附件
    private java.io.File invoiceFile;

    // 所有附件
    private java.io.File[] attachments;

    @Override
    public String getTitle() {
        return title != null ? title : "";
    }

    @JsonIgnore
    @Override
    public Map<String, Object> getVariables() {
        Map<String, Object> variables = new HashMap<>();
        variables.put("order", order);
        variables.put("eventType", eventType);
        variables.put("title", getTitle());
        variables.put("isAdmin", isAdmin);

        return variables;
    }
}
