package com.imhuso.wholesale.core.service.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.domain.WhsProductSeries;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductSeriesBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductSeriesVo;
import com.imhuso.wholesale.core.mapper.WhsProductSeriesMapper;
import com.imhuso.wholesale.core.service.IWhsProductSeriesService;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 批发产品系列Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsProductSeriesServiceImpl implements IWhsProductSeriesService {

    private final WhsProductSeriesMapper seriesMapper;

    @Override
    public Map<Long, String> getSeriesNames(List<Long> seriesIds) {
        if (seriesIds.isEmpty()) {
            return Map.of();
        }
        List<WhsProductSeries> series = seriesMapper.selectByIds(seriesIds);
        return series.stream().collect(Collectors.toMap(WhsProductSeries::getId, WhsProductSeries::getName, (a, b) -> a));
    }

    @Override
    public List<WhsProductSeriesVo> queryList(WhsProductSeriesBo bo) {
        LambdaQueryWrapper<WhsProductSeries> lqw = buildQueryWrapper(bo);
        return seriesMapper.selectVoList(lqw);
    }

    @Override
    public WhsProductSeriesVo getById(Long id) {
        return seriesMapper.selectVoById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertSeries(WhsProductSeriesBo bo) {
        // 检查系列名称是否已存在
        if (isSeriesNameExists(bo.getName(), null)) {
            throw new ServiceException(MessageUtils.message("wholesale.product.series.name.exists"));
        }
        WhsProductSeries series = MapstructUtils.convert(bo, WhsProductSeries.class);
        // 默认设置为正常状态
        if (series != null && StringUtils.isEmpty(series.getStatus())) {
            series.setStatus(BusinessConstants.NORMAL);
        }
        return seriesMapper.insert(series);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateSeries(WhsProductSeriesBo bo) {
        // 检查系列名称是否已存在
        if (isSeriesNameExists(bo.getName(), bo.getId())) {
            throw new ServiceException(MessageUtils.message("wholesale.product.series.name.exists"));
        }
        WhsProductSeries series = MapstructUtils.convert(bo, WhsProductSeries.class);
        return seriesMapper.updateById(series);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteSeriesByIds(Long[] ids) {
        // 检查是否有产品正在使用该系列
        checkSeriesInUse(ids);
        return seriesMapper.deleteByIds(List.of(ids));
    }

    @Override
    public TableDataInfo<WhsProductSeriesVo> queryPageList(WhsProductSeriesBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WhsProductSeries> lqw = buildQueryWrapper(bo);
        Page<WhsProductSeriesVo> page = seriesMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    /**
     * 检查系列名称是否已存在
     *
     * @param name 系列名称
     * @param id   排除的ID（更新时使用）
     * @return 存在返回true，不存在返回false
     */
    private boolean isSeriesNameExists(String name, Long id) {
        LambdaQueryWrapper<WhsProductSeries> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WhsProductSeries::getName, name).ne(id != null, WhsProductSeries::getId, id);
        return seriesMapper.selectCount(lqw) > 0;
    }

    /**
     * 检查系列是否正在被产品使用
     *
     * @param ids 系列ID数组
     */
    private void checkSeriesInUse(Long[] ids) {
        // 这里可以根据实际业务需求实现
        // 例如，如果有产品使用了该系列，则抛出异常
        // 实际应查询产品表是否有引用这些系列ID的记录

        // 此示例中暂不实现具体检查逻辑，允许删除所有系列
        // 实际业务中应添加查询逻辑，避免删除正在使用的系列
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<WhsProductSeries> buildQueryWrapper(WhsProductSeriesBo bo) {
        LambdaQueryWrapper<WhsProductSeries> lqw = new LambdaQueryWrapper<>();
        if (bo != null) {
            lqw.like(StringUtils.isNotBlank(bo.getName()), WhsProductSeries::getName, bo.getName())
               .eq(StringUtils.isNotBlank(bo.getStatus()), WhsProductSeries::getStatus, bo.getStatus());
        }
        lqw.orderByAsc(WhsProductSeries::getSort);
        return lqw;
    }
}
