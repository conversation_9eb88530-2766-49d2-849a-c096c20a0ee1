package com.imhuso.wholesale.core.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.mybatis.core.mapper.BaseMapperPlus;
import com.imhuso.wholesale.core.domain.WhsStockLog;
import com.imhuso.wholesale.core.domain.vo.admin.WhsStockLogVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 库存操作日志Mapper接口
 *
 * <AUTHOR>
 */

@Mapper
public interface WhsStockLogMapper extends BaseMapperPlus<WhsStockLog, WhsStockLogVo> {

    /**
     * 自定义分页查询
     * 使用JOIN方式关联仓库、产品和变体信息
     *
     * @param page         分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    Page<WhsStockLogVo> selectStockLogVoPage(Page<WhsStockLogVo> page,
                                               @Param(Constants.WRAPPER) Wrapper<WhsStockLog> queryWrapper);
}
