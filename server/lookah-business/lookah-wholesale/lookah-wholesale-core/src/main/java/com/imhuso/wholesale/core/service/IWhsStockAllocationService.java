package com.imhuso.wholesale.core.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.WhsOrderItem;
import com.imhuso.wholesale.core.domain.WhsStockAllocation;
import com.imhuso.wholesale.core.domain.bo.admin.WhsStockAllocationBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsStockAllocationVo;

import java.util.List;
import java.util.Map;

/**
 * 库存分配服务接口
 *
 * <AUTHOR>
 */
public interface IWhsStockAllocationService {

    /**
     * 查询库存分配分页列表
     *
     * @param bo        查询参数
     * @param pageQuery 分页参数
     * @return 库存分配分页数据
     */
    TableDataInfo<WhsStockAllocationVo> queryPageList(WhsStockAllocationBo bo, PageQuery pageQuery);

    /**
     * 查询库存分配详情
     *
     * @param id 库存分配ID
     * @return 库存分配信息
     */
    WhsStockAllocationVo getById(Long id);

    /**
     * 锁定订单项对应的库存
     *
     * @param orderItems 订单项列表
     * @param orderId    订单ID
     * @return 是否锁定成功
     */
    boolean lockStockForOrderItems(List<WhsOrderItem> orderItems, Long orderId);

    /**
     * 释放订单已锁定的库存
     *
     * @param orderId 订单ID
     * @return 是否释放成功
     */
    boolean releaseStockByOrderId(Long orderId);

    /**
     * 获取指定计划锁定的库存
     * 返回计划中各变体在各仓库的锁定库存
     *
     * @param planId 发货计划ID
     * @return 变体ID到仓库锁定库存的映射 Map<变体ID, Map<仓库ID, 锁定数量>>
     */
    Map<Long, Map<Long, Integer>> getLockedStockByPlanId(Long planId);

    /**
     * 释放指定数量的库存分配
     *
     * @param allocationId      库存分配ID
     * @param quantityToRelease 要释放的数量
     * @param remark            释放备注
     * @return 是否释放成功
     */
    boolean releaseSpecificAllocation(Long allocationId, int quantityToRelease, String remark);

    /**
     * 根据条件查询库存分配记录
     *
     * @param queryWrapper 查询条件
     * @return 库存分配记录列表
     */
    List<WhsStockAllocation> list(LambdaQueryWrapper<WhsStockAllocation> queryWrapper);
}
