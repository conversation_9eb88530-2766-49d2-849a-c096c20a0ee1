package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.wholesale.core.domain.WhsProduct;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 产品业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WhsProduct.class)
public class WhsProductBo extends BaseEntity {
    /**
     * 产品ID
     */
    @NotNull(message = "wholesale.admin.product.id.not.null", groups = {EditGroup.class})
    private Long id;

    /**
     * 产品名称
     */
    @NotBlank(message = "wholesale.admin.product.name.not.blank", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 100, message = "wholesale.admin.product.name.length", groups = {AddGroup.class, EditGroup.class})
    private String itemName;

    /**
     * 产品分类ID
     */
    @NotNull(message = "wholesale.admin.product.category.not.null", groups = {AddGroup.class, EditGroup.class})
    private Long categoryId;

    /**
     * 产品系列ID
     */
    private Long seriesId;

    /**
     * 状态（0停用 1正常）
     */
    private String status;

    /**
     * 产品属性ID列表
     */
    private List<Long> attributeIds;

    /**
     * 变体SKU（用于模糊搜索相关产品）
     */
    private String skuCode;
}
