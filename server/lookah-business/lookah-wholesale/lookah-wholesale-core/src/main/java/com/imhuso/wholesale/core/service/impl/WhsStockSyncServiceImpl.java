package com.imhuso.wholesale.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.WhsWarehouse;
import com.imhuso.wholesale.core.domain.vo.warehouse.StockSyncResultVo;
import com.imhuso.wholesale.core.domain.vo.warehouse.StockSyncSummary;
import com.imhuso.wholesale.core.mapper.WhsWarehouseMapper;
import com.imhuso.wholesale.core.service.IWhsOverseasWarehouseService;
import com.imhuso.wholesale.core.service.IWhsProductVariantService;
import com.imhuso.wholesale.core.service.IWhsStockSyncService;
import com.imhuso.wholesale.core.service.IWhsErpStockSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 库存同步服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsStockSyncServiceImpl implements IWhsStockSyncService {

    private final WhsWarehouseMapper warehouseMapper;
    private final IWhsOverseasWarehouseService overseasWarehouseService;
    private final IWhsProductVariantService productVariantService;
    private final IWhsErpStockSyncService erpStockSyncService;

    /**
     * 同步所有海外仓库的库存（内部方法）
     *
     * @return 同步成功的仓库数量
     */
    private int syncAllWarehouses() {
        log.info("开始同步所有海外仓库库存");

        // 查询所有启用的海外仓库
        LambdaQueryWrapper<WhsWarehouse> query = new LambdaQueryWrapper<>();
        query.eq(WhsWarehouse::getIsOverseas, BusinessConstants.YES).eq(WhsWarehouse::getStatus, BusinessConstants.NORMAL);

        List<WhsWarehouse> warehouses = warehouseMapper.selectList(query);
        if (warehouses.isEmpty()) {
            log.warn("没有找到可用的海外仓库，同步终止");
            return 0;
        }

        log.info("找到{}个可用海外仓库准备同步", warehouses.size());

        // 获取所有有效变体
        List<WhsProductVariant> allVariants = productVariantService.getAllActiveVariants();
        if (allVariants.isEmpty()) {
            log.warn("没有找到有效的产品变体，同步终止");
            return 0;
        }

        log.info("找到{}个有效产品变体准备同步", allVariants.size());

        // 使用线程池并行处理多个仓库的同步
        int threadCount = Math.min(Runtime.getRuntime().availableProcessors(), Math.min(5, warehouses.size()));
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        try {
            log.info("创建线程池，线程数: {}", threadCount);

            // 并行同步每个仓库
            List<CompletableFuture<Boolean>> futures = warehouses.stream().map(warehouse -> CompletableFuture.supplyAsync(() -> {
                try {
                    // 对每个仓库使用所有变体进行同步
                    log.info("开始同步仓库[{}]的库存", warehouse.getName());

                    // 同步操作
                    return syncWarehouseStock(warehouse, allVariants);
                } catch (Exception e) {
                    log.error("仓库[{}]同步出错", warehouse.getName(), e);
                    return false;
                }
            }, executor)).toList();

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            // 统计成功数量
            long successCount = futures.stream().map(CompletableFuture::join).filter(success -> success).count();

            log.info("所有仓库同步完成，成功{}个，失败{}个", successCount, warehouses.size() - successCount);

            return (int) successCount;
        } finally {
            shutdownExecutor(executor);
        }
    }

    /**
     * 同步单个仓库的库存
     *
     * @param warehouse 仓库信息
     * @param variants  变体列表
     * @return 是否同步成功
     */
    private boolean syncWarehouseStock(WhsWarehouse warehouse, List<WhsProductVariant> variants) {
        try {
            // 调用海外仓服务进行同步
            StockSyncResultVo result = overseasWarehouseService.syncStock(warehouse.getId(), variants);

            log.info("仓库[{}]同步完成，结果: {}", warehouse.getName(), result.isSuccess() ? "成功" : "失败: " + result.getErrorMessage());
            return result.isSuccess();
        } catch (Exception e) {
            log.error("同步仓库[{}]的库存时发生异常: {}", warehouse.getName(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 同步所有ERP库存（内部方法）
     *
     * @return 同步成功的产品变体数量
     */
    private int syncAllErpStocks() {
        log.info("开始同步所有ERP库存");
        try {
            return erpStockSyncService.syncAllVariantErpStock();
        } catch (Exception e) {
            log.error("同步ERP库存失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 执行完整的库存同步（海外仓 + ERP）
     *
     * @return 同步结果统计
     */
    @Override
    public StockSyncSummary syncAllStocks() {
        log.info("开始执行完整的库存同步（海外仓 + ERP）");
        long startTime = System.currentTimeMillis();

        int warehouseSuccessCount = 0;
        int warehouseFailureCount = 0;
        int erpSuccessCount = 0;
        int erpFailureCount = 0;

        // 同步海外仓库存
        try {
            log.info("开始同步海外仓库存");
            warehouseSuccessCount = syncAllWarehouses();
            log.info("海外仓库存同步完成，成功: {}", warehouseSuccessCount);
        } catch (Exception e) {
            log.error("海外仓库存同步失败: {}", e.getMessage(), e);
            warehouseFailureCount = 1; // 标记为失败
        }
        log.info("海外仓库存同步已临时跳过，仅同步ERP库存");

        // 同步ERP库存
        try {
            log.info("开始同步ERP库存");
            erpSuccessCount = syncAllErpStocks();
            log.info("ERP库存同步完成，成功: {}", erpSuccessCount);
        } catch (Exception e) {
            log.error("ERP库存同步失败: {}", e.getMessage(), e);
            erpFailureCount = 1; // 标记为失败
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        StockSyncSummary summary = StockSyncSummary.partial(
            warehouseSuccessCount, warehouseFailureCount,
            erpSuccessCount, erpFailureCount, duration
        );

        log.info("完整库存同步完成，海外仓成功: {}, ERP成功: {}, 总耗时: {}ms",
            warehouseSuccessCount, erpSuccessCount, duration);

        return summary;
    }

    /**
     * 安全关闭ExecutorService
     */
    private void shutdownExecutor(ExecutorService executor) {
        if (executor == null || executor.isShutdown()) {
            return;
        }

        try {
            // 启动有序关闭
            executor.shutdown();
            
            // 等待现有任务完成，最多等待60秒
            if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                log.warn("ExecutorService未能在60秒内正常关闭，尝试强制关闭");
                // 强制关闭仍在执行的任务
                executor.shutdownNow();
                
                // 再等待一段时间确保关闭完成
                if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                    log.error("ExecutorService强制关闭失败");
                }
            }
        } catch (InterruptedException e) {
            log.warn("等待ExecutorService关闭被中断", e);
            executor.shutdownNow();
            // 保留中断状态
            Thread.currentThread().interrupt();
        }
    }
}
