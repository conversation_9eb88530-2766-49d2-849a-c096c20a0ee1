package com.imhuso.wholesale.core.listener;

import com.imhuso.wholesale.core.domain.WhsOrder;
import com.imhuso.wholesale.core.enums.ShipmentStatus;
import com.imhuso.wholesale.core.event.WhsOrderEvent;
import com.imhuso.wholesale.core.service.IWhsOrderBaseService;
import com.imhuso.wholesale.core.service.IWhsStockAllocationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.Objects;

import static com.imhuso.wholesale.core.event.WhsOrderEvent.WhsOrderEventType;

/**
 * 订单发货库存释放监听器
 * 当订单完全发货后，释放掉订单中所有未释放的锁定库存
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WhsOrderShipmentStockReleaseListener {

    private final IWhsStockAllocationService stockAllocationService;
    private final IWhsOrderBaseService orderBaseService;

    /**
     * 处理订单发货事件，当订单完全发货后释放所有未释放的库存
     * 异步执行，避免阻塞主流程
     */
    @Async
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleOrderShipmentEvent(WhsOrderEvent event) {
        // 只处理订单发货事件
        if (event.getEventType() != WhsOrderEventType.ORDER_SHIPPED) {
            return;
        }

        Long orderId = event.getOrderId();
        if (orderId == null) {
            log.error("订单ID为空，无法处理库存释放");
            return;
        }

        try {
            // 查询订单信息，确认是否完全发货
            WhsOrder order = orderBaseService.getOrderById(orderId);
            if (order == null) {
                log.error("订单不存在, orderId={}", orderId);
                return;
            }

            // 确认订单发货状态为已完全发货
            if (!Objects.equals(order.getShipmentStatus(), ShipmentStatus.SHIPPED.getValue())) {
                log.debug("订单 {} 未完全发货，跳过库存释放", orderId);
                return;
            }

            log.info("开始释放订单 {} 的所有未释放锁定库存", orderId);

            // 调用库存分配服务释放所有未释放的库存
            boolean result = stockAllocationService.releaseStockByOrderId(orderId);

            if (result) {
                log.info("成功释放订单 {} 的所有未释放锁定库存", orderId);
            } else {
                log.warn("释放订单 {} 的未释放锁定库存失败", orderId);
            }
        } catch (Exception e) {
            log.error("处理订单发货库存释放时发生异常: orderId={}, error={}", orderId, e.getMessage(), e);
        }
    }
}
