package com.imhuso.wholesale.core.domain.bo.admin;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 订单项
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2025-04-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WhsOrderCreateItemBo {
    /**
     * 变体ID
     */
    @NotNull(message = "必须指定变体")
    private Long variantId;

    /**
     * 数量
     */
    @NotNull(message = "必须指定数量")
    private Integer quantity;

    /**
     * 采购单价（成本价）- 可为空
     */
    private BigDecimal purchasePrice;

    /**
     * 销售单价（手动填入）- 必填但可为0
     */
    @NotNull(message = "销售单价不能为空")
    private BigDecimal salesPrice;
}
