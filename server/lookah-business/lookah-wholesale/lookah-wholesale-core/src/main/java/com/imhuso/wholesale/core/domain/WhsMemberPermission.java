package com.imhuso.wholesale.core.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户权限(WhsMemberPermission)实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "whs_member_permission")
public class WhsMemberPermission extends BaseEntity {

    /**
     * 权限ID
     */
    private Long id;

    /**
     * 客户ID
     */
    private Long memberId;

    /**
     * 权限键
     */
    private String permissionKey;

    /**
     * 权限值
     */
    private String permissionValue;

    /**
     * 帐号状态（0停用 1正常）
     */
    private String status;

    /**
     * 创建部门
     */
    private Long createDept;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private String delFlag;
}
