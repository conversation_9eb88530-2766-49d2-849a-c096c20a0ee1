package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 发货转换记录对象
 * 记录原始订单项如何转换为发货计划项，用于追踪库存释放
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_shipment_conversion")
public class WhsShipmentConversion extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 转换记录ID
     */
    @TableId
    private Long id;

    /**
     * 原始订单ID
     */
    private Long orderId;

    /**
     * 原始变体ID
     */
    private Long originalVariantId;

    /**
     * 原始变体SKU编码
     */
    private String originalSkuCode;

    /**
     * 参与转换的原始订单项数量
     */
    private Integer originalQuantity;

    /**
     * 原始包装类型: 0-单品 1-展示盒 2-整箱
     *
     * @see com.imhuso.wholesale.enums.PackagingType
     */
    private Integer originalPackagingType;

    /**
     * 关联的发货计划ID
     */
    private Long planId;

    /**
     * 转换后生成的发货计划项ID
     */
    private Long planItemId;

    /**
     * 实际发货变体ID
     */
    private Long actualVariantId;

    /**
     * 实际发货变体SKU编码
     */
    private String actualSkuCode;

    /**
     * 转换后的数量
     */
    private Integer actualQuantity;

    /**
     * 转换后包装类型: 0-单品 1-展示盒 2-整箱
     *
     * @see com.imhuso.wholesale.enums.PackagingType
     */
    private Integer actualPackagingType;

    /**
     * 转换说明 (新增)
     */
    private String remark;
}
