package com.imhuso.wholesale.core.domain.bo.front;

import com.imhuso.wholesale.core.domain.bo.IMailVariables;
import com.imhuso.wholesale.core.domain.vo.admin.WhsReplenishmentVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 补货通知模板参数对象
 * 用于补货通知邮件的模板变量传递
 *
 * <AUTHOR>
 */
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ReplenishmentNotifyTemplateBo implements IMailVariables, Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 邮件标题
     */
    private String title;

    /**
     * 需要补货的产品列表
     */
    private List<WhsReplenishmentVo> replenishmentList;

    /**
     * 检查时间
     */
    private LocalDateTime checkTime;

    /**
     * 总需要补货的产品数量
     */
    private Integer totalProductCount;

    /**
     * 总推荐补货数量
     */
    private Integer totalRecommendedQuantity;

    @Override
    public String getTitle() {
        return title != null ? title : "库存补货提醒";
    }

    @Override
    public Map<String, Object> getVariables() {
        Map<String, Object> variables = new HashMap<>();
        variables.put("title", getTitle());
        variables.put("replenishmentList", replenishmentList);
        variables.put("checkTime", checkTime);
        variables.put("checkTimeFormatted", checkTime != null ?
            checkTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");
        variables.put("totalProductCount", totalProductCount != null ? totalProductCount : 0);
        variables.put("totalRecommendedQuantity", totalRecommendedQuantity != null ? totalRecommendedQuantity : 0);
        return variables;
    }
}
