package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 入库单明细对象 whs_inbound_order_item
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("whs_inbound_order_item")
public class WhsInboundOrderItem extends BaseEntity {

    /**
     * 明细ID
     */
    @TableId
    private Long id;

    /**
     * 入库单ID
     */
    private Long inboundOrderId;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 已接收数量
     */
    private Integer receivedQuantity;
}
