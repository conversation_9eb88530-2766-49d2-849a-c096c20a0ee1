package com.imhuso.wholesale.core.service;

import com.imhuso.wholesale.core.domain.bo.admin.WhsPackageVariantBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVariantVo;

/**
 * 包装变体服务接口
 *
 * <AUTHOR>
 */
public interface IWhsPackageVariantService {

    /**
     * 创建包装变体
     *
     * @param packageVariantBo 包装变体创建参数
     * @return 变体信息
     */
    WhsProductVariantVo createPackageVariant(WhsPackageVariantBo packageVariantBo);

    /**
     * 更新包装变体
     *
     * @param packageVariantBo 包装变体更新参数
     * @return 变体信息
     */
    WhsProductVariantVo updatePackageVariant(WhsPackageVariantBo packageVariantBo);

    /**
     * 删除包装变体
     *
     * @param id 包装变体ID
     * @return 结果
     */
    int deletePackageVariant(Long id);
}
