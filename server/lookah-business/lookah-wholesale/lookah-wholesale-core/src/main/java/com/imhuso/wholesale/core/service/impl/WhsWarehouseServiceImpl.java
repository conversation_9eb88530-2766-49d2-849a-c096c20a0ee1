package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.constant.WhsCacheConstants;
import com.imhuso.wholesale.core.domain.WhsWarehouse;
import com.imhuso.wholesale.core.domain.bo.admin.WhsWarehouseBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsWarehouseVo;
import com.imhuso.wholesale.core.mapper.WhsWarehouseMapper;
import com.imhuso.wholesale.core.service.IWhsWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 仓库Service业务层实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsWarehouseServiceImpl implements IWhsWarehouseService {

    private final WhsWarehouseMapper warehouseMapper;

    /**
     * 查询仓库列表（不分页）
     *
     * @param bo 仓库查询业务对象
     * @return 仓库列表
     */
    @Override
    public List<WhsWarehouseVo> queryList(WhsWarehouseBo bo) {
        LambdaQueryWrapper<WhsWarehouse> lqw = buildQueryWrapper(bo);
        // 限制查询数量
        return warehouseMapper.selectVoList(lqw);
    }

    /**
     * 查询仓库分页列表
     *
     * @param bo        仓库查询业务对象
     * @param pageQuery 分页参数
     * @return 仓库分页列表
     */
    @Override
    public TableDataInfo<WhsWarehouseVo> queryPageList(WhsWarehouseBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WhsWarehouse> lqw = buildQueryWrapper(bo);
        Page<WhsWarehouseVo> page = warehouseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    /**
     * 根据ID查询仓库详情
     *
     * @param id 仓库ID
     * @return 仓库详情
     */
    @Override
    public WhsWarehouseVo getById(Long id) {
        return warehouseMapper.selectVoById(id);
    }

    /**
     * 新增仓库
     *
     * @param bo 仓库业务对象
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertWarehouse(WhsWarehouseBo bo) {
        // 检查仓库编码是否唯一
        if (isWarehouseCodeDuplicate(bo)) {
            throw new ServiceException("仓库编码[" + bo.getCode() + "]已存在，请更换其他编码");
        }
        WhsWarehouse warehouse = MapstructUtils.convert(bo, WhsWarehouse.class);
        int result = warehouseMapper.insert(warehouse);

        return result;
    }

    /**
     * 修改仓库
     *
     * @param bo 仓库业务对象
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateWarehouse(WhsWarehouseBo bo) {
        // 检查仓库编码是否唯一
        if (isWarehouseCodeDuplicate(bo)) {
            throw new ServiceException("仓库编码[" + bo.getCode() + "]已存在，请更换其他编码");
        }
        WhsWarehouse warehouse = MapstructUtils.convert(bo, WhsWarehouse.class);
        int result = warehouseMapper.updateById(warehouse);

        return result;
    }

    /**
     * 检查仓库编码是否已被占用 (除自身外)
     *
     * @param bo 仓库业务对象 (需要包含 id 和 code)
     * @return true 已被占用 / false 未被占用 (唯一)
     */
    @Override
    public boolean isWarehouseCodeDuplicate(WhsWarehouseBo bo) {
        Long warehouseId = bo.getId() != null ? bo.getId() : -1L;
        WhsWarehouse warehouse = warehouseMapper.selectOne(new LambdaQueryWrapper<WhsWarehouse>().eq(WhsWarehouse::getCode, bo.getCode()));
        // 存在记录且不是当前编辑记录，则表示编码已被占用
        return warehouse != null && !warehouse.getId().equals(warehouseId);
    }

    /**
     * 删除仓库
     *
     * @param id 仓库ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteWarehouseById(Long id) {
        int result = warehouseMapper.deleteById(id);

        return result;
    }

    /**
     * 设置默认仓库
     * 使用原子性SQL操作避免并发问题
     *
     * @param id 仓库ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int setDefaultWarehouse(Long id) {
        // 先验证仓库是否存在
        WhsWarehouse warehouse = warehouseMapper.selectById(id);
        if (warehouse == null) {
            log.error("设置默认仓库失败，仓库[id={}]不存在", id);
            return 0;
        }

        // 检查仓库状态是否正常
        if (!BusinessConstants.NORMAL.equals(warehouse.getStatus())) {
            log.error("设置默认仓库失败，仓库[id={}]状态异常", id);
            return 0;
        }

        // 使用原子性 SQL 操作，避免并发问题
        int result = warehouseMapper.setDefaultWarehouseAtomic(id);

        return result;
    }


    /**
     * 构建查询条件
     *
     * @param bo 查询参数
     * @return 查询条件
     */
    private LambdaQueryWrapper<WhsWarehouse> buildQueryWrapper(WhsWarehouseBo bo) {
        LambdaQueryWrapper<WhsWarehouse> lqw = new LambdaQueryWrapper<>();
        Map<String, Object> params = bo.getParams();

        lqw.eq(bo.getId() != null, WhsWarehouse::getId, bo.getId());
        lqw.like(StringUtils.isNotBlank(bo.getName()), WhsWarehouse::getName, bo.getName());
        lqw.like(StringUtils.isNotBlank(bo.getCode()), WhsWarehouse::getCode, bo.getCode());
        lqw.like(StringUtils.isNotBlank(bo.getAddress()), WhsWarehouse::getAddress, bo.getAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), WhsWarehouse::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getIsDefault()), WhsWarehouse::getIsDefault, bo.getIsDefault());

        // 按创建时间范围查询
        if (params != null) {
            lqw.between(params.get("beginTime") != null && params.get("endTime") != null, WhsWarehouse::getCreateTime, params.get("beginTime"), params.get("endTime"));
        }

        // 按创建时间倒序排序
        lqw.orderByDesc(WhsWarehouse::getCreateTime);

        return lqw;
    }

    /**
     * 获取所有仓库列表
     *
     * @return 仓库列表
     */
    @Override
    public List<WhsWarehouse> list() {
        return warehouseMapper.selectList(new LambdaQueryWrapper<>());
    }

    /**
     * 根据仓库ID获取对象
     *
     * @param id 仓库ID
     * @return 仓库对象
     */
    @Override
    public WhsWarehouse selectById(Long id) {
        return warehouseMapper.selectById(id);
    }

    /**
     * 更新仓库
     *
     * @param warehouse 仓库对象
     * @return 是否成功
     */
    @Override
    public boolean updateById(WhsWarehouse warehouse) {
        boolean result = warehouseMapper.updateById(warehouse) > 0;

        return result;
    }

    /**
     * 获取所有状态正常的仓库ID到名称的映射
     *
     * @return Map<仓库ID, 仓库名称>，只包含状态正常的仓库
     */
    @Override
    public Map<Long, String> getActiveWarehouseIdNameMap() {
        return getActiveWarehouseIdMapping(WhsWarehouse::getName);
    }

    /**
     * 获取所有状态正常的仓库ID到编码的映射
     *
     * @return Map<仓库ID, 仓库编码>，只包含状态正常的仓库
     */
    @Override
    public Map<Long, String> getActiveWarehouseIdCodeMap() {
        return getActiveWarehouseIdMapping(WhsWarehouse::getCode);
    }

    /**
     * 获取状态正常的仓库ID到指定字段的映射
     *
     * @param fieldMapper 字段映射函数
     * @return Map<仓库ID, 字段值>，只包含状态正常的仓库
     */
    private Map<Long, String> getActiveWarehouseIdMapping(java.util.function.Function<WhsWarehouse, String> fieldMapper) {
        LambdaQueryWrapper<WhsWarehouse> lqw = new LambdaQueryWrapper<>();
        lqw.select(WhsWarehouse::getId, WhsWarehouse::getName, WhsWarehouse::getCode);
        // 只查询状态正常的仓库
        lqw.eq(WhsWarehouse::getStatus, BusinessConstants.NORMAL);
        // 只查询未删除的仓库
        lqw.eq(WhsWarehouse::getDelFlag, BusinessConstants.LOGIC_NOT_DELETE);

        List<WhsWarehouse> warehouses = warehouseMapper.selectList(lqw);
        if (CollUtil.isEmpty(warehouses)) {
            return Collections.emptyMap();
        }

        return warehouses.stream()
            .collect(Collectors.toMap(WhsWarehouse::getId, fieldMapper, (oldValue, newValue) -> oldValue));
    }
}
