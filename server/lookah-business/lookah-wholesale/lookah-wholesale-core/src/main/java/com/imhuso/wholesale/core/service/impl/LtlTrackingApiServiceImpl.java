package com.imhuso.wholesale.core.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.constant.LtlCacheNames;
import com.imhuso.wholesale.core.domain.vo.warehouse.LtlTrackingResponseVo;
import com.imhuso.wholesale.core.service.ILtlTrackingApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import com.imhuso.common.core.utils.EnvironmentUtils;
import com.imhuso.common.redis.utils.RedisUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * LTL追踪API服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LtlTrackingApiServiceImpl implements ILtlTrackingApiService {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${ltl.tracking.api.url:https://tracking.shipping1.com/PubApi/shipping1API.aspx}")
    private String ltlApiUrl;

    @Value("${ltl.tracking.api.key:707427cac3884da6aa01d8069422d450}")
    private String ltlApiKey;

    /**
     * 获取LTL承运商追踪链接
     * 只缓存成功获取的链接，缓存60天
     */
    @Override
    @Cacheable(cacheNames = LtlCacheNames.LTL_TRACKING_SUCCESS,
        key = "#trackingNumber",
        unless = "#result == null")
    public String getCarrierTrackUrl(String trackingNumber) {
        if (StringUtils.isBlank(trackingNumber)) {
            return null;
        }

        try {
            // 判断是否为开发环境，调整日志级别
            boolean isDevelopment = safeIsDevelopmentEnvironment();

            if (isDevelopment) {
                log.info("LTL追踪API请求开始: URL={}, TrackingNumber={}", ltlApiUrl, trackingNumber);
            } else {
                log.debug("LTL追踪API请求开始: URL={}, TrackingNumber={}", ltlApiUrl, trackingNumber);
            }

            // 构建请求体
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("NO", trackingNumber);

            // 序列化请求体
            String requestBodyJson = objectMapper.writeValueAsString(requestBody);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Accept", "application/json");
            headers.set("apikey", ltlApiKey);
            headers.set("User-Agent", "curl/7.68.0");  // 模拟CURL

            // 移除可能触发ModSecurity的默认头
            headers.remove("Connection");
            headers.remove("Host");

            // 确保精确的Content-Length
            headers.setContentLength(requestBodyJson.getBytes().length);

            // 记录请求报文
            if (isDevelopment) {
                log.info("LTL追踪API请求头: {}", headers.toSingleValueMap());
                log.info("LTL追踪API请求体: {}", requestBodyJson);
            } else {
                log.debug("LTL追踪API请求头: {}", headers.toSingleValueMap());
                log.debug("LTL追踪API请求体: {}", requestBodyJson);
            }

            // 使用字符串作为请求体，避免额外的序列化头
            HttpEntity<String> request = new HttpEntity<>(requestBodyJson, headers);

            // 发送API请求
            ResponseEntity<String> response;
            String responseBody;

            try {
                response = restTemplate.exchange(
                    ltlApiUrl,
                    HttpMethod.POST,
                    request,
                    String.class
                );
                responseBody = response.getBody();

                // 记录响应报文
                if (isDevelopment) {
                    log.info("LTL追踪API响应状态: {}", response.getStatusCode());
                    log.info("LTL追踪API响应体: {}", responseBody);
                } else {
                    log.debug("LTL追踪API响应状态: {}", response.getStatusCode());
                    log.debug("LTL追踪API响应体: {}", responseBody);
                }
            } catch (Exception httpException) {
                // 尝试从异常中提取响应信息
                if (httpException instanceof org.springframework.web.client.HttpClientErrorException httpError) {
                    responseBody = httpError.getResponseBodyAsString();

                    // 即使HTTP异常也要记录响应报文
                    if (isDevelopment) {
                        log.info("LTL追踪API HTTP异常状态: {}", httpError.getStatusCode());
                        log.info("LTL追踪API HTTP异常响应体: {}", responseBody);
                    } else {
                        log.debug("LTL追踪API HTTP异常状态: {}", httpError.getStatusCode());
                        log.debug("LTL追踪API HTTP异常响应体: {}", responseBody);
                    }
                } else if (httpException instanceof org.springframework.web.client.HttpServerErrorException serverError) {
                    responseBody = serverError.getResponseBodyAsString();

                    // 服务器错误也要记录响应报文
                    if (isDevelopment) {
                        log.info("LTL追踪API服务器异常状态: {}", serverError.getStatusCode());
                        log.info("LTL追踪API服务器异常响应体: {}", responseBody);
                    } else {
                        log.debug("LTL追踪API服务器异常状态: {}", serverError.getStatusCode());
                        log.debug("LTL追踪API服务器异常响应体: {}", responseBody);
                    }
                }

                // 重新抛出异常，让外层catch处理
                throw httpException;
            }

            if (response.getStatusCode().is2xxSuccessful() && responseBody != null) {
                // 解析响应
                LtlTrackingResponseVo trackingResponse = objectMapper.readValue(
                    responseBody,
                    LtlTrackingResponseVo.class
                );

                if (trackingResponse != null && Boolean.TRUE.equals(trackingResponse.getSuccess())) {
                    // API调用成功
                    if (StringUtils.isNotBlank(trackingResponse.getCarrierTrackUrl())) {
                        // 成功获取到追踪链接
                        log.info("成功获取LTL追踪链接: {} -> {}", trackingNumber, trackingResponse.getCarrierTrackUrl());
                        return trackingResponse.getCarrierTrackUrl();
                    } else {
                        // API成功但暂无追踪信息，标记为无追踪信息状态
                        markNoTrackingInfo(trackingNumber);
                        log.debug("LTL追踪API成功但暂无追踪信息: trackingNumber={}, msg={}",
                            trackingNumber, trackingResponse.getMsg());
                        return null;
                    }
                } else {
                    // API成功但返回success=false，通常表示暂无追踪信息
                    markNoTrackingInfo(trackingNumber);
                    log.debug("LTL追踪API成功但暂无追踪信息: trackingNumber={}, success={}, msg={}",
                        trackingNumber, trackingResponse != null ? trackingResponse.getSuccess() : null,
                        trackingResponse != null ? trackingResponse.getMsg() : null);
                }
            } else {
                // HTTP状态码非2xx，标记1分钟冷却期
                markTrackingFailed(trackingNumber);
                log.error("LTL追踪API调用失败: trackingNumber={}, status={}, responseBody={}",
                    trackingNumber, response.getStatusCode(), responseBody);
            }

        } catch (Exception e) {
            log.error("LTL追踪API调用异常: trackingNumber={}, 错误类型: {}, 错误信息: {}",
                trackingNumber, e.getClass().getSimpleName(), e.getMessage(), e);
        }

        return null;
    }

    /**
     * 检查追踪号是否处于失败冷却期
     *
     * @param trackingNumber 追踪号
     * @return 如果在冷却期内返回true，否则返回false
     */
    @Override
    public boolean isInFailureCooldown(String trackingNumber) {
        try {
            String cacheKey = LtlCacheNames.LTL_TRACKING_FAILURE_NAME + ":" + trackingNumber;
            return RedisUtils.hasKey(cacheKey);
        } catch (Exception e) {
            log.debug("检查失败冷却期异常: {}, 错误: {}", trackingNumber, e.getMessage());
            return false;
        }
    }

    /**
     * 检查追踪号是否处于无追踪信息状态（API成功但暂无追踪链接）
     *
     * @param trackingNumber 追踪号
     * @return 如果处于无追踪信息状态返回true，否则返回false
     */
    @Override
    public boolean isInNoTrackingInfoState(String trackingNumber) {
        try {
            String cacheKey = LtlCacheNames.LTL_TRACKING_NO_INFO_NAME + ":" + trackingNumber;
            return RedisUtils.hasKey(cacheKey);
        } catch (Exception e) {
            log.debug("检查无追踪信息状态异常: {}, 错误: {}", trackingNumber, e.getMessage());
            return false;
        }
    }

    /**
     * 标记追踪号获取失败，进入1分钟冷却期
     * 在冷却期内，该追踪号将使用静态链接而不是API查询
     *
     * @param trackingNumber 追踪号
     */
    @Override
    public void markTrackingFailed(String trackingNumber) {
        try {
            String cacheKey = LtlCacheNames.LTL_TRACKING_FAILURE_NAME + ":" + trackingNumber;
            RedisUtils.setCacheObject(cacheKey, true, Duration.ofMinutes(LtlCacheNames.FAILURE_COOLDOWN_MINUTES));
            log.debug("标记LTL追踪号失败冷却: {}", trackingNumber);
        } catch (Exception e) {
            log.warn("标记失败冷却异常: {}, 错误: {}", trackingNumber, e.getMessage());
        }
    }

    /**
     * 标记追踪号暂无追踪信息（API成功但无追踪链接），进入30分钟冷却期
     * 在冷却期内，该追踪号将返回null而不是进行API查询
     *
     * @param trackingNumber 追踪号
     */
    @Override
    public void markNoTrackingInfo(String trackingNumber) {
        try {
            String cacheKey = LtlCacheNames.LTL_TRACKING_NO_INFO_NAME + ":" + trackingNumber;
            RedisUtils.setCacheObject(cacheKey, true, Duration.ofMinutes(LtlCacheNames.NO_INFO_COOLDOWN_MINUTES));
            log.debug("标记LTL追踪号暂无追踪信息: {}", trackingNumber);
        } catch (Exception e) {
            log.warn("标记无追踪信息状态异常: {}, 错误: {}", trackingNumber, e.getMessage());
        }
    }

    /**
     * 安全检查是否为开发环境
     *
     * @return 是否为开发环境
     */
    private boolean safeIsDevelopmentEnvironment() {
        try {
            return EnvironmentUtils.isDevelopmentEnvironment();
        } catch (Exception e) {
            log.debug("检查开发环境异常，默认为非开发环境: {}", e.getMessage());
            return false;
        }
    }
}
