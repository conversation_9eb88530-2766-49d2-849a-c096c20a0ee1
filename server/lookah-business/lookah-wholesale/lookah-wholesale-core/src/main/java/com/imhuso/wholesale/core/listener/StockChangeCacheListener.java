package com.imhuso.wholesale.core.listener;

import cn.hutool.core.collection.CollUtil;
import com.imhuso.common.redis.utils.CacheUtils;
import com.imhuso.wholesale.core.constant.WhsCacheConstants;
import com.imhuso.wholesale.core.event.StockChangeEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 库存变更缓存处理监听器
 * 负责监听库存变更事件并清除相关缓存
 * 实现库存与产品缓存的解耦
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StockChangeCacheListener {

    private static final long THRESHOLD_TIME_MS = 1000; // 1秒内的多次事件只处理一次
    private final AtomicLong lastClearTime = new AtomicLong(0); // 上次清除缓存的时间

    /**
     * 监听库存变更事件，清除产品层级缓存
     * 使用异步处理并添加防抖功能
     *
     * @param event 库存变更事件
     */
    @Async
    @EventListener
    @Order(Ordered.HIGHEST_PRECEDENCE)
    public void handleStockChangeEvent(StockChangeEvent event) {
        try {
            // 防抖处理: 在1秒内的多次事件，只处理一次
            long currentTime = System.currentTimeMillis();
            long previousTime = lastClearTime.get();

            // 如果距离上次清除缓存不足1秒且不是更新操作，则跳过本次缓存清除
            if ((currentTime - previousTime) < THRESHOLD_TIME_MS && !event.isProductUpdated()) {
                log.debug("收到库存变更事件，但距离上次缓存清除时间小于{}ms，跳过处理", THRESHOLD_TIME_MS);
                return;
            }

            // 更新上次清除时间
            if (lastClearTime.compareAndSet(previousTime, currentTime)) {
                List<Long> productIds = event.getProductIds();

                // 无论是否有产品ID，都清除整个产品层级缓存
                // 因为当前设计使用了单一key保存所有产品层级数据
                CacheUtils.clear(WhsCacheConstants.PRODUCT_HIERARCHY_CACHE_KEY);
                
                // 清除订单库存状态缓存，因为库存变更会影响订单的库存状态计算
                CacheUtils.clear(WhsCacheConstants.WHS_ORDER_STOCK_STATUS);

                if (event.isProductUpdated()) {
                    log.info("产品信息更新，已清除产品层级缓存和订单库存状态缓存");
                } else if (CollUtil.isNotEmpty(productIds)) {
                    log.debug("库存变更事件，已清除产品层级缓存和订单库存状态缓存，受影响产品数: {}", productIds.size());
                } else {
                    log.debug("库存变更事件，已清除产品层级缓存和订单库存状态缓存");
                }
            }
        } catch (Exception e) {
            log.error("处理库存变更事件失败: {}", e.getMessage());
        }
    }
}
