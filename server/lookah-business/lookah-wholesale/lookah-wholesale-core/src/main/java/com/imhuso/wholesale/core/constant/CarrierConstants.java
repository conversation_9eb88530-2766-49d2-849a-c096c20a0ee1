package com.imhuso.wholesale.core.constant;

/**
 * 物流承运商常量
 * 支持的美国和国际主要承运商
 * 注意：承运商名称需要统一为小写
 *
 * <AUTHOR>
 */
public class CarrierConstants {

    /** 未知承运商 */
    public static final String UNKNOWN = "unknown";

    /** UPS */
    public static final String UPS = "ups";

    /** FedEx */
    public static final String FEDEX = "fedex";

    /** USPS */
    public static final String USPS = "usps";

    /** DHL */
    public static final String DHL = "dhl";

    /** Canada Post */
    public static final String CANADA_POST = "canada-post";

    /** China Post */
    public static final String CHINA_POST = "china-post";

    /** Shipping1 - Truck Shipping 屏蔽shipping1 显示，设置为 LTL */
    public static final String SHIPPING1 = "ltl";
}
