package com.imhuso.wholesale.core.service;

import java.util.List;
import java.util.Map;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductSeriesBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductSeriesVo;

/**
 * 批发产品系列Service接口
 *
 * <AUTHOR>
 */
public interface IWhsProductSeriesService {

    /**
     * 批量获取系列名称
     *
     * @param seriesIds 系列ID列表
     * @return 系列ID到名称的映射
     */
    Map<Long, String> getSeriesNames(List<Long> seriesIds);

    /**
     * 获取产品系列列表
     *
     * @return 产品系列VO列表
     */
    List<WhsProductSeriesVo> queryList(WhsProductSeriesBo bo);

    /**
     * 分页查询产品系列列表
     *
     * @param bo        查询参数
     * @param pageQuery 分页参数
     * @return 产品系列分页列表
     */
    TableDataInfo<WhsProductSeriesVo> queryPageList(WhsProductSeriesBo bo, PageQuery pageQuery);

    /**
     * 根据ID获取产品系列详情
     *
     * @param id 系列ID
     * @return 产品系列VO
     */
    WhsProductSeriesVo getById(Long id);

    /**
     * 新增产品系列
     *
     * @param bo 产品系列业务对象
     * @return 结果
     */
    int insertSeries(WhsProductSeriesBo bo);

    /**
     * 修改产品系列
     *
     * @param bo 产品系列业务对象
     * @return 结果
     */
    int updateSeries(WhsProductSeriesBo bo);

    /**
     * 批量删除产品系列
     *
     * @param ids 需要删除的产品系列ID数组
     * @return 结果
     */
    int deleteSeriesByIds(Long[] ids);
}
