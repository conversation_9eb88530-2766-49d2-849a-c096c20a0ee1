package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.util.StrUtil;
import com.imhuso.wholesale.core.constant.OrderConstants;
import com.imhuso.wholesale.core.domain.WhsOrder;
import com.imhuso.wholesale.core.mapper.WhsOrderMapper;
import com.imhuso.wholesale.core.service.IWhsOrderNoFormatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 订单号格式化服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsOrderNoFormatServiceImpl implements IWhsOrderNoFormatService {
    private final WhsOrderMapper whsOrderMapper;

    @Override
    public String getOrderNoByOrder(WhsOrder order) {
        // 1. 如果填写了内部订单号，则返回内部订单号，
        if (StrUtil.isNotBlank(order.getInternalOrderNo())) {
            return order.getInternalOrderNo();
        }
        // 2. 如果没有填写，则通过发票号生成订单号
        return formatOrderInvoiceId(order.getInvoiceId());
    }

    @Override
    public String getOrderNoByOrderId(long orderId) {
        // 1. 通过订单ID查询订单
        WhsOrder order = whsOrderMapper.selectById(orderId);
        // 2. 如果订单为空，则返回空字符串
        if (order == null) {
            return "";
        }
        return getOrderNoByOrder(order);
    }

    @Override
    public String getCustomerOrderNoByOrder(WhsOrder order) {
        // 1. 如果填写了客户订单号，则返回客户订单号
        if (StrUtil.isNotBlank(order.getCustomerOrderNo())) {
            return order.getCustomerOrderNo();
        }
        // 2. 如果没有填写，直接返回订单号 # 作为前缀
        return "#" + order.getId().toString();
    }

    /**
     * 格式化订单/发票号
     * 格式: 使用OrderConstants.ORDER_INVOICE_ID_PREFIX + invoiceId
     *
     * @param invoiceId 订单/发票号
     * @return 格式化后的订单/发票号
     */
    private String formatOrderInvoiceId(Long invoiceId) {
        if (invoiceId == null) {
            return "";
        }
        return OrderConstants.ORDER_INVOICE_ID_PREFIX + invoiceId;
    }
}
