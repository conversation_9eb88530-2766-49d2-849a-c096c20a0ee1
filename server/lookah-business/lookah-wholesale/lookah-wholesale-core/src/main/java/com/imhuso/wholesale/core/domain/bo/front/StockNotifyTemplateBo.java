package com.imhuso.wholesale.core.domain.bo.front;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.imhuso.wholesale.core.domain.WhsProduct;
import com.imhuso.wholesale.core.domain.WhsStockNotify;
import com.imhuso.wholesale.core.domain.bo.IMailVariables;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import cn.hutool.json.JSONObject;

import java.io.Serial;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 库存通知模板参数对象
 * 同时支持客户端和管理员通知，提供完整的邮件模板变量
 *
 * <AUTHOR>
 */
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class StockNotifyTemplateBo implements IMailVariables, Serializable {
    /**
     * 序列化版本ID
     */
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 通知信息
     */
    private WhsStockNotify notify;

    /**
     * 产品列表
     */
    private List<WhsProduct> products;

    /**
     * 标题
     */
    private String title;

    /**
     * 管理员邮件标题（中文）
     */
    private String adminTitle;

    /**
     * 客户邮件标题（英文）
     */
    private String customerTitle;

    /**
     * 包装类型文本
     */
    private String packagingTypeText;

    /**
     * 客户邮箱（用于管理员邮件）
     */
    private String customerEmail;

    /**
     * 管理员邮箱
     */
    private String adminEmail;

    /**
     * 客户WhatsApp号码（用于管理员邮件）
     */
    private String whatsapp;

    /**
     * 客户通知模板名称
     */
    private String customerTemplate;

    /**
     * 管理员通知模板名称
     */
    private String adminTemplate;

    /**
     * 目标邮箱
     */
    private String email;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 规格属性JSON
     */
    private JSONObject specs;

    /**
     * 获取通知ID
     */
    @JsonIgnore
    public Long getNotifyId() {
        return notify != null ? notify.getId() : null;
    }

    /**
     * 获取管理员可用的模板BO
     */
    @JsonIgnore
    public StockNotifyTemplateBo forAdmin() {
        // 使用toBuilder保留所有字段，仅修改需要变更的字段
        return this.toBuilder()
            .title(this.adminTitle)
            .email(this.adminEmail)
            .build();
    }

    /**
     * 获取客户可用的模板BO
     */
    @JsonIgnore
    public StockNotifyTemplateBo forCustomer() {
        // 使用toBuilder保留所有字段，仅修改需要变更的字段
        return this.toBuilder()
            .title(this.customerTitle)
            .email(this.customerEmail)
            .build();
    }

    @JsonIgnore
    @Override
    public Map<String, Object> getVariables() {
        Map<String, Object> variables = new HashMap<>();
        variables.put("notify", notify);
        variables.put("products", products);

        // 添加标题到模板变量中
        variables.put("title", title);

        // 优先使用自定义设置的值，如果没有则从notify对象中获取
        if (customerEmail != null) {
            variables.put("customerEmail", customerEmail);
        } else if (notify != null) {
            variables.put("customerEmail", notify.getEmail());
        }

        if (whatsapp != null) {
            variables.put("whatsapp", whatsapp);
        } else if (notify != null) {
            variables.put("whatsapp", notify.getWhatsapp());
        }

        variables.put("packagingTypeText", packagingTypeText);

        // 添加SKU编码
        if (skuCode != null) {
            variables.put("skuCode", skuCode);
        }

        // 添加规格属性
        if (specs != null) {
            variables.put("specs", specs);
        }

        return variables;
    }

    @Override
    public String getTitle() {
        return title;
    }
}
