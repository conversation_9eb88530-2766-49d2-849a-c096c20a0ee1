package com.imhuso.wholesale.core.service.impl;

import com.imhuso.wholesale.core.service.IWhsInboundOrderAsyncService;
import com.imhuso.wholesale.core.service.IWhsInboundOrderSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * 入库单异步服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WhsInboundOrderAsyncServiceImpl implements IWhsInboundOrderAsyncService {

    private final IWhsInboundOrderSyncService inboundOrderSyncService;

    @Override
    @Async
    public void asyncSyncWarehouseFull(Long warehouseId) {
        try {
            log.info("开始异步全量同步仓库[{}]的入库单（查询最近一年数据）", warehouseId);
            inboundOrderSyncService.syncWarehouseInboundOrders(warehouseId, 365);
            log.info("仓库[{}]入库单全量同步完成", warehouseId);
            CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("仓库[{}]入库单全量同步失败", warehouseId, e);
            CompletableFuture.failedFuture(e);
        }
    }

    @Override
    @Async
    public void asyncSyncAllFull() {
        try {
            log.info("开始异步全量同步所有仓库的入库单（查询最近一年数据）");
            int count = inboundOrderSyncService.syncAllWarehousesFull();
            log.info("所有仓库入库单全量同步完成，成功同步{}个仓库", count);
            CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("所有仓库入库单全量同步失败", e);
            CompletableFuture.failedFuture(e);
        }
    }
}
