package com.imhuso.wholesale.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.wholesale.core.constant.MemberPermissionConstants;
import com.imhuso.wholesale.core.constant.WhsCacheConstants;
import com.imhuso.wholesale.core.domain.WhsProductVariant;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsProductSeriesBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductSeriesVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVariantVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsProductVo;
import com.imhuso.wholesale.core.domain.vo.front.PackageVariantVo;
import com.imhuso.wholesale.core.domain.vo.front.ProductHierarchyVo;
import com.imhuso.wholesale.core.domain.vo.front.ProductSeriesHierarchyVo;
import com.imhuso.wholesale.core.domain.vo.front.ProductVariantHierarchyVo;
import com.imhuso.wholesale.core.satoken.WholesaleLoginHelper;
import com.imhuso.wholesale.core.service.*;
import com.imhuso.wholesale.core.enums.PackagingType;
import com.imhuso.wholesale.core.utils.MemberPermissionHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品层级结构服务实现类
 * 专门处理前端展示的产品层级数据
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductHierarchyServiceImpl implements IProductHierarchyService {

    private final IWhsProductService productService;
    private final IWhsProductVariantService variantService;
    private final IWhsProductSeriesService seriesService;
    private final IWhsPackageItemService packageItemService;
    private final IWhsStockService stockService;
    private final MemberPermissionHelper permissionHelper;

    // 包装类型常量，避免重复转换
    private static final Integer PACKAGING_TYPE_CASE = PackagingType.CASE.getValue();
    private static final Integer PACKAGING_TYPE_INDIVIDUAL = PackagingType.INDIVIDUAL.getValue();
    private static final Integer PACKAGING_TYPE_DISPLAY = PackagingType.DISPLAY.getValue();

    /**
     * 查询产品层级结构
     * 使用内置的Spring缓存机制，配置了适当的TTL、空闲时间和容量限制
     *
     * @return 产品层级结构列表
     */
    @Override
    @Cacheable(cacheNames = WhsCacheConstants.PRODUCT_HIERARCHY_CACHE_KEY
        + "#5m#10m#10", key = "'all'", unless = "#result == null || #result.isEmpty()")
    public List<ProductSeriesHierarchyVo> queryProductHierarchy() {
        log.debug("从数据库加载产品层级结构");
        List<ProductSeriesHierarchyVo> result = buildProductHierarchyFromDatabase();

        if (CollUtil.isNotEmpty(result)) {
            refreshStockInfo(result);

            // 根据客户权限过滤价格和库存信息
            applyMemberPermissions(result);
        }

        return result;
    }

    /**
     * 根据客户权限过滤价格和库存信息
     *
     * @param hierarchyList 产品层级结构列表
     */
    private void applyMemberPermissions(List<ProductSeriesHierarchyVo> hierarchyList) {
        if (CollUtil.isEmpty(hierarchyList)) {
            return;
        }

        // 获取当前登录的客户ID
        Long memberId = null;
        try {
            // 如果用户已登录，获取用户ID
            if (WholesaleLoginHelper.isLogin()) {
                memberId = WholesaleLoginHelper.getUserId();
            }
        } catch (Exception e) {
            log.debug("获取当前登录客户ID失败，可能是未登录状态: {}", e.getMessage());
        }

        // 检查是否有显示价格权限
        boolean showPrice = memberId != null &&
            permissionHelper.hasPermission(memberId, MemberPermissionConstants.SHOW_PRICE);

        // 检查是否有显示库存权限
        boolean showStock = memberId != null &&
            permissionHelper.hasPermission(memberId, MemberPermissionConstants.SHOW_STOCK);

        // 应用权限过滤
        for (ProductSeriesHierarchyVo series : hierarchyList) {
            if (CollUtil.isEmpty(series.getProducts())) {
                continue;
            }

            for (ProductHierarchyVo product : series.getProducts()) {
                if (CollUtil.isEmpty(product.getVariants())) {
                    continue;
                }

                for (ProductVariantHierarchyVo variant : product.getVariants()) {
                    // 过滤价格信息
                    if (!showPrice) {
                        variant.setMsrp(null);
                        variant.setWholesalePrice(null);
                    }

                    // 过滤库存信息
                    if (!showStock) {
                        variant.setAvailableStock(null);
                    }

                    // 处理包装变体
                    if (CollUtil.isNotEmpty(variant.getPackageVariants())) {
                        for (PackageVariantVo packageVariant : variant.getPackageVariants()) {
                            // 过滤价格信息
                            if (!showPrice) {
                                packageVariant.setMsrp(null);
                                packageVariant.setWholesalePrice(null);
                            }

                            // 过滤库存信息
                            if (!showStock) {
                                packageVariant.setAvailableStock(null);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 刷新产品层级结构中的库存信息
     */
    private void refreshStockInfo(List<ProductSeriesHierarchyVo> hierarchyList) {
        if (CollUtil.isEmpty(hierarchyList)) {
            return;
        }

        List<Long> allVariantIds = collectAllVariantIds(hierarchyList);
        if (CollUtil.isEmpty(allVariantIds)) {
            return;
        }

        log.debug("批量从缓存获取{}个变体的库存信息", allVariantIds.size());
        Map<Long, Integer> stockMap = stockService.getAvailableStocks(allVariantIds);
        updateHierarchyStockInfo(hierarchyList, stockMap);
    }

    /**
     * 收集所有变体ID
     */
    private List<Long> collectAllVariantIds(List<ProductSeriesHierarchyVo> hierarchyList) {
        List<Long> allVariantIds = new ArrayList<>();

        for (ProductSeriesHierarchyVo series : hierarchyList) {
            if (CollUtil.isEmpty(series.getProducts())) {
                continue;
            }

            for (ProductHierarchyVo product : series.getProducts()) {
                if (CollUtil.isEmpty(product.getVariants())) {
                    continue;
                }

                for (ProductVariantHierarchyVo variant : product.getVariants()) {
                    allVariantIds.add(variant.getId());

                    if (CollUtil.isNotEmpty(variant.getPackageVariants())) {
                        for (PackageVariantVo packageVariant : variant.getPackageVariants()) {
                            allVariantIds.add(packageVariant.getId());
                        }
                    }
                }
            }
        }

        return allVariantIds;
    }

    /**
     * 更新层级结构中的库存信息
     */
    private void updateHierarchyStockInfo(List<ProductSeriesHierarchyVo> hierarchyList, Map<Long, Integer> stockMap) {
        for (ProductSeriesHierarchyVo series : hierarchyList) {
            if (CollUtil.isEmpty(series.getProducts())) {
                continue;
            }

            for (ProductHierarchyVo product : series.getProducts()) {
                if (CollUtil.isEmpty(product.getVariants())) {
                    continue;
                }

                for (ProductVariantHierarchyVo variant : product.getVariants()) {
                    variant.setAvailableStock(stockMap.getOrDefault(variant.getId(), 0));

                    if (CollUtil.isNotEmpty(variant.getPackageVariants())) {
                        for (PackageVariantVo packageVariant : variant.getPackageVariants()) {
                            packageVariant.setAvailableStock(stockMap.getOrDefault(packageVariant.getId(), 0));
                        }
                    }
                }
            }
        }
    }

    /**
     * 从数据库查询并构建完整的产品层级结构
     */
    private List<ProductSeriesHierarchyVo> buildProductHierarchyFromDatabase() {
        WhsProductSeriesBo seriesQuery = new WhsProductSeriesBo();
        seriesQuery.setStatus(BusinessConstants.NORMAL);
        List<WhsProductSeriesVo> seriesList = seriesService.queryList(seriesQuery);

        if (CollUtil.isEmpty(seriesList)) {
            return Collections.emptyList();
        }

        return seriesList.stream()
            .map(this::buildSeriesHierarchy)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * 构建系列层级结构
     */
    private ProductSeriesHierarchyVo buildSeriesHierarchy(WhsProductSeriesVo series) {
        ProductSeriesHierarchyVo seriesHierarchy = new ProductSeriesHierarchyVo();
        BeanUtil.copyProperties(series, seriesHierarchy);

        WhsProductBo productQuery = new WhsProductBo();
        productQuery.setSeriesId(series.getId());
        productQuery.setStatus(BusinessConstants.NORMAL);
        List<WhsProductVo> products = productService.queryList(productQuery);

        if (CollUtil.isEmpty(products)) {
            return null;
        }

        List<ProductHierarchyVo> productHierarchyList = buildProductHierarchy(products);
        if (CollUtil.isEmpty(productHierarchyList)) {
            return null;
        }

        seriesHierarchy.setProducts(productHierarchyList);
        return seriesHierarchy;
    }

    /**
     * 构建产品层级结构
     */
    private List<ProductHierarchyVo> buildProductHierarchy(List<WhsProductVo> products) {
        return products.stream().map(product -> {
                List<ProductVariantHierarchyVo> variants = buildVariantHierarchy(product);
                if (CollUtil.isEmpty(variants)) {
                    return null;
                }

                ProductHierarchyVo productHierarchy = new ProductHierarchyVo();
                BeanUtil.copyProperties(product, productHierarchy);
                productHierarchy.setVariants(variants);

                return productHierarchy;
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * 构建产品变体层级结构
     */
    private List<ProductVariantHierarchyVo> buildVariantHierarchy(final WhsProductVo product) {
        // 获取产品所有变体
        Map<Long, List<WhsProductVariant>> variantMap = variantService
            .getVariantsByProductIds(Collections.singletonList(product.getId()));
        List<WhsProductVariant> variantEntities = Optional.ofNullable(variantMap.get(product.getId()))
            .orElse(Collections.emptyList());

        if (CollUtil.isEmpty(variantEntities)) {
            return Collections.emptyList();
        }

        List<WhsProductVariantVo> allVariants = variantService.convertToVoList(variantEntities);
        Map<Long, WhsProductVariantVo> variantById = allVariants.stream()
            .collect(Collectors.toMap(WhsProductVariantVo::getId, v -> v, (v1, v2) -> v1));

        // 查询所有变体ID
        List<Long> allVariantIds = new ArrayList<>(variantById.keySet());

        // 获取变体关系
        Map<Long, Map<Long, Integer>> parentToChildrenMap = packageItemService
            .getAllPackageRelationships(allVariantIds);

        // 构建子变体到父变体的映射
        Map<Long, Set<Long>> childToParentsMap = new HashMap<>();
        for (Map.Entry<Long, Map<Long, Integer>> entry : parentToChildrenMap.entrySet()) {
            Long parentId = entry.getKey();
            Set<Long> childIds = entry.getValue().keySet();

            for (Long childId : childIds) {
                childToParentsMap.computeIfAbsent(childId, k -> new HashSet<>()).add(parentId);
            }
        }

        // 将所有相关变体组织成包装变体组
        Set<Long> processedVariants = new HashSet<>();
        List<Set<Long>> variantGroups = new ArrayList<>();

        for (Long variantId : allVariantIds) {
            if (processedVariants.contains(variantId)) {
                continue;
            }

            Set<Long> connectedVariants = new HashSet<>();
            collectConnectedVariants(variantId, connectedVariants, parentToChildrenMap, childToParentsMap, 0, 100);

            if (connectedVariants.isEmpty()) {
                connectedVariants.add(variantId);
            }

            variantGroups.add(connectedVariants);
            processedVariants.addAll(connectedVariants);
        }

        // 为每个变体组构建层级结构
        List<ProductVariantHierarchyVo> result = new ArrayList<>();

        for (Set<Long> variantGroup : variantGroups) {
            // 按包装类型分类
            List<WhsProductVariantVo> caseVariants = new ArrayList<>();
            List<WhsProductVariantVo> displayVariants = new ArrayList<>();
            List<WhsProductVariantVo> individualVariants = new ArrayList<>();

            for (Long variantId : variantGroup) {
                WhsProductVariantVo variant = variantById.get(variantId);
                if (variant == null)
                    continue;

                Integer packagingType = variant.getPackagingType();
                if (PACKAGING_TYPE_CASE.equals(packagingType)) {
                    caseVariants.add(variant);
                } else if (PACKAGING_TYPE_DISPLAY.equals(packagingType)) {
                    displayVariants.add(variant);
                } else if (PACKAGING_TYPE_INDIVIDUAL.equals(packagingType)) {
                    individualVariants.add(variant);
                }
            }

            // 排序保证结果一致性
            caseVariants.sort(Comparator.comparing(WhsProductVariantVo::getId));
            displayVariants.sort(Comparator.comparing(WhsProductVariantVo::getId));
            individualVariants.sort(Comparator.comparing(WhsProductVariantVo::getId));

            // 按包装层级构建结构
            if (!caseVariants.isEmpty()) {
                // 箱装作为主变体
                WhsProductVariantVo mainVariant = caseVariants.get(0);
                List<WhsProductVariantVo> otherVariants = new ArrayList<>();
                otherVariants.addAll(displayVariants);
                otherVariants.addAll(individualVariants);
                if (caseVariants.size() > 1) {
                    otherVariants.addAll(caseVariants.subList(1, caseVariants.size()));
                }

                result.add(buildVariantGroupHierarchy(product, mainVariant, otherVariants, parentToChildrenMap));
            } else if (!displayVariants.isEmpty()) {
                // 展示盒作为主变体
                WhsProductVariantVo mainVariant = displayVariants.get(0);
                List<WhsProductVariantVo> otherVariants = new ArrayList<>(individualVariants);
                if (displayVariants.size() > 1) {
                    otherVariants.addAll(displayVariants.subList(1, displayVariants.size()));
                }

                result.add(buildVariantGroupHierarchy(product, mainVariant, otherVariants, parentToChildrenMap));
            } else if (!individualVariants.isEmpty()) {
                // 单品作为主变体
                WhsProductVariantVo mainVariant = individualVariants.get(0);
                List<WhsProductVariantVo> otherVariants = new ArrayList<>();
                if (individualVariants.size() > 1) {
                    otherVariants.addAll(individualVariants.subList(1, individualVariants.size()));
                }

                result.add(buildVariantGroupHierarchy(product, mainVariant, otherVariants, parentToChildrenMap));
            }
        }

        return result;
    }

    /**
     * 收集与指定变体直接或间接关联的所有变体
     */
    private void collectConnectedVariants(Long variantId, Set<Long> connectedVariants,
                                          Map<Long, Map<Long, Integer>> parentToChildrenMap,
                                          Map<Long, Set<Long>> childToParentsMap,
                                          int currentDepth, int maxDepth) {
        if (currentDepth >= maxDepth) {
            log.warn("收集关联变体超过最大递归深度: {}, variantId: {}", maxDepth, variantId);
            return;
        }

        if (connectedVariants.contains(variantId)) {
            return;
        }

        connectedVariants.add(variantId);

        // 添加子变体及其关联变体
        Map<Long, Integer> children = parentToChildrenMap.getOrDefault(variantId, Collections.emptyMap());
        for (Long childId : children.keySet()) {
            collectConnectedVariants(childId, connectedVariants, parentToChildrenMap, childToParentsMap,
                currentDepth + 1, maxDepth);
        }

        // 添加父变体及其关联变体
        Set<Long> parents = childToParentsMap.getOrDefault(variantId, Collections.emptySet());
        for (Long parentId : parents) {
            collectConnectedVariants(parentId, connectedVariants, parentToChildrenMap, childToParentsMap,
                currentDepth + 1, maxDepth);
        }
    }

    /**
     * 生成组合标题：产品标题 + 变体属性
     */
    private String generateCombinedTitle(final String productName, final WhsProductVariantVo variant) {
        if (StringUtils.isBlank(productName)) {
            return "";
        }

        StringBuilder titleBuilder = new StringBuilder(productName);

        if (StringUtils.isNotBlank(variant.getSpecs())) {
            try {
                JSONObject specsJson = JSONUtil.parseObj(variant.getSpecs());
                List<Map.Entry<String, Object>> attributeEntries = new ArrayList<>(specsJson.entrySet());
                attributeEntries.sort(Map.Entry.comparingByKey());

                for (Map.Entry<String, Object> entry : attributeEntries) {
                    String attrValue = String.valueOf(entry.getValue());
                    if (StringUtils.isNotBlank(attrValue)) {
                        titleBuilder.append("-").append(attrValue.trim());
                    }
                }
            } catch (Exception e) {
                log.error("解析变体specs失败: {}, specs内容: {}", e.getMessage(), variant.getSpecs(), e);
            }
        }

        return titleBuilder.toString();
    }

    /**
     * 为每个变体组构建层级结构
     */
    private ProductVariantHierarchyVo buildVariantGroupHierarchy(final WhsProductVo product,
                                                                 final WhsProductVariantVo mainVariant,
                                                                 final List<WhsProductVariantVo> otherVariants,
                                                                 final Map<Long, Map<Long, Integer>> parentToChildrenMap) {
        // 创建变体层级结构
        ProductVariantHierarchyVo hierarchyVo = new ProductVariantHierarchyVo();
        BeanUtil.copyProperties(mainVariant, hierarchyVo);
        hierarchyVo.setSpecs(mainVariant.getSpecs());

        // 生成标题
        String title = generateCombinedTitle(product.getItemName(), mainVariant);
        hierarchyVo.setTitle(title);

        // 获取主变体的直接子变体及数量
        Map<Long, Integer> directChildQuantities = parentToChildrenMap.getOrDefault(mainVariant.getId(),
            Collections.emptyMap());

        // 设置PCS值
        hierarchyVo.setPcs(calculatePcsValue(mainVariant, directChildQuantities));

        // 构建包装变体列表
        List<PackageVariantVo> packageVariants = new ArrayList<>();
        for (WhsProductVariantVo otherVariant : otherVariants) {
            PackageVariantVo packageVariant = new PackageVariantVo();
            BeanUtil.copyProperties(otherVariant, packageVariant);
            packageVariant.setSpecs(otherVariant.getSpecs());

            // 设置PCS值
            Map<Long, Integer> childQuantities = parentToChildrenMap.getOrDefault(
                otherVariant.getId(), Collections.emptyMap());
            packageVariant.setPcs(calculatePcsValue(otherVariant, childQuantities));

            // 设置标题
            packageVariant.setTitle(generateCombinedTitle(product.getItemName(), otherVariant));
            packageVariants.add(packageVariant);
        }

        hierarchyVo.setPackageVariants(packageVariants);
        return hierarchyVo;
    }

    /**
     * 计算变体的PCS值
     *
     * @param variant         变体对象
     * @param childQuantities 子变体数量映射
     * @return PCS值
     */
    private Integer calculatePcsValue(WhsProductVariantVo variant, Map<Long, Integer> childQuantities) {
        if (PACKAGING_TYPE_INDIVIDUAL.equals(variant.getPackagingType())) {
            return 1;
        }

        // 对于非单品，查找单品子变体的数量作为PCS值
        Integer pcsValue = null;
        for (Map.Entry<Long, Integer> entry : childQuantities.entrySet()) {
            WhsProductVariantVo childVariant = variantService.getVariant(entry.getKey(), false);
            if (childVariant != null && PACKAGING_TYPE_INDIVIDUAL.equals(childVariant.getPackagingType())) {
                pcsValue = entry.getValue();
                break;
            }
        }
        return pcsValue != null ? pcsValue : 1;
    }
}
