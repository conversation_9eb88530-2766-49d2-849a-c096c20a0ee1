package com.imhuso.wholesale.core.domain.vo.admin;

import java.io.Serial;
import java.io.Serializable;

import com.imhuso.wholesale.core.domain.WhsWarehouseLogisticsMethod;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

/**
 * 仓库物流方式视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsWarehouseLogisticsMethod.class)
public class WhsWarehouseLogisticsMethodVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 提供商ID
     */
    private Long providerId;

    /**
     * 物流方式代码
     */
    private String methodCode;

    /**
     * 物流方式名称
     */
    private String methodName;

    /**
     * 渠道ID(第三方系统)
     */
    private String channelId;

    /**
     * 状态（0停用 1启用）
     */
    private String status;

    /**
     * 优先级(1-10，数字越小优先级越高)
     */
    private Integer priority;
}
