package com.imhuso.wholesale.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.*;

import java.io.Serial;

/**
 * 发货方案项对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("whs_shipment_plan_item")
public class WhsShipmentPlanItem extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    @TableId
    private Long id;

    /**
     * 方案ID
     */
    private Long planId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 变体ID
     */
    private Long variantId;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 计划项数量 (例如: 1箱, 5个散件)
     */
    private Integer quantity;

    /**
     * 包装类型: 0-单品 1-展示盒 2-整箱
     *
     * @see com.imhuso.wholesale.enums.PackagingType
     */
    private Integer packagingType;

    /**
     * 包装数量 (每计划单位包含的原始物品数)
     */
    private Integer packagingQuantity;

    /**
     * 已发货数量
     */
    private Integer shippedQuantity;
}
