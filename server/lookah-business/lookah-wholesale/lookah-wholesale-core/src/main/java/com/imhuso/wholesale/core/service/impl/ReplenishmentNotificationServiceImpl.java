package com.imhuso.wholesale.core.service.impl;

import com.imhuso.common.excel.utils.ExcelUtil;
import com.imhuso.wholesale.core.domain.bo.admin.WhsReplenishmentBo;
import com.imhuso.wholesale.core.domain.bo.front.ReplenishmentNotifyTemplateBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsReplenishmentVo;
import com.imhuso.wholesale.core.service.IReplenishmentNotificationService;
import com.imhuso.wholesale.core.service.IWecomService;
import com.imhuso.wholesale.core.service.IWhsMailService;
import com.imhuso.wholesale.core.service.IWhsReplenishmentService;
import com.imhuso.wholesale.core.service.INotificationConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 补货通知服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReplenishmentNotificationServiceImpl implements IReplenishmentNotificationService {

    private final IWhsReplenishmentService replenishmentService;
    private final IWhsMailService mailService;
    private final IWecomService wecomService;
    private final INotificationConfigService notificationConfigService;

    @Override
    public int checkAndSendReplenishmentNotifications() {
        log.info("开始执行补货通知检查");

        try {
            // 1. 查询需要补货的产品列表
            List<WhsReplenishmentVo> replenishmentList = replenishmentService.exportReplenishmentList(new WhsReplenishmentBo());

            if (replenishmentList == null || replenishmentList.isEmpty()) {
                log.info("当前无需补货的产品，跳过通知发送");
                return 0;
            }

            log.info("检测到 {} 个产品需要补货", replenishmentList.size());

            // 2. 计算统计信息
            int totalProductCount = replenishmentList.size();
            int totalRecommendedQuantity = replenishmentList.stream()
                .mapToInt(item -> item.getRecommendedQuantity() != null ? item.getRecommendedQuantity() : 0)
                .sum();

            // 3. 生成Excel文件
            File excelFile = generateReplenishmentExcel(replenishmentList);

            // 4. 构建邮件模板数据
            ReplenishmentNotifyTemplateBo templateBo = ReplenishmentNotifyTemplateBo.builder()
                .title("库存补货提醒 - " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                .replenishmentList(replenishmentList)
                .checkTime(LocalDateTime.now())
                .totalProductCount(totalProductCount)
                .totalRecommendedQuantity(totalRecommendedQuantity)
                .build();

            // 5. 发送邮件通知
            int emailCount = sendEmailNotifications(templateBo, excelFile);

            // 6. 发送企业微信通知
            sendWecomNotification(totalProductCount, totalRecommendedQuantity, excelFile);

            // 7. 清理临时文件
            if (excelFile != null && excelFile.exists()) {
                boolean deleted = excelFile.delete();
                if (!deleted) {
                    log.warn("临时Excel文件删除失败: {}", excelFile.getAbsolutePath());
                }
            }

            log.info("补货通知发送完成，共发送 {} 条邮件通知", emailCount);
            return emailCount;

        } catch (Exception e) {
            log.error("补货通知检查和发送失败", e);
            throw new RuntimeException("补货通知发送失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成补货Excel文件
     */
    private File generateReplenishmentExcel(List<WhsReplenishmentVo> replenishmentList) {
        try {
            // 创建临时文件
            File tempFile = File.createTempFile("replenishment_", ".xlsx");

            // 生成Excel
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                ExcelUtil.exportExcel(replenishmentList, "补货清单", WhsReplenishmentVo.class, fos);
            }

            // 重命名文件为中文名称
            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String fileName = "补货通知-" + dateStr + ".xlsx";
            File renamedFile = new File(tempFile.getParent(), fileName);

            if (tempFile.renameTo(renamedFile)) {
                log.info("补货Excel文件生成成功: {}", renamedFile.getAbsolutePath());
                return renamedFile;
            } else {
                log.warn("文件重命名失败，使用原文件名: {}", tempFile.getAbsolutePath());
                return tempFile;
            }

        } catch (IOException e) {
            log.error("生成补货Excel文件失败", e);
            return null;
        }
    }

    /**
     * 发送邮件通知
     */
    private int sendEmailNotifications(ReplenishmentNotifyTemplateBo templateBo, File excelFile) {
        // 检查补货邮件通知是否启用
        if (!notificationConfigService.isStockEmailEnabled()) {
            log.info("补货邮件通知未启用，跳过邮件发送");
            return 0;
        }

        List<String> adminEmails = notificationConfigService.getStockEmailList();

        if (adminEmails.isEmpty()) {
            log.warn("未配置补货通知管理员邮箱，跳过邮件通知");
            return 0;
        }

        log.info("开始发送补货通知邮件，收件人: {}", String.join(", ", adminEmails));

        int successCount = 0;
        for (String adminEmail : adminEmails) {
            try {
                log.info("正在发送补货通知邮件到: {}", adminEmail);
                if (excelFile != null) {
                    mailService.sendAdminTemplateEmail(
                        "stock_replenishment",
                        templateBo,
                        adminEmail,
                        new File[]{excelFile}
                    );
                } else {
                    mailService.sendAdminTemplateEmail(
                        "stock_replenishment",
                        templateBo,
                        adminEmail
                    );
                }
                successCount++;
                log.info("补货通知邮件发送成功: {} (第{}个)", adminEmail, successCount);
            } catch (Exception e) {
                log.error("补货通知邮件发送失败: {}", adminEmail, e);
            }
        }

        log.info("补货通知邮件发送完成，成功发送 {}/{} 封邮件", successCount, adminEmails.size());
        return successCount;
    }

    /**
     * 发送企业微信通知
     */
    private void sendWecomNotification(int totalProductCount, int totalRecommendedQuantity, File excelFile) {
        // 使用数据库配置检查企业微信通知是否启用
        if (!notificationConfigService.isStockWecomEnabled()) {
            log.debug("企业微信通知未启用，跳过发送");
            return;
        }

        // 获取数据库配置的webhook key列表
        List<String> webhookKeys = notificationConfigService.getStockWecomKeys();
        if (webhookKeys.isEmpty()) {
            log.warn("未配置补货通知企业微信 webhook key，跳过企业微信发送");
            return;
        }

        try {
            String title = "📦 库存补货提醒";
            String content = String.format(
                """
                    ## 📦 库存补货提醒\s

                    ---

                    🕐 **检查时间：** `%s`

                    📊 **补货统计：**
                    - 🔢 需要补货的产品数量：<font color="warning">**%d 个**</font>
                    - 📦 总推荐补货数量：<font color="info">**%d 件**</font>

                    📋 **详细报表：** 请查看下方Excel附件

                    ---

                    ⚠️ **请及时安排补货，确保库存充足！**

                    > 💡 提示：详细的补货清单和建议数量请参考Excel报表""",
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                totalProductCount,
                totalRecommendedQuantity
            );

            // 向每个配置的webhook key发送消息和Excel文件
            int successCount = 0;
            for (String webhookKey : webhookKeys) {
                try {
                    wecomService.sendMarkdownMessageWithFile(title, content, excelFile, webhookKey);
                    successCount++;
                    log.info("补货通知企业微信消息和文件发送成功: webhook key={}", webhookKey);
                } catch (Exception e) {
                    log.error("补货通知企业微信消息发送失败: webhook key={}, error={}", webhookKey, e.getMessage(), e);
                }
            }
            log.info("补货通知企业微信发送完成，成功发送 {}/{} 个群", successCount, webhookKeys.size());
        } catch (Exception e) {
            log.error("补货通知企业微信消息发送失败", e);
        }
    }
}
