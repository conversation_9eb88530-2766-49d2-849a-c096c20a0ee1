package com.imhuso.wholesale.core.domain.vo.admin;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * ERP提供商状态视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ErpProviderStatusVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 是否启用ERP集成
     */
    private Boolean enabled;

    /**
     * 提供商名称
     */
    private String providerName;

    /**
     * 提供商类型
     */
    private String type;

    /**
     * API基础地址
     */
    private String baseUrl;

    /**
     * 是否为内部系统
     */
    private Boolean internal;

    /**
     * 是否启用Webhook
     */
    private Boolean webhookEnabled;

    /**
     * 是否可用
     */
    private Boolean available;

    /**
     * 是否为当前使用的提供商
     */
    private Boolean current;

    /**
     * 配置信息
     */
    private String configInfo;

    /**
     * 最后检查时间
     */
    private Long lastCheckTime;

    /**
     * 错误信息（如果不可用）
     */
    private String errorMessage;

    /**
     * 创建可用状态
     *
     * @param providerName 提供商名称
     * @param providerType 提供商类型
     * @param current 是否为当前提供商
     * @param configInfo 配置信息
     * @return 状态对象
     */
    public static ErpProviderStatusVo available(String providerName, String providerType,
                                               Boolean current, String configInfo) {
        ErpProviderStatusVo status = new ErpProviderStatusVo();
        status.setProviderName(providerName);
        status.setType(providerType);
        status.setAvailable(true);
        status.setCurrent(current);
        status.setConfigInfo(configInfo);
        status.setLastCheckTime(System.currentTimeMillis());
        return status;
    }

    /**
     * 创建不可用状态
     *
     * @param providerName 提供商名称
     * @param providerType 提供商类型
     * @param current 是否为当前提供商
     * @param errorMessage 错误信息
     * @return 状态对象
     */
    public static ErpProviderStatusVo unavailable(String providerName, String providerType,
                                                 Boolean current, String errorMessage) {
        ErpProviderStatusVo status = new ErpProviderStatusVo();
        status.setProviderName(providerName);
        status.setType(providerType);
        status.setAvailable(false);
        status.setCurrent(current);
        status.setErrorMessage(errorMessage);
        status.setLastCheckTime(System.currentTimeMillis());
        return status;
    }
}
