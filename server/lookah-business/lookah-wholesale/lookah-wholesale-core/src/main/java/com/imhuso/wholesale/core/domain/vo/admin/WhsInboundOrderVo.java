package com.imhuso.wholesale.core.domain.vo.admin;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imhuso.wholesale.core.domain.WhsInboundOrder;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 入库单视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WhsInboundOrder.class)
public class WhsInboundOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 入库单ID
     */
    private Long id;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 外部入库单号
     */
    private String externalOrderNo;

    /**
     * 我方订单号
     */
    private String orderNo;

    /**
     * 状态文本
     */
    private String status;

    /**
     * 状态编码
     */
    private String statusCode;

    /**
     * 状态值（数字）
     */
    private Integer statusValue;

    /**
     * 状态文本描述
     */
    private String statusText;

    /**
     * 预计到达时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectedArrival;

    /**
     * 实际到达时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualArrival;

    /**
     * 最后同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastSyncTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 总数量
     */
    private Integer totalQuantity;

    /**
     * 已接收数量
     */
    private Integer totalReceivedQuantity;

    /**
     * 总在途数量
     */
    private Integer totalInTransitQuantity;

    /**
     * 备注
     */
    private String remark;

    /**
     * 入库单明细列表
     */
    private List<WhsInboundOrderItemVo> items;
}
