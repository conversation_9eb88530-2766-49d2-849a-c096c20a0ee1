package com.imhuso.wholesale.core.service.impl;

import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderVo;
import com.imhuso.wholesale.core.event.WhsOrderEvent.WhsOrderEventType;
import com.imhuso.wholesale.core.service.IOrderNotificationService;
import com.imhuso.wholesale.core.strategy.orderNotification.IOrderNotificationStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单通知服务实现类
 * <p>
 * 基于策略模式实现，支持多种通知渠道，如邮件、短信等
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OrderNotificationServiceImpl implements IOrderNotificationService {

    /**
     * 注入所有通知策略实现
     * Spring会自动查找所有实现了IOrderNotificationStrategy接口的Bean并注入
     */
    private final List<IOrderNotificationStrategy> notificationStrategies;

    @Override
    public void processNotification(WhsOrderVo order, WhsOrderEventType eventType) {
        if (order == null || eventType == null) {
            log.warn("订单或事件类型为空，无法处理通知");
            return;
        }

        log.info("处理订单通知：订单号={}, 事件类型={}", order.getOrderNo(), eventType);

        // 如果没有可用的通知策略，记录警告并返回
        if (notificationStrategies == null || notificationStrategies.isEmpty()) {
            log.warn("没有可用的通知策略实现");
            return;
        }

        // 遍历所有通知策略，对每个策略处理管理员和客户通知
        for (IOrderNotificationStrategy strategy : notificationStrategies) {
            try {
                log.debug("使用 [{}] 策略处理订单通知", strategy.getName());

                // 处理管理员通知
                if (strategy.isAdminNotificationEnabled(eventType)) {
                    boolean adminResult = strategy.sendAdminNotification(order, eventType);
                    log.debug("[{}] 策略管理员通知结果: {}", strategy.getName(), adminResult ? "成功" : "失败");
                }

                // 处理客户通知
                if (strategy.isCustomerNotificationEnabled(eventType)) {
                    boolean customerResult = strategy.sendCustomerNotification(order, eventType);
                    log.debug("[{}] 策略客户通知结果: {}", strategy.getName(), customerResult ? "成功" : "失败");
                }
            } catch (Exception e) {
                // 单个策略失败不应影响其他策略的执行
                log.error("[{}] 策略处理通知异常: {}", strategy.getName(), e.getMessage(), e);
            }
        }

        log.info("订单通知处理完成: 订单号={}, 事件类型={}", order.getOrderNo(), eventType);
    }
}
