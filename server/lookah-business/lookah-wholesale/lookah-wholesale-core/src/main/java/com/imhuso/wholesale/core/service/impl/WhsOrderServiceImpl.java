package com.imhuso.wholesale.core.service.impl;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderCreateBo;
import com.imhuso.wholesale.core.domain.bo.admin.WhsOrderNoEditBo;
import com.imhuso.wholesale.core.domain.bo.front.OrderBo;
import com.imhuso.wholesale.core.domain.bo.front.OrderListBo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderProgressVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentVo;
import com.imhuso.wholesale.core.domain.vo.admin.WhsOrderVo;
import com.imhuso.wholesale.core.domain.vo.front.OrderVo;
import com.imhuso.wholesale.core.service.*;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 批发订单Service实现类
 * <p>
 * 本类作为门面模式实现，将具体功能委派给拆分后的专职服务：
 * 1. IWhsOrderQueryService - 处理订单查询相关逻辑
 * 2. IWhsOrderManagementService - 处理订单状态变更、取消等管理功能
 * 3. IWhsOrderCreationService - 处理订单创建相关逻辑
 * 4. IWhsOrderItemService - 处理订单项相关逻辑
 * 5. IWhsOrderBaseService - 处理订单基础操作
 * 6. IWhsOrderStatusService - 统一处理订单状态变更
 * 7. IWhsOrderInvoiceService - 处理订单发票相关逻辑
 * 8. IWhsOrderShipmentQueryService - 处理订单发货记录查询逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhsOrderServiceImpl implements IWhsOrderService {

    private final IWhsOrderCreationService orderCreationService;
    private final IWhsOrderQueryService orderQueryService;
    private final IWhsOrderManagementService orderManagementService;
    private final IWhsOrderStatusService orderStatusService;
    private final IWhsOrderInvoiceService orderInvoiceService;
    private final IWhsOrderPackingSlipService orderPackingSlipService;
    private final IWhsOrderShipmentQueryService orderShipmentQueryService;
    private final IWhsOrderProgressService orderProgressService;

    @Override
    public Long createOrder(OrderBo bo) {
        return orderCreationService.createOrder(bo);
    }

    @Override
    public TableDataInfo<OrderVo> queryMemberOrderPageList(OrderListBo bo, PageQuery pageQuery) {
        return orderQueryService.queryMemberOrderPageList(bo, pageQuery);
    }

    @Override
    public OrderVo selectOrderById(Long orderId) {
        return orderQueryService.selectOrderById(orderId);
    }

    @Override
    public TableDataInfo<WhsOrderVo> queryPageList(WhsOrderBo bo, PageQuery pageQuery) {
        return orderQueryService.queryPageList(bo, pageQuery);
    }

    @Override
    public WhsOrderVo getOrderById(Long id) {
        return orderQueryService.getOrderDetail(id);
    }

    @Override
    public boolean updateOrderStatus(Long id, Integer orderStatus, String remark) {
        // 日志记录
        log.info("更新订单主状态: id={}, orderStatus={}, remark={}", id, orderStatus, remark);
        return orderStatusService.updateOrderStatus(id, orderStatus, null, null, null, remark);
    }

    @Override
    public boolean updatePaymentStatus(Long id, Integer paymentStatus, String remark) {
        // 日志记录
        log.info("更新订单支付状态: id={}, paymentStatus={}, remark={}", id, paymentStatus, remark);
        return orderStatusService.updateOrderStatus(id, null, paymentStatus, null, null, remark);
    }

    @Override
    public boolean updateInvoiceStatus(Long id, Integer invoiceStatus, String remark) {
        // 日志记录
        log.info("更新订单发票状态: id={}, invoiceStatus={}, remark={}", id, invoiceStatus, remark);
        return orderStatusService.updateOrderStatus(id, null, null, null, invoiceStatus, remark);
    }

    @Override
    public void cancelOrder(Long orderId, String cancelReason) {
        log.info("取消订单: orderId={}, cancelReason={}", orderId, cancelReason);
        orderManagementService.cancelOrder(orderId, cancelReason);
    }

    @Override
    public void downloadOrderInvoice(Long orderId, HttpServletResponse response) throws Exception {
        orderInvoiceService.downloadOrderInvoice(orderId, response);
    }

    @Override
    public void downloadOrderPackingSlip(Long orderId, HttpServletResponse response) throws Exception {
        log.info("下载订单[{}]装箱单", orderId);
        orderPackingSlipService.downloadOrderPackingSlip(orderId, response);
    }

    @Override
    public List<WhsOrderShipmentVo> getOrderShipmentRecords(Long orderId) {
        // 日志记录
        log.info("查询订单发货记录: orderId={}", orderId);
        return orderShipmentQueryService.getOrderShipmentRecords(orderId);
    }

    @Override
    public WhsOrderProgressVo getOrderProgress(Long id) {
        // 日志记录
        log.info("查询订单进度: id={}", id);
        return orderProgressService.getOrderProgress(id);
    }

    @Override
    public boolean updateOrderNo(Long orderId, WhsOrderNoEditBo bo) {
        return orderManagementService.updateOrderNo(orderId, bo);
    }

    /**
     * 手动下单
     *
     * @param bo 订单信息
     */
    @Override
    public boolean manualOrder(WhsOrderCreateBo bo) {
        return orderCreationService.manualOrder(bo);
    }

    /**
     * 撤回订单到草稿状态
     *
     * @param orderId 订单ID
     * @param remark  撤回原因
     * @return 成功与否
     */
    @Override
    public boolean revertOrderToDraft(Long orderId, String remark) {
        return orderManagementService.revertOrderToDraft(orderId, remark);
    }

    /**
     * 编辑草稿状态订单
     *
     * @param orderId 订单ID
     * @param bo      订单信息
     * @return 成功与否
     */
    @Override
    public boolean editDraftOrder(Long orderId, WhsOrderCreateBo bo) {
        log.info("编辑草稿状态订单: orderId={}, customerOrderNo={}", orderId, bo.getCustomerOrderNo());
        return orderCreationService.editDraftOrder(orderId, bo);
    }

    /**
     * 强制完成订单
     *
     * @param orderId 订单ID
     * @param reason  强制完成的原因
     * @return 成功与否
     */
    @Override
    public boolean forceCompleteOrder(Long orderId, String reason) {
        log.info("强制完成订单: orderId={}, reason={}", orderId, reason);
        return orderStatusService.forceCompleteOrder(orderId, reason);
    }

}
