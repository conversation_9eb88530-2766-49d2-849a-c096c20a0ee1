package com.imhuso.wholesale.core.domain.bo.admin;

import com.imhuso.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 包裹状态更新业务对象
 *
 * <AUTHOR>
 */
@Data
public class WhsPackageStatusUpdateBo {

    /**
     * 包裹状态
     */
    @NotNull(message = "包裹状态不能为空", groups = EditGroup.class)
    private Integer packageStatus;

    /**
     * 备注
     */
    private String remark;
}
