package com.imhuso.wholesale.core.adapter;

import com.imhuso.common.core.exception.ServiceException;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 海外仓提供商工厂类
 *
 * <AUTHOR>
 */
@Component
public class OverseasWarehouseProviderFactory {

    /**
     * 提供商映射
     */
    private final Map<String, IOverseasWarehouseProvider> providerMap = new HashMap<>();

    /**
     * 所有提供商实现列表
     */
    private final List<IOverseasWarehouseProvider> providerList;

    public OverseasWarehouseProviderFactory(List<IOverseasWarehouseProvider> providerList) {
        this.providerList = providerList;
    }

    /**
     * 初始化提供商映射
     */
    @PostConstruct
    public void init() {
        for (IOverseasWarehouseProvider provider : providerList) {
            providerMap.put(provider.getProviderType(), provider);
        }
    }

    /**
     * 获取提供商实现
     *
     * @param providerType 提供商类型
     * @return 提供商实现
     */
    public IOverseasWarehouseProvider getProvider(String providerType) {
        IOverseasWarehouseProvider provider = providerMap.get(providerType);
        if (provider == null) {
            throw new ServiceException("未找到海外仓提供商实现: " + providerType);
        }
        return provider;
    }
}
