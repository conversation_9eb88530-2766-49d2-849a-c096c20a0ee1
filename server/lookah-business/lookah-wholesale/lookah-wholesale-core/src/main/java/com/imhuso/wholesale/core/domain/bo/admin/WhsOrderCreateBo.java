package com.imhuso.wholesale.core.domain.bo.admin;

import java.math.BigDecimal;
import java.util.List;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-04-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WhsOrderCreateBo {
    /**
     * 订单号
     */
    @NotBlank(message = "wholesale.admin.order.no.not.blank")
    private String orderNo;

    /**
     * 客户订单号
     */
    private String customerOrderNo;

    /**
     * 参考号
     */
    private String referenceNo;

    /**
     * 客户ID
     */
    @NotNull(message = "wholesale.admin.order.customer.id.not.null")
    private Long customerId;

    /**
     * 销售代表ID（可选，如果不指定则使用客户绑定的销售代表）
     */
    private Long salespersonId;

    /**
     * 客户地址ID
     */
    @NotNull(message = "wholesale.admin.order.customer.address.not.null")
    private Long customerAddressId;

    /**
     * 订单项
     */
    @NotEmpty(message = "wholesale.admin.order.items.not.empty")
    private List<WhsOrderCreateItemBo> items;

    /**
     * 折扣金额
     */
    @DecimalMin(value = "0.00", message = "wholesale.admin.order.discount.amount.min")
    private BigDecimal discountAmount;

    /**
     * 是否为补货订单
     * 0-否，1-是
     */
    private Boolean isReplenishment;

    /**
     * 订单文件列表
     */
    private List<WhsOrderFileCreateBo> files;
}
