package com.imhuso.wholesale.core.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * ERP库存列表数据包装类
 *
 * 设计用于兼容ERP API的两种不同返回格式：
 * 1. 指定SKU查询时：{list: [...], total: 100}
 * 2. 分页查询时：{list: [...], pagination: {page, size, total}}
 *
 * 通过智能的getTotal()方法自动判断使用哪种格式的总数字段，
 * 确保上层代码无需关心具体的数据格式差异。
 *
 * 使用示例：
 * <pre>
 * ErpStockListData data = apiResponse.getData();
 * List&lt;ErpStockBo&gt; stocks = data.getList();
 * Integer total = data.getTotal(); // 自动适配不同格式
 * </pre>
 *
 * <AUTHOR>
 * @since 1.6.0
 */
@Data
@Slf4j
@JsonIgnoreProperties(ignoreUnknown = true)
public class ErpStockListData {

    /**
     * 库存数据列表
     */
    private List<ErpStockBo> list;

    /**
     * 分页信息（可选，无SKUS参数时使用）
     */
    private PaginationInfo pagination;

    /**
     * 直接的总数字段（可选，有SKUS参数时使用）
     */
    private Integer total;

    /**
     * 获取总数，兼容两种格式
     */
    public Integer getTotal() {
        if (total != null) {
            // 直接total字段（SKUS模式）
            return total;
        }
        if (pagination != null && pagination.getTotal() != null) {
            // pagination.total字段（分页模式）
            return pagination.getTotal();
        }
        // 都没有时返回list长度作为fallback
        return list != null ? list.size() : 0;
    }

    /**
     * 设置总数，优先设置到直接total字段
     */
    public void setTotal(Integer total) {
        this.total = total;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PaginationInfo {
        private Integer page;
        private Integer size;
        private Integer total;
    }
}
