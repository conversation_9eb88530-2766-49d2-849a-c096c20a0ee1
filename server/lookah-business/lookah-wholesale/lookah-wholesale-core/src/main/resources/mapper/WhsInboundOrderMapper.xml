<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imhuso.wholesale.core.mapper.WhsInboundOrderMapper">

    <resultMap type="com.imhuso.wholesale.core.domain.vo.admin.WhsInboundOrderVo" id="WhsInboundOrderVoResult">
        <id property="id" column="id"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="warehouseName" column="warehouse_name"/>
        <result property="externalOrderNo" column="external_order_no"/>
        <result property="orderNo" column="order_no"/>
        <result property="status" column="status"/>
        <result property="statusCode" column="status_code"/>
        <result property="statusValue" column="status_value"/>
        <result property="statusText" column="status_text"/>
        <result property="expectedArrival" column="expected_arrival"/>
        <result property="actualArrival" column="actual_arrival"/>
        <result property="lastSyncTime" column="last_sync_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="totalQuantity" column="total_quantity"/>
        <result property="totalReceivedQuantity" column="total_received_quantity"/>
        <result property="totalInTransitQuantity" column="total_in_transit_quantity"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 根据产品ID分页查询相关的在途入库单 -->
    <select id="selectInboundOrdersByProductId" resultMap="WhsInboundOrderVoResult">
        SELECT
            o.id,
            o.warehouse_id,
            w.name AS warehouse_name,
            o.external_order_no,
            o.order_no,
            o.status,
            o.status_code,
            o.status AS status_value,
            o.status_text,
            o.expected_arrival,
            o.actual_arrival,
            o.last_sync_time,
            o.create_time,
            o.update_time,
            o.remark,
            agg.total_quantity,
            agg.total_received_quantity,
            agg.total_in_transit_quantity
        FROM whs_inbound_order o
        LEFT JOIN whs_warehouse w ON o.warehouse_id = w.id
        INNER JOIN (
            SELECT
                i.inbound_order_id,
                SUM(i.quantity) AS total_quantity,
                SUM(COALESCE(i.received_quantity, 0)) AS total_received_quantity,
                SUM(i.quantity - COALESCE(i.received_quantity, 0)) AS total_in_transit_quantity
            FROM whs_inbound_order_item i
            INNER JOIN whs_product_variant v ON i.variant_id = v.id
            WHERE v.product_id = #{productId}
            AND i.quantity > COALESCE(i.received_quantity, 0) -- 确保有未接收的数量
            GROUP BY i.inbound_order_id
            HAVING SUM(i.quantity - COALESCE(i.received_quantity, 0)) > 0
        ) agg ON o.id = agg.inbound_order_id
        WHERE o.status NOT IN (4, 10) -- 排除已上架和已作废状态，包含待入库、部分入库、已入库、部分上架
        AND o.del_flag = '0'
        <if test="ew != null and ew.customSqlSegment != null and ew.customSqlSegment != ''">
            ${ew.customSqlSegment}
        </if>
        ORDER BY o.expected_arrival DESC, o.create_time DESC
    </select>

    <!-- 根据变体ID分页查询相关的在途入库单 -->
    <select id="selectInboundOrdersByVariantId" resultMap="WhsInboundOrderVoResult">
        SELECT
            o.id,
            o.warehouse_id,
            w.name AS warehouse_name,
            o.external_order_no,
            o.order_no,
            o.status,
            o.status_code,
            o.status AS status_value,
            o.status_text,
            o.expected_arrival,
            o.actual_arrival,
            o.last_sync_time,
            o.create_time,
            o.update_time,
            o.remark,
            agg.total_quantity,
            agg.total_received_quantity,
            agg.total_in_transit_quantity
        FROM whs_inbound_order o
        LEFT JOIN whs_warehouse w ON o.warehouse_id = w.id
        INNER JOIN (
            SELECT
                i.inbound_order_id,
                SUM(i.quantity) AS total_quantity,
                SUM(COALESCE(i.received_quantity, 0)) AS total_received_quantity,
                SUM(i.quantity - COALESCE(i.received_quantity, 0)) AS total_in_transit_quantity
            FROM whs_inbound_order_item i
            WHERE i.variant_id = #{variantId}
            AND i.quantity > COALESCE(i.received_quantity, 0) -- 确保有未接收的数量
            GROUP BY i.inbound_order_id
            HAVING SUM(i.quantity - COALESCE(i.received_quantity, 0)) > 0
        ) agg ON o.id = agg.inbound_order_id
        WHERE o.status NOT IN (4, 10) -- 排除已上架和已作废状态，包含待入库、部分入库、已入库、部分上架
        AND o.del_flag = '0'
        <if test="ew != null and ew.customSqlSegment != null and ew.customSqlSegment != ''">
            ${ew.customSqlSegment}
        </if>
        ORDER BY o.expected_arrival DESC, o.create_time DESC
    </select>

</mapper>
