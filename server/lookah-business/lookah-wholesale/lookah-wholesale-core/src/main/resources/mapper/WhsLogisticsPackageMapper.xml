<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imhuso.wholesale.core.mapper.WhsLogisticsPackageMapper">

    <!-- 查询指定仓库未同步运费的包裹 -->
    <select id="selectUnsyncedPackagesByWarehouseId" resultType="com.imhuso.wholesale.core.domain.WhsLogisticsPackage">
        SELECT p.*
        FROM whs_logistics_package p
        LEFT JOIN whs_order_freight f ON p.id = f.package_id AND f.sync_status = '1'
        WHERE p.warehouse_id = #{warehouseId}
          AND p.external_order_no IS NOT NULL
          AND p.external_order_no != ''
          AND p.del_flag = '0'
          AND f.id IS NULL
        ORDER BY p.id ASC
    </select>



    <!-- 根据发货ID查询物流包裹列表（包含物流方式名称、仓库名称和渠道信息） -->
    <select id="selectPackagesByShipmentId" resultType="com.imhuso.wholesale.core.domain.vo.admin.WhsLogisticsPackageVo">
        SELECT p.*,
               m.method_name as logistics_method_name,
               m.channel_id as channel_id,
               m.method_code as method_code,
               m.provider_id as provider_id,
               p2.name as provider_name,
               w.name as warehouse_name
        FROM whs_logistics_package p
        LEFT JOIN whs_warehouse_logistics_method m ON p.logistics_method_id = m.id
        LEFT JOIN whs_warehouse w ON p.warehouse_id = w.id
        LEFT JOIN whs_overseas_warehouse_provider p2 ON m.provider_id = p2.id
        WHERE p.shipment_id = #{shipmentId}
          AND p.del_flag = '0'
        ORDER BY p.id ASC
    </select>

    <!-- 根据订单ID查询物流包裹列表（用于用户端展示） -->
    <select id="selectPackagesByOrderId" resultType="com.imhuso.wholesale.core.domain.vo.front.LogisticsPackageVo">
        SELECT p.id,
               p.tracking_number,
               p.carrier,
               p.package_status,
               p.shipped_date,
               p.expected_delivery,
               p.actual_delivery
        FROM whs_logistics_package p
        WHERE p.order_id = #{orderId}
          AND p.del_flag = '0'
        ORDER BY p.id ASC
    </select>

</mapper>
