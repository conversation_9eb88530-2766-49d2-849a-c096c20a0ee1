<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imhuso.wholesale.core.mapper.WhsOrderShipmentItemMapper">

    <resultMap type="com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentItemVo" id="WhsOrderShipmentItemResult">
        <id property="id" column="id"/>
        <result property="shipmentId" column="shipment_id"/>
        <result property="orderId" column="order_id"/>
        <result property="packageId" column="package_id"/>
        <result property="planItemId" column="plan_item_id"/>
        <result property="quantity" column="quantity"/>
        <result property="warehouseId" column="warehouse_id"/>
        <!-- 扩展字段 -->
        <result property="productName" column="product_name"/>
        <result property="skuCode" column="sku_code"/>
        <result property="packagingType" column="packaging_type"/>
        <result property="packagingQuantity" column="packaging_quantity"/>
    </resultMap>

    <!-- 根据发货ID查询发货项列表（包含产品名称、包装类型等扩展信息） -->
    <select id="selectItemsByShipmentId" resultMap="WhsOrderShipmentItemResult">
        SELECT i.id,
               i.shipment_id,
               i.order_id,
               i.package_id,
               i.plan_item_id,
               i.quantity,
               i.warehouse_id,
               sp.item_name AS product_name, 
               spi.sku_code, 
               spi.packaging_type, 
               spi.packaging_quantity 
        FROM whs_order_shipment_item i 
        LEFT JOIN whs_shipment_plan_item spi ON i.plan_item_id = spi.id 
        LEFT JOIN whs_product_variant spv ON spi.variant_id = spv.id 
        LEFT JOIN whs_product sp ON spv.product_id = sp.id 
        WHERE i.shipment_id = #{shipmentId} 
          AND i.del_flag = '0' 
        ORDER BY i.id
    </select>

</mapper>
