<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imhuso.wholesale.core.mapper.WhsOrderShipmentApprovalMapper">

    <resultMap id="WhsOrderShipmentApprovalVoResult" type="com.imhuso.wholesale.core.domain.vo.admin.WhsOrderShipmentApprovalVo">
        <result property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="applicantId" column="applicant_id"/>
        <result property="applicantName" column="applicant_name"/>
        <result property="applicationReason" column="application_reason"/>
        <result property="approvalStatus" column="approval_status"/>
        <result property="operationType" column="operation_type"/>
        <result property="operationSequence" column="operation_sequence"/>
        <result property="recordStatus" column="record_status"/>
        <result property="approverId" column="approver_id"/>
        <result property="approverName" column="approver_name"/>
        <result property="approvalTime" column="approval_time"/>
        <result property="approvalComment" column="approval_comment"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectVoPageWithOrderNo" resultMap="WhsOrderShipmentApprovalVoResult">
        SELECT 
            wsa.id,
            wsa.order_id,
            wo.internal_order_no as order_no,
            wsa.applicant_id,
            wsa.applicant_name,
            wsa.application_reason,
            wsa.approval_status,
            wsa.operation_type,
            wsa.operation_sequence,
            wsa.record_status,
            wsa.approver_id,
            wsa.approver_name,
            wsa.approval_time,
            wsa.approval_comment,
            wsa.remark,
            wsa.create_time,
            wsa.update_time
        FROM whs_order_shipment_approval wsa
        LEFT JOIN whs_order wo ON wsa.order_id = wo.id
        ${ew.customSqlSegment}
    </select>

    <select id="selectVoOneWithOrderNo" resultMap="WhsOrderShipmentApprovalVoResult">
        SELECT 
            wsa.id,
            wsa.order_id,
            wo.internal_order_no as order_no,
            wsa.applicant_id,
            wsa.applicant_name,
            wsa.application_reason,
            wsa.approval_status,
            wsa.operation_type,
            wsa.operation_sequence,
            wsa.record_status,
            wsa.approver_id,
            wsa.approver_name,
            wsa.approval_time,
            wsa.approval_comment,
            wsa.remark,
            wsa.create_time,
            wsa.update_time
        FROM whs_order_shipment_approval wsa
        LEFT JOIN whs_order wo ON wsa.order_id = wo.id
        ${ew.customSqlSegment}
    </select>

    <select id="selectVoListWithOrderNo" resultMap="WhsOrderShipmentApprovalVoResult">
        SELECT 
            wsa.id,
            wsa.order_id,
            wo.internal_order_no as order_no,
            wsa.applicant_id,
            wsa.applicant_name,
            wsa.application_reason,
            wsa.approval_status,
            wsa.operation_type,
            wsa.operation_sequence,
            wsa.record_status,
            wsa.approver_id,
            wsa.approver_name,
            wsa.approval_time,
            wsa.approval_comment,
            wsa.remark,
            wsa.create_time,
            wsa.update_time
        FROM whs_order_shipment_approval wsa
        LEFT JOIN whs_order wo ON wsa.order_id = wo.id
        ${ew.customSqlSegment}
    </select>

</mapper>