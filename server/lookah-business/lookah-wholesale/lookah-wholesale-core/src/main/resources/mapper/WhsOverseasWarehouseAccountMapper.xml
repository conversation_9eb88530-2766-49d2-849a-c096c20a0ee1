<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imhuso.wholesale.core.mapper.WhsOverseasWarehouseAccountMapper">

    <select id="selectByWarehouseId" parameterType="long" resultType="com.imhuso.wholesale.core.domain.WhsOverseasWarehouseAccount">
        SELECT a.id,
               a.name,
               a.code,
               a.provider_id,
               a.account_config,
               a.status,
               a.callback_url,
               a.support_signature_service,
               a.create_time,
               a.update_time,
               a.create_by,
               a.update_by,
               a.del_flag
        FROM whs_overseas_warehouse_account a
        INNER JOIN whs_warehouse w ON a.id = w.account_id
        WHERE w.id = #{warehouseId}
          AND w.del_flag = '0'
          AND a.del_flag = '0'
          LIMIT 1
    </select>

</mapper>