<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imhuso.wholesale.core.mapper.WhsWarehouseMapper">

    <!-- 原子性设置默认仓库 -->
    <update id="setDefaultWarehouseAtomic">
        UPDATE whs_warehouse 
        SET is_default = CASE WHEN id = #{warehouseId} THEN 'Y' ELSE 'N' END 
        WHERE del_flag = '0'
    </update>

</mapper>
