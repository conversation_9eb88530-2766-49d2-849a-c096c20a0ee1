# Lookah Whs 商城模块

## 📋 模块概述

Lookah Whs 是基于 Spring Boot 的电商平台后端服务，采用业务域驱动设计（DDD），按照电商业务领域进行模块化拆分。

## 🏗️ 架构设计

### 设计原则
- **业务域驱动**：按照电商业务领域划分模块
- **高内聚低耦合**：每个业务域独立完整
- **分层架构**：清晰的分层结构和职责划分
- **API分离**：前台和后台API完全分离

### 模块架构图
```
lookah-store/
├── 业务域模块 (Business Domain Modules)
│   ├── lookah-store-member/        # 会员管理域
│   ├── lookah-store-product/       # 商品管理域
│   ├── lookah-store-order/         # 订单管理域
│   ├── lookah-store-cart/          # 购物车域
│   ├── lookah-store-payment/       # 支付域
│   ├── lookah-store-promotion/     # 营销促销域
│   └── lookah-store-logistics/     # 物流域
├── 基础设施模块 (Infrastructure Modules)
│   └── lookah-store-core/          # 核心基础设施
├── API聚合模块 (API Aggregation Modules)
│   ├── lookah-store-api-front/     # 前台API聚合
│   └── lookah-store-api-admin/     # 后台API聚合
└── 启动器模块 (Starter Module)
    └── lookah-store-starter/       # 依赖聚合启动器
```

## 📦 模块详细设计

### 业务域模块

#### lookah-store-member（会员管理域）
```
lookah-store-member/
├── src/main/java/com/imhuso/store/member/
│   ├── domain/
│   │   ├── entity/                 # 实体类
│   │   │   ├── WhsMember.java
│   │   │   └── WhsMemberSocial.java
│   │   ├── vo/                     # 视图对象
│   │   │   ├── front/              # 前台VO
│   │   │   └── admin/              # 后台VO
│   │   └── bo/                     # 业务对象
│   ├── mapper/                     # 数据访问层
│   │   ├── WhsMemberMapper.java
│   │   └── WhsMemberSocialMapper.java
│   ├── service/                    # 业务逻辑层
│   │   ├── WhsMemberService.java
│   │   └── impl/
│   └── enums/                      # 枚举类
└── src/main/resources/
    └── mapper/                     # MyBatis映射文件
```

#### lookah-store-product（商品管理域）
```
lookah-store-product/
├── src/main/java/com/imhuso/store/product/
│   ├── domain/
│   │   ├── entity/
│   │   │   ├── WhsProduct.java
│   │   │   ├── WhsCategory.java
│   │   │   ├── WhsBrand.java
│   │   │   └── WhsProductSku.java
│   │   ├── vo/
│   │   │   ├── front/
│   │   │   └── admin/
│   │   └── bo/
│   ├── mapper/
│   ├── service/
│   └── enums/
└── src/main/resources/mapper/
```

#### lookah-store-order（订单管理域）
```
lookah-store-order/
├── src/main/java/com/imhuso/store/order/
│   ├── domain/
│   │   ├── entity/
│   │   │   ├── WhsOrder.java
│   │   │   ├── WhsOrderItem.java
│   │   │   └── WhsOrderLog.java
│   │   ├── vo/
│   │   └── bo/
│   ├── mapper/
│   ├── service/
│   └── enums/
└── src/main/resources/mapper/
```

#### lookah-store-cart（购物车域）
```
lookah-store-cart/
├── src/main/java/com/imhuso/store/cart/
│   ├── domain/
│   │   ├── entity/
│   │   │   └── WhsCart.java
│   │   ├── vo/
│   │   └── bo/
│   ├── mapper/
│   ├── service/
│   └── enums/
└── src/main/resources/mapper/
```

#### lookah-store-payment（支付域）
```
lookah-store-payment/
├── src/main/java/com/imhuso/store/payment/
│   ├── domain/
│   │   ├── entity/
│   │   │   ├── WhsPayment.java
│   │   │   └── WhsPaymentLog.java
│   │   ├── vo/
│   │   └── bo/
│   ├── mapper/
│   ├── service/
│   └── enums/
└── src/main/resources/mapper/
```

#### lookah-store-promotion（营销促销域）
```
lookah-store-promotion/
├── src/main/java/com/imhuso/store/promotion/
│   ├── domain/
│   │   ├── entity/
│   │   │   ├── WhsCoupon.java
│   │   │   ├── WhsPromotion.java
│   │   │   └── WhsMemberCoupon.java
│   │   ├── vo/
│   │   └── bo/
│   ├── mapper/
│   ├── service/
│   └── enums/
└── src/main/resources/mapper/
```

#### lookah-store-logistics（物流域）
```
lookah-store-logistics/
├── src/main/java/com/imhuso/store/logistics/
│   ├── domain/
│   │   ├── entity/
│   │   │   ├── WhsLogistics.java
│   │   │   └── WhsLogisticsTrack.java
│   │   ├── vo/
│   │   └── bo/
│   ├── mapper/
│   ├── service/
│   └── enums/
└── src/main/resources/mapper/
```

### 基础设施模块

#### lookah-store-core（核心基础设施）
```
lookah-store-core/
├── src/main/java/com/imhuso/store/core/
│   ├── config/                     # 配置类
│   │   ├── WhsWebConfig.java
│   │   └── WhsSecurityConfig.java
│   ├── constant/                   # 常量定义
│   │   └── WhsConstants.java
│   ├── exception/                  # 异常定义
│   │   └── WhsException.java
│   ├── utils/                      # 工具类
│   │   └── WhsUtils.java
│   └── base/                       # 基础类
│       ├── BaseEntity.java
│       ├── BaseService.java
│       └── BaseMapper.java
└── src/main/resources/
    └── META-INF/spring/
        └── org.springframework.boot.autoconfigure.AutoConfiguration.imports
```

### API聚合模块

#### lookah-store-api-front（前台API聚合）
```
lookah-store-api-front/
├── src/main/java/com/imhuso/store/front/
│   ├── controller/                 # 前台控制器
│   │   ├── auth/                   # 认证相关
│   │   │   ├── AuthController.java
│   │   │   └── SocialAuthController.java
│   │   ├── member/                 # 会员相关
│   │   │   └── MemberController.java
│   │   ├── product/                # 商品相关
│   │   │   ├── ProductController.java
│   │   │   └── CategoryController.java
│   │   ├── cart/                   # 购物车相关
│   │   │   └── CartController.java
│   │   ├── order/                  # 订单相关
│   │   │   └── OrderController.java
│   │   └── payment/                # 支付相关
│   │       └── PaymentController.java
│   └── config/                     # 前台配置
│       └── FrontWebConfig.java
└── src/main/resources/
    └── META-INF/spring/
        └── org.springframework.boot.autoconfigure.AutoConfiguration.imports
```

#### lookah-store-api-admin（后台API聚合）
```
lookah-store-api-admin/
├── src/main/java/com/imhuso/store/admin/
│   ├── controller/                 # 后台控制器
│   │   ├── member/                 # 会员管理
│   │   │   └── WhsMemberController.java
│   │   ├── product/                # 商品管理
│   │   │   ├── WhsProductController.java
│   │   │   ├── WhsCategoryController.java
│   │   │   └── WhsBrandController.java
│   │   ├── order/                  # 订单管理
│   │   │   └── WhsOrderController.java
│   │   ├── promotion/              # 营销管理
│   │   │   ├── WhsCouponController.java
│   │   │   └── WhsPromotionController.java
│   │   └── logistics/              # 物流管理
│   │       └── WhsLogisticsController.java
│   └── config/                     # 后台配置
│       └── AdminWebConfig.java
└── src/main/resources/
    └── META-INF/spring/
        └── org.springframework.boot.autoconfigure.AutoConfiguration.imports
```

## 🔧 包命名规范

### 基础包结构
```
com.imhuso.store.{domain}.{layer}.{function}
```

### 具体规范
- **业务域包**：`com.imhuso.store.{domain}`
  - member（会员）、product（商品）、order（订单）等
- **分层包**：`{domain}.{layer}`
  - domain（领域层）、mapper（数据层）、service（服务层）
- **功能包**：`{layer}.{function}`
  - domain.entity（实体）、domain.vo.front（前台VO）、domain.vo.admin（后台VO）

### 示例
```java
// 会员实体
com.imhuso.store.member.domain.entity.WhsMember

// 会员前台VO
com.imhuso.store.member.domain.vo.front.MemberVo

// 会员后台VO  
com.imhuso.store.member.domain.vo.admin.WhsMemberVo

// 会员服务
com.imhuso.store.member.service.WhsMemberService

// 前台会员控制器
com.imhuso.store.front.controller.member.MemberController

// 后台会员控制器
com.imhuso.store.admin.controller.member.WhsMemberController
```

## 🚀 模块依赖关系

### 依赖层次
```
lookah-store-starter
├── lookah-store-api-front
│   ├── lookah-store-member
│   ├── lookah-store-product
│   ├── lookah-store-order
│   ├── lookah-store-cart
│   ├── lookah-store-payment
│   └── lookah-store-core
└── lookah-store-api-admin
    ├── lookah-store-member
    ├── lookah-store-product
    ├── lookah-store-order
    ├── lookah-store-promotion
    ├── lookah-store-logistics
    └── lookah-store-core
```

### 依赖原则
1. **业务域模块**：只依赖 lookah-store-core，不互相依赖
2. **API模块**：依赖所需的业务域模块和 core 模块
3. **Core模块**：只依赖公共基础设施，不依赖业务域
4. **Starter模块**：聚合所有模块依赖

## 📋 开发规范

### 代码规范
- 遵循阿里巴巴Java开发规范
- 使用 Lombok 注解简化代码
- 统一使用 VO/BO 模式，避免 Request/Response 命名
- 前台包无 Whs 前缀，后台包有 Whs 前缀

### 数据访问规范
- 使用 MyBatis Plus 的 Wrapper 进行数据库操作
- 避免 XML/注解 SQL，优先使用 Wrapper
- Mapper 默认使用后台 VO 作为泛型参数

### 国际化规范
- Whs 模块必须使用 i18n 国际化（英文）
- 使用 `MessageUtils.message()` 进行消息翻译
- 国际化文件统一在 `lookah-app` 模块管理

### API规范
- 前台API前缀：`/api`
- 后台API前缀：`/api/admin/store`
- 控制器使用 JavaDoc 注释
- 重要操作添加 `@Log` 注解

## 🔄 迁移计划

### 第一阶段：创建新模块结构
1. 创建各业务域模块目录
2. 配置模块 pom.xml
3. 建立基础包结构

### 第二阶段：迁移现有代码
1. 将 lookah-store-core 中的业务代码迁移到对应业务域
2. 重构包名和类名
3. 更新依赖关系

### 第三阶段：优化和测试
1. 完善单元测试
2. 集成测试验证
3. 文档更新

## 📚 相关文档

- [项目整体架构](../../README.md)
- [开发规范](../../../docs/development-guide.md)
- [API文档](../../../docs/api-guide.md)

---

> 💡 **设计理念**：通过业务域驱动的模块化设计，实现高内聚低耦合的架构，提升代码的可维护性和可扩展性。
