package com.imhuso.mall.core.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imhuso.common.mybatis.core.mapper.BaseMapperPlus;
import com.imhuso.mall.core.enums.MallMemberVerificationStatus;
import com.imhuso.mall.core.domain.MallMember;
import com.imhuso.mall.core.domain.vo.MallMemberVo;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;

/**
 * 商城会员Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Mapper
public interface MallMemberMapper extends BaseMapperPlus<MallMember, MallMemberVo> {

    /**
     * 根据邮箱查询会员
     *
     * @param email 邮箱地址
     * @return 会员信息
     */
    default MallMember selectByEmail(String email) {
        return selectOne(Wrappers.<MallMember>lambdaQuery()
            .eq(MallMember::getEmail, email));
    }

    /**
     * 根据手机号查询会员
     *
     * @param phone 手机号
     * @return 会员信息
     */
    default MallMember selectByPhone(String phone) {
        return selectOne(Wrappers.<MallMember>lambdaQuery()
            .eq(MallMember::getPhone, phone));
    }

    /**
     * 更新最后登录信息
     *
     * @param memberId      会员ID
     * @param loginTime     登录时间
     * @param loginIp       登录IP
     * @param loginLocation 登录地点
     */
    default void updateLastLoginInfo(Long memberId, LocalDateTime loginTime, String loginIp, String loginLocation) {
        LambdaUpdateWrapper<MallMember> updateWrapper = Wrappers.<MallMember>lambdaUpdate()
            .eq(MallMember::getMemberId, memberId)
            .set(MallMember::getLastLoginTime, loginTime)
            .set(MallMember::getLastLoginIp, loginIp)
            .set(MallMember::getLastLoginLocation, loginLocation);
        update(null, updateWrapper);
    }

    /**
     * 增加登录次数
     *
     * @param memberId 会员ID
     */
    default void incrementLoginCount(Long memberId) {
        LambdaUpdateWrapper<MallMember> updateWrapper = Wrappers.<MallMember>lambdaUpdate()
            .eq(MallMember::getMemberId, memberId)
            .setSql("login_count = login_count + 1");
        update(null, updateWrapper);
    }

    /**
     * 更新失败登录信息
     *
     * @param memberId       会员ID
     * @param failedCount    失败次数
     * @param lastFailedTime 最后失败时间
     */
    default void updateFailedLoginInfo(Long memberId, Integer failedCount, LocalDateTime lastFailedTime) {
        LambdaUpdateWrapper<MallMember> updateWrapper = Wrappers.<MallMember>lambdaUpdate()
            .eq(MallMember::getMemberId, memberId)
            .set(MallMember::getFailedLoginCount, failedCount)
            .set(MallMember::getLastFailedLoginTime, lastFailedTime);
        update(null, updateWrapper);
    }

    /**
     * 锁定账户
     *
     * @param memberId    会员ID
     * @param lockedUntil 锁定到期时间
     */
    default void lockAccount(Long memberId, LocalDateTime lockedUntil) {
        LambdaUpdateWrapper<MallMember> updateWrapper = Wrappers.<MallMember>lambdaUpdate()
            .eq(MallMember::getMemberId, memberId)
            .set(MallMember::getLockedUntil, lockedUntil);
        update(null, updateWrapper);
    }

    /**
     * 解锁账户
     *
     * @param memberId 会员ID
     */
    default void unlockAccount(Long memberId) {
        LambdaUpdateWrapper<MallMember> updateWrapper = Wrappers.<MallMember>lambdaUpdate()
            .eq(MallMember::getMemberId, memberId)
            .set(MallMember::getLockedUntil, null);
        update(null, updateWrapper);
    }

    /**
     * 重置失败登录次数
     *
     * @param memberId 会员ID
     */
    default void resetFailedLoginCount(Long memberId) {
        LambdaUpdateWrapper<MallMember> updateWrapper = Wrappers.<MallMember>lambdaUpdate()
            .eq(MallMember::getMemberId, memberId)
            .set(MallMember::getFailedLoginCount, 0)
            .set(MallMember::getLastFailedLoginTime, null);
        update(null, updateWrapper);
    }

    /**
     * 验证邮箱
     *
     * @param memberId 会员ID
     */
    default void verifyEmail(Long memberId) {
        LambdaUpdateWrapper<MallMember> updateWrapper = Wrappers.<MallMember>lambdaUpdate()
            .eq(MallMember::getMemberId, memberId)
            .set(MallMember::getEmailVerified, MallMemberVerificationStatus.VERIFIED.getCode());
        update(null, updateWrapper);
    }

    /**
     * 验证手机号
     *
     * @param memberId 会员ID
     */
    default void verifyPhone(Long memberId) {
        LambdaUpdateWrapper<MallMember> updateWrapper = Wrappers.<MallMember>lambdaUpdate()
            .eq(MallMember::getMemberId, memberId)
            .set(MallMember::getPhoneVerified, MallMemberVerificationStatus.VERIFIED.getCode());
        update(null, updateWrapper);
    }
}
