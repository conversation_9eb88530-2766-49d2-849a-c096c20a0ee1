package com.imhuso.mall.core.service.impl;

import cn.dev33.satoken.stp.parameter.SaLoginParameter;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.imhuso.common.client.domain.ClientVo;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.core.utils.ValidatorUtils;
import com.imhuso.common.json.utils.JsonUtils;
import com.imhuso.common.satoken.utils.LoginHelper;
import com.imhuso.mall.core.constant.MallConstants;
import com.imhuso.mall.core.domain.MallMember;
import com.imhuso.mall.core.domain.bo.MallLoginBo;
import com.imhuso.mall.core.domain.vo.MallLoginVo;
import com.imhuso.mall.core.domain.vo.MallMemberInfoVo;
import com.imhuso.mall.core.exception.MallException;
import com.imhuso.mall.core.model.MallLoginUser;
import com.imhuso.mall.core.satoken.MallLoginHelper;
import com.imhuso.mall.core.satoken.utils.StpMallUtil;
import com.imhuso.mall.core.service.IMallAuthStrategy;
import com.imhuso.mall.core.service.IMallMemberService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 商城密码认证策略
 *
 * <AUTHOR>
 */
@Slf4j
@Service("password" + IMallAuthStrategy.BASE_NAME)
@RequiredArgsConstructor
public class MallPasswordAuthStrategy implements IMallAuthStrategy {

    private final IMallMemberService memberService;

    @Override
    public MallLoginVo login(String body, ClientVo client) {
        MallLoginBo loginBody = JsonUtils.parseObject(body, MallLoginBo.class);
        ValidatorUtils.validate(loginBody);

        String email = loginBody.getEmail();
        String password = loginBody.getPassword();

        // 获取会员信息
        MallMember member = memberService.selectByEmail(email);
        if (ObjectUtil.isNull(member)) {
            throw new MallException(MessageUtils.message("mall.auth.invalid.email.or.password"));
        }

        // 检查账户状态
        if (!BusinessConstants.NORMAL.equals(member.getStatus())) {
            throw new MallException(MessageUtils.message("mall.auth.account.disabled"));
        }

        // 检查账户锁定
        if (member.isLocked()) {
            throw new MallException(MessageUtils.message("mall.auth.account.locked"));
        }

        // 验证密码
        if (!BCrypt.checkpw(password, member.getPassword())) {
            throw new MallException(MessageUtils.message("mall.auth.invalid.email.or.password"));
        }

        // 更新登录信息
        memberService.updateLastLoginInfo(member.getMemberId(), com.imhuso.common.core.utils.ServletUtils.getClientIP(), "");
        memberService.incrementLoginCount(member.getMemberId());

        // 重置失败登录次数
        if (member.getFailedLoginCount() > 0) {
            memberService.resetFailedLoginCount(member.getMemberId());
        }

        // 构建登录用户
        MallLoginUser loginUser = buildLoginUser(member);

        // 设置客户端信息
        if (ObjectUtil.isNotNull(client)) {
            loginUser.setClientId(client.getClientId());
            loginUser.setClientType(client.getClientType());
        }

        // 生成token
        SaLoginParameter model = new SaLoginParameter();
        if (ObjectUtil.isNotNull(client)) {
            model.setTimeout(client.getTimeout());
            model.setActiveTimeout(client.getActiveTimeout());
        }
        model.setExtra(LoginHelper.CLIENT_ID, loginBody.getClientId());

        // 登录
        MallLoginHelper.login(loginUser, model);

        // 记录成功登录日志
        String sessionId = MallLoginHelper.getTokenSession().getId();
        String token = MallLoginHelper.getToken();
        memberService.recordSuccessLogin(member.getMemberId(), member.getEmail(), MallConstants.Module.NAME, loginBody.getClientId(), sessionId, token);

        // 构建响应
        MallLoginVo loginVo = buildAuthResponse(member, StpMallUtil.getTokenValue());

        log.info("商城用户登录成功: {}, 客户端ID: {}", email, loginBody.getClientId());
        return loginVo;
    }

    /**
     * 构建登录用户
     */
    private MallLoginUser buildLoginUser(MallMember member) {
        MallLoginUser loginUser = new MallLoginUser();
        loginUser.setUserId(member.getMemberId());
        loginUser.setUsername(member.getEmail());
        loginUser.setEmail(member.getEmail());
        loginUser.setFullName(member.getFullName());
        loginUser.setAvatar(member.getAvatar());
        return loginUser;
    }

    /**
     * 构建认证响应
     */
    private MallLoginVo buildAuthResponse(MallMember member, String accessToken) {
        MallLoginVo response = new MallLoginVo();
        response.setAccessToken(accessToken);
        response.setExpiresIn(StpMallUtil.getTokenTimeout());
        response.setTokenType("Bearer");

        // 构建用户信息
        MallMemberInfoVo memberInfo = new MallMemberInfoVo();
        memberInfo.setMemberId(member.getMemberId());
        memberInfo.setEmail(member.getEmail());
        memberInfo.setFullName(member.getFullName());
        memberInfo.setFirstName(member.getFirstName());
        memberInfo.setLastName(member.getLastName());
        memberInfo.setPhone(member.getPhone());
        memberInfo.setAvatar(member.getAvatar());
        memberInfo.setGender(member.getGender());
        memberInfo.setStatus(member.getStatus());
        memberInfo.setEmailVerified(member.getEmailVerified());
        memberInfo.setPhoneVerified(member.getPhoneVerified());
        memberInfo.setRegisterSource(member.getRegisterSource());
        memberInfo.setLastLoginTime(member.getLastLoginTime());
        memberInfo.setLoginCount(member.getLoginCount());

        response.setMemberInfo(memberInfo);
        return response;
    }
}
