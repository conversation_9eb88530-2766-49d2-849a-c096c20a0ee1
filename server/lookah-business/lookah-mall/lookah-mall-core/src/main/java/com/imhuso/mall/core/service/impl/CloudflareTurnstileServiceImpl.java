package com.imhuso.mall.core.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.imhuso.common.core.utils.EnvironmentUtils;
import com.imhuso.common.core.utils.ServletUtils;
import com.imhuso.mall.core.config.CloudflareTurnstileProperties;
import com.imhuso.mall.core.service.ICloudflareService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Cloudflare Turnstile 验证码服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CloudflareTurnstileServiceImpl implements ICloudflareService {

    private final CloudflareTurnstileProperties turnstileProperties;

    @Override
    public boolean verifyTurnstile(String token) {
        // 未启用时跳过验证
        if (!turnstileProperties.isEnabled()) {
            log.debug("Turnstile验证码已禁用，跳过验证");
            return true;
        }

        // 开发环境跳过验证
        if (EnvironmentUtils.isDevelopmentEnvironment() && turnstileProperties.isSkipInDev()) {
            log.debug("开发环境跳过Turnstile验证");
            return true;
        }

        // 参数校验
        if (StrUtil.isBlank(token)) {
            log.warn("Turnstile验证码token为空");
            return false;
        }

        // 配置校验
        if (!isConfigured()) {
            log.error("Turnstile配置无效");
            return false;
        }

        return performVerification(token);
    }

    /**
     * 执行Turnstile验证
     *
     * @param token 验证码token
     * @return 验证是否成功
     */
    private boolean performVerification(String token) {
        Map<String, Object> params = new HashMap<>();
        params.put("secret", turnstileProperties.getSecretKey());
        params.put("response", token);

        String clientIp = ServletUtils.getClientIP();
        if (StrUtil.isNotBlank(clientIp)) {
            params.put("remoteip", clientIp);
        }

        try (HttpResponse response = HttpRequest.post(turnstileProperties.getVerifyUrl())
                .form(params)
                .timeout(turnstileProperties.getConnectTimeout())
                .execute()) {

            if (!response.isOk()) {
                log.warn("Turnstile验证请求失败: HTTP {}", response.getStatus());
                return false;
            }

            JSONObject result = JSONUtil.parseObj(response.body());
            boolean success = result.getBool("success", false);

            if (!success) {
                log.warn("Turnstile验证失败: {}", result.get("error-codes"));
            }

            return success;

        } catch (Exception e) {
            log.error("Turnstile验证异常: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public String getSiteKey() {
        return turnstileProperties.getSiteKey();
    }

    @Override
    public boolean isEnabled() {
        return turnstileProperties.isEnabled();
    }

    @Override
    public boolean isConfigured() {
        return turnstileProperties.isEnabled()
                && StrUtil.isNotBlank(turnstileProperties.getSecretKey())
                && StrUtil.isNotBlank(turnstileProperties.getSiteKey());
    }
}
