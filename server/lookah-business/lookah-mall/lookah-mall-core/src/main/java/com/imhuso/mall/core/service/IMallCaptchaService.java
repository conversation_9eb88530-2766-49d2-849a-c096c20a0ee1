package com.imhuso.mall.core.service;

import com.imhuso.mall.core.domain.vo.MallCaptchaConfigVo;

/**
 * 商城验证码Service接口
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface IMallCaptchaService {

    /**
     * 生成验证码配置
     *
     * @return 验证码配置信息
     */
    MallCaptchaConfigVo generateCaptcha();

    /**
     * 验证验证码
     *
     * @param type 验证码类型（turnstile 或其他类型）
     * @param code 用户输入的验证码
     * @return 验证结果
     */
    boolean validateCaptcha(String type, String code);

    /**
     * 发送邮箱验证码
     *
     * @param email 邮箱地址
     * @param type  验证码类型（register注册 login登录 reset重置密码）
     * @return 发送结果
     */
    boolean sendEmailCode(String email, String type);

    /**
     * 验证邮箱验证码
     *
     * @param email 邮箱地址
     * @param code  验证码
     * @param type  验证码类型
     * @return 验证结果
     */
    boolean validateEmailCode(String email, String code, String type);


    /**
     * 检查验证码发送频率限制
     *
     * @param target 目标（邮箱或手机号）
     * @param type   验证码类型
     * @return 是否可以发送
     */
    boolean checkSendLimit(String target, String type);

    /**
     * 清理过期验证码
     *
     * @return 清理数量
     */
    int cleanExpiredCodes();


}
