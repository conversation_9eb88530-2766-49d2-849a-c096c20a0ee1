package com.imhuso.mall.core.service;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.mall.core.domain.MallMember;
import com.imhuso.mall.core.domain.bo.MallMemberBo;
import com.imhuso.mall.core.domain.bo.MallLoginBo;
import com.imhuso.mall.core.domain.bo.MallRegisterBo;
import com.imhuso.mall.core.domain.bo.MallChangePasswordBo;
import com.imhuso.mall.core.domain.vo.MallLoginVo;
import com.imhuso.mall.core.domain.vo.MallMemberVo;

import java.util.Collection;
import java.util.List;

/**
 * 商城会员Service接口
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface IMallMemberService {

    /**
     * 查询商城会员
     */
    MallMemberVo queryById(Long memberId);

    /**
     * 查询商城会员列表
     */
    TableDataInfo<MallMemberVo> queryPageList(MallMemberBo bo, PageQuery pageQuery);

    /**
     * 查询商城会员列表
     */
    List<MallMemberVo> queryList(MallMemberBo bo);

    /**
     * 新增商城会员
     */
    Boolean insertByBo(MallMemberBo bo);

    /**
     * 修改商城会员
     */
    Boolean updateByBo(MallMemberBo bo);

    /**
     * 校验并批量删除商城会员信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据邮箱查询会员
     */
    MallMember selectByEmail(String email);

    /**
     * 根据手机号查询会员
     */
    MallMember selectByPhone(String phone);

    /**
     * 检查邮箱是否唯一
     */
    boolean checkEmailUnique(String email, Long memberId);

    /**
     * 检查手机号是否唯一
     */
    boolean checkPhoneUnique(String phone, Long memberId);

    /**
     * 用户注册
     */
    MallLoginVo register(MallRegisterBo request);

    /**
     * 用户登录
     */
    MallLoginVo login(MallLoginBo request);

    /**
     * 用户退出登录
     */
    void logout();

    /**
     * 更新最后登录信息
     */
    void updateLastLoginInfo(Long memberId, String loginIp, String loginLocation);

    /**
     * 增加登录次数
     */
    void incrementLoginCount(Long memberId);

    /**
     * 更新失败登录信息
     */
    void updateFailedLoginInfo(Long memberId, Integer failedCount);

    /**
     * 锁定账户
     */
    void lockAccount(Long memberId, Integer lockMinutes);

    /**
     * 解锁账户
     */
    void unlockAccount(Long memberId);

    /**
     * 重置失败登录次数
     */
    void resetFailedLoginCount(Long memberId);

    /**
     * 验证邮箱
     */
    void verifyEmail(Long memberId);

    /**
     * 通过验证码验证邮箱
     */
    void verifyEmailWithCode(Long memberId, String code);

    /**
     * 检查账户是否被锁定
     */
    boolean isAccountLocked(Long memberId);

    /**
     * 获取失败登录次数
     */
    int getFailedLoginCount(String email, String ipAddress);

    /**
     * 修改密码
     */
    void changePassword(Long memberId, MallChangePasswordBo bo);

    /**
     * 更新用户头像
     */
    void updateAvatar(Long memberId, String avatarUrl);

    /**
     * 注销账户
     */
    void deactivateAccount(Long memberId, String password);

    /**
     * 记录成功登录
     */
    void recordSuccessLogin(Long memberId, String email, String module, String clientId, String sessionId, String token);
}
