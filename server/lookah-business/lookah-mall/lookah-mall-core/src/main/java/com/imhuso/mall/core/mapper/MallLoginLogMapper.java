package com.imhuso.mall.core.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.mybatis.core.mapper.BaseMapperPlus;
import com.imhuso.mall.core.constant.MallLoginStatusConstants;
import com.imhuso.mall.core.domain.MallLoginLog;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 商城会员登录日志Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Mapper
public interface MallLoginLogMapper extends BaseMapperPlus<MallLoginLog, MallLoginLog> {

    /**
     * 根据会员ID查询登录日志
     *
     * @param memberId 会员ID
     * @param limit 限制条数
     * @return 登录日志列表
     */
    default List<MallLoginLog> selectByMemberId(Long memberId, Integer limit) {
        LambdaQueryWrapper<MallLoginLog> queryWrapper = Wrappers.<MallLoginLog>lambdaQuery()
            .eq(MallLoginLog::getMemberId, memberId)
            .orderByDesc(MallLoginLog::getLoginTime);

        if (limit != null && limit > 0) {
            Page<MallLoginLog> page = new Page<>(1, limit);
            return selectPage(page, queryWrapper).getRecords();
        }
        return selectList(queryWrapper);
    }

    /**
     * 根据邮箱查询登录日志
     *
     * @param email 邮箱地址
     * @param limit 限制条数
     * @return 登录日志列表
     */
    default List<MallLoginLog> selectByEmail(String email, Integer limit) {
        LambdaQueryWrapper<MallLoginLog> queryWrapper = Wrappers.<MallLoginLog>lambdaQuery()
            .eq(MallLoginLog::getEmail, email)
            .orderByDesc(MallLoginLog::getLoginTime);

        if (limit != null && limit > 0) {
            Page<MallLoginLog> page = new Page<>(1, limit);
            return selectPage(page, queryWrapper).getRecords();
        }
        return selectList(queryWrapper);
    }

    /**
     * 根据IP地址查询登录日志
     *
     * @param ipAddress IP地址
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 登录日志列表
     */
    default List<MallLoginLog> selectByIpAddress(String ipAddress, LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<MallLoginLog> queryWrapper = Wrappers.<MallLoginLog>lambdaQuery()
            .eq(MallLoginLog::getIpAddress, ipAddress)
            .ge(startTime != null, MallLoginLog::getLoginTime, startTime)
            .le(endTime != null, MallLoginLog::getLoginTime, endTime)
            .orderByDesc(MallLoginLog::getLoginTime);
        return selectList(queryWrapper);
    }

    /**
     * 统计指定时间段内的失败登录次数
     *
     * @param email 邮箱地址
     * @param ipAddress IP地址
     * @param startTime 开始时间
     * @return 失败次数
     */
    default int countFailedLoginAttempts(String email, String ipAddress, LocalDateTime startTime) {
        LambdaQueryWrapper<MallLoginLog> queryWrapper = Wrappers.<MallLoginLog>lambdaQuery()
            .eq(email != null, MallLoginLog::getEmail, email)
            .eq(ipAddress != null, MallLoginLog::getIpAddress, ipAddress)
            .eq(MallLoginLog::getStatus, MallLoginStatusConstants.FAILED)
            .ge(startTime != null, MallLoginLog::getLoginTime, startTime);
        return Math.toIntExact(selectCount(queryWrapper));
    }

    /**
     * 统计指定IP在时间段内的登录次数
     *
     * @param ipAddress IP地址
     * @param startTime 开始时间
     * @return 登录次数
     */
    default int countLoginAttemptsByIp(String ipAddress, LocalDateTime startTime) {
        LambdaQueryWrapper<MallLoginLog> queryWrapper = Wrappers.<MallLoginLog>lambdaQuery()
            .eq(MallLoginLog::getIpAddress, ipAddress)
            .ge(startTime != null, MallLoginLog::getLoginTime, startTime);
        return Math.toIntExact(selectCount(queryWrapper));
    }

    /**
     * 更新退出时间和会话持续时间
     *
     * @param sessionId       会话ID
     * @param logoutTime      退出时间
     * @param sessionDuration 会话持续时间
     */
    default void updateLogoutInfo(String sessionId, LocalDateTime logoutTime, Integer sessionDuration) {
        LambdaUpdateWrapper<MallLoginLog> updateWrapper = Wrappers.<MallLoginLog>lambdaUpdate()
            .eq(MallLoginLog::getSessionId, sessionId)
            .set(MallLoginLog::getLogoutTime, logoutTime)
            .set(MallLoginLog::getSessionDuration, sessionDuration);
        update(null, updateWrapper);
    }

    /**
     * 根据Token查询登录日志
     *
     * @param token 登录Token
     * @return 登录日志
     */
    default MallLoginLog selectByToken(String token) {
        return selectOne(Wrappers.<MallLoginLog>lambdaQuery()
            .eq(MallLoginLog::getToken, token));
    }

    /**
     * 根据会话ID查询登录日志
     *
     * @param sessionId 会话ID
     * @return 登录日志
     */
    default MallLoginLog selectBySessionId(String sessionId) {
        return selectOne(Wrappers.<MallLoginLog>lambdaQuery()
            .eq(MallLoginLog::getSessionId, sessionId));
    }

    /**
     * 清理过期的登录日志
     *
     * @param beforeTime 清理此时间之前的日志
     * @return 清理的记录数
     */
    default int cleanExpiredLogs(LocalDateTime beforeTime) {
        LambdaQueryWrapper<MallLoginLog> queryWrapper = Wrappers.<MallLoginLog>lambdaQuery()
            .lt(MallLoginLog::getLoginTime, beforeTime);
        return delete(queryWrapper);
    }

    /**
     * 查询最近的成功登录记录
     *
     * @param memberId 会员ID
     * @return 最近的成功登录记录
     */
    default MallLoginLog selectLastSuccessLogin(Long memberId) {
        return selectOne(Wrappers.<MallLoginLog>lambdaQuery()
            .eq(MallLoginLog::getMemberId, memberId)
            .eq(MallLoginLog::getStatus, MallLoginStatusConstants.SUCCESS)
            .orderByDesc(MallLoginLog::getLoginTime)
            .last("LIMIT 1"));
    }

    /**
     * 查询指定时间段内的登录统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 登录统计数据
     */
    default List<MallLoginLog> selectLoginStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<MallLoginLog> queryWrapper = Wrappers.<MallLoginLog>lambdaQuery()
            .ge(startTime != null, MallLoginLog::getLoginTime, startTime)
            .le(endTime != null, MallLoginLog::getLoginTime, endTime)
            .orderByDesc(MallLoginLog::getLoginTime);
        return selectList(queryWrapper);
    }
}
