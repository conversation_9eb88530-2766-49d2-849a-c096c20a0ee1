package com.imhuso.mall.core.domain.bo;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 商城会员业务对象 MallMemberBo
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = com.imhuso.mall.core.domain.MallMember.class, reverseConvertGenerate = false)
public class MallMemberBo extends BaseEntity {

    /**
     * 会员ID
     */
    @NotNull(message = "{mall.validation.member.id.required}", groups = { EditGroup.class })
    private Long memberId;

    /**
     * 邮箱地址
     */
    @NotBlank(message = "{mall.validation.email.required}", groups = { AddGroup.class, EditGroup.class })
    @Email(message = "{mall.validation.email.invalid}")
    @Size(max = 100, message = "{mall.validation.email.max.length}")
    private String email;

    /**
     * 密码（BCrypt加密）
     */
    @NotBlank(message = "{mall.validation.password.required}", groups = { AddGroup.class })
    @Size(min = 6, max = 20, message = "{mall.validation.password.length}", groups = { AddGroup.class })
    private String password;

    /**
     * 确认密码
     */
    @NotBlank(message = "{mall.validation.confirm.password.required}", groups = { AddGroup.class })
    private String confirmPassword;

    /**
     * 全名
     */
    @Size(max = 100, message = "{mall.validation.full.name.max.length}")
    private String fullName;

    /**
     * 名字
     */
    @Size(max = 50, message = "{mall.validation.first.name.max.length}")
    private String firstName;

    /**
     * 姓氏
     */
    @Size(max = 50, message = "{mall.validation.last.name.max.length}")
    private String lastName;

    /**
     * 手机号码（可选）
     */
    private String phone;

    /**
     * 头像URL
     */
    @Size(max = 200, message = "{mall.validation.avatar.max.length}")
    private String avatar;

    /**
     * 性别（0未知 1男 2女）
     */
    @Pattern(regexp = "^[012]$", message = "{mall.validation.gender.invalid}")
    private String gender;

    /**
     * 生日
     */
    @Past(message = "{mall.validation.birthday.past}")
    private LocalDate birthday;

    /**
     * 账户状态（0停用 1正常 2待验证）
     */
    @Pattern(regexp = "^[012]$", message = "{mall.validation.status.invalid}")
    private String status;

    /**
     * 邮箱验证状态（0未验证 1已验证）
     */
    @Pattern(regexp = "^[01]$", message = "{mall.validation.email.verified.invalid}")
    private String emailVerified;

    /**
     * 手机验证状态（0未验证 1已验证）
     */
    @Pattern(regexp = "^[01]$", message = "{mall.validation.phone.verified.invalid}")
    private String phoneVerified;

    /**
     * 注册来源（email邮箱注册 google谷歌登录 facebook脸书登录）
     */
    @Pattern(regexp = "^(email|google|facebook|apple|wechat|qq)$", message = "{mall.validation.register.source.invalid}")
    private String registerSource;

    /**
     * 注册IP地址
     */
    private String registerIp;

    /**
     * 注册地点
     */
    private String registerLocation;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 最后登录地点
     */
    private String lastLoginLocation;

    /**
     * 登录次数
     */
    @Min(value = 0, message = "{mall.validation.login.count.min}")
    private Integer loginCount;

    /**
     * 连续失败登录次数
     */
    @Min(value = 0, message = "{mall.validation.failed.login.count.min}")
    private Integer failedLoginCount;

    /**
     * 最后失败登录时间
     */
    private LocalDateTime lastFailedLoginTime;

    /**
     * 账户锁定到期时间
     */
    private LocalDateTime lockedUntil;

    /**
     * 乐观锁版本号
     */
    private Integer version;
}
