package com.imhuso.mall.core.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 商城会员信息Vo
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
public class MallMemberInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 邮箱地址
     */
    private String email;

    /**
     * 全名
     */
    private String fullName;

    /**
     * 名字
     */
    private String firstName;

    /**
     * 姓氏
     */
    private String lastName;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 性别（0未知 1男 2女）
     */
    private String gender;

    /**
     * 账户状态（0停用 1正常 2待验证）
     */
    private String status;

    /**
     * 邮箱验证状态（0未验证 1已验证）
     */
    private String emailVerified;

    /**
     * 手机验证状态（0未验证 1已验证）
     */
    private String phoneVerified;

    /**
     * 注册来源
     */
    private String registerSource;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 登录次数
     */
    private Integer loginCount;
}
