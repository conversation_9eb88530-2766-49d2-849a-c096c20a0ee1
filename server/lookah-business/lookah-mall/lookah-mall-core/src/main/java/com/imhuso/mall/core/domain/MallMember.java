package com.imhuso.mall.core.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 商城会员实体类
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_member")
public class MallMember extends BaseEntity {

    /**
     * 会员ID
     */
    @TableId(value = "member_id", type = IdType.ASSIGN_ID)
    private Long memberId;

    /**
     * 邮箱地址
     */
    private String email;

    /**
     * 密码（BCrypt加密）
     */
    @TableField(insertStrategy = FieldStrategy.NOT_EMPTY, updateStrategy = FieldStrategy.NOT_EMPTY, whereStrategy = FieldStrategy.NOT_EMPTY)
    private String password;

    /**
     * 全名
     */
    private String fullName;

    /**
     * 名字
     */
    private String firstName;

    /**
     * 姓氏
     */
    private String lastName;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 性别（0未知 1男 2女）
     */
    private String gender;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 账户状态（0停用 1正常 2待验证）
     */
    private String status;

    /**
     * 邮箱验证状态（0未验证 1已验证）
     */
    private String emailVerified;

    /**
     * 手机验证状态（0未验证 1已验证）
     */
    private String phoneVerified;

    /**
     * 注册来源（email邮箱注册 google谷歌登录 facebook脸书登录）
     */
    private String registerSource;

    /**
     * 注册IP地址
     */
    private String registerIp;

    /**
     * 注册地点
     */
    private String registerLocation;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 最后登录地点
     */
    private String lastLoginLocation;

    /**
     * 登录次数
     */
    private Integer loginCount;

    /**
     * 连续失败登录次数
     */
    private Integer failedLoginCount;

    /**
     * 最后失败登录时间
     */
    private LocalDateTime lastFailedLoginTime;

    /**
     * 账户锁定到期时间
     */
    private LocalDateTime lockedUntil;

    /**
     * 乐观锁版本号
     */
    @Version
    private Integer version;

    /**
     * 检查账户是否被锁定
     */
    public boolean isLocked() {
        return lockedUntil != null && lockedUntil.isAfter(LocalDateTime.now());
    }
}
