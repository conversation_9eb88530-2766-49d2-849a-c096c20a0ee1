package com.imhuso.mall.core.service;

import com.imhuso.common.client.domain.ClientVo;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.core.utils.SpringUtils;
import com.imhuso.mall.core.domain.vo.MallLoginVo;
import com.imhuso.mall.core.exception.MallException;

/**
 * 商城授权策略接口
 *
 * <AUTHOR>
 */
public interface IMallAuthStrategy {

    String BASE_NAME = "MallAuthStrategy";

    /**
     * 登录
     *
     * @param body      登录对象
     * @param client    客户端配置信息
     * @param grantType 授权类型
     * @return 登录验证信息
     */
    static MallLoginVo login(String body, ClientVo client, String grantType) {
        // 授权类型和客户端id
        String beanName = grantType + BASE_NAME;
        if (!SpringUtils.containsBean(beanName)) {
            throw new MallException(MessageUtils.message("mall.auth.grant.type.invalid"));
        }
        IMallAuthStrategy instance = SpringUtils.getBean(beanName);
        return instance.login(body, client);
    }

    /**
     * 登录
     *
     * @param body   登录对象
     * @param client 客户端配置信息
     * @return 登录验证信息
     */
    MallLoginVo login(String body, ClientVo client);

}
