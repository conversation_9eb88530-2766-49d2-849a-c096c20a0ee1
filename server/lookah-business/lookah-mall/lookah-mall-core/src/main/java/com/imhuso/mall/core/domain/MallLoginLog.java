package com.imhuso.mall.core.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.mall.core.constant.MallLoginStatusConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 商城会员登录日志实体类
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_login_log")
public class MallLoginLog extends BaseEntity {

    /**
     * 日志ID
     */
    @TableId(value = "log_id", type = IdType.ASSIGN_ID)
    private Long logId;

    /**
     * 会员ID（登录失败时可能为空）
     */
    private Long memberId;

    /**
     * 登录类型（password密码登录 google谷歌登录 facebook脸书登录）
     */
    private String loginType;

    /**
     * 登录邮箱
     */
    private String email;

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 设备类型（pc mobile tablet）
     */
    private String deviceType;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 登录IP地址
     */
    private String ipAddress;

    /**
     * 登录地点
     */
    private String location;

    /**
     * 浏览器类型
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 登录状态（1成功 0失败 2被锁定）
     */
    private String status;

    /**
     * 失败原因（密码错误 账户锁定 账户不存在 验证码错误）
     */
    private String failureReason;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 登录Token（成功时记录）
     */
    private String token;

    /**
     * 登录时间
     */
    private LocalDateTime loginTime;

    /**
     * 退出时间
     */
    private LocalDateTime logoutTime;

    /**
     * 会话持续时间（秒）
     */
    private Integer sessionDuration;

    /**
     * 乐观锁版本号
     */
    @Version
    private Integer version;

    /**
     * 检查登录是否成功
     */
    public boolean isLoginSuccess() {
        return MallLoginStatusConstants.SUCCESS.equals(status);
    }

    /**
     * 检查登录是否失败
     */
    public boolean isLoginFailed() {
        return MallLoginStatusConstants.FAILED.equals(status);
    }

    /**
     * 检查是否因锁定而失败
     */
    public boolean isLockedFailure() {
        return MallLoginStatusConstants.LOCKED.equals(status);
    }

    /**
     * 计算会话持续时间
     */
    public void calculateSessionDuration() {
        if (loginTime != null && logoutTime != null) {
            this.sessionDuration = (int) java.time.Duration.between(loginTime, logoutTime).getSeconds();
        }
    }
}
