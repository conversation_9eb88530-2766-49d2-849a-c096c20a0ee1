package com.imhuso.mall.core.service;

/**
 * 商城安全服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface IMallSecurityService {

    /**
     * 检查登录频率限制
     * 
     * @param email 邮箱地址
     * @param ipAddress IP地址
     * @return 是否允许登录
     */
    boolean checkLoginRateLimit(String email, String ipAddress);

    /**
     * 记录登录尝试
     * 
     * @param email 邮箱地址
     * @param ipAddress IP地址
     * @param success 是否成功
     */
    void recordLoginAttempt(String email, String ipAddress, boolean success);

    /**
     * 检查IP是否被封禁
     * 
     * @param ipAddress IP地址
     * @return 是否被封禁
     */
    boolean isIpBlocked(String ipAddress);

    /**
     * 封禁IP地址
     * 
     * @param ipAddress IP地址
     * @param minutes 封禁时长（分钟）
     * @param reason 封禁原因
     */
    void blockIp(String ipAddress, int minutes, String reason);

    /**
     * 解封IP地址
     * 
     * @param ipAddress IP地址
     */
    void unblockIp(String ipAddress);

    /**
     * 检查邮箱是否需要验证码
     * 
     * @param email 邮箱地址
     * @return 是否需要验证码
     */
    boolean requiresCaptcha(String email);

    /**
     * 检查IP是否需要验证码
     * 
     * @param ipAddress IP地址
     * @return 是否需要验证码
     */
    boolean requiresCaptchaForIp(String ipAddress);

    /**
     * 重置登录失败计数
     * 
     * @param email 邮箱地址
     * @param ipAddress IP地址
     */
    void resetFailureCount(String email, String ipAddress);

    /**
     * 获取登录失败次数
     * 
     * @param email 邮箱地址
     * @return 失败次数
     */
    int getEmailFailureCount(String email);

    /**
     * 获取IP登录失败次数
     * 
     * @param ipAddress IP地址
     * @return 失败次数
     */
    int getIpFailureCount(String ipAddress);

    /**
     * 检查是否为可疑登录
     * 
     * @param email 邮箱地址
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @return 是否可疑
     */
    boolean isSuspiciousLogin(String email, String ipAddress, String userAgent);

    /**
     * 清理过期的安全记录
     * 
     * @return 清理数量
     */
    int cleanExpiredRecords();
}
