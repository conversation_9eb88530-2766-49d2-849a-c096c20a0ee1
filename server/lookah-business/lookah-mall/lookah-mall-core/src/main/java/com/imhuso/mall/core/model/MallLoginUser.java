package com.imhuso.mall.core.model;

import com.imhuso.common.core.domain.model.LoginUser;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.mall.core.constant.MallConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 商城登录用户身份权限
 * <p>
 * Mall模块专用的登录用户模型，继承自通用LoginUser
 * 提供Mall模块特有的用户信息和登录标识
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class MallLoginUser extends LoginUser {

    /**
     * 用户全名
     */
    private String fullName;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 客户端类型（web、app、admin等）
     */
    private String clientType;

    /**
     * 获取登录id
     * Mall模块使用特定的前缀来区分不同模块的用户
     *
     * @return 格式为 "mall_user:{userId}" 的登录ID
     */
    @Override
    public String getLoginId() {
        if (getUserId() == null) {
            throw new IllegalArgumentException(MessageUtils.message("mall.validation.member.id.required"));
        }
        return MallConstants.SaToken.USER_TYPE + ":" + getUserId();
    }
}
