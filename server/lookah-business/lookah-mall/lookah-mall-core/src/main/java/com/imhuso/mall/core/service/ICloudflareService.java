package com.imhuso.mall.core.service;

/**
 * Cloudflare Turnstile 验证码服务接口
 *
 * <AUTHOR>
 */
public interface ICloudflareService {

    /**
     * 验证 Turnstile 验证码
     *
     * @param token 前端获取的验证码token
     * @return 验证结果
     */
    boolean verifyTurnstile(String token);

    /**
     * 获取站点密钥（前端使用）
     *
     * @return 站点密钥
     */
    String getSiteKey();

    /**
     * 检查Turnstile是否已启用
     *
     * @return 是否启用
     */
    boolean isEnabled();

    /**
     * 检查Turnstile配置是否有效
     *
     * @return 配置是否有效
     */
    boolean isConfigured();
}
