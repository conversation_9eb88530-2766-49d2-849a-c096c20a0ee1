package com.imhuso.mall.core.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.imhuso.common.core.utils.DeviceTypeUtils;
import com.imhuso.common.core.utils.ServletUtils;
import com.imhuso.mall.core.constant.MallLoginStatusConstants;
import com.imhuso.mall.core.domain.MallLoginLog;
import com.imhuso.mall.core.mapper.MallLoginLogMapper;
import com.imhuso.mall.core.service.IMallLoginLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商城登录日志Service业务层处理
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MallLoginLogServiceImpl implements IMallLoginLogService {

    private final MallLoginLogMapper loginLogMapper;

    @Override
    public Long recordLoginLog(Long memberId, String email, String loginType, String clientId,
                              String deviceType, String userAgent, String ipAddress, String location,
                              String browser, String os, boolean success, String failureReason,
                              String sessionId, String token) {
        MallLoginLog loginLog = new MallLoginLog();
        loginLog.setMemberId(memberId);
        loginLog.setEmail(StrUtil.blankToDefault(email, ""));
        loginLog.setLoginType(StrUtil.blankToDefault(loginType, "password"));
        loginLog.setClientId(StrUtil.blankToDefault(clientId, ""));
        loginLog.setDeviceType(StrUtil.blankToDefault(deviceType, "pc"));
        loginLog.setUserAgent(StrUtil.blankToDefault(userAgent, ""));
        loginLog.setIpAddress(StrUtil.blankToDefault(ipAddress, ""));
        loginLog.setLocation(StrUtil.blankToDefault(location, ""));
        loginLog.setBrowser(StrUtil.blankToDefault(browser, ""));
        loginLog.setOs(StrUtil.blankToDefault(os, ""));
        loginLog.setStatus(success ? MallLoginStatusConstants.SUCCESS : MallLoginStatusConstants.FAILED);
        loginLog.setFailureReason(StrUtil.blankToDefault(failureReason, ""));
        loginLog.setSessionId(StrUtil.blankToDefault(sessionId, ""));
        loginLog.setToken(StrUtil.blankToDefault(token, ""));
        loginLog.setLoginTime(LocalDateTime.now());
        loginLog.setSessionDuration(0);

        if (loginLogMapper.insert(loginLog) > 0) {
            return loginLog.getLogId();
        }
        return null;
    }

    @Override
    public Long recordSuccessLogin(Long memberId, String email, String loginType, String clientId,
                                  String sessionId, String token) {
        // 获取请求信息
        String userAgent = ServletUtils.getRequest().getHeader("User-Agent");
        String ipAddress = ServletUtils.getClientIP();

        // 解析User-Agent
        UserAgent ua = UserAgentUtil.parse(userAgent);
        String browser = ua.getBrowser().getName();
        String os = ua.getOs().getName();

        // 动态判断设备类型
        String deviceType = DeviceTypeUtils.determineDeviceType(ua);

        return recordLoginLog(memberId, email, loginType, clientId, deviceType, userAgent,
                            ipAddress, "", browser, os, true, "", sessionId, token);
    }

    @Override
    public Long recordFailedLogin(String email, String loginType, String clientId, String failureReason) {
        // 获取请求信息
        String userAgent = ServletUtils.getRequest().getHeader("User-Agent");
        String ipAddress = ServletUtils.getClientIP();

        // 解析User-Agent
        UserAgent ua = UserAgentUtil.parse(userAgent);
        String browser = ua.getBrowser().getName();
        String os = ua.getOs().getName();

        // 动态判断设备类型
        String deviceType = DeviceTypeUtils.determineDeviceType(ua);

        return recordLoginLog(null, email, loginType, clientId, deviceType, userAgent,
                            ipAddress, "", browser, os, false, failureReason, "", "");
    }

    @Override
    public void recordLogout(String sessionId) {
        if (StrUtil.isBlank(sessionId)) {
            return;
        }

        // 查找对应的登录记录
        MallLoginLog loginLog = loginLogMapper.selectBySessionId(sessionId);
        if (loginLog != null && loginLog.getLogoutTime() == null) {
            LocalDateTime logoutTime = LocalDateTime.now();
            int sessionDuration = (int) Duration.between(loginLog.getLoginTime(), logoutTime).getSeconds();
            loginLogMapper.updateLogoutInfo(sessionId, logoutTime, sessionDuration);
        }
    }

    @Override
    public List<MallLoginLog> getLoginLogsByMemberId(Long memberId, Integer limit) {
        return loginLogMapper.selectByMemberId(memberId, limit);
    }

    @Override
    public List<MallLoginLog> getLoginLogsByEmail(String email, Integer limit) {
        return loginLogMapper.selectByEmail(email, limit);
    }

    @Override
    public int countFailedLoginAttempts(String email, String ipAddress, LocalDateTime since) {
        return loginLogMapper.countFailedLoginAttempts(email, ipAddress, since);
    }

    @Override
    public int countLoginAttemptsByIp(String ipAddress, LocalDateTime since) {
        return loginLogMapper.countLoginAttemptsByIp(ipAddress, since);
    }

    @Override
    public MallLoginLog getLastSuccessLogin(Long memberId) {
        return loginLogMapper.selectLastSuccessLogin(memberId);
    }

    @Override
    public int cleanExpiredLogs(LocalDateTime beforeTime) {
        return loginLogMapper.cleanExpiredLogs(beforeTime);
    }

    @Override
    public MallLoginLog getLoginLogByToken(String token) {
        return loginLogMapper.selectByToken(token);
    }
}
