package com.imhuso.mall.core.domain.bo;

import jakarta.validation.constraints.*;
import lombok.Data;
import com.imhuso.mall.core.constant.MallConstants;

import java.io.Serial;
import java.io.Serializable;

/**
 * 商城用户登录业务对象
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
public class MallLoginBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 邮箱地址
     */
    @NotBlank(message = "{mall.validation.email.required}")
    @Email(message = "{mall.validation.email.invalid}")
    @Size(max = 100, message = "{mall.validation.email.max.length}")
    private String email;

    /**
     * 密码
     */
    @NotBlank(message = "{mall.validation.password.required}")
    @Size(min = 6, max = 20, message = "{mall.validation.password.length}")
    private String password;

    /**
     * 验证码
     */
    private String code;

    /**
     * 验证码类型
     */
    private String type;

    /**
     * 客户端ID（从请求头中获取）
     */
    private String clientId;

    /**
     * 授权类型
     */
    private String grantType = MallConstants.Module.NAME;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 记住我
     */
    private Boolean rememberMe = false;
}
