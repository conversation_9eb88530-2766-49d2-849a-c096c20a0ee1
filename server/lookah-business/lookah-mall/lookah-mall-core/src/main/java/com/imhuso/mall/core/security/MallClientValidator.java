package com.imhuso.mall.core.security;

import cn.dev33.satoken.exception.NotLoginException;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.core.utils.ServletUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.satoken.utils.LoginHelper;
import com.imhuso.mall.core.satoken.utils.StpMallUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 商城客户端验证器
 * 验证客户端ID与Token的一致性
 *
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MallClientValidator {

    /**
     * 验证客户端ID与Token的一致性
     *
     * @param request HTTP请求对象
     * @throws NotLoginException 验证失败时抛出
     */
    public static void validateConsistency(HttpServletRequest request) {
        // 检查 header 与 param 里的 clientId 与 token 里的是否一致
        String headerCid = request.getHeader(LoginHelper.CLIENT_ID);
        if (StringUtils.isBlank(headerCid)) {
            // 兼容小写的clientid
            headerCid = request.getHeader("clientid");
        }

        String paramCid = ServletUtils.getParameter(LoginHelper.CLIENT_ID);
        if (StringUtils.isBlank(paramCid)) {
            // 兼容小写参数
            paramCid = ServletUtils.getParameter("clientid");
        }

        Object clientIdObj = StpMallUtil.getExtra(LoginHelper.CLIENT_ID);
        String clientId = clientIdObj != null ? clientIdObj.toString() : null;

        if (!StringUtils.equalsAny(clientId, headerCid, paramCid)) {
            // token 无效
            throw NotLoginException.newInstance(StpMallUtil.getLoginType(),
                "-100", MessageUtils.message("mall.auth.client.token.mismatch"),
                StpMallUtil.getTokenValue());
        }
    }

    /**
     * 提取客户端ID
     *
     * @param request HTTP请求对象
     * @return 客户端ID
     */
    public static String extractClientId(HttpServletRequest request) {
        // 优先从header获取（支持驼峰和小写）
        String clientId = request.getHeader(LoginHelper.CLIENT_ID);
        if (StringUtils.isNotBlank(clientId)) {
            return clientId;
        }

        // 兼容小写的clientid
        clientId = request.getHeader("clientid");
        if (StringUtils.isNotBlank(clientId)) {
            return clientId;
        }

        // 其次从参数获取
        clientId = ServletUtils.getParameter(LoginHelper.CLIENT_ID);
        if (StringUtils.isNotBlank(clientId)) {
            return clientId;
        }

        // 兼容小写参数
        clientId = ServletUtils.getParameter("clientid");
        if (StringUtils.isNotBlank(clientId)) {
            return clientId;
        }

        return null;
    }
}
