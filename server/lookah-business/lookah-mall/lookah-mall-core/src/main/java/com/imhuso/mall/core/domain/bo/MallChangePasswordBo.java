package com.imhuso.mall.core.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * Mall修改密码业务对象
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
public class MallChangePasswordBo {

    /**
     * 旧密码
     */
    @NotBlank(message = "{mall.validation.old.password.required}")
    private String oldPassword;

    /**
     * 新密码
     */
    @NotBlank(message = "{mall.validation.new.password.required}")
    @Size(min = 6, max = 20, message = "{mall.validation.password.length}")
    private String newPassword;

    /**
     * 确认密码
     */
    @NotBlank(message = "{mall.validation.confirm.password.required}")
    private String confirmPassword;
}
