package com.imhuso.mall.core.exception;

import com.imhuso.common.core.exception.base.BaseException;
import com.imhuso.mall.core.constant.MallConstants;

import java.io.Serial;

/**
 * 商城业务异常
 *
 * <AUTHOR>
 */
public class MallException extends BaseException {

    @Serial
    private static final long serialVersionUID = 1L;

    public MallException(String code, Object... args) {
        super(MallConstants.Module.NAME, code, args);
    }

    public MallException(String defaultMessage) {
        super(MallConstants.Module.NAME, defaultMessage);
    }

    public MallException(String code, Object[] args, String defaultMessage) {
        super(MallConstants.Module.NAME, code, args, defaultMessage);
    }
}
