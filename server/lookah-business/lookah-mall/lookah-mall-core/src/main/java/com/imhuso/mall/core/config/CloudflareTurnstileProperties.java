package com.imhuso.mall.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Cloudflare Turnstile 验证码配置
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "mall.captcha.turnstile")
public class CloudflareTurnstileProperties {

    /**
     * 是否启用 Turnstile 验证码
     */
    private boolean enabled = true;

    /**
     * 站点密钥（前端使用）
     */
    private String siteKey;

    /**
     * 密钥（后端验证使用）
     */
    private String secretKey;

    /**
     * 验证API地址
     */
    private String verifyUrl = "https://challenges.cloudflare.com/turnstile/v0/siteverify";

    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 5000;

    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 10000;

    /**
     * 是否在开发环境跳过验证
     */
    private boolean skipInDev = false;
}
