package com.imhuso.mall.core.service.impl;

import cn.dev33.satoken.stp.parameter.SaLoginParameter;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.imhuso.common.client.domain.ClientVo;
import com.imhuso.common.client.service.IClientService;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.core.utils.ServletUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.social.config.properties.SocialProperties;
import com.imhuso.common.social.utils.SocialUtils;
import com.imhuso.mall.core.constant.MallConstants;
import com.imhuso.mall.core.enums.MallMemberAccountStatus;
import com.imhuso.mall.core.enums.MallMemberGender;
import com.imhuso.mall.core.enums.MallMemberVerificationStatus;
import com.imhuso.mall.core.domain.MallMember;
import com.imhuso.mall.core.domain.MallMemberSocial;
import com.imhuso.mall.core.domain.bo.MallMemberBo;
import com.imhuso.mall.core.domain.vo.MallLoginVo;
import com.imhuso.mall.core.domain.vo.MallMemberInfoVo;
import com.imhuso.mall.core.domain.vo.MallMemberVo;
import com.imhuso.mall.core.exception.MallException;
import com.imhuso.mall.core.mapper.MallMemberSocialMapper;
import com.imhuso.mall.core.model.MallLoginUser;
import com.imhuso.mall.core.satoken.MallLoginHelper;
import com.imhuso.mall.core.service.IMallMemberService;
import com.imhuso.mall.core.service.IMallSocialService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 商城社交登录Service业务层处理
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MallSocialServiceImpl implements IMallSocialService {

    private final MallMemberSocialMapper socialMapper;
    private final IMallMemberService memberService;
    private final IClientService clientService;
    private final SocialProperties socialProperties;

    @Override
    public String getAuthUrl(String provider, String clientId, String redirectUri, String state) {
        try {
            // 校验客户端
            ClientVo client = clientService.getClientConfig(clientId);
            if (ObjectUtil.isNull(client)) {
                throw new MallException(MessageUtils.message("mall.client.id.invalid"));
            }

            // 生成状态参数（如果未提供）
            if (StringUtils.isBlank(state)) {
                state = UUID.randomUUID().toString();
            }

            // 使用SocialUtils获取授权URL
            return SocialUtils.getAuthRequest(provider, socialProperties).authorize(state);
        } catch (Exception e) {
            log.error("获取社交登录授权URL失败: provider={}, error={}", provider, e.getMessage(), e);
            throw new MallException(MessageUtils.message("mall.social.auth.url.failed"));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MallLoginVo handleSocialCallback(String provider, String code, String state, String clientId) {
        try {
            // 1. 校验客户端
            ClientVo client = clientService.getClientConfig(clientId);
            if (ObjectUtil.isNull(client)) {
                throw new MallException(MessageUtils.message("mall.auth.client.id.invalid"));
            }

            // 2. 获取第三方用户信息
            AuthResponse<AuthUser> authResponse = SocialUtils.loginAuth(provider, code, state, socialProperties);
            if (!authResponse.ok()) {
                throw new MallException(MessageUtils.message("mall.social.login.failed", authResponse.getMsg()));
            }

            AuthUser authUser = authResponse.getData();
            if (ObjectUtil.isNull(authUser)) {
                throw new MallException(MessageUtils.message("mall.social.user.info.failed"));
            }

            // 3. 查找或创建会员
            Long memberId = findOrCreateMember(provider, authUser);

            // 4. 更新或创建社交账户关联
            updateOrCreateSocialAccount(memberId, provider, authUser);

            // 5. 执行登录
            return performLogin(memberId, client);

        } catch (Exception e) {
            log.error("处理社交登录回调失败: provider={}, error={}", provider, e.getMessage(), e);
            throw new MallException(MessageUtils.message("mall.social.login.failed", e.getMessage()));
        }
    }

    /**
     * 查找或创建会员
     */
    private Long findOrCreateMember(String provider, AuthUser authUser) {
        // 1. 先查找是否已有社交账户关联
        MallMemberSocial existingSocial = findByProviderAndUserId(provider, authUser.getUuid());
        if (existingSocial != null) {
            return existingSocial.getMemberId();
        }

        // 2. 根据邮箱查找现有会员
        if (StringUtils.isNotBlank(authUser.getEmail())) {
            MallMember existingMember = memberService.selectByEmail(authUser.getEmail());
            if (existingMember != null) {
                return existingMember.getMemberId();
            }
        }

        // 3. 创建新会员
        return createOrUpdateMemberFromSocial(provider, authUser);
    }

    /**
     * 更新或创建社交账户关联
     */
    private void updateOrCreateSocialAccount(Long memberId, String provider, AuthUser authUser) {
        MallMemberSocial social = findByProviderAndUserId(provider, authUser.getUuid());

        if (social != null) {
            // 更新现有关联
            updateTokenInfo(social.getId(), authUser);
            updateLoginInfo(social.getId());
        } else {
            // 创建新关联
            bindSocialAccount(memberId, provider, authUser);
        }
    }

    /**
     * 执行登录
     */
    private MallLoginVo performLogin(Long memberId, ClientVo client) {
        // 获取会员信息
        MallMemberVo memberVo = memberService.queryById(memberId);
        if (memberVo == null) {
            throw new MallException(MessageUtils.message("mall.member.info.not.exists"));
        }

        // 转换为实体对象
        MallMember member = MapstructUtils.convert(memberVo, MallMember.class);

        // 检查账户状态
        if (!BusinessConstants.NORMAL.equals(member.getStatus())) {
            throw new MallException(MessageUtils.message("mall.member.account.disabled"));
        }

        // 更新登录信息
        memberService.updateLastLoginInfo(memberId, ServletUtils.getClientIP(), StringUtils.EMPTY);
        memberService.incrementLoginCount(memberId);

        // 创建登录用户
        MallLoginUser loginUser = buildLoginUser(member);
        loginUser.setClientId(client.getClientId());


        // 生成Token
        SaLoginParameter loginParameter = new SaLoginParameter();

        loginParameter.setTimeout(client.getTimeout());
        loginParameter.setActiveTimeout(client.getActiveTimeout());

        MallLoginHelper.login(loginUser, loginParameter);

        // 构建响应
        return buildAuthResponse(member, MallLoginHelper.getToken());
    }

    /**
     * 构建登录用户
     */
    private MallLoginUser buildLoginUser(MallMember member) {
        MallLoginUser loginUser = new MallLoginUser();
        loginUser.setUserId(member.getMemberId());
        loginUser.setUsername(member.getEmail());
        loginUser.setFullName(member.getFullName());
        loginUser.setEmail(member.getEmail());
        loginUser.setAvatar(member.getAvatar());
        return loginUser;
    }

    /**
     * 构建认证响应
     */
    private MallLoginVo buildAuthResponse(MallMember member, String token) {
        MallLoginVo response = new MallLoginVo();
        response.setAccessToken(token);
        response.setTokenType(MallConstants.Token.BEARER_TYPE);
        response.setExpiresIn(MallConstants.Token.DEFAULT_EXPIRES_IN);

        // 构建用户信息
        MallMemberInfoVo memberInfo = new MallMemberInfoVo();
        memberInfo.setMemberId(member.getMemberId());
        memberInfo.setEmail(member.getEmail());
        memberInfo.setFullName(member.getFullName());
        memberInfo.setFirstName(member.getFirstName());
        memberInfo.setLastName(member.getLastName());
        memberInfo.setPhone(member.getPhone());
        memberInfo.setAvatar(member.getAvatar());
        memberInfo.setGender(member.getGender());
        memberInfo.setStatus(member.getStatus());
        memberInfo.setEmailVerified(member.getEmailVerified());
        memberInfo.setPhoneVerified(member.getPhoneVerified());
        memberInfo.setRegisterSource(member.getRegisterSource());
        memberInfo.setLastLoginTime(member.getLastLoginTime());
        memberInfo.setLoginCount(member.getLoginCount());

        response.setMemberInfo(memberInfo);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bindSocialAccount(Long memberId, String provider, AuthUser authUser) {
        // 检查是否已绑定
        if (isSocialAccountBound(provider, authUser.getUuid())) {
            throw new MallException(MessageUtils.message("mall.social.account.already.bound"));
        }

        MallMemberSocial social = new MallMemberSocial();
        social.setMemberId(memberId);
        social.setProvider(provider);
        social.setProviderUserId(authUser.getUuid());
        social.setProviderUsername(StrUtil.blankToDefault(authUser.getUsername(), ""));
        social.setProviderEmail(StrUtil.blankToDefault(authUser.getEmail(), ""));
        social.setProviderAvatar(StrUtil.blankToDefault(authUser.getAvatar(), ""));
        social.setProviderProfile(authUser.getRawUserInfo().toString());

        if (authUser.getToken() != null) {
            social.setAccessToken(StrUtil.blankToDefault(authUser.getToken().getAccessToken(), ""));
            social.setRefreshToken(StrUtil.blankToDefault(authUser.getToken().getRefreshToken(), ""));
            social.setExpiresIn(authUser.getToken().getExpireIn());
            social.setScope(StrUtil.blankToDefault(authUser.getToken().getScope(), ""));
        }

        social.setStatus(BusinessConstants.NORMAL); // 绑定状态
        social.setFirstLoginTime(LocalDateTime.now());
        social.setLoginCount(1);

        return socialMapper.insert(social) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unbindSocialAccount(Long memberId, String provider) {
        return socialMapper.unbindSocialAccount(memberId, provider) > 0;
    }

    @Override
    public MallMemberSocial findByProviderAndUserId(String provider, String providerUserId) {
        return socialMapper.selectByProviderAndUserId(provider, providerUserId);
    }

    @Override
    public List<MallMemberSocial> findByMemberId(Long memberId) {
        return socialMapper.selectByMemberId(memberId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTokenInfo(Long socialId, AuthUser authUser) {
        if (authUser.getToken() == null) {
            return false;
        }

        LocalDateTime expiresAt = null;
        Integer expireIn = authUser.getToken().getExpireIn();
        if (expireIn != null && expireIn > 0) {
            expiresAt = LocalDateTime.now().plusSeconds(expireIn);
        }

        return socialMapper.updateTokenInfo(
            socialId,
            authUser.getToken().getAccessToken(),
            authUser.getToken().getRefreshToken(),
            authUser.getToken().getExpireIn(),
            expiresAt
        ) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLoginInfo(Long socialId) {
        socialMapper.updateLastLoginTime(socialId, LocalDateTime.now());
        return socialMapper.incrementLoginCount(socialId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createOrUpdateMemberFromSocial(String provider, AuthUser authUser) {
        // 创建新会员
        MallMember member = new MallMember();
        member.setEmail(StrUtil.blankToDefault(authUser.getEmail(), ""));
        member.setPassword(StringUtils.EMPTY); // 社交登录用户无密码

        // 解析全名
        String fullName = StrUtil.blankToDefault(authUser.getNickname(), authUser.getUsername());
        member.setFullName(fullName);

        // 尝试分解姓名
        if (StringUtils.isNotBlank(fullName)) {
            String[] nameParts = fullName.split(StringUtils.SPACE, 2);
            member.setFirstName(nameParts[0]);
            if (nameParts.length > 1) {
                member.setLastName(nameParts[1]);
            }
        }

        member.setAvatar(StrUtil.blankToDefault(authUser.getAvatar(), ""));
        member.setGender(MallMemberGender.UNKNOWN.getCode());
        member.setStatus(MallMemberAccountStatus.ACTIVE.getCode());
        member.setEmailVerified(StringUtils.isNotBlank(authUser.getEmail()) ?
            MallMemberVerificationStatus.VERIFIED.getCode() : MallMemberVerificationStatus.NOT_VERIFIED.getCode());
        member.setPhoneVerified(MallMemberVerificationStatus.NOT_VERIFIED.getCode());
        member.setRegisterSource(provider);
        member.setRegisterIp(ServletUtils.getClientIP());
        member.setLoginCount(0);
        member.setFailedLoginCount(0);

        MallMemberBo memberBo = MapstructUtils.convert(member, MallMemberBo.class);
        if (memberService.insertByBo(memberBo)) {
            return memberBo.getMemberId();
        }

        throw new MallException(MessageUtils.message("mall.member.create.failed"));
    }

    @Override
    public boolean isSocialAccountBound(String provider, String providerUserId) {
        MallMemberSocial social = findByProviderAndUserId(provider, providerUserId);
        return social != null && social.isBound();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setPrimaryAccount(Long memberId, String provider) {
        // 先取消所有主要账户设置
        socialMapper.unsetPrimaryAccount(memberId);
        // 设置指定账户为主要账户
        return socialMapper.setPrimaryAccount(memberId, provider) > 0;
    }
}
