package com.imhuso.mall.core.constant;

/**
 * Mall 模块常量定义
 *
 * <AUTHOR>
 */
public class MallConstants {
    // 模块命名
    public static class Module {
        /**
         * 模块名称
         */
        public static final String NAME = "mall";
    }

    public static class Client {
        /**
         * 客户端ID
         */
        public static final String ID_KEY = "clientId";
    }

    /**
     * Sa-Token 相关常量
     */
    public static class SaToken {
        /**
         * Mall 模块的 loginType
         */
        public static final String LOGIN_TYPE = Module.NAME;

        /**
         * User Type
         */
        public static final String USER_TYPE = Module.NAME + "_user";
    }

    /**
     * Token常量
     */
    public static class Token {
        /**
         * Bearer Token类型
         */
        public static final String BEARER_TYPE = "Bearer";

        /**
         * 默认过期时间（2小时）
         */
        public static final Long DEFAULT_EXPIRES_IN = 7200L;
    }

}
