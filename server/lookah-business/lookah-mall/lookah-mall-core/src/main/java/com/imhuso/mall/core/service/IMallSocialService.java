package com.imhuso.mall.core.service;

import com.imhuso.mall.core.domain.MallMemberSocial;
import com.imhuso.mall.core.domain.vo.MallLoginVo;
import me.zhyd.oauth.model.AuthUser;

import java.util.List;

/**
 * 商城社交登录Service接口
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface IMallSocialService {

    /**
     * 获取社交登录授权URL
     *
     * @param provider 第三方平台
     * @param clientId 客户端ID
     * @param redirectUri 回调地址
     * @param state 状态参数
     * @return 授权URL
     */
    String getAuthUrl(String provider, String clientId, String redirectUri, String state);

    /**
     * 处理社交登录回调
     *
     * @param provider 第三方平台
     * @param code 授权码
     * @param state 状态参数
     * @param clientId 客户端ID
     * @return 认证响应
     */
    MallLoginVo handleSocialCallback(String provider, String code, String state, String clientId);

    /**
     * 绑定社交账户
     *
     * @param memberId 会员ID
     * @param provider 第三方平台
     * @param authUser 第三方用户信息
     * @return 绑定结果
     */
    boolean bindSocialAccount(Long memberId, String provider, AuthUser authUser);

    /**
     * 解绑社交账户
     *
     * @param memberId 会员ID
     * @param provider 第三方平台
     * @return 解绑结果
     */
    boolean unbindSocialAccount(Long memberId, String provider);

    /**
     * 根据第三方信息查询社交账户
     *
     * @param provider 第三方平台
     * @param providerUserId 第三方用户ID
     * @return 社交账户信息
     */
    MallMemberSocial findByProviderAndUserId(String provider, String providerUserId);

    /**
     * 根据会员ID查询所有社交账户
     *
     * @param memberId 会员ID
     * @return 社交账户列表
     */
    List<MallMemberSocial> findByMemberId(Long memberId);

    /**
     * 更新社交账户Token信息
     *
     * @param socialId 社交账户ID
     * @param authUser 第三方用户信息
     * @return 更新结果
     */
    boolean updateTokenInfo(Long socialId, AuthUser authUser);

    /**
     * 更新社交账户登录信息
     *
     * @param socialId 社交账户ID
     * @return 更新结果
     */
    boolean updateLoginInfo(Long socialId);

    /**
     * 根据第三方用户信息创建或更新会员
     *
     * @param provider 第三方平台
     * @param authUser 第三方用户信息
     * @return 会员ID
     */
    Long createOrUpdateMemberFromSocial(String provider, AuthUser authUser);

    /**
     * 检查社交账户是否已绑定
     *
     * @param provider 第三方平台
     * @param providerUserId 第三方用户ID
     * @return 是否已绑定
     */
    boolean isSocialAccountBound(String provider, String providerUserId);

    /**
     * 设置主要社交账户
     *
     * @param memberId 会员ID
     * @param provider 第三方平台
     * @return 设置结果
     */
    boolean setPrimaryAccount(Long memberId, String provider);
}
