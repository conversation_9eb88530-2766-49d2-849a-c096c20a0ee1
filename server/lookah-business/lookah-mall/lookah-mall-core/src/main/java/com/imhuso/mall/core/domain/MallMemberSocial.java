package com.imhuso.mall.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.mall.core.enums.MallMemberBindingStatus;
import com.imhuso.mall.core.enums.MallMemberPrimaryStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 商城会员社交登录关联实体类
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_member_social")
public class MallMemberSocial extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 第三方平台（google facebook apple wechat qq）
     */
    private String provider;

    /**
     * 第三方平台用户ID
     */
    private String providerUserId;

    /**
     * 第三方平台用户名
     */
    private String providerUsername;

    /**
     * 第三方平台邮箱
     */
    private String providerEmail;

    /**
     * 第三方平台头像
     */
    private String providerAvatar;

    /**
     * 第三方平台用户资料（JSON格式）
     */
    private String providerProfile;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * Token过期时间（秒）
     */
    private Integer expiresIn;

    /**
     * Token过期时间点
     */
    private LocalDateTime tokenExpiresAt;

    /**
     * 授权范围
     */
    private String scope;

    /**
     * 是否主要账户（0否 1是）
     */
    private String isPrimary;

    /**
     * 绑定状态（0解绑 1绑定）
     */
    private String status;

    /**
     * 首次登录时间
     */
    private LocalDateTime firstLoginTime;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 登录次数
     */
    private Integer loginCount;

    /**
     * 乐观锁版本号
     */
    @Version
    private Integer version;

    /**
     * 检查是否为主要账户
     */
    public boolean isPrimaryAccount() {
        return MallMemberPrimaryStatus.YES.getCode().equals(isPrimary);
    }

    /**
     * 检查绑定状态
     */
    public boolean isBound() {
        return MallMemberBindingStatus.BOUND.getCode().equals(status);
    }

    /**
     * 检查Token是否过期
     */
    public boolean isTokenExpired() {
        return tokenExpiresAt != null && tokenExpiresAt.isBefore(LocalDateTime.now());
    }
}
