package com.imhuso.mall.core.service.impl;

import cn.hutool.core.util.StrUtil;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.redis.utils.RedisUtils;
import com.imhuso.mall.core.service.IMallSecurityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashSet;
import java.util.Set;

/**
 * 商城安全服务实现
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MallSecurityServiceImpl implements IMallSecurityService {

    // Redis Key前缀
    private static final String EMAIL_FAILURE_PREFIX = "mall:security:email:failure:";
    private static final String IP_FAILURE_PREFIX = "mall:security:ip:failure:";
    private static final String IP_BLOCK_PREFIX = "mall:security:ip:block:";
    private static final String LOGIN_ATTEMPT_PREFIX = "mall:security:attempt:";

    // 安全配置
    private static final int EMAIL_MAX_FAILURES = 5; // 邮箱最大失败次数
    private static final int IP_MAX_FAILURES = 10; // IP最大失败次数
    private static final int EMAIL_FAILURE_WINDOW = 900; // 邮箱失败窗口期（15分钟）
    private static final int IP_FAILURE_WINDOW = 3600; // IP失败窗口期（1小时）
    private static final int CAPTCHA_THRESHOLD = 3; // 需要验证码的失败次数阈值
    private static final int AUTO_BLOCK_MINUTES = 60; // 自动封禁时长（分钟）

    @Override
    public boolean checkLoginRateLimit(String email, String ipAddress) {
        // 检查IP是否被封禁
        if (isIpBlocked(ipAddress)) {
            log.warn("IP blocked, login denied: ip={}", ipAddress);
            return false;
        }

        // 检查邮箱失败次数
        int emailFailures = getEmailFailureCount(email);
        if (emailFailures >= EMAIL_MAX_FAILURES) {
            log.warn("Too many email failures, login denied: email={}, failures={}", email, emailFailures);
            return false;
        }

        // 检查IP失败次数
        int ipFailures = getIpFailureCount(ipAddress);
        if (ipFailures >= IP_MAX_FAILURES) {
            log.warn("Too many IP failures, auto-blocking: ip={}, failures={}", ipAddress, ipFailures);
            blockIp(ipAddress, AUTO_BLOCK_MINUTES, MessageUtils.message("mall.security.too.many.failures"));
            return false;
        }

        return true;
    }

    @Override
    public void recordLoginAttempt(String email, String ipAddress, boolean success) {
        if (success) {
            // 登录成功，重置失败计数
            resetFailureCount(email, ipAddress);
        } else {
            // 登录失败，增加失败计数
            incrementEmailFailureCount(email);
            incrementIpFailureCount(ipAddress);
        }

        // 记录登录尝试
        String attemptKey = LOGIN_ATTEMPT_PREFIX + ipAddress + ":" + System.currentTimeMillis();
        RedisUtils.setCacheObject(attemptKey, email + ":" + success, Duration.ofHours(24));
    }

    @Override
    public boolean isIpBlocked(String ipAddress) {
        if (StrUtil.isBlank(ipAddress)) {
            return false;
        }

        String key = IP_BLOCK_PREFIX + ipAddress;
        return RedisUtils.hasKey(key);
    }

    @Override
    public void blockIp(String ipAddress, int minutes, String reason) {
        if (StrUtil.isBlank(ipAddress)) {
            return;
        }

        String key = IP_BLOCK_PREFIX + ipAddress;
        String value = reason + ":" + System.currentTimeMillis();
        RedisUtils.setCacheObject(key, value, Duration.ofMinutes(minutes));

        log.warn("封禁IP地址: ip={}, minutes={}, reason={}", ipAddress, minutes, reason);
    }

    @Override
    public void unblockIp(String ipAddress) {
        if (StrUtil.isBlank(ipAddress)) {
            return;
        }

        String key = IP_BLOCK_PREFIX + ipAddress;
        RedisUtils.deleteObject(key);

        log.info("解封IP地址: ip={}", ipAddress);
    }

    @Override
    public boolean requiresCaptcha(String email) {
        return getEmailFailureCount(email) >= CAPTCHA_THRESHOLD;
    }

    @Override
    public boolean requiresCaptchaForIp(String ipAddress) {
        return getIpFailureCount(ipAddress) >= CAPTCHA_THRESHOLD;
    }

    @Override
    public void resetFailureCount(String email, String ipAddress) {
        if (StrUtil.isNotBlank(email)) {
            String emailKey = EMAIL_FAILURE_PREFIX + email;
            RedisUtils.deleteObject(emailKey);
        }

        if (StrUtil.isNotBlank(ipAddress)) {
            String ipKey = IP_FAILURE_PREFIX + ipAddress;
            RedisUtils.deleteObject(ipKey);
        }
    }

    @Override
    public int getEmailFailureCount(String email) {
        if (StrUtil.isBlank(email)) {
            return 0;
        }

        String key = EMAIL_FAILURE_PREFIX + email;
        Integer count = RedisUtils.getCacheObject(key);
        return count != null ? count : 0;
    }

    @Override
    public int getIpFailureCount(String ipAddress) {
        if (StrUtil.isBlank(ipAddress)) {
            return 0;
        }

        String key = IP_FAILURE_PREFIX + ipAddress;
        Integer count = RedisUtils.getCacheObject(key);
        return count != null ? count : 0;
    }

    @Override
    public boolean isSuspiciousLogin(String email, String ipAddress, String userAgent) {
        // 可疑登录检测逻辑

        // 检查User-Agent是否异常
        if (StrUtil.isBlank(userAgent) || userAgent.length() < 10) {
            return true;
        }

        // 检查是否为新IP（基于历史登录记录）
        if (isNewIpForUser(email, ipAddress)) {
            return true;
        }

        // 检查登录频率
        int ipFailures = getIpFailureCount(ipAddress);
        return ipFailures > 0;
    }

    /**
     * 检查是否为用户的新IP地址
     */
    private boolean isNewIpForUser(String email, String ipAddress) {
        String key = "mall:login:history:" + email;
        Set<String> historicalIps = RedisUtils.getCacheObject(key);

        if (historicalIps == null) {
            historicalIps = new HashSet<>();
        }

        boolean isNewIp = !historicalIps.contains(ipAddress);

        // 记录新IP
        if (isNewIp) {
            historicalIps.add(ipAddress);
            // 保留最近30天的IP记录
            RedisUtils.setCacheObject(key, historicalIps, Duration.ofDays(30));
        }

        return isNewIp;
    }

    @Override
    public int cleanExpiredRecords() {
        // Redis会自动清理过期的key，这里返回0
        return 0;
    }

    /**
     * 增加邮箱失败计数
     */
    private void incrementEmailFailureCount(String email) {
        if (StrUtil.isBlank(email)) {
            return;
        }

        String key = EMAIL_FAILURE_PREFIX + email;
        Integer count = RedisUtils.getCacheObject(key);
        count = count != null ? count + 1 : 1;

        RedisUtils.setCacheObject(key, count, Duration.ofSeconds(EMAIL_FAILURE_WINDOW));
    }

    /**
     * 增加IP失败计数
     */
    private void incrementIpFailureCount(String ipAddress) {
        if (StrUtil.isBlank(ipAddress)) {
            return;
        }

        String key = IP_FAILURE_PREFIX + ipAddress;
        Integer count = RedisUtils.getCacheObject(key);
        count = count != null ? count + 1 : 1;

        RedisUtils.setCacheObject(key, count, Duration.ofSeconds(IP_FAILURE_WINDOW));
    }
}
