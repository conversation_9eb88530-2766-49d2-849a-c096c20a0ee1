package com.imhuso.mall.core.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.imhuso.common.excel.annotation.ExcelDictFormat;
import com.imhuso.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 商城会员视图对象 MallMemberVo
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = com.imhuso.mall.core.domain.MallMember.class)
public class MallMemberVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员ID
     */
    @ExcelProperty(value = "会员ID")
    private Long memberId;

    /**
     * 邮箱地址
     */
    @ExcelProperty(value = "邮箱地址")
    private String email;

    /**
     * 全名
     */
    @ExcelProperty(value = "全名")
    private String fullName;

    /**
     * 名字
     */
    @ExcelProperty(value = "名字")
    private String firstName;

    /**
     * 姓氏
     */
    @ExcelProperty(value = "姓氏")
    private String lastName;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "手机号码")
    private String phone;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 性别（0未知 1男 2女）
     */
    @ExcelProperty(value = "性别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_user_sex")
    private String gender;

    /**
     * 生日
     */
    @ExcelProperty(value = "生日")
    private LocalDate birthday;

    /**
     * 账户状态（0停用 1正常 2待验证）
     */
    @ExcelProperty(value = "账户状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=停用,1=正常,2=待验证")
    private String status;

    /**
     * 邮箱验证状态（0未验证 1已验证）
     */
    @ExcelProperty(value = "邮箱验证状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=未验证,1=已验证")
    private String emailVerified;

    /**
     * 手机验证状态（0未验证 1已验证）
     */
    @ExcelProperty(value = "手机验证状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=未验证,1=已验证")
    private String phoneVerified;

    /**
     * 注册来源（email邮箱注册 google谷歌登录 facebook脸书登录）
     */
    @ExcelProperty(value = "注册来源", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "email=邮箱注册,google=谷歌登录,facebook=脸书登录,apple=苹果登录,wechat=微信登录,qq=QQ登录")
    private String registerSource;

    /**
     * 注册IP地址
     */
    @ExcelProperty(value = "注册IP")
    private String registerIp;

    /**
     * 注册地点
     */
    @ExcelProperty(value = "注册地点")
    private String registerLocation;

    /**
     * 最后登录时间
     */
    @ExcelProperty(value = "最后登录时间")
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    @ExcelProperty(value = "最后登录IP")
    private String lastLoginIp;

    /**
     * 最后登录地点
     */
    @ExcelProperty(value = "最后登录地点")
    private String lastLoginLocation;

    /**
     * 登录次数
     */
    @ExcelProperty(value = "登录次数")
    private Integer loginCount;

    /**
     * 连续失败登录次数
     */
    @ExcelProperty(value = "失败登录次数")
    private Integer failedLoginCount;

    /**
     * 最后失败登录时间
     */
    @ExcelProperty(value = "最后失败登录时间")
    private LocalDateTime lastFailedLoginTime;

    /**
     * 账户锁定到期时间
     */
    @ExcelProperty(value = "锁定到期时间")
    private LocalDateTime lockedUntil;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 乐观锁版本号
     */
    private Integer version;
}
