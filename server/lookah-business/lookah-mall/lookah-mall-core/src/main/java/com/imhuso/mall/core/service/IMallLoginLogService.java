package com.imhuso.mall.core.service;

import com.imhuso.mall.core.domain.MallLoginLog;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 商城登录日志Service接口
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface IMallLoginLogService {

    /**
     * 记录登录日志
     * 
     * @param memberId 会员ID（可为空，登录失败时）
     * @param email 登录邮箱
     * @param loginType 登录类型
     * @param clientId 客户端ID
     * @param deviceType 设备类型
     * @param userAgent 用户代理
     * @param ipAddress IP地址
     * @param location 登录地点
     * @param browser 浏览器
     * @param os 操作系统
     * @param success 是否成功
     * @param failureReason 失败原因
     * @param sessionId 会话ID
     * @param token 登录Token
     * @return 日志ID
     */
    Long recordLoginLog(Long memberId, String email, String loginType, String clientId, 
                       String deviceType, String userAgent, String ipAddress, String location,
                       String browser, String os, boolean success, String failureReason,
                       String sessionId, String token);

    /**
     * 记录成功登录日志
     */
    Long recordSuccessLogin(Long memberId, String email, String loginType, String clientId,
                           String sessionId, String token);

    /**
     * 记录失败登录日志
     */
    Long recordFailedLogin(String email, String loginType, String clientId, String failureReason);

    /**
     * 更新退出登录信息
     */
    void recordLogout(String sessionId);

    /**
     * 根据会员ID查询登录日志
     */
    List<MallLoginLog> getLoginLogsByMemberId(Long memberId, Integer limit);

    /**
     * 根据邮箱查询登录日志
     */
    List<MallLoginLog> getLoginLogsByEmail(String email, Integer limit);

    /**
     * 统计失败登录次数
     */
    int countFailedLoginAttempts(String email, String ipAddress, LocalDateTime since);

    /**
     * 统计IP登录次数
     */
    int countLoginAttemptsByIp(String ipAddress, LocalDateTime since);

    /**
     * 获取最近成功登录记录
     */
    MallLoginLog getLastSuccessLogin(Long memberId);

    /**
     * 清理过期日志
     */
    int cleanExpiredLogs(LocalDateTime beforeTime);

    /**
     * 根据Token查询登录日志
     */
    MallLoginLog getLoginLogByToken(String token);
}
