package com.imhuso.mall.core.mapper;

import com.imhuso.common.mybatis.core.mapper.BaseMapperPlus;
import com.imhuso.mall.core.domain.MallMemberSocial;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 商城会员社交登录关联Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Mapper
public interface MallMemberSocialMapper extends BaseMapperPlus<MallMemberSocial, MallMemberSocial> {

    /**
     * 根据第三方平台和用户ID查询社交账户
     *
     * @param provider 第三方平台
     * @param providerUserId 第三方用户ID
     * @return 社交账户信息
     */
    MallMemberSocial selectByProviderAndUserId(@Param("provider") String provider,
                                              @Param("providerUserId") String providerUserId);

    /**
     * 根据会员ID查询所有社交账户
     *
     * @param memberId 会员ID
     * @return 社交账户列表
     */
    List<MallMemberSocial> selectByMemberId(@Param("memberId") Long memberId);

    /**
     * 根据会员ID和平台查询社交账户
     *
     * @param memberId 会员ID
     * @param provider 第三方平台
     * @return 社交账户信息
     */
    MallMemberSocial selectByMemberIdAndProvider(@Param("memberId") Long memberId,
                                                @Param("provider") String provider);

    /**
     * 更新Token信息
     *
     * @param id 主键ID
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn 过期时间（秒）
     * @param tokenExpiresAt Token过期时间点
     * @return 更新行数
     */
    int updateTokenInfo(@Param("id") Long id,
                       @Param("accessToken") String accessToken,
                       @Param("refreshToken") String refreshToken,
                       @Param("expiresIn") Integer expiresIn,
                       @Param("tokenExpiresAt") LocalDateTime tokenExpiresAt);

    /**
     * 更新最后登录时间
     *
     * @param id            主键ID
     * @param lastLoginTime 最后登录时间
     */
    void updateLastLoginTime(@Param("id") Long id,
                             @Param("lastLoginTime") LocalDateTime lastLoginTime);

    /**
     * 增加登录次数
     *
     * @param id 主键ID
     * @return 更新行数
     */
    int incrementLoginCount(@Param("id") Long id);

    /**
     * 设置为主要账户
     *
     * @param memberId 会员ID
     * @param provider 第三方平台
     * @return 更新行数
     */
    int setPrimaryAccount(@Param("memberId") Long memberId,
                         @Param("provider") String provider);

    /**
     * 取消主要账户设置
     *
     * @param memberId 会员ID
     */
    void unsetPrimaryAccount(@Param("memberId") Long memberId);

    /**
     * 解绑社交账户
     *
     * @param memberId 会员ID
     * @param provider 第三方平台
     * @return 更新行数
     */
    int unbindSocialAccount(@Param("memberId") Long memberId,
                           @Param("provider") String provider);

    /**
     * 绑定社交账户
     *
     * @param memberId 会员ID
     * @param provider 第三方平台
     * @return 更新行数
     */
    int bindSocialAccount(@Param("memberId") Long memberId,
                         @Param("provider") String provider);
}
