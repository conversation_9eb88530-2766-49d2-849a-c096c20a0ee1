package com.imhuso.mall.core.constant;

/**
 * 商城缓存常量
 * 缓存常量定义格式: cacheNames#ttl#maxIdleTime#maxSize
 *
 * <AUTHOR>
 */
public interface MallCacheNames {

    /**
     * 商城产品缓存 - 30分钟过期，15分钟空闲，最大1000条
     */
    String MALL_PRODUCT = MallConstants.Module.NAME + ":product#1800#900#1000";

    /**
     * 商城分类缓存 - 1小时过期，30分钟空闲，最大500条
     */
    String MALL_CATEGORY = MallConstants.Module.NAME + ":category#3600#1800#500";

    /**
     * 商城会员缓存 - 15分钟过期，10分钟空闲，最大2000条
     */
    String MALL_MEMBER = MallConstants.Module.NAME + ":member#900#600#2000";

    /**
     * 商城购物车缓存 - 24小时过期，2小时空闲，最大5000条
     */
    String MALL_CART = MallConstants.Module.NAME + ":cart#86400#7200#5000";

    /**
     * 商城订单缓存 - 30分钟过期，15分钟空闲，最大3000条
     */
    String MALL_ORDER = MallConstants.Module.NAME + ":order#1800#900#3000";

    /**
     * 商城促销缓存 - 1小时过期，30分钟空闲，最大1000条
     */
    String MALL_PROMOTION = MallConstants.Module.NAME + ":promotion#3600#1800#1000";

    /**
     * 商城库存缓存 - 5分钟过期，2分钟空闲，最大10000条
     */
    String MALL_INVENTORY = MallConstants.Module.NAME + ":inventory#300#120#10000";

    /**
     * 商城支付缓存 - 10分钟过期，5分钟空闲，最大1000条
     */
    String MALL_PAYMENT = MallConstants.Module.NAME + ":payment#600#300#1000";
}
