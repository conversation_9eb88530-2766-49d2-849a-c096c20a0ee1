package com.imhuso.mall.core.satoken;

import com.imhuso.mall.core.model.MallLoginUser;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import com.imhuso.mall.core.constant.MallConstants;
import com.imhuso.mall.core.satoken.utils.StpMallUtil;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpLogic;
import cn.dev33.satoken.stp.parameter.SaLoginParameter;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * Store 模块专用登录鉴权助手
 * 参考 LoginHelper 的设计风格，专门处理商城用户的登录、权限验证等操作
 * 与系统管理员登录完全隔离
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@DependsOn("stpMallUtil")
public class MallLoginHelper {

    public static final String LOGIN_USER_KEY = "loginUser";
    public static final String USER_KEY = "userId";
    public static final String USER_NAME_KEY = "userName";
    public static final String CLIENT_ID = MallConstants.Client.ID_KEY;

    private static StpLogic stpLogic;

    /**
     * 获取 StpLogic 实例，如果为空则尝试重新获取
     */
    private static StpLogic getStpLogic() {
        if (stpLogic == null) {
            stpLogic = StpMallUtil.getStpLogic();
        }
        return stpLogic;
    }

    /**
     * Mall 用户登录
     *
     * @param loginUser 登录用户信息
     * @param model     配置参数
     */
    public static void login(MallLoginUser loginUser, SaLoginParameter model) {
        model = ObjectUtil.defaultIfNull(model, new SaLoginParameter());

        // 设置用户类型为 MALL_USER
        loginUser.setUserType(MallConstants.SaToken.USER_TYPE);

        getStpLogic().login(loginUser.getLoginId(), model.setExtra(USER_KEY, loginUser.getUserId()).setExtra(USER_NAME_KEY, loginUser.getUsername()));

        getStpLogic().getTokenSession().set(LOGIN_USER_KEY, loginUser);

        log.debug("Mall 用户登录成功，用户ID: {}, 用户名: {}", loginUser.getUserId(), loginUser.getUsername());
    }

    /**
     * Mall 用户登录（简化版本）
     */
    public static void login(MallLoginUser loginUser) {
        login(loginUser, new SaLoginParameter());
    }

    /**
     * 获取当前登录的 Mall 用户
     */
    public static MallLoginUser getLoginUser() {
        SaSession session = getStpLogic().getTokenSession();
        if (ObjectUtil.isNull(session)) {
            return null;
        }
        return (MallLoginUser) session.get(LOGIN_USER_KEY);
    }

    /**
     * 获取当前用户ID
     */
    public static Long getUserId() {
        Object userId = getExtra();
        if (ObjectUtil.isNull(userId)) {
            return null;
        }
        return Long.valueOf(userId.toString());
    }

    /**
     * 获取当前 Token
     */
    public static String getToken() {
        return getStpLogic().getTokenValue();
    }

    /**
     * 检查当前用户是否已登录
     */
    public static boolean isLogin() {
        try {
            return getStpLogic().isLogin() && getLoginUser() != null;
        } catch (Exception e) {
            // 在异步环境（如定时任务）中，SaToken上下文未初始化是正常情况，使用debug级别
            if (e instanceof cn.dev33.satoken.exception.SaTokenContextException) {
                log.debug("SaToken上下文未初始化（可能在异步环境中）: {}", e.getMessage());
            } else {
                log.warn("检查登录状态时发生异常", e);
            }
            return false;
        }
    }

    /**
     * 获取当前 Token Session
     */
    public static SaSession getTokenSession() {
        return getStpLogic().getTokenSession();
    }

    /**
     * Mall 用户退出登录
     */
    public static void logout() {
        getStpLogic().logout();
        log.debug("Mall 用户退出登录成功");
    }

    /**
     * 获取当前 Token 的扩展信息
     */
    private static Object getExtra() {
        try {
            return getStpLogic().getExtra(MallLoginHelper.USER_KEY);
        } catch (Exception e) {
            return null;
        }
    }
}
