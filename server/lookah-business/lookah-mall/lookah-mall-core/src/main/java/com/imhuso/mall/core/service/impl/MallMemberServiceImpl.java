package com.imhuso.mall.core.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.client.domain.ClientVo;
import com.imhuso.common.client.service.IClientService;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.core.utils.ServletUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.json.utils.JsonUtils;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.mall.core.constant.MallConstants;
import com.imhuso.mall.core.enums.MallMemberAccountStatus;
import com.imhuso.mall.core.enums.MallMemberGender;
import com.imhuso.mall.core.enums.MallMemberVerificationStatus;
import com.imhuso.mall.core.domain.MallMember;
import com.imhuso.mall.core.domain.bo.MallChangePasswordBo;
import com.imhuso.mall.core.domain.bo.MallLoginBo;
import com.imhuso.mall.core.domain.bo.MallMemberBo;
import com.imhuso.mall.core.domain.bo.MallRegisterBo;
import com.imhuso.mall.core.domain.vo.MallLoginVo;
import com.imhuso.mall.core.domain.vo.MallMemberVo;
import com.imhuso.mall.core.exception.MallException;
import com.imhuso.mall.core.mapper.MallMemberMapper;
import com.imhuso.mall.core.satoken.MallLoginHelper;
import com.imhuso.mall.core.service.IMallAuthStrategy;
import com.imhuso.mall.core.service.IMallCaptchaService;
import com.imhuso.mall.core.service.IMallLoginLogService;
import com.imhuso.mall.core.service.IMallMemberService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 商城会员Service业务层处理
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MallMemberServiceImpl implements IMallMemberService {

    private final MallMemberMapper baseMapper;
    private final IClientService clientService;
    private final IMallLoginLogService loginLogService;
    private final IMallCaptchaService captchaService;

    /**
     * 查询商城会员
     */
    @Override
    public MallMemberVo queryById(Long memberId) {
        return baseMapper.selectVoById(memberId);
    }

    /**
     * 查询商城会员列表
     */
    @Override
    public TableDataInfo<MallMemberVo> queryPageList(MallMemberBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MallMember> lqw = buildQueryWrapper(bo);
        Page<MallMemberVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询商城会员列表
     */
    @Override
    public List<MallMemberVo> queryList(MallMemberBo bo) {
        LambdaQueryWrapper<MallMember> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MallMember> buildQueryWrapper(MallMemberBo bo) {
        LambdaQueryWrapper<MallMember> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getEmail()), MallMember::getEmail, bo.getEmail());
        lqw.like(StringUtils.isNotBlank(bo.getFullName()), MallMember::getFullName, bo.getFullName());
        lqw.like(StringUtils.isNotBlank(bo.getFirstName()), MallMember::getFirstName, bo.getFirstName());
        lqw.like(StringUtils.isNotBlank(bo.getLastName()), MallMember::getLastName, bo.getLastName());
        lqw.eq(StringUtils.isNotBlank(bo.getPhone()), MallMember::getPhone, bo.getPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getGender()), MallMember::getGender, bo.getGender());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), MallMember::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getEmailVerified()), MallMember::getEmailVerified, bo.getEmailVerified());
        lqw.eq(StringUtils.isNotBlank(bo.getPhoneVerified()), MallMember::getPhoneVerified, bo.getPhoneVerified());
        lqw.eq(StringUtils.isNotBlank(bo.getRegisterSource()), MallMember::getRegisterSource, bo.getRegisterSource());
        return lqw;
    }

    /**
     * 新增商城会员
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(MallMemberBo bo) {
        MallMember add = MapstructUtils.convert(bo, MallMember.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setMemberId(add.getMemberId());
        }
        return flag;
    }

    /**
     * 修改商城会员
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(MallMemberBo bo) {
        MallMember update = MapstructUtils.convert(bo, MallMember.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MallMember entity) {
        // 校验邮箱唯一性
        if (StringUtils.isNotBlank(entity.getEmail()) &&
            !checkEmailUnique(entity.getEmail(), entity.getMemberId())) {
            throw new MallException(MessageUtils.message("mall.auth.email.already.exists"));
        }
    }

    /**
     * 批量删除商城会员
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public MallMember selectByEmail(String email) {
        return baseMapper.selectByEmail(email);
    }

    @Override
    public MallMember selectByPhone(String phone) {
        return baseMapper.selectByPhone(phone);
    }

    @Override
    public boolean checkEmailUnique(String email, Long memberId) {
        MallMember member = selectByEmail(email);
        return ObjectUtil.isNull(member) || member.getMemberId().equals(memberId);
    }

    @Override
    public boolean checkPhoneUnique(String phone, Long memberId) {
        if (StringUtils.isBlank(phone)) {
            return true;
        }
        MallMember member = selectByPhone(phone);
        return ObjectUtil.isNull(member) || member.getMemberId().equals(memberId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MallLoginVo register(MallRegisterBo request) {
        // 1. 校验客户端
        ClientVo client = clientService.getClientConfig(request.getClientId());
        if (ObjectUtil.isNull(client)) {
            throw new MallException(MessageUtils.message("mall.auth.client.id.invalid"));
        }

        // 2. 校验邮箱唯一性
        if (!checkEmailUnique(request.getEmail(), null)) {
            throw new MallException(MessageUtils.message("mall.auth.email.already.registered"));
        }

        // 4. 创建会员对象
        MallMember member = new MallMember();
        member.setEmail(request.getEmail());
        member.setPassword(BCrypt.hashpw(request.getPassword()));
        member.setFullName(StrUtil.blankToDefault(request.getFullName(), ""));
        member.setFirstName(StrUtil.blankToDefault(request.getFirstName(), ""));
        member.setLastName(StrUtil.blankToDefault(request.getLastName(), ""));
        member.setPhone(StrUtil.blankToDefault(request.getPhone(), ""));
        member.setGender(StrUtil.blankToDefault(request.getGender(), MallMemberGender.UNKNOWN.getCode()));
        member.setStatus(MallMemberAccountStatus.ACTIVE.getCode()); // 正常状态
        member.setEmailVerified(MallMemberVerificationStatus.NOT_VERIFIED.getCode()); // 待验证
        member.setPhoneVerified(MallMemberVerificationStatus.NOT_VERIFIED.getCode()); // 待验证
        member.setRegisterSource(MallConstants.Module.NAME);
        member.setRegisterIp(ServletUtils.getClientIP());
        member.setLoginCount(0);
        member.setFailedLoginCount(0);

        // 5. 保存会员
        if (baseMapper.insert(member) <= 0) {
            throw new MallException(MessageUtils.message("mall.auth.registration.failed"));
        }

        // 6. 自动登录
        MallLoginBo loginRequest = new MallLoginBo();
        loginRequest.setEmail(request.getEmail());
        loginRequest.setPassword(request.getPassword());
        loginRequest.setClientId(request.getClientId());
        loginRequest.setGrantType(request.getGrantType());

        return login(loginRequest);
    }

    @Override
    public MallLoginVo login(MallLoginBo request) {
        // 1. 校验客户端
        ClientVo client = clientService.getClientConfig(request.getClientId());
        if (ObjectUtil.isNull(client)) {
            log.warn("客户端配置不存在，clientId: {}", request.getClientId());
            throw new MallException(MessageUtils.message("mall.auth.client.id.invalid"));
        }

        // 2. 使用策略模式进行登录
        String grantType = StrUtil.isBlank(request.getGrantType()) ? "password" : request.getGrantType();
        String body = JsonUtils.toJsonString(request);

        return IMallAuthStrategy.login(body, client, grantType);
    }

    @Override
    public void logout() {
        // 记录退出日志
        String sessionId = MallLoginHelper.getTokenSession().getId();
        loginLogService.recordLogout(sessionId);

        // 执行退出
        MallLoginHelper.logout();
    }


    @Override
    public void updateLastLoginInfo(Long memberId, String loginIp, String loginLocation) {
        baseMapper.updateLastLoginInfo(memberId, LocalDateTime.now(), loginIp, loginLocation);
    }

    @Override
    public void incrementLoginCount(Long memberId) {
        baseMapper.incrementLoginCount(memberId);
    }

    @Override
    public void updateFailedLoginInfo(Long memberId, Integer failedCount) {
        baseMapper.updateFailedLoginInfo(memberId, failedCount, LocalDateTime.now());
    }

    @Override
    public void lockAccount(Long memberId, Integer lockMinutes) {
        LocalDateTime lockedUntil = LocalDateTime.now().plusMinutes(lockMinutes);
        baseMapper.lockAccount(memberId, lockedUntil);
    }

    @Override
    public void unlockAccount(Long memberId) {
        baseMapper.unlockAccount(memberId);
    }

    @Override
    public void resetFailedLoginCount(Long memberId) {
        baseMapper.resetFailedLoginCount(memberId);
    }

    @Override
    public void verifyEmail(Long memberId) {
        baseMapper.verifyEmail(memberId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void verifyEmailWithCode(Long memberId, String code) {
        // 获取用户信息
        MallMember member = baseMapper.selectById(memberId);
        if (member == null) {
            throw new MallException(MessageUtils.message("mall.member.info.not.found"));
        }

        // 验证邮箱验证码
        if (!captchaService.validateEmailCode(member.getEmail(), code, "verify")) {
            throw new MallException(MessageUtils.message("mall.captcha.invalid.or.expired"));
        }

        // 更新邮箱验证状态
        verifyEmail(memberId);
    }

    @Override
    public boolean isAccountLocked(Long memberId) {
        MallMember member = baseMapper.selectById(memberId);
        return member != null && member.isLocked();
    }

    @Override
    public int getFailedLoginCount(String email, String ipAddress) {
        // 这里可以结合登录日志表来统计失败次数
        // 暂时返回用户表中的失败次数
        MallMember member = selectByEmail(email);
        return member != null ? member.getFailedLoginCount() : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changePassword(Long memberId, MallChangePasswordBo bo) {
        // 验证新密码确认
        if (!bo.getNewPassword().equals(bo.getConfirmPassword())) {
            throw new MallException(MessageUtils.message("mall.member.password.confirm.not.match"));
        }

        // 获取当前用户信息
        MallMember member = baseMapper.selectById(memberId);
        if (member == null) {
            throw new MallException(MessageUtils.message("mall.member.info.not.found"));
        }

        // 验证旧密码
        if (!BCrypt.checkpw(bo.getOldPassword(), member.getPassword())) {
            throw new MallException(MessageUtils.message("mall.member.password.not.match"));
        }

        // 更新密码
        MallMemberBo updateBo = new MallMemberBo();
        updateBo.setMemberId(memberId);
        updateBo.setPassword(BCrypt.hashpw(bo.getNewPassword()));

        boolean result = updateByBo(updateBo);
        if (!result) {
            throw new MallException(MessageUtils.message("mall.member.password.change.failed"));
        }

        log.info("用户ID: {} 修改密码成功", memberId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAvatar(Long memberId, String avatarUrl) {
        if (StringUtils.isBlank(avatarUrl)) {
            throw new MallException(MessageUtils.message("mall.member.avatar.url.empty"));
        }

        MallMemberBo updateBo = new MallMemberBo();
        updateBo.setMemberId(memberId);
        updateBo.setAvatar(avatarUrl);

        boolean result = updateByBo(updateBo);
        if (!result) {
            throw new MallException(MessageUtils.message("mall.member.avatar.upload.failed"));
        }

        log.info("用户ID: {} 更新头像成功", memberId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deactivateAccount(Long memberId, String password) {
        // 获取用户信息验证密码
        MallMember member = baseMapper.selectById(memberId);
        if (member == null) {
            throw new MallException(MessageUtils.message("mall.member.info.not.found"));
        }

        // 验证密码
        if (!BCrypt.checkpw(password, member.getPassword())) {
            throw new MallException(MessageUtils.message("mall.member.password.not.match"));
        }

        // 停用账户
        MallMemberBo updateBo = new MallMemberBo();
        updateBo.setMemberId(memberId);
        updateBo.setStatus(BusinessConstants.DISABLE); // 使用常量替代硬编码

        boolean result = updateByBo(updateBo);
        if (!result) {
            throw new MallException(MessageUtils.message("mall.member.deactivate.failed"));
        }

        // 退出登录
        MallLoginHelper.logout();
        log.info("用户ID: {} 注销账户成功", memberId);
    }

    @Override
    public void recordSuccessLogin(Long memberId, String email, String module, String clientId, String sessionId, String token) {
        loginLogService.recordSuccessLogin(memberId, email, module, clientId, sessionId, token);
    }
}
