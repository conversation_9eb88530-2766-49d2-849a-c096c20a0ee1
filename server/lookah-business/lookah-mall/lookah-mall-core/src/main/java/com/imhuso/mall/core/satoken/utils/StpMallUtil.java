package com.imhuso.mall.core.satoken.utils;

import cn.dev33.satoken.SaManager;
import cn.dev33.satoken.listener.SaTokenEventCenter;
import cn.dev33.satoken.jwt.StpLogicJwtForSimple;
import cn.dev33.satoken.stp.StpLogic;
import cn.dev33.satoken.stp.parameter.SaLoginParameter;
import com.imhuso.common.satoken.config.LoginTypeJwtConfig;
import com.imhuso.mall.core.constant.MallConstants;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Sa-Token 权限认证工具类 Mall版
 * 提供Mall模块专用的Sa-Token操作接口，自动读取配置创建专用StpLogic
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class StpMallUtil {

    /**
     * JWT密钥配置
     */
    private final LoginTypeJwtConfig loginTypeJwtConfig;

    /**
     * 构造函数，注入JWT配置
     */
    public StpMallUtil(LoginTypeJwtConfig loginTypeJwtConfig) {
        this.loginTypeJwtConfig = loginTypeJwtConfig;
    }

    /**
     * 多账号体系下的类型标识
     */
    public static final String TYPE = MallConstants.SaToken.LOGIN_TYPE;

    /**
     * 底层的 StpLogic 对象
     * -- GETTER --
     * 获取 StpLogic 对象
     */
    @Getter
    private static StpLogic stpLogic;

    /**
     * 初始化静态StpLogic实例
     * 自动读取配置创建Mall专用的StpLogic
     */
    @PostConstruct
    public void initMallStpLogic() {
        try {
            // 从配置中获取Mall专用的JWT密钥
            String jwtSecretKey = loginTypeJwtConfig.getJwtSecretKey(TYPE);

            // 创建Mall专用的StpLogic实例
            StpLogic storeStpLogic = new StpLogicJwtForSimple(TYPE) {
                @Override
                public String jwtSecretKey() {
                    // 直接返回配置的 JWT 密钥
                    return jwtSecretKey;
                }
            };

            // 设置为静态实例
            setStpLogic(storeStpLogic);
            log.info("Mall模块StpLogic初始化成功");
        } catch (Exception e) {
            log.error("StpMallUtil 初始化失败", e);
            throw new RuntimeException("Mall模块Sa-Token初始化失败", e);
        }
    }

    /**
     * 获取当前 StpLogic 的账号类型
     *
     * @return /
     */
    public static String getLoginType() {
        return stpLogic.getLoginType();
    }

    /**
     * 安全的重置 StpLogic 对象
     *
     * <br>
     * 1、更改此账户的 StpLogic 对象
     * <br>
     * 2、put 到全局 StpLogic 集合中
     * <br>
     * 3、发送日志
     *
     * @param newStpLogic /
     */
    public static void setStpLogic(StpLogic newStpLogic) {
        // 1、重置此账户的 StpLogic 对象
        stpLogic = newStpLogic;

        // 2、添加到全局 StpLogic 集合中
        // 以便可以通过 SaManager.getStpLogic(type) 的方式来全局获取到这个 StpLogic
        SaManager.putStpLogic(newStpLogic);

        // 3、$$ 发布事件：更新了 stpLogic 对象
        SaTokenEventCenter.doSetStpLogic(stpLogic);
    }

    /**
     * 获取当前请求的 token 值
     *
     * @return 当前tokenValue
     */
    public static String getTokenValue() {
        return stpLogic.getTokenValue();
    }

    // ------------------- 登录相关操作 -------------------

    // --- 登录

    /**
     * 会话登录
     *
     * @param id 账号id，建议的类型：（long | int | String）
     */
    public static void login(Object id) {
        stpLogic.login(id);
    }

    /**
     * 会话登录，并指定登录设备类型
     *
     * @param id     账号id，建议的类型：（long | int | String）
     * @param device 设备类型
     */
    public static void login(Object id, String device) {
        stpLogic.login(id, device);
    }

    /**
     * 会话登录，并指定是否 [记住我]
     *
     * @param id              账号id，建议的类型：（long | int | String）
     * @param isLastingCookie 是否为持久Cookie，值为 true 时记住我，值为 false 时关闭浏览器需要重新登录
     */
    public static void login(Object id, boolean isLastingCookie) {
        stpLogic.login(id, isLastingCookie);
    }

    /**
     * 会话登录，并指定此次登录 token 的有效期, 单位:秒
     *
     * @param id      账号id，建议的类型：（long | int | String）
     * @param timeout 此次登录 token 的有效期, 单位:秒
     */
    public static void login(Object id, long timeout) {
        stpLogic.login(id, timeout);
    }

    /**
     * 会话登录，并指定所有登录参数 Model
     *
     * @param id             账号id，建议的类型：（long | int | String）
     * @param loginParameter 此次登录的参数Model
     */
    public static void login(Object id, SaLoginParameter loginParameter) {
        stpLogic.login(id, loginParameter);
    }

    // 会话查询

    /**
     * 判断当前会话是否已经登录
     *
     * @return 已登录返回 true，未登录返回 false
     */
    public static boolean isLogin() {
        return stpLogic.isLogin();
    }

    /**
     * 检验当前会话是否已经登录，如未登录，则抛出异常
     */
    public static void checkLogin() {
        stpLogic.checkLogin();
    }

    /**
     * 获取当前 Token 的扩展信息（此函数只在jwt模式下生效）
     *
     * @param key 键值
     * @return 对应的扩展数据
     */
    public static Object getExtra(String key) {
        return stpLogic.getExtra(key);
    }

    /**
     * 获取当前会话 token 剩余有效时间（单位: 秒，返回 -1 代表永久有效，-2 代表没有这个值）
     *
     * @return token剩余有效时间
     */
    public static long getTokenTimeout() {
        return stpLogic.getTokenTimeout();
    }
}
