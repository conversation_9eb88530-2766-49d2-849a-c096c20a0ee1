package com.imhuso.mall.core.domain.bo;

import jakarta.validation.constraints.*;
import lombok.Data;
import com.imhuso.mall.core.constant.MallConstants;

import java.io.Serial;
import java.io.Serializable;

/**
 * 商城用户注册业务对象
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
public class MallRegisterBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 邮箱地址
     */
    @NotBlank(message = "{mall.validation.email.required}")
    @Email(message = "{mall.validation.email.invalid}")
    @Size(max = 100, message = "{mall.validation.email.max.length}")
    private String email;

    /**
     * 密码
     */
    @NotBlank(message = "{mall.validation.password.required}")
    @Size(min = 6, max = 20, message = "{mall.validation.password.length}")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{6,20}$",
             message = "{mall.validation.password.pattern}")
    private String password;

    /**
     * 确认密码
     */
    @NotBlank(message = "{mall.validation.confirm.password.required}")
    private String confirmPassword;

    /**
     * 全名
     */
    @Size(max = 100, message = "{mall.validation.full.name.max.length}")
    private String fullName;

    /**
     * 名字
     */
    @Size(max = 50, message = "{mall.validation.first.name.max.length}")
    private String firstName;

    /**
     * 姓氏
     */
    @Size(max = 50, message = "{mall.validation.last.name.max.length}")
    private String lastName;

    /**
     * 手机号码（可选）
     */
    private String phone;

    /**
     * 性别（0未知 1男 2女）
     */
    @Pattern(regexp = "^[012]$", message = "{mall.validation.gender.invalid}")
    private String gender;

    /**
     * 验证码
     */
    private String code;

    /**
     * 验证码类型
     */
    private String type;

    /**
     * 客户端ID（从请求头中获取）
     */
    private String clientId;

    /**
     * 授权类型
     */
    private String grantType = MallConstants.Module.NAME;

    /**
     * 是否同意用户协议
     */
    @AssertTrue(message = "{mall.validation.terms.required}")
    private Boolean agreeTerms;

    /**
     * 是否同意隐私政策
     */
    @AssertTrue(message = "{mall.validation.privacy.required}")
    private Boolean agreePrivacy;

    /**
     * 验证密码一致性
     */
    @AssertTrue(message = "{mall.validation.passwords.not.match}")
    public boolean isPasswordMatching() {
        return password != null && password.equals(confirmPassword);
    }
}
