package com.imhuso.mall.core.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.redis.utils.RedisUtils;
import com.imhuso.mall.core.exception.MallException;
import com.imhuso.mall.core.service.IMallCaptchaService;
import com.imhuso.mall.core.domain.vo.MallCaptchaConfigVo;
import com.imhuso.mall.core.service.ICloudflareService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * 商城验证码Service业务层处理
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MallCaptchaServiceImpl implements IMallCaptchaService {

    private final ICloudflareService cloudflareService;

    private static final String EMAIL_CODE_PREFIX = "mall:email:";
    private static final String SEND_LIMIT_PREFIX = "mall:limit:";

    // 验证码配置
    private static final int EMAIL_CODE_EXPIRE = 600; // 10分钟
    private static final int SEND_LIMIT_EXPIRE = 60; // 1分钟发送限制

    @Override
    public MallCaptchaConfigVo generateCaptcha() {
        // 只支持 Turnstile 验证码
        if (cloudflareService.isEnabled()) {
            return new MallCaptchaConfigVo(
                "turnstile",
                cloudflareService.getSiteKey(),
                0  // Turnstile不需要过期时间
            );
        }

        // Turnstile 未启用时返回禁用状态
        return new MallCaptchaConfigVo(
            "none",
            "",
            0
        );
    }

    @Override
    public boolean validateCaptcha(String type, String code) {
        if (StrUtil.isBlank(type) || StrUtil.isBlank(code)) {
            return false;
        }

        // 只支持 Turnstile 验证码
        if ("turnstile".equals(type)) {
            return cloudflareService.verifyTurnstile(code);
        }

        // 如果 Turnstile 未启用，跳过验证
        if (!cloudflareService.isEnabled()) {
            log.debug("Turnstile未启用，跳过验证码验证");
            return true;
        }

        // 不支持的验证码类型
        log.warn("不支持的验证码类型: type={}", type);
        return false;
    }

    @Override
    public boolean sendEmailCode(String email, String type) {
        if (StrUtil.isBlank(email) || StrUtil.isBlank(type)) {
            return false;
        }

        // 检查发送频率限制
        if (!checkSendLimit(email, type)) {
            throw new MallException(MessageUtils.message("mall.captcha.too.frequent"));
        }

        try {
            // 生成6位数字验证码
            String code = RandomUtil.randomNumbers(6);

            // 存储到Redis
            String key = EMAIL_CODE_PREFIX + type + ":" + email;
            RedisUtils.setCacheObject(key, code, Duration.ofSeconds(EMAIL_CODE_EXPIRE));

            // 设置发送限制
            setSendLimit(email, type);

            // 发送邮件验证码
            sendEmailCodeActual(email, code, type);
            log.info("发送邮箱验证码: email={}, type={}", email, type);

            return true;

        } catch (Exception e) {
            log.error("发送邮箱验证码失败: email={}, type={}, error={}", email, type, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean validateEmailCode(String email, String code, String type) {
        if (StrUtil.isBlank(email) || StrUtil.isBlank(code) || StrUtil.isBlank(type)) {
            return false;
        }

        try {
            String key = EMAIL_CODE_PREFIX + type + ":" + email;
            String cachedCode = RedisUtils.getCacheObject(key);

            if (StrUtil.isBlank(cachedCode)) {
                return false;
            }

            // 验证成功后删除验证码
            boolean valid = code.equals(cachedCode);
            if (valid) {
                RedisUtils.deleteObject(key);
            }

            return valid;

        } catch (Exception e) {
            log.error("验证邮箱验证码失败: email={}, type={}, error={}", email, type, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean checkSendLimit(String target, String type) {
        String key = SEND_LIMIT_PREFIX + type + ":" + target;
        return !RedisUtils.hasKey(key);
    }

    /**
     * 设置发送限制
     */
    private void setSendLimit(String target, String type) {
        String key = SEND_LIMIT_PREFIX + type + ":" + target;
        RedisUtils.setCacheObject(key, "1", Duration.ofSeconds(SEND_LIMIT_EXPIRE));
    }

    /**
     * 实际发送邮件验证码
     */
    private void sendEmailCodeActual(String email, String code, String type) {
        // 实际的邮件发送逻辑
        // 这里可以集成邮件服务提供商，如阿里云邮件推送、腾讯云邮件等
        log.debug("邮件验证码发送: email={}, code={}, type={}", email, code, type);
    }


    @Override
    public int cleanExpiredCodes() {
        // Redis会自动清理过期的key，这里返回0
        return 0;
    }
}
