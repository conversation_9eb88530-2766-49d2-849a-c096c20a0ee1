<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imhuso.mall.core.mapper.MallMemberSocialMapper">

    <resultMap type="com.imhuso.mall.core.domain.MallMemberSocial" id="MallMemberSocialResult">
        <id property="id" column="id"/>
        <result property="memberId" column="member_id"/>
        <result property="provider" column="provider"/>
        <result property="providerUserId" column="provider_user_id"/>
        <result property="providerUsername" column="provider_username"/>
        <result property="providerEmail" column="provider_email"/>
        <result property="providerAvatar" column="provider_avatar"/>
        <result property="providerProfile" column="provider_profile"/>
        <result property="accessToken" column="access_token"/>
        <result property="refreshToken" column="refresh_token"/>
        <result property="expiresIn" column="expires_in"/>
        <result property="tokenExpiresAt" column="token_expires_at"/>
        <result property="scope" column="scope"/>
        <result property="isPrimary" column="is_primary"/>
        <result property="status" column="status"/>
        <result property="firstLoginTime" column="first_login_time"/>
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="loginCount" column="login_count"/>
        <result property="createDept" column="create_dept"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="version" column="version"/>
    </resultMap>

    <sql id="selectMallMemberSocialVo">
        select id, member_id, provider, provider_user_id, provider_username, provider_email,
               provider_avatar, provider_profile, access_token, refresh_token, expires_in, token_expires_at,
               scope, is_primary, status, first_login_time, last_login_time, login_count,
               create_dept, create_by, create_time, update_by, update_time, del_flag, version
        from mall_member_social
    </sql>

    <select id="selectByProviderAndUserId" resultMap="MallMemberSocialResult">
        <include refid="selectMallMemberSocialVo"/>
        where provider = #{provider} and provider_user_id = #{providerUserId} and del_flag = '0'
    </select>

    <select id="selectByMemberId" resultMap="MallMemberSocialResult">
        <include refid="selectMallMemberSocialVo"/>
        where member_id = #{memberId} and del_flag = '0'
        order by is_primary desc, create_time desc
    </select>

    <select id="selectByMemberIdAndProvider" resultMap="MallMemberSocialResult">
        <include refid="selectMallMemberSocialVo"/>
        where member_id = #{memberId} and provider = #{provider} and del_flag = '0'
    </select>

    <update id="updateTokenInfo">
        update mall_member_social
        set access_token = #{accessToken},
            refresh_token = #{refreshToken},
            expires_in = #{expiresIn},
            token_expires_at = #{tokenExpiresAt},
            update_time = now()
        where id = #{id} and del_flag = '0'
    </update>

    <update id="updateLastLoginTime">
        update mall_member_social
        set last_login_time = #{lastLoginTime},
            update_time = now()
        where id = #{id} and del_flag = '0'
    </update>

    <update id="incrementLoginCount">
        update mall_member_social
        set login_count = login_count + 1,
            update_time = now()
        where id = #{id} and del_flag = '0'
    </update>

    <update id="setPrimaryAccount">
        update mall_member_social
        set is_primary = '1',
            update_time = now()
        where member_id = #{memberId} and provider = #{provider} and del_flag = '0'
    </update>

    <update id="unsetPrimaryAccount">
        update mall_member_social
        set is_primary = '0',
            update_time = now()
        where member_id = #{memberId} and del_flag = '0'
    </update>

    <update id="unbindSocialAccount">
        update mall_member_social
        set status = '0',
            update_time = now()
        where member_id = #{memberId} and provider = #{provider} and del_flag = '0'
    </update>

    <update id="bindSocialAccount">
        update mall_member_social
        set status = '1',
            update_time = now()
        where member_id = #{memberId} and provider = #{provider} and del_flag = '0'
    </update>

</mapper>
