<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>lookah-mall</artifactId>
        <groupId>com.imhuso</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>lookah-mall-core</artifactId>

    <description>
        商城核心业务模块 - 包含商品、会员、订单、购物车、支付、促销、物流等业务域
    </description>

    <dependencies>
        <!-- 业务共用模块 - 跨业务域通用的核心功能和实体 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-business-common-core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 公共核心模块 - 系统基础工具类、常量、枚举等 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-core</artifactId>
        </dependency>
        
        <!-- MyBatis Plus 数据访问层模块 - 数据库操作、分页、查询等 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-mybatis</artifactId>
        </dependency>
        
        <!-- Redis 缓存模块 - 分布式缓存、会话管理、限流等 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-redis</artifactId>
        </dependency>
        
        <!-- 安全模块 - 数据脱敏、权限校验、安全工具等 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-security</artifactId>
        </dependency>
        
        <!-- Sa-Token 认证授权模块 - 登录认证、权限管理、会话控制等 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-satoken</artifactId>
        </dependency>
        
        <!-- 邮件发送模块 - 邮件模板、发送队列、通知服务等 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-mail</artifactId>
        </dependency>
        
        <!-- JSON 处理模块 - JSON 序列化、反序列化、格式化等 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-json</artifactId>
        </dependency>
        
        <!-- 加密解密模块 - 数据加密、密码散列、证书管理等 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-encrypt</artifactId>
        </dependency>

        <!-- Hutool 加密工具 - 提供多种加密算法和工具方法 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
        </dependency>
        
        <!-- 多语言翻译模块 - 国际化支持、语言切换、文本翻译等 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-translation</artifactId>
        </dependency>
        
        <!-- Excel 处理模块 - 数据导入导出、报表生成、模板处理等 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-excel</artifactId>
        </dependency>
        
        <!-- Web 公共模块 - 请求响应处理、异常处理、过滤器等 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-web</artifactId>
        </dependency>
        
        <!-- 客户端模块 - HTTP 客户端、API 调用、第三方接口等 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-client</artifactId>
        </dependency>

        <!-- Spring Boot 核心启动器 - 基础的 Spring Boot 功能 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        
        <!-- Spring Boot 数据验证启动器 - Bean Validation、参数校验等 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- MyBatis Plus Spring Boot 3 启动器 - ORM 框架、代码生成、条件构造器等 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>

        <!-- Spring Boot 配置处理器 - 配置文件提示、元数据生成等 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        
        <!-- 日志模块 - 日志配置、格式化、输出管理等 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-log</artifactId>
        </dependency>
    </dependencies>

</project>
