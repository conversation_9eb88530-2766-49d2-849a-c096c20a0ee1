<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>lookah-mall</artifactId>
        <groupId>com.imhuso</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>lookah-mall-api</artifactId>

    <description>
        商城API模块 - 包含前台和后台API接口
    </description>

    <dependencies>
        <!-- 零售商城业务核心模块 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-mall-core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 公共模块 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-encrypt</artifactId>
        </dependency>
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-social</artifactId>
        </dependency>
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-client</artifactId>
        </dependency>

        <!-- Google Auth Library for JWT verification -->
        <dependency>
            <groupId>com.google.auth</groupId>
            <artifactId>google-auth-library-oauth2-http</artifactId>
        </dependency>

        <!-- Spring Boot -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- 配置处理器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-mybatis</artifactId>
        </dependency>
    </dependencies>

</project>
