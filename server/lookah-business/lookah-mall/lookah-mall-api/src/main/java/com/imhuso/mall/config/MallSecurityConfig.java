package com.imhuso.mall.config;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import com.imhuso.common.core.config.ModuleConfigProperties;
import com.imhuso.common.core.utils.ServletUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.satoken.utils.LoginHelper;
import com.imhuso.common.security.config.ModuleSecurityAutoConfiguration;
import com.imhuso.mall.core.constant.MallConstants;
import com.imhuso.mall.core.satoken.utils.StpMallUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 商城业务安全配置
 * <p>
 * 基于统一的模块化配置系统，使用 StpMallUtil 进行商城会员认证
 * 配置信息来源于 modules.yml 中的 mall_front 模块配置
 *
 * <AUTHOR>
 */
@Slf4j
@AutoConfiguration
public class MallSecurityConfig extends ModuleSecurityAutoConfiguration.BaseModuleSecurityConfig
    implements WebMvcConfigurer {

    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(MallSecurityConfig.class);

    public MallSecurityConfig(ModuleConfigProperties moduleConfigProperties) {
        super(moduleConfigProperties);
    }

    @Override
    public void addInterceptors(@NonNull InterceptorRegistry registry) {
        // 获取商城模块的拦截路径
        String[] interceptPatterns = getInterceptPatterns(getCurrentSecurityModule());
        if (interceptPatterns.length == 0) {
            logger.warn("商城模块未配置拦截路径，跳过安全配置");
            return;
        }

        // 获取排除路径
        String[] excludePatterns = getModuleExcludes(getCurrentSecurityModule());

        logger.info("初始化商城业务安全拦截器");
        logger.info("拦截路径: {}", String.join(", ", interceptPatterns));
        logger.info("排除路径: {}", String.join(", ", excludePatterns));

        // 注册路由拦截器，使用商城专用的认证逻辑
        registry.addInterceptor(new SaInterceptor(handler -> {
                SaRouter
                    // 匹配需要验证的路径
                    .match(interceptPatterns)
                    // 对未排除的路径进行检查
                    .check(() -> {
                        HttpServletRequest request = ServletUtils.getRequest();
                        // 使用 StpMallUtil 检查商城会员登录状态
                        try {
                            StpMallUtil.checkLogin();
                        } catch (NotLoginException e) {
                            logger.debug("商城会员未登录，请求路径: {}", request != null ? request.getRequestURI() : "unknown");
                            throw e;
                        }

                        // 检查 header 与 param 里的 clientId 与 token 里的是否一致
                        String headerCid = request != null ? request.getHeader(LoginHelper.CLIENT_ID) : null;
                        String paramCid = ServletUtils.getParameter(LoginHelper.CLIENT_ID);
                        Object clientIdObj = StpMallUtil.getExtra(LoginHelper.CLIENT_ID);
                        String clientId = clientIdObj != null ? clientIdObj.toString() : null;
                        if (!StringUtils.equalsAny(clientId, headerCid, paramCid)) {
                            // token 无效
                            throw NotLoginException.newInstance(StpMallUtil.getLoginType(),
                                "-100", "客户端ID与Token不匹配",
                                StpMallUtil.getTokenValue());
                        }
                    });
            })).addPathPatterns("/**")
            // 使用智能排除路径（自动排除其他模块）
            .excludePathPatterns(excludePatterns);
    }

    @Override
    protected String getCurrentSecurityModule() {
        return MallConstants.Module.NAME;
    }
}
