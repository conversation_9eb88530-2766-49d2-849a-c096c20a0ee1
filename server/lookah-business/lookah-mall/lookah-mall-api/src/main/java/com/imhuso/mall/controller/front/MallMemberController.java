package com.imhuso.mall.controller.front;

import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.mall.core.domain.bo.MallChangePasswordBo;
import com.imhuso.mall.core.domain.bo.MallMemberBo;
import com.imhuso.mall.core.domain.vo.MallMemberVo;
import com.imhuso.mall.core.exception.MallException;
import com.imhuso.mall.core.satoken.MallLoginHelper;
import com.imhuso.mall.core.service.IMallMemberService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 商城会员信息控制器
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/member")
public class MallMemberController extends BaseController {

    private final IMallMemberService mallMemberService;

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/profile")
    public R<MallMemberVo> getProfile() {
        Long memberId = getCurrentMemberId();
        MallMemberVo memberVo = mallMemberService.queryById(memberId);
        if (memberVo == null) {
            throw new MallException(MessageUtils.message("mall.member.info.not.found"));
        }
        return R.ok(memberVo);
    }

    /**
     * 更新用户信息
     *
     * @param memberBo 用户信息
     * @return 更新结果
     */
    @PutMapping("/profile")
    public R<Void> updateProfile(@Validated(EditGroup.class) @RequestBody MallMemberBo memberBo) {
        Long memberId = getCurrentMemberId();
        // 设置当前用户ID，防止越权修改
        memberBo.setMemberId(memberId);

        boolean result = mallMemberService.updateByBo(memberBo);
        return result ? R.ok(MessageUtils.message("mall.member.update.success")) :
            R.failed(MessageUtils.message("mall.member.update.failed"));
    }

    /**
     * 修改密码
     *
     * @param bo 修改密码请求
     * @return 修改结果
     */
    @PutMapping("/change-password")
    @Log(title = "修改密码", businessType = BusinessType.UPDATE)
    public R<Void> changePassword(@Valid @RequestBody MallChangePasswordBo bo) {
        Long memberId = getCurrentMemberId();
        mallMemberService.changePassword(memberId, bo);
        return R.ok(MessageUtils.message("mall.member.password.change.success"));
    }

    /**
     * 验证邮箱
     *
     * @param code 验证码
     * @return 验证结果
     */
    @PostMapping("/verify-email")
    @Log(title = "验证邮箱", businessType = BusinessType.UPDATE)
    public R<Void> verifyEmail(@RequestParam String code) {
        Long memberId = getCurrentMemberId();
        mallMemberService.verifyEmailWithCode(memberId, code);
        return R.ok(MessageUtils.message("mall.member.email.verify.success"));
    }


    /**
     * 上传头像
     *
     * @param avatarUrl 头像URL
     * @return 上传结果
     */
    @PostMapping("/upload-avatar")
    @Log(title = "上传头像", businessType = BusinessType.UPDATE)
    public R<String> uploadAvatar(@RequestParam String avatarUrl) {
        Long memberId = getCurrentMemberId();
        mallMemberService.updateAvatar(memberId, avatarUrl);
        return R.ok(MessageUtils.message("mall.member.avatar.upload.success"), avatarUrl);
    }

    /**
     * 注销账户
     *
     * @param password 用户密码
     * @return 注销结果
     */
    @DeleteMapping("/deactivate")
    @Log(title = "注销账户", businessType = BusinessType.DELETE)
    public R<Void> deactivateAccount(@RequestParam String password) {
        Long memberId = getCurrentMemberId();
        mallMemberService.deactivateAccount(memberId, password);
        return R.ok(MessageUtils.message("mall.member.deactivate.success"));
    }

    /**
     * 获取当前登录用户ID
     *
     * @return 用户ID
     */
    private Long getCurrentMemberId() {
        Long memberId = MallLoginHelper.getUserId();
        if (memberId == null) {
            throw new MallException(MessageUtils.message("mall.member.not.logged.in"));
        }
        return memberId;
    }
}
