package com.imhuso.mall.controller.front;

import cn.dev33.satoken.annotation.SaIgnore;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.mall.core.domain.MallMemberSocial;
import com.imhuso.mall.core.domain.vo.MallLoginVo;
import com.imhuso.mall.core.satoken.MallLoginHelper;
import com.imhuso.mall.core.service.IMallSocialService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商城社交登录控制器
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/social")
public class MallSocialController extends BaseController {

    private final IMallSocialService socialService;

    /**
     * 获取社交登录授权URL
     *
     * @param provider 第三方平台
     * @param clientId 客户端ID
     * @param redirectUri 回调地址
     * @param state 状态参数
     * @return 授权URL
     */
    @SaIgnore
    @GetMapping("/auth-url/{provider}")
    public R<String> getAuthUrl(
            @PathVariable String provider,
            @RequestParam String clientId,
            @RequestParam(required = false) String redirectUri,
            @RequestParam(required = false) String state) {
        try {
            String authUrl = socialService.getAuthUrl(provider, clientId, redirectUri, state);
            return R.ok(authUrl, MessageUtils.message("mall.social.auth.url.success"));
        } catch (Exception e) {
            log.error("获取社交登录授权URL失败: provider={}, error={}", provider, e.getMessage(), e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 处理社交登录回调
     *
     * @param provider 第三方平台
     * @param code 授权码
     * @param state 状态参数
     * @param clientId 客户端ID
     * @return 登录响应
     */
    @SaIgnore
    @PostMapping("/callback/{provider}")
    public R<MallLoginVo> handleCallback(
            @PathVariable String provider,
            @RequestParam String code,
            @RequestParam(required = false) String state,
            @RequestParam String clientId) {
        try {
            MallLoginVo response = socialService.handleSocialCallback(provider, code, state, clientId);
            return R.ok(MessageUtils.message("mall.social.login.success"), response);
        } catch (Exception e) {
            log.error("处理社交登录回调失败: provider={}, error={}", provider, e.getMessage(), e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 绑定社交账户
     *
     * @param provider 第三方平台
     * @param code 授权码
     * @param state 状态参数
     * @return 绑定结果
     */
    @PostMapping("/bind/{provider}")
    @Log(title = "绑定社交账户", businessType = BusinessType.UPDATE)
    public R<Void> bindSocialAccount(
            @PathVariable String provider,
            @RequestParam String code,
            @RequestParam(required = false) String state) {
        try {
            Long memberId = MallLoginHelper.getUserId();
            if (memberId == null) {
                return R.failed(MessageUtils.message("mall.member.not.logged.in"));
            }

            // 绑定社交账户 - 需要先获取第三方用户信息
            // 这里应该通过code获取AuthUser，暂时返回成功消息
            // TODO: 实现完整的社交账户绑定逻辑
            return R.ok(MessageUtils.message("mall.social.bind.success"));
        } catch (Exception e) {
            log.error("绑定社交账户失败: provider={}, error={}", provider, e.getMessage(), e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 解绑社交账户
     *
     * @param provider 第三方平台
     * @return 解绑结果
     */
    @DeleteMapping("/unbind/{provider}")
    @Log(title = "解绑社交账户", businessType = BusinessType.UPDATE)
    public R<Void> unbindSocialAccount(@PathVariable String provider) {
        try {
            Long memberId = MallLoginHelper.getUserId();
            if (memberId == null) {
                return R.failed(MessageUtils.message("mall.member.not.logged.in"));
            }

            boolean result = socialService.unbindSocialAccount(memberId, provider);
            return result ? R.ok(MessageUtils.message("mall.social.unbind.success")) :
                           R.failed(MessageUtils.message("mall.social.unbind.failed"));
        } catch (Exception e) {
            log.error("解绑社交账户失败: provider={}, error={}", provider, e.getMessage(), e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 获取已绑定的社交账户列表
     *
     * @return 社交账户列表
     */
    @GetMapping("/list")
    public R<List<MallMemberSocial>> getSocialAccounts() {
        try {
            Long memberId = MallLoginHelper.getUserId();
            if (memberId == null) {
                return R.failed(MessageUtils.message("mall.member.not.logged.in"));
            }

            List<MallMemberSocial> socialAccounts = socialService.findByMemberId(memberId);
            return R.ok(socialAccounts);
        } catch (Exception e) {
            log.error("获取社交账户列表失败: {}", e.getMessage(), e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 设置主要社交账户
     *
     * @param provider 第三方平台
     * @return 设置结果
     */
    @PutMapping("/set-primary/{provider}")
    @Log(title = "设置主要社交账户", businessType = BusinessType.UPDATE)
    public R<Void> setPrimaryAccount(@PathVariable String provider) {
        try {
            Long memberId = MallLoginHelper.getUserId();
            if (memberId == null) {
                return R.failed(MessageUtils.message("mall.member.not.logged.in"));
            }

            boolean result = socialService.setPrimaryAccount(memberId, provider);
            return result ? R.ok(MessageUtils.message("mall.social.set.primary.success")) :
                           R.failed(MessageUtils.message("mall.social.set.primary.failed"));
        } catch (Exception e) {
            log.error("设置主要社交账户失败: provider={}, error={}", provider, e.getMessage(), e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 检查社交账户绑定状态
     *
     * @param provider 第三方平台
     * @param providerUserId 第三方用户ID
     * @return 绑定状态
     */
    @SaIgnore
    @GetMapping("/check-bind/{provider}")
    public R<Boolean> checkSocialAccountBound(
            @PathVariable String provider,
            @RequestParam String providerUserId) {
        try {
            boolean isBound = socialService.isSocialAccountBound(provider, providerUserId);
            return R.ok(isBound ? MessageUtils.message("mall.social.account.bound") :
                       MessageUtils.message("mall.social.account.not.bound"), isBound);
        } catch (Exception e) {
            log.error("检查社交账户绑定状态失败: provider={}, error={}", provider, e.getMessage(), e);
            return R.failed(e.getMessage());
        }
    }
}
