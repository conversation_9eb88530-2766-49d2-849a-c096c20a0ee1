package com.imhuso.mall.controller.front;

import cn.dev33.satoken.annotation.SaIgnore;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.utils.EnvironmentUtils;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.core.utils.ServletUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.core.validate.AddGroup;

import com.imhuso.common.web.core.BaseController;
import com.imhuso.mall.core.domain.bo.MallLoginBo;
import com.imhuso.mall.core.domain.bo.MallRegisterBo;
import com.imhuso.mall.core.domain.vo.MallCaptchaConfigVo;
import com.imhuso.mall.core.domain.vo.MallLoginVo;
import com.imhuso.mall.core.satoken.MallLoginHelper;
import com.imhuso.mall.core.security.MallClientValidator;
import com.imhuso.mall.core.service.IMallCaptchaService;
import com.imhuso.mall.core.service.IMallMemberService;
import com.imhuso.mall.core.service.IMallSecurityService;
import com.imhuso.mall.core.service.ICloudflareService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 商城认证控制器
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/auth")
public class MallAuthController extends BaseController {

    private final IMallMemberService mallMemberService;
    private final IMallCaptchaService captchaService;
    private final IMallSecurityService securityService;
    private final ICloudflareService cloudflareService;

    /**
     * 用户注册
     *
     * @param request 注册请求参数
     * @return 注册结果
     */
    @SaIgnore
    @PostMapping("/register")
    public R<MallLoginVo> register(@Validated(AddGroup.class) @RequestBody MallRegisterBo request) {
        try {
            // 从请求头中获取clientId
            String clientId = extractClientId();
            if (StringUtils.isBlank(clientId)) {
                return R.failed(MessageUtils.message("mall.auth.client.id.invalid"));
            }
            request.setClientId(clientId);

            // 根据配置验证验证码
            if (cloudflareService.isEnabled()) {
                if (!captchaService.validateCaptcha(request.getType(), request.getCode())) {
                    return R.failed(MessageUtils.message("mall.captcha.invalid.or.expired"));
                }
            }

            MallLoginVo response = mallMemberService.register(request);
            return R.ok(MessageUtils.message("mall.auth.registration.successful"), response);
        } catch (Exception e) {
            log.error("用户注册失败: {}", e.getMessage(), e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 用户登录
     *
     * @param request 登录请求参数
     * @return 登录结果
     */
    @SaIgnore
    @PostMapping("/login")
    public R<MallLoginVo> login(@Valid @RequestBody MallLoginBo request) {
        try {
            String ipAddress = ServletUtils.getClientIP();

            // 从请求头中获取clientId
            String clientId = extractClientId();
            if (StringUtils.isBlank(clientId)) {
                return R.failed(MessageUtils.message("mall.auth.client.id.invalid"));
            }
            request.setClientId(clientId);

            // 检查登录频率限制，如果是开发环境则不需要检查
            if (!EnvironmentUtils.isDevelopmentEnvironment()
                    && !securityService.checkLoginRateLimit(request.getEmail(), ipAddress)) {
                return R.failed(MessageUtils.message("mall.security.too.many.attempts"));
            }

            // 根据配置验证验证码
            if (cloudflareService.isEnabled()) {
                if (!captchaService.validateCaptcha(request.getType(), request.getCode())) {
                    return R.failed(MessageUtils.message("mall.captcha.invalid.or.expired"));
                }
            }

            MallLoginVo response = mallMemberService.login(request);

            // 记录登录成功
            securityService.recordLoginAttempt(request.getEmail(), ipAddress, true);

            return R.ok(MessageUtils.message("mall.auth.login.successful"), response);
        } catch (Exception e) {
            log.error("用户登录失败: {}", e.getMessage(), e);

            // 记录登录失败
            String ipAddress = ServletUtils.getClientIP();
            securityService.recordLoginAttempt(request.getEmail(), ipAddress, false);

            return R.failed(e.getMessage());
        }
    }

    /**
     * 用户退出登录
     *
     * @return 退出结果
     */
    @PostMapping("/logout")
    public R<Void> logout() {
        try {
            mallMemberService.logout();
            return R.ok(MessageUtils.message("mall.auth.logout.successful"));
        } catch (Exception e) {
            log.error("用户退出失败: {}", e.getMessage(), e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 检查邮箱是否可用
     *
     * @param email 邮箱地址
     * @return 是否可用
     */
    @SaIgnore
    @GetMapping("/check-email")
    public R<Boolean> checkEmail(@RequestParam String email) {
        try {
            boolean available = mallMemberService.checkEmailUnique(email, null);
            return R.ok(available ? MessageUtils.message("mall.member.email.available")
                    : MessageUtils.message("mall.member.email.already.registered"), available);
        } catch (Exception e) {
            log.error("检查邮箱失败: {}", e.getMessage(), e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 检查手机号是否可用
     *
     * @param phone 手机号码
     * @return 是否可用
     */
    @SaIgnore
    @GetMapping("/check-phone")
    public R<Boolean> checkPhone(@RequestParam String phone) {
        try {
            boolean available = mallMemberService.checkPhoneUnique(phone, null);
            return R.ok(available ? MessageUtils.message("mall.member.phone.available")
                    : MessageUtils.message("mall.member.phone.already.registered"), available);
        } catch (Exception e) {
            log.error("检查手机号失败: {}", e.getMessage(), e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 获取验证码配置信息
     *
     * @return 验证码配置
     */
    @SaIgnore
    @GetMapping("/captcha/config")
    public R<MallCaptchaConfigVo> getCaptchaConfig() {
        try {
            MallCaptchaConfigVo result = captchaService.generateCaptcha();
            return R.ok(result);
        } catch (Exception e) {
            log.error("获取验证码配置失败: {}", e.getMessage(), e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 发送邮箱验证码
     *
     * @param email 邮箱地址
     * @param type  验证码类型
     * @return 发送结果
     */
    @SaIgnore
    @PostMapping("/send-email-code")
    public R<Void> sendEmailCode(
            @RequestParam String email,
            @RequestParam(defaultValue = "register") String type) {
        try {
            boolean result = captchaService.sendEmailCode(email, type);
            return result ? R.ok(MessageUtils.message("mall.captcha.verification.code.sent"))
                    : R.failed(MessageUtils.message("mall.captcha.send.failed"));
        } catch (Exception e) {
            log.error("发送邮箱验证码失败: {}", e.getMessage(), e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 检查登录状态
     *
     * @return 登录状态
     */
    @GetMapping("/check-login")
    public R<Boolean> checkLogin() {
        try {
            boolean isLogin = MallLoginHelper.isLogin();
            return R.ok(isLogin ? MessageUtils.message("mall.member.logged.in")
                    : MessageUtils.message("mall.member.not.logged.in"), isLogin);
        } catch (Exception e) {
            log.error("检查登录状态失败: {}", e.getMessage(), e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 检查是否需要验证码
     * 根据 Turnstile 配置决定是否需要验证码
     *
     * @return 是否需要验证码
     */
    @SaIgnore
    @GetMapping("/check-captcha")
    public R<Boolean> checkCaptchaRequired() {
        try {
            // 根据 Turnstile 配置决定是否需要验证码
            boolean required = cloudflareService.isEnabled();

            String message = required ? MessageUtils.message("mall.captcha.required")
                    : MessageUtils.message("mall.captcha.not.required");

            return R.ok(message, required);
        } catch (Exception e) {
            log.error("检查验证码需求失败: {}", e.getMessage(), e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 从请求头中提取clientId
     * @return clientId，如果为空则返回null
     */
    private String extractClientId() {
        try {
            return MallClientValidator.extractClientId(Objects.requireNonNull(ServletUtils.getRequest()));
        } catch (Exception e) {
            log.warn("提取clientId失败: {}", e.getMessage());
            return null;
        }
    }
}
