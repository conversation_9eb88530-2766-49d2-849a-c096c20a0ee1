package com.imhuso.crm.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.excel.utils.ExcelUtil;
import com.imhuso.common.idempotent.annotation.RepeatSubmit;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.crm.core.domain.bo.CrmOrderBo;
import com.imhuso.crm.core.domain.vo.CrmOrderVo;
import com.imhuso.crm.core.service.ICrmOrderService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * CRM客户订单管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("crm/order")
public class CrmOrderController extends BaseController {

    private final ICrmOrderService crmOrderService;

    /**
     * 查询CRM客户订单列表
     */
    @SaCheckPermission("crm:order:list")
    @GetMapping("/list")
    public TableDataInfo<CrmOrderVo> list(CrmOrderBo bo, PageQuery pageQuery) {
        return crmOrderService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询我的客户订单列表
     */
    @SaCheckPermission("crm:order:list")
    @GetMapping("/my-list")
    public TableDataInfo<CrmOrderVo> myList(CrmOrderBo bo, PageQuery pageQuery) {
        return crmOrderService.queryMyPageList(bo, pageQuery);
    }

    /**
     * 导出CRM客户订单列表
     */
    @SaCheckPermission("crm:order:export")
    @Log(title = "CRM客户订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CrmOrderBo bo, HttpServletResponse response) {
        List<CrmOrderVo> list = crmOrderService.queryList(bo);
        ExcelUtil.exportExcel(list, "CRM客户订单", CrmOrderVo.class, response);
    }

    /**
     * 获取CRM客户订单详细信息
     */
    @SaCheckPermission("crm:order:query")
    @GetMapping("/{orderId}")
    public R<CrmOrderVo> getInfo(@NotNull(message = "主键不能为空")
                                 @PathVariable Long orderId) {
        return R.ok(crmOrderService.queryById(orderId));
    }

    /**
     * 新增CRM客户订单
     */
    @SaCheckPermission("crm:order:add")
    @Log(title = "CRM客户订单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CrmOrderBo bo) {
        return toAjax(crmOrderService.insertByBo(bo));
    }

    /**
     * 修改CRM客户订单
     */
    @SaCheckPermission("crm:order:edit")
    @Log(title = "CRM客户订单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CrmOrderBo bo) {
        return toAjax(crmOrderService.updateByBo(bo));
    }

    /**
     * 删除CRM客户订单
     */
    @SaCheckPermission("crm:order:remove")
    @Log(title = "CRM客户订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] orderIds) {
        return toAjax(crmOrderService.deleteWithValidByIds(List.of(orderIds), true));
    }

    /**
     * 更新订单状态
     */
    @SaCheckPermission("crm:order:edit")
    @Log(title = "更新订单状态", businessType = BusinessType.UPDATE)
    @PutMapping("/{orderId}/status")
    public R<Void> updateStatus(@NotNull(message = "订单ID不能为空")
                                @PathVariable Long orderId,
                                @NotNull(message = "订单状态不能为空")
                                @RequestParam String orderStatus,
                                @RequestParam(required = false) String remark) {
        return toAjax(crmOrderService.updateOrderStatus(orderId, orderStatus, remark));
    }

    /**
     * 生成订单号
     */
    @SaCheckPermission("crm:order:add")
    @GetMapping("/generate-order-no")
    public R<String> generateOrderNo() {
        return R.ok(crmOrderService.generateOrderNo());
    }

}
