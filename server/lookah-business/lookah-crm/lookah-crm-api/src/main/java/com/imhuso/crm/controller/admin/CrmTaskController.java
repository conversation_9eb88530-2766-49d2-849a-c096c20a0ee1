package com.imhuso.crm.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.web.core.BaseController;

import com.imhuso.crm.core.service.ICrmCustomerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;



/**
 * CRM定时任务管理Controller
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/crm/task")
@Tag(name = "CRM定时任务管理", description = "CRM定时任务管理")
public class CrmTaskController extends BaseController {

    private final ICrmCustomerService crmCustomerService;

    /**
     * 未跟进天数阈值：15天
     */
    private static final int DAYS_THRESHOLD = 15;



    /**
     * 手动触发自动转换公海客户任务
     * 将15天未跟进的客户转为公海客户
     */
    @SaCheckPermission("crm:task:execute")
    @Log(title = "手动触发自动转换公海客户", businessType = BusinessType.OTHER)
    @PostMapping("/auto-convert/trigger")
    @Operation(summary = "手动触发自动转换公海客户任务")
    public R<String> triggerAutoConvert() {
        try {
            int convertedCount = crmCustomerService.autoConvertToPublicCustomers(DAYS_THRESHOLD);
            String message = String.format("手动触发成功，共转换 %d 个客户为公海客户（未跟进天数：%d 天）", convertedCount, DAYS_THRESHOLD);
            log.info(message);
            return R.ok(message);
        } catch (Exception e) {
            log.error("手动触发自动转换公海客户任务失败", e);
            return R.failed("手动触发失败：" + e.getMessage());
        }
    }


}
