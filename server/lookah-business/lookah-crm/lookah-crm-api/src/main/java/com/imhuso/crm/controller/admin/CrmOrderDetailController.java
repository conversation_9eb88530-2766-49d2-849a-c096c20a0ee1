package com.imhuso.crm.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.excel.utils.ExcelUtil;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.crm.core.domain.bo.CrmOrderDetailBo;
import com.imhuso.crm.core.domain.vo.CrmOrderDetailVo;
import com.imhuso.crm.core.service.ICrmOrderDetailService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * CRM订单详情管理（销售数据）
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("crm/order-detail")
public class CrmOrderDetailController extends BaseController {

    private final ICrmOrderDetailService crmOrderDetailService;

    /**
     * 查询CRM订单详情列表（销售数据）
     */
    @SaCheckPermission("crm:sales:data:list")
    @GetMapping("/list")
    public TableDataInfo<CrmOrderDetailVo> list(CrmOrderDetailBo bo, PageQuery pageQuery) {
        return crmOrderDetailService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出CRM订单详情列表（销售数据）
     */
    @SaCheckPermission("crm:sales:data:export")
    @Log(title = "CRM销售数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CrmOrderDetailBo bo, HttpServletResponse response) {
        List<CrmOrderDetailVo> list = crmOrderDetailService.queryList(bo);
        ExcelUtil.exportExcel(list, "CRM销售数据", CrmOrderDetailVo.class, response);
    }

    /**
     * 获取CRM订单详情详细信息
     */
    @SaCheckPermission("crm:sales:data:query")
    @GetMapping("/{detailId}")
    public R<CrmOrderDetailVo> getInfo(@NotNull(message = "主键不能为空")
                                       @PathVariable Long detailId) {
        return R.ok(crmOrderDetailService.queryById(detailId));
    }
}
