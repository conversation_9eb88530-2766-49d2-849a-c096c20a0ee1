package com.imhuso.crm.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.excel.utils.ExcelUtil;
import com.imhuso.common.idempotent.annotation.RepeatSubmit;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.crm.core.domain.bo.CrmCustomerFollowRecordBo;
import com.imhuso.crm.core.domain.vo.CrmCustomerFollowRecordVo;
import com.imhuso.crm.core.service.ICrmCustomerFollowRecordService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * CRM客户跟进记录管理
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/crm/customer/follow-record")
public class CrmCustomerFollowRecordController extends BaseController {

    private final ICrmCustomerFollowRecordService crmCustomerFollowRecordService;

    /**
     * 查询CRM客户跟进记录列表
     */
    @SaCheckPermission("crm:follow:list")
    @GetMapping("/list")
    public TableDataInfo<CrmCustomerFollowRecordVo> list(CrmCustomerFollowRecordBo bo, PageQuery pageQuery) {
        return crmCustomerFollowRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 根据客户ID查询跟进记录列表
     */
    @SaCheckPermission("crm:follow:list")
    @GetMapping("/list/{customerId}")
    public R<List<CrmCustomerFollowRecordVo>> listByCustomerId(@PathVariable("customerId") Long customerId) {
        CrmCustomerFollowRecordBo bo = new CrmCustomerFollowRecordBo();
        bo.setCustomerId(customerId);
        List<CrmCustomerFollowRecordVo> list = crmCustomerFollowRecordService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 导出CRM客户跟进记录列表
     */
    @SaCheckPermission("crm:follow:export")
    @Log(title = "CRM客户跟进记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CrmCustomerFollowRecordBo bo, HttpServletResponse response) {
        List<CrmCustomerFollowRecordVo> list = crmCustomerFollowRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "CRM客户跟进记录", CrmCustomerFollowRecordVo.class, response);
    }

    /**
     * 获取CRM客户跟进记录详细信息
     */
    @SaCheckPermission("crm:follow:query")
    @GetMapping("/{followId}")
    public R<CrmCustomerFollowRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                                @PathVariable Long followId) {
        return R.ok(crmCustomerFollowRecordService.queryById(followId));
    }

    /**
     * 新增CRM客户跟进记录
     */
    @SaCheckPermission("crm:follow:add")
    @Log(title = "CRM客户跟进记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CrmCustomerFollowRecordBo bo) {
        return toAjax(crmCustomerFollowRecordService.insertByBo(bo));
    }

    /**
     * 修改CRM客户跟进记录
     */
    @SaCheckPermission("crm:follow:edit")
    @Log(title = "CRM客户跟进记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CrmCustomerFollowRecordBo bo) {
        return toAjax(crmCustomerFollowRecordService.updateByBo(bo));
    }

    /**
     * 删除CRM客户跟进记录
     */
    @SaCheckPermission("crm:follow:remove")
    @Log(title = "CRM客户跟进记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{followIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] followIds) {
        return toAjax(crmCustomerFollowRecordService.deleteWithValidByIds(List.of(followIds), true));
    }

    /**
     * 批量删除CRM客户跟进记录
     */
    @SaCheckPermission("crm:follow:remove")
    @Log(title = "CRM客户跟进记录", businessType = BusinessType.DELETE)
    @PostMapping("/batch-delete")
    public R<Void> batchRemove(@RequestBody List<Long> followIds) {
        return toAjax(crmCustomerFollowRecordService.deleteWithValidByIds(followIds, true));
    }

    /**
     * 获取客户最近的跟进记录
     */
    @SaCheckPermission("crm:follow:query")
    @GetMapping("/latest/{customerId}")
    public R<CrmCustomerFollowRecordVo> getLatestByCustomerId(@PathVariable("customerId") Long customerId) {
        CrmCustomerFollowRecordVo latestRecord = crmCustomerFollowRecordService.getLatestByCustomerId(customerId);
        return R.ok(latestRecord);
    }

    /**
     * 统计客户跟进次数
     */
    @SaCheckPermission("crm:follow:query")
    @GetMapping("/count/{customerId}")
    public R<Long> countByCustomerId(@PathVariable("customerId") Long customerId) {
        Long count = crmCustomerFollowRecordService.countByCustomerId(customerId);
        return R.ok(count);
    }

    /**
     * 审核跟进记录
     */
    @SaCheckPermission("crm:follow:audit")
    @Log(title = "CRM客户跟进记录审核", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/audit")
    public R<Void> audit(@RequestBody CrmCustomerFollowRecordBo bo) {
        return toAjax(crmCustomerFollowRecordService.auditFollowRecord(bo));
    }
}
