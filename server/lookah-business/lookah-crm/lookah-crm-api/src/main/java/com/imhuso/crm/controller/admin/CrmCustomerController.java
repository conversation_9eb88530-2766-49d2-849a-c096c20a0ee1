package com.imhuso.crm.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.imhuso.common.core.domain.R;
import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.excel.core.ExcelResult;
import com.imhuso.common.excel.utils.ExcelUtil;
import com.imhuso.common.idempotent.annotation.RepeatSubmit;
import com.imhuso.common.log.annotation.Log;
import com.imhuso.common.log.enums.BusinessType;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.web.core.BaseController;
import com.imhuso.crm.core.domain.bo.CrmCustomerBo;
import com.imhuso.crm.core.domain.vo.CrmCustomerImportVo;
import com.imhuso.crm.core.domain.vo.CrmCustomerVo;
import com.imhuso.crm.core.domain.vo.CustomerOptionVo;
import com.imhuso.crm.core.listener.CrmCustomerImportListener;
import com.imhuso.crm.core.service.ICrmCustomerService;
import com.imhuso.system.service.ISysUserService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * CRM客户信息管理
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("crm/customer")
public class CrmCustomerController extends BaseController {

    private final ICrmCustomerService crmCustomerService;
    private final ISysUserService sysUserService;

    /**
     * 查询CRM客户信息列表
     */
    @SaCheckPermission("crm:customer:list")
    @GetMapping("/list")
    public TableDataInfo<CrmCustomerVo> list(CrmCustomerBo bo, PageQuery pageQuery) {
        return crmCustomerService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询我的客户列表
     */
    @SaCheckPermission("crm:customer:list")
    @GetMapping("/my-list")
    public TableDataInfo<CrmCustomerVo> myList(CrmCustomerBo bo, PageQuery pageQuery) {
        return crmCustomerService.queryMyCustomerPageList(bo, pageQuery);
    }

    /**
     * 查询公海客户列表
     * TODO: 实现手动触发或定时任务的自动转换逻辑，替代每次查询时的自动转换
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return 公海客户列表
     */
    @SaCheckPermission("crm:customer:public")
    @GetMapping("/public-list")
    public TableDataInfo<CrmCustomerVo> publicList(CrmCustomerBo bo, PageQuery pageQuery) {
        return crmCustomerService.queryPublicCustomerPageList(bo, pageQuery);
    }

    /**
     * 导出CRM客户信息列表
     */
    @SaCheckPermission("crm:customer:export")
    @Log(title = "CRM客户信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CrmCustomerBo bo, HttpServletResponse response) {
        List<CrmCustomerVo> list = crmCustomerService.queryList(bo);
        ExcelUtil.exportExcel(list, "CRM客户信息", CrmCustomerVo.class, response);
    }

    /**
     * 获取CRM客户信息详细信息
     */
    @SaCheckPermission("crm:customer:query")
    @GetMapping("/{customerId}")
    public R<CrmCustomerVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long customerId) {
        return R.ok(crmCustomerService.queryById(customerId));
    }

    /**
     * 新增CRM客户信息
     */
    @SaCheckPermission("crm:customer:add")
    @Log(title = "CRM客户信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CrmCustomerBo bo) {
        // 添加日志来调试数据接收
        log.info("接收到客户数据: dealInfluenceFactors={}, dealInfluenceFactorsArray={}",
                bo.getDealInfluenceFactors(),
                bo.getDealInfluenceFactorsArray() != null ? Arrays.toString(bo.getDealInfluenceFactorsArray()) : "null");
        return toAjax(crmCustomerService.insertByBo(bo));
    }

    /**
     * 修改CRM客户信息
     */
    @SaCheckPermission("crm:customer:edit")
    @Log(title = "CRM客户信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CrmCustomerBo bo) {
        // 添加日志来调试数据接收
        log.info("接收到客户更新数据: dealInfluenceFactors={}, dealInfluenceFactorsArray={}",
                bo.getDealInfluenceFactors(),
                bo.getDealInfluenceFactorsArray() != null ? Arrays.toString(bo.getDealInfluenceFactorsArray()) : "null");
        return toAjax(crmCustomerService.updateByBo(bo));
    }

    /**
     * 删除CRM客户信息
     */
    @SaCheckPermission("crm:customer:remove")
    @Log(title = "CRM客户信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{customerIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] customerIds) {
        return toAjax(crmCustomerService.deleteWithValidByIds(List.of(customerIds), true));
    }

    /**
     * 生成客户编号
     */
    @SaCheckPermission("crm:customer:add")
    @GetMapping("/generate-code")
    public R<String> generateCustomerCode() {
        return R.ok(crmCustomerService.generateCustomerCode());
    }

    /**
     * 检查客户编号是否唯一
     */
    @GetMapping("/check-code-unique")
    public R<Boolean> checkCustomerCodeUnique(@RequestParam String customerCode,
                                              @RequestParam(required = false) Long customerId) {
        return R.ok(crmCustomerService.checkCustomerCodeUnique(customerCode, customerId));
    }

    /**
     * 检查账号邮箱是否唯一
     */
    @GetMapping("/check-email-unique")
    public R<Boolean> checkAccountEmailUnique(@RequestParam String accountEmail,
                                              @RequestParam(required = false) Long customerId) {
        return R.ok(crmCustomerService.checkAccountEmailUnique(accountEmail, customerId));
    }

    /**
     * 更新最后跟进时间
     */
    @SaCheckPermission("crm:customer:edit")
    @Log(title = "更新客户跟进时间", businessType = BusinessType.UPDATE)
    @PutMapping("/{customerId}/follow")
    public R<Void> updateFollowTime(@NotNull(message = "客户ID不能为空")
                                    @PathVariable Long customerId) {
        crmCustomerService.updateLastFollowTime(customerId);
        return R.ok();
    }

    /**
     * 更新客户星级评定
     */
    @SaCheckPermission("crm:customer:edit")
    @Log(title = "更新客户星级", businessType = BusinessType.UPDATE)
    @PutMapping("/{customerId}/star/{starRating}")
    public R<Void> updateStarRating(@NotNull(message = "客户ID不能为空")
                                    @PathVariable Long customerId,
                                    @NotNull(message = "星级评定不能为空")
                                    @PathVariable Integer starRating) {
        crmCustomerService.updateCustomerStarRating(customerId, starRating);
        return R.ok();
    }

    /**
     * 导入客户数据
     *
     * @param file          导入文件
     * @param updateSupport 是否更新已存在数据
     */
    @Log(title = "CRM客户信息", businessType = BusinessType.IMPORT)
    @SaCheckPermission("crm:customer:import")
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> importData(@RequestPart("file") MultipartFile file, boolean updateSupport) throws Exception {
        try {
            ExcelResult<CrmCustomerImportVo> result = ExcelUtil.importExcel(file.getInputStream(),
                CrmCustomerImportVo.class,
                new CrmCustomerImportListener(crmCustomerService, sysUserService ,updateSupport));
            return R.ok(result.getAnalysis());
        } catch (RuntimeException e) {
            // 捕获导入过程中的业务异常，直接返回错误信息给前端
            return R.failed(e.getMessage());
        }
    }

    /**
     * 获取客户导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "客户数据", CrmCustomerImportVo.class, response);
    }

    /**
     * 手动执行自动转换公海客户任务
     */
    @SaCheckPermission("crm:customer:public")
    @Log(title = "自动转换公海客户", businessType = BusinessType.UPDATE)
    @PostMapping("/auto-convert-public")
    public R<String> autoConvertToPublic(@RequestParam(defaultValue = "15") int days) {
        int convertedCount = crmCustomerService.autoConvertToPublicCustomers(days);
        return R.ok("成功转换 " + convertedCount + " 个客户为公海客户");
    }

    /**
     * 将客户转为公海客户
     */
    @SaCheckPermission("crm:customer:edit")
    @Log(title = "转换公海客户", businessType = BusinessType.UPDATE)
    @PutMapping("/{customerId}/convert-to-public")
    public R<Void> convertToPublic(@NotNull(message = "客户ID不能为空")
                                   @PathVariable Long customerId) {
        return toAjax(crmCustomerService.convertToPublicCustomer(customerId));
    }

    /**
     * 将公海客户分配给业务员
     */
    @SaCheckPermission("crm:customer:public")
    @Log(title = "分配公海客户", businessType = BusinessType.UPDATE)
    @PutMapping("/{customerId}/assign-to-sales")
    public R<Void> assignToSales(@NotNull(message = "客户ID不能为空")
                                 @PathVariable Long customerId,
                                 @NotNull(message = "业务员ID不能为空")
                                 @RequestParam Long salesUserId,
                                 @NotBlank(message = "业务员姓名不能为空")
                                 @RequestParam String salesUserName) {
        return toAjax(crmCustomerService.assignPublicCustomerToSales(customerId, salesUserId, salesUserName));
    }

    /**
     * 批量分配公海客户给业务员
     */
    @SaCheckPermission("crm:customer:public")
    @Log(title = "批量分配公海客户", businessType = BusinessType.UPDATE)
    @PutMapping("/batch-assign-to-sales")
    public R<String> batchAssignToSales(@NotNull(message = "客户ID列表不能为空")
                                        @RequestParam List<Long> customerIds,
                                        @NotNull(message = "业务员ID不能为空")
                                        @RequestParam Long salesUserId,
                                        @NotBlank(message = "业务员姓名不能为空")
                                        @RequestParam String salesUserName) {
        int successCount = crmCustomerService.batchAssignPublicCustomersToSales(customerIds, salesUserId, salesUserName);
        return R.ok("成功分配 " + successCount + " 个客户");
    }

    /**
     * 查询所有客户信息列表（用于客户查重）
     */
    @SaCheckPermission("crm:customer:duplicate")
    @GetMapping("/duplicate-list")
    public TableDataInfo<CrmCustomerVo> duplicateList(CrmCustomerBo bo, PageQuery pageQuery) {
        return crmCustomerService.queryAllCustomerPageList(bo, pageQuery);
    }

    /**
     * 获取客户选择列表（用于订单等模块选择客户）
     */
    @SaCheckPermission("crm:customer:query")
    @GetMapping("/options")
    public R<List<CustomerOptionVo>> getCustomerOptions() {
        return R.ok(crmCustomerService.getCustomerOptions());
    }

    /**
     * 更新客户说明
     *
     * @param customerId 客户ID
     * @param customerDescription 客户说明
     * @return 更新结果
     */
    @SaCheckPermission("crm:customer:edit")
    @Log(title = "更新客户说明", businessType = BusinessType.UPDATE)
    @PutMapping("/{customerId}/description")
    public R<Void> updateCustomerDescription(@PathVariable Long customerId,
                                           @RequestBody String customerDescription) {
        boolean result = crmCustomerService.updateCustomerDescription(customerId, customerDescription);
        return toAjax(result);
    }

    /**
     * 根据客户编号或邮箱获取已存在的客户信息
     *
     * @param customerCode 客户编号
     * @param accountEmail 账户邮箱
     * @return 如果存在返回客户信息，否则返回null
     */
    @SaCheckPermission("crm:customer:query")
    @GetMapping("/get-existing-customer")
    public R<CrmCustomerVo> getExistingCustomer(@RequestParam(required = false) String customerCode,
                                               @RequestParam(required = false) String accountEmail) {
        CrmCustomerVo existingCustomer = crmCustomerService.getExistingCustomer(customerCode, accountEmail);
        return R.ok(existingCustomer);
    }



}
