# CRM客户关系管理模块

## 模块概述

CRM（Customer Relationship Management）客户关系管理模块，用于管理客户全生命周期，包括客户开发、跟进、成交、维护等各个环节，并提供销售业绩管理和数据分析功能。

## 模块结构

```
lookah-crm/
├── lookah-crm-api/          # API接口层
│   ├── src/main/java/com/imhuso/crm/
│   │   ├── controller/      # 控制器
│   │   └── service/         # 服务接口
│   └── pom.xml
├── lookah-crm-core/         # 核心业务层
│   ├── src/main/java/com/imhuso/crm/core/
│   │   ├── domain/          # 实体类
│   │   ├── mapper/          # 数据访问层
│   │   ├── service/         # 服务实现
│   │   ├── enums/           # 枚举类
│   │   └── constant/        # 常量类
│   └── pom.xml
├── lookah-crm-starter/      # 自动配置启动器
│   ├── src/main/java/com/imhuso/crm/starter/
│   │   └── CrmAutoConfiguration.java
│   ├── src/main/resources/META-INF/spring/
│   │   └── org.springframework.boot.autoconfigure.AutoConfiguration.imports
│   └── pom.xml
├── pom.xml                  # 父模块POM
└── README.md               # 说明文档
```

## 核心功能模块

### 1. 客户池管理
- 客户信息管理
- 客户状态管理
- 客户公海机制
- 客户查重与防冲突

### 2. 销售流程管理
- 新客户开发流程
- 客户触达管理
- 老客户维护

### 3. 计划与报告
- 销售计划制定（日/周/月/季/年）
- 日报管理
- 业绩统计

### 4. 订单管理
- 订单录入
- 订单跟踪
- 业绩统计

## 技术栈

- **框架**: Spring Boot 3.x
- **数据库**: MySQL 8.0+
- **ORM**: MyBatis Plus
- **权限**: Sa-Token
- **构建工具**: Maven 3.6+

## 使用方式

### 1. 添加依赖

在需要使用CRM模块的项目中添加依赖：

```xml
<dependency>
    <groupId>com.imhuso</groupId>
    <artifactId>lookah-crm-starter</artifactId>
</dependency>
```

### 2. 自动配置

模块使用Spring Boot自动配置，无需手动配置。

### 3. API访问

如需暴露API接口，添加API模块依赖：

```xml
<dependency>
    <groupId>com.imhuso</groupId>
    <artifactId>lookah-crm-api</artifactId>
</dependency>
```

## 开发规范

1. 遵循项目统一的代码规范
2. 实体类使用`Crm`前缀
3. 服务接口以`I`开头，实现类以`Impl`结尾
4. 控制器按功能模块分包管理
5. 使用统一的异常处理和返回格式

## 注意事项

- 本模块依赖于`lookah-business-common-core`公共模块
- 需要配置相应的数据库表结构
- 权限控制基于Sa-Token实现
- 支持多租户架构（如需要）
