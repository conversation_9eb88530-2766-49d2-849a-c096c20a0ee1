package com.imhuso.crm.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrderStatus {

    /**
     * 待处理
     */
    PENDING("pending", "待处理"),

    /**
     * 已确认
     */
    CONFIRMED("confirmed", "已确认"),

    /**
     * 处理中
     */
    PROCESSING("processing", "处理中"),

    /**
     * 已发货
     */
    SHIPPED("shipped", "已发货"),

    /**
     * 已送达
     */
    DELIVERED("delivered", "已送达"),

    /**
     * 已完成
     */
    COMPLETED("completed", "已完成"),

    /**
     * 已取消
     */
    CANCELLED("cancelled", "已取消"),

    /**
     * 已退款
     */
    REFUNDED("refunded", "已退款");

    /**
     * 状态值
     */
    private final String value;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static OrderStatus fromValue(String value) {
        for (OrderStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 获取不允许删除的订单状态列表
     * 这些状态的订单不允许删除
     *
     * @return 不允许删除的状态值列表
     */
    public static String[] getUndeletableStatuses() {
        return new String[]{
            CONFIRMED.getValue(),
            PROCESSING.getValue(),
            SHIPPED.getValue(),
            DELIVERED.getValue(),
            COMPLETED.getValue()
        };
    }

    /**
     * 检查订单状态是否允许删除
     *
     * @param status 订单状态
     * @return true-允许删除，false-不允许删除
     */
    public static boolean isDeletable(String status) {
        String[] undeletableStatuses = getUndeletableStatuses();
        for (String undeletableStatus : undeletableStatuses) {
            if (undeletableStatus.equals(status)) {
                return false;
            }
        }
        return true;
    }
}
