package com.imhuso.crm.core.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.excel.core.ExcelListener;
import com.imhuso.common.excel.core.ExcelResult;
import com.imhuso.common.satoken.utils.LoginHelper;
import com.imhuso.crm.core.constant.CrmConstants;
import com.imhuso.crm.core.domain.CrmCustomer;
import com.imhuso.crm.core.domain.bo.CrmCustomerBo;
import com.imhuso.crm.core.domain.bo.CrmCustomerSocialMediaBo;
import com.imhuso.crm.core.domain.vo.CrmCustomerImportVo;
import com.imhuso.crm.core.domain.vo.CrmCustomerVo;
import com.imhuso.crm.core.service.ICrmCustomerService;
import com.imhuso.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * CRM客户信息导入监听器
 *
 * <AUTHOR>
 */
@Slf4j
public class CrmCustomerImportListener extends AnalysisEventListener<CrmCustomerImportVo> implements ExcelListener<CrmCustomerImportVo> {

    private final ICrmCustomerService customerService;
    private final ISysUserService userService;
    private final boolean updateSupport;

    private final List<CrmCustomerImportVo> validList = new ArrayList<>();
    private final List<String> errorMsgList = new ArrayList<>();

    private int successNum = 0;
    private int failureNum = 0;
    private final StringBuilder successMsg = new StringBuilder();
    private final StringBuilder failureMsg = new StringBuilder();

    public CrmCustomerImportListener(ICrmCustomerService customerService, ISysUserService userService, boolean updateSupport) {
        this.customerService = customerService;
        this.userService = userService;
        this.updateSupport = updateSupport;
    }

    @Override
    public void invoke(CrmCustomerImportVo data, AnalysisContext context) {
        String errorMsg = validateData(data, context.readRowHolder().getRowIndex() + 1);
        if (StringUtils.isNotEmpty(errorMsg)) {
            errorMsgList.add(errorMsg);
            failureNum++;
        } else {
            validList.add(data);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("所有数据解析完成！共{}条数据", validList.size());
        importData();
    }

    @Override
    public ExcelResult<CrmCustomerImportVo> getExcelResult() {
        return new ExcelResult<CrmCustomerImportVo>() {
            @Override
            public String getAnalysis() {
                if (failureNum > 0) {
                    for (String msg : errorMsgList) {
                        failureMsg.append(msg).append("\n");
                    }
                    failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
                    throw new RuntimeException(failureMsg.toString());
                } else {
                    successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                }
                return successMsg.toString();
            }

            @Override
            public List<CrmCustomerImportVo> getList() {
                return validList;
            }

            @Override
            public List<String> getErrorList() {
                return errorMsgList;
            }
        };
    }

    /**
     * 验证数据
     */
    private String validateData(CrmCustomerImportVo data, int rowIndex) {
        StringBuilder errorMsg = new StringBuilder();

        // 验证必填字段
        if (StringUtils.isEmpty(data.getCompanyName())) {
            errorMsg.append("第").append(rowIndex).append("行：公司名称不能为空；");
        }
        if (StringUtils.isEmpty(data.getCustomerType())) {
            errorMsg.append("第").append(rowIndex).append("行：客户类型不能为空；");
        }
        if (StringUtils.isEmpty(data.getCountry())) {
            errorMsg.append("第").append(rowIndex).append("行：国家不能为空；");
        }
        if (StringUtils.isEmpty(data.getAccountEmail())) {
            errorMsg.append("第").append(rowIndex).append("行：账户邮箱不能为空；");
        }
        if (StringUtils.isEmpty(data.getContactPerson())) {
            errorMsg.append("第").append(rowIndex).append("行：联系人不能为空；");
        }
        if (StringUtils.isEmpty(data.getPosition())) {
            errorMsg.append("第").append(rowIndex).append("行：职位不能为空；");
        }

        // 验证邮箱格式
        if (StringUtils.isNotEmpty(data.getAccountEmail()) && !isValidEmail(data.getAccountEmail())) {
            errorMsg.append("第").append(rowIndex).append("行：账户邮箱格式不正确；");
        }
        if (StringUtils.isNotEmpty(data.getContactEmail()) && !isValidEmail(data.getContactEmail())) {
            errorMsg.append("第").append(rowIndex).append("行：联系邮箱格式不正确；");
        }

        // 验证客户编号唯一性（如果提供了客户编号）
        if (StringUtils.isNotEmpty(data.getCustomerCode())) {
            if (!customerService.checkCustomerCodeUniqueInActive(data.getCustomerCode(), null)) {
                if (!updateSupport) {
                    errorMsg.append("第").append(rowIndex).append("行：客户编号已存在；");
                }
            }
        }

        // 验证账户邮箱唯一性
        if (StringUtils.isNotEmpty(data.getAccountEmail())) {
            if (!customerService.checkAccountEmailUniqueInActive(data.getAccountEmail(), null)) {
                if (!updateSupport) {
                    errorMsg.append("第").append(rowIndex).append("行：账户邮箱已存在；");
                }
            }
        }

        // 验证星级评分范围
        if (ObjectUtil.isNotNull(data.getStarRating()) && (data.getStarRating() < 1 || data.getStarRating() > 5)) {
            errorMsg.append("第").append(rowIndex).append("行：星级评分必须在1-5之间；");
        }

        // 验证业务员是否存在
        if (StringUtils.isNotEmpty(data.getSalesUserName())) {
            if (userService.selectUserByUserName(data.getSalesUserName()) == null) {
                errorMsg.append("第").append(rowIndex).append("行：业务员不存在；");
            }
        }

        return errorMsg.toString();
    }

    /**
     * 导入数据
     */
    private void importData() {
        if (validList.isEmpty()) {
            return;
        }

        for (CrmCustomerImportVo importVo : validList) {
            try {
                // 转换为业务对象
                CrmCustomerBo customerBo = convertToCustomerBo(importVo);

                // 判断是新增还是更新
                boolean isUpdate = false;
                if (StringUtils.isNotEmpty(importVo.getCustomerCode())) {
                    CrmCustomerVo existCustomer = customerService.selectCustomerByCode(importVo.getCustomerCode());
                    if (existCustomer != null) {
                        if (updateSupport) {
                            customerBo.setCustomerId(existCustomer.getCustomerId());
                            isUpdate = true;
                        } else {
                            failureNum++;
                            failureMsg.append("<br/>").append("客户编号 ").append(importVo.getCustomerCode()).append(" 已存在");
                            continue;
                        }
                    }
                }

                // 执行新增或更新
                if (isUpdate) {
                    customerService.updateByBo(customerBo);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、客户 ").append(importVo.getCompanyName()).append(" 更新成功");
                } else {
                    customerService.insertByBo(customerBo);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、客户 ").append(importVo.getCompanyName()).append(" 导入成功");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、客户 " + importVo.getCompanyName() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error("导入客户失败", e);
            }
        }
    }

    /**
     * 转换为客户业务对象
     */
    private CrmCustomerBo convertToCustomerBo(CrmCustomerImportVo importVo) {
        CrmCustomerBo customerBo = new CrmCustomerBo();
        BeanUtil.copyProperties(importVo, customerBo);

        // 设置默认值
        if (StringUtils.isBlank(customerBo.getCustomerCode())) {
            customerBo.setCustomerCode(customerService.generateCustomerCode());
        }
        if (ObjectUtil.isNull(customerBo.getStarRating())) {
            customerBo.setStarRating(CrmConstants.STAR_RATING_DEFAULT);
        }
        if (ObjectUtil.isNull(customerBo.getTotalOrderCount())) {
            customerBo.setTotalOrderCount(0);
        }
        if (ObjectUtil.isNull(customerBo.getTotalOrderAmount())) {
            customerBo.setTotalOrderAmount(BigDecimal.ZERO);
        }
        if (StringUtils.isBlank(customerBo.getIsPublic())) {
            customerBo.setIsPublic(CrmConstants.IS_PUBLIC_NO);
        }
        if (StringUtils.isBlank(customerBo.getCustomerStatus())) {
            customerBo.setCustomerStatus(CrmConstants.CUSTOMER_STATUS_POTENTIAL);
        }

        // 根据业务员姓名设置业务员ID
        if (StringUtils.isNotEmpty(importVo.getSalesUserName())) {
            var user = userService.selectUserByUserName(importVo.getSalesUserName());
            if (user != null) {
                customerBo.setSalesUserId(user.getUserId());
                customerBo.setSalesUserName(user.getNickName());
            }
        } else {
            // 默认设置为当前登录用户
            Long currentUserId = LoginHelper.getUserId();
            customerBo.setSalesUserId(currentUserId);
            var currentUser = userService.selectUserById(currentUserId);
            if (currentUser != null) {
                customerBo.setSalesUserName(currentUser.getNickName());
            }
        }

        // 处理影响成交因素多选（将逗号分隔的字符串转换为数组）
        if (StringUtils.isNotEmpty(importVo.getDealInfluenceFactors())) {
            // 将逗号分隔的字符串转换为数组格式，去除空格
            String[] factors = importVo.getDealInfluenceFactors().split(",");
            StringBuilder processedFactors = new StringBuilder();
            for (int i = 0; i < factors.length; i++) {
                String factor = factors[i].trim();
                if (StringUtils.isNotEmpty(factor)) {
                    if (processedFactors.length() > 0) {
                        processedFactors.append(",");
                    }
                    processedFactors.append(factor);
                }
            }
            customerBo.setDealInfluenceFactors(processedFactors.toString());
        }

        // 设置新老客户默认值
        if (StringUtils.isBlank(customerBo.getIsNewCustomer())) {
            customerBo.setIsNewCustomer("1"); // 默认为新客户
        }

        // 处理多组社交媒体数据
        List<CrmCustomerSocialMediaBo> socialMediaList = new ArrayList<>();

        // 处理第一组社交媒体
        if (StringUtils.isNotEmpty(importVo.getSocialMediaAccount1()) && StringUtils.isNotEmpty(importVo.getSocialMediaType1())) {
            CrmCustomerSocialMediaBo socialMedia1 = new CrmCustomerSocialMediaBo();
            socialMedia1.setSocialMediaAccount(importVo.getSocialMediaAccount1());
            socialMedia1.setSocialMediaType(importVo.getSocialMediaType1());
            socialMedia1.setIsPrimary("1"); // 第一个设为主要账号
            socialMedia1.setSortOrder(1);
            socialMedia1.setStatus("1");
            socialMediaList.add(socialMedia1);
        }

        // 处理第二组社交媒体
        if (StringUtils.isNotEmpty(importVo.getSocialMediaAccount2()) && StringUtils.isNotEmpty(importVo.getSocialMediaType2())) {
            CrmCustomerSocialMediaBo socialMedia2 = new CrmCustomerSocialMediaBo();
            socialMedia2.setSocialMediaAccount(importVo.getSocialMediaAccount2());
            socialMedia2.setSocialMediaType(importVo.getSocialMediaType2());
            socialMedia2.setIsPrimary("0");
            socialMedia2.setSortOrder(2);
            socialMedia2.setStatus("1");
            socialMediaList.add(socialMedia2);
        }

        // 处理第三组社交媒体
        if (StringUtils.isNotEmpty(importVo.getSocialMediaAccount3()) && StringUtils.isNotEmpty(importVo.getSocialMediaType3())) {
            CrmCustomerSocialMediaBo socialMedia3 = new CrmCustomerSocialMediaBo();
            socialMedia3.setSocialMediaAccount(importVo.getSocialMediaAccount3());
            socialMedia3.setSocialMediaType(importVo.getSocialMediaType3());
            socialMedia3.setIsPrimary("0");
            socialMedia3.setSortOrder(3);
            socialMedia3.setStatus("1");
            socialMediaList.add(socialMedia3);
        }

        // 设置社交媒体列表
        customerBo.setSocialMediaList(socialMediaList);

        return customerBo;
    }

    /**
     * 验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        return StrUtil.isNotBlank(email) && email.matches("^[\\w.-]+@[\\w.-]+\\.[a-zA-Z]{2,}$");
    }
}
