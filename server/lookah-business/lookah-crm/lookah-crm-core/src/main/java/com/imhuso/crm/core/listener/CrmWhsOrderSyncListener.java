package com.imhuso.crm.core.listener;

import com.imhuso.crm.core.domain.bo.CrmCustomerBo;
import com.imhuso.crm.core.domain.bo.CrmOrderBo;
import com.imhuso.crm.core.domain.vo.CrmCustomerVo;
import com.imhuso.crm.core.service.ICrmCustomerService;
import com.imhuso.crm.core.service.ICrmOrderService;
import com.imhuso.wholesale.core.domain.WhsOrder;
import com.imhuso.wholesale.core.event.WhsOrderEvent;
import com.imhuso.wholesale.core.service.IWhsOrderBaseService;
import com.imhuso.system.service.ISysDictDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * CRM批发订单同步监听器
 * 监听批发订单事件，自动同步到CRM系统
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CrmWhsOrderSyncListener {

    private final ICrmOrderService crmOrderService;
    private final ICrmCustomerService crmCustomerService;
    private final IWhsOrderBaseService whsOrderBaseService;
    private final ISysDictDataService sysDictDataService;

    /**
     * 监听批发订单创建事件
     * 在事务提交后异步处理，避免影响原有业务性能
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleWhsOrderCreated(WhsOrderEvent event) {
        if (!WhsOrderEvent.WhsOrderEventType.ORDER_CREATED.equals(event.getEventType())) {
            return;
        }

        try {
            log.info("开始处理批发订单创建事件，订单ID: {}", event.getOrderId());

            // 查询批发订单详细信息
            WhsOrder whsOrder = whsOrderBaseService.getOrderById(event.getOrderId());
            if (whsOrder == null) {
                log.warn("批发订单不存在，订单ID: {}", event.getOrderId());
                return;
            }

            // 同步订单到CRM系统
            syncWhsOrderToCrm(whsOrder, event);

        } catch (Exception e) {
            log.error("处理批发订单创建事件失败，订单ID: {}, 错误: {}", event.getOrderId(), e.getMessage(), e);
        }
    }

    /**
     * 同步批发订单到CRM系统
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncWhsOrderToCrm(WhsOrder whsOrder, WhsOrderEvent event) {
        try {
            // 查找或创建CRM客户
            CrmCustomerVo crmCustomer = findOrCreateCrmCustomer(whsOrder);
            if (crmCustomer == null) {
                log.warn("无法创建CRM客户，跳过订单同步，批发订单ID: {}", whsOrder.getId());
                return;
            }

            // 创建CRM订单
            CrmOrderBo crmOrderBo = createCrmOrderFromWhsOrder(whsOrder, crmCustomer);

            // 保存CRM订单
            Boolean success = crmOrderService.insertByBo(crmOrderBo);
            if (success) {
                log.info("成功同步批发订单到CRM，批发订单ID: {}, CRM订单号: {}",
                        whsOrder.getId(), crmOrderBo.getOrderNo());
            } else {
                log.error("同步批发订单到CRM失败，批发订单ID: {}", whsOrder.getId());
            }

        } catch (Exception e) {
            log.error("同步批发订单到CRM异常，批发订单ID: {}, 错误: {}", whsOrder.getId(), e.getMessage(), e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    /**
     * 查找或创建CRM客户
     */
    private CrmCustomerVo findOrCreateCrmCustomer(WhsOrder whsOrder) {
        try {
            String shippingEmail = whsOrder.getShippingEmail();
            String shippingName = whsOrder.getShippingName();

            if (shippingEmail == null && shippingName == null) {
                log.warn("批发订单缺少客户邮箱和客户姓名，无法创建CRM客户，订单ID: {}", whsOrder.getId());
                return null;
            }

            // 先尝试通过邮箱查找已存在的客户
            CrmCustomerVo existingCustomer = crmCustomerService.getExistingCustomer(null, shippingEmail);
            if (existingCustomer != null) {
                log.info("找到已存在的CRM客户，客户ID: {}, 邮箱: {}", existingCustomer.getCustomerId(), shippingEmail);
                return existingCustomer;
            }

            // 创建新的CRM客户
            CrmCustomerBo newCustomerBo = new CrmCustomerBo();
            newCustomerBo.setCustomerCode(null); // 自动生成
            newCustomerBo.setAccountEmail(shippingEmail);
            newCustomerBo.setContactEmail(shippingEmail);
            newCustomerBo.setCompanyName(whsOrder.getShippingCompanyName());
            newCustomerBo.setContactPerson(shippingName);
            newCustomerBo.setCustomerPhone(whsOrder.getShippingPhone());
            // 验证并设置客户来源，确保符合系统字典规范
            String validatedCustomerSource = validateDictValue("crm_customer_source", "wholesale_sync", "online", "客户来源");
            newCustomerBo.setCustomerSource(validatedCustomerSource);
            
            // 验证并设置客户状态，确保符合系统字典规范
            String validatedCustomerStatus = validateDictValue("crm_customer_status", "potential", "potential", "客户状态");
            newCustomerBo.setCustomerStatus(validatedCustomerStatus);
            newCustomerBo.setIsNewCustomer("1"); // 新客户
            newCustomerBo.setIsPublic("0"); // 非公海客户

            // 地址信息
            newCustomerBo.setCountry(whsOrder.getShippingCountry());
            newCustomerBo.setState(whsOrder.getShippingState());
            newCustomerBo.setZipCode(whsOrder.getShippingZip());

            Boolean createSuccess = crmCustomerService.insertByBo(newCustomerBo);
            if (createSuccess) {
                log.info("成功创建新的CRM客户，客户ID: {}, 邮箱: {}", newCustomerBo.getCustomerId(), shippingEmail);
                return crmCustomerService.queryById(newCustomerBo.getCustomerId());
            } else {
                log.error("创建CRM客户失败，邮箱: {}", shippingEmail);
                return null;
            }

        } catch (Exception e) {
            log.error("查找或创建CRM客户异常，批发订单ID: {}, 错误: {}", whsOrder.getId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从批发订单创建CRM订单
     */
    private CrmOrderBo createCrmOrderFromWhsOrder(WhsOrder whsOrder, CrmCustomerVo crmCustomer) {
        CrmOrderBo crmOrderBo = new CrmOrderBo();

        // 基本订单信息
        crmOrderBo.setOrderNo("WHS-" + whsOrder.getInternalOrderNo()); // 添加前缀标识来源
        crmOrderBo.setOrderDate(whsOrder.getCreateTime());
        crmOrderBo.setCustomerId(crmCustomer.getCustomerId());
        crmOrderBo.setCustomerCode(crmCustomer.getCustomerCode());
        crmOrderBo.setCompanyName(crmCustomer.getCompanyName());

        // 订单状态映射
        crmOrderBo.setOrderStatus(mapWhsOrderStatusToCrmStatus(whsOrder.getOrderStatus()));
        crmOrderBo.setOrderType("online"); // 批发订单标记为在线订单
        crmOrderBo.setPaymentMethod("wholesale_sync"); // 批发同步订单，支付方式标记为特殊值
        crmOrderBo.setRelatedOrderNo(whsOrder.getInternalOrderNo()); // 关联原批发订单号

        // 金额信息
        crmOrderBo.setOrderTotalAmount(whsOrder.getTotalAmount());
        crmOrderBo.setShippingFee(BigDecimal.ZERO); // 批发订单可能没有单独的运费字段，暂设为0

        // 备注信息
        crmOrderBo.setRemark("批发订单自动同步 - 原订单号: " + whsOrder.getInternalOrderNo());

        // 订单详情 - 暂时创建空列表，避免验证错误
        crmOrderBo.setOrderDetails(new ArrayList<>());

        return crmOrderBo;
    }


    /**
     * 批发订单状态映射到CRM订单状态
     */
    private String mapWhsOrderStatusToCrmStatus(Integer whsStatus) {
        if (whsStatus == null) {
            return "pending";
        }

        switch (whsStatus) {
            case 1: // 待支付
                return "pending";
            case 2: // 已支付
                return "confirmed";
            case 3: // 备货中
                return "processing";
            case 4: // 已发货
                return "shipped";
            case 5: // 已完成
                return "completed";
            case 6: // 已取消
                return "cancelled";
            case 7: // 已退款
                return "refunded";
            default:
                return "pending";
        }
    }

    /**
     * 验证并获取有效的字典值
     * 如果字典值不存在，记录警告并返回默认值
     *
     * @param dictType     字典类型
     * @param dictValue    字典值
     * @param defaultValue 默认值
     * @param fieldName    字段名称（用于日志）
     * @return 有效的字典值
     */
    private String validateDictValue(String dictType, String dictValue, String defaultValue, String fieldName) {
        // 参数空值检查，防止空指针异常
        if (dictType == null || dictType.trim().isEmpty()) {
            log.warn("字典类型为空: 字段[{}], 使用默认值[{}]", 
                    fieldName != null ? fieldName : "unknown", 
                    defaultValue != null ? defaultValue : "null");
            return defaultValue != null ? defaultValue : "";
        }
        
        if (dictValue == null || dictValue.trim().isEmpty()) {
            log.warn("字典值为空: 字段[{}], 字典类型[{}], 使用默认值[{}]", 
                    fieldName != null ? fieldName : "unknown", 
                    dictType, 
                    defaultValue != null ? defaultValue : "null");
            return defaultValue != null ? defaultValue : "";
        }
        
        if (defaultValue == null) {
            log.warn("默认值为空: 字段[{}], 字典类型[{}], 字典值[{}], 将使用空字符串作为默认值", 
                    fieldName != null ? fieldName : "unknown", 
                    dictType, 
                    dictValue);
            defaultValue = "";
        }

        try {
            String dictLabel = sysDictDataService.selectDictLabel(dictType, dictValue);
            if (dictLabel != null && !dictLabel.trim().isEmpty()) {
                // 字典值存在且有效
                return dictValue;
            } else {
                // 字典值不存在，使用默认值并记录警告
                log.warn("字典值验证失败: 字段[{}], 字典类型[{}], 尝试的值[{}]不存在，使用默认值[{}]", 
                        fieldName != null ? fieldName : "unknown", 
                        dictType, 
                        dictValue, 
                        defaultValue);
                return defaultValue;
            }
        } catch (Exception e) {
            // 验证过程中出现异常，使用默认值并记录错误
            log.error("字典值验证过程中发生异常: 字段[{}], 字典类型[{}], 尝试的值[{}], 错误: {}, 使用默认值[{}]", 
                     fieldName != null ? fieldName : "unknown", 
                     dictType, 
                     dictValue, 
                     e.getMessage() != null ? e.getMessage() : "未知错误", 
                     defaultValue);
            return defaultValue;
        }
    }
}
