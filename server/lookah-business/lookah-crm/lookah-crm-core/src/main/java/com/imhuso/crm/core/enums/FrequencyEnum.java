package com.imhuso.crm.core.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 频率枚举
 * 用于统一管理各种频率相关的常量值
 *
 * <AUTHOR>
 */
@Getter
public enum FrequencyEnum {

    /**
     * 不规律
     */
    IRREGULAR("irregular", "不规律"),

    /**
     * 每天
     */
    DAILY("daily", "每天"),

    /**
     * 每周
     */
    WEEKLY("weekly", "每周"),

    /**
     * 每月
     */
    MONTHLY("monthly", "每月"),

    /**
     * 每季度
     */
    QUARTERLY("quarterly", "每季度");

    /**
     * 频率代码
     */
    private final String code;

    /**
     * 频率描述
     */
    private final String description;

    /**
     * 代码到枚举的映射缓存，提高查找性能
     */
    private static final Map<String, FrequencyEnum> CODE_MAP =
        Arrays.stream(values()).collect(Collectors.toMap(FrequencyEnum::getCode, Function.identity()));

    FrequencyEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 频率代码
     * @return 频率枚举
     */
    public static FrequencyEnum fromCode(String code) {
        if (code == null) {
            return IRREGULAR; // 当代码为null时，默认返回不规律频率
        }
        return CODE_MAP.getOrDefault(code, IRREGULAR); // 使用缓存Map提高查找性能
    }

    /**
     * 检查代码是否有效
     *
     * @param code 频率代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        if (code == null) {
            return false; // null值被认为是无效的
        }
        return CODE_MAP.containsKey(code); // 使用缓存Map提高查找性能
    }
}
