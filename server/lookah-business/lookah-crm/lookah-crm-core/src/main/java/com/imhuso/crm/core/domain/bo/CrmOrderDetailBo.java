package com.imhuso.crm.core.domain.bo;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import com.imhuso.crm.core.domain.CrmOrderDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * CRM客户订单详情业务对象 CrmOrderDetailBo
 *
 * <AUTHOR> Assistant
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CrmOrderDetail.class, reverseConvertGenerate = false)
public class CrmOrderDetailBo extends BaseEntity {

    /**
     * 订单详情ID
     */
    @NotNull(message = "订单详情ID不能为空", groups = { EditGroup.class })
    private Long detailId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 200, message = "产品名称长度不能超过200个字符")
    private String productName;

    /**
     * 颜色
     */
    @Size(max = 100, message = "颜色长度不能超过100个字符")
    private String productColor;

    /**
     * 单价
     */
    @NotNull(message = "单价不能为空", groups = { AddGroup.class, EditGroup.class })
    @DecimalMin(value = "0.00", message = "单价不能小于0")
    private BigDecimal unitPrice;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空", groups = { AddGroup.class, EditGroup.class })
    @Min(value = 1, message = "数量不能小于1")
    private Integer quantity;

    /**
     * 单款产品总金额
     */
    @NotNull(message = "单款产品总金额不能为空", groups = { AddGroup.class, EditGroup.class })
    @DecimalMin(value = "0.00", message = "单款产品总金额不能小于0")
    private BigDecimal productTotalAmount;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 订单详情状态（normal=正常, refund=退款, return=退货, defective=到货不良, shortage=到货少货, wrong=发错货, resend=重发）
     */
    @Size(max = 20, message = "订单详情状态长度不能超过20个字符")
    private String detailStatus;

    /**
     * 是否赠品（1=是, 0=否）
     */
    @Pattern(regexp = "^[01]$", message = "是否赠品值必须为0或1")
    private String isGift;

    /**
     * 退款原因
     */
    @Size(max = 500, message = "退款原因长度不能超过500个字符")
    private String refundReason;

    /**
     * 客户公司名称（查询条件）
     */
    private String companyName;

    /**
     * 下单日期
     */
    private Date orderDate;

}
