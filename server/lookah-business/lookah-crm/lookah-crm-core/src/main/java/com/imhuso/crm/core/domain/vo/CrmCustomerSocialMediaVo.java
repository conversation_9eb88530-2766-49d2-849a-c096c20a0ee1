package com.imhuso.crm.core.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.imhuso.common.excel.annotation.ExcelDictFormat;
import com.imhuso.common.excel.convert.ExcelDictConvert;
import com.imhuso.crm.core.domain.CrmCustomerSocialMedia;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * CRM客户社媒账号视图对象 crm_customer_social_media
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CrmCustomerSocialMedia.class)
public class CrmCustomerSocialMediaVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 社媒账号ID
     */
    @ExcelProperty(value = "社媒账号ID")
    private Long socialMediaId;

    /**
     * 客户ID
     */
    @ExcelProperty(value = "客户ID")
    private Long customerId;

    /**
     * 社媒类型
     */
    @ExcelProperty(value = "社媒类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "CRM_SOCIAL_MEDIA_TYPE")
    private String socialMediaType;

    /**
     * 社媒账号
     */
    @ExcelProperty(value = "社媒账号")
    private String socialMediaAccount;

    /**
     * 是否主要账号
     */
    @ExcelProperty(value = "是否主要账号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String isPrimary;

    /**
     * 排序顺序
     */
    @ExcelProperty(value = "排序顺序")
    private Integer sortOrder;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_normal_disable")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

}
