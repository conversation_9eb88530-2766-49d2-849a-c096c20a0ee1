package com.imhuso.crm.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 职位枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PositionEnum {

    BOSS("Boss", "Boss"),
    PURCHASE_MANAGER("PurchaseManager", "Purchase Manager"),
    STORE_MANAGER("StoreManager", "Store Manager"),
    CO_PARTNER("Co-partner", "Co-partner"),
    SALES_MANAGER("SalesManager", "Sales Manager"),
    OTHERS("Others", "Others");

    /**
     * 代码
     */
    private final String code;

    /**
     * 描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     */
    public static PositionEnum getByCode(String code) {
        for (PositionEnum position : values()) {
            if (position.getCode().equals(code)) {
                return position;
            }
        }
        return null;
    }
}
