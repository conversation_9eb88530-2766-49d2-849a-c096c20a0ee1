package com.imhuso.crm.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.mybatis.annotation.DataColumn;
import com.imhuso.common.mybatis.annotation.DataPermission;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.satoken.utils.LoginHelper;
import com.imhuso.crm.core.domain.CrmCustomer;
import com.imhuso.crm.core.domain.CrmOrder;
import com.imhuso.crm.core.domain.bo.CrmOrderBo;
import com.imhuso.crm.core.domain.bo.CrmOrderDetailBo;
import com.imhuso.crm.core.domain.vo.CrmOrderVo;
import com.imhuso.crm.core.enums.OrderDetailStatus;
import com.imhuso.crm.core.mapper.CrmOrderMapper;
import com.imhuso.crm.core.service.ICrmOrderService;
import com.imhuso.crm.core.service.ICrmOrderDetailService;
import com.imhuso.crm.core.service.ICrmCustomerService;
import com.imhuso.crm.core.utils.OrderStatusUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * CRM客户订单Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CrmOrderServiceImpl implements ICrmOrderService {

    private final CrmOrderMapper baseMapper;
    private final ICrmOrderDetailService orderDetailService;
    private final ICrmCustomerService customerService;
    private final OrderStatusUtils orderStatusUtils;

    public CrmOrderServiceImpl(CrmOrderMapper baseMapper,
                             ICrmOrderDetailService orderDetailService,
                             @Lazy ICrmCustomerService customerService,
                             OrderStatusUtils orderStatusUtils) {
        this.baseMapper = baseMapper;
        this.orderDetailService = orderDetailService;
        this.customerService = customerService;
        this.orderStatusUtils = orderStatusUtils;
    }

    /**
     * 查询CRM客户订单
     */
    @Override
    public CrmOrderVo queryById(Long orderId) {
        CrmOrderVo orderVo = baseMapper.selectVoByIdWithUserInfo(orderId);
        if (ObjectUtil.isNotNull(orderVo)) {
            // 查询订单详情
            orderVo.setOrderDetails(orderDetailService.queryByOrderId(orderId));
        }
        return orderVo;
    }

    /**
     * 查询CRM客户订单列表
     */
    @Override
    @DataPermission({
        @DataColumn(key = "userName", value = "o.create_by")
    })
    public TableDataInfo<CrmOrderVo> queryPageList(CrmOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CrmOrder> lqw = buildQueryWrapperWithTableAlias(bo);
        Page<CrmOrderVo> result = baseMapper.selectVoPageWithUserInfo(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询我的客户订单列表（只查询当前用户负责客户的订单）
     */
    @Override
    @DataPermission({
        @DataColumn(key = "userName", value = "o.create_by")
    })
    public TableDataInfo<CrmOrderVo> queryMyPageList(CrmOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CrmOrder> lqw = buildQueryWrapperWithTableAlias(bo);
        // 只查询当前用户创建的订单
        lqw.eq(CrmOrder::getCreateBy, LoginHelper.getUsername());
        IPage<CrmOrderVo> result = baseMapper.selectVoPageWithUserInfo(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询CRM客户订单列表
     */
    @Override
    public List<CrmOrderVo> queryList(CrmOrderBo bo) {
        LambdaQueryWrapper<CrmOrder> lqw = buildQueryWrapperWithTableAlias(bo);
        return baseMapper.selectVoListWithUserInfo(lqw);
    }

    private LambdaQueryWrapper<CrmOrder> buildQueryWrapper(CrmOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CrmOrder> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getOrderNoLike()), CrmOrder::getOrderNo, bo.getOrderNoLike());
        lqw.eq(ObjectUtil.isNotNull(bo.getCustomerId()), CrmOrder::getCustomerId, bo.getCustomerId());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerCode()), CrmOrder::getCustomerCode, bo.getCustomerCode());
        lqw.like(StringUtils.isNotBlank(bo.getCompanyNameLike()), CrmOrder::getCompanyName, bo.getCompanyNameLike());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderStatus()), CrmOrder::getOrderStatus, bo.getOrderStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderType()), CrmOrder::getOrderType, bo.getOrderType());
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentMethod()), CrmOrder::getPaymentMethod, bo.getPaymentMethod());
        lqw.ge(ObjectUtil.isNotNull(bo.getOrderDateStart()), CrmOrder::getOrderDate, bo.getOrderDateStart());
        lqw.le(ObjectUtil.isNotNull(bo.getOrderDateEnd()), CrmOrder::getOrderDate, bo.getOrderDateEnd());
        lqw.ge(ObjectUtil.isNotNull(bo.getCreateTimeStart()), CrmOrder::getCreateTime, bo.getCreateTimeStart());
        lqw.le(ObjectUtil.isNotNull(bo.getCreateTimeEnd()), CrmOrder::getCreateTime, bo.getCreateTimeEnd());
        lqw.orderByDesc(CrmOrder::getCreateTime);
        // 下单时间范围查询
        lqw.between(params.get("beginTime") != null && params.get("endTime") != null,
            CrmOrder::getCreateTime, params.get("beginTime"), params.get("endTime"));
        return lqw;
    }

    /**
     * 构建查询条件包装器（带表别名，用于关联查询）
     */
    private LambdaQueryWrapper<CrmOrder> buildQueryWrapperWithTableAlias(CrmOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CrmOrder> lqw = Wrappers.lambdaQuery();

        // 基础查询条件（这些字段在crm_order表中是唯一的，不需要别名）
        lqw.like(StringUtils.isNotBlank(bo.getOrderNoLike()), CrmOrder::getOrderNo, bo.getOrderNoLike());
        lqw.eq(ObjectUtil.isNotNull(bo.getCustomerId()), CrmOrder::getCustomerId, bo.getCustomerId());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerCode()), CrmOrder::getCustomerCode, bo.getCustomerCode());
        lqw.like(StringUtils.isNotBlank(bo.getCompanyNameLike()), CrmOrder::getCompanyName, bo.getCompanyNameLike());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderStatus()), CrmOrder::getOrderStatus, bo.getOrderStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderType()), CrmOrder::getOrderType, bo.getOrderType());
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentMethod()), CrmOrder::getPaymentMethod, bo.getPaymentMethod());
        lqw.ge(ObjectUtil.isNotNull(bo.getOrderDateStart()), CrmOrder::getOrderDate, bo.getOrderDateStart());
        lqw.le(ObjectUtil.isNotNull(bo.getOrderDateEnd()), CrmOrder::getOrderDate, bo.getOrderDateEnd());

        // 时间相关条件需要特殊处理，因为可能存在字段冲突
        if (ObjectUtil.isNotNull(bo.getCreateTimeStart())) {
            lqw.apply("o.create_time >= {0}", bo.getCreateTimeStart());
        }
        if (ObjectUtil.isNotNull(bo.getCreateTimeEnd())) {
            lqw.apply("o.create_time <= {0}", bo.getCreateTimeEnd());
        }

        // 下单时间范围查询（使用表别名）
        if (params.get("beginTime") != null && params.get("endTime") != null) {
            lqw.apply("o.create_time BETWEEN {0} AND {1}", params.get("beginTime"), params.get("endTime"));
        }

        // 注意：不在这里添加ORDER BY，让XML中统一处理

        return lqw;
    }

    /**
     * 新增CRM客户订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(CrmOrderBo bo) {
        // 计算订单总金额
        calculateOrderAmount(bo);

        CrmOrder add = MapstructUtils.convert(bo, CrmOrder.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;

        if (flag) {
            bo.setOrderId(add.getOrderId());
            // 保存订单详情
            if (CollUtil.isNotEmpty(bo.getOrderDetails())) {
                for (CrmOrderDetailBo detail : bo.getOrderDetails()) {
                    detail.setOrderId(add.getOrderId());
                    detail.setOrderNo(add.getOrderNo());
                }
                orderDetailService.insertBatch(bo.getOrderDetails());
            }

            // 更新客户订单统计信息
            if (add.getCustomerId() != null) {
                try {
                    customerService.updateCustomerOrderStatistics(add.getCustomerId());
                    log.info("更新客户订单统计信息成功，客户ID: {}", add.getCustomerId());
                } catch (Exception e) {
                    log.error("更新客户订单统计信息失败，客户ID: {}, 错误: {}", add.getCustomerId(), e.getMessage());
                    // 不影响主流程，只记录日志
                }

                // 更新客户感兴趣的产品名称
                try {
                    customerService.updateInterestedProducts(add.getCustomerId());
                    log.info("更新客户感兴趣产品成功，客户ID: {}", add.getCustomerId());
                } catch (Exception e) {
                    log.error("更新客户感兴趣产品失败，客户ID: {}, 错误: {}", add.getCustomerId(), e.getMessage());
                    // 不影响主流程，只记录日志
                }

                // 更新客户星级
                try {
                    customerService.updateCustomerStarRating(add.getCustomerId());
                    log.info("更新客户星级成功，客户ID: {}", add.getCustomerId());
                } catch (Exception e) {
                    log.error("更新客户星级失败，客户ID: {}, 错误: {}", add.getCustomerId(), e.getMessage());
                    // 不影响主流程，只记录日志
                }

                // 将客户变为老客户
                try {
                    customerService.updateCustomerToOldCustomer(add.getCustomerId());
                    log.info("更新客户为老客户成功，客户ID: {}", add.getCustomerId());
                } catch (Exception e) {
                    log.error("更新客户为老客户失败，客户ID: {}, 错误: {}", add.getCustomerId(), e.getMessage());
                    // 不影响主流程，只记录日志
                }
            }
        }
        return flag;
    }

    /**
     * 修改CRM客户订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(CrmOrderBo bo) {
        // 计算订单总金额
        calculateOrderAmount(bo);

        CrmOrder update = MapstructUtils.convert(bo, CrmOrder.class);
        validEntityBeforeSave(update);
        boolean flag = baseMapper.updateById(update) > 0;

        if (flag) {
            if (CollUtil.isNotEmpty(bo.getOrderDetails())) {
                // 删除原有订单详情
                orderDetailService.deleteByOrderId(bo.getOrderId());
                // 重新保存订单详情
                for (CrmOrderDetailBo detail : bo.getOrderDetails()) {
                    detail.setOrderId(bo.getOrderId());
                    detail.setOrderNo(bo.getOrderNo());
                    detail.setDetailId(null); // 清空ID，重新生成
                }
                orderDetailService.insertBatch(bo.getOrderDetails());
            }

            // 更新客户订单统计信息
            if (update.getCustomerId() != null) {
                try {
                    customerService.updateCustomerOrderStatistics(update.getCustomerId());
                    log.info("更新客户订单统计信息成功，客户ID: {}", update.getCustomerId());
                } catch (Exception e) {
                    log.error("更新客户订单统计信息失败，客户ID: {}, 错误: {}", update.getCustomerId(), e.getMessage());
                    // 不影响主流程，只记录日志
                }

                // 更新客户感兴趣的产品名称
                try {
                    customerService.updateInterestedProducts(update.getCustomerId());
                    log.info("更新客户感兴趣产品成功，客户ID: {}", update.getCustomerId());
                } catch (Exception e) {
                    log.error("更新客户感兴趣产品失败，客户ID: {}, 错误: {}", update.getCustomerId(), e.getMessage());
                    // 不影响主流程，只记录日志
                }

                // 更新客户星级
                try {
                    customerService.updateCustomerStarRating(update.getCustomerId());
                    log.info("更新客户星级成功，客户ID: {}", update.getCustomerId());
                } catch (Exception e) {
                    log.error("更新客户星级失败，客户ID: {}, 错误: {}", update.getCustomerId(), e.getMessage());
                    // 不影响主流程，只记录日志
                }

                // 将客户变为老客户
                try {
                    customerService.updateCustomerToOldCustomer(update.getCustomerId());
                    log.info("更新客户为老客户成功，客户ID: {}", update.getCustomerId());
                } catch (Exception e) {
                    log.error("更新客户为老客户失败，客户ID: {}, 错误: {}", update.getCustomerId(), e.getMessage());
                    // 不影响主流程，只记录日志
                }
            }
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(CrmOrder entity) {
        // 校验订单号唯一性
        if (!checkOrderNoUnique(entity.getOrderNo(), entity.getOrderId())) {
            throw new ServiceException("订单号已存在");
        }
    }

    /**
     * 校验订单号是否唯一
     */
    private boolean checkOrderNoUnique(String orderNo, Long orderId) {
        if (StringUtils.isBlank(orderNo)) {
            return true;
        }

        LambdaQueryWrapper<CrmOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(CrmOrder::getOrderNo, orderNo);
        if (ObjectUtil.isNotNull(orderId)) {
            lqw.ne(CrmOrder::getOrderId, orderId);
        }

        return baseMapper.selectCount(lqw) == 0;
    }

    /**
     * 删除前的业务校验
     */
    private void validateOrdersBeforeDelete(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        // 查询要删除的订单
        List<CrmOrder> orders = baseMapper.selectList(
            new LambdaQueryWrapper<CrmOrder>()
                .in(CrmOrder::getOrderId, ids)
                .eq(CrmOrder::getDelFlag, BusinessConstants.LOGIC_NOT_DELETE)
        );
        for (CrmOrder order : orders) {
            // 检查订单状态，某些状态的订单不允许删除
            // 使用字典配置的状态判断，而不是硬编码
            if (!orderStatusUtils.isDeletable(order.getOrderStatus())) {
                String statusLabel = orderStatusUtils.getOrderStatusLabel(order.getOrderStatus());
                throw new ServiceException("订单【" + order.getOrderNo() + "】状态为【" + statusLabel + "】，不能删除");
            }
        }
    }

    /**
     * 批量删除CRM客户订单（软删除，设置删除时间戳）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 执行业务校验：检查订单是否可以删除
            // 例如：已支付的订单不能删除，已发货的订单不能删除等
            validateOrdersBeforeDelete(ids);
        }

        // 软删除：设置删除标志和删除时间戳
        if (CollUtil.isNotEmpty(ids)) {
            // 先查询要删除的订单，获取客户ID列表
            LambdaQueryWrapper<CrmOrder> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.in(CrmOrder::getOrderId, ids);
            queryWrapper.select(CrmOrder::getCustomerId);
            List<CrmOrder> ordersToDelete = baseMapper.selectList(queryWrapper);

            long currentTimestamp = System.currentTimeMillis();

            // 批量更新删除标志和删除时间戳
            LambdaUpdateWrapper<CrmOrder> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(CrmOrder::getOrderId, ids)
                .set(CrmOrder::getDelFlag, BusinessConstants.LOGIC_DELETE)
                .set(CrmOrder::getDeletedAt, currentTimestamp);

            boolean result = baseMapper.update(null, updateWrapper) > 0;

            if (result) {
                // 软删除订单详情
                for (Long orderId : ids) {
                    orderDetailService.deleteByOrderId(orderId);
                }

                // 更新相关客户的订单统计信息
                ordersToDelete.stream()
                    .map(CrmOrder::getCustomerId)
                    .filter(ObjectUtil::isNotNull)
                    .distinct()
                    .forEach(customerId -> {
                        try {
                            customerService.updateCustomerOrderStatistics(customerId);
                            log.info("删除订单后更新客户订单统计信息成功，客户ID: {}", customerId);
                        } catch (Exception e) {
                            log.error("删除订单后更新客户订单统计信息失败，客户ID: {}, 错误: {}", customerId, e.getMessage());
                            // 不影响主流程，只记录日志
                        }
                    });
            }

            return result;
        }

        return false;
    }

    /**
     * 更新订单状态
     */
    @Override
    public Boolean updateOrderStatus(Long orderId, String orderStatus, String remark) {
        // 先查询订单信息，获取客户ID
        CrmOrder order = baseMapper.selectById(orderId);
        if (order == null) {
            return false;
        }

        LambdaUpdateWrapper<CrmOrder> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(CrmOrder::getOrderId, orderId);
        updateWrapper.set(CrmOrder::getOrderStatus, orderStatus);
        if (StringUtils.isNotBlank(remark)) {
            updateWrapper.set(CrmOrder::getRemark, remark);
        }

        boolean result = baseMapper.update(null, updateWrapper) > 0;

        // 如果更新成功且客户ID存在，更新客户统计信息
        if (result && order.getCustomerId() != null) {
            try {
                customerService.updateCustomerOrderStatistics(order.getCustomerId());
                log.info("更新订单状态后更新客户订单统计信息成功，客户ID: {}", order.getCustomerId());
            } catch (Exception e) {
                log.error("更新订单状态后更新客户订单统计信息失败，客户ID: {}, 错误: {}", order.getCustomerId(), e.getMessage());
                // 不影响主流程，只记录日志
            }
        }

        return result;
    }

    /**
     * 生成订单号
     */
    @Override
    public String generateOrderNo() {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        // 使用时间戳生成序列号，生产环境建议使用分布式序列号生成器（如雪花算法）
        String timeStr = String.valueOf(System.currentTimeMillis()).substring(8);
        return "ORD" + dateStr + timeStr;
    }

    /**
     * 计算订单总金额
     */
    @Override
    public void calculateOrderAmount(CrmOrderBo bo) {
        if (CollUtil.isEmpty(bo.getOrderDetails())) {
            bo.setOrderTotalAmount(BigDecimal.ZERO);
            return;
        }

        // 计算产品总金额
        BigDecimal productTotalAmount = BigDecimal.ZERO;
        for (CrmOrderDetailBo detail : bo.getOrderDetails()) {
            // 计算单款产品总金额
            BigDecimal productTotal = detail.getUnitPrice().multiply(new BigDecimal(detail.getQuantity()));
            detail.setProductTotalAmount(productTotal);

            // 如果订单详情状态为退款，则不计入订单总金额
            if (!OrderDetailStatus.REFUND.getValue().equals(detail.getDetailStatus())) {
                productTotalAmount = productTotalAmount.add(productTotal);
            }
        }

        // 获取运费，如果为空则默认为0
        BigDecimal shippingFee = bo.getShippingFee() != null ? bo.getShippingFee() : BigDecimal.ZERO;

        // 订单总金额 = 产品总金额 + 运费
        BigDecimal totalAmount = productTotalAmount.add(shippingFee);
        bo.setOrderTotalAmount(totalAmount);
    }

}
