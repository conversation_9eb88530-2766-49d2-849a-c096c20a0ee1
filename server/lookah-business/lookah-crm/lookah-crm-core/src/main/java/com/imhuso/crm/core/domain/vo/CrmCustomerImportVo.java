package com.imhuso.crm.core.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.imhuso.common.excel.annotation.ExcelDictFormat;
import com.imhuso.common.excel.convert.ExcelDictConvert;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * CRM客户信息导入VO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CrmCustomerImportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户编号（老客户手动输入，新客户按LCS#00000规则自动生成）
     */
    @ExcelProperty(value = "客户编号")
    private String customerCode;

    /**
     * 公司名称
     */
    @ExcelProperty(value = "公司名称")
    private String companyName;

    /**
     * 客户类型
     */
    @ExcelProperty(value = "客户类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_customer_type")
    private String customerType;

    /**
     * 经营地点数量
     */
    @ExcelProperty(value = "经营地点数量")
    private Integer businessLocationCount;

    /**
     * 所在国家
     */
    @ExcelProperty(value = "国家")
    private String country;

    /**
     * 所在州
     */
    @ExcelProperty(value = "州/省")
    private String state;

    /**
     * 邮编
     */
    @ExcelProperty(value = "邮编")
    private String zipCode;

    /**
     * 账户邮箱
     */
    @ExcelProperty(value = "账户邮箱")
    private String accountEmail;

    /**
     * 联系邮箱
     */
    @ExcelProperty(value = "联系邮箱")
    private String contactEmail;

    /**
     * 联系人
     */
    @ExcelProperty(value = "联系人")
    private String contactPerson;

    /**
     * 客户电话
     */
    @ExcelProperty(value = "客户电话")
    private String customerPhone;

    /**
     * WhatsApp号码
     */
    @ExcelProperty(value = "WhatsApp号码")
    private String whatsappNumber;

    /**
     * 职位
     */
    @ExcelProperty(value = "职位", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_position_type")
    private String position;

    /**
     * 社交媒体账号1
     */
    @ExcelProperty(value = "社交媒体账号1")
    private String socialMediaAccount1;

    /**
     * 社交媒体类型1
     */
    @ExcelProperty(value = "社交媒体类型1", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_social_media_type")
    private String socialMediaType1;

    /**
     * 社交媒体账号2
     */
    @ExcelProperty(value = "社交媒体账号2")
    private String socialMediaAccount2;

    /**
     * 社交媒体类型2
     */
    @ExcelProperty(value = "社交媒体类型2", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_social_media_type")
    private String socialMediaType2;

    /**
     * 社交媒体账号3
     */
    @ExcelProperty(value = "社交媒体账号3")
    private String socialMediaAccount3;

    /**
     * 社交媒体类型3
     */
    @ExcelProperty(value = "社交媒体类型3", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_social_media_type")
    private String socialMediaType3;

    /**
     * 客户来源
     */
    @ExcelProperty(value = "客户来源", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_customer_source")
    private String customerSource;

    /**
     * 客户状态
     */
    @ExcelProperty(value = "客户状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_customer_status")
    private String customerStatus;

    /**
     * 状态描述
     */
    @ExcelProperty(value = "状态描述")
    private String statusDescription;

    /**
     * 星级评定
     */
    @ExcelProperty(value = "星级评分", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_system_star_rating")
    private Integer starRating;

    /**
     * 负责业务员姓名
     */
    @ExcelProperty(value = "业务员")
    private String salesUserName;

    /**
     * 是否公海客户
     */
    @ExcelProperty(value = "客户归属", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_is_public")
    private String isPublic;

    /**
     * 是否为新客户
     */
    @ExcelProperty(value = "新老客户", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_is_new_customer")
    private String isNewCustomer;

    /**
     * 客户说明
     */
    @ExcelProperty(value = "客户说明")
    private String customerDescription;

    /**
     * 客户画像
     */
    @ExcelProperty(value = "客户画像")
    private String customerProfile;

    /**
     * 感兴趣的产品
     */
    @ExcelProperty(value = "感兴趣的产品")
    private String interestedProducts;

    /**
     * 已销售的竞品品牌
     */
    @ExcelProperty(value = "竞品品牌")
    private String competitorBrands;

    /**
     * 是否好沟通
     */
    @ExcelProperty(value = "是否好沟通",converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_is_easy_communicate")
    private String isEasyCommunicate;

    /**
     * 联系方式偏好
     */
    @ExcelProperty(value = "联系方式偏好", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_contact_preference")
    private String contactPreference;

    /**
     * 决策权大小
     */
    @ExcelProperty(value = "决策权大小", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_decision_authority")
    private String decisionAuthority;

    /**
     * 对价格敏感的程度
     */
    @ExcelProperty(value = "价格敏感程度", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_price_sensitivity")
    private String priceSensitivity;

    /**
     * 联系频次
     */
    @ExcelProperty(value = "联系频次", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_contact_frequency")
    private String contactFrequency;

    /**
     * 下单频率
     */
    @ExcelProperty(value = "下单频率", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_purchase_frequency")
    private String orderFrequency;

    /**
     * 成交的关键点
     */
    @ExcelProperty(value = "成交关键点")
    private String dealKeyPoints;

    /**
     * 影响成交的主要因素（多选，用逗号分隔）
     */
    @ExcelProperty(value = "影响成交的主要因素")
    private String dealInfluenceFactors;

    /**
     * 影响成交的其他因素
     */
    @ExcelProperty(value = "影响成交的其他因素")
    private String dealInfluenceOther;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
}
