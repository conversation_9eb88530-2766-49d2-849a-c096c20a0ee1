package com.imhuso.crm.core.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * CRM客户订单统计信息VO
 *
 * <AUTHOR> Assistant
 */
@Data
public class CrmCustomerOrderStatisticsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 累计下单次数
     */
    private Integer totalOrderCount;

    /**
     * 累计下单金额
     */
    private BigDecimal totalOrderAmount;

    /**
     * 第一次下单日期
     */
    private Date firstOrderDate;

    /**
     * 最后一次下单日期
     */
    private Date lastOrderDate;

}
