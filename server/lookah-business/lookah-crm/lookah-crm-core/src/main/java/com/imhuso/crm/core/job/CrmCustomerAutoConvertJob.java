package com.imhuso.crm.core.job;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import com.aizuda.snailjob.common.log.SnailJobLog;

import com.imhuso.crm.core.service.ICrmCustomerService;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Component;

/**
 * CRM客户自动转换定时任务
 * 使用 SnailJob 框架执行定时任务，将超过15天未跟进的客户转为公海客户
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@SuppressWarnings("unused") // SnailJob框架调用，IDE检测不到使用
public class CrmCustomerAutoConvertJob {

    private final ICrmCustomerService crmCustomerService;

    /**
     * 未跟进天数阈值：15天
     */
    private static final int DAYS_THRESHOLD = 15;

    /**
     * 自动将15天未跟进的客户转为公海客户
     * 任务名称：autoConvertToPublicCustomers
     * 执行时间：由 SnailJob 管理平台配置
     *
     * @param jobArgs 任务参数（未使用）
     * @return 执行结果
     */
    @JobExecutor(name = "autoConvertToPublicCustomers")
    public ExecuteResult jobExecute(JobArgs jobArgs) {
        SnailJobLog.LOCAL.info("开始执行CRM客户自动转换任务，将{}天未跟进的客户转为公海客户", DAYS_THRESHOLD);

        try {
            // 调用服务方法，将15天未跟进的客户转为公海客户
            int convertedCount = crmCustomerService.autoConvertToPublicCustomers(DAYS_THRESHOLD);

            String message;
            if (convertedCount > 0) {
                message = String.format("CRM客户自动转换任务执行成功：共转换 %d 个客户为公海客户（未跟进天数：%d 天）",
                    convertedCount, DAYS_THRESHOLD);
                SnailJobLog.LOCAL.info(message);
            } else {
                message = String.format("CRM客户自动转换任务执行完成：没有需要转换的客户（未跟进天数：%d 天）", DAYS_THRESHOLD);
                SnailJobLog.LOCAL.info(message);
            }

            return ExecuteResult.success(message);

        } catch (Exception e) {
            String errorMessage = "CRM客户自动转换任务执行失败: " + e.getMessage();
            SnailJobLog.LOCAL.error(errorMessage, e);
            return ExecuteResult.failure(errorMessage);
        }
    }

    /**
     * 手动触发自动转换任务
     * 任务名称：manualConvertToPublicCustomers
     * 用于手动执行或测试
     *
     * @param jobArgs 任务参数（未使用）
     * @return 执行结果
     */
    @JobExecutor(name = "manualConvertToPublicCustomers")
    public ExecuteResult manualJobExecute(JobArgs jobArgs) {
        SnailJobLog.LOCAL.info("手动触发CRM客户自动转换任务，将{}天未跟进的客户转为公海客户", DAYS_THRESHOLD);

        try {
            // 调用服务方法，将15天未跟进的客户转为公海客户
            int convertedCount = crmCustomerService.autoConvertToPublicCustomers(DAYS_THRESHOLD);

            String message = String.format("手动执行CRM客户自动转换任务完成：共转换 %d 个客户为公海客户（未跟进天数：%d 天）",
                convertedCount, DAYS_THRESHOLD);
            SnailJobLog.LOCAL.info(message);

            return ExecuteResult.success(message);

        } catch (Exception e) {
            String errorMessage = "手动执行CRM客户自动转换任务失败: " + e.getMessage();
            SnailJobLog.LOCAL.error(errorMessage, e);
            return ExecuteResult.failure(errorMessage);
        }
    }
}
