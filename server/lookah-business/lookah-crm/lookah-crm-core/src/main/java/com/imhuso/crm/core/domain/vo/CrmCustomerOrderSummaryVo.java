package com.imhuso.crm.core.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * CRM客户订单汇总统计VO（用于计算星级）
 *
 * <AUTHOR>
 */
@Data
public class CrmCustomerOrderSummaryVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单总数
     */
    private Integer totalOrderCount;

    /**
     * 订单总金额
     */
    private BigDecimal totalOrderAmount;

    /**
     * 平均订单金额
     */
    private BigDecimal avgOrderAmount;

    /**
     * 最大单笔订单金额
     */
    private BigDecimal maxOrderAmount;

}
