package com.imhuso.crm.core.domain.bo;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.imhuso.crm.core.domain.CrmCustomerFollowRecord;

import java.util.Date;

/**
 * CRM客户跟进记录业务对象 crm_customer_follow_record
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CrmCustomerFollowRecord.class, reverseConvertGenerate = false)
public class CrmCustomerFollowRecordBo extends BaseEntity {

    /**
     * 跟进记录ID
     */
    @NotNull(message = "跟进记录ID不能为空", groups = {EditGroup.class})
    private Long followId;

    /**
     * 客户ID
     */
    @NotNull(message = "客户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long customerId;

    /**
     * 跟进日期
     */
    @NotNull(message = "跟进日期不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date followDate;

    /**
     * 跟进内容
     */
    @NotBlank(message = "跟进内容不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 2000, message = "跟进内容长度不能超过2000个字符")
    private String followContent;

    /**
     * 跟进过程存在的难点
     */
    @Size(max = 1000, message = "跟进过程存在的难点长度不能超过1000个字符")
    private String followDifficulties;

    /**
     * 跟进方式
     */
    @NotBlank(message = "跟进方式不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 50, message = "跟进方式长度不能超过50个字符")
    private String followType;

    /**
     * 跟进主题
     */
    @Size(max = 50, message = "跟进主题长度不能超过50个字符")
    private String followTheme;

    /**
     * 跟进客户状态
     */
    @Size(max = 50, message = "跟进客户状态长度不能超过50个字符")
    private String followCustomerStatus;

    /**
     * 跟进结果
     */
    @Size(max = 100, message = "跟进结果长度不能超过100个字符")
    private String followResult;

    /**
     * 下次跟进日期
     */
    private Date nextFollowDate;

    /**
     * 跟进业务员ID
     */
    @NotNull(message = "跟进业务员ID不能为空", groups = {EditGroup.class})
    private Long followUserId;

    /**
     * 跟进业务员姓名
     */
    @NotBlank(message = "跟进业务员姓名不能为空", groups = {EditGroup.class})
    @Size(max = 100, message = "跟进业务员姓名长度不能超过100个字符")
    private String followUserName;

    /**
     * 公司名称（用于查询条件）
     */
    private String companyName;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 跟进附件（逗号分隔的URL字符串）
     */
    private String followAttachments;
    
    /**
     * 跟进附件数组（用于接收前端传递的数组数据）
     */
    @JsonSetter("followAttachmentsArray")
    public void setFollowAttachmentsArray(String[] followAttachmentsArray) {
        if (followAttachmentsArray == null || followAttachmentsArray.length == 0) {
            this.followAttachments = null;
        } else {
            this.followAttachments = String.join(",", followAttachmentsArray);
        }
    }
    
    /**
     * 获取跟进附件数组（用于返回给前端）
     */
    @JsonGetter("followAttachmentsArray")
    public String[] getFollowAttachmentsArray() {
        if (followAttachments == null || followAttachments.trim().isEmpty()) {
            return new String[0];
        }
        return followAttachments.split(",");
    }

    /**
     * 跟进建议（具体的跟进建议和行动计划）
     */
    @Size(max = 1000, message = "跟进建议长度不能超过1000个字符")
    private String followSuggestions;

    /**
     * 是否审批（0-否 1-是）
     */
    @Size(max = 1, message = "是否审批只能为1个字符")
    private String isApprovalRequired;

    /**
     * 是否已读（0-未读 1-已读）
     */
    @Size(max = 1, message = "是否已读只能为1个字符")
    private String isRead;

    /**
     * 审核时间
     */
    private Date approvalTime;

}
