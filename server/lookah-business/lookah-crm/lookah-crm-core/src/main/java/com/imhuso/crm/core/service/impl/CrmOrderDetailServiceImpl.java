package com.imhuso.crm.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.mybatis.annotation.DataColumn;
import com.imhuso.common.mybatis.annotation.DataPermission;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.crm.core.domain.CrmOrderDetail;
import com.imhuso.crm.core.domain.bo.CrmOrderDetailBo;
import com.imhuso.crm.core.domain.vo.CrmOrderDetailVo;
import com.imhuso.crm.core.enums.OrderDetailStatus;
import com.imhuso.crm.core.mapper.CrmOrderDetailMapper;
import com.imhuso.crm.core.service.ICrmOrderDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * CRM客户订单详情Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CrmOrderDetailServiceImpl implements ICrmOrderDetailService {

    private final CrmOrderDetailMapper baseMapper;

    /**
     * 查询CRM客户订单详情
     */
    @Override
    public CrmOrderDetailVo queryById(Long detailId) {
        return baseMapper.selectVoByIdWithOrderInfo(detailId);
    }

    /**
     * 根据订单ID查询订单详情列表
     */
    @Override
    public List<CrmOrderDetailVo> queryByOrderId(Long orderId) {
        LambdaQueryWrapper<CrmOrderDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(CrmOrderDetail::getOrderId, orderId);
        lqw.orderByAsc(CrmOrderDetail::getDetailId);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询CRM客户订单详情列表
     */
    @Override
    @DataPermission({
        @DataColumn(key = "userName", value = "od.create_by")
    })
    public TableDataInfo<CrmOrderDetailVo> queryPageList(CrmOrderDetailBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CrmOrderDetail> lqw = buildQueryWrapper(bo);
        Page<CrmOrderDetailVo> result = baseMapper.selectVoPageWithOrderInfo(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询CRM客户订单详情列表
     */
    @Override
    @DataPermission({
        @DataColumn(key = "userName", value = "od.create_by")
    })
    public List<CrmOrderDetailVo> queryList(CrmOrderDetailBo bo) {
        LambdaQueryWrapper<CrmOrderDetail> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoListWithOrderInfo(lqw);
    }

    private LambdaQueryWrapper<CrmOrderDetail> buildQueryWrapper(CrmOrderDetailBo bo) {
        LambdaQueryWrapper<CrmOrderDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(ObjectUtil.isNotNull(bo.getProductId()), CrmOrderDetail::getProductId, bo.getProductId());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), CrmOrderDetail::getProductName, bo.getProductName());
        lqw.eq(StringUtils.isNotBlank(bo.getProductColor()), CrmOrderDetail::getProductColor, bo.getProductColor());
        lqw.eq(StringUtils.isNotBlank(bo.getDetailStatus()), CrmOrderDetail::getDetailStatus, bo.getDetailStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getIsGift()), CrmOrderDetail::getIsGift, bo.getIsGift());
        // 可能存在字段冲突
        if (ObjectUtil.isNotNull(bo.getOrderId())) {
            lqw.apply("od.order_id = {0}", bo.getOrderId());
        }
        if (ObjectUtil.isNotNull(bo.getOrderNo())) {
            lqw.apply("od.order_no like CONCAT('%', {0}, '%')", bo.getOrderNo());
        }

        if (ObjectUtil.isNotNull(bo.getCompanyName())) {
            lqw.apply("o.company_name like CONCAT('%', {0}, '%')", bo.getCompanyName());
        }

        // 注意：关联字段的查询条件需要在XML中处理，这里暂时不处理
        // 因为这些字段不在CrmOrderDetail表中，而是在关联表中
        return lqw;
    }

    /**
     * 新增CRM客户订单详情
     */
    @Override
    public Boolean insertByBo(CrmOrderDetailBo bo) {
        CrmOrderDetail add = MapstructUtils.convert(bo, CrmOrderDetail.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setDetailId(add.getDetailId());
        }
        return flag;
    }

    /**
     * 修改CRM客户订单详情
     */
    @Override
    public Boolean updateByBo(CrmOrderDetailBo bo) {
        CrmOrderDetail update = MapstructUtils.convert(bo, CrmOrderDetail.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(CrmOrderDetail entity) {
        // 校验必要字段
        if (entity.getOrderId() == null) {
            throw new ServiceException("订单ID不能为空");
        }
        if (StringUtils.isBlank(entity.getProductName())) {
            throw new ServiceException("产品名称不能为空");
        }
        if (entity.getQuantity() == null || entity.getQuantity() <= 0) {
            throw new ServiceException("产品数量必须大于0");
        }
        if (entity.getUnitPrice() == null || entity.getUnitPrice().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("单价不能为负数");
        }
    }

    /**
     * 批量删除CRM客户订单详情
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids) {
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 批量新增订单详情
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<CrmOrderDetailBo> orderDetailList) {
        if (CollUtil.isEmpty(orderDetailList)) {
            return true;
        }

        List<CrmOrderDetail> entityList = MapstructUtils.convert(orderDetailList, CrmOrderDetail.class);
        for (CrmOrderDetail entity : entityList) {
            validEntityBeforeSave(entity);
        }

        return baseMapper.insertBatch(entityList);
    }

    /**
     * 根据订单ID删除订单详情
     */
    @Override
    public Boolean deleteByOrderId(Long orderId) {
        LambdaQueryWrapper<CrmOrderDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(CrmOrderDetail::getOrderId, orderId);
        return baseMapper.delete(lqw) >= 0;
    }

}
