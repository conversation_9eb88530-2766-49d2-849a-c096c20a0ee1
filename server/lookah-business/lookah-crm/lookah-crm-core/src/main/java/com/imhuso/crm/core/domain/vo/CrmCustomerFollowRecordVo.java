package com.imhuso.crm.core.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.imhuso.common.excel.annotation.ExcelDictFormat;
import com.imhuso.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.imhuso.crm.core.domain.CrmCustomerFollowRecord;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * CRM客户跟进记录视图对象 crm_customer_follow_record
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CrmCustomerFollowRecord.class)
public class CrmCustomerFollowRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 跟进记录ID
     */
    @ExcelProperty(value = "跟进记录ID")
    private Long followId;

    /**
     * 客户ID
     */
    @ExcelProperty(value = "客户ID")
    private Long customerId;

    /**
     * 客户编号（连表查询客户表）
     */
    @ExcelProperty(value = "客户编号")
    private String customerCode;

    /**
     * 公司名称（连表查询客户表）
     */
    @ExcelProperty(value = "公司名称")
    private String companyName;

    /**
     * 跟进日期
     */
    @ExcelProperty(value = "跟进日期")
    private Date followDate;

    /**
     * 跟进内容
     */
    @ExcelProperty(value = "跟进内容")
    private String followContent;

    /**
     * 跟进过程存在的难点
     */
    @ExcelProperty(value = "跟进过程存在的难点")
    private String followDifficulties;

    /**
     * 跟进方式
     */
    @ExcelProperty(value = "跟进方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_follow_type")
    private String followType;

    /**
     * 跟进主题
     */
    @ExcelProperty(value = "跟进主题", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_follow_theme")
    private String followTheme;

    /**
     * 跟进客户状态
     */
    @ExcelProperty(value = "跟进客户状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_customer_status")
    private String followCustomerStatus;

    /**
     * 跟进结果
     */
    @ExcelProperty(value = "跟进结果")
    private String followResult;

    /**
     * 下次跟进日期
     */
    @ExcelProperty(value = "下次跟进日期")
    private Date nextFollowDate;

    /**
     * 跟进业务员ID
     */
    @ExcelProperty(value = "跟进业务员ID")
    private Long followUserId;

    /**
     * 跟进业务员姓名
     */
    @ExcelProperty(value = "跟进业务员姓名")
    private String followUserName;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 跟进附件（逗号分隔的URL字符串，返回给前端时转换为数组）
     */
    @ExcelProperty(value = "跟进附件")
    private String followAttachments;
    
    /**
     * 获取跟进附件数组（用于前端显示）
     */
    @JsonGetter("followAttachmentsArray")
    public String[] getFollowAttachmentsArray() {
        if (followAttachments == null || followAttachments.trim().isEmpty()) {
            return new String[0];
        }
        return followAttachments.split(",");
    }

    /**
     * 跟进建议（具体的跟进建议和行动计划）
     */
    @ExcelProperty(value = "跟进建议")
    private String followSuggestions;

    /**
     * 是否审批（0-否 1-是）
     */
    @ExcelProperty(value = "是否审批", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_is_approval")
    private String isApprovalRequired;

    /**
     * 是否已读（0-未读 1-已读）
     */
    @ExcelProperty(value = "是否已读", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_is_read")
    private String isRead;

    /**
     * 审核时间
     */
    @ExcelProperty(value = "审核时间")
    private Date approvalTime;

    /**
     * 创建部门
     */
    @ExcelProperty(value = "创建部门")
    private Long createDept;

    /**
     * 创建者
     */
    @ExcelProperty(value = "创建者")
    private Long createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新者
     */
    @ExcelProperty(value = "更新者")
    private Long updateBy;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

}
