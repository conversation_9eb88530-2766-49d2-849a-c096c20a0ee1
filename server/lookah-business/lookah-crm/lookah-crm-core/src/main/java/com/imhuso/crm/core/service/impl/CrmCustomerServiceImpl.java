package com.imhuso.crm.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.MessageUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.mybatis.annotation.DataColumn;
import com.imhuso.common.mybatis.annotation.DataPermission;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.satoken.utils.LoginHelper;
import com.imhuso.crm.core.constant.CrmConstants;
import com.imhuso.crm.core.enums.FrequencyEnum;
import com.imhuso.crm.core.domain.CrmCustomer;
import com.imhuso.crm.core.domain.bo.CrmCustomerBo;
import com.imhuso.crm.core.domain.vo.CrmCustomerVo;
import com.imhuso.crm.core.domain.vo.CrmCustomerOrderStatisticsVo;
import com.imhuso.crm.core.domain.vo.CrmCustomerInterestedProductVo;
import com.imhuso.crm.core.domain.vo.CrmCustomerFollowStatisticsVo;
import com.imhuso.crm.core.domain.vo.CrmCustomerOrderSummaryVo;
import com.imhuso.crm.core.domain.vo.CustomerOptionVo;
import com.imhuso.crm.core.mapper.CrmCustomerMapper;
import com.imhuso.crm.core.service.ICrmCustomerService;
import com.imhuso.crm.core.service.ICrmCustomerSocialMediaService;
import com.imhuso.crm.core.service.ICrmCustomerFollowRecordService;
import com.imhuso.crm.core.service.ICrmOrderService;
import com.imhuso.crm.core.domain.vo.CrmCustomerSocialMediaVo;
import com.imhuso.crm.core.domain.bo.CrmCustomerSocialMediaBo;
import com.imhuso.crm.core.domain.bo.CrmOrderBo;
import com.imhuso.crm.core.domain.bo.CrmCustomerFollowRecordBo;
import com.imhuso.crm.core.domain.vo.CrmOrderVo;
import com.imhuso.crm.core.domain.vo.CrmCustomerFollowRecordVo;
import com.imhuso.crm.core.domain.CrmOrder;
import com.imhuso.crm.core.domain.CrmCustomerFollowRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * CRM客户信息Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CrmCustomerServiceImpl implements ICrmCustomerService {

    private final CrmCustomerMapper baseMapper;
    private final ICrmCustomerSocialMediaService socialMediaService;
    private final ICrmCustomerFollowRecordService crmCustomerFollowRecordService;
    private final ICrmOrderService crmOrderService;

    public CrmCustomerServiceImpl(CrmCustomerMapper baseMapper,
                                ICrmCustomerSocialMediaService socialMediaService,
                                @Lazy ICrmCustomerFollowRecordService crmCustomerFollowRecordService,
                                @Lazy ICrmOrderService crmOrderService) {
        this.baseMapper = baseMapper;
        this.socialMediaService = socialMediaService;
        this.crmCustomerFollowRecordService = crmCustomerFollowRecordService;
        this.crmOrderService = crmOrderService;
    }

    /**
     * 查询CRM客户信息
     */
    @Override
    public CrmCustomerVo queryById(Long customerId) {
        CrmCustomerVo customerVo = baseMapper.selectVoById(customerId);
        if (customerVo != null) {
            // 查询客户的社媒账号列表
            List<CrmCustomerSocialMediaVo> socialMediaList = socialMediaService.queryByCustomerId(customerId);
            customerVo.setSocialMediaList(socialMediaList);
        }
        return customerVo;
    }

    /**
     * 查询CRM客户信息列表
     */
    @Override
    @DataPermission({
        @DataColumn(key = "userName",value = "sales_user_id")
    })
    public TableDataInfo<CrmCustomerVo> queryPageList(CrmCustomerBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CrmCustomer> lqw = buildQueryWrapper(bo);
        Page<CrmCustomerVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询CRM客户信息列表
     */
    @Override
    @DataPermission({
        @DataColumn(key = "userName",value = "sales_user_id")
    })
    public List<CrmCustomerVo> queryList(CrmCustomerBo bo) {
        LambdaQueryWrapper<CrmCustomer> lqw = buildQueryWrapper(bo);
        List<CrmCustomerVo> customerList = baseMapper.selectVoList(lqw);

        // 填充社交媒体数据用于导出
        for (CrmCustomerVo customerVo : customerList) {
            fillSocialMediaForExport(customerVo);
        }

        return customerList;
    }

    private LambdaQueryWrapper<CrmCustomer> buildQueryWrapper(CrmCustomerBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CrmCustomer> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getCustomerCode()), CrmCustomer::getCustomerCode, bo.getCustomerCode());
        lqw.like(StringUtils.isNotBlank(bo.getCompanyName()), CrmCustomer::getCompanyName, bo.getCompanyName());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerType()), CrmCustomer::getCustomerType, bo.getCustomerType());
        lqw.eq(StringUtils.isNotBlank(bo.getCountry()), CrmCustomer::getCountry, bo.getCountry());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), CrmCustomer::getState, bo.getState());
        lqw.like(StringUtils.isNotBlank(bo.getAccountEmail()), CrmCustomer::getAccountEmail, bo.getAccountEmail());
        lqw.like(StringUtils.isNotBlank(bo.getContactEmail()), CrmCustomer::getContactEmail, bo.getContactEmail());
        lqw.like(StringUtils.isNotBlank(bo.getContactPerson()), CrmCustomer::getContactPerson, bo.getContactPerson());
        lqw.eq(StringUtils.isNotBlank(bo.getPosition()), CrmCustomer::getPosition, bo.getPosition());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerSource()), CrmCustomer::getCustomerSource, bo.getCustomerSource());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerStatus()), CrmCustomer::getCustomerStatus, bo.getCustomerStatus());
        lqw.eq(ObjectUtil.isNotNull(bo.getStarRating()), CrmCustomer::getStarRating, bo.getStarRating());
        lqw.eq(ObjectUtil.isNotNull(bo.getSalesUserId()), CrmCustomer::getSalesUserId, bo.getSalesUserId());
        lqw.like(StringUtils.isNotBlank(bo.getSalesUserName()), CrmCustomer::getSalesUserName, bo.getSalesUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getIsPublic()), CrmCustomer::getIsPublic, bo.getIsPublic());
        lqw.eq(StringUtils.isNotBlank(bo.getIsNewCustomer()), CrmCustomer::getIsNewCustomer, bo.getIsNewCustomer());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerDescription()), CrmCustomer::getCustomerDescription, bo.getCustomerDescription());
        lqw.eq(ObjectUtil.isNotNull(bo.getSystemStarRating()), CrmCustomer::getSystemStarRating, bo.getSystemStarRating());
        lqw.eq(StringUtils.isNotBlank(bo.getPurchaseFrequency()), CrmCustomer::getPurchaseFrequency, bo.getPurchaseFrequency());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderFrequency()), CrmCustomer::getOrderFrequency, bo.getOrderFrequency());

        // 创建时间范围查询
        lqw.between(params.get("beginTime") != null && params.get("endTime") != null,
            CrmCustomer::getCreateTime, params.get("beginTime"), params.get("endTime"));

        // 最后跟进时间范围查询
        lqw.between(params.get("beginFollowTime") != null && params.get("endFollowTime") != null,
            CrmCustomer::getLastFollowTime, params.get("beginFollowTime"), params.get("endFollowTime"));

        lqw.orderByDesc(CrmCustomer::getCreateTime);
        return lqw;
    }

    /**
     * 新增CRM客户信息
     */
    @Override
    public Boolean insertByBo(CrmCustomerBo bo) {
        // 处理影响成交因素数组转换
        processDealInfluenceFactorsArray(bo);

        CrmCustomer add = MapstructUtils.convert(bo, CrmCustomer.class);
        validEntityBeforeSave(add);

        // 如果客户编号为空，自动生成
        if (StringUtils.isBlank(add.getCustomerCode())) {
            add.setCustomerCode(generateCustomerCode());
        }

        // 检查账户邮箱唯一性（仅在活跃记录中）
        if (!checkAccountEmailUniqueInActive(add.getAccountEmail(), null)) {
            throw new ServiceException("账户邮箱已存在");
        }

        // 检查客户编号唯一性（仅在活跃记录中）
        if (!checkCustomerCodeUniqueInActive(add.getCustomerCode(), null)) {
            throw new ServiceException("客户编号已存在");
        }

        // 确保删除时间戳为空（新增的记录）
        add.setDeletedAt(null);

        // 设置默认值
        if (ObjectUtil.isNull(add.getStarRating())) {
            add.setStarRating(CrmConstants.STAR_RATING_DEFAULT);
        }
        if (ObjectUtil.isNull(add.getTotalOrderCount())) {
            add.setTotalOrderCount(0);
        }
        if (ObjectUtil.isNull(add.getTotalOrderAmount())) {
            add.setTotalOrderAmount(BigDecimal.ZERO);
        }
        if (StringUtils.isBlank(add.getIsPublic())) {
            add.setIsPublic(CrmConstants.IS_PUBLIC_NO);
        }
        if (StringUtils.isBlank(add.getCustomerStatus())) {
            add.setCustomerStatus(CrmConstants.CUSTOMER_STATUS_POTENTIAL);
        }

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setCustomerId(add.getCustomerId());
            // 保存社媒账号列表
            List<CrmCustomerSocialMediaBo> socialMediaList = buildSocialMediaList(bo);
            if (CollUtil.isNotEmpty(socialMediaList)) {
                socialMediaService.saveBatchByCustomerId(add.getCustomerId(), socialMediaList);
            }
        }
        return flag;
    }

    /**
     * 修改CRM客户信息
     */
    @Override
    public Boolean updateByBo(CrmCustomerBo bo) {
        // 处理影响成交因素数组转换
        processDealInfluenceFactorsArray(bo);

        CrmCustomer update = MapstructUtils.convert(bo, CrmCustomer.class);
        validEntityBeforeSave(update);

        // 检查账户邮箱唯一性（仅在活跃记录中，排除自己）
        if (!checkAccountEmailUniqueInActive(update.getAccountEmail(), update.getCustomerId())) {
            throw new ServiceException("账户邮箱已存在");
        }

        // 检查客户编号唯一性（仅在活跃记录中，排除自己）
        if (StringUtils.isNotBlank(update.getCustomerCode()) &&
            !checkCustomerCodeUniqueInActive(update.getCustomerCode(), update.getCustomerId())) {
            throw new ServiceException("客户编号已存在");
        }

        boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            // 更新社媒账号列表
            List<CrmCustomerSocialMediaBo> socialMediaList = buildSocialMediaList(bo);
            socialMediaService.saveBatchByCustomerId(update.getCustomerId(), socialMediaList);
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(CrmCustomer entity) {
        // 校验客户编号唯一性
        if (!checkCustomerCodeUnique(entity.getCustomerCode(), entity.getCustomerId())) {
            throw new ServiceException(MessageUtils.message("crm.customer.code.not.unique"));
        }

        // 校验账号邮箱唯一性
        if (!checkAccountEmailUnique(entity.getAccountEmail(), entity.getCustomerId())) {
            throw new ServiceException(MessageUtils.message("crm.customer.account.email.not.unique"));
        }
    }

    /**
     * 删除前的业务校验
     */
    private void validateCustomersBeforeDelete(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }

        // 查询要删除的客户
        List<CrmCustomer> customers = baseMapper.selectList(
            new LambdaQueryWrapper<CrmCustomer>()
                .in(CrmCustomer::getCustomerId, ids)
                .eq(CrmCustomer::getDelFlag, BusinessConstants.LOGIC_NOT_DELETE)
        );
        for (CrmCustomer customer : customers) {
            // 检查客户是否有关联的订单
            long orderCount = countOrdersByCustomerId(customer.getCustomerId());
            if (orderCount > 0) {
                throw new ServiceException("客户【" + customer.getCompanyName() + "】存在关联订单，不能删除");
            }

            // 检查客户是否有关联的跟进记录
            long followCount = countFollowRecordsByCustomerId(customer.getCustomerId());
            if (followCount > 0) {
                throw new ServiceException("客户【" + customer.getCompanyName() + "】存在跟进记录，不能删除");
            }
        }
    }

    /**
     * 批量删除CRM客户信息（软删除，设置删除时间戳）
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        // 执行业务校验：检查客户是否可以删除
        if (isValid) {
            validateCustomersBeforeDelete(ids);
        }

        // 软删除：设置删除标志和删除时间戳
        if (CollUtil.isNotEmpty(ids)) {
            long currentTimestamp = System.currentTimeMillis();

            // 批量更新删除标志和删除时间戳
            LambdaUpdateWrapper<CrmCustomer> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(CrmCustomer::getCustomerId, ids)
                .set(CrmCustomer::getDelFlag, BusinessConstants.LOGIC_DELETE)
                .set(CrmCustomer::getDeletedAt, currentTimestamp);

            return baseMapper.update(null, updateWrapper) > 0;
        }

        return false;
    }

    /**
     * 生成客户编号
     */
    @Override
    public String generateCustomerCode() {
        // 获取当前日期时间
        String dateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        // 查询当天最大的客户编号
        LambdaQueryWrapper<CrmCustomer> lqw = Wrappers.lambdaQuery();
        lqw.likeRight(CrmCustomer::getCustomerCode, CrmConstants.CUSTOMER_CODE_PREFIX + dateTime)
            .orderByDesc(CrmCustomer::getCustomerCode)
            .last("LIMIT 1");

        CrmCustomer lastCustomer = baseMapper.selectOne(lqw);

        int sequence = 1;
        if (ObjectUtil.isNotNull(lastCustomer) && StringUtils.isNotBlank(lastCustomer.getCustomerCode())) {
            String lastCode = lastCustomer.getCustomerCode();
            // 提取序号部分
            String sequenceStr = lastCode.substring(lastCode.length() - 5);
            try {
                sequence = Integer.parseInt(sequenceStr) + 1;
            } catch (NumberFormatException e) {
                log.warn("解析客户编号序号失败: {}", lastCode);
            }
        }

        return String.format("%s%s%05d", CrmConstants.CUSTOMER_CODE_PREFIX, dateTime, sequence);
    }

    /**
     * 检查客户编号是否唯一
     */
    @Override
    public Boolean checkCustomerCodeUnique(String customerCode, Long customerId) {
        LambdaQueryWrapper<CrmCustomer> lqw = Wrappers.lambdaQuery();
        lqw.eq(CrmCustomer::getCustomerCode, customerCode);
        if (ObjectUtil.isNotNull(customerId)) {
            lqw.ne(CrmCustomer::getCustomerId, customerId);
        }
        return baseMapper.selectCount(lqw) == 0;
    }

    /**
     * 检查账号邮箱是否唯一
     */
    @Override
    public Boolean checkAccountEmailUnique(String accountEmail, Long customerId) {
        LambdaQueryWrapper<CrmCustomer> lqw = Wrappers.lambdaQuery();
        lqw.eq(CrmCustomer::getAccountEmail, accountEmail);
        if (ObjectUtil.isNotNull(customerId)) {
            lqw.ne(CrmCustomer::getCustomerId, customerId);
        }
        return baseMapper.selectCount(lqw) == 0;
    }

    /**
     * 更新客户统计信息
     */
    @Override
    public void updateCustomerStatistics(Long customerId, Integer orderCount, BigDecimal orderAmount) {
        CrmCustomer customer = new CrmCustomer();
        customer.setCustomerId(customerId);
        customer.setTotalOrderCount(orderCount);
        customer.setTotalOrderAmount(orderAmount);
        baseMapper.updateById(customer);
    }

    /**
     * 更新客户订单统计信息（包含所有订单相关字段）
     */
    @Override
    public void updateCustomerOrderStatistics(Long customerId) {
        // 查询客户的所有订单统计信息
        CrmCustomerOrderStatisticsVo orderStats = baseMapper.selectCustomerOrderStatistics(customerId);

        if (orderStats != null) {
            CrmCustomer customer = new CrmCustomer();
            customer.setCustomerId(customerId);

            // 累计下单次数
            Integer totalOrderCount = orderStats.getTotalOrderCount();
            customer.setTotalOrderCount(totalOrderCount != null ? totalOrderCount : 0);

            // 累计下单金额
            BigDecimal totalOrderAmount = orderStats.getTotalOrderAmount();
            customer.setTotalOrderAmount(totalOrderAmount != null ? totalOrderAmount : BigDecimal.ZERO);

            // 第一次下单日期
            Date firstOrderDate = orderStats.getFirstOrderDate();
            customer.setFirstOrderDate(firstOrderDate);

            // 最后一次下单日期
            Date lastOrderDate = orderStats.getLastOrderDate();
            customer.setLastOrderDate(lastOrderDate);

            // 计算下单频率（基于订单数量和时间跨度）
            String orderFrequency = calculateOrderFrequency(totalOrderCount, firstOrderDate, lastOrderDate);
            customer.setOrderFrequency(orderFrequency);

            baseMapper.updateById(customer);
        }
    }

    /**
     * 计算下单频率
     */
    private String calculateOrderFrequency(Integer orderCount, Date firstOrderDate, Date lastOrderDate) {
        if (orderCount == null || orderCount <= 1 || firstOrderDate == null || lastOrderDate == null) {
            return FrequencyEnum.IRREGULAR.getCode(); // 不规律
        }

        try {
            // 将Date转换为LocalDateTime
            LocalDateTime first = firstOrderDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
            LocalDateTime last = lastOrderDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

            long daysBetween = java.time.Duration.between(first, last).toDays();

            if (daysBetween <= 0) {
                return FrequencyEnum.IRREGULAR.getCode();
            }

            // 计算平均下单间隔天数
            double avgDaysBetweenOrders = (double) daysBetween / (orderCount - 1);

            if (avgDaysBetweenOrders <= 10) {
                return FrequencyEnum.WEEKLY.getCode(); // 每周：平均10天内下单一次
            } else if (avgDaysBetweenOrders <= 35) {
                return FrequencyEnum.MONTHLY.getCode(); // 每月：平均35天内下单一次
            } else if (avgDaysBetweenOrders <= 100) {
                return FrequencyEnum.QUARTERLY.getCode(); // 每季度：平均100天内下单一次
            } else {
                return FrequencyEnum.IRREGULAR.getCode(); // 不规律：平均100天以上下单一次
            }
        } catch (Exception e) {
            log.warn("计算下单频率失败: {}", e.getMessage());
            return FrequencyEnum.IRREGULAR.getCode();
        }
    }

    /**
     * 获取客户的下单频率
     */
    private String getCustomerOrderFrequency(Long customerId) {
        if (customerId == null) {
            return FrequencyEnum.IRREGULAR.getCode();
        }

        CrmCustomer customer = baseMapper.selectById(customerId);
        if (customer == null || StringUtils.isBlank(customer.getOrderFrequency())) {
            return FrequencyEnum.IRREGULAR.getCode();
        }

        return customer.getOrderFrequency();
    }

    /**
     * 更新客户星级评定
     */
    @Override
    public void updateCustomerStarRating(Long customerId, Integer starRating) {
        CrmCustomer customer = new CrmCustomer();
        customer.setCustomerId(customerId);
        customer.setStarRating(starRating);
        baseMapper.updateById(customer);
    }

    /**
     * 更新最后跟进时间
     */
    @Override
    public void updateLastFollowTime(Long customerId) {
        CrmCustomer customer = new CrmCustomer();
        customer.setCustomerId(customerId);
        customer.setLastFollowTime(new Date());
        baseMapper.updateById(customer);
    }

    /**
     * 统计并更新客户感兴趣的产品名称
     */
    @Override
    public void updateInterestedProducts(Long customerId) {
        try {
            // 使用Mapper查询感兴趣产品统计数据
            List<CrmCustomerInterestedProductVo> results = baseMapper.selectInterestedProductsByCustomerId(customerId);

            if (CollUtil.isNotEmpty(results)) {
                // 将产品名称按数量从多到少用逗号连接
                String interestedProducts = results.stream()
                    .map(CrmCustomerInterestedProductVo::getProductName)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining(","));

                // 更新客户的感兴趣产品字段
                CrmCustomer customer = new CrmCustomer();
                customer.setCustomerId(customerId);
                customer.setInterestedProducts(interestedProducts);
                baseMapper.updateById(customer);

                log.info("更新客户感兴趣产品成功，客户ID: {}, 产品: {}", customerId, interestedProducts);
            }
        } catch (Exception e) {
            log.error("更新客户感兴趣产品失败，客户ID: {}, 错误: {}", customerId, e.getMessage(), e);
        }
    }

    /**
     * 统计并更新客户联系频次
     */
    @Override
    public void updateContactFrequency(Long customerId) {
        try {
            // 查询客户跟进记录统计信息
            CrmCustomerFollowStatisticsVo statistics = baseMapper.selectFollowStatisticsByCustomerId(customerId);

            if (statistics != null && statistics.getTotalCount() != null) {
                int totalCount = statistics.getTotalCount();

                if (totalCount > 1) {
                    // 获取时间跨度（天数）
                    int dateSpanDays = statistics.getDateSpanDays() != null ?
                        statistics.getDateSpanDays() : 0;

                    // 根据时间间隔计算联系频次
                    String contactFrequency = calculateContactFrequencyByTimeInterval(totalCount, dateSpanDays);

                    // 更新客户的联系频次字段
                    CrmCustomer customer = new CrmCustomer();
                    customer.setCustomerId(customerId);
                    customer.setContactFrequency(contactFrequency);
                    baseMapper.updateById(customer);

                    log.info("更新客户联系频次成功，客户ID: {}, 跟进次数: {}, 时间跨度: {}天, 联系频次: {}",
                        customerId, totalCount, dateSpanDays, contactFrequency);
                } else if (totalCount == 1) {
                    // 只有一次跟进记录，设为不定期
                    CrmCustomer customer = new CrmCustomer();
                    customer.setCustomerId(customerId);
                    customer.setContactFrequency(FrequencyEnum.IRREGULAR.getCode());
                    baseMapper.updateById(customer);

                    log.info("更新客户联系频次成功，客户ID: {}, 跟进次数: 1, 联系频次: {}", customerId, FrequencyEnum.IRREGULAR.getCode());
                }
            }
        } catch (Exception e) {
            log.error("更新客户联系频次失败，客户ID: {}, 错误: {}", customerId, e.getMessage(), e);
        }
    }

    /**
     * 根据时间间隔计算联系频次
     *
     * @param totalCount 总跟进次数
     * @param dateSpanDays 时间跨度（天数）
     * @return 联系频次
     */
    private String calculateContactFrequencyByTimeInterval(int totalCount, int dateSpanDays) {
        if (dateSpanDays <= 0 || totalCount <= 1) {
            return FrequencyEnum.IRREGULAR.getCode(); // 不定期
        }

        // 计算平均间隔天数
        double avgIntervalDays = (double) dateSpanDays / (totalCount - 1);

        if (avgIntervalDays <= 2) {
            return FrequencyEnum.DAILY.getCode(); // 每天：平均2天内联系一次
        } else if (avgIntervalDays <= 10) {
            return FrequencyEnum.WEEKLY.getCode(); // 每周：平均10天内联系一次
        } else if (avgIntervalDays <= 35) {
            return FrequencyEnum.MONTHLY.getCode(); // 每月：平均35天内联系一次
        } else if (avgIntervalDays <= 100) {
            return FrequencyEnum.QUARTERLY.getCode(); // 每季度：平均100天内联系一次
        } else {
            return FrequencyEnum.IRREGULAR.getCode(); // 不定期：超过100天才联系一次
        }
    }

    /**
     * 根据订单总金额和数量计算并更新客户星级
     */
    @Override
    public void updateCustomerStarRating(Long customerId) {
        try {
            // 查询客户订单汇总统计
            CrmCustomerOrderSummaryVo orderSummary = baseMapper.selectOrderSummaryByCustomerId(customerId);

            if (orderSummary != null) {
                // 计算客户星级
                Integer starRating = calculateStarRating(customerId, orderSummary);

                // 更新客户的星级字段
                CrmCustomer customer = new CrmCustomer();
                customer.setCustomerId(customerId);
                customer.setSystemStarRating(starRating);
                baseMapper.updateById(customer);

                log.info("更新客户星级成功，客户ID: {}, 订单总数: {}, 订单总金额: {}, 星级: {}",
                    customerId, orderSummary.getTotalOrderCount(), orderSummary.getTotalOrderAmount(), starRating);
            }
        } catch (Exception e) {
            log.error("更新客户星级失败，客户ID: {}, 错误: {}", customerId, e.getMessage(), e);
        }
    }

    /**
     * 将客户更新为老客户
     */
    @Override
    public void updateCustomerToOldCustomer(Long customerId) {
        try {
            // 查询客户当前状态
            CrmCustomer customer = baseMapper.selectById(customerId);
            if (customer == null) {
                log.warn("客户不存在，无法更新为老客户，客户ID: {}", customerId);
                return;
            }

            // 如果客户已经是老客户，则不需要更新
            if (BusinessConstants.NO_NUMBER_STRING.equals(customer.getIsNewCustomer())) {
                log.debug("客户已经是老客户，无需更新，客户ID: {}", customerId);
                return;
            }

            // 更新客户为老客户
            CrmCustomer updateCustomer = new CrmCustomer();
            updateCustomer.setCustomerId(customerId);
            updateCustomer.setIsNewCustomer(BusinessConstants.NO_NUMBER_STRING); // 设置为老客户
            baseMapper.updateById(updateCustomer);

            log.info("成功将客户更新为老客户，客户ID: {}", customerId);
        } catch (Exception e) {
            log.error("将客户更新为老客户失败，客户ID: {}, 错误: {}", customerId, e.getMessage(), e);
        }
    }

    /**
     * 更新客户状态
     */
    @Override
    public void updateCustomerStatus(Long customerId, String customerStatus) {
        try {
            // 查询客户当前状态
            CrmCustomer customer = baseMapper.selectById(customerId);
            if (customer == null) {
                log.warn("客户不存在，无法更新客户状态，客户ID: {}", customerId);
                return;
            }

            // 如果客户状态没有变化，则不需要更新
            if (Objects.equals(customer.getCustomerStatus(), customerStatus)) {
                log.debug("客户状态未发生变化，无需更新，客户ID: {}, 状态: {}", customerId, customerStatus);
                return;
            }

            // 更新客户状态
            CrmCustomer updateCustomer = new CrmCustomer();
            updateCustomer.setCustomerId(customerId);
            updateCustomer.setCustomerStatus(customerStatus);
            baseMapper.updateById(updateCustomer);

            log.info("成功更新客户状态，客户ID: {}, 原状态: {}, 新状态: {}",
                customerId, customer.getCustomerStatus(), customerStatus);
        } catch (Exception e) {
            log.error("更新客户状态失败，客户ID: {}, 状态: {}, 错误: {}", customerId, customerStatus, e.getMessage(), e);
        }
    }

    /**
     * 根据订单统计信息计算客户星级
     *
     * @param customerId 客户ID
     * @param orderSummary 订单汇总统计
     * @return 客户星级
     */
    private Integer calculateStarRating(Long customerId, CrmCustomerOrderSummaryVo orderSummary) {
        if (orderSummary == null || orderSummary.getTotalOrderCount() == null || orderSummary.getTotalOrderCount() == 0) {
            return 1; // 无订单，1星
        }

        int orderCount = orderSummary.getTotalOrderCount();
        BigDecimal totalAmount = orderSummary.getTotalOrderAmount() != null ? orderSummary.getTotalOrderAmount() : BigDecimal.ZERO;
        BigDecimal avgAmount = orderSummary.getAvgOrderAmount() != null ? orderSummary.getAvgOrderAmount() : BigDecimal.ZERO;

        // 获取客户的下单频率
        String orderFrequency = getCustomerOrderFrequency(customerId);

        // 星级计算逻辑（总分100分）
        int score = 0;

        // 根据订单总数评分（最高25分）
        if (orderCount >= 50) {
            score += 25;
        } else if (orderCount >= 20) {
            score += 20;
        } else if (orderCount >= 10) {
            score += 16;
        } else if (orderCount >= 5) {
            score += 12;
        } else if (orderCount >= 2) {
            score += 8;
        } else {
            score += 4;
        }

        // 根据订单总金额评分（最高20分）
        if (totalAmount.compareTo(new BigDecimal("100000")) >= 0) { // 10万以上
            score += 20;
        } else if (totalAmount.compareTo(new BigDecimal("50000")) >= 0) { // 5万以上
            score += 17;
        } else if (totalAmount.compareTo(new BigDecimal("20000")) >= 0) { // 2万以上
            score += 14;
        } else if (totalAmount.compareTo(new BigDecimal("10000")) >= 0) { // 1万以上
            score += 11;
        } else if (totalAmount.compareTo(new BigDecimal("5000")) >= 0) { // 5千以上
            score += 8;
        } else if (totalAmount.compareTo(new BigDecimal("1000")) >= 0) { // 1千以上
            score += 5;
        } else {
            score += 2;
        }

        // 根据平均订单金额评分（最高20分）
        if (avgAmount.compareTo(new BigDecimal("5000")) >= 0) { // 平均5千以上
            score += 20;
        } else if (avgAmount.compareTo(new BigDecimal("2000")) >= 0) { // 平均2千以上
            score += 16;
        } else if (avgAmount.compareTo(new BigDecimal("1000")) >= 0) { // 平均1千以上
            score += 12;
        } else if (avgAmount.compareTo(new BigDecimal("500")) >= 0) { // 平均500以上
            score += 8;
        } else if (avgAmount.compareTo(new BigDecimal("200")) >= 0) { // 平均200以上
            score += 4;
        } else {
            score += 2;
        }

        // 根据下单频率评分（最高35分）
        if (FrequencyEnum.WEEKLY.getCode().equals(orderFrequency)) { // 每周下单
            score += 35;
        } else if (FrequencyEnum.MONTHLY.getCode().equals(orderFrequency)) { // 每月下单（满分条件）
            score += 35;
        } else if (FrequencyEnum.QUARTERLY.getCode().equals(orderFrequency)) { // 每季度下单
            score += 20;
        } else { // 不规律下单
            score += 5;
        }

        // 根据总分确定星级
        if (score >= 90) {
            return 5; // 5星：90-100分
        } else if (score >= 75) {
            return 4; // 4星：75-89分
        } else if (score >= 60) {
            return 3; // 3星：60-74分
        } else if (score >= 40) {
            return 2; // 2星：40-59分
        } else {
            return 1; // 1星：0-39分
        }
    }

    /**
     * 查询我的客户列表（根据当前登录用户）
     */
    @Override
    public TableDataInfo<CrmCustomerVo> queryMyCustomerPageList(CrmCustomerBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CrmCustomer> lqw = buildQueryWrapper(bo);
        // 只查询当前用户负责的客户
        Long currentUserId = LoginHelper.getUserId();
        lqw.eq(CrmCustomer::getSalesUserId, currentUserId);
        lqw.eq(CrmCustomer::getIsPublic, CrmConstants.IS_PUBLIC_NO); // 非公海客户

        Page<CrmCustomerVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 根据客户编号查询客户
     */
    @Override
    public CrmCustomerVo selectCustomerByCode(String customerCode) {
        LambdaQueryWrapper<CrmCustomer> lqw = new LambdaQueryWrapper<>();
        lqw.eq(CrmCustomer::getCustomerCode, customerCode);
        return baseMapper.selectVoOne(lqw);
    }

    /**
     * 检查账户邮箱唯一性（仅在未删除的记录中检查）
     */
    @Override
    public boolean checkAccountEmailUniqueInActive(String accountEmail, Long customerId) {
        LambdaQueryWrapper<CrmCustomer> lqw = new LambdaQueryWrapper<>();
        lqw.eq(CrmCustomer::getAccountEmail, accountEmail);
        lqw.eq(CrmCustomer::getDelFlag, BusinessConstants.LOGIC_NOT_DELETE); // 只检查未删除的记录
        lqw.isNull(CrmCustomer::getDeletedAt); // 删除时间戳为空
        if (ObjectUtil.isNotNull(customerId)) {
            lqw.ne(CrmCustomer::getCustomerId, customerId); // 修改时排除自己
        }
        return baseMapper.selectCount(lqw) == 0;
    }

    /**
     * 检查客户编号唯一性（仅在未删除的记录中检查）
     */
    @Override
    public boolean checkCustomerCodeUniqueInActive(String customerCode, Long customerId) {
        LambdaQueryWrapper<CrmCustomer> lqw = new LambdaQueryWrapper<>();
        lqw.eq(CrmCustomer::getCustomerCode, customerCode);
        lqw.eq(CrmCustomer::getDelFlag, BusinessConstants.LOGIC_NOT_DELETE); // 只检查未删除的记录
        lqw.isNull(CrmCustomer::getDeletedAt); // 删除时间戳为空
        if (ObjectUtil.isNotNull(customerId)) {
            lqw.ne(CrmCustomer::getCustomerId, customerId); // 修改时排除自己
        }
        return baseMapper.selectCount(lqw) == 0;
    }

    /**
     * 查询公海客户列表
     */
    @Override
    public TableDataInfo<CrmCustomerVo> queryPublicCustomerPageList(CrmCustomerBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CrmCustomer> lqw = buildQueryWrapper(bo);
        lqw.eq(CrmCustomer::getIsPublic, CrmConstants.IS_PUBLIC_YES); // 公海客户

        Page<CrmCustomerVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 自动将超过指定天数未跟进的客户转为公海客户
     */
    @Override
    public int autoConvertToPublicCustomers(int days) {
        log.info("开始执行自动转换公海客户任务，未跟进天数阈值：{} 天", days);

        // 计算阈值日期
        Date thresholdDate = DateUtil.offsetDay(new Date(), -days);

        // 查询超过指定天数未跟进的私有客户
        LambdaQueryWrapper<CrmCustomer> lqw = new LambdaQueryWrapper<>();
        lqw.eq(CrmCustomer::getIsPublic, CrmConstants.IS_PUBLIC_NO) // 私有客户
           .and(wrapper -> wrapper
               .isNull(CrmCustomer::getLastFollowTime) // 从未跟进过
               .or()
               .lt(CrmCustomer::getLastFollowTime, thresholdDate) // 或者超过阈值时间未跟进
           )
           .eq(CrmCustomer::getDelFlag, BusinessConstants.LOGIC_NOT_DELETE) // 未删除
           .isNull(CrmCustomer::getDeletedAt); // 删除时间戳为空

        List<CrmCustomer> customersToConvert = baseMapper.selectList(lqw);

        if (CollUtil.isEmpty(customersToConvert)) {
            log.info("没有需要转换为公海客户的记录");
            return 0;
        }

        // 批量更新为公海客户
        int convertedCount = 0;
        for (CrmCustomer customer : customersToConvert) {
            try {
                CrmCustomer updateCustomer = new CrmCustomer();
                updateCustomer.setCustomerId(customer.getCustomerId());
                updateCustomer.setIsPublic(CrmConstants.IS_PUBLIC_YES);
                updateCustomer.setSalesUserId(0L); // 清空业务员
                updateCustomer.setSalesUserName(null); // 清空业务员姓名

                if (baseMapper.updateById(updateCustomer) > 0) {
                    convertedCount++;
                    log.debug("客户 {} ({}) 已转换为公海客户",
                        customer.getCompanyName(), customer.getCustomerCode());
                }
            } catch (Exception e) {
                log.error("转换客户 {} 为公海客户失败: {}",
                    customer.getCustomerCode(), e.getMessage());
            }
        }

        log.info("自动转换公海客户任务完成，共转换 {} 个客户", convertedCount);
        return convertedCount;
    }

    /**
     * 将客户转为公海客户
     */
    @Override
    public Boolean convertToPublicCustomer(Long customerId) {
        CrmCustomer customer = baseMapper.selectById(customerId);
        if (ObjectUtil.isNull(customer)) {
            throw new ServiceException("客户不存在");
        }

        if (CrmConstants.IS_PUBLIC_YES.equals(customer.getIsPublic())) {
            throw new ServiceException("客户已经是公海客户");
        }

        CrmCustomer updateCustomer = new CrmCustomer();
        updateCustomer.setCustomerId(customerId);
        updateCustomer.setIsPublic(CrmConstants.IS_PUBLIC_YES);
        updateCustomer.setSalesUserId(null); // 清空业务员
        updateCustomer.setSalesUserName(null); // 清空业务员姓名

        boolean result = baseMapper.updateById(updateCustomer) > 0;
        if (result) {
            log.info("客户 {} ({}) 已转换为公海客户",
                customer.getCompanyName(), customer.getCustomerCode());
        }
        return result;
    }

    /**
     * 将公海客户分配给业务员
     */
    @Override
    public Boolean assignPublicCustomerToSales(Long customerId, Long salesUserId, String salesUserName) {
        CrmCustomer customer = baseMapper.selectById(customerId);
        if (ObjectUtil.isNull(customer)) {
            throw new ServiceException("客户不存在");
        }

        if (CrmConstants.IS_PUBLIC_NO.equals(customer.getIsPublic())) {
            throw new ServiceException("客户不是公海客户，无法分配");
        }

        CrmCustomer updateCustomer = new CrmCustomer();
        updateCustomer.setCustomerId(customerId);
        updateCustomer.setIsPublic(CrmConstants.IS_PUBLIC_NO); // 转为私有客户
        updateCustomer.setSalesUserId(salesUserId);
        updateCustomer.setSalesUserName(salesUserName);

        boolean result = baseMapper.updateById(updateCustomer) > 0;
        if (result) {
            log.info("公海客户 {} ({}) 已分配给业务员 {} (ID: {})",
                customer.getCompanyName(), customer.getCustomerCode(), salesUserName, salesUserId);
        }
        return result;
    }

    /**
     * 批量分配公海客户给业务员
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchAssignPublicCustomersToSales(List<Long> customerIds, Long salesUserId, String salesUserName) {
        if (CollUtil.isEmpty(customerIds)) {
            throw new ServiceException("客户ID列表不能为空");
        }

        if (ObjectUtil.isNull(salesUserId)) {
            throw new ServiceException("业务员ID不能为空");
        }

        if (StringUtils.isBlank(salesUserName)) {
            throw new ServiceException("业务员姓名不能为空");
        }

        log.info("开始批量分配公海客户，客户数量: {}, 业务员: {} (ID: {})",
            customerIds.size(), salesUserName, salesUserId);

        int successCount = 0;
        int failCount = 0;

        for (Long customerId : customerIds) {
            try {
                // 查询客户信息
                CrmCustomer customer = baseMapper.selectById(customerId);
                if (ObjectUtil.isNull(customer)) {
                    log.warn("客户不存在，跳过分配: {}", customerId);
                    failCount++;
                    continue;
                }

                // 检查是否为公海客户
                if (CrmConstants.IS_PUBLIC_NO.equals(customer.getIsPublic())) {
                    log.warn("客户 {} ({}) 不是公海客户，跳过分配",
                        customer.getCompanyName(), customer.getCustomerCode());
                    failCount++;
                    continue;
                }

                // 执行分配
                CrmCustomer updateCustomer = new CrmCustomer();
                updateCustomer.setCustomerId(customerId);
                updateCustomer.setIsPublic(CrmConstants.IS_PUBLIC_NO); // 转为私有客户
                updateCustomer.setSalesUserId(salesUserId);
                updateCustomer.setSalesUserName(salesUserName);

                boolean result = baseMapper.updateById(updateCustomer) > 0;
                if (result) {
                    successCount++;
                    log.debug("成功分配客户 {} ({}) 给业务员 {} (ID: {})",
                        customer.getCompanyName(), customer.getCustomerCode(), salesUserName, salesUserId);
                } else {
                    failCount++;
                    log.warn("分配客户失败: {} ({})",
                        customer.getCompanyName(), customer.getCustomerCode());
                }
            } catch (Exception e) {
                failCount++;
                log.error("分配客户 {} 时发生异常: {}", customerId, e.getMessage(), e);
            }
        }

        log.info("批量分配公海客户完成，成功: {}, 失败: {}, 业务员: {} (ID: {})",
            successCount, failCount, salesUserName, salesUserId);

        return successCount;
    }

    /**
     * 查询所有客户信息分页列表（用于客户查重）
     */
    @Override
    public TableDataInfo<CrmCustomerVo> queryAllCustomerPageList(CrmCustomerBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CrmCustomer> lqw = buildQueryWrapper(bo);

        // 客户查重不需要数据权限过滤，查询所有客户
        Page<CrmCustomerVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        log.info("查询客户查重列表，查询条件: {}, 结果数量: {}", bo, result.getRecords().size());

        return TableDataInfo.build(result);
    }

    /**
     * 获取客户选择列表（用于订单等模块选择客户）
     */
    @Override
    @DataPermission({
        @DataColumn(key = "userName",value = "sales_user_id")
    })
    public List<CustomerOptionVo> getCustomerOptions() {
        LambdaQueryWrapper<CrmCustomer> lqw = Wrappers.lambdaQuery();
        lqw.eq(CrmCustomer::getDelFlag, BusinessConstants.LOGIC_NOT_DELETE);
        lqw.select(CrmCustomer::getCustomerId, CrmCustomer::getCustomerCode,
                  CrmCustomer::getCompanyName, CrmCustomer::getCustomerStatus);
        lqw.orderByDesc(CrmCustomer::getCreateTime);

        List<CrmCustomer> customerList = baseMapper.selectList(lqw);

        return customerList.stream().map(customer -> {
            CustomerOptionVo option = new CustomerOptionVo();
            option.setCustomerId(customer.getCustomerId());
            option.setCustomerCode(customer.getCustomerCode());
            option.setCompanyName(customer.getCompanyName());
            option.setCustomerStatus(customer.getCustomerStatus());
            return option;
        }).collect(Collectors.toList());
    }

    /**
     * 处理影响成交因素数组转换
     * 将前端传来的数组转换为逗号分隔的字符串
     */
    private void processDealInfluenceFactorsArray(CrmCustomerBo bo) {
        if (bo.getDealInfluenceFactorsArray() != null && bo.getDealInfluenceFactorsArray().length > 0) {
            // 如果前端传了数组，将数组转换为逗号分隔的字符串
            bo.setDealInfluenceFactors(String.join(",", bo.getDealInfluenceFactorsArray()));
        }
    }

    /**
     * 构建社媒账号列表
     * 处理前端传来的动态社媒账号列表
     */
    private List<CrmCustomerSocialMediaBo> buildSocialMediaList(CrmCustomerBo bo) {
        if (CollUtil.isEmpty(bo.getSocialMediaList())) {
            return new ArrayList<>();
        }

        List<CrmCustomerSocialMediaBo> socialMediaList = new ArrayList<>();

        for (int i = 0; i < bo.getSocialMediaList().size(); i++) {
            CrmCustomerSocialMediaBo socialMediaBo = bo.getSocialMediaList().get(i);

            // 验证必填字段
            if (StringUtils.isBlank(socialMediaBo.getSocialMediaType()) ||
                StringUtils.isBlank(socialMediaBo.getSocialMediaAccount())) {
                continue;
            }

            // 设置默认值
            if (socialMediaBo.getSortOrder() == null) {
                socialMediaBo.setSortOrder(i + 1);
            }
            if (StringUtils.isBlank(socialMediaBo.getStatus())) {
                socialMediaBo.setStatus(BusinessConstants.NORMAL);
            }
            if (StringUtils.isBlank(socialMediaBo.getIsPrimary())) {
                socialMediaBo.setIsPrimary(i == 0 ? BusinessConstants.YES_NUMBER_STRING : BusinessConstants.NO_NUMBER_STRING); // 第一个默认为主要账号
            }

            socialMediaList.add(socialMediaBo);
        }

        return socialMediaList;
    }

    /**
     * 填充社交媒体数据用于导出
     */
    private void fillSocialMediaForExport(CrmCustomerVo customerVo) {
        if (customerVo.getCustomerId() != null) {
            List<CrmCustomerSocialMediaVo> socialMediaList = socialMediaService.queryByCustomerId(customerVo.getCustomerId());
            customerVo.setSocialMediaList(socialMediaList);

            // 将社交媒体列表转换为导出用的单独字段
            if (socialMediaList != null && !socialMediaList.isEmpty()) {
                for (int i = 0; i < socialMediaList.size() && i < 3; i++) {
                    CrmCustomerSocialMediaVo socialMedia = socialMediaList.get(i);
                    switch (i) {
                        case 0:
                            customerVo.setSocialMediaAccount1(socialMedia.getSocialMediaAccount());
                            customerVo.setSocialMediaType1(socialMedia.getSocialMediaType());
                            break;
                        case 1:
                            customerVo.setSocialMediaAccount2(socialMedia.getSocialMediaAccount());
                            customerVo.setSocialMediaType2(socialMedia.getSocialMediaType());
                            break;
                        case 2:
                            customerVo.setSocialMediaAccount3(socialMedia.getSocialMediaAccount());
                            customerVo.setSocialMediaType3(socialMedia.getSocialMediaType());
                            break;
                    }
                }
            }
        }
    }

    /**
     * 更新客户说明
     */
    @Override
    public Boolean updateCustomerDescription(Long customerId, String customerDescription) {
        if (customerId == null) {
            throw new IllegalArgumentException("客户ID不能为空");
        }

        // 验证客户是否存在
        CrmCustomer customer = baseMapper.selectById(customerId);
        if (customer == null) {
            throw new RuntimeException("客户不存在");
        }

        // 更新客户说明
        CrmCustomer updateCustomer = new CrmCustomer();
        updateCustomer.setCustomerId(customerId);
        updateCustomer.setCustomerDescription(customerDescription);

        int result = baseMapper.updateById(updateCustomer);

        log.info("更新客户说明，客户ID: {}, 客户说明: {}, 更新结果: {}",
                customerId, customerDescription, result > 0 ? "成功" : "失败");

        return result > 0;
    }

    /**
     * 根据客户编号或邮箱获取已存在的客户信息
     */
    @Override
    public CrmCustomerVo getExistingCustomer(String customerCode, String accountEmail) {
        LambdaQueryWrapper<CrmCustomer> lqw = Wrappers.lambdaQuery();

        // 构建查询条件：客户编号或账户邮箱匹配
        lqw.and(wrapper -> {
            if (StringUtils.isNotEmpty(customerCode)) {
                wrapper.eq(CrmCustomer::getCustomerCode, customerCode);
            }
            if (StringUtils.isNotEmpty(accountEmail)) {
                if (StringUtils.isNotEmpty(customerCode)) {
                    wrapper.or();
                }
                wrapper.eq(CrmCustomer::getAccountEmail, accountEmail);
            }
        });

        // 只查询未删除的记录
        lqw.eq(CrmCustomer::getDelFlag, BusinessConstants.LOGIC_NOT_DELETE);
        lqw.isNull(CrmCustomer::getDeletedAt);

        // 只查询第一个匹配的客户
        lqw.last("LIMIT 1");

        CrmCustomerVo existingCustomer = baseMapper.selectVoOne(lqw);

        if (existingCustomer != null) {
            log.info("发现已存在客户，客户编号: {}, 邮箱: {}, 客户ID: {}",
                    customerCode, accountEmail, existingCustomer.getCustomerId());

            // 填充社交媒体数据
            fillSocialMediaForExport(existingCustomer);
        }

        return existingCustomer;
    }

    /**
     * 统计客户的订单数量
     */
    private long countOrdersByCustomerId(Long customerId) {
        // 由于 ICrmOrderService 没有继承 IService，我们需要通过其他方式计数
        // 这里可以通过查询订单列表的方式来计数，或者添加专门的计数方法
        try {
            CrmOrderBo orderBo = new CrmOrderBo();
            orderBo.setCustomerId(customerId);
            List<CrmOrderVo> orders = crmOrderService.queryList(orderBo);
            return orders != null ? orders.size() : 0;
        } catch (Exception e) {
            log.warn("统计客户订单数量失败，客户ID: {}", customerId, e);
            return 0;
        }
    }

    /**
     * 统计客户的跟进记录数量
     */
    private long countFollowRecordsByCustomerId(Long customerId) {
        // 由于 ICrmCustomerFollowRecordService 没有继承 IService，我们需要通过其他方式计数
        // 这里可以通过查询跟进记录列表的方式来计数，或者添加专门的计数方法
        try {
            CrmCustomerFollowRecordBo followBo = new CrmCustomerFollowRecordBo();
            followBo.setCustomerId(customerId);
            List<CrmCustomerFollowRecordVo> followRecords = crmCustomerFollowRecordService.queryList(followBo);
            return followRecords != null ? followRecords.size() : 0;
        } catch (Exception e) {
            log.warn("统计客户跟进记录数量失败，客户ID: {}", customerId, e);
            return 0;
        }
    }

}
