package com.imhuso.crm.core.utils;

import com.imhuso.crm.core.enums.OrderStatus;
import com.imhuso.system.domain.vo.SysDictDataVo;
import com.imhuso.system.service.ISysDictTypeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 订单状态工具类
 * 提供基于字典数据的订单状态相关操作
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderStatusUtils {

    private final ISysDictTypeService dictTypeService;

    /**
     * 字典类型：订单状态
     */
    private static final String DICT_TYPE_ORDER_STATUS = "crm_order_status";

    /**
     * 获取不允许删除的订单状态集合
     * 从字典中获取，如果字典不可用则使用枚举默认值
     *
     * @return 不允许删除的订单状态集合
     */
    public Set<String> getUndeletableStatuses() {
        try {
            // 尝试从字典服务获取所有订单状态
            List<SysDictDataVo> dictDataList = dictTypeService.selectDictDataByType(DICT_TYPE_ORDER_STATUS);

            if (dictDataList != null && !dictDataList.isEmpty()) {
                // 从字典中获取不允许删除的状态
                // 根据业务规则：已确认、处理中、已发货、已送达、已完成状态不允许删除
                return dictDataList.stream()
                    .map(SysDictDataVo::getDictValue)
                    .filter(this::isUndeletableStatus)
                    .collect(Collectors.toSet());
            }
        } catch (Exception e) {
            log.warn("从字典获取订单状态失败，使用默认配置: {}", e.getMessage());
        }

        // 如果字典不可用，使用枚举默认值
        return Arrays.stream(OrderStatus.getUndeletableStatuses())
            .collect(Collectors.toSet());
    }

    /**
     * 检查订单状态是否允许删除
     *
     * @param orderStatus 订单状态
     * @return true-允许删除，false-不允许删除
     */
    public boolean isDeletable(String orderStatus) {
        if (orderStatus == null || orderStatus.trim().isEmpty()) {
            return true; // 空状态允许删除
        }
        
        Set<String> undeletableStatuses = getUndeletableStatuses();
        return !undeletableStatuses.contains(orderStatus);
    }

    /**
     * 获取订单状态的显示标签
     *
     * @param orderStatus 订单状态值
     * @return 状态显示标签
     */
    public String getOrderStatusLabel(String orderStatus) {
        if (orderStatus == null || orderStatus.trim().isEmpty()) {
            return "";
        }

        try {
            // 从字典中获取状态标签
            List<SysDictDataVo> dictDataList = dictTypeService.selectDictDataByType(DICT_TYPE_ORDER_STATUS);
            if (dictDataList != null) {
                return dictDataList.stream()
                    .filter(data -> orderStatus.equals(data.getDictValue()))
                    .map(SysDictDataVo::getDictLabel)
                    .findFirst()
                    .orElse(orderStatus);
            }
        } catch (Exception e) {
            log.warn("获取订单状态标签失败: {}", e.getMessage());
        }

        // 如果字典不可用，使用枚举
        OrderStatus status = OrderStatus.fromValue(orderStatus);
        return status != null ? status.getDescription() : orderStatus;
    }

    /**
     * 判断状态是否为不可删除状态
     * 根据业务规则定义
     *
     * @param statusValue 状态值
     * @return true-不可删除，false-可删除
     */
    private boolean isUndeletableStatus(String statusValue) {
        // 根据业务规则，以下状态不允许删除
        return "confirmed".equals(statusValue) ||
               "processing".equals(statusValue) ||
               "shipped".equals(statusValue) ||
               "delivered".equals(statusValue) ||
               "completed".equals(statusValue);
    }
}
