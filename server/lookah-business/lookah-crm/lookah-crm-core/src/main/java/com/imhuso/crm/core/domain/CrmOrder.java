package com.imhuso.crm.core.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * CRM客户订单(CrmOrder)实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("crm_order")
public class CrmOrder extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @TableId(value = "order_id")
    private Long orderId;

    /**
     * 订单号（自动生成）
     */
    private String orderNo;

    /**
     * 下单日期
     */
    private Date orderDate;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户编号
     */
    private String customerCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 订单类型（online:线上订单, offline:线下订单）
     */
    private String orderType;

    /**
     * 订单总金额
     */
    private BigDecimal orderTotalAmount;

    /**
     * 运费
     */
    private BigDecimal shippingFee;

    /**
     * 付款方式（bank_transfer:银行转账, ach:ACH, online_credit_card:线上信用卡, offline_credit_card:线下信用卡, zelle:Zelle）
     */
    private String paymentMethod;

    /**
     * 关联订单号
     */
    private String relatedOrderNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除时间戳（毫秒，软删除时记录，NULL表示未删除）
     */
    private Long deletedAt;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

}
