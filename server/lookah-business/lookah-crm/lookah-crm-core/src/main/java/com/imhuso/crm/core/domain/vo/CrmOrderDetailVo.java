package com.imhuso.crm.core.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import com.imhuso.crm.core.domain.CrmOrderDetail;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * CRM客户订单详情视图对象 CrmOrderDetailVo
 *
 * <AUTHOR> Assistant
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CrmOrderDetail.class)
public class CrmOrderDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单详情ID
     */
    @ExcelProperty(value = "订单详情ID")
    private Long detailId;

    /**
     * 订单ID
     */
    @ExcelProperty(value = "订单ID")
    private Long orderId;

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    private String orderNo;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 颜色
     */
    @ExcelProperty(value = "颜色")
    private String productColor;

    /**
     * 单价
     */
    @ExcelProperty(value = "单价")
    private BigDecimal unitPrice;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private Integer quantity;

    /**
     * 单款产品总金额
     */
    @ExcelProperty(value = "单款产品总金额")
    private BigDecimal productTotalAmount;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 订单详情状态（normal=正常, refund=退款, return=退货, defective=到货不良, shortage=到货少货, wrong=发错货, resend=重发）
     */
    @ExcelProperty(value = "订单详情状态")
    private String detailStatus;

    /**
     * 是否赠品（1=是, 0=否）
     */
    @ExcelProperty(value = "是否赠品")
    private String isGift;

    /**
     * 退款原因
     */
    @ExcelProperty(value = "退款原因")
    private String refundReason;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建者
     */
    @ExcelProperty(value = "创建者")
    private String createBy;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 更新者
     */
    @ExcelProperty(value = "更新者")
    private String updateBy;

    /**
     * 客户公司名称（关联字段）
     */
    @ExcelProperty(value = "客户公司")
    private String companyName;

    /**
     * 创建人名称（销售员姓名）
     */
    @ExcelProperty(value = "销售员")
    private String createByName;

    /**
     * 下单日期
     */
    @ExcelProperty(value = "下单日期")
    private Date orderDate;

}
