package com.imhuso.crm.core.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * CRM客户订单详情(CrmOrderDetail)实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("crm_order_detail")
public class CrmOrderDetail extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单详情ID
     */
    @TableId(value = "detail_id")
    private Long detailId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 颜色
     */
    private String productColor;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 单款产品总金额
     */
    private BigDecimal productTotalAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 订单详情状态（normal=正常, refund=退款, return=退货, defective=到货不良, shortage=到货少货, wrong=发错货, resend=重发）
     */
    private String detailStatus;

    /**
     * 是否赠品（1=是, 0=否）
     */
    private String isGift;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 删除时间戳（毫秒，软删除时记录，NULL表示未删除）
     */
    private Long deletedAt;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

}
