package com.imhuso.crm.core.domain.bo;

import com.imhuso.common.mybatis.core.domain.BaseEntity;
import com.imhuso.crm.core.domain.CrmCustomerSocialMedia;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * CRM客户社媒账号业务对象 crm_customer_social_media
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CrmCustomerSocialMedia.class, reverseConvertGenerate = false)
public class CrmCustomerSocialMediaBo extends BaseEntity {

    /**
     * 社媒账号ID
     */
    private Long socialMediaId;

    /**
     * 客户ID
     */
    @NotNull(message = "客户ID不能为空")
    private Long customerId;

    /**
     * 社媒类型
     */
    @NotBlank(message = "社媒类型不能为空")
    @Size(max = 50, message = "社媒类型长度不能超过50个字符")
    private String socialMediaType;

    /**
     * 社媒账号
     */
    @NotBlank(message = "社媒账号不能为空")
    @Size(max = 200, message = "社媒账号长度不能超过200个字符")
    private String socialMediaAccount;

    /**
     * 是否主要账号（1=是, 0=否）
     */
    @Pattern(regexp = "^[01]$", message = "是否主要账号值必须为0或1")
    private String isPrimary;

    /**
     * 排序顺序
     */
    @Min(value = 1, message = "排序顺序不能小于1")
    @Max(value = 999, message = "排序顺序不能大于999")
    private Integer sortOrder;

    /**
     * 状态（1=正常, 0=停用）
     */
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    private String status;

}
