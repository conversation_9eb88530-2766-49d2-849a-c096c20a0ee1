package com.imhuso.crm.core.domain.bo;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import com.imhuso.crm.core.domain.CrmCustomer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.*;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.Arrays;
import java.util.Collections;

// CRM相关BO导入
import com.imhuso.crm.core.domain.bo.CrmCustomerSocialMediaBo;

/**
 * CRM客户信息业务对象 CrmCustomerBo
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CrmCustomer.class, reverseConvertGenerate = false)
public class CrmCustomerBo extends BaseEntity {

    /**
     * 客户ID
     */
    @NotNull(message = "客户ID不能为空", groups = { EditGroup.class })
    private Long customerId;

    /**
     * 客户编号
     */
//    @NotBlank(message = "客户编号不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 50, message = "客户编号长度不能超过50个字符")
    private String customerCode;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 200, message = "公司名称长度不能超过200个字符")
    private String companyName;

    /**
     * 客户类型
     */
    @NotBlank(message = "客户类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String customerType;

    /**
     * 经营地点数量
     */
    @Min(value = 1, message = "经营地点数量不能小于1")
    private Integer businessLocationCount;

    /**
     * 所在国家
     */
    @NotBlank(message = "所在国家不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 100, message = "所在国家长度不能超过100个字符")
    private String country;

    /**
     * 所在州
     */
    @Size(max = 100, message = "所在州长度不能超过100个字符")
    private String state;

    /**
     * 邮编
     */
    @Size(max = 20, message = "邮编长度不能超过20个字符")
    private String zipCode;

    /**
     * 账号邮箱
     */
    @NotBlank(message = "账号邮箱不能为空", groups = { AddGroup.class, EditGroup.class })
    @Email(message = "账号邮箱格式不正确")
    @Size(max = 100, message = "账号邮箱长度不能超过100个字符")
    private String accountEmail;

    /**
     * 联系邮箱
     */
    @Email(message = "联系邮箱格式不正确")
    @Size(max = 100, message = "联系邮箱长度不能超过100个字符")
    private String contactEmail;

    /**
     * 客户电话
     */
    @Size(max = 20, message = "客户电话长度不能超过20个字符")
    private String customerPhone;

    /**
     * WhatsApp号码
     */
    @Size(max = 20, message = "WhatsApp号码长度不能超过20个字符")
    private String whatsappNumber;

    /**
     * 联系人
     */
    @NotBlank(message = "联系人不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 100, message = "联系人长度不能超过100个字符")
    private String contactPerson;

    /**
     * 职位
     */
    @NotBlank(message = "职位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String position;

    /**
     * 客户画像
     */
    @Size(max = 1000, message = "客户画像长度不能超过1000个字符")
    private String customerProfile;

    /**
     * 感兴趣的产品
     */
    @Size(max = 500, message = "感兴趣的产品长度不能超过500个字符")
    private String interestedProducts;

    /**
     * 客户来源
     */
    @Size(max = 100, message = "客户来源长度不能超过100个字符")
    private String customerSource;

    /**
     * 客户状态
     */
    private String customerStatus;

    /**
     * 状态描述
     */
    private String statusDescription;

    /**
     * 星级评定
     */
    @Min(value = 1, message = "星级评定不能小于1")
    @Max(value = 5, message = "星级评定不能大于5")
    private Integer starRating;

    /**
     * 累计下单次数
     */
    @Min(value = 0, message = "累计下单次数不能小于0")
    private Integer totalOrderCount;

    /**
     * 累计下单金额
     */
    @DecimalMin(value = "0.00", message = "累计下单金额不能小于0")
    private BigDecimal totalOrderAmount;

    /**
     * 第一次下单日期
     */
    private Date firstOrderDate;

    /**
     * 最后一次下单日期
     */
    private Date lastOrderDate;

    /**
     * 最后跟进时间
     */
    private Date lastFollowTime;

    /**
     * 负责销售代表ID
     */
    private Long salesUserId;

    /**
     * 负责销售代表姓名
     */
    @Size(max = 100, message = "负责销售代表姓名长度不能超过100个字符")
    private String salesUserName;

    /**
     * 是否公海客户
     */
    private String isPublic;

    /**
     * 是否为新客户（1=是, 0=否）
     */
    @Pattern(regexp = "^[01]$", message = "是否为新客户值必须为0或1")
    private String isNewCustomer;

    /**
     * 客户说明
     */
    @Size(max = 1000, message = "客户说明长度不能超过1000个字符")
    private String customerDescription;

    /**
     * 系统星级评分（1-5星）
     */
    @Min(value = 1, message = "系统星级评分不能小于1")
    @Max(value = 5, message = "系统星级评分不能大于5")
    private Integer systemStarRating;

    /**
     * 单次采购金额
     */
    @DecimalMin(value = "0.00", message = "单次采购金额不能小于0")
    private BigDecimal singlePurchaseAmount;

    /**
     * 采购频率
     */
    @Size(max = 50, message = "采购频率长度不能超过50个字符")
    private String purchaseFrequency;

    /**
     * 是否好沟通
     */
    private String isEasyCommunicate;

    /**
     * 联系方式偏好
     */
    @Size(max = 100, message = "联系方式偏好长度不能超过100个字符")
    private String contactPreference;

    /**
     * 决策权大小
     */
    @Size(max = 50, message = "决策权大小长度不能超过50个字符")
    private String decisionAuthority;

    /**
     * 对价格敏感的程度
     */
    @Size(max = 50, message = "对价格敏感的程度长度不能超过50个字符")
    private String priceSensitivity;

    /**
     * 成交的关键点
     */
    @Size(max = 1000, message = "成交的关键点长度不能超过1000个字符")
    private String dealKeyPoints;

    /**
     * 已销售的竞品品牌
     */
    @Size(max = 500, message = "已销售的竞品品牌长度不能超过500个字符")
    private String competitorBrands;

    /**
     * 联系频次
     */
    @Size(max = 50, message = "联系频次长度不能超过50个字符")
    private String contactFrequency;

    /**
     * 下单频率
     */
    @Size(max = 50, message = "下单频率长度不能超过50个字符")
    private String orderFrequency;

    /**
     * 影响成交的主要因素
     */
    @Size(max = 500, message = "影响成交的主要因素长度不能超过500个字符")
    private String dealInfluenceFactors;

    /**
     * 影响成交的主要因素（多选数组，用于前端）
     */
    private String[] dealInfluenceFactorsArray;

    /**
     * 获取影响成交因素数组
     */
    public String[] getDealInfluenceFactorsArray() {
        if (dealInfluenceFactors != null && !dealInfluenceFactors.isEmpty()) {
            return dealInfluenceFactors.split(",");
        }
        return new String[0];
    }

    /**
     * 设置影响成交因素数组
     */
    public void setDealInfluenceFactorsArray(String[] dealInfluenceFactorsArray) {
        this.dealInfluenceFactorsArray = dealInfluenceFactorsArray;
        if (dealInfluenceFactorsArray != null && dealInfluenceFactorsArray.length > 0) {
            this.dealInfluenceFactors = String.join(",", dealInfluenceFactorsArray);
        } else {
            this.dealInfluenceFactors = "";
        }
    }

    /**
     * 影响成交的其他因素
     */
    @Size(max = 200, message = "影响成交的其他因素长度不能超过200个字符")
    private String dealInfluenceOther;

    /**
     * 社媒账号
     */
    @Size(max = 200, message = "社媒账号长度不能超过200个字符")
    private String socialMediaAccount;

    /**
     * 社媒类型
     */
    @Size(max = 50, message = "社媒类型长度不能超过50个字符")
    private String socialMediaType;

    /**
     * 客户图片（逗号分隔存储多张图片URL）
     */
    @Size(max = 2000, message = "客户图片数据长度不能超过2000个字符")
    private String customerImages;

    /**
     * 自定义JSON setter，处理前端传来的数组格式
     * 前端可能传递字符串或数组，统一转换为逗号分隔的字符串存储
     */
    @JsonSetter("customerImages")
    public void setCustomerImagesFromJson(JsonNode jsonNode) {
        if (jsonNode == null || jsonNode.isNull()) {
            this.customerImages = null;
            return;
        }

        if (jsonNode.isArray()) {
            // 如果是数组，转换为逗号分隔的字符串
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < jsonNode.size(); i++) {
                if (i > 0) {
                    sb.append(",");
                }
                sb.append(jsonNode.get(i).asText());
            }
            this.customerImages = sb.toString();
        } else if (jsonNode.isTextual()) {
            // 如果是字符串，直接使用
            this.customerImages = jsonNode.asText();
        } else {
            // 其他类型，设置为null
            this.customerImages = null;
        }
    }

    /**
     * 获取客户图片数组格式（用于前端组件）
     * 将逗号分隔的字符串转换为字符串数组
     */
    @JsonGetter("customerImages")
    public List<String> getCustomerImagesArray() {
        if (customerImages == null || customerImages.trim().isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.asList(customerImages.split(","));
    }

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 删除时间戳（毫秒，软删除时记录，NULL表示未删除）
     */
    private Long deletedAt;

    /**
     * 客户社媒账号列表
     */
    @Valid
    private List<CrmCustomerSocialMediaBo> socialMediaList;


}
