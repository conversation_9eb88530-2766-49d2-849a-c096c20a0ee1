package com.imhuso.crm.core.domain.bo;

import com.imhuso.common.core.validate.AddGroup;
import com.imhuso.common.core.validate.EditGroup;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import com.imhuso.crm.core.domain.CrmOrder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * CRM客户订单业务对象 CrmOrderBo
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CrmOrder.class, reverseConvertGenerate = false)
public class CrmOrderBo extends BaseEntity {

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空", groups = { EditGroup.class })
    private Long orderId;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 50, message = "订单号长度不能超过50个字符")
    private String orderNo;

    /**
     * 下单日期
     */
    @NotNull(message = "下单日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date orderDate;

    /**
     * 客户ID
     */
    @NotNull(message = "客户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long customerId;

    /**
     * 客户编号
     */
    @NotBlank(message = "客户编号不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 50, message = "客户编号长度不能超过50个字符")
    private String customerCode;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 200, message = "公司名称长度不能超过200个字符")
    private String companyName;

    /**
     * 订单状态
     */
    @NotBlank(message = "订单状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderStatus;

    /**
     * 订单类型
     */
    @NotBlank(message = "订单类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderType;

    /**
     * 订单总金额
     */
    @DecimalMin(value = "0.00", message = "订单总金额不能小于0")
    private BigDecimal orderTotalAmount;

    /**
     * 运费
     */
    @DecimalMin(value = "0.00", message = "运费不能小于0")
    private BigDecimal shippingFee;

    /**
     * 付款方式
     */
    @NotBlank(message = "付款方式不能为空", groups = { AddGroup.class, EditGroup.class })
    private String paymentMethod;

    /**
     * 关联订单号
     */
    @Size(max = 50, message = "关联订单号长度不能超过50个字符")
    private String relatedOrderNo;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 订单详情列表
     */
    private List<CrmOrderDetailBo> orderDetails;

    // ========== 查询条件字段 ==========

    /**
     * 订单号（模糊查询）
     */
    private String orderNoLike;

    /**
     * 公司名称（模糊查询）
     */
    private String companyNameLike;

    /**
     * 下单开始日期
     */
    private Date orderDateStart;

    /**
     * 下单结束日期
     */
    private Date orderDateEnd;

    /**
     * 创建开始时间
     */
    private Date createTimeStart;

    /**
     * 创建结束时间
     */
    private Date createTimeEnd;

}
