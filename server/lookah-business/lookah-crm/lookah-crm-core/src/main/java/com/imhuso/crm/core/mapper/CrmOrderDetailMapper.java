package com.imhuso.crm.core.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.mybatis.core.mapper.BaseMapperPlus;
import com.imhuso.crm.core.domain.CrmOrderDetail;
import com.imhuso.crm.core.domain.vo.CrmOrderDetailVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * CRM客户订单详情Mapper接口
 *
 * <AUTHOR> Assistant
 */
@Mapper
public interface CrmOrderDetailMapper extends BaseMapperPlus<CrmOrderDetail, CrmOrderDetailVo> {

    /**
     * 分页查询订单详情列表（关联订单和客户信息）
     */
    Page<CrmOrderDetailVo> selectVoPageWithOrderInfo(IPage<CrmOrderDetail> page, @Param(Constants.WRAPPER) Wrapper<CrmOrderDetail> queryWrapper);

    /**
     * 查询订单详情列表（关联订单和客户信息）
     */
    List<CrmOrderDetailVo> selectVoListWithOrderInfo(@Param(Constants.WRAPPER) Wrapper<CrmOrderDetail> queryWrapper);

    /**
     * 根据ID查询订单详情（关联订单和客户信息）
     */
    CrmOrderDetailVo selectVoByIdWithOrderInfo(@Param("detailId") Long detailId);

}
