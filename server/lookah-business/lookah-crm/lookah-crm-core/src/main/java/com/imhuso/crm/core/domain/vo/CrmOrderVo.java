package com.imhuso.crm.core.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.imhuso.common.excel.annotation.ExcelDictFormat;
import com.imhuso.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import com.imhuso.crm.core.domain.CrmOrder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * CRM客户订单视图对象 CrmOrderVo
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CrmOrder.class)
public class CrmOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @ExcelProperty(value = "订单ID")
    private Long orderId;

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    private String orderNo;

    /**
     * 下单日期
     */
    @ExcelProperty(value = "下单日期")
    private Date orderDate;

    /**
     * 客户ID
     */
    @ExcelProperty(value = "客户ID")
    private Long customerId;

    /**
     * 客户编号
     */
    @ExcelProperty(value = "客户编号")
    private String customerCode;

    /**
     * 公司名称
     */
    @ExcelProperty(value = "公司名称")
    private String companyName;

    /**
     * 订单状态
     */
    @ExcelProperty(value = "订单状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_order_status")
    private String orderStatus;

    /**
     * 订单类型
     */
    @ExcelProperty(value = "订单类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_order_type")
    private String orderType;

    /**
     * 订单总金额
     */
    @ExcelProperty(value = "订单总金额")
    private BigDecimal orderTotalAmount;

    /**
     * 运费
     */
    @ExcelProperty(value = "运费")
    private BigDecimal shippingFee;

    /**
     * 付款方式
     */
    @ExcelProperty(value = "付款方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_payment_method")
    private String paymentMethod;

    /**
     * 关联订单号
     */
    @ExcelProperty(value = "关联订单号")
    private String relatedOrderNo;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建者
     */
    @ExcelProperty(value = "创建者")
    private String createBy;

    /**
     * 创建者名称
     */
    @ExcelProperty(value = "创建者名称")
    private String createByName;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 更新者
     */
    @ExcelProperty(value = "更新者")
    private String updateBy;

    /**
     * 更新者名称
     */
    @ExcelProperty(value = "更新者名称")
    private String updateByName;

    /**
     * 订单详情列表
     */
    private List<CrmOrderDetailVo> orderDetails;

}
