package com.imhuso.crm.core.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * CRM客户跟进记录统计VO
 *
 * <AUTHOR>
 */
@Data
public class CrmCustomerFollowStatisticsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 跟进记录总数
     */
    private Integer totalCount;

    /**
     * 首次跟进日期
     */
    private Date firstFollowDate;

    /**
     * 最后跟进日期
     */
    private Date lastFollowDate;

    /**
     * 时间跨度（天数）
     */
    private Integer dateSpanDays;

}
