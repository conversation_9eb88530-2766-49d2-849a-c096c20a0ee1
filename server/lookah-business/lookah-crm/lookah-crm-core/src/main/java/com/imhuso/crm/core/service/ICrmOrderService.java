package com.imhuso.crm.core.service;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.crm.core.domain.bo.CrmOrderBo;
import com.imhuso.crm.core.domain.vo.CrmOrderVo;

import java.util.Collection;
import java.util.List;

/**
 * CRM客户订单Service接口
 *
 * <AUTHOR>
 */
public interface ICrmOrderService {

    /**
     * 查询CRM客户订单
     *
     * @param orderId 订单ID
     * @return CRM客户订单
     */
    CrmOrderVo queryById(Long orderId);

    /**
     * 查询CRM客户订单列表
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return CRM客户订单集合
     */
    TableDataInfo<CrmOrderVo> queryPageList(CrmOrderBo bo, PageQuery pageQuery);

    /**
     * 查询我的客户订单列表（只查询当前用户负责客户的订单）
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return CRM客户订单集合
     */
    TableDataInfo<CrmOrderVo> queryMyPageList(CrmOrderBo bo, PageQuery pageQuery);

    /**
     * 查询CRM客户订单列表
     *
     * @param bo 查询条件
     * @return CRM客户订单集合
     */
    List<CrmOrderVo> queryList(CrmOrderBo bo);

    /**
     * 新增CRM客户订单
     *
     * @param bo CRM客户订单
     * @return 结果
     */
    Boolean insertByBo(CrmOrderBo bo);

    /**
     * 修改CRM客户订单
     *
     * @param bo CRM客户订单
     * @return 结果
     */
    Boolean updateByBo(CrmOrderBo bo);

    /**
     * 校验并批量删除CRM客户订单信息
     *
     * @param ids 需要删除的CRM客户订单主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 更新订单状态
     *
     * @param orderId 订单ID
     * @param orderStatus 新状态
     * @param remark 状态变更备注
     * @return 结果
     */
    Boolean updateOrderStatus(Long orderId, String orderStatus, String remark);

    /**
     * 生成订单号
     *
     * @return 订单号
     */
    String generateOrderNo();

    /**
     * 计算订单总金额
     *
     * @param bo 订单业务对象
     * @return 订单总金额
     */
    void calculateOrderAmount(CrmOrderBo bo);

}
