package com.imhuso.crm.core.mapper;

import com.imhuso.common.mybatis.core.mapper.BaseMapperPlus;
import com.imhuso.crm.core.domain.CrmCustomer;
import com.imhuso.crm.core.domain.vo.CrmCustomerVo;
import com.imhuso.crm.core.domain.vo.CrmCustomerOrderStatisticsVo;
import com.imhuso.crm.core.domain.vo.CrmCustomerInterestedProductVo;
import com.imhuso.crm.core.domain.vo.CrmCustomerFollowStatisticsVo;
import com.imhuso.crm.core.domain.vo.CrmCustomerOrderSummaryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * CRM客户信息Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface CrmCustomerMapper extends BaseMapperPlus<CrmCustomer, CrmCustomerVo> {

    /**
     * 查询客户订单统计信息
     *
     * @param customerId 客户ID
     * @return 订单统计信息
     */
    CrmCustomerOrderStatisticsVo selectCustomerOrderStatistics(@Param("customerId") Long customerId);

    /**
     * 查询客户感兴趣的产品名称（按订单数量排序）
     *
     * @param customerId 客户ID
     * @return 产品名称和数量统计
     */
    List<CrmCustomerInterestedProductVo> selectInterestedProductsByCustomerId(@Param("customerId") Long customerId);

    /**
     * 查询客户跟进记录统计信息（用于计算联系频次）
     *
     * @param customerId 客户ID
     * @return 跟进记录统计信息
     */
    CrmCustomerFollowStatisticsVo selectFollowStatisticsByCustomerId(@Param("customerId") Long customerId);

    /**
     * 查询客户订单金额和数量统计（用于计算星级）
     *
     * @param customerId 客户ID
     * @return 订单统计信息
     */
    CrmCustomerOrderSummaryVo selectOrderSummaryByCustomerId(@Param("customerId") Long customerId);

}
