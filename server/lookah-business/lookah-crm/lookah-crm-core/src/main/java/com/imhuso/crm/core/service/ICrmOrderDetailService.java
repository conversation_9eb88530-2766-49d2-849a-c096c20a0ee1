package com.imhuso.crm.core.service;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.crm.core.domain.bo.CrmOrderDetailBo;
import com.imhuso.crm.core.domain.vo.CrmOrderDetailVo;

import java.util.Collection;
import java.util.List;

/**
 * CRM客户订单详情Service接口
 *
 * <AUTHOR> Assistant
 */
public interface ICrmOrderDetailService {

    /**
     * 查询CRM客户订单详情
     *
     * @param detailId 订单详情ID
     * @return CRM客户订单详情
     */
    CrmOrderDetailVo queryById(Long detailId);

    /**
     * 根据订单ID查询订单详情列表
     *
     * @param orderId 订单ID
     * @return 订单详情列表
     */
    List<CrmOrderDetailVo> queryByOrderId(Long orderId);

    /**
     * 查询CRM客户订单详情列表
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return CRM客户订单详情集合
     */
    TableDataInfo<CrmOrderDetailVo> queryPageList(CrmOrderDetailBo bo, PageQuery pageQuery);

    /**
     * 查询CRM客户订单详情列表
     *
     * @param bo 查询条件
     * @return CRM客户订单详情集合
     */
    List<CrmOrderDetailVo> queryList(CrmOrderDetailBo bo);

    /**
     * 新增CRM客户订单详情
     *
     * @param bo CRM客户订单详情
     * @return 结果
     */
    Boolean insertByBo(CrmOrderDetailBo bo);

    /**
     * 修改CRM客户订单详情
     *
     * @param bo CRM客户订单详情
     * @return 结果
     */
    Boolean updateByBo(CrmOrderDetailBo bo);

    /**
     * 校验并批量删除CRM客户订单详情信息
     *
     * @param ids 需要删除的CRM客户订单详情主键集合
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids);

    /**
     * 批量新增订单详情
     *
     * @param orderDetailList 订单详情列表
     * @return 结果
     */
    Boolean insertBatch(List<CrmOrderDetailBo> orderDetailList);

    /**
     * 根据订单ID删除订单详情
     *
     * @param orderId 订单ID
     * @return 结果
     */
    Boolean deleteByOrderId(Long orderId);

}
