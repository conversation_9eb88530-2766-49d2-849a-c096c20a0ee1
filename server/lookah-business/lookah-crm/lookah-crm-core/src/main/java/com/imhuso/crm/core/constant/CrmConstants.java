package com.imhuso.crm.core.constant;

/**
 * CRM模块常量信息
 *
 * <AUTHOR>
 */
public class CrmConstants {

    /**
     * 客户编号前缀
     */
    public static final String CUSTOMER_CODE_PREFIX = "LCS";

    /**
     * 是否公海客户
     */
    public static final String IS_PUBLIC_YES = "1";
    public static final String IS_PUBLIC_NO = "0";

    /**
     * 客户状态
     */
    public static final String CUSTOMER_STATUS_POTENTIAL = "potential";
    public static final String CUSTOMER_STATUS_CONTACTED = "contacted";
    public static final String CUSTOMER_STATUS_INTERESTED = "interested";
    public static final String CUSTOMER_STATUS_NEGOTIATING = "negotiating";
    public static final String CUSTOMER_STATUS_CLOSED_WON = "closed_won";
    public static final String CUSTOMER_STATUS_CLOSED_LOST = "closed_lost";
    public static final String CUSTOMER_STATUS_ACTIVE = "active";
    public static final String CUSTOMER_STATUS_INACTIVE = "inactive";
    public static final String CUSTOMER_STATUS_CLOSED = "closed";
    public static final String CUSTOMER_STATUS_SEMI_ACTIVE = "semi_active";

    /**
     * 订单状态
     */
    public static final String ORDER_STATUS_PENDING = "pending";
    public static final String ORDER_STATUS_CONFIRMED = "confirmed";
    public static final String ORDER_STATUS_PROCESSING = "processing";
    public static final String ORDER_STATUS_SHIPPED = "shipped";
    public static final String ORDER_STATUS_DELIVERED = "delivered";
    public static final String ORDER_STATUS_COMPLETED = "completed";
    public static final String ORDER_STATUS_CANCELLED = "cancelled";
    public static final String ORDER_STATUS_REFUNDED = "refunded";

    /**
     * 客户类型
     */
    public static final String CUSTOMER_TYPE_DISTRIBUTOR = "Distributor";
    public static final String CUSTOMER_TYPE_CASH_AND_CARRY = "Cash&Carry";
    public static final String CUSTOMER_TYPE_SMOKE_SHOP = "SmokeShop";
    public static final String CUSTOMER_TYPE_DISPENSARY = "Dispensary";
    public static final String CUSTOMER_TYPE_GAS_STATION = "GasStation";
    public static final String CUSTOMER_TYPE_VAPE_SHOP = "VapeShop";
    public static final String CUSTOMER_TYPE_CHAIN_SMOKE_SHOPS = "ChainSmokeShops";
    public static final String CUSTOMER_TYPE_CHAIN_DISPENSARIES = "ChainDispensaries";
    public static final String CUSTOMER_TYPE_E_COMMERCE = "E-commerce";

    /**
     * 职位类型
     */
    public static final String POSITION_BOSS = "Boss";
    public static final String POSITION_PURCHASE_MANAGER = "PurchaseManager";
    public static final String POSITION_STORE_MANAGER = "StoreManager";
    public static final String POSITION_CO_PARTNER = "Co-partner";
    public static final String POSITION_SALES_MANAGER = "SalesManager";
    public static final String POSITION_OTHERS = "Others";

    /**
     * 星级评定
     */
    public static final Integer STAR_RATING_MIN = 1;
    public static final Integer STAR_RATING_MAX = 5;
    public static final Integer STAR_RATING_DEFAULT = 1;

    /**
     * 客户来源
     */
    public static final String CUSTOMER_SOURCE_TPE = "TPE";
    public static final String CUSTOMER_SOURCE_CHAMPS = "Champs";
    public static final String CUSTOMER_SOURCE_MJBIZ = "MJBIZ";
    public static final String CUSTOMER_SOURCE_OTHER_EXHIBITION = "Other Exhibition";
    public static final String CUSTOMER_SOURCE_CUSTOMER_REFERRAL = "Customer Referral";
    public static final String CUSTOMER_SOURCE_EMAIL_DEVELOPMENT = "Email Development";
    public static final String CUSTOMER_SOURCE_GOOGLE_DEVELOPMENT = "Google Development";
    public static final String CUSTOMER_SOURCE_SOCIAL_MEDIA = "Social Media";
    public static final String CUSTOMER_SOURCE_ACQUISITION_PLATFORM = "Acquisition Platform";

    /**
     * 公海客户自动回收天数
     */
    public static final Integer PUBLIC_CUSTOMER_RECYCLE_DAYS = 15;

    /**
     * 影响成交的主要因素
     */
    public static final String DEAL_INFLUENCE_FACTOR_PRICE = "price";
    public static final String DEAL_INFLUENCE_FACTOR_LOCAL_PURCHASE = "local_purchase";
    public static final String DEAL_INFLUENCE_FACTOR_PRODUCT_QUALITY = "product_quality";
    public static final String DEAL_INFLUENCE_FACTOR_AFTER_SALES_SERVICE = "after_sales_service";
    public static final String DEAL_INFLUENCE_FACTOR_SHIPPING_TIME = "shipping_time";
    public static final String DEAL_INFLUENCE_FACTOR_PRODUCT_POPULARITY = "product_popularity";
    public static final String DEAL_INFLUENCE_FACTOR_INVENTORY_PRESSURE = "inventory_pressure";
    public static final String DEAL_INFLUENCE_FACTOR_FINANCIAL_PRESSURE = "financial_pressure";
    public static final String DEAL_INFLUENCE_FACTOR_OTHER = "other";

    /**
     * 跟进方式
     */
    public static final String FOLLOW_TYPE_PHONE = "phone";
    public static final String FOLLOW_TYPE_EMAIL = "email";
    public static final String FOLLOW_TYPE_MEETING = "meeting";
    public static final String FOLLOW_TYPE_WECHAT = "wechat";
    public static final String FOLLOW_TYPE_WHATSAPP = "whatsapp";
    public static final String FOLLOW_TYPE_TELEGRAM = "telegram";
    public static final String FOLLOW_TYPE_VIDEO_CALL = "video_call";
    public static final String FOLLOW_TYPE_OTHER = "other";

    /**
     * 社媒类型
     */
    public static final String SOCIAL_MEDIA_TYPE_FACEBOOK = "facebook";
    public static final String SOCIAL_MEDIA_TYPE_INSTAGRAM = "instagram";
    public static final String SOCIAL_MEDIA_TYPE_LINKEDIN = "linkedin";
    public static final String SOCIAL_MEDIA_TYPE_TIKTOK = "tiktok";
    public static final String SOCIAL_MEDIA_TYPE_YOUTUBE = "youtube";
    public static final String SOCIAL_MEDIA_TYPE_OTHER = "other";

}
