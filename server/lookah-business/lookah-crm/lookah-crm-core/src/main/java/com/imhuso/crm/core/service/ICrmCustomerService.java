package com.imhuso.crm.core.service;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.crm.core.domain.bo.CrmCustomerBo;
import com.imhuso.crm.core.domain.vo.CrmCustomerVo;
import com.imhuso.crm.core.domain.vo.CustomerOptionVo;

import java.util.Collection;
import java.util.List;

/**
 * CRM客户信息Service接口
 *
 * <AUTHOR>
 */
public interface ICrmCustomerService {

    /**
     * 查询CRM客户信息
     *
     * @param customerId 客户ID
     * @return CRM客户信息
     */
    CrmCustomerVo queryById(Long customerId);

    /**
     * 查询CRM客户信息列表
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return CRM客户信息集合
     */
    TableDataInfo<CrmCustomerVo> queryPageList(CrmCustomerBo bo, PageQuery pageQuery);

    /**
     * 查询CRM客户信息列表
     *
     * @param bo 查询条件
     * @return CRM客户信息集合
     */
    List<CrmCustomerVo> queryList(CrmCustomerBo bo);

    /**
     * 新增CRM客户信息
     *
     * @param bo CRM客户信息
     * @return 结果
     */
    Boolean insertByBo(CrmCustomerBo bo);

    /**
     * 修改CRM客户信息
     *
     * @param bo CRM客户信息
     * @return 结果
     */
    Boolean updateByBo(CrmCustomerBo bo);

    /**
     * 校验并批量删除CRM客户信息
     *
     * @param ids 需要删除的CRM客户信息主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 生成客户编号
     *
     * @return 客户编号
     */
    String generateCustomerCode();

    /**
     * 检查客户编号是否唯一
     *
     * @param customerCode 客户编号
     * @param customerId 客户ID（编辑时传入，新增时为null）
     * @return 是否唯一
     */
    Boolean checkCustomerCodeUnique(String customerCode, Long customerId);

    /**
     * 检查账号邮箱是否唯一
     *
     * @param accountEmail 账号邮箱
     * @param customerId 客户ID（编辑时传入，新增时为null）
     * @return 是否唯一
     */
    Boolean checkAccountEmailUnique(String accountEmail, Long customerId);

    /**
     * 更新客户统计信息
     *
     * @param customerId 客户ID
     * @param orderCount 订单数量
     * @param orderAmount 订单金额
     */
    void updateCustomerStatistics(Long customerId, Integer orderCount, java.math.BigDecimal orderAmount);

    /**
     * 更新客户订单统计信息（包含所有订单相关字段）
     *
     * @param customerId 客户ID
     */
    void updateCustomerOrderStatistics(Long customerId);

    /**
     * 更新客户星级评定
     *
     * @param customerId 客户ID
     * @param starRating 星级评定
     */
    void updateCustomerStarRating(Long customerId, Integer starRating);

    /**
     * 更新最后跟进时间
     *
     * @param customerId 客户ID
     */
    void updateLastFollowTime(Long customerId);

    /**
     * 统计并更新客户感兴趣的产品名称
     *
     * @param customerId 客户ID
     */
    void updateInterestedProducts(Long customerId);

    /**
     * 统计并更新客户联系频次
     *
     * @param customerId 客户ID
     */
    void updateContactFrequency(Long customerId);

    /**
     * 根据订单总金额和数量计算并更新客户星级
     *
     * @param customerId 客户ID
     */
    void updateCustomerStarRating(Long customerId);

    /**
     * 将客户更新为老客户
     *
     * @param customerId 客户ID
     */
    void updateCustomerToOldCustomer(Long customerId);

    /**
     * 更新客户状态
     *
     * @param customerId 客户ID
     * @param customerStatus 客户状态
     */
    void updateCustomerStatus(Long customerId, String customerStatus);

    /**
     * 查询我的客户列表（根据当前登录用户）
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return 客户列表
     */
    TableDataInfo<CrmCustomerVo> queryMyCustomerPageList(CrmCustomerBo bo, PageQuery pageQuery);

    /**
     * 根据客户编号查询客户
     *
     * @param customerCode 客户编号
     * @return 客户信息
     */
    CrmCustomerVo selectCustomerByCode(String customerCode);

    /**
     * 检查账户邮箱唯一性（仅在未删除的记录中检查）
     *
     * @param accountEmail 账户邮箱
     * @param customerId   客户ID（修改时排除自己）
     * @return true-唯一，false-重复
     */
    boolean checkAccountEmailUniqueInActive(String accountEmail, Long customerId);

    /**
     * 检查客户编号唯一性（仅在未删除的记录中检查）
     *
     * @param customerCode 客户编号
     * @param customerId   客户ID（修改时排除自己）
     * @return true-唯一，false-重复
     */
    boolean checkCustomerCodeUniqueInActive(String customerCode, Long customerId);

    /**
     * 查询公海客户列表
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return 客户列表
     */
    TableDataInfo<CrmCustomerVo> queryPublicCustomerPageList(CrmCustomerBo bo, PageQuery pageQuery);

    /**
     * 自动将超过指定天数未跟进的客户转为公海客户
     *
     * @param days 未跟进天数阈值，默认15天
     * @return 转换的客户数量
     */
    int autoConvertToPublicCustomers(int days);

    /**
     * 将客户转为公海客户
     *
     * @param customerId 客户ID
     * @return 是否成功
     */
    Boolean convertToPublicCustomer(Long customerId);

    /**
     * 将公海客户分配给业务员
     *
     * @param customerId 客户ID
     * @param salesUserId 业务员ID
     * @param salesUserName 业务员姓名
     * @return 是否成功
     */
    Boolean assignPublicCustomerToSales(Long customerId, Long salesUserId, String salesUserName);

    /**
     * 批量分配公海客户给业务员
     *
     * @param customerIds 客户ID列表
     * @param salesUserId 业务员ID
     * @param salesUserName 业务员姓名
     * @return 成功分配的客户数量
     */
    int batchAssignPublicCustomersToSales(List<Long> customerIds, Long salesUserId, String salesUserName);

    /**
     * 查询所有客户信息分页列表（用于客户查重）
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return 客户信息分页列表
     */
    TableDataInfo<CrmCustomerVo> queryAllCustomerPageList(CrmCustomerBo bo, PageQuery pageQuery);

    /**
     * 获取客户选择列表（用于订单等模块选择客户）
     *
     * @return 客户选择列表
     */
    List<CustomerOptionVo> getCustomerOptions();

    /**
     * 更新客户说明
     *
     * @param customerId 客户ID
     * @param customerDescription 客户说明
     * @return 是否更新成功
     */
    Boolean updateCustomerDescription(Long customerId, String customerDescription);

    /**
     * 根据客户编号或邮箱获取已存在的客户信息
     *
     * @param customerCode 客户编号
     * @param accountEmail 账户邮箱
     * @return 如果存在返回客户信息，否则返回null
     */
    CrmCustomerVo getExistingCustomer(String customerCode, String accountEmail);



}
