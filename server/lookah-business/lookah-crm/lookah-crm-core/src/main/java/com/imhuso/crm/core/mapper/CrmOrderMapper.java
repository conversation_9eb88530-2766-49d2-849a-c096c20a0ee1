package com.imhuso.crm.core.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.mybatis.core.mapper.BaseMapperPlus;
import com.imhuso.crm.core.domain.CrmOrder;
import com.imhuso.crm.core.domain.vo.CrmOrderVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * CRM客户订单Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface CrmOrderMapper extends BaseMapperPlus<CrmOrder, CrmOrderVo> {

    /**
     * 分页查询订单列表（关联用户信息）
     */
    Page<CrmOrderVo> selectVoPageWithUserInfo(IPage<CrmOrder> page, @Param(Constants.WRAPPER) Wrapper<CrmOrder> queryWrapper);

    /**
     * 查询订单列表（关联用户信息）
     */
    List<CrmOrderVo> selectVoListWithUserInfo(@Param(Constants.WRAPPER) Wrapper<CrmOrder> queryWrapper);

    /**
     * 根据ID查询订单（关联用户信息）
     */
    CrmOrderVo selectVoByIdWithUserInfo(@Param("orderId") Long orderId);

}
