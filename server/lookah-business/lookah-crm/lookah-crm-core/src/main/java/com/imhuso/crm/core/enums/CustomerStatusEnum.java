package com.imhuso.crm.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 客户状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CustomerStatusEnum {

    POTENTIAL("potential", "Potential Customer"),
    CONTACTED("contacted", "Contacted"),
    INTERESTED("interested", "Interested"),
    NEGOTIATING("negotiating", "Negotiating"),
    CLOSED_WON("closed_won", "Closed Won"),
    CLOSED_LOST("closed_lost", "Closed Lost"),
    ACTIVE("active", "Active Customer"),
    INACTIVE("inactive", "Inactive Customer"),
    CLOSED("closed", "Closed Business"),
    SEMI_ACTIVE("semi_active", "Semi Active Customer");

    /**
     * 代码
     */
    private final String code;

    /**
     * 描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 对应的枚举值，如果未找到或code为null则返回null
     */
    public static CustomerStatusEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (CustomerStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
