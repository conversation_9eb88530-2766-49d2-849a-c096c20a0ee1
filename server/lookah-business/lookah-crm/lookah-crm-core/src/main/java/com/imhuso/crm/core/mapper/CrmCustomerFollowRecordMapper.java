package com.imhuso.crm.core.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.imhuso.common.mybatis.core.mapper.BaseMapperPlus;
import com.imhuso.crm.core.domain.CrmCustomerFollowRecord;
import com.imhuso.crm.core.domain.vo.CrmCustomerFollowRecordVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * CRM客户跟进记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Mapper
public interface CrmCustomerFollowRecordMapper extends BaseMapperPlus<CrmCustomerFollowRecord, CrmCustomerFollowRecordVo> {

    /**
     * 分页查询跟进记录列表（连表查询客户信息）
     */
    IPage<CrmCustomerFollowRecordVo> selectVoPageWithCustomer(IPage<CrmCustomerFollowRecordVo> page, @Param(Constants.WRAPPER) Wrapper<CrmCustomerFollowRecord> queryWrapper);

    /**
     * 查询跟进记录列表（连表查询客户信息）
     */
    List<CrmCustomerFollowRecordVo> selectVoListWithCustomer(@Param(Constants.WRAPPER) Wrapper<CrmCustomerFollowRecord> queryWrapper);

}
