package com.imhuso.crm.core.service;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.crm.core.domain.bo.CrmCustomerSocialMediaBo;
import com.imhuso.crm.core.domain.vo.CrmCustomerSocialMediaVo;

import java.util.Collection;
import java.util.List;

/**
 * CRM客户社媒账号Service接口
 *
 * <AUTHOR>
 */
public interface ICrmCustomerSocialMediaService {

    /**
     * 查询CRM客户社媒账号
     */
    CrmCustomerSocialMediaVo queryById(Long socialMediaId);

    /**
     * 查询CRM客户社媒账号列表
     */
    TableDataInfo<CrmCustomerSocialMediaVo> queryPageList(CrmCustomerSocialMediaBo bo, PageQuery pageQuery);

    /**
     * 查询CRM客户社媒账号列表
     */
    List<CrmCustomerSocialMediaVo> queryList(CrmCustomerSocialMediaBo bo);

    /**
     * 根据客户ID查询社媒账号列表
     */
    List<CrmCustomerSocialMediaVo> queryByCustomerId(Long customerId);

    /**
     * 新增CRM客户社媒账号
     */
    Boolean insertByBo(CrmCustomerSocialMediaBo bo);

    /**
     * 修改CRM客户社媒账号
     */
    Boolean updateByBo(CrmCustomerSocialMediaBo bo);

    /**
     * 校验并批量删除CRM客户社媒账号信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids);

    /**
     * 批量保存客户社媒账号
     */
    Boolean saveBatchByCustomerId(Long customerId, List<CrmCustomerSocialMediaBo> socialMediaList);

    /**
     * 删除客户的所有社媒账号
     */
    Boolean deleteByCustomerId(Long customerId);

}
