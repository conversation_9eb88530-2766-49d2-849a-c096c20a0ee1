package com.imhuso.crm.core.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 客户选择选项视图对象 CustomerOptionVo
 *
 * <AUTHOR> Assistant
 */
@Data
public class CustomerOptionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户编号
     */
    private String customerCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 客户状态
     */
    private String customerStatus;

    /**
     * 显示标签（公司名称 - 客户编号）
     */
    public String getLabel() {
        return companyName + " - " + customerCode;
    }

    /**
     * 选项值（客户ID）
     */
    public Long getValue() {
        return customerId;
    }

}
