package com.imhuso.crm.core.service;

import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.crm.core.domain.bo.CrmCustomerFollowRecordBo;
import com.imhuso.crm.core.domain.vo.CrmCustomerFollowRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * CRM客户跟进记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
public interface ICrmCustomerFollowRecordService {

    /**
     * 查询CRM客户跟进记录
     */
    CrmCustomerFollowRecordVo queryById(Long followId);

    /**
     * 查询CRM客户跟进记录列表
     */
    TableDataInfo<CrmCustomerFollowRecordVo> queryPageList(CrmCustomerFollowRecordBo bo, PageQuery pageQuery);

    /**
     * 查询CRM客户跟进记录列表
     */
    List<CrmCustomerFollowRecordVo> queryList(CrmCustomerFollowRecordBo bo);

    /**
     * 根据客户ID查询跟进记录列表
     */
    List<CrmCustomerFollowRecordVo> queryByCustomerId(Long customerId);

    /**
     * 新增CRM客户跟进记录
     */
    Boolean insertByBo(CrmCustomerFollowRecordBo bo);

    /**
     * 修改CRM客户跟进记录
     */
    Boolean updateByBo(CrmCustomerFollowRecordBo bo);

    /**
     * 校验并批量删除CRM客户跟进记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取客户最近一次跟进记录
     */
    CrmCustomerFollowRecordVo getLatestFollowRecord(Long customerId);

    /**
     * 获取客户最近的跟进记录（Controller使用）
     */
    CrmCustomerFollowRecordVo getLatestByCustomerId(Long customerId);

    /**
     * 统计客户跟进次数
     */
    Long countFollowRecords(Long customerId);

    /**
     * 统计客户跟进次数（Controller使用）
     */
    Long countByCustomerId(Long customerId);

    /**
     * 审核跟进记录
     */
    Boolean auditFollowRecord(CrmCustomerFollowRecordBo bo);

}
