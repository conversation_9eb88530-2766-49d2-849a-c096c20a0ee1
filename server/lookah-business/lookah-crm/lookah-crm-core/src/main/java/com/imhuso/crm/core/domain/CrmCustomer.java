package com.imhuso.crm.core.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * CRM客户信息(CrmCustomer)实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("crm_customer")
public class CrmCustomer extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    @TableId(value = "customer_id")
    private Long customerId;

    /**
     * 客户编号（老客户手动输入，新客户按LCS#00000规则自动生成）
     */
    private String customerCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 客户类型（Distributor, Cash&Carry, SmokeShop, Dispensary, Gas Station, Vape Shop, Chain Smoke Shops, Chain Dispensaries, E-commerce）
     */
    private String customerType;

    /**
     * 经营地点数量
     */
    private Integer businessLocationCount;

    /**
     * 所在国家
     */
    private String country;

    /**
     * 所在州
     */
    private String state;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 账号邮箱（商城开户邮箱）
     */
    private String accountEmail;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * 客户电话
     */
    private String customerPhone;

    /**
     * WhatsApp号码
     */
    private String whatsappNumber;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 职位（Boss, Purchase Manager, Store Manager, Co-partner, Sales Manager, others）
     */
    private String position;

    /**
     * 客户画像（销售代表填写）
     */
    private String customerProfile;

    /**
     * 感兴趣的产品
     */
    private String interestedProducts;

    /**
     * 客户来源
     */
    private String customerSource;

    /**
     * 客户状态
     */
    private String customerStatus;

    /**
     * 状态描述
     */
    private String statusDescription;

    /**
     * 星级评定（1-5星）
     */
    private Integer starRating;

    /**
     * 累计下单次数
     */
    private Integer totalOrderCount;

    /**
     * 累计下单金额
     */
    private BigDecimal totalOrderAmount;

    /**
     * 第一次下单日期
     */
    private Date firstOrderDate;

    /**
     * 最后一次下单日期
     */
    private Date lastOrderDate;

    /**
     * 最后跟进时间
     */
    private Date lastFollowTime;

    /**
     * 负责销售代表ID
     */
    private Long salesUserId;

    /**
     * 负责销售代表姓名
     */
    private String salesUserName;

    /**
     * 是否公海客户（0否 1是）
     */
    private String isPublic;

    /**
     * 是否为新客户（1=是, 0=否）
     */
    private String isNewCustomer;

    /**
     * 客户说明
     */
    private String customerDescription;

    /**
     * 系统星级评分（1-5星）
     */
    private Integer systemStarRating;

    /**
     * 单次采购金额
     */
    private BigDecimal singlePurchaseAmount;

    /**
     * 采购频率（每周、每月、每季度、不定期）
     */
    private String purchaseFrequency;

    /**
     * 是否好沟通（0否 1是）
     */
    private String isEasyCommunicate;

    /**
     * 联系方式偏好（电话、邮件、WhatsApp、微信等）
     */
    private String contactPreference;

    /**
     * 决策权大小（完全决策权、部分决策权、无决策权、需要上级审批）
     */
    private String decisionAuthority;

    /**
     * 对价格敏感的程度（高、中、低）
     */
    private String priceSensitivity;

    /**
     * 成交的关键点
     */
    private String dealKeyPoints;

    /**
     * 已销售的竞品品牌
     */
    private String competitorBrands;

    /**
     * 联系频次（每天、每周、每月、不定期）
     */
    private String contactFrequency;

    /**
     * 下单频率（每周、每月、每季度、不定期）
     */
    private String orderFrequency;

    /**
     * 影响成交的主要因素
     */
    private String dealInfluenceFactors;

    /**
     * 影响成交的其他因素（自定义填写）
     */
    private String dealInfluenceOther;

    /**
     * 社媒账号
     */
    private String socialMediaAccount;

    /**
     * 社媒类型（Facebook、Ins、领英、TikTok、Youtube）
     */
    private String socialMediaType;

    /**
     * 客户图片（逗号分隔存储多张图片URL）
     */
    private String customerImages;

    /**
     * 自定义JSON setter，处理前端传来的数组格式
     * 前端可能传递字符串或数组，统一转换为逗号分隔的字符串存储
     */
    @JsonSetter("customerImages")
    public void setCustomerImagesFromJson(JsonNode jsonNode) {
        if (jsonNode == null || jsonNode.isNull()) {
            this.customerImages = null;
            return;
        }

        if (jsonNode.isArray()) {
            // 如果是数组，转换为逗号分隔的字符串
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < jsonNode.size(); i++) {
                if (i > 0) {
                    sb.append(",");
                }
                sb.append(jsonNode.get(i).asText());
            }
            this.customerImages = sb.toString();
        } else if (jsonNode.isTextual()) {
            // 如果是字符串，直接使用
            this.customerImages = jsonNode.asText();
        } else {
            // 其他类型，设置为null
            this.customerImages = null;
        }
    }

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除时间戳（毫秒，软删除时记录，NULL表示未删除）
     */
    private Long deletedAt;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

}
