package com.imhuso.crm.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.crm.core.domain.CrmCustomerSocialMedia;
import com.imhuso.crm.core.domain.bo.CrmCustomerSocialMediaBo;
import com.imhuso.crm.core.domain.vo.CrmCustomerSocialMediaVo;
import com.imhuso.crm.core.mapper.CrmCustomerSocialMediaMapper;
import com.imhuso.crm.core.service.ICrmCustomerSocialMediaService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * CRM客户社媒账号Service业务层处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class CrmCustomerSocialMediaServiceImpl implements ICrmCustomerSocialMediaService {

    private final CrmCustomerSocialMediaMapper baseMapper;

    /**
     * 查询CRM客户社媒账号
     */
    @Override
    public CrmCustomerSocialMediaVo queryById(Long socialMediaId) {
        return baseMapper.selectVoById(socialMediaId);
    }

    /**
     * 查询CRM客户社媒账号列表
     */
    @Override
    public TableDataInfo<CrmCustomerSocialMediaVo> queryPageList(CrmCustomerSocialMediaBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CrmCustomerSocialMedia> lqw = buildQueryWrapper(bo);
        Page<CrmCustomerSocialMediaVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询CRM客户社媒账号列表
     */
    @Override
    public List<CrmCustomerSocialMediaVo> queryList(CrmCustomerSocialMediaBo bo) {
        LambdaQueryWrapper<CrmCustomerSocialMedia> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 根据客户ID查询社媒账号列表
     */
    @Override
    public List<CrmCustomerSocialMediaVo> queryByCustomerId(Long customerId) {
        LambdaQueryWrapper<CrmCustomerSocialMedia> lqw = Wrappers.lambdaQuery();
        lqw.eq(CrmCustomerSocialMedia::getCustomerId, customerId);
        lqw.eq(CrmCustomerSocialMedia::getStatus, BusinessConstants.NORMAL);
        lqw.orderByAsc(CrmCustomerSocialMedia::getSortOrder);
        lqw.orderByDesc(CrmCustomerSocialMedia::getIsPrimary);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<CrmCustomerSocialMedia> buildQueryWrapper(CrmCustomerSocialMediaBo bo) {
        LambdaQueryWrapper<CrmCustomerSocialMedia> lqw = Wrappers.lambdaQuery();
        lqw.eq(ObjectUtil.isNotNull(bo.getCustomerId()), CrmCustomerSocialMedia::getCustomerId, bo.getCustomerId());
        lqw.eq(StringUtils.isNotBlank(bo.getSocialMediaType()), CrmCustomerSocialMedia::getSocialMediaType, bo.getSocialMediaType());
        lqw.like(StringUtils.isNotBlank(bo.getSocialMediaAccount()), CrmCustomerSocialMedia::getSocialMediaAccount, bo.getSocialMediaAccount());
        lqw.eq(StringUtils.isNotBlank(bo.getIsPrimary()), CrmCustomerSocialMedia::getIsPrimary, bo.getIsPrimary());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), CrmCustomerSocialMedia::getStatus, bo.getStatus());
        lqw.orderByAsc(CrmCustomerSocialMedia::getSortOrder);
        lqw.orderByDesc(CrmCustomerSocialMedia::getIsPrimary);
        return lqw;
    }

    /**
     * 新增CRM客户社媒账号
     */
    @Override
    public Boolean insertByBo(CrmCustomerSocialMediaBo bo) {
        CrmCustomerSocialMedia add = BeanUtil.toBean(bo, CrmCustomerSocialMedia.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setSocialMediaId(add.getSocialMediaId());
        }
        return flag;
    }

    /**
     * 修改CRM客户社媒账号
     */
    @Override
    public Boolean updateByBo(CrmCustomerSocialMediaBo bo) {
        CrmCustomerSocialMedia update = BeanUtil.toBean(bo, CrmCustomerSocialMedia.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(CrmCustomerSocialMedia entity) {
        // 设置默认值
        if (entity.getIsPrimary() == null) {
            entity.setIsPrimary(BusinessConstants.NO_NUMBER_STRING);
        }
        if (entity.getSortOrder() == null) {
            entity.setSortOrder(1);
        }
        if (entity.getStatus() == null) {
            entity.setStatus(BusinessConstants.NORMAL);
        }

        // 校验必要字段
        if (entity.getCustomerId() == null) {
            throw new ServiceException("客户ID不能为空");
        }
        if (StringUtils.isBlank(entity.getSocialMediaType())) {
            throw new ServiceException("社交媒体类型不能为空");
        }
        if (StringUtils.isBlank(entity.getSocialMediaAccount())) {
            throw new ServiceException("账号不能为空");
        }
    }

    /**
     * 批量删除CRM客户社媒账号
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids) {
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 批量保存客户社媒账号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveBatchByCustomerId(Long customerId, List<CrmCustomerSocialMediaBo> socialMediaList) {
        // 先删除客户现有的社媒账号
        deleteByCustomerId(customerId);

        // 批量插入新的社媒账号
        if (CollUtil.isNotEmpty(socialMediaList)) {
            for (int i = 0; i < socialMediaList.size(); i++) {
                CrmCustomerSocialMediaBo bo = socialMediaList.get(i);
                bo.setCustomerId(customerId);
                bo.setSortOrder(i + 1);
                // 第一个设为主要账号
                bo.setIsPrimary(i == 0 ? BusinessConstants.YES_NUMBER_STRING : BusinessConstants.NO_NUMBER_STRING); // 第一个默认为主要账号
                bo.setStatus(BusinessConstants.NORMAL);
                insertByBo(bo);
            }
        }
        return true;
    }

    /**
     * 删除客户的所有社媒账号
     */
    @Override
    public Boolean deleteByCustomerId(Long customerId) {
        LambdaQueryWrapper<CrmCustomerSocialMedia> lqw = Wrappers.lambdaQuery();
        lqw.eq(CrmCustomerSocialMedia::getCustomerId, customerId);
        return baseMapper.delete(lqw) >= 0;
    }
}
