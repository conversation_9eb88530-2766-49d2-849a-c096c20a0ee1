package com.imhuso.crm.core.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * CRM客户跟进记录对象 crm_customer_follow_record
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("crm_customer_follow_record")
public class CrmCustomerFollowRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 跟进记录ID
     */
    @TableId(value = "follow_id")
    private Long followId;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 跟进日期
     */
    private Date followDate;

    /**
     * 跟进内容
     */
    private String followContent;

    /**
     * 跟进过程存在的难点
     */
    private String followDifficulties;

    /**
     * 跟进方式（电话、邮件、面谈、微信等）
     */
    private String followType;

    /**
     * 跟进主题（新客首次触达、日常跟进、新品推广、回答疑问）
     */
    private String followTheme;

    /**
     * 跟进客户状态（与客户状态字典一致）
     */
    private String followCustomerStatus;

    /**
     * 跟进结果
     */
    private String followResult;

    /**
     * 下次跟进日期
     */
    private Date nextFollowDate;

    /**
     * 跟进业务员ID
     */
    private Long followUserId;

    /**
     * 跟进业务员姓名
     */
    private String followUserName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 跟进附件（JSON格式存储文件信息数组）
     */
    private String followAttachments;

    /**
     * 跟进建议（具体的跟进建议和行动计划）
     */
    private String followSuggestions;

    /**
     * 是否审批（0-否 1-是）
     */
    private String isApprovalRequired;

    /**
     * 是否已读（0-未读 1-已读）
     */
    private String isRead;

    /**
     * 审核时间
     */
    private Date approvalTime;

}
