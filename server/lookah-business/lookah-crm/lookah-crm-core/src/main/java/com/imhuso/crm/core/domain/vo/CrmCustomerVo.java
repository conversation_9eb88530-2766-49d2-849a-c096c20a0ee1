package com.imhuso.crm.core.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.imhuso.common.excel.annotation.ExcelDictFormat;
import com.imhuso.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import com.imhuso.crm.core.domain.CrmCustomer;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Arrays;
import java.util.Collections;
import com.fasterxml.jackson.annotation.JsonGetter;

/**
 * CRM客户信息视图对象 CrmCustomerVo
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CrmCustomer.class)
public class CrmCustomerVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    @ExcelProperty(value = "客户ID")
    private Long customerId;

    /**
     * 客户编号
     */
    @ExcelProperty(value = "客户编号")
    private String customerCode;

    /**
     * 公司名称
     */
    @ExcelProperty(value = "公司名称")
    private String companyName;

    /**
     * 客户类型
     */
    @ExcelProperty(value = "客户类型")
    private String customerType;

    /**
     * 经营地点数量
     */
    @ExcelProperty(value = "经营地点数量")
    private Integer businessLocationCount;

    /**
     * 所在国家
     */
    @ExcelProperty(value = "所在国家")
    private String country;

    /**
     * 所在州
     */
    @ExcelProperty(value = "所在州")
    private String state;

    /**
     * 邮编
     */
    @ExcelProperty(value = "邮编")
    private String zipCode;

    /**
     * 账号邮箱
     */
    @ExcelProperty(value = "账号邮箱")
    private String accountEmail;

    /**
     * 联系邮箱
     */
    @ExcelProperty(value = "联系邮箱")
    private String contactEmail;

    /**
     * 客户电话
     */
    @ExcelProperty(value = "客户电话")
    private String customerPhone;

    /**
     * WhatsApp号码
     */
    @ExcelProperty(value = "WhatsApp号码")
    private String whatsappNumber;

    /**
     * 联系人
     */
    @ExcelProperty(value = "联系人")
    private String contactPerson;

    /**
     * 职位
     */
    @ExcelProperty(value = "职位")
    private String position;

    /**
     * 客户画像
     */
    @ExcelProperty(value = "客户画像")
    private String customerProfile;

    /**
     * 感兴趣的产品
     */
    @ExcelProperty(value = "感兴趣的产品")
    private String interestedProducts;

    /**
     * 客户来源
     */
    @ExcelProperty(value = "客户来源")
    private String customerSource;

    /**
     * 客户状态
     */
    @ExcelProperty(value = "客户状态")
    private String customerStatus;

    /**
     * 状态描述
     */
    @ExcelProperty(value = "状态描述")
    private String statusDescription;

    /**
     * 星级评定
     */
    @ExcelProperty(value = "星级评定")
    private Integer starRating;

    /**
     * 累计下单次数
     */
    @ExcelProperty(value = "累计下单次数")
    private Integer totalOrderCount;

    /**
     * 累计下单金额
     */
    @ExcelProperty(value = "累计下单金额")
    private BigDecimal totalOrderAmount;

    /**
     * 第一次下单日期
     */
    @ExcelProperty(value = "第一次下单日期")
    private Date firstOrderDate;

    /**
     * 最后一次下单日期
     */
    @ExcelProperty(value = "最后一次下单日期")
    private Date lastOrderDate;

    /**
     * 最后跟进时间
     */
    @ExcelProperty(value = "最后跟进时间")
    private Date lastFollowTime;

    /**
     * 负责销售代表ID
     */
    private Long salesUserId;

    /**
     * 负责销售代表姓名
     */
    @ExcelProperty(value = "负责销售代表")
    private String salesUserName;

    /**
     * 是否公海客户
     */
    @ExcelProperty(value = "是否公海客户", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String isPublic;

    /**
     * 是否为新客户
     */
    @ExcelProperty(value = "是否为新客户", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "CRM_IS_NEW_CUSTOMER")
    private String isNewCustomer;

    /**
     * 客户说明
     */
    @ExcelProperty(value = "客户说明")
    private String customerDescription;

    /**
     * 系统星级评分
     */
    @ExcelProperty(value = "系统星级评分", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "CRM_SYSTEM_STAR_RATING")
    private Integer systemStarRating;

    /**
     * 单次采购金额
     */
    private BigDecimal singlePurchaseAmount;

    /**
     * 采购频率
     */
    private String purchaseFrequency;

    /**
     * 是否好沟通
     */
    @ExcelProperty(value = "是否好沟通", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String isEasyCommunicate;

    /**
     * 联系方式偏好
     */
    @ExcelProperty(value = "联系方式偏好")
    private String contactPreference;

    /**
     * 决策权大小
     */
    @ExcelProperty(value = "决策权大小")
    private String decisionAuthority;

    /**
     * 对价格敏感的程度
     */
    @ExcelProperty(value = "对价格敏感的程度")
    private String priceSensitivity;

    /**
     * 成交的关键点
     */
    @ExcelProperty(value = "成交的关键点")
    private String dealKeyPoints;

    /**
     * 已销售的竞品品牌
     */
    @ExcelProperty(value = "已销售的竞品品牌")
    private String competitorBrands;

    /**
     * 联系频次
     */
    @ExcelProperty(value = "联系频次")
    private String contactFrequency;

    /**
     * 下单频率
     */
    @ExcelProperty(value = "下单频率")
    private String orderFrequency;

    /**
     * 影响成交的主要因素
     */
    @ExcelProperty(value = "影响成交的主要因素")
    private String dealInfluenceFactors;

    /**
     * 获取影响成交因素数组（用于前端多选显示）
     */
    public String[] getDealInfluenceFactorsArray() {
        if (dealInfluenceFactors != null && !dealInfluenceFactors.isEmpty()) {
            return dealInfluenceFactors.split(",");
        }
        return new String[0];
    }

    /**
     * 影响成交的其他因素
     */
    @ExcelProperty(value = "影响成交的其他因素")
    private String dealInfluenceOther;

    /**
     * 社交媒体账号1（用于导出）
     */
    @ExcelProperty(value = "社交媒体账号1")
    private String socialMediaAccount1;

    /**
     * 社交媒体类型1（用于导出）
     */
    @ExcelProperty(value = "社交媒体类型1", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_social_media_type")
    private String socialMediaType1;

    /**
     * 社交媒体账号2（用于导出）
     */
    @ExcelProperty(value = "社交媒体账号2")
    private String socialMediaAccount2;

    /**
     * 社交媒体类型2（用于导出）
     */
    @ExcelProperty(value = "社交媒体类型2", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_social_media_type")
    private String socialMediaType2;

    /**
     * 社交媒体账号3（用于导出）
     */
    @ExcelProperty(value = "社交媒体账号3")
    private String socialMediaAccount3;

    /**
     * 社交媒体类型3（用于导出）
     */
    @ExcelProperty(value = "社交媒体类型3", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "crm_social_media_type")
    private String socialMediaType3;

    /**
     * 客户图片（逗号分隔存储多张图片URL）
     */
    @ExcelProperty(value = "客户图片")
    private String customerImages;

    /**
     * 获取客户图片数组格式（用于前端组件）
     * 将逗号分隔的字符串转换为字符串数组
     */
    @JsonGetter("customerImages")
    public List<String> getCustomerImagesArray() {
        if (customerImages == null || customerImages.trim().isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.asList(customerImages.split(","));
    }

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 删除时间戳（毫秒，软删除时记录，NULL表示未删除）
     */
    private Long deletedAt;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 客户社媒账号列表
     */
    private List<CrmCustomerSocialMediaVo> socialMediaList;



}
