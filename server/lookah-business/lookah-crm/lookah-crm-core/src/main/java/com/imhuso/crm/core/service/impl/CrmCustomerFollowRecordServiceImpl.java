package com.imhuso.crm.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.imhuso.common.core.constant.BusinessConstants;
import com.imhuso.common.core.exception.ServiceException;
import com.imhuso.common.core.utils.MapstructUtils;
import com.imhuso.common.core.utils.StringUtils;
import com.imhuso.common.mybatis.annotation.DataColumn;
import com.imhuso.common.mybatis.annotation.DataPermission;
import com.imhuso.common.mybatis.core.page.PageQuery;
import com.imhuso.common.mybatis.core.page.TableDataInfo;
import com.imhuso.common.satoken.utils.LoginHelper;
import com.imhuso.crm.core.domain.CrmCustomerFollowRecord;
import com.imhuso.crm.core.domain.bo.CrmCustomerFollowRecordBo;
import com.imhuso.crm.core.domain.vo.CrmCustomerFollowRecordVo;
import com.imhuso.crm.core.mapper.CrmCustomerFollowRecordMapper;
import com.imhuso.crm.core.service.ICrmCustomerFollowRecordService;
import com.imhuso.crm.core.service.ICrmCustomerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * CRM客户跟进记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CrmCustomerFollowRecordServiceImpl implements ICrmCustomerFollowRecordService {

    private final CrmCustomerFollowRecordMapper baseMapper;
    @Lazy
    private final ICrmCustomerService crmCustomerService;

    /**
     * 查询CRM客户跟进记录
     */
    @Override
    public CrmCustomerFollowRecordVo queryById(Long followId) {
        return baseMapper.selectVoById(followId);
    }

    /**
     * 查询CRM客户跟进记录列表（连表查询客户信息）
     */
    @Override
    @DataPermission({
        @DataColumn(key = "userName",value = "cfr.follow_user_id")
    })
    public TableDataInfo<CrmCustomerFollowRecordVo> queryPageList(CrmCustomerFollowRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CrmCustomerFollowRecord> lqw = buildQueryWrapperWithTableAlias(bo);
        IPage<CrmCustomerFollowRecordVo> result = baseMapper.selectVoPageWithCustomer(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询CRM客户跟进记录列表（连表查询客户信息）
     */
    @Override
    @DataPermission({
        @DataColumn(key = "userName",value = "cfr.follow_user_id")
    })
    public List<CrmCustomerFollowRecordVo> queryList(CrmCustomerFollowRecordBo bo) {
        LambdaQueryWrapper<CrmCustomerFollowRecord> lqw = buildQueryWrapperWithTableAlias(bo);
        return baseMapper.selectVoListWithCustomer(lqw);
    }

    /**
     * 根据客户ID查询跟进记录列表
     */
    @Override
    public List<CrmCustomerFollowRecordVo> queryByCustomerId(Long customerId) {
        LambdaQueryWrapper<CrmCustomerFollowRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(CrmCustomerFollowRecord::getCustomerId, customerId);
        lqw.orderByDesc(CrmCustomerFollowRecord::getFollowDate);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 构建查询条件包装器（带表别名，用于关联查询）
     * 参考 CrmOrderServiceImpl.buildQueryWrapperWithTableAlias 实现
     */
    private LambdaQueryWrapper<CrmCustomerFollowRecord> buildQueryWrapperWithTableAlias(CrmCustomerFollowRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CrmCustomerFollowRecord> lqw = Wrappers.lambdaQuery();

        // 需要使用表别名的字段（避免字段歧义）
        if (ObjectUtil.isNotNull(bo.getCustomerId())) {
            lqw.apply("cfr.customer_id = {0}", bo.getCustomerId());
        }

        // 基础查询条件（这些字段在 crm_customer_follow_record 表中是唯一的）
        lqw.eq(ObjectUtil.isNotNull(bo.getFollowDate()), CrmCustomerFollowRecord::getFollowDate, bo.getFollowDate());
        lqw.like(StringUtils.isNotBlank(bo.getFollowContent()), CrmCustomerFollowRecord::getFollowContent, bo.getFollowContent());
        lqw.eq(StringUtils.isNotBlank(bo.getFollowType()), CrmCustomerFollowRecord::getFollowType, bo.getFollowType());
        lqw.eq(StringUtils.isNotBlank(bo.getFollowTheme()), CrmCustomerFollowRecord::getFollowTheme, bo.getFollowTheme());
        lqw.like(StringUtils.isNotBlank(bo.getFollowResult()), CrmCustomerFollowRecord::getFollowResult, bo.getFollowResult());
        lqw.eq(ObjectUtil.isNotNull(bo.getFollowUserId()), CrmCustomerFollowRecord::getFollowUserId, bo.getFollowUserId());
        lqw.like(StringUtils.isNotBlank(bo.getFollowUserName()), CrmCustomerFollowRecord::getFollowUserName, bo.getFollowUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getIsApprovalRequired()), CrmCustomerFollowRecord::getIsApprovalRequired, bo.getIsApprovalRequired());

        // 公司名称查询需要使用表别名，因为需要连表查询客户表
        if (StringUtils.isNotBlank(bo.getCompanyName())) {
            lqw.apply("c.company_name LIKE {0}", "%" + bo.getCompanyName() + "%");
        }

        // 时间范围查询（使用表别名避免字段冲突）
        if (params != null) {
            if (params.get("beginFollowDate") != null && params.get("endFollowDate") != null) {
                lqw.apply("cfr.follow_date BETWEEN {0} AND {1}", params.get("beginFollowDate"), params.get("endFollowDate"));
            }
        }

        // 注意：不在这里添加 ORDER BY，让 XML 中统一处理
        return lqw;
    }

    /**
     * 新增CRM客户跟进记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(CrmCustomerFollowRecordBo bo) {
        CrmCustomerFollowRecord add = MapstructUtils.convert(bo, CrmCustomerFollowRecord.class);
        validEntityBeforeSave(add);

        // 设置跟进人员信息（如果未设置）
        if (ObjectUtil.isNull(add.getFollowUserId())) {
            add.setFollowUserId(LoginHelper.getUserId());
        }
        if (StringUtils.isBlank(add.getFollowUserName())) {
            add.setFollowUserName(LoginHelper.getUsername());
        }

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setFollowId(add.getFollowId());
            // 更新客户最后跟进时间
            crmCustomerService.updateLastFollowTime(add.getCustomerId());

            // 更新客户联系频次
            try {
                crmCustomerService.updateContactFrequency(add.getCustomerId());
                log.info("更新客户联系频次成功，客户ID: {}", add.getCustomerId());
            } catch (Exception e) {
                log.error("更新客户联系频次失败，客户ID: {}, 错误: {}", add.getCustomerId(), e.getMessage());
                // 不影响主流程，只记录日志
            }

            // 如果跟进记录中设置了跟进客户状态，则同步更新客户状态
            if (StringUtils.isNotBlank(add.getFollowCustomerStatus())) {
                try {
                    crmCustomerService.updateCustomerStatus(add.getCustomerId(), add.getFollowCustomerStatus());
                    log.info("根据跟进记录更新客户状态成功，客户ID: {}, 新状态: {}", add.getCustomerId(), add.getFollowCustomerStatus());
                } catch (Exception e) {
                    log.error("根据跟进记录更新客户状态失败，客户ID: {}, 状态: {}, 错误: {}",
                        add.getCustomerId(), add.getFollowCustomerStatus(), e.getMessage());
                    // 不影响主流程，只记录日志
                }
            }
        }
        return flag;
    }

    /**
     * 修改CRM客户跟进记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(CrmCustomerFollowRecordBo bo) {
        CrmCustomerFollowRecord update = MapstructUtils.convert(bo, CrmCustomerFollowRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(CrmCustomerFollowRecord entity) {
        // 校验必要字段
        if (entity.getCustomerId() == null) {
            throw new ServiceException("客户ID不能为空");
        }
        if (entity.getFollowDate() == null) {
            throw new ServiceException("跟进日期不能为空");
        }
        if (StringUtils.isBlank(entity.getFollowType())) {
            throw new ServiceException("跟进方式不能为空");
        }
        if (StringUtils.isBlank(entity.getFollowContent())) {
            throw new ServiceException("跟进内容不能为空");
        }
    }

    /**
     * 批量删除CRM客户跟进记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 获取客户最近一次跟进记录
     */
    @Override
    public CrmCustomerFollowRecordVo getLatestFollowRecord(Long customerId) {
        LambdaQueryWrapper<CrmCustomerFollowRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(CrmCustomerFollowRecord::getCustomerId, customerId);
        lqw.orderByDesc(CrmCustomerFollowRecord::getFollowDate);
        lqw.last("LIMIT 1");
        return baseMapper.selectVoOne(lqw);
    }

    /**
     * 统计客户跟进次数
     */
    @Override
    public Long countFollowRecords(Long customerId) {
        LambdaQueryWrapper<CrmCustomerFollowRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(CrmCustomerFollowRecord::getCustomerId, customerId);
        return baseMapper.selectCount(lqw);
    }

    /**
     * 获取客户最近的跟进记录（Controller使用）
     */
    @Override
    public CrmCustomerFollowRecordVo getLatestByCustomerId(Long customerId) {
        return getLatestFollowRecord(customerId);
    }

    /**
     * 统计客户跟进次数（Controller使用）
     */
    @Override
    public Long countByCustomerId(Long customerId) {
        return countFollowRecords(customerId);
    }

    /**
     * 审核跟进记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean auditFollowRecord(CrmCustomerFollowRecordBo bo) {
        if (ObjectUtil.isNull(bo.getFollowId())) {
            throw new ServiceException("跟进记录ID不能为空");
        }

        // 获取现有记录
        CrmCustomerFollowRecord existEntity = baseMapper.selectById(bo.getFollowId());
        if (ObjectUtil.isNull(existEntity)) {
            throw new ServiceException("跟进记录不存在");
        }

        // 创建更新实体
        CrmCustomerFollowRecord updateEntity = new CrmCustomerFollowRecord();
        updateEntity.setFollowId(bo.getFollowId());

        // 设置审核相关字段
        if (StringUtils.isNotBlank(bo.getFollowSuggestions())) {
            updateEntity.setFollowSuggestions(bo.getFollowSuggestions());
        }
        if (StringUtils.isNotBlank(bo.getIsApprovalRequired())) {
            updateEntity.setIsApprovalRequired(bo.getIsApprovalRequired());
        }

        // 设置审核时间
        updateEntity.setApprovalTime(new Date());

        return baseMapper.updateById(updateEntity) > 0;
    }

}
