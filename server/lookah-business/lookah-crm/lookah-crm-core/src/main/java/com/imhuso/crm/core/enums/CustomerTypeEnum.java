package com.imhuso.crm.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 客户类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CustomerTypeEnum {

    DISTRIBUTOR("Distributor", "Distributor"),
    CASH_AND_CARRY("Cash&Carry", "Cash & Carry"),
    SMOKE_SHOP("SmokeShop", "Smoke Shop"),
    DISPENSARY("Dispensary", "Dispensary"),
    GAS_STATION("GasStation", "Gas Station"),
    VAPE_SHOP("VapeShop", "Vape Shop"),
    CHAIN_SMOKE_SHOPS("ChainSmokeShops", "Chain Smoke Shops"),
    CHAIN_DISPENSARIES("ChainDispensaries", "Chain Dispensaries"),
    E_COMMERCE("E-commerce", "E-commerce");

    /**
     * 代码
     */
    private final String code;

    /**
     * 描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     */
    public static CustomerTypeEnum getByCode(String code) {
        for (CustomerTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
