package com.imhuso.crm.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单详情状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrderDetailStatus {

    /**
     * 正常
     */
    NORMAL("normal", "正常"),

    /**
     * 退款
     */
    REFUND("refund", "退款"),

    /**
     * 退货
     */
    RETURN("return", "退货"),

    /**
     * 到货不良
     */
    DEFECTIVE("defective", "到货不良"),

    /**
     * 到货少货
     */
    SHORTAGE("shortage", "到货少货"),

    /**
     * 发错货
     */
    WRONG("wrong", "发错货"),

    /**
     * 重发
     */
    RESEND("resend", "重发");

    /**
     * 状态值
     */
    private final String value;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static OrderDetailStatus fromValue(String value) {
        for (OrderDetailStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据值获取描述
     *
     * @param value 状态值
     * @return 对应的描述，如果不存在则返回原值
     */
    public static String getDescription(String value) {
        OrderDetailStatus status = fromValue(value);
        return status != null ? status.getDescription() : value;
    }
}
