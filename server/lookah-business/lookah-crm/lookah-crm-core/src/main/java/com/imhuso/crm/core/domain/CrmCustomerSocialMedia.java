package com.imhuso.crm.core.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imhuso.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * CRM客户社媒账号对象 crm_customer_social_media
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("crm_customer_social_media")
public class CrmCustomerSocialMedia extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 社媒账号ID
     */
    @TableId
    private Long socialMediaId;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 社媒类型
     */
    private String socialMediaType;

    /**
     * 社媒账号
     */
    private String socialMediaAccount;

    /**
     * 是否主要账号（1=是, 0=否）
     */
    private String isPrimary;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 状态（1=正常, 0=停用）
     */
    private String status;

}
