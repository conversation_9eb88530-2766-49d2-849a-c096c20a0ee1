<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imhuso.crm.core.mapper.CrmCustomerFollowRecordMapper">

    <!-- 连表查询结果映射 -->
    <resultMap type="com.imhuso.crm.core.domain.vo.CrmCustomerFollowRecordVo" id="CrmCustomerFollowRecordWithCustomerResult">
        <result property="followId" column="follow_id"/>
        <result property="customerId" column="customer_id"/>
        <result property="followDate" column="follow_date"/>
        <result property="followContent" column="follow_content"/>
        <result property="followDifficulties" column="follow_difficulties"/>
        <result property="followType" column="follow_type"/>
        <result property="followTheme" column="follow_theme"/>
        <result property="followCustomerStatus" column="follow_customer_status"/>
        <result property="followResult" column="follow_result"/>
        <result property="nextFollowDate" column="next_follow_date"/>
        <result property="followUserId" column="follow_user_id"/>
        <result property="followUserName" column="follow_user_name"/>
        <result property="followAttachments" column="follow_attachments"/>
        <result property="followSuggestions" column="follow_suggestions"/>
        <result property="isApprovalRequired" column="is_approval_required"/>
        <result property="isRead" column="is_read"/>
        <result property="approvalTime" column="approval_time"/>
        <result property="remark" column="remark"/>
        <result property="createDept" column="create_dept"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <!-- 客户信息字段 -->
        <result property="companyName" column="company_name"/>
        <result property="customerCode" column="customer_code"/>
    </resultMap>

    <!-- 连表查询SQL -->
    <sql id="selectCrmCustomerFollowRecordWithCustomerVo">
        SELECT
            cfr.follow_id,
            cfr.customer_id,
            cfr.follow_date,
            cfr.follow_content,
            cfr.follow_difficulties,
            cfr.follow_type,
            cfr.follow_theme,
            cfr.follow_customer_status,
            cfr.follow_result,
            cfr.next_follow_date,
            cfr.follow_user_id,
            cfr.follow_user_name,
            cfr.follow_attachments,
            cfr.follow_suggestions,
            cfr.is_approval_required,
            cfr.is_read,
            cfr.approval_time,
            cfr.remark,
            cfr.create_dept,
            cfr.create_by,
            cfr.create_time,
            cfr.update_by,
            cfr.update_time,
            c.company_name,
            c.customer_code
        FROM crm_customer_follow_record cfr
        LEFT JOIN crm_customer c ON cfr.customer_id = c.customer_id
    </sql>

    <!-- 分页查询跟进记录列表（连表查询客户信息） -->
    <select id="selectVoPageWithCustomer" resultMap="CrmCustomerFollowRecordWithCustomerResult">
        <include refid="selectCrmCustomerFollowRecordWithCustomerVo"/>
        <where>
            (c.del_flag = '0' OR c.del_flag IS NULL)
            <!-- MyBatis Plus EntityWrapper的sqlSegment已经过框架安全处理，无SQL注入风险 -->
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                AND ${ew.sqlSegment}
            </if>
        </where>
        ORDER BY cfr.follow_date DESC
    </select>

    <!-- 查询跟进记录列表（连表查询客户信息） -->
    <select id="selectVoListWithCustomer" resultMap="CrmCustomerFollowRecordWithCustomerResult">
        <include refid="selectCrmCustomerFollowRecordWithCustomerVo"/>
        <where>
            (c.del_flag = '0' OR c.del_flag IS NULL)
            <!-- MyBatis Plus EntityWrapper的sqlSegment已经过框架安全处理，无SQL注入风险 -->
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                AND ${ew.sqlSegment}
            </if>
        </where>
        ORDER BY cfr.follow_date DESC
    </select>

</mapper>
