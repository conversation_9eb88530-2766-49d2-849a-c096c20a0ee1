<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imhuso.crm.core.mapper.CrmOrderMapper">

    <!-- 结果映射 -->
    <resultMap id="CrmOrderVoResult" type="com.imhuso.crm.core.domain.vo.CrmOrderVo">
        <id property="orderId" column="order_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="orderDate" column="order_date"/>
        <result property="customerId" column="customer_id"/>
        <result property="customerCode" column="customer_code"/>
        <result property="companyName" column="company_name"/>
        <result property="orderStatus" column="order_status"/>
        <result property="orderType" column="order_type"/>
        <result property="orderTotalAmount" column="order_total_amount"/>
        <result property="shippingFee" column="shipping_fee"/>
        <result property="paymentMethod" column="payment_method"/>
        <result property="relatedOrderNo" column="related_order_no"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="createByName" column="createByName"/>
        <result property="updateByName" column="updateByName"/>
    </resultMap>

    <!-- 基础查询SQL片段 -->
    <sql id="selectCrmOrderVoWithUserInfo">
        SELECT
            o.order_id,
            o.order_no,
            o.order_date,
            o.customer_id,
            o.customer_code,
            o.company_name,
            o.order_status,
            o.order_type,
            o.order_total_amount,
            o.shipping_fee,
            o.payment_method,
            o.related_order_no,
            o.remark,
            o.create_time,
            o.create_by,
            o.update_time,
            o.update_by,
            cu.nick_name as createByName,
            uu.nick_name as updateByName
        FROM crm_order o
        LEFT JOIN sys_user cu ON o.create_by = cu.user_id
        LEFT JOIN sys_user uu ON o.update_by = uu.user_id
    </sql>

    <!-- 分页查询订单列表（关联用户信息） -->
    <select id="selectVoPageWithUserInfo" resultMap="CrmOrderVoResult">
        <include refid="selectCrmOrderVoWithUserInfo"/>
        <where>
            o.del_flag = '0'
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                AND ${ew.sqlSegment}
            </if>
        </where>
        ORDER BY o.create_time DESC
    </select>

    <!-- 查询订单列表（关联用户信息） -->
    <select id="selectVoListWithUserInfo" resultMap="CrmOrderVoResult">
        <include refid="selectCrmOrderVoWithUserInfo"/>
        <where>
            o.del_flag = '0'
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                AND ${ew.sqlSegment}
            </if>
        </where>
        ORDER BY o.create_time DESC
    </select>

    <!-- 根据ID查询订单（关联用户信息） -->
    <select id="selectVoByIdWithUserInfo" resultMap="CrmOrderVoResult">
        <include refid="selectCrmOrderVoWithUserInfo"/>
        <where>
            o.del_flag = '0'
            AND o.order_id = #{orderId}
        </where>
    </select>

</mapper>
