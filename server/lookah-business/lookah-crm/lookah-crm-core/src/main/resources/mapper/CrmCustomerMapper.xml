<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imhuso.crm.core.mapper.CrmCustomerMapper">

    <!-- 查询客户订单统计信息 -->
    <select id="selectCustomerOrderStatistics" parameterType="Long" resultType="com.imhuso.crm.core.domain.vo.CrmCustomerOrderStatisticsVo">
        SELECT
            COUNT(*) as totalOrderCount,
            COALESCE(SUM(
                CASE
                    WHEN EXISTS (
                        SELECT 1 FROM crm_order_detail cod
                        WHERE cod.order_id = co.order_id
                        AND cod.detail_status = 'refund'
                        AND cod.del_flag = '0'
                    ) THEN (
                        -- 如果订单有退款项，则计算非退款项的金额
                        SELECT COALESCE(SUM(cod2.product_total_amount), 0)
                        FROM crm_order_detail cod2
                        WHERE cod2.order_id = co.order_id
                        AND cod2.detail_status != 'refund'
                        AND cod2.del_flag = '0'
                    )
                    ELSE co.order_total_amount
                END
            ), 0) as totalOrderAmount,
            MIN(order_date) as firstOrderDate,
            MAX(order_date) as lastOrderDate
        FROM crm_order co
        WHERE co.customer_id = #{customerId}
          AND co.del_flag = '0'
          AND co.order_status != 'cancelled'
    </select>


    <!-- 查询客户感兴趣的产品名称（按订单数量排序） -->
    <select id="selectInterestedProductsByCustomerId" parameterType="java.lang.Long"
            resultType="com.imhuso.crm.core.domain.vo.CrmCustomerInterestedProductVo">
        SELECT
            od.product_name as productName,
            SUM(od.quantity) as totalQuantity
        FROM crm_order o
                 JOIN crm_order_detail od ON o.order_id = od.order_id
        WHERE o.customer_id = #{customerId}
          AND o.del_flag = '0'
          AND od.del_flag = '0'
        GROUP BY od.product_name
        ORDER BY totalQuantity DESC
    </select>

    <!-- 查询客户跟进记录统计信息（用于计算联系频次） -->
    <select id="selectFollowStatisticsByCustomerId" parameterType="java.lang.Long"
            resultType="com.imhuso.crm.core.domain.vo.CrmCustomerFollowStatisticsVo">
        SELECT
            COUNT(*) as totalCount,
            MIN(follow_date) as firstFollowDate,
            MAX(follow_date) as lastFollowDate,
            DATEDIFF(MAX(follow_date), MIN(follow_date)) as dateSpanDays
        FROM crm_customer_follow_record
        WHERE customer_id = #{customerId}
          AND follow_date IS NOT NULL
    </select>


    <!-- 查询客户订单金额和数量统计（用于计算星级） -->
    <select id="selectOrderSummaryByCustomerId" parameterType="java.lang.Long"
            resultType="com.imhuso.crm.core.domain.vo.CrmCustomerOrderSummaryVo">
        WITH order_amounts AS (
            SELECT
                co.order_id,
                CASE
                    WHEN EXISTS (
                        SELECT 1 FROM crm_order_detail cod
                        WHERE cod.order_id = co.order_id
                        AND cod.detail_status = 'refund'
                        AND cod.del_flag = '0'
                    ) THEN (
                        -- 如果订单有退款项，则计算非退款项的金额
                        SELECT COALESCE(SUM(cod2.product_total_amount), 0)
                        FROM crm_order_detail cod2
                        WHERE cod2.order_id = co.order_id
                        AND cod2.detail_status != 'refund'
                        AND cod2.del_flag = '0'
                    )
                    ELSE co.order_total_amount
                END as effective_amount
            FROM crm_order co
            WHERE co.customer_id = #{customerId}
              AND co.del_flag = '0'
        )
        SELECT
            COUNT(*) as totalOrderCount,
            COALESCE(SUM(effective_amount), 0) as totalOrderAmount,
            COALESCE(AVG(effective_amount), 0) as avgOrderAmount,
            COALESCE(MAX(effective_amount), 0) as maxOrderAmount
        FROM order_amounts
    </select>
</mapper>
