<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imhuso.crm.core.mapper.CrmOrderDetailMapper">

    <!-- 结果映射 -->
    <resultMap id="CrmOrderDetailVoResult" type="com.imhuso.crm.core.domain.vo.CrmOrderDetailVo">
        <id property="detailId" column="detail_id"/>
        <result property="orderId" column="order_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="productColor" column="product_color"/>
        <result property="unitPrice" column="unit_price"/>
        <result property="quantity" column="quantity"/>
        <result property="productTotalAmount" column="product_total_amount"/>
        <result property="remark" column="remark"/>
        <result property="detailStatus" column="detail_status"/>
        <result property="isGift" column="is_gift"/>
        <result property="refundReason" column="refund_reason"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <!-- 关联字段 -->
        <result property="companyName" column="company_name"/>
        <result property="createByName" column="create_by_name"/>
        <result property="orderDate" column="order_date"/>
    </resultMap>

    <!-- 基础查询SQL片段（关联订单和客户信息） -->
    <sql id="selectCrmOrderDetailVoWithOrderInfo">
        SELECT
            od.detail_id,
            od.order_id,
            od.order_no,
            od.product_id,
            od.product_name,
            od.product_color,
            od.unit_price,
            od.quantity,
            od.product_total_amount,
            od.remark,
            od.detail_status,
            od.is_gift,
            od.refund_reason,
            od.create_time,
            od.create_by,
            od.update_time,
            od.update_by,
            -- 关联订单信息
            o.company_name,
            o.order_date,
            -- 关联创建人信息（销售员）
            cu.nick_name as create_by_name
        FROM crm_order_detail od
        LEFT JOIN crm_order o ON od.order_id = o.order_id AND o.del_flag = '0'
        LEFT JOIN sys_user cu ON od.create_by = cu.user_id
    </sql>

    <!-- 分页查询订单详情列表（关联订单和客户信息） -->
    <select id="selectVoPageWithOrderInfo" resultMap="CrmOrderDetailVoResult">
        <include refid="selectCrmOrderDetailVoWithOrderInfo"/>
        <where>
            od.del_flag = '0'
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                AND ${ew.sqlSegment}
            </if>
        </where>
        ORDER BY od.create_time DESC
    </select>

    <!-- 查询订单详情列表（关联订单和客户信息） -->
    <select id="selectVoListWithOrderInfo" resultMap="CrmOrderDetailVoResult">
        <include refid="selectCrmOrderDetailVoWithOrderInfo"/>
        <where>
            od.del_flag = '0'
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                AND ${ew.sqlSegment}
            </if>
        </where>
        ORDER BY od.create_time DESC
    </select>

    <!-- 根据ID查询订单详情（关联订单和客户信息） -->
    <select id="selectVoByIdWithOrderInfo" resultMap="CrmOrderDetailVoResult">
        <include refid="selectCrmOrderDetailVoWithOrderInfo"/>
        <where>
            od.del_flag = '0'
            AND od.detail_id = #{detailId}
        </where>
    </select>

</mapper>
