<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.imhuso</groupId>
        <artifactId>lookah-crm</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>lookah-crm-core</artifactId>
    <description>
        lookah-crm-core CRM核心业务层
    </description>

    <dependencies>


        <!-- Common Core -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-core</artifactId>
        </dependency>

        <!-- Common MyBatis -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-mybatis</artifactId>
        </dependency>

        <!-- Common Redis -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-redis</artifactId>
        </dependency>

        <!-- Common Translation -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-translation</artifactId>
        </dependency>

        <!-- Wholesale Core -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-wholesale-core</artifactId>
        </dependency>

        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- MyBatis Plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>

        <!-- Sa-Token -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot3-starter</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- Excel 依赖 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-excel</artifactId>
        </dependency>

        <!-- System系统模块 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-system</artifactId>
        </dependency>

        <!-- SnailJob 定时任务模块 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-job</artifactId>
        </dependency>
    </dependencies>

</project>
