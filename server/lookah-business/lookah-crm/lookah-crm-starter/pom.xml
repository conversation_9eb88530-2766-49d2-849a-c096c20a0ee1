<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.imhuso</groupId>
        <artifactId>lookah-crm</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>lookah-crm-starter</artifactId>
    <description>
        lookah-crm-starter CRM自动配置启动器
    </description>

    <dependencies>
        <!-- CRM核心模块 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-crm-core</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- CRM API模块 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-crm-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务共用核心模块 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-business-common-core</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Spring Boot 自动配置 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>

        <!-- Spring Boot 配置处理器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

</project>
