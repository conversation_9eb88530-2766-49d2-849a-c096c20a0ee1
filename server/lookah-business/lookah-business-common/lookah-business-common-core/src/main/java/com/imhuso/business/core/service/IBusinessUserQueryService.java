package com.imhuso.business.core.service;

import com.imhuso.business.core.domain.vo.BusinessUserOptionVo;

import java.util.List;

/**
 * 业务用户查询服务接口（用于跨业务模块查询用户信息）
 *
 * <AUTHOR>
 */
public interface IBusinessUserQueryService {

    /**
     * 获取管理员候选列表（用于下拉选择）
     *
     * @return 管理员选项列表
     */
    List<BusinessUserOptionVo> getAdminUserOptions();

    /**
     * 获取业务代表候选列表（用于下拉选择）
     *
     * @return 业务代表选项列表
     */
    List<BusinessUserOptionVo> getSalespersonOptions();
}
