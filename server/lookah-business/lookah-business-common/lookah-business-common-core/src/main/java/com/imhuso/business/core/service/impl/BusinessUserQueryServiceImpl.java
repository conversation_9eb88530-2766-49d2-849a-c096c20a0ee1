package com.imhuso.business.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imhuso.business.core.domain.BusinessSysUser;
import com.imhuso.business.core.domain.vo.BusinessUserOptionVo;
import com.imhuso.business.core.mapper.BusinessSysUserMapper;
import com.imhuso.business.core.service.IBusinessUserQueryService;
import com.imhuso.common.core.constant.BusinessConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务用户查询服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BusinessUserQueryServiceImpl implements IBusinessUserQueryService {

    private final BusinessSysUserMapper businessSysUserMapper;

    /**
     * 获取管理员候选列表（用于下拉选择）
     *
     * @return 管理员选项列表
     */
    @Override
    public List<BusinessUserOptionVo> getAdminUserOptions() {
        try {
            // 使用LambdaQueryWrapper查询正常状态的用户
            LambdaQueryWrapper<BusinessSysUser> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(BusinessSysUser::getStatus, BusinessConstants.NORMAL)  // 正常状态
                   .eq(BusinessSysUser::getDelFlag, BusinessConstants.LOGIC_NOT_DELETE)  // 未删除
                   .orderByAsc(BusinessSysUser::getUserName);

            // 使用BaseMapperPlus的selectVoList方法，自动转换为VO
            List<BusinessUserOptionVo> userOptions = businessSysUserMapper.selectVoList(wrapper);

            log.debug("查询管理员候选列表成功，数量: {}", userOptions.size());
            return userOptions;
        } catch (Exception e) {
            log.error("查询管理员候选列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("查询管理员列表失败", e);
        }
    }

    /**
     * 获取业务代表候选列表（用于下拉选择）
     *
     * @return 业务代表选项列表
     */
    @Override
    public List<BusinessUserOptionVo> getSalespersonOptions() {
        try {
            // 使用LambdaQueryWrapper查询正常状态且为业务代表的用户
            LambdaQueryWrapper<BusinessSysUser> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(BusinessSysUser::getStatus, BusinessConstants.NORMAL)  // 正常状态
                   .eq(BusinessSysUser::getDelFlag, BusinessConstants.LOGIC_NOT_DELETE)  // 未删除
                   .eq(BusinessSysUser::getIsSalesperson, "1")  // 是业务代表
                   .orderByAsc(BusinessSysUser::getUserName);

            // 使用BaseMapperPlus的selectVoList方法，自动转换为VO
            List<BusinessUserOptionVo> salespersonOptions = businessSysUserMapper.selectVoList(wrapper);

            log.debug("查询业务代表候选列表成功，数量: {}", salespersonOptions.size());
            return salespersonOptions;
        } catch (Exception e) {
            log.error("查询业务代表候选列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("查询业务代表列表失败", e);
        }
    }
}
