package com.imhuso.business.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 系统用户实体（用于跨业务模块查询用户信息）
 *
 * <AUTHOR>
 */
@Data
@TableName("sys_user")
public class BusinessSysUser implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableId(type = IdType.AUTO)
    private Long userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户状态（0停用 1正常）
     */
    private String status;

    /**
     * 是否为业务代表（0否 1是）
     */
    private String isSalesperson;

    /**
     * 删除标志（0存在 1删除）
     */
    private String delFlag;

    /**
     * 创建时间
     */
    private Date createTime;
}
