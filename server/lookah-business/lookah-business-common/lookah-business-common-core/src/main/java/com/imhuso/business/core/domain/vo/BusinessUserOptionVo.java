package com.imhuso.business.core.domain.vo;

import com.imhuso.business.core.domain.BusinessSysUser;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户选项视图对象（用于下拉选择）
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = BusinessSysUser.class)
public class BusinessUserOptionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户状态（0正常 1停用）
     */
    private String status;
}