<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>lookah-business-common</artifactId>
        <groupId>com.imhuso</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>lookah-business-common-core</artifactId>

    <description>
        业务共用核心模块 - 共享的核心业务逻辑
    </description>

    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-translation</artifactId>
        </dependency>

        <!-- 客户端管理模块 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-client</artifactId>
        </dependency>

        <!-- OSS功能模块 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-oss</artifactId>
        </dependency>

        <!-- 社交登录模块 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-social</artifactId>
        </dependency>

        <!-- 加密模块 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-encrypt</artifactId>
        </dependency>

        <!-- 敏感词模块 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-sensitive</artifactId>
        </dependency>

        <!-- 邮件模块 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-mail</artifactId>
        </dependency>

        <!-- 限流模块 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-ratelimiter</artifactId>
        </dependency>

        <!-- 幂等性模块 -->
        <dependency>
            <groupId>com.imhuso</groupId>
            <artifactId>lookah-common-idempotent</artifactId>
        </dependency>
    </dependencies>

</project>
