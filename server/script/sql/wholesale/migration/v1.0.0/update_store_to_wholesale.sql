-- ----------------------------
-- 数据库迁移脚本：Store模块重构为Whs模块
-- 功能：
-- 1. 将所有 store_ 开头的表名改为 whs_ 前缀
-- 2. 移除所有数据表中的 tenant_id 字段
-- ----------------------------

-- 设置 SQL 模式为严格模式，避免意外删除
SET SQL_SAFE_UPDATES = 0;

-- ----------------------------
-- 第一部分：动态发现并重命名所有 store_ 表为 whs_ 表
-- ----------------------------

-- 首先显示将要重命名的表
SELECT
    table_name as 'Current Store Tables',
    CONCAT('whs_', SUBSTRING(table_name, 7)) as 'New Whs Tables'
FROM INFORMATION_SCHEMA.TABLES
WHERE table_schema = DATABASE()
AND table_name LIKE 'store_%'
ORDER BY table_name;

-- 生成重命名SQL语句并显示
SET SESSION group_concat_max_len = 50000;
SET @rename_sql = (
    SELECT GROUP_CONCAT(
        CONCAT('RENAME TABLE `', table_name, '` TO `whs_', SUBSTRING(table_name, 7), '`;')
        SEPARATOR '\n'
    )
    FROM INFORMATION_SCHEMA.TABLES
    WHERE table_schema = DATABASE()
    AND table_name LIKE 'store_%'
    ORDER BY table_name
);

-- 显示将要执行的重命名SQL
SELECT IFNULL(@rename_sql, 'No whs_ tables found to rename') as 'Rename SQL Commands';

-- 执行动态生成的重命名操作
-- 注意：MySQL不能直接执行动态生成的多个RENAME语句，需要逐一执行
-- 所以我们使用存储过程来实现

DROP PROCEDURE IF EXISTS RenameStoreTables;

DELIMITER //
CREATE PROCEDURE RenameStoreTables()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE table_name_var VARCHAR(255);
    DECLARE new_table_name VARCHAR(255);
    DECLARE rename_sql TEXT;

    -- 声明游标
    DECLARE table_cursor CURSOR FOR
        SELECT table_name
        FROM INFORMATION_SCHEMA.TABLES
        WHERE table_schema = DATABASE()
        AND table_name LIKE 'store_%'
        ORDER BY table_name;

    -- 声明异常处理
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    -- 打开游标
    OPEN table_cursor;

    -- 循环处理每个表
    read_loop: LOOP
        FETCH table_cursor INTO table_name_var;
        IF done THEN
            LEAVE read_loop;
        END IF;

        -- 生成新表名
        SET new_table_name = CONCAT('whs_', SUBSTRING(table_name_var, 7));

        -- 生成并执行重命名SQL
        SET @sql = CONCAT('RENAME TABLE `', table_name_var, '` TO `', new_table_name, '`');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;

        -- 输出执行信息
        SELECT CONCAT('Renamed: ', table_name_var, ' -> ', new_table_name) as 'Rename Result';

    END LOOP;

    -- 关闭游标
    CLOSE table_cursor;

END //
DELIMITER ;

-- 执行重命名存储过程
CALL RenameStoreTables();

-- 清理存储过程
DROP PROCEDURE IF EXISTS RenameStoreTables;

-- ----------------------------
-- 第二部分：智能移除所有表中的 tenant_id 字段
-- ----------------------------

-- 首先显示所有包含 tenant_id 字段的表
SELECT
    table_name as 'Tables with tenant_id',
    column_name as 'Column Name',
    data_type as 'Data Type',
    is_nullable as 'Nullable'
FROM INFORMATION_SCHEMA.COLUMNS
WHERE table_schema = DATABASE()
AND column_name = 'tenant_id'
ORDER BY table_name;

-- 使用存储过程动态删除所有 tenant_id 字段
DROP PROCEDURE IF EXISTS RemoveTenantIdColumns;

DELIMITER //
CREATE PROCEDURE RemoveTenantIdColumns()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE table_name_var VARCHAR(255);
    DECLARE drop_sql TEXT;

    -- 声明游标 - 查找所有包含 tenant_id 字段的表
    DECLARE tenant_cursor CURSOR FOR
        SELECT DISTINCT table_name
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE table_schema = DATABASE()
        AND column_name = 'tenant_id'
        ORDER BY table_name;

    -- 声明异常处理
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    -- 打开游标
    OPEN tenant_cursor;

    -- 循环处理每个表
    tenant_loop: LOOP
        FETCH tenant_cursor INTO table_name_var;
        IF done THEN
            LEAVE tenant_loop;
        END IF;

        -- 生成并执行删除 tenant_id 字段的SQL
        SET @sql = CONCAT('ALTER TABLE `', table_name_var, '` DROP COLUMN `tenant_id`');

        -- 执行删除操作
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;

        -- 输出执行信息
        SELECT CONCAT('Removed tenant_id from: ', table_name_var) as 'Tenant ID Removal Result';

    END LOOP;

    -- 关闭游标
    CLOSE tenant_cursor;

END //
DELIMITER ;

-- 执行删除 tenant_id 字段的存储过程
CALL RemoveTenantIdColumns();

-- 清理存储过程
DROP PROCEDURE IF EXISTS RemoveTenantIdColumns;

-- ----------------------------
-- 第三部分：验证 tenant_id 字段清理结果
-- ----------------------------

-- 检查是否还有剩余的 tenant_id 字段
SELECT
    CASE
        WHEN COUNT(*) = 0 THEN 'SUCCESS: All tenant_id columns have been removed!'
        ELSE CONCAT('WARNING: ', COUNT(*), ' tenant_id columns still exist')
    END as 'Tenant ID Cleanup Status',
    GROUP_CONCAT(CONCAT(table_name, '.', column_name)) as 'Remaining Columns'
FROM INFORMATION_SCHEMA.COLUMNS
WHERE table_schema = DATABASE()
AND column_name = 'tenant_id';

-- ----------------------------
-- 第四部分：更新相关的配置和引用
-- ----------------------------

-- 更新字典类型中的 store 前缀
UPDATE `sys_dict_type`
SET `dict_type` = REPLACE(`dict_type`, 'store_', 'whs_')
WHERE `dict_type` LIKE 'store_%';

-- 更新字典数据中的相关引用
UPDATE `sys_dict_data`
SET `dict_type` = REPLACE(`dict_type`, 'store_', 'whs_')
WHERE `dict_type` LIKE 'store_%';

-- 更新菜单权限中的 store 前缀（避免影响已有的wholesale权限）
UPDATE `sys_menu`
SET `perms` = REPLACE(`perms`, 'store:', 'wholesale:')
WHERE `perms` LIKE 'store:%'
AND `perms` NOT LIKE '%wholesale%';

-- 单独更新组件路径（避免影响已有的wholesale组件）
UPDATE `sys_menu`
SET `component` = REPLACE(`component`, 'store/', 'wholesale/')
WHERE `component` LIKE '%store/%'
AND `component` NOT LIKE '%wholesale%'
AND `menu_id` >= 6000 AND `menu_id` < 7000;  -- 假设批发模块菜单ID范围

-- 更新菜单名称中的"商城"为"批发"
UPDATE `sys_menu`
SET `menu_name` = REPLACE(`menu_name`, '商城', '批发')
WHERE `menu_name` LIKE '%商城%'
AND `menu_id` >= 6000 AND `menu_id` < 7000;

-- ----------------------------
-- 第五部分：更新whs表的备注信息
-- ----------------------------

-- 使用存储过程动态更新所有whs表的备注，将"商城"替换为"批发"
DROP PROCEDURE IF EXISTS UpdateWhsTableComments;

DELIMITER //
CREATE PROCEDURE UpdateWhsTableComments()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE table_name_var VARCHAR(255);
    DECLARE table_comment_var VARCHAR(2000);
    DECLARE new_comment VARCHAR(2000);

    -- 声明游标 - 查找所有whs_开头且备注包含"商城"的表
    DECLARE comment_cursor CURSOR FOR
        SELECT table_name, table_comment
        FROM INFORMATION_SCHEMA.TABLES
        WHERE table_schema = DATABASE()
        AND table_name LIKE 'whs_%'
        AND table_comment LIKE '%商城%'
        ORDER BY table_name;

    -- 声明异常处理
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    -- 打开游标
    OPEN comment_cursor;

    -- 循环处理每个表
    comment_loop: LOOP
        FETCH comment_cursor INTO table_name_var, table_comment_var;
        IF done THEN
            LEAVE comment_loop;
        END IF;

        -- 替换备注中的"商城"为"批发"
        SET new_comment = REPLACE(table_comment_var, '商城', '批发');

        -- 生成并执行修改表备注的SQL
        SET @sql = CONCAT('ALTER TABLE `', table_name_var, '` COMMENT = ''', new_comment, '''');

        -- 执行修改操作
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;

        -- 输出执行信息
        SELECT CONCAT('Updated comment for: ', table_name_var, ' - ', table_comment_var, ' -> ', new_comment) as 'Table Comment Update Result';

    END LOOP;

    -- 关闭游标
    CLOSE comment_cursor;

END //
DELIMITER ;

-- 执行更新表备注的存储过程
CALL UpdateWhsTableComments();

-- 清理存储过程
DROP PROCEDURE IF EXISTS UpdateWhsTableComments;

-- ----------------------------
-- 第六部分：验证迁移结果
-- ----------------------------

-- 检查是否还有 store_ 开头的表
SELECT COUNT(*) as remaining_store_tables,
       GROUP_CONCAT(table_name) as table_list
FROM INFORMATION_SCHEMA.TABLES
WHERE table_schema = DATABASE()
AND table_name LIKE 'store_%';

-- 检查是否还有 tenant_id 字段
SELECT COUNT(*) as remaining_tenant_id_columns,
       GROUP_CONCAT(CONCAT(table_name, '.', column_name)) as column_list
FROM INFORMATION_SCHEMA.COLUMNS
WHERE table_schema = DATABASE()
AND column_name = 'tenant_id';

-- 检查新的 whs_ 表数量
SELECT COUNT(*) as whs_tables_count
FROM INFORMATION_SCHEMA.TABLES
WHERE table_schema = DATABASE()
AND table_name LIKE 'whs_%';

-- 检查whs表中是否还有包含"商城"的备注
SELECT
    CASE
        WHEN COUNT(*) = 0 THEN 'SUCCESS: All table comments have been updated from "商城" to "批发"!'
        ELSE CONCAT('WARNING: ', COUNT(*), ' whs tables still have "商城" in their comments')
    END as 'Table Comment Update Status',
    GROUP_CONCAT(CONCAT(table_name, ': ', table_comment)) as 'Remaining Comments'
FROM INFORMATION_SCHEMA.TABLES
WHERE table_schema = DATABASE()
AND table_name LIKE 'whs_%'
AND table_comment LIKE '%商城%';

-- 恢复 SQL 安全模式
SET SQL_SAFE_UPDATES = 1;

-- 迁移完成提示
SELECT 'Store to Whs migration completed!' as status,
       'Please verify the results above and update application configuration accordingly.' as note;
