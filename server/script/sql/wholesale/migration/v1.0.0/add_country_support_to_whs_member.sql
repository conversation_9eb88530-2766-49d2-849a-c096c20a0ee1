-- ----------------------------
-- Migration: 为地址系统添加加拿大支持
-- Author: jf
-- Date: 2025-06-27
-- Description:
-- 1. 在whs_country表中添加加拿大数据
-- 2. 在whs_state表中添加加拿大所有省份和地区数据（10个省份 + 3个地区）
--
-- 注意：会员本身不应有国家概念，国家信息通过地址设置
-- 地址表(whs_shipping_address)已有country字段，这是正确的设计
--
-- 加拿大省份和地区包括：
-- 省份(10个): AB, BC, MB, NB, NL, NS, ON, PE, QC, SK
-- 地区(3个): NT, NU, YT
-- ----------------------------

SET SQL_SAFE_UPDATES = 0;

-- 开始事务
START TRANSACTION;

-- ----------------------------
-- 第一步：在whs_country表中添加加拿大数据
-- ----------------------------

-- 检查是否已存在加拿大数据
SET @canada_exists = (
    SELECT COUNT(*)
    FROM `whs_country`
    WHERE `code` = 'CA'
);

-- 如果不存在加拿大数据，则插入
SET @sql = IF(@canada_exists = 0,
    'INSERT INTO `whs_country` (`id`, `code`, `name`, `name_zh`, `status`, `sort`) VALUES (2, ''CA'', ''Canada'', ''加拿大'', ''1'', 2)',
    'SELECT ''Canada data already exists in whs_country table'' as ''Info'''
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ----------------------------
-- 第二步：在whs_state表中添加加拿大省份和地区数据
-- ----------------------------

-- 检查是否已存在加拿大省份数据
SET @canada_states_exist = (
    SELECT COUNT(*)
    FROM `whs_state`
    WHERE `country_id` = 2
);

-- 如果不存在加拿大省份数据，则批量插入
SET @sql = IF(@canada_states_exist = 0,
    'INSERT INTO `whs_state` (`id`, `country_id`, `code`, `name`, `name_zh`, `status`, `sort`) VALUES
    (57, 2, ''AB'', ''Alberta'', ''阿尔伯塔省'', ''1'', 1),
    (58, 2, ''BC'', ''British Columbia'', ''不列颠哥伦比亚省'', ''1'', 2),
    (59, 2, ''MB'', ''Manitoba'', ''曼尼托巴省'', ''1'', 3),
    (60, 2, ''NB'', ''New Brunswick'', ''新不伦瑞克省'', ''1'', 4),
    (61, 2, ''NL'', ''Newfoundland and Labrador'', ''纽芬兰和拉布拉多省'', ''1'', 5),
    (62, 2, ''NS'', ''Nova Scotia'', ''新斯科舍省'', ''1'', 6),
    (63, 2, ''ON'', ''Ontario'', ''安大略省'', ''1'', 7),
    (64, 2, ''PE'', ''Prince Edward Island'', ''爱德华王子岛省'', ''1'', 8),
    (65, 2, ''QC'', ''Quebec'', ''魁北克省'', ''1'', 9),
    (66, 2, ''SK'', ''Saskatchewan'', ''萨斯喀彻温省'', ''1'', 10),
    (67, 2, ''NT'', ''Northwest Territories'', ''西北地区'', ''1'', 11),
    (68, 2, ''NU'', ''Nunavut'', ''努纳武特地区'', ''1'', 12),
    (69, 2, ''YT'', ''Yukon'', ''育空地区'', ''1'', 13)',
    'SELECT ''Canada states data already exists in whs_state table'' as ''Info'''
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ----------------------------
-- 第三步：验证数据完整性
-- ----------------------------

-- 验证国家数据
SELECT
    COUNT(*) as 'Total Countries',
    GROUP_CONCAT(CONCAT(code, ':', name) SEPARATOR ', ') as 'Available Countries'
FROM `whs_country`
WHERE `status` = '1';

-- 验证加拿大省份数据
SELECT
    COUNT(*) as 'Canada States Count',
    GROUP_CONCAT(CONCAT(code, ':', name) SEPARATOR ', ') as 'Canada States'
FROM `whs_state`
WHERE `country_id` = 2 AND `status` = '1';

-- 验证美国州数据（确保原有数据完整）
SELECT
    COUNT(*) as 'US States Count'
FROM `whs_state`
WHERE `country_id` = 1 AND `status` = '1';

-- 提交事务
COMMIT;

-- 恢复安全更新模式
SET SQL_SAFE_UPDATES = 1;

-- 完成提示
SELECT 'Canada address support migration completed successfully!' as 'Migration Result';
