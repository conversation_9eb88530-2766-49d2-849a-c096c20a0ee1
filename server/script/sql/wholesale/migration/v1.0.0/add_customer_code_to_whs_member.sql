-- ----------------------------
-- Migration: 为客户表添加客户编码字段
-- Author: jf
-- Date: 2025-06-23
-- Description: 在whs_member表中添加customer_code客户编码字段，支持手动输入，要求唯一
-- ----------------------------

-- 添加客户编码字段
ALTER TABLE `whs_member` ADD COLUMN `customer_code` varchar(50) DEFAULT NULL COMMENT '客户编码' AFTER `id`;

-- 创建唯一索引，确保客户编码唯一性（排除已删除记录）
ALTER TABLE `whs_member` ADD UNIQUE KEY `uk_customer_code` (`customer_code`, `del_flag`);

-- 创建普通索引，优化查询性能
ALTER TABLE `whs_member` ADD KEY `idx_customer_code` (`customer_code`);