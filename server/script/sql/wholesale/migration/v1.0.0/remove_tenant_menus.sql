-- ===========================
-- 移除租户管理相关菜单和权限
-- 执行时间：2025-06-21
-- 描述：清理多租户功能相关的所有菜单项和权限配置
-- ===========================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 开始事务
START TRANSACTION;

-- 第一步：删除租户管理相关的权限菜单 (功能按钮)
DELETE FROM `sys_menu` WHERE `menu_id` IN (
    1606, -- 租户查询
    1607, -- 租户新增  
    1608, -- 租户修改
    1609, -- 租户删除
    1610, -- 租户导出
    1611, -- 租户套餐查询
    1612, -- 租户套餐新增
    1613, -- 租户套餐修改
    1614, -- 租户套餐删除
    1615  -- 租户套餐导出
);

-- 第二步：删除租户管理页面菜单
DELETE FROM `sys_menu` WHERE `menu_id` IN (
    121, -- 租户管理页面
    122  -- 租户套餐管理页面
);

-- 第三步：删除租户管理目录菜单
DELETE FROM `sys_menu` WHERE `menu_id` = 6 AND `menu_name` = '租户管理';

-- 第四步：删除可能关联的角色菜单权限
DELETE FROM `sys_role_menu` WHERE `menu_id` IN (
    6, 121, 122, 1606, 1607, 1608, 1609, 1610, 1611, 1612, 1613, 1614, 1615
);

-- 验证清理结果
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'SUCCESS: All tenant-related menus have been removed!'
        ELSE CONCAT('WARNING: ', COUNT(*), ' tenant-related menus still exist')
    END as 'Tenant Menu Cleanup Status',
    COUNT(*) as 'Remaining Count'
FROM `sys_menu` 
WHERE `menu_id` IN (6, 121, 122, 1606, 1607, 1608, 1609, 1610, 1611, 1612, 1613, 1614, 1615)
   OR `menu_name` LIKE '%租户%'
   OR `perms` LIKE '%tenant%';

-- 提交事务
COMMIT;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 清理完成提示
SELECT 'Tenant menu cleanup completed successfully!' as 'Cleanup Result';