-- =====================================================
-- Wholesale模块权限细化增强脚本
-- 版本: v1.2.0
-- 创建时间: 2025-07-03
-- 描述: 为wholesale模块添加缺失的权限配置，细化敏感操作权限控制
-- 注意: 仅添加权限配置，不涉及代码逻辑变更
-- =====================================================

-- 开始事务，确保原子性
START TRANSACTION;

-- 1. 入库管理权限补充
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7001, '入库单列表', 6201, 8, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:inbound:list', '#', 103, 1, NOW(), 1, NOW(), '入库单列表权限');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7002, '入库单同步', 6201, 9, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:inbound:sync', '#', 103, 1, NOW(), 1, NOW(), '入库单同步权限');

-- 2. 产品变体权限补充
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7003, '变体查询', 6105, 1, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:variant:query', '#', 103, 1, NOW(), 1, NOW(), '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7004, '变体新增', 6105, 2, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:variant:add', '#', 103, 1, NOW(), 1, NOW(), '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7005, '变体修改', 6105, 3, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:variant:edit', '#', 103, 1, NOW(), 1, NOW(), '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7006, '变体删除', 6105, 4, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:variant:remove', '#', 103, 1, NOW(), 1, NOW(), '');

-- 3. 订单敏感操作权限细化
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7007, '订单取消', 6201, 3, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:order:cancel', '#', 103, 1, NOW(), 1, NOW(), '订单取消权限');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7008, '订单状态修改', 6201, 4, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:order:status:edit', '#', 103, 1, NOW(), 1, NOW(), '订单状态修改权限');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7009, '发票状态修改', 6201, 5, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:order:invoice:edit', '#', 103, 1, NOW(), 1, NOW(), '发票状态修改权限');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7010, '手动创建订单', 6201, 6, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:order:manual:create', '#', 103, 1, NOW(), 1, NOW(), '手动创建订单权限');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7011, '订单导出', 6201, 7, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:order:export', '#', 103, 1, NOW(), 1, NOW(), '订单导出权限');

-- 4. 库存敏感权限细化
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7012, '库存调整', 6401, 5, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:stock:adjust', '#', 103, 1, NOW(), 1, NOW(), '库存调整权限');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7013, '批量库存同步', 6401, 6, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:stock:batch:sync', '#', 103, 1, NOW(), 1, NOW(), '批量库存同步权限');

-- 5. 会员权限管理细化

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7014, '会员权限查看', 6301, 5, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:member:permission:view', '#', 103, 1, NOW(), 1, NOW(), '查看会员权限');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7015, '会员权限修改', 6301, 6, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:member:permission:edit', '#', 103, 1, NOW(), 1, NOW(), '修改会员权限');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7016, '批量权限设置', 6301, 7, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:member:permission:batch', '#', 103, 1, NOW(), 1, NOW(), '批量设置会员权限');

-- 6. 数据导入导出权限

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7017, '产品导出', 6101, 6, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:product:export', '#', 103, 1, NOW(), 1, NOW(), '产品导出权限');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7018, '产品导入', 6101, 7, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:product:import', '#', 103, 1, NOW(), 1, NOW(), '产品导入权限');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7019, '会员导出', 6301, 8, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:member:export', '#', 103, 1, NOW(), 1, NOW(), '会员导出权限');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7020, '会员导入', 6301, 9, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:member:import', '#', 103, 1, NOW(), 1, NOW(), '会员导入权限');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7021, '库存导出', 6401, 7, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:stock:export', '#', 103, 1, NOW(), 1, NOW(), '库存导出权限');

-- 7. 状态修改权限细化

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7022, '产品状态修改', 6101, 8, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:product:status:edit', '#', 103, 1, NOW(), 1, NOW(), '产品状态修改权限');

-- =====================================================
-- 权限细化总结
-- =====================================================
-- 新增功能权限: 24个
-- 涵盖模块:
--   - 入库管理: 2个权限 (列表、同步)
--   - 产品变体: 4个权限
--   - 订单敏感操作: 5个权限
--   - 库存敏感操作: 2个权限
--   - 会员权限管理: 3个权限
--   - 数据导入导出: 6个权限
--   - 状态修改: 1个权限 (仅产品状态修改)
-- 注意: 已移除区域管理和不存在的状态修改权限，与后端代码权限标识完全同步
-- =====================================================

-- 提交事务
COMMIT;
