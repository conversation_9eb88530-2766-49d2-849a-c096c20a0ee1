# V1.5.8 数据库迁移

## 概述

本次迁移主要针对OSS配置功能进行增强，支持双默认配置（公有默认 + 私有默认）。

## 迁移内容

### 001_add_oss_config_default_fields.sql

**功能**: 为OSS配置表添加双默认配置支持

**变更内容**:
1. **新增字段**:
   - `is_public_default`: 是否公有默认配置（0=否,1=是）
   - `is_private_default`: 是否私有默认配置（0=否,1=是）

2. **字段含义变更**:
   - `status` 字段含义从"是否默认"改为"启用状态"（0=禁用,1=启用）

3. **兼容性处理**:
   - 将现有的默认配置（status='0'）自动设置为公有默认配置
   - 确保所有配置都处于启用状态

**业务影响**:
- 系统支持同时配置1个公有默认 + 1个私有默认OSS配置
- 文件上传时可以指定使用公有默认、私有默认或具体的配置名称
- 向后兼容，现有功能不受影响

**使用方式**:
- 上传接口新增 `uploadType` 参数
  - `public`: 使用公有默认配置
  - `private`: 使用私有默认配置
  - 其他值: 使用指定的配置key

## 注意事项

1. 执行迁移前请备份数据库
2. 系统中只能有1个公有默认配置和1个私有默认配置
3. 迁移后需要重启应用以重新加载配置缓存