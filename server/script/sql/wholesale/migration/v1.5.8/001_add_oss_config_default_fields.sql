-- OSS配置表增加双默认配置字段
-- 添加公有默认配置标识字段
ALTER TABLE `sys_oss_config` ADD COLUMN `is_public_default` varchar(1) DEFAULT '0' COMMENT '是否公有默认配置（0=否,1=是）';

-- 添加私有默认配置标识字段
ALTER TABLE `sys_oss_config` ADD COLUMN `is_private_default` varchar(1) DEFAULT '0' COMMENT '是否私有默认配置（0=否,1=是）';

-- 更新状态字段注释，明确其含义变更：从"是否默认"改为"启用状态"
ALTER TABLE `sys_oss_config` MODIFY COLUMN `status` varchar(1) DEFAULT '1' COMMENT '启用状态（0=禁用,1=启用）';

-- 为了兼容性，将现有status='0'（原来的默认配置）的记录设置为公有默认配置
-- 但只在没有设置任何默认配置的情况下执行
UPDATE `sys_oss_config`
SET `is_public_default` = '1', `status` = '1'
WHERE `status` = '0'
AND NOT EXISTS (
    SELECT 1 FROM `sys_oss_config` t2
    WHERE t2.`is_public_default` = '1' OR t2.`is_private_default` = '1'
)
LIMIT 1;

-- 确保所有记录的status字段都是启用状态（兼容性处理）
UPDATE `sys_oss_config` SET `status` = '1' WHERE `status` = '0';
