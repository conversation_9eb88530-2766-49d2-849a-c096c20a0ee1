-- =====================================================
-- Wholesale模块订单强制完成权限脚本
-- 版本: v1.5.8
-- 创建时间: 2025-08-04
-- 描述: 为wholesale模块添加订单强制完成权限配置
-- 功能: 允许管理员强制完成因中途变化导致无法正常完成的订单
-- =====================================================

-- 开始事务，确保原子性
START TRANSACTION;

-- 1. 添加订单强制完成权限
-- 使用7043作为menu_id，确保不与现有权限冲突
INSERT INTO `sys_menu` (
    `menu_id`,
    `menu_name`,
    `parent_id`,
    `order_num`,
    `path`,
    `component`,
    `query_param`,
    `is_frame`,
    `is_cache`,
    `menu_type`,
    `visible`,
    `status`,
    `perms`,
    `icon`,
    `create_dept`,
    `create_by`,
    `create_time`,
    `update_by`,
    `update_time`,
    `remark`
) VALUES (
    7043,
    '订单强制完成',
    6201,
    13,
    '',
    '',
    '',
    0,
    0,
    'F',
    '1',
    '1',
    'wholesale:order:forceComplete',
    '#',
    103,
    1,
    NOW(),
    1,
    NOW(),
    '订单强制完成权限，用于处理因订单中途变化导致无法正常完成的订单'
);

-- =====================================================
-- 权限配置总结
-- =====================================================
-- 新增功能权限: 1个
-- 权限标识: wholesale:order:forceComplete
-- 权限名称: 订单强制完成
-- 归属模块: 订单管理 (parent_id = 6201)
--
-- 说明:
-- 1. 该权限用于控制订单强制完成功能的访问
-- 2. 强制完成功能绕过正常的完成条件检查（如发货状态）
-- 3. 必须提供强制完成的原因说明
-- 4. 操作会记录在订单状态日志中
-- =====================================================

-- 提交事务
COMMIT;

-- 验证新增权限
SELECT
    menu_id,
    menu_name,
    perms,
    remark
FROM sys_menu
WHERE menu_id = 7043;

-- 显示执行结果
SELECT '订单强制完成权限配置完成！权限标识：wholesale:order:forceComplete' as 'Migration Result';
