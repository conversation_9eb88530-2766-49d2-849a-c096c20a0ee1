-- =====================================================
-- 文件: 005_add_shipment_management_permission.sql
-- 版本: v1.6.0
-- 功能: 发货管理权限优化
-- 日期: 2025-08-06
-- 描述: 在发货管理菜单下复制订单发货权限，方便权限分配
-- =====================================================

-- 开始事务，确保原子性
START TRANSACTION;

-- 检查权限是否已存在，避免重复执行
-- 使用INSERT IGNORE防止主键冲突
INSERT IGNORE INTO `sys_menu` (`menu_id`,
                        `menu_name`,
                        `parent_id`,
                        `order_num`,
                        `path`,
                        `component`,
                        `query_param`,
                        `is_frame`,
                        `is_cache`,
                        `menu_type`,
                        `visible`,
                        `status`,
                        `perms`,
                        `icon`,
                        `create_dept`,
                        `create_by`,
                        `create_time`,
                        `update_by`,
                        `update_time`,
                        `remark`)
VALUES (70371, -- 使用70371作为menu_id，避免与现有权限ID冲突
        '订单发货',
        7037, -- 发货管理菜单的ID
        1,
        '',
        '',
        '',
        0,
        0,
        'F', -- 按钮类型
        '1', -- 显示
        '1', -- 正常状态
        'wholesale:order:shipment',
        '#',
        103,
        1,
        NOW(),
        1,
        NOW(),
        '订单发货权限，包括获取发货方案、确认发货等操作');

-- 提交事务
COMMIT;

-- =====================================================
-- 权限说明
-- =====================================================
-- wholesale:order:shipment - 订单发货权限
--   - 控制发货按钮显示
--   - 控制发货抽屉打开
--   - 控制发货相关的所有操作（包括查看和执行）
--   - 包括发货库存查询、发货方案查看、确认发货等
--
-- 此权限现在在两个菜单下都有：
-- 1. 订单管理菜单下 (parent_id: 6201, menu_id: 7036)
-- 2. 发货管理菜单下 (parent_id: 7037, menu_id: 7043)
-- =====================================================

-- =====================================================
-- 使用说明
-- =====================================================
-- 1. 管理员可以选择在订单管理或发货管理中分配发货权限
-- 2. 两个权限是相同的 wholesale:order:shipment，功能完全一致
-- 3. 方便不同角色的权限管理和分配
-- =====================================================
