-- 为批发模块产品变体表增加库存字段
-- 增加成品库存和待产库存字段，用于从ERP接口同步数据

-- 检查字段是否已存在，避免重复添加
SET @finished_stock_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'lookah'
      AND TABLE_NAME = 'whs_product_variant'
      AND COLUMN_NAME = 'finished_stock'
);

SET @pending_stock_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'lookah'
      AND TABLE_NAME = 'whs_product_variant'
      AND COLUMN_NAME = 'pending_stock'
);

-- 添加成品库存字段
SET @sql_finished = IF(@finished_stock_exists = 0,
    'ALTER TABLE whs_product_variant ADD COLUMN finished_stock INT NOT NULL DEFAULT 0 COMMENT ''成品库存数量'' AFTER alert_stock',
    'SELECT ''finished_stock field already exists'' as message'
);

PREPARE stmt_finished FROM @sql_finished;
EXECUTE stmt_finished;
DEALLOCATE PREPARE stmt_finished;

-- 添加待产库存字段
SET @sql_pending = IF(@pending_stock_exists = 0,
    'ALTER TABLE whs_product_variant ADD COLUMN pending_stock INT NOT NULL DEFAULT 0 COMMENT ''待产库存数量'' AFTER finished_stock',
    'SELECT ''pending_stock field already exists'' as message'
);

PREPARE stmt_pending FROM @sql_pending;
EXECUTE stmt_pending;
DEALLOCATE PREPARE stmt_pending;

-- 添加库存字段的索引（用于库存查询和统计）
SET @idx_finished_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = 'lookah'
      AND TABLE_NAME = 'whs_product_variant'
      AND INDEX_NAME = 'idx_finished_stock'
);

SET @idx_pending_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = 'lookah'
      AND TABLE_NAME = 'whs_product_variant'
      AND INDEX_NAME = 'idx_pending_stock'
);

-- 创建成品库存索引
SET @sql_idx_finished = IF(@idx_finished_exists = 0,
    'CREATE INDEX idx_finished_stock ON whs_product_variant (finished_stock)',
    'SELECT ''idx_finished_stock already exists'' as message'
);

PREPARE stmt_idx_finished FROM @sql_idx_finished;
EXECUTE stmt_idx_finished;
DEALLOCATE PREPARE stmt_idx_finished;

-- 创建待产库存索引
SET @sql_idx_pending = IF(@idx_pending_exists = 0,
    'CREATE INDEX idx_pending_stock ON whs_product_variant (pending_stock)',
    'SELECT ''idx_pending_stock already exists'' as message'
);

PREPARE stmt_idx_pending FROM @sql_idx_pending;
EXECUTE stmt_idx_pending;
DEALLOCATE PREPARE stmt_idx_pending;
