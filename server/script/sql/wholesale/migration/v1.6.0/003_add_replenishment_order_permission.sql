-- =====================================================
-- Wholesale模块补货订单权限添加脚本
-- 版本: v1.6.0
-- 创建时间: 2025-08-04
-- 描述: 为wholesale模块添加补货订单生成权限配置
-- 功能: 支持一键生成补货订单，自动检测缺货商品并创建补货订单
-- =====================================================

-- 开始事务，确保原子性
START TRANSACTION;

-- 1. 补货订单生成权限添加
-- 注意：使用7042的menu_id，避免与之前脚本冲突

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
                        `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`,
                        `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7042, '补货订单生成', 6201, 21, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:order:replenishment:generate', '#', 103, 1, NOW(), 1,
        NOW(), '补货订单生成权限，支持一键生成补货订单，自动检测缺货商品并创建草稿状态的补货订单');

-- =====================================================
-- 权限添加总结
-- =====================================================
-- 新增功能权限: 1个
-- 涵盖模块:
--   - 订单管理: 1个权限 (补货订单生成)
--
-- 说明:
-- 1. 补货订单生成权限归属于订单管理模块 (parent_id = 6201)
-- 2. 权限码: wholesale:order:replenishment:generate
-- 3. 功能特性:
--    - 自动检测洛杉矶仓缺货预警商品
--    - 查找芝加哥仓对应箱装库存
--    - 使用指定人的默认地址
--    - 创建为草稿状态，需人工确认
-- 4. 业务逻辑:
--    - 缺货检测：洛杉矶仓单品(packaging_type=0)
--    - 库存匹配：芝加哥仓箱装(packaging_type=2)
--    - 推荐数量：每个产品1箱
--    - 订单价格：0元（补货订单）
-- =====================================================

-- 提交事务
COMMIT;

-- 验证新增权限
SELECT menu_id,
       menu_name,
       perms,
       remark
FROM sys_menu
WHERE menu_id = 7042;

-- 显示执行结果
SELECT 'v1.6.0 补货订单权限添加完成！新增1个权限配置：补货订单生成权限。' as 'Migration Result';
