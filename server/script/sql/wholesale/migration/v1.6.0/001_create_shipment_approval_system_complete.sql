-- =====================================================
-- 发货审批系统完整建表脚本 (v1.6.0)
-- 版本: v1.6.0
-- 创建时间: 2025-08-02
-- 描述: 为wholesale模块添加完整的发货审批功能，包括审核流程、权限控制和系统配置
-- 特性: 支持事件溯源模式，记录状态管理，完整的审批操作追踪
-- =====================================================

-- 开始事务，确保原子性
START TRANSACTION;

-- 1. 创建发货审批表（包含所有必要字段）
CREATE TABLE `whs_order_shipment_approval` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '审核ID',
    `order_id` bigint NOT NULL COMMENT '订单ID',
    `applicant_id` bigint NOT NULL COMMENT '申请人ID',
    `applicant_name` varchar(100) NOT NULL COMMENT '申请人姓名',
    `application_reason` text NOT NULL COMMENT '申请理由',
    `approval_status` tinyint NOT NULL DEFAULT '0' COMMENT '审核状态(0-无需审批 1-待审批 2-审批通过 3-审批拒绝 4-已撤销)',
    `operation_type` varchar(20) NOT NULL DEFAULT 'SUBMIT' COMMENT '操作类型：SUBMIT-提交申请，APPROVE-审批通过，REJECT-审批拒绝，CANCEL-撤销申请',
    `operation_sequence` int(11) NOT NULL DEFAULT 1 COMMENT '操作序号，同一订单的操作按时间顺序递增',
    `record_status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '记录状态：ACTIVE-有效，SUPERSEDED-已被覆盖，ARCHIVED-已归档',
    `approver_id` bigint DEFAULT NULL COMMENT '审核人ID',
    `approver_name` varchar(100) DEFAULT NULL COMMENT '审核人姓名',
    `approval_time` datetime DEFAULT NULL COMMENT '审核时间',
    `approval_comment` text COMMENT '审核意见',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
    `create_by` bigint DEFAULT NULL COMMENT '创建者',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` bigint DEFAULT NULL COMMENT '更新者',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_applicant_id` (`applicant_id`),
    KEY `idx_approver_id` (`approver_id`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_order_operation_time` (`order_id`, `operation_sequence`, `create_time`),
    KEY `idx_operation_type_time` (`operation_type`, `create_time`),
    KEY `idx_record_status` (`record_status`),
    KEY `idx_order_record_status` (`order_id`, `record_status`),
    CONSTRAINT `chk_record_status` CHECK (`record_status` IN ('ACTIVE', 'SUPERSEDED', 'ARCHIVED'))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单发货审批记录表，采用事件溯源模式记录所有审批操作，通过record_status控制记录有效性';

-- 2. 创建发货审批操作类型字典
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES ('发货审批操作类型', 'whs_shipment_approval_operation_type', 103, 1, NOW(), 1, NOW(), '发货审批操作类型字典');

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1, '提交申请', 'SUBMIT', 'whs_shipment_approval_operation_type', '', 'primary', 'Y', 103, 1, NOW(), 1, NOW(), '提交发货审批申请'),
(2, '审批通过', 'APPROVE', 'whs_shipment_approval_operation_type', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '审批通过操作'),
(3, '审批拒绝', 'REJECT', 'whs_shipment_approval_operation_type', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), '审批拒绝操作'),
(4, '撤销申请', 'CANCEL', 'whs_shipment_approval_operation_type', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '撤销审批申请');

-- 3. 创建发货审批记录状态字典
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES ('发货审批记录状态', 'whs_shipment_approval_record_status', 103, 1, NOW(), 1, NOW(), '发货审批记录状态字典');

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1, '有效', 'ACTIVE', 'whs_shipment_approval_record_status', '', 'success', 'Y', 103, 1, NOW(), 1, NOW(), '当前有效的审批记录'),
(2, '已被覆盖', 'SUPERSEDED', 'whs_shipment_approval_record_status', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '被新操作覆盖的记录'),
(3, '已归档', 'ARCHIVED', 'whs_shipment_approval_record_status', '', 'default', 'N', 103, 1, NOW(), 1, NOW(), '已归档的记录');

-- 4. 为订单表添加发货审批状态字段
ALTER TABLE `whs_order`
ADD COLUMN `shipment_approval_status` TINYINT(1) NOT NULL DEFAULT 0
COMMENT '发货审批状态：0-无需审批,1-待审批,2-审批通过,3-审批拒绝,4-已撤销' 
AFTER `shipment_status`;

-- 5. 为订单表添加审批状态相关索引
CREATE INDEX `idx_shipment_approval_status` ON `whs_order` (`shipment_approval_status`);
CREATE INDEX `idx_order_shipment_approval` ON `whs_order` (`order_status`, `shipment_status`, `shipment_approval_status`);

-- 6. 添加发货管理权限菜单
INSERT INTO `sys_menu` (
    `menu_id`,
    `menu_name`,
    `parent_id`,
    `order_num`,
    `path`,
    `component`,
    `query_param`,
    `is_frame`,
    `is_cache`,
    `menu_type`,
    `visible`,
    `status`,
    `perms`,
    `icon`,
    `create_dept`,
    `create_by`,
    `create_time`,
    `update_by`,
    `update_time`,
    `remark`
) VALUES (
    7037,
    '发货管理',
    6200,
    15,
    'shipment-management',
    'wholesale/shipment-management/index',
    '',
    0,
    0,
    'C',
    '1',
    '1',
    'wholesale:shipment:list',
    'lucide:truck',
    103,
    1,
    NOW(),
    1,
    NOW(),
    '发货管理页面，显示待发货订单列表'
);

-- 7. 添加发货审批管理页面
INSERT INTO `sys_menu` (
    `menu_id`,
    `menu_name`,
    `parent_id`,
    `order_num`,
    `path`,
    `component`,
    `query_param`,
    `is_frame`,
    `is_cache`,
    `menu_type`,
    `visible`,
    `status`,
    `perms`,
    `icon`,
    `create_dept`,
    `create_by`,
    `create_time`,
    `update_by`,
    `update_time`,
    `remark`
) VALUES (
    7038,
    '发货审批',
    6200,
    16,
    'shipment-approval',
    'wholesale/shipment-approval/index',
    '',
    0,
    0,
    'C',
    '1',
    '1',
    'wholesale:shipment:approve',
    'lucide:clipboard-check',
    103,
    1,
    NOW(),
    1,
    NOW(),
    '发货审批管理页面，管理员审核发货申请'
);

-- 8. 添加发货审批相关权限按钮
INSERT INTO `sys_menu` (
    `menu_id`,
    `menu_name`,
    `parent_id`,
    `order_num`,
    `path`,
    `component`,
    `query_param`,
    `is_frame`,
    `is_cache`,
    `menu_type`,
    `visible`,
    `status`,
    `perms`,
    `icon`,
    `create_dept`,
    `create_by`,
    `create_time`,
    `update_by`,
    `update_time`,
    `remark`
) VALUES
(7039, '发货审批申请', 6201, 13, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:shipment:request', '#', 103, 1, NOW(), 1, NOW(), '发货审批申请权限'),
(7040, '撤回审批申请', 6201, 14, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:shipment:cancel', '#', 103, 1, NOW(), 1, NOW(), '撤回发货审批申请权限');

-- 9. 添加系统配置参数
INSERT INTO `sys_config` (
    `config_id`,
    `config_name`,
    `config_key`,
    `config_value`,
    `config_type`,
    `remark`,
    `create_dept`,
    `create_by`,
    `create_time`,
    `update_by`,
    `update_time`
) VALUES
(1001, '发货审批启用状态', 'wholesale.shipment.approval.enabled', 'true', 'Y', '是否启用发货审批功能，true-启用，false-禁用', 103, 1, NOW(), 1, NOW()),
(1003, '自动发起审核申请', 'wholesale.shipment.approval.auto_request', 'false', 'Y', '订单状态变为处理中时是否自动发起审核申请，true-自动发起，false-手动发起', 103, 1, NOW(), 1, NOW()),
(1004, '自动审批通过', 'wholesale.shipment.approval.auto_approve', 'false', 'Y', '发起审核申请后是否自动审批通过，true-自动通过，false-需要人工审批', 103, 1, NOW(), 1, NOW());

-- 提交事务
COMMIT;

-- =====================================================
-- 功能特性说明
-- =====================================================
-- 
-- 【事件溯源模式】
-- - 每个审批操作都会创建新的记录，而不是更新现有记录
-- - operation_type字段记录操作类型：SUBMIT、APPROVE、REJECT、CANCEL
-- - operation_sequence字段确保同一订单的操作顺序
-- 
-- 【记录状态管理】  
-- - record_status字段控制记录的有效性
-- - ACTIVE: 当前有效的记录，用于业务判断
-- - SUPERSEDED: 被新操作覆盖的历史记录
-- - ARCHIVED: 已归档的记录
-- 
-- 【智能按钮显示】
-- - 后端只返回ACTIVE状态的记录用于业务判断
-- - 前端根据最新ACTIVE记录的状态显示相应操作按钮
-- - 解决了"撤销后还显示审批按钮"的问题
-- 
-- =====================================================
-- 权限说明
-- =====================================================
-- wholesale:shipment:list - 发货管理列表权限
-- wholesale:shipment:request - 发货审批申请权限
-- wholesale:shipment:cancel - 撤回审批申请权限
-- wholesale:shipment:approve - 发货审批管理权限
-- =====================================================