-- =====================================================
-- 订单表添加补货单标识字段
-- 版本: v1.6.0
-- 创建时间: 2025-08-04
-- 描述: 为whs_order表添加is_replenishment字段，用于标识补货订单
-- 优化: 替代通过订单号模糊匹配的方式，提供更优雅的补货单识别机制
-- =====================================================

-- 开始事务，确保原子性
START TRANSACTION;

-- 1. 为whs_order表添加is_replenishment字段
ALTER TABLE `whs_order` 
ADD COLUMN `is_replenishment` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为补货订单：0-否，1-是' 
AFTER `order_status`;

-- 2. 为现有的补货订单设置标识（通过订单号识别）
UPDATE `whs_order` 
SET `is_replenishment` = 1 
WHERE `internal_order_no` LIKE '%#2363%' 
  AND `del_flag` = '0';

-- 3. 为is_replenishment字段添加索引，提高查询性能
CREATE INDEX `idx_whs_order_is_replenishment` ON `whs_order` (`is_replenishment`);

-- 4. 创建复合索引，优化补货订单状态查询
CREATE INDEX `idx_whs_order_replenishment_status` ON `whs_order` (`is_replenishment`, `order_status`, `del_flag`);

-- =====================================================
-- 字段说明
-- =====================================================
-- is_replenishment字段说明:
-- - 类型: tinyint(1)
-- - 默认值: 0 (非补货订单)
-- - 取值: 0-普通订单, 1-补货订单
-- - 用途: 
--   1. 标识补货订单，替代订单号模糊匹配
--   2. 防止重复生成补货订单时的查询优化
--   3. 补货订单统计和报表功能
--   4. 订单列表筛选和分类显示
--
-- 索引说明:
-- - idx_whs_order_is_replenishment: 单字段索引，用于快速筛选补货订单
-- - idx_whs_order_replenishment_status: 复合索引，用于补货订单状态查询优化
-- =====================================================

-- 提交事务
COMMIT;

-- 验证字段添加结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'whs_order' 
  AND COLUMN_NAME = 'is_replenishment';

-- 验证现有补货订单标识设置
SELECT 
    COUNT(*) as replenishment_order_count,
    GROUP_CONCAT(DISTINCT order_status) as order_statuses
FROM whs_order 
WHERE is_replenishment = 1 
  AND del_flag = '0';

-- 显示执行结果
SELECT 'v1.6.0 补货订单标识字段添加完成！已为whs_order表添加is_replenishment字段并设置现有补货订单标识。' as 'Migration Result';
