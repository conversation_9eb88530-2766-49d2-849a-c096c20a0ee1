-- ----------------------------
-- 修复客户分配给业务员的权限机制BUG
-- 问题描述：业务代表创建的客户会自动绑定到该业务名下，当管理员将这个客户分配给了别人时，
--          创建人依旧可以通过数据权限中的"本人数据"条件读取到该客户
-- 解决方案：修改数据权限逻辑，仅基于sales_user_id判断客户访问权限，移除基于create_by的权限判断
-- 创建时间：2025-08-02
-- 版本：v1.6.0
-- ----------------------------

-- 注意：此迁移脚本主要是代码层面的修改，数据库结构无需变更
-- 主要修改内容：
-- 1. DataScopeType.SALESPERSON_OR_SELF 改为 SALESPERSON_ONLY
-- 2. 权限SQL模板从 "#{#salespersonName} = #{#user.userId} OR #{#userName} = #{#user.userId}"
--    修改为 "#{#salespersonName} = #{#user.userId}"
-- 3. 更新相关注释和文档描述

-- 查看当前使用数据权限"7"的角色
SELECT role_id, role_name, role_key, data_scope, remark
FROM sys_role
WHERE data_scope = '7' AND del_flag = '0';

-- 更新使用数据权限"7"的角色备注信息，反映新的权限名称
-- 将"业务代表或本人数据权限"更新为"业务代表数据权限"
UPDATE sys_role
SET remark = REPLACE(
    REPLACE(remark, '业务代表或本人数据权限', '业务代表数据权限'),
    '[系统自动更新：支持业务代表或本人数据权限]',
    '[系统自动更新：支持业务代表数据权限，仅基于sales_user_id判断]'
),
    update_time = NOW(),
    update_by = 1
WHERE data_scope = '7'
  AND del_flag = '0'
  AND (remark LIKE '%业务代表或本人数据权限%' OR remark LIKE '%业务代表或本人%');

-- 验证更新结果
SELECT role_id, role_name, role_key, data_scope, remark
FROM sys_role
WHERE data_scope = '7' AND del_flag = '0';

-- 实际的权限逻辑修改在以下Java文件中：
-- - DataScopeType.java: 修改枚举定义和SQL模板
-- - SysRoleVo.java: 更新Excel导出描述
-- - SysRoleBo.java: 更新业务对象注释
-- - SysRole.java: 更新实体类注释

-- 影响范围：
-- - 所有使用"业务代表数据权限"的客户查询将仅基于sales_user_id判断
-- - 原创建者将无法访问已分配给他人的客户
-- - 客户的访问权限完全由当前负责的业务员(sales_user_id)决定

-- 验证方法：
-- 1. 业务代表A创建客户X
-- 2. 管理员将客户X分配给业务代表B
-- 3. 业务代表A应无法再查看客户X
-- 4. 业务代表B可以正常查看和操作客户X

SELECT 'Customer assignment permission bug fix migration completed' as status;
