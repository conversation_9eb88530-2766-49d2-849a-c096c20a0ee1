-- 扩展whs_member表字段以支持国际销售团队功能
-- 新增字段：公司类型、店面数量、客户来源、公司名称

-- 公司类型枚举：批发、零售店、连锁店、cash&carry
ALTER TABLE `whs_member` ADD COLUMN `company_type` varchar(50) DEFAULT NULL COMMENT '公司类型（wholesale,retail,chain,cash_carry）' AFTER `last_name`;

-- 店面数量
ALTER TABLE `whs_member` ADD COLUMN `store_count` int DEFAULT 1 COMMENT '店面数量' AFTER `company_type`;

-- 客户来源
ALTER TABLE `whs_member` ADD COLUMN `customer_source` varchar(200) DEFAULT NULL COMMENT '客户来源' AFTER `store_count`;

-- 公司名称（独立字段）
ALTER TABLE `whs_member` ADD COLUMN `company_name` varchar(200) DEFAULT NULL COMMENT '公司名称' AFTER `customer_source`;