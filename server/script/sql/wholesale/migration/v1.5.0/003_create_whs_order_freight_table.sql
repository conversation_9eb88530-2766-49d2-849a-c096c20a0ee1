-- 创建订单运费信息表（与包裹关联）
CREATE TABLE `whs_order_freight` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `package_id` bigint NOT NULL COMMENT '包裹ID，关联whs_logistics_package表',
  `order_id` bigint NOT NULL COMMENT '订单ID（冗余字段，便于查询）',
  `estimated_freight` decimal(10,2) DEFAULT NULL COMMENT '预估运费',
  `actual_freight` decimal(10,2) DEFAULT NULL COMMENT '实际运费',
  `freight_currency` varchar(10) DEFAULT 'USD' COMMENT '运费币种',
  `sync_time` datetime DEFAULT NULL COMMENT '运费同步时间',
  `sync_status` char(1) DEFAULT '0' COMMENT '同步状态（0未同步 1已同步）',
  `provider_type` varchar(50) DEFAULT NULL COMMENT '提供商类型（如：haotong、other）',
  `provider_tracking_no` varchar(100) DEFAULT NULL COMMENT '提供商跟踪号',
  `provider_order_no` varchar(100) DEFAULT NULL COMMENT '提供商订单号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) NOT NULL DEFAULT '0' COMMENT '删除标志',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_package_id` (`package_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_sync_status` (`sync_status`),
  KEY `idx_sync_time` (`sync_time`),
  KEY `idx_provider_type` (`provider_type`)
  -- 注意：按照项目规范，禁止使用外键约束，在应用层维护数据一致性
  -- 参考：.rules/database.md - 禁止外键约束规范
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单运费信息表（与包裹关联）';
