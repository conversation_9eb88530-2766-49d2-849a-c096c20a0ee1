-- 批发管理相关菜单国际化迁移
-- 将菜单名称更新为 i18n key 格式
-- 注意：只更新菜单类型为 'M'(目录) 和 'C'(菜单) 的项，排除 'F'(权限按钮)

-- 更新批发管理主菜单（只更新非权限类型的菜单）
UPDATE sys_menu SET menu_name = 'menu.wholesale.root' WHERE menu_id = 6000 AND menu_type != 'F';

-- 更新会员管理菜单（只更新非权限类型的菜单）
UPDATE sys_menu SET menu_name = 'menu.wholesale.member.root' WHERE menu_id = 6300 AND menu_type != 'F';
UPDATE sys_menu SET menu_name = 'menu.wholesale.member.list' WHERE menu_id = 6301 AND menu_type != 'F';

-- 更新订单管理菜单（只更新非权限类型的菜单）
UPDATE sys_menu SET menu_name = 'menu.wholesale.order.root' WHERE menu_id = 6200 AND menu_type != 'F';
UPDATE sys_menu SET menu_name = 'menu.wholesale.order.list' WHERE menu_id = 6201 AND menu_type != 'F';

-- 更新产品管理菜单（只更新非权限类型的菜单）
UPDATE sys_menu SET menu_name = 'menu.wholesale.product.root' WHERE menu_id = 6100 AND menu_type != 'F';
UPDATE sys_menu SET menu_name = 'menu.wholesale.product.list' WHERE menu_id = 6101 AND menu_type != 'F';
UPDATE sys_menu SET menu_name = 'menu.wholesale.product.category' WHERE menu_id = 6102 AND menu_type != 'F';
UPDATE sys_menu SET menu_name = 'menu.wholesale.product.series' WHERE menu_id = 6103 AND menu_type != 'F';
UPDATE sys_menu SET menu_name = 'menu.wholesale.product.attribute' WHERE menu_id = 6104 AND menu_type != 'F';
UPDATE sys_menu SET menu_name = 'menu.wholesale.product.variant' WHERE menu_id = 6105 AND menu_type != 'F';

-- 更新库存管理菜单（只更新非权限类型的菜单）
UPDATE sys_menu SET menu_name = 'menu.wholesale.stock.root' WHERE menu_id = 6400 AND menu_type != 'F';
UPDATE sys_menu SET menu_name = 'menu.wholesale.stock.list' WHERE menu_id = 6401 AND menu_type != 'F';
UPDATE sys_menu SET menu_name = 'menu.wholesale.stock.summary' WHERE menu_id = 6402 AND menu_type != 'F';
UPDATE sys_menu SET menu_name = 'menu.wholesale.stock.warehouse' WHERE menu_id = 6404 AND menu_type != 'F';
UPDATE sys_menu SET menu_name = 'menu.wholesale.stock.inbound' WHERE menu_id = 6405 AND menu_type != 'F';
