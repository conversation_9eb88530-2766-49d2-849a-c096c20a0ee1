-- =====================================================
-- Wholesale模块运费同步权限添加脚本
-- 版本: v1.5.7
-- 创建时间: 2025-07-28
-- 描述: 为wholesale模块添加运费同步权限配置
-- 功能: 支持后台手动同步海外仓运费信息
-- =====================================================

-- 开始事务，确保原子性
START TRANSACTION;

-- 1. 运费同步权限添加
-- 注意：使用7030范围的menu_id，避免与之前脚本冲突

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
                        `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`,
                        `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7036, '运费同步', 6201, 20, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:order:freight:sync', '#', 103, 1, NOW(), 1,
        NOW(), '订单运费同步权限，支持同步海外仓运费信息');

-- =====================================================
-- 权限添加总结
-- =====================================================
-- 新增功能权限: 1个
-- 涵盖模块:
--   - 订单管理: 1个权限 (运费同步)
--
-- 说明:
-- 1. 运费同步权限归属于订单管理模块 (parent_id = 6201)
-- 2. 权限码: wholesale:order:freight:sync
-- 3. 支持同步所有海外仓运费和单个订单运费
-- 4. 异步执行，避免前端请求超时
-- =====================================================

-- 提交事务
COMMIT;

-- 验证新增权限
SELECT menu_id,
       menu_name,
       perms,
       remark
FROM sys_menu
WHERE menu_id = 7036;

-- 显示执行结果
SELECT 'v1.5.7 运费同步权限添加完成！新增1个权限配置：运费同步权限。' as 'Migration Result';
