-- =====================================================
-- 敏感信息查看权限配置脚本
-- 版本: v1.5.6
-- 创建时间: 2025-07-28
-- 描述: 为wholesale模块添加敏感信息查看权限，支持分级权限控制
-- 权限说明：
--   - wholesale:member:sensitive:view: 会员敏感信息查看（登录IP、位置、时间、注册时间等）
--   - wholesale:order:sensitive:view: 订单敏感信息查看（会员邮箱、收货电话、收货邮箱）
-- =====================================================

-- 开始事务，确保原子性
START TRANSACTION;

-- 1. 会员敏感信息查看权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7034, '会员敏感信息查看', 6301, 10, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:member:sensitive:view', '#', 103, 1, NOW(), 1, NOW(), '查看会员敏感信息权限（登录IP、登录位置、最后登录时间、注册时间）');

-- 2. 订单敏感信息查看权限（暂时留空，为后续扩展预留）
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7035, '订单敏感信息查看', 6201, 10, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:order:sensitive:view', '#', 103, 1, NOW(), 1, NOW(), '查看订单敏感信息权限（暂时留空，为后续扩展预留）');

-- =====================================================
-- 权限配置总结
-- =====================================================
-- 新增权限: 2个
-- 权限详情:
--   1. wholesale:member:sensitive:view (菜单ID: 7034)
--      - 控制会员列表中敏感字段的显示
--      - 敏感字段包括：loginIp, loginLocation, loginDate, registerTime
--      - 隶属于会员管理模块 (parent_id: 6301)
--
--   2. wholesale:order:sensitive:view (菜单ID: 7035)  
--      - 控制订单列表中敏感字段的显示（暂时留空）
--      - 暂时不过滤任何敏感字段，为后续扩展预留
--      - 隶属于订单管理模块 (parent_id: 6201)
--
-- 使用说明:
--   - 管理员可通过系统管理->角色管理为不同角色分配相应权限
--   - 拥有权限的用户可在前端列表中查看对应的敏感信息字段
--   - 不具备权限的用户将无法看到敏感信息列
--   - 后端会根据权限过滤敏感数据，确保数据安全
-- =====================================================

-- 提交事务
COMMIT;