# Migration v1.2.1 - Wholesale权限补充

## 概述
本版本是对v1.2.0权限细化的补充，主要添加wholesale模块缺失的地址管理权限配置和订单查看权限。

## 主要变更

### wholesale_address_permission_supplement.sql
**目的**: 补充wholesale模块缺失的权限配置

**新增权限统计**:
- **新增功能权限**: 6个

**权限分类**:
1. **地址管理权限** (5个)
   - wholesale:address:list - 地址列表
   - wholesale:address:query - 地址查询
   - wholesale:address:add - 地址新增
   - wholesale:address:edit - 地址修改
   - wholesale:address:remove - 地址删除

2. **订单管理权限** (1个)
   - wholesale:order:view - 订单查看

## 权限依赖关系说明

基于SaToken OR机制，这些权限支持以下依赖关系：

### 地址管理权限依赖
- `wholesale:address:list` 支持OR权限：
  - 基础权限：`wholesale:address:list`
  - 业务权限：`wholesale:order:manual:create` (手动创建订单需要选择收货地址)

- `wholesale:address:query` 支持OR权限：
  - 基础权限：`wholesale:address:query`
  - 业务权限：`wholesale:order:manual:create` (手动创建订单需要查看地址详情)

### 订单查看权限依赖
- `wholesale:order:view` 支持OR权限：
  - 基础权限：`wholesale:order:view`
  - 业务权限：`wholesale:order:edit` (订单编辑需要查看发货库存等信息)



## 执行说明

### 前置条件
1. 必须先执行 v1.2.0/wholesale_permission_enhancement.sql
2. 备份现有sys_menu表数据
3. 确认menu_id不冲突 (使用7023-7028范围)

### 执行命令
```sql
source server/script/sql/migration/v1.2.1/wholesale_address_permission_supplement.sql
```

### 执行后验证
1. 检查新增的6个权限是否正确创建
2. 验证地址管理功能的权限控制
3. 验证订单发货功能的权限控制
4. 测试手动创建订单时的地址权限依赖
5. 测试订单编辑时的查看权限依赖

### 回滚方案
如需回滚，删除新增的权限记录：
```sql
DELETE FROM sys_menu WHERE menu_id BETWEEN 7023 AND 7028;
```

## 相关代码变更

### 已更新的Controller权限注解
- `WhsShippingAddressController.java` - 地址管理权限细化，支持OR权限
- `WhsOrderShipmentController.java` - 订单发货管理权限细化，支持OR权限

### 权限注解示例
```java
// 地址列表 - 支持OR权限
@SaCheckPermission(value = {
    "wholesale:address:list",           // 基础权限
    "wholesale:order:manual:create"     // 手动创建订单需要选择地址
}, mode = SaMode.OR)

// 发货库存查询 - 支持OR权限
@SaCheckPermission(value = {
    "wholesale:order:view",             // 基础订单查看权限
    "wholesale:order:edit"              // 订单编辑需要查看发货库存
}, mode = SaMode.OR)

// 地址详情 - 支持OR权限
@SaCheckPermission(value = {
    "wholesale:address:query",          // 基础权限
    "wholesale:order:manual:create"     // 手动创建订单需要查看地址详情
}, mode = SaMode.OR)
```

## 业务场景说明

### 手动创建订单完整权限依赖
分配 `wholesale:order:manual:create` 权限后，用户自动获得：
- ✅ 查看会员列表 (选择客户)
- ✅ 查看产品列表 (选择商品)
- ✅ 查看产品详情 (查看价格信息)
- ✅ 查看库存状态 (确认库存)
- ✅ 查看仓库列表 (选择发货仓库)
- ✅ 查看地址列表 (选择收货地址) ⭐ **新增**
- ✅ 查看地址详情 (确认地址信息) ⭐ **新增**

## 注意事项

1. **权限兼容性**: 现有角色可能需要重新分配地址管理权限
2. **OR权限机制**: 基于SaToken的OR模式实现权限依赖
3. **业务完整性**: 确保手动创建订单的完整业务流程权限覆盖
4. **测试验证**: 执行后需要测试地址管理相关功能

## 影响范围
- 补充了wholesale模块地址管理的权限配置
- 完善了手动创建订单的权限依赖关系
- 提升了权限管理的完整性和精细度
