-- =====================================================
-- Wholesale模块地址管理权限补充脚本
-- 版本: v1.2.1
-- 创建时间: 2025-07-04
-- 描述: 补充wholesale模块缺失的地址管理权限配置
-- 注意: 这是对v1.2.0权限增强脚本的补充
-- =====================================================

-- 开始事务，确保原子性
START TRANSACTION;

-- 1. 地址管理权限补充
-- 注意：使用7023-7030范围的menu_id，避免与v1.2.0脚本冲突

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
                        `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`,
                        `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7023, '地址列表', 6301, 10, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:address:list', '#', 103, 1, NOW(), 1,
        NOW(), '会员地址列表权限');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
                        `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`,
                        `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7024, '地址查询', 6301, 11, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:address:query', '#', 103, 1, NOW(), 1,
        NOW(), '会员地址详情查询权限');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
                        `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`,
                        `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7025, '地址新增', 6301, 12, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:address:add', '#', 103, 1, NOW(), 1,
        NOW(), '新增会员地址权限');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
                        `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`,
                        `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7026, '地址修改', 6301, 13, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:address:edit', '#', 103, 1, NOW(), 1,
        NOW(), '修改会员地址权限');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
                        `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`,
                        `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (7027, '地址删除', 6301, 14, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:address:remove', '#', 103, 1, NOW(), 1,
        NOW(), '删除会员地址权限');

-- 2. 订单权限补充
-- 添加代码中使用但数据库中缺失的订单权限

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
                        `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`,
                        `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (6202, '订单查看', 6201, 15, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:order:view', '#', 103, 1, NOW(), 1,
        NOW(), '订单查看权限');

-- =====================================================
-- 权限补充总结
-- =====================================================
-- 新增功能权限: 6个
-- 涵盖模块:
--   - 地址管理: 5个权限 (列表、查询、新增、修改、删除)
--   - 订单管理: 1个权限 (查看)
--
-- 说明:
-- 1. 地址管理权限归属于会员管理模块 (parent_id = 6301)
-- 2. 订单查看权限归属于订单管理模块 (parent_id = 6201)
-- 3. 修复代码中使用但数据库中缺失的 wholesale:order:view 权限
-- =====================================================

-- 提交事务
COMMIT;

-- 验证新增权限
SELECT menu_id,
       menu_name,
       perms,
       remark
FROM sys_menu
WHERE menu_id BETWEEN 7023 AND 7028
ORDER BY menu_id;

-- 显示执行结果
SELECT 'v1.2.1 权限补充完成！新增6个权限配置：5个地址管理权限 + 1个订单查看权限。' as 'Migration Result';
