-- 删除违反项目规范的外键约束
-- 根据项目规范，禁止使用外键约束，在应用层维护数据一致性
-- 参考：.rules/database.md - 禁止外键约束规范

-- ========================================
-- 删除 whs_order_file 表的外键约束
-- ========================================

-- 检查并删除 fk_whs_order_file_order_id 外键约束
SET @sql = (
    SELECT IF(
        COUNT(*) > 0,
        'ALTER TABLE `whs_order_file` DROP FOREIGN KEY `fk_whs_order_file_order_id`;',
        'SELECT "Foreign key fk_whs_order_file_order_id does not exist" AS message;'
    )
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'whs_order_file'
    AND CONSTRAINT_NAME = 'fk_whs_order_file_order_id'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并删除 fk_whs_order_file_oss_id 外键约束
SET @sql = (
    SELECT IF(
        COUNT(*) > 0,
        'ALTER TABLE `whs_order_file` DROP FOREIGN KEY `fk_whs_order_file_oss_id`;',
        'SELECT "Foreign key fk_whs_order_file_oss_id does not exist" AS message;'
    )
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'whs_order_file'
    AND CONSTRAINT_NAME = 'fk_whs_order_file_oss_id'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- 删除 whs_order_freight 表的外键约束
-- ========================================

-- 检查并删除 fk_freight_package 外键约束
SET @sql = (
    SELECT IF(
        COUNT(*) > 0,
        'ALTER TABLE `whs_order_freight` DROP FOREIGN KEY `fk_freight_package`;',
        'SELECT "Foreign key fk_freight_package does not exist" AS message;'
    )
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'whs_order_freight'
    AND CONSTRAINT_NAME = 'fk_freight_package'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并删除 fk_freight_order 外键约束
SET @sql = (
    SELECT IF(
        COUNT(*) > 0,
        'ALTER TABLE `whs_order_freight` DROP FOREIGN KEY `fk_freight_order`;',
        'SELECT "Foreign key fk_freight_order does not exist" AS message;'
    )
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'whs_order_freight'
    AND CONSTRAINT_NAME = 'fk_freight_order'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- 验证清理结果
-- ========================================

-- 确保索引仍然存在（用于查询性能）
-- whs_order_file 表索引：
-- KEY `idx_order_id` (`order_id`)
-- KEY `idx_oss_id` (`oss_id`)
-- KEY `idx_file_type` (`file_type`)

-- whs_order_freight 表索引：
-- UNIQUE KEY `uk_package_id` (`package_id`)
-- KEY `idx_order_id` (`order_id`)
-- KEY `idx_sync_status` (`sync_status`)
-- KEY `idx_sync_time` (`sync_time`)
-- KEY `idx_provider_type` (`provider_type`)

SELECT 'whs_order_file 和 whs_order_freight 表外键约束删除完成，保留索引以维持查询性能' AS result;
