-- 创建订单文件关联表（兼容版本）
CREATE TABLE IF NOT EXISTS `whs_order_file` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件关联ID',
    `order_id` bigint NOT NULL COMMENT '订单ID',
    `oss_id` bigint NOT NULL COMMENT 'OSS文件ID',
    `file_type` varchar(20) NOT NULL DEFAULT 'INVOICE' COMMENT '文件类型：INVOICE-发票文件, CONTRACT-合同文件, OTHER-其他文件',
    `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
    `file_size` bigint DEFAULT NULL COMMENT '文件大小（字节）',
    `content_type` varchar(100) DEFAULT NULL COMMENT '文件MIME类型',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
    `create_by` bigint DEFAULT NULL COMMENT '创建者',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` bigint DEFAULT NULL COMMENT '更新者',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_file_type` (`file_type`),
    KEY `idx_oss_id` (`oss_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='批发订单文件关联表';

-- 检查并删除订单表中的单个INVOICE文件字段
-- 先检查字段是否存在，如果存在则删除
SET @sql = (
    SELECT IF(
        COUNT(*) > 0,
        'ALTER TABLE `whs_order` DROP COLUMN `invoice_oss_id`;',
        'SELECT "Column invoice_oss_id does not exist" AS message;'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'whs_order' 
    AND COLUMN_NAME = 'invoice_oss_id'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
