-- =====================================================
-- Wholesale模块发货权限细化增强脚本
-- 版本: v1.5.7
-- 创建时间: 2025-01-30
-- 描述: 为wholesale模块添加专门的发货权限，细化发货操作权限控制
-- 注意: 新增 wholesale:order:shipment 权限，用于替代过于泛化的 wholesale:order:edit 权限
-- =====================================================

-- 开始事务，确保原子性
START TRANSACTION;

-- 1. 添加订单发货权限
-- 使用7036作为menu_id，确保不与现有权限冲突
INSERT INTO `sys_menu` (
    `menu_id`,
    `menu_name`,
    `parent_id`,
    `order_num`,
    `path`,
    `component`,
    `query_param`,
    `is_frame`,
    `is_cache`,
    `menu_type`,
    `visible`,
    `status`,
    `perms`,
    `icon`,
    `create_dept`,
    `create_by`,
    `create_time`,
    `update_by`,
    `update_time`,
    `remark`
) VALUES (
    7036,
    '订单发货',
    6201,
    12,
    '',
    '',
    '',
    0,
    0,
    'F',
    '1',
    '1',
    'wholesale:order:shipment',
    '#',
    103,
    1,
    NOW(),
    1,
    NOW(),
    '订单发货权限，包括获取发货方案、确认发货等操作'
);



-- 提交事务
COMMIT;

-- =====================================================
-- 权限说明
-- =====================================================
-- wholesale:order:shipment - 订单发货权限
--   - 控制发货按钮显示
--   - 控制发货抽屉打开
--   - 控制发货相关的所有操作（包括查看和执行）
--   - 包括发货库存查询、发货方案查看、确认发货等
-- =====================================================
