# v1.5.7 批发发货权限细化增强

## 概述

本次更新为批发模块添加了专门的发货权限，解决了 `wholesale:order:edit` 权限过于泛化的问题。

## 问题背景

之前的发货功能使用 `wholesale:order:edit` 权限控制，但这个权限包含了订单编辑的所有操作，权限粒度过粗。当用户没有发货权限时，发货按钮和相关操作应该完全不可见和不可执行。

## 解决方案

### 1. 新增权限

- **wholesale:order:shipment** - 订单发货权限
  - 控制发货按钮显示
  - 控制发货抽屉打开
  - 控制发货相关的所有操作（包括查看和执行）
  - 包括发货库存查询、发货方案查看、确认发货等

### 2. 权限应用范围

#### 后端API权限更新

**WhsOrderShipmentController.java**
- `getOrGenerateShipmentPlans()` - 改为 `wholesale:order:shipment`
- `prepareCustomPackagePlan()` - 改为 `wholesale:order:shipment`
- `submitCustomShipmentPlan()` - 改为 `wholesale:order:shipment`
- `previewWarehouseAssignment()` - 改为 `wholesale:order:shipment`
- `confirmShipment()` - 改为 `wholesale:order:shipment`
- `getWarehouseSignatureSupport()` - 改为 `wholesale:order:shipment`
- `getShipmentStocks()` - 支持OR权限：`wholesale:order:view` OR `wholesale:order:shipment`

**WhsLogisticsController.java**
- `getLogisticsList()` - 改为支持OR权限：`wholesale:logistics:list` OR `wholesale:order:shipment`

#### 前端权限更新

**订单列表页面 (index.vue)**
- 发货按钮权限从 `wholesale:order:edit` 改为 `wholesale:order:shipment`

### 3. 权限控制逻辑

```
用户没有 wholesale:order:shipment 权限时：
├── ❌ 发货按钮不显示
├── ❌ 准备发货抽屉不能打开
├── ❌ 发货相关的所有操作都不能执行
└── ✅ 仍可查看订单基本信息（如有 wholesale:order:view 权限）

用户有 wholesale:order:shipment 权限时：
├── ✅ 发货按钮显示
├── ✅ 可以打开发货抽屉
├── ✅ 可以查看发货库存和方案
├── ✅ 可以执行所有发货操作
└── ✅ 可以更新包裹状态和跟踪信息
```

## 执行说明

### 前置条件
1. 备份现有sys_menu表数据
2. 确认menu_id不冲突 (使用7036)

### 执行命令
```sql
source server/script/sql/migration/v1.5.7/001_add_wholesale_shipment_permissions.sql
```

### 执行后验证
1. 检查新增的权限是否正确创建
2. 验证发货按钮的权限控制
3. 验证发货抽屉的权限控制
4. 测试发货相关API的权限检查
5. 测试只读权限的功能

### 回滚方案
如需回滚，删除新增的权限记录：
```sql
DELETE FROM sys_menu WHERE menu_id = 7036;
```

## 相关代码变更

### 后端文件
- `WhsOrderShipmentController.java` - 发货权限细化
- `WhsLogisticsController.java` - 物流权限支持发货权限

### 前端文件
- `web/apps/lookah-admin/src/views/wholesale/order/index.vue` - 发货按钮权限更新

## 注意事项

1. **向后兼容性**: 现有的 `wholesale:order:edit` 权限仍然保留，不影响其他订单编辑功能
2. **权限分配**: 管理员需要为相关角色分配新的发货权限
3. **测试建议**: 建议在测试环境先验证权限控制的正确性
4. **文档更新**: 需要更新相关的权限管理文档

## 影响评估

- **兼容性**: 高 - 不影响现有功能
- **安全性**: 高 - 提供更精确的权限控制
- **可维护性**: 高 - 权限职责更清晰
- **用户体验**: 高 - 权限控制更精准，避免误操作
