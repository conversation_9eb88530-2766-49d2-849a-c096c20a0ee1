-- ===========================================================================================
-- 批发客户地址优化
-- 日期: 2025-08-06
-- 版本: v1.6.3
-- ===========================================================================================

-- 功能说明：
-- 1. 客户地址姓名验证优化
--    - 收件人姓名（lastName + " " + firstName）总长度不能超过35字符
--    - 满足Haotong海外仓发货系统的RecipientName字段要求
--    - 在应用层（Java和前端）实现验证，无需修改数据库结构
--
-- 2. 批发客户地址搜索功能
--    - 支持多字段模糊搜索：姓名、电话、邮箱、公司名称、地址等
--    - 在后台管理系统中提供统一的地址搜索接口
--    - 无需修改数据库结构，搜索功能在应用层实现

-- ===========================================================================================
-- 注意事项：
-- 1. 此次优化不涉及数据库结构变更
-- 2. 所有验证和搜索逻辑在应用层实现
-- 3. 确保现有数据的兼容性
-- ===========================================================================================

-- 数据验证：检查现有数据中是否有超过35字符的收件人姓名
SELECT 
    id,
    member_id,
    CONCAT(last_name, ' ', first_name) AS recipient_name,
    LENGTH(CONCAT(last_name, ' ', first_name)) AS name_length
FROM whs_shipping_address
WHERE LENGTH(CONCAT(last_name, ' ', first_name)) > 35
ORDER BY name_length DESC;

-- 如果发现有超长的姓名，可以通过以下查询生成修改建议
-- （实际修改需要根据业务需求进行，这里仅作为参考）
/*
SELECT 
    id,
    member_id,
    first_name,
    last_name,
    CONCAT(last_name, ' ', first_name) AS original_name,
    LENGTH(CONCAT(last_name, ' ', first_name)) AS original_length,
    CONCAT(
        SUBSTRING(last_name, 1, 17), 
        ' ', 
        SUBSTRING(first_name, 1, 17)
    ) AS suggested_name,
    LENGTH(CONCAT(
        SUBSTRING(last_name, 1, 17), 
        ' ', 
        SUBSTRING(first_name, 1, 17)
    )) AS suggested_length
FROM whs_shipping_address
WHERE LENGTH(CONCAT(last_name, ' ', first_name)) > 35;
*/

-- ===========================================================================================
-- 验证完成标记
-- ===========================================================================================
-- 此迁移脚本仅用于文档记录和数据验证，不执行任何DDL或DML操作