-- ----------------------------
-- Migration: 为客户表添加销售代表ID字段
-- Author: jf
-- Date: 2025-07-24
-- Description: 在whs_member表中添加salesperson_id销售代表ID字段，关联sys_user表
-- ----------------------------

-- 添加销售代表ID字段
ALTER TABLE `whs_member` ADD COLUMN `salesperson_id` bigint(20) DEFAULT NULL COMMENT '销售代表ID，关联sys_user表' AFTER `customer_code`;

-- 创建索引，优化查询性能
ALTER TABLE `whs_member` ADD KEY `idx_salesperson_id` (`salesperson_id`);

-- 添加复合索引，优化常见查询场景
-- 按销售代表查询活跃客户
ALTER TABLE `whs_member` ADD KEY `idx_salesperson_status` (`salesperson_id`, `status`);
-- 按销售代表和创建时间查询
ALTER TABLE `whs_member` ADD KEY `idx_salesperson_create_time` (`salesperson_id`, `create_time`);

-- 注意：按照项目规范，禁止使用外键约束，在应用层维护数据一致性
-- 参考：.rules/database.md - 禁止外键约束规范
