-- ----------------------------
-- Migration: 更新角色数据权限配置，支持业务代表或本人数据权限
-- Author: jf
-- Date: 2025-07-25
-- Description: 将数据权限为"仅本人数据权限"(5)的角色更新为"业务代表或本人数据权限"(7)
--              以支持业务代表能看到自己创建的客户和自己名下的客户
-- ----------------------------

-- 查看当前数据权限为5的角色
SELECT role_id, role_name, role_key, data_scope, remark 
FROM sys_role 
WHERE data_scope = '5' AND del_flag = '0';

-- 更新数据权限为5的角色为7（业务代表或本人数据权限）
-- 注意：这里只更新非系统内置角色，避免影响系统核心功能
UPDATE sys_role 
SET data_scope = '7',
    remark = CONCAT(IFNULL(remark, ''), ' [系统自动更新：支持业务代表或本人数据权限]'),
    update_time = NOW(),
    update_by = 1
WHERE data_scope = '5' 
  AND del_flag = '0'
  AND role_id NOT IN (1, 2); -- 排除超级管理员和普通管理员角色

-- 验证更新结果
SELECT role_id, role_name, role_key, data_scope, remark 
FROM sys_role 
WHERE data_scope = '7' AND del_flag = '0';
