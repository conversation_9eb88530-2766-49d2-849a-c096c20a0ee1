-- ----------------------------
-- Migration: 为订单表添加销售代表ID字段
-- Author: jf
-- Date: 2025-07-24
-- Description: 在whs_order表中添加salesperson_id销售代表ID字段，关联sys_user表
-- ----------------------------

-- 添加销售代表ID字段
ALTER TABLE `whs_order` ADD COLUMN `salesperson_id` bigint(20) DEFAULT NULL COMMENT '销售代表ID，关联sys_user表' AFTER `member_id`;

-- 创建索引，优化查询性能
ALTER TABLE `whs_order` ADD KEY `idx_salesperson_id` (`salesperson_id`);

-- 添加复合索引，优化常见查询场景
-- 按销售代表和订单状态查询
ALTER TABLE `whs_order` ADD KEY `idx_salesperson_status` (`salesperson_id`, `order_status`);
-- 按销售代表和创建时间查询（用于业绩统计）
ALTER TABLE `whs_order` ADD KEY `idx_salesperson_create_time` (`salesperson_id`, `create_time`);
-- 按销售代表、客户和时间查询（用于客户订单历史）
ALTER TABLE `whs_order` ADD KEY `idx_salesperson_member_time` (`salesperson_id`, `member_id`, `create_time`);

-- 注意：按照项目规范，禁止使用外键约束，在应用层维护数据一致性
-- 参考：.rules/database.md - 禁止外键约束规范
