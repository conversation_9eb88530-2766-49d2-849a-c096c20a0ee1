-- ----------------------------
-- Migration: 添加业务代表标识字段
-- Author: imhuso
-- Date: 2025-07-25
-- Description: 在sys_user表中添加is_salesperson字段，用于标识用户是否为业务代表
-- ----------------------------

-- 检查字段是否已存在
-- SELECT COLUMN_NAME
-- FROM INFORMATION_SCHEMA.COLUMNS
-- WHERE TABLE_SCHEMA = DATABASE()
--   AND TABLE_NAME = 'sys_user'
--   AND COLUMN_NAME = 'is_salesperson';

-- 添加业务代表标识字段
ALTER TABLE sys_user
ADD COLUMN is_salesperson CHAR(1) DEFAULT '0' COMMENT '是否为业务代表（0否 1是）'
AFTER status;

-- 添加索引，提升查询性能
CREATE INDEX idx_is_salesperson ON sys_user(is_salesperson);

-- 验证字段添加成功
DESCRIBE sys_user;
