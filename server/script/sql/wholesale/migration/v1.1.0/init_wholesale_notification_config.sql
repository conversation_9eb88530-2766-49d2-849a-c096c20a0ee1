-- =============================================
-- 迁移脚本: v1.1.0 - 添加批发模块默认企业微信配置
-- 功能: 在 sys_config 表中添加默认企业微信配置
-- 创建时间: 2025-07-02
-- 说明: 企业微信密钥现在通过数据库管理，提供更好的安全性和灵活性
-- =============================================

-- 开始事务
START TRANSACTION;

-- 插入默认企业微信webhook密钥列表（如果不存在）
INSERT INTO sys_config (
    config_name,
    config_key,
    config_value,
    config_type,
    create_dept,
    create_by,
    create_time,
    remark
)
SELECT
    '批发模块-默认企业微信配置',
    'wholesale.notification.wecom.list',
    'cc661ded-2b76-4328-97f0-f3a01861a3b6',
    'Y',
    103,
    1,
    NOW(),
    '默认企业微信webhook密钥列表，多个密钥用逗号分隔。请手动配置实际的webhook密钥。'
WHERE NOT EXISTS (
    SELECT 1 FROM sys_config WHERE config_key = 'wholesale.notification.wecom.list'
);

-- 验证配置是否插入成功
SELECT
    config_name as '配置名称',
    config_key as '配置键',
    config_value as '配置值',
    remark as '备注'
FROM sys_config
WHERE config_key = 'wholesale.notification.wecom.list';

-- 提交事务
COMMIT;

-- 输出完成信息
SELECT 'SUCCESS: 批发模块默认企业微信配置初始化完成！' as '执行结果';
