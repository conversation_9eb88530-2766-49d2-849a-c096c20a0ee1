-- =============================================
-- 迁移脚本: v1.1.0 - 重新整理字典数据
-- 功能:
--   1. sys_grant_type 只保留密码认证
--   2. sys_device_type 重新整理为后台、批发、商城三个端
-- 创建时间: 2025-07-02
-- =============================================

-- 开始事务
START TRANSACTION;

-- =============================================
-- 1. 更新 sys_grant_type (授权类型) - 只保留密码认证
-- =============================================

-- 删除非密码认证的授权类型
DELETE FROM sys_dict_data
WHERE dict_type = 'sys_grant_type'
AND dict_value != 'password';

-- 更新密码认证的排序和标签
UPDATE sys_dict_data
SET dict_sort = 1,
    dict_label = '密码认证',
    remark = '密码认证方式'
WHERE dict_type = 'sys_grant_type'
AND dict_value = 'password';

-- =============================================
-- 2. 更新 sys_device_type (设备类型) - 重新整理为三个端
-- =============================================

-- 删除现有的设备类型数据
DELETE FROM sys_dict_data WHERE dict_type = 'sys_device_type';

-- 插入新的设备类型数据
INSERT INTO sys_dict_data (
    dict_code,
    dict_sort,
    dict_label,
    dict_value,
    dict_type,
    css_class,
    list_class,
    is_default,
    create_dept,
    create_by,
    create_time,
    remark
) VALUES
-- 后台管理端
(
    FLOOR(RAND() * 9000000000000000000) + 1000000000000000000,
    1,
    '后台管理',
    'admin',
    'sys_device_type',
    '',
    'primary',
    'N',
    103,
    1,
    NOW(),
    '后台管理端'
),
-- 批发端
(
    FLOOR(RAND() * 9000000000000000000) + 1000000000000000000,
    2,
    '批发端',
    'wholesale',
    'sys_device_type',
    '',
    'success',
    'N',
    103,
    1,
    NOW(),
    '批发端'
),
-- 商城端
(
    FLOOR(RAND() * 9000000000000000000) + 1000000000000000000,
    3,
    '商城端',
    'mall',
    'sys_device_type',
    '',
    'warning',
    'N',
    103,
    1,
    NOW(),
    '商城端'
);

-- =============================================
-- 3. 更新字典类型表的备注信息
-- =============================================

-- 更新授权类型的备注
UPDATE sys_dict_type
SET remark = '认证授权类型（仅支持密码认证）',
    update_by = 1,
    update_time = NOW()
WHERE dict_type = 'sys_grant_type';

-- 更新设备类型的备注
UPDATE sys_dict_type
SET remark = '客户端设备类型（后台、批发、商城三个端）',
    update_by = 1,
    update_time = NOW()
WHERE dict_type = 'sys_device_type';

-- =============================================
-- 4. 更新客户端配置表中的设备类型映射和密钥
-- =============================================

-- 生成随机密钥变量
SET @admin_secret = SUBSTRING(MD5(RAND()), 1, 32);
SET @wholesale_secret = SUBSTRING(MD5(RAND()), 1, 32);

-- 更新现有客户端配置的设备类型和密钥
-- pc -> admin (后台管理)
UPDATE sys_client
SET device_type = 'admin',
    client_key = 'admin',
    client_secret = @admin_secret,
    client_id = MD5(CONCAT('admin', @admin_secret)),
    update_by = 1,
    update_time = NOW()
WHERE device_type = 'pc' OR client_key = 'pc';

-- xcx/app -> wholesale (批发端)
UPDATE sys_client
SET device_type = 'wholesale',
    client_key = 'wholesale',
    client_secret = @wholesale_secret,
    client_id = MD5(CONCAT('wholesale', @wholesale_secret)),
    update_by = 1,
    update_time = NOW()
WHERE device_type = 'xcx' OR client_key = 'app';

-- android -> wholesale (批发端，如果有的话)
UPDATE sys_client
SET device_type = 'wholesale',
    client_key = 'wholesale',
    client_secret = @wholesale_secret,
    client_id = MD5(CONCAT('wholesale', @wholesale_secret)),
    update_by = 1,
    update_time = NOW()
WHERE device_type = 'android';

-- ios -> wholesale (批发端，如果有的话)
UPDATE sys_client
SET device_type = 'wholesale',
    client_key = 'wholesale',
    client_secret = @wholesale_secret,
    client_id = MD5(CONCAT('wholesale', @wholesale_secret)),
    update_by = 1,
    update_time = NOW()
WHERE device_type = 'ios';

-- =============================================
-- 5. 更新客户端配置表中的授权类型（只保留密码认证）
-- =============================================

-- 将所有客户端的授权类型更新为只支持密码认证
UPDATE sys_client
SET grant_type = 'password',
    update_by = 1,
    update_time = NOW()
WHERE grant_type != 'password';

-- =============================================
-- 6. 为商城端添加新的客户端配置
-- =============================================

-- 生成随机的客户端密钥和ID
SET @mall_client_key = 'mall';
SET @mall_client_secret = SUBSTRING(MD5(RAND()), 1, 32);
SET @mall_client_id = MD5(CONCAT(@mall_client_key, @mall_client_secret));

-- 插入商城端客户端配置（如果不存在）
INSERT INTO sys_client (
    id,
    client_id,
    client_key,
    client_secret,
    grant_type,
    device_type,
    active_timeout,
    timeout,
    status,
    del_flag,
    create_dept,
    create_by,
    create_time
)
SELECT
    FLOOR(RAND() * 9000000000000000000) + 1000000000000000000,
    @mall_client_id,
    'mall',
    @mall_client_secret,
    'password',
    'mall',
    1800,
    604800,
    '1',
    '0',
    103,
    1,
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM sys_client WHERE device_type = 'mall'
);

-- 提交事务
COMMIT;

-- =============================================
-- 验证脚本执行结果
-- =============================================

-- 查看更新后的授权类型
SELECT '=== sys_grant_type 更新结果 ===' as info;
SELECT dict_sort, dict_label, dict_value, remark
FROM sys_dict_data
WHERE dict_type = 'sys_grant_type'
ORDER BY dict_sort;

-- 查看更新后的设备类型
SELECT '=== sys_device_type 更新结果 ===' as info;
SELECT dict_sort, dict_label, dict_value, list_class, remark
FROM sys_dict_data
WHERE dict_type = 'sys_device_type'
ORDER BY dict_sort;

-- 查看字典类型表更新结果
SELECT '=== 字典类型表更新结果 ===' as info;
SELECT dict_name, dict_type, remark
FROM sys_dict_type
WHERE dict_type IN ('sys_grant_type', 'sys_device_type');

-- 查看客户端配置更新结果
SELECT '=== 客户端配置更新结果 ===' as info;
SELECT client_key, device_type, grant_type, status
FROM sys_client
ORDER BY client_key;
