-- 批发订单表性能优化索引
-- 创建时间：2025-07-07
-- 目标：优化订单列表查询性能，从600ms降低到100ms以内

-- 1. 单列索引 - 用于精确查询和过滤
CREATE INDEX idx_member_id ON whs_order(member_id) COMMENT '会员ID索引';
CREATE INDEX idx_order_status ON whs_order(order_status) COMMENT '订单状态索引';
CREATE INDEX idx_payment_status ON whs_order(payment_status) COMMENT '支付状态索引';
CREATE INDEX idx_shipment_status ON whs_order(shipment_status) COMMENT '发货状态索引';
CREATE INDEX idx_invoice_status ON whs_order(invoice_status) COMMENT '发票状态索引';
CREATE INDEX idx_create_time ON whs_order(create_time) COMMENT '创建时间索引';
CREATE INDEX idx_del_flag ON whs_order(del_flag) COMMENT '删除标记索引';

-- 2. 复合索引 - 用于复杂查询和排序优化
-- 最常用的查询组合：按删除标记过滤 + 按创建时间排序
CREATE INDEX idx_del_flag_create_time ON whs_order(del_flag, create_time DESC) COMMENT '删除标记+创建时间复合索引';

-- 会员相关查询优化：会员ID + 删除标记 + 创建时间
CREATE INDEX idx_member_del_create ON whs_order(member_id, del_flag, create_time DESC) COMMENT '会员+删除标记+创建时间复合索引';

-- 3. 订单项表索引优化 - 解决N+1查询问题
-- 订单项按订单ID查询的索引（用于批量查询订单项）
CREATE INDEX idx_order_id ON whs_order_item(order_id) COMMENT '订单ID索引，优化订单项批量查询';
