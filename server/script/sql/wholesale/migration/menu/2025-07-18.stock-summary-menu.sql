-- 添加库存汇总菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES (6402, '库存汇总', 6400, 2, 'summary', 'wholesale/stock-summary/index', '', 0, 0, 'C', '1', '1', 'wholesale:stock:summary', 'ep:data-analysis', 103, 1, NOW(), 1, NOW(), '库存汇总菜单');

-- 添加库存汇总权限按钮
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES (6412, '库存汇总查询', 6402, 1, '', '', '', 0, 0, 'F', '1', '1', 'wholesale:stock:summary', '#', 103, 1, NOW(), 1, NOW(), '');
