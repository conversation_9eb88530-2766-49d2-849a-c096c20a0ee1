-- 添加客户端类型和设备类型字典数据
-- 版本: v1.2.7
-- 日期: 2025-07-08
-- 描述: 统一添加客户端类型和设备类型的字典配置，合并原有的3个迁移脚本

-- ========================================
-- 1. 客户端类型字典配置
-- ========================================

-- 1.1 添加客户端类型字典类型
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (100, '客户端类型', 'sys_client_type', 103, 1, NOW(), 1, NOW(), '系统客户端类型列表')
ON DUPLICATE KEY UPDATE
    `dict_name` = VALUES(`dict_name`),
    `update_time` = NOW(),
    `remark` = VALUES(`remark`);

-- 1.2 清理旧的客户端类型字典数据（如果存在）
DELETE FROM `sys_dict_data` WHERE `dict_type` = 'sys_client_type';

-- 1.3 添加客户端类型字典数据
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1001, 1, '管理后台', 'admin', 'sys_client_type', '', 'primary', 'Y', 103, 1, NOW(), 1, NOW(), '系统管理后台（包含CRM功能）'),
(1002, 2, '商城模块', 'mall', 'sys_client_type', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), 'Mall电商模块'),
(1003, 3, '批发模块', 'wholesale', 'sys_client_type', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), 'Wholesale批发模块');

-- ========================================
-- 2. 设备类型字典配置
-- ========================================

-- 2.1 更新设备类型字典类型（已存在，只更新备注）
UPDATE `sys_dict_type`
SET `remark` = '用户设备类型列表（PC、移动、平板）',
    `update_by` = 1,
    `update_time` = NOW()
WHERE `dict_type` = 'sys_device_type';

-- 2.2 清理旧的设备类型字典数据（包括错误的模块名称数据）
DELETE FROM `sys_dict_data` WHERE `dict_type` = 'sys_device_type';

-- 2.3 添加正确的设备类型字典数据
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1004, 1, 'PC端', 'pc', 'sys_device_type', '', 'primary', 'Y', 103, 1, NOW(), 1, NOW(), '个人电脑桌面端'),
(1005, 2, '移动端', 'mobile', 'sys_device_type', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '手机移动端设备'),
(1006, 3, '平板端', 'tablet', 'sys_device_type', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), '平板设备');

-- ========================================
-- 3. 验证数据插入结果
-- ========================================

-- 验证客户端类型字典
-- SELECT * FROM `sys_dict_type` WHERE `dict_type` = 'sys_client_type';
-- SELECT * FROM `sys_dict_data` WHERE `dict_type` = 'sys_client_type' ORDER BY `dict_sort`;

-- 验证设备类型字典
-- SELECT * FROM `sys_dict_type` WHERE `dict_type` = 'sys_device_type';
-- SELECT * FROM `sys_dict_data` WHERE `dict_type` = 'sys_device_type' ORDER BY `dict_sort`;
