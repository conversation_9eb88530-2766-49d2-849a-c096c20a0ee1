-- 优化sys_logininfor表结构：添加client_id字段，移除client_key字段
-- 版本: v1.2.7
-- 日期: 2025-07-08
-- 描述: 统一登录日志表字段，只保留client_id用于客户端标识

-- ========================================
-- 1. 添加client_id字段
-- ========================================

-- 检查client_id字段是否已存在，如果不存在则添加
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
         WHERE TABLE_SCHEMA = DATABASE()
         AND TABLE_NAME = 'sys_logininfor'
         AND COLUMN_NAME = 'client_id') = 0,
        'ALTER TABLE `sys_logininfor` ADD COLUMN `client_id` varchar(32) DEFAULT NULL COMMENT ''客户端ID'' AFTER `user_name`;',
        'SELECT ''client_id字段已存在，跳过添加'' as message;'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- 2. 移除client_key字段（如果存在）
-- ========================================

-- 检查client_key字段是否存在，如果存在则删除
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
         WHERE TABLE_SCHEMA = DATABASE()
         AND TABLE_NAME = 'sys_logininfor'
         AND COLUMN_NAME = 'client_key') > 0,
        'ALTER TABLE `sys_logininfor` DROP COLUMN `client_key`;',
        'SELECT ''client_key字段不存在，跳过删除'' as message;'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- 3. 更新现有数据（可选）
-- ========================================

-- 为现有的登录日志记录设置默认的client_id
-- 这里可以根据实际情况设置默认值，比如设置为'admin'
UPDATE `sys_logininfor`
SET `client_id` = 'admin'
WHERE `client_id` IS NULL;

-- ========================================
-- 4. 验证表结构修改结果
-- ========================================

-- 验证字段是否添加成功
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
-- FROM INFORMATION_SCHEMA.COLUMNS
-- WHERE TABLE_SCHEMA = DATABASE()
-- AND TABLE_NAME = 'sys_logininfor'
-- AND COLUMN_NAME = 'client_id';

-- 验证数据更新结果
-- SELECT COUNT(*) as total_records,
--        COUNT(client_id) as records_with_client_id,
--        COUNT(*) - COUNT(client_id) as records_without_client_id
-- FROM `sys_logininfor`;
