-- ----------------------------
-- Table structure for crm_customer_follow_record
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer_follow_record`;
CREATE TABLE `crm_customer_follow_record`
(
    `follow_id`               bigint       NOT NULL COMMENT '跟进记录ID',
    `customer_id`             bigint       NOT NULL COMMENT '客户ID',
    `follow_date`             datetime     NOT NULL COMMENT '跟进日期',
    `follow_content`          text         NOT NULL COMMENT '跟进内容',
    `follow_difficulties`     text         DEFAULT NULL COMMENT '跟进过程存在的难点',
    `follow_type`             varchar(50)  NOT NULL DEFAULT 'phone' COMMENT '跟进方式（电话、邮件、面谈、微信等）',
    `follow_result`           varchar(100) DEFAULT NULL COMMENT '跟进结果',
    `next_follow_date`        datetime     DEFAULT NULL COMMENT '下次跟进日期',
    `follow_user_id`          bigint       NOT NULL COMMENT '跟进人员ID',
    `follow_user_name`        varchar(100) NOT NULL COMMENT '跟进人员姓名',
    `create_dept`             bigint       DEFAULT NULL COMMENT '创建部门',
    `create_by`               bigint       DEFAULT NULL COMMENT '创建者',
    `create_time`             datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`               bigint       DEFAULT NULL COMMENT '更新者',
    `update_time`             datetime     DEFAULT NULL COMMENT '更新时间',
    `remark`                  varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`follow_id`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_follow_date` (`follow_date`),
    KEY `idx_follow_user_id` (`follow_user_id`),
    KEY `idx_next_follow_date` (`next_follow_date`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = 'CRM客户跟进记录表';
