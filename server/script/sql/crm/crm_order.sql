-- ----------------------------
-- Table structure for crm_order
-- ----------------------------
DROP TABLE IF EXISTS `crm_order`;
CREATE TABLE `crm_order`
(
    `order_id`          bigint         NOT NULL COMMENT '订单ID',
    `order_no`          varchar(50)    NOT NULL COMMENT '订单号（自动生成）',
    `order_date`        datetime       NOT NULL COMMENT '下单日期',
    `customer_id`       bigint         NOT NULL COMMENT '客户ID',
    `customer_code`     varchar(50)    NOT NULL COMMENT '客户编号',
    `company_name`      varchar(200)   NOT NULL COMMENT '公司名称',
    `order_status`      varchar(50)    NOT NULL DEFAULT 'pending' COMMENT '订单状态',
    `order_total_amount` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '订单总金额',
    `payment_method`    varchar(50)    NOT NULL COMMENT '付款方式（bank_transfer:银行转账, ach:ACH, online_credit_card:线上信用卡, offline_credit_card:线下信用卡, zelle:Zelle）',
    `related_order_no`  varchar(50)    DEFAULT NULL COMMENT '关联订单号',
    `create_dept`       bigint         DEFAULT NULL COMMENT '创建部门',
    `create_by`         varchar(64)    DEFAULT '' COMMENT '创建者',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_by`         varchar(64)    DEFAULT '' COMMENT '更新者',
    `update_time`       datetime       DEFAULT NULL COMMENT '更新时间',
    `remark`            varchar(500)   DEFAULT NULL COMMENT '备注',
    `del_flag`          char(1)        DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
    `deleted_at`        bigint         DEFAULT NULL COMMENT '删除时间戳（毫秒，软删除时记录，NULL表示未删除）',
    PRIMARY KEY (`order_id`),
    UNIQUE KEY `uk_order_no` (`order_no`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_order_date` (`order_date`),
    KEY `idx_order_status` (`order_status`),
    KEY `idx_customer_order_date` (`customer_id`, `order_date`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_del_flag` (`del_flag`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='CRM客户订单表';

-- ----------------------------
-- Records of crm_order
-- ----------------------------
