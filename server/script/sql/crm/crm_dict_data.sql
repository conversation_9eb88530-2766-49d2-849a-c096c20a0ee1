-- CRM模块数据字典初始化脚本
-- 根据CrmConstants生成字典数据
-- <AUTHOR>
-- @date 2025-07-16

-- 1. 客户状态字典
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1002, 'CRM客户状态', 'crm_customer_status', 103, 1, NOW(), 1, NOW(), 'CRM客户状态列表');

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2087, 1, '潜在客户', 'potential', 'crm_customer_status', '', 'default', 'Y', 103, 1, NOW(), 1, NOW(), '潜在客户'),
(2088, 2, '已联系', 'contacted', 'crm_customer_status', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), '已联系客户'),
(2089, 3, '有兴趣', 'interested', 'crm_customer_status', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '有兴趣客户'),
(2090, 4, '谈判中', 'negotiating', 'crm_customer_status', '', 'primary', 'N', 103, 1, NOW(), 1, NOW(), '谈判中客户'),
(2091, 5, '成交', 'closed_won', 'crm_customer_status', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '成交客户'),
(2092, 6, '流失', 'closed_lost', 'crm_customer_status', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), '流失客户'),
(2093, 7, '活跃', 'active', 'crm_customer_status', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '活跃客户'),
(2094, 8, '不活跃', 'inactive', 'crm_customer_status', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), '不活跃客户');

-- 2. 客户类型字典
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1003, 'CRM客户类型', 'crm_customer_type', 103, 1, NOW(), 1, NOW(), 'CRM客户类型列表');

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2095, 1, '分销商', 'Distributor', 'crm_customer_type', '', 'primary', 'Y', 103, 1, NOW(), 1, NOW(), '分销商'),
(2096, 2, '现购自运', 'Cash&Carry', 'crm_customer_type', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '现购自运'),
(2097, 3, '烟草店', 'SmokeShop', 'crm_customer_type', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '烟草店'),
(2098, 4, '药房', 'Dispensary', 'crm_customer_type', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), '药房'),
(2099, 5, '加油站', 'GasStation', 'crm_customer_type', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), '加油站'),
(2100, 6, '电子烟店', 'VapeShop', 'crm_customer_type', '', 'primary', 'N', 103, 1, NOW(), 1, NOW(), '电子烟店'),
(2101, 7, '连锁烟草店', 'ChainSmokeShops', 'crm_customer_type', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '连锁烟草店'),
(2102, 8, '连锁药房', 'ChainDispensaries', 'crm_customer_type', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '连锁药房'),
(2103, 9, '电商', 'E-commerce', 'crm_customer_type', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), '电商');

-- 3. 职位类型字典
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1004, 'CRM职位类型', 'crm_position_type', 103, 1, NOW(), 1, NOW(), 'CRM职位类型列表');

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2104, 1, '老板', 'Boss', 'crm_position_type', '', 'warning', 'Y', 103, 1, NOW(), 1, NOW(), '老板'),
(2105, 2, '采购经理', 'PurchaseManager', 'crm_position_type', '', 'primary', 'N', 103, 1, NOW(), 1, NOW(), '采购经理'),
(2106, 3, '店长', 'StoreManager', 'crm_position_type', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '店长'),
(2107, 4, '合伙人', 'Co-partner', 'crm_position_type', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), '合伙人'),
(2108, 5, '销售经理', 'SalesManager', 'crm_position_type', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), '销售经理'),
(2109, 6, '其他', 'Others', 'crm_position_type', '', 'default', 'N', 103, 1, NOW(), 1, NOW(), '其他');

-- 4. 客户来源字典
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1005, 'CRM客户来源', 'crm_customer_source', 103, 1, NOW(), 1, NOW(), 'CRM客户来源列表');

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2110, 1, 'TPE展会', 'TPE', 'crm_customer_source', '', 'primary', 'Y', 103, 1, NOW(), 1, NOW(), 'TPE展会'),
(2111, 2, 'Champs展会', 'Champs', 'crm_customer_source', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), 'Champs展会'),
(2112, 3, 'MJBIZ展会', 'MJBIZ', 'crm_customer_source', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), 'MJBIZ展会'),
(2113, 4, '其他展会', 'Other Exhibition', 'crm_customer_source', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), '其他展会'),
(2114, 5, '客户推荐', 'Customer Referral', 'crm_customer_source', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), '客户推荐'),
(2115, 6, '邮件开发', 'Email Development', 'crm_customer_source', '', 'primary', 'N', 103, 1, NOW(), 1, NOW(), '邮件开发'),
(2116, 7, '谷歌开发', 'Google Development', 'crm_customer_source', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '谷歌开发'),
(2117, 8, '社交媒体', 'Social Media', 'crm_customer_source', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '社交媒体'),
(2118, 9, '获客平台', 'Acquisition Platform', 'crm_customer_source', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), '获客平台');

-- 5. 影响成交因素字典
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1006, 'CRM影响成交因素', 'crm_deal_influence_factor', 103, 1, NOW(), 1, NOW(), 'CRM影响成交因素列表');

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2119, 1, '价格', 'price', 'crm_deal_influence_factor', '', 'danger', 'Y', 103, 1, NOW(), 1, NOW(), '价格因素'),
(2120, 2, '已从当地购买', 'local_purchase', 'crm_deal_influence_factor', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '已从当地购买'),
(2121, 3, '产品质量', 'product_quality', 'crm_deal_influence_factor', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '产品质量'),
(2122, 4, '售后服务', 'after_sales_service', 'crm_deal_influence_factor', '', 'primary', 'N', 103, 1, NOW(), 1, NOW(), '售后服务'),
(2123, 5, '运输时效', 'shipping_time', 'crm_deal_influence_factor', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), '运输时效'),
(2124, 6, '产品是否畅销', 'product_popularity', 'crm_deal_influence_factor', '', 'primary', 'N', 103, 1, NOW(), 1, NOW(), '产品是否畅销'),
(2125, 7, '库存压力', 'inventory_pressure', 'crm_deal_influence_factor', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '库存压力'),
(2126, 8, '资金压力', 'financial_pressure', 'crm_deal_influence_factor', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), '资金压力'),
(2127, 9, '其他', 'other', 'crm_deal_influence_factor', '', 'default', 'N', 103, 1, NOW(), 1, NOW(), '其他因素');

-- 6. 跟进方式字典
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1007, 'CRM跟进方式', 'crm_follow_type', 103, 1, NOW(), 1, NOW(), 'CRM跟进方式列表');

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2128, 1, '电话', 'phone', 'crm_follow_type', '', 'primary', 'Y', 103, 1, NOW(), 1, NOW(), '电话跟进'),
(2129, 2, '邮件', 'email', 'crm_follow_type', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '邮件跟进'),
(2130, 3, '面谈', 'meeting', 'crm_follow_type', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '面谈'),
(2131, 4, '微信', 'wechat', 'crm_follow_type', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '微信'),
(2132, 5, 'WhatsApp', 'whatsapp', 'crm_follow_type', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), 'WhatsApp'),
(2133, 6, 'Telegram', 'telegram', 'crm_follow_type', '', 'primary', 'N', 103, 1, NOW(), 1, NOW(), 'Telegram'),
(2134, 7, '视频通话', 'video_call', 'crm_follow_type', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), '视频通话'),
(2135, 8, '其他', 'other', 'crm_follow_type', '', 'default', 'N', 103, 1, NOW(), 1, NOW(), '其他方式');

-- 7. 社媒类型字典
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1008, 'CRM社媒类型', 'crm_social_media_type', 103, 1, NOW(), 1, NOW(), 'CRM社媒类型列表');

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2136, 1, 'Facebook', 'facebook', 'crm_social_media_type', '', 'primary', 'Y', 103, 1, NOW(), 1, NOW(), 'Facebook社交媒体'),
(2137, 2, 'Instagram', 'instagram', 'crm_social_media_type', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), 'Instagram社交媒体'),
(2138, 3, '领英', 'linkedin', 'crm_social_media_type', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), 'LinkedIn领英'),
(2139, 4, 'TikTok', 'tiktok', 'crm_social_media_type', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), 'TikTok短视频'),
(2140, 5, 'YouTube', 'youtube', 'crm_social_media_type', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), 'YouTube视频平台'),
(2141, 6, '其他', 'other', 'crm_social_media_type', '', 'default', 'N', 103, 1, NOW(), 1, NOW(), '其他社交媒体平台');

-- 8. 是否公海客户字典
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1009, 'CRM客户归属', 'crm_is_public', 103, 1, NOW(), 1, NOW(), 'CRM客户归属类型');

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2142, 1, '私有客户', '0', 'crm_is_public', '', 'success', 'Y', 103, 1, NOW(), 1, NOW(), '私有客户'),
(2143, 2, '公海客户', '1', 'crm_is_public', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '公海客户');
