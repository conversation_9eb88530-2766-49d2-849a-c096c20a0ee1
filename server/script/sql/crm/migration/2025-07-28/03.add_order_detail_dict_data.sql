-- =============================================
-- CRM订单详情字典数据 - 2025年7月28日
-- =============================================
-- 功能说明：为订单详情状态和是否赠品添加字典数据
-- 影响表：sys_dict_type, sys_dict_data
-- 执行环境：开发/测试/生产环境
-- ID说明：使用雪花算法生成的19位长整型ID，确保全局唯一性
-- =============================================

-- 1. 订单详情状态字典类型
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1854078945234567168, 'CRM订单详情状态', 'crm_order_detail_status', 103, 1, NOW(), 1, NOW(), 'CRM订单详情状态列表');

-- 订单详情状态字典数据
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1854078945234567169, 1, '正常', 'normal', 'crm_order_detail_status', '', 'success', 'Y', 103, 1, NOW(), 1, NOW(), '订单详情正常状态'),
(1854078945234567170, 2, '退款', 'refund', 'crm_order_detail_status', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '订单详情退款状态'),
(1854078945234567171, 3, '退货', 'return', 'crm_order_detail_status', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '订单详情退货状态'),
(1854078945234567172, 4, '到货不良', 'defective', 'crm_order_detail_status', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), '订单详情到货不良状态'),
(1854078945234567173, 5, '到货少货', 'shortage', 'crm_order_detail_status', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), '订单详情到货少货状态'),
(1854078945234567174, 6, '发错货', 'wrong', 'crm_order_detail_status', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), '订单详情发错货状态'),
(1854078945234567175, 7, '重发', 'resend', 'crm_order_detail_status', '', 'processing', 'N', 103, 1, NOW(), 1, NOW(), '订单详情重发状态');

-- 2. 是否赠品字典类型
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1854078945234567176, 'CRM是否赠品', 'crm_is_gift', 103, 1, NOW(), 1, NOW(), 'CRM是否赠品标识');

-- 是否赠品字典数据
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1854078945234567177, 1, '否', '0', 'crm_is_gift', '', 'default', 'Y', 103, 1, NOW(), 1, NOW(), '非赠品，正常商品'),
(1854078945234567178, 2, '是', '1', 'crm_is_gift', '', 'orange', 'N', 103, 1, NOW(), 1, NOW(), '赠品商品');

-- =============================================
-- 验证SQL
-- =============================================
-- 查看字典类型创建情况
-- SELECT * FROM `sys_dict_type` WHERE `dict_type` IN ('crm_order_detail_status', 'crm_is_gift');

-- 查看字典数据创建情况
-- SELECT * FROM `sys_dict_data` WHERE `dict_type` IN ('crm_order_detail_status', 'crm_is_gift') ORDER BY `dict_type`, `dict_sort`;

-- 查看订单详情状态字典
-- SELECT dict_label, dict_value, list_class FROM `sys_dict_data` 
-- WHERE `dict_type` = 'crm_order_detail_status' ORDER BY `dict_sort`;

-- 查看是否赠品字典
-- SELECT dict_label, dict_value, list_class FROM `sys_dict_data` 
-- WHERE `dict_type` = 'crm_is_gift' ORDER BY `dict_sort`;

-- =============================================
-- 回滚SQL（如需回滚，请谨慎执行）
-- =============================================
-- 删除字典数据
-- DELETE FROM `sys_dict_data` WHERE `dict_type` IN ('crm_order_detail_status', 'crm_is_gift');

-- 删除字典类型
-- DELETE FROM `sys_dict_type` WHERE `dict_type` IN ('crm_order_detail_status', 'crm_is_gift');
