-- =============================================
-- CRM订单详情状态字段扩展 - 2025年7月28日
-- =============================================
-- 功能说明：为订单详情表添加状态字段、是否赠品字段和退款原因字段
-- 影响表：crm_order_detail
-- 执行环境：开发/测试/生产环境
-- =============================================

-- 1. 添加订单详情状态字段
ALTER TABLE `crm_order_detail` 
ADD COLUMN `detail_status` VARCHAR(20) DEFAULT 'normal' COMMENT '订单详情状态（normal=正常, refund=退款, return=退货, defective=到货不良, shortage=到货少货, wrong=发错货, resend=重发）' 
AFTER `remark`;

-- 2. 添加是否赠品字段
ALTER TABLE `crm_order_detail`
ADD COLUMN `is_gift` CHAR(1) DEFAULT '0' COMMENT '是否赠品（1=是, 0=否）'
AFTER `detail_status`;

-- 3. 添加退款原因字段
ALTER TABLE `crm_order_detail` 
ADD COLUMN `refund_reason` VARCHAR(500) DEFAULT NULL COMMENT '退款原因' 
AFTER `is_gift`;

-- 4. 添加索引以提高查询性能
-- 为订单详情状态添加索引
ALTER TABLE `crm_order_detail` 
ADD INDEX `idx_detail_status` (`detail_status`);

-- 为是否赠品添加索引
ALTER TABLE `crm_order_detail` 
ADD INDEX `idx_is_gift` (`is_gift`);

-- 5. 更新现有数据（可选）
-- 将所有现有记录的状态设置为正常
UPDATE `crm_order_detail`
SET `detail_status` = 'normal', `is_gift` = '0'
WHERE `detail_status` IS NULL OR `is_gift` IS NULL;

-- =============================================
-- 验证SQL
-- =============================================
-- 查看表结构变更
-- DESCRIBE `crm_order_detail`;

-- 查看索引创建情况
-- SHOW INDEX FROM `crm_order_detail`;

-- 查看数据更新情况
-- SELECT detail_status, is_gift, COUNT(*) as count 
-- FROM `crm_order_detail` 
-- GROUP BY detail_status, is_gift;

-- =============================================
-- 回滚SQL（如需回滚，请谨慎执行）
-- =============================================
-- 删除添加的索引
-- ALTER TABLE `crm_order_detail` DROP INDEX `idx_detail_status`;
-- ALTER TABLE `crm_order_detail` DROP INDEX `idx_is_gift`;

-- 删除添加的字段
-- ALTER TABLE `crm_order_detail` DROP COLUMN `refund_reason`;
-- ALTER TABLE `crm_order_detail` DROP COLUMN `is_gift`;
-- ALTER TABLE `crm_order_detail` DROP COLUMN `detail_status`;
