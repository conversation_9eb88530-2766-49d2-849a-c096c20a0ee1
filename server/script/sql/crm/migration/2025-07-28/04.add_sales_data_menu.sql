-- =============================================
-- CRM销售数据菜单配置SQL - 2025年7月28日
-- =============================================
-- 功能说明：为CRM系统添加销售数据管理菜单
-- 菜单层级：CRM管理 -> 销售管理 -> 销售数据
-- ID说明：使用雪花算法生成的19位长整型ID，确保全局唯一性
-- =============================================

-- 1. 插入销售管理父级菜单（目录）
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1854078945234567200, '销售管理', 8000, 2, 'sales', '', '',
    1, 0, 'M', '1', '1', '', 'lucide:trending-up',
    103, 1, NOW(), 1, NOW(), '销售业务管理'
);

-- 2. 插入销售数据菜单（页面）
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1854078945234567201, '销售数据', 1854078945234567200, 1, 'data', 'crm/sales-data/index', '',
    1, 1, 'C', '1', '1', 'crm:sales:data:list', 'lucide:bar-chart-3',
    103, 1, NOW(), 1, NOW(), '销售数据统计页面'
);

-- =============================================
-- 插入销售数据相关的功能按钮权限
-- =============================================

-- 销售数据查询权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1854078945234567202, '销售数据查询', 1854078945234567201, 1, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:sales:data:query', '#',
    103, 1, NOW(), 1, NOW(), '销售数据查询权限'
);

-- 销售数据查看权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1854078945234567203, '销售数据查看', 1854078945234567201, 2, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:sales:data:view', '#',
    103, 1, NOW(), 1, NOW(), '销售数据查看权限'
);

-- 销售数据导出权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1854078945234567204, '销售数据导出', 1854078945234567201, 3, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:sales:data:export', '#',
    103, 1, NOW(), 1, NOW(), '销售数据导出权限'
);

-- =============================================
-- 验证SQL
-- =============================================
-- 查看菜单创建情况
-- SELECT menu_id, menu_name, parent_id, path, component, perms 
-- FROM `sys_menu` 
-- WHERE menu_id IN (1854078945234567200, 1854078945234567201, 1854078945234567202, 1854078945234567203, 1854078945234567204)
-- ORDER BY menu_id;

-- 查看菜单层级结构
-- SELECT 
--     CASE 
--         WHEN parent_id = 0 THEN CONCAT('├─ ', menu_name)
--         WHEN parent_id = 8000 THEN CONCAT('│  ├─ ', menu_name)
--         WHEN parent_id = 1854078945234567200 THEN CONCAT('│  │  ├─ ', menu_name)
--         ELSE CONCAT('│  │  │  ├─ ', menu_name)
--     END as menu_tree
-- FROM `sys_menu` 
-- WHERE menu_id IN (8000, 1854078945234567200, 1854078945234567201, 1854078945234567202, 1854078945234567203, 1854078945234567204)
-- ORDER BY parent_id, order_num;

-- =============================================
-- 回滚SQL（如需回滚，请谨慎执行）
-- =============================================
-- 删除销售数据菜单及权限
-- DELETE FROM `sys_menu` WHERE menu_id IN (1854078945234567200, 1854078945234567201, 1854078945234567202, 1854078945234567203, 1854078945234567204);
