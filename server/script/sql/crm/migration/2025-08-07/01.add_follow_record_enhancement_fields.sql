-- =============================================
-- CRM客户跟进记录增强字段添加
-- Author: AI Assistant  
-- Date: 2025-08-07
-- Description: 为客户跟进记录表添加跟进附件、跟进建议、是否审批三个字段
-- =============================================

-- 1. 为 crm_customer_follow_record 表添加新字段
ALTER TABLE `crm_customer_follow_record` 
ADD COLUMN `follow_attachments` TEXT DEFAULT NULL COMMENT '跟进附件（逗号分隔的URL字符串）' AFTER `remark`,
ADD COLUMN `follow_suggestions` VARCHAR(1000) DEFAULT NULL COMMENT '跟进建议（具体的跟进建议和行动计划）' AFTER `follow_attachments`,
ADD COLUMN `is_approval_required` CHAR(1) DEFAULT '0' COMMENT '是否审批（0-否 1-是）' AFTER `follow_suggestions`;

-- 2. 添加字段索引以提高查询性能
ALTER TABLE `crm_customer_follow_record` 
ADD INDEX `idx_is_approval_required` (`is_approval_required`);

-- 3. 添加CRM是否审批字典配置
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(1854078945234567200, 'CRM是否审批', 'crm_is_approval', 103, 1, NOW(), 1, NOW(), 'CRM跟进记录是否审批标识');

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(1854078945234567201, 1, '否', '0', 'crm_is_approval', '', 'default', 'Y', 103, 1, NOW(), 1, NOW(), '未审批'),
(1854078945234567202, 2, '是', '1', 'crm_is_approval', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '已审批');

-- =============================================
-- 验证字段添加结果
-- =============================================

-- 查看表结构
DESCRIBE `crm_customer_follow_record`;

-- 查看索引
SHOW INDEX FROM `crm_customer_follow_record`;

-- 验证字典数据
SELECT * FROM `sys_dict_type` WHERE `dict_type` = 'crm_is_approval';
SELECT * FROM `sys_dict_data` WHERE `dict_type` = 'crm_is_approval';

-- =============================================
-- 字段说明
-- =============================================
/*
新增字段说明：

1. follow_attachments (跟进附件)
   - 类型: TEXT
   - 允许空值: YES
   - 默认值: NULL
   - 格式: 逗号分隔的URL字符串
   - 示例: "https://xxx.com/files/quote.pdf,https://xxx.com/files/catalog.xlsx"
   - 说明: 存储跟进过程中的相关附件URL，如报价单、产品目录、合同等，多个URL以逗号分隔

2. follow_suggestions (跟进建议)
   - 类型: VARCHAR(1000)
   - 允许空值: YES
   - 默认值: NULL
   - 说明: 记录具体的跟进建议和行动计划，为下次跟进提供指导

3. is_approval_required (是否审批)
   - 类型: CHAR(1)
   - 允许空值: NO
   - 默认值: '0'
   - 取值: '0'-否, '1'-是
   - 说明: 标识该跟进记录是否已经过审批

索引说明：
- idx_is_approval_required: 是否审批索引，便于筛选已审批的跟进记录

数据字典说明：
- sys_yes_no: 通用的是否字典，可复用于其他模块

注意事项：
1. 附件信息以逗号分隔的URL字符串存储，后端自动转换为数组返回给前端
2. 跟进建议字段长度为1000字符，满足大部分场景需求
3. 是否审批字段默认为'否'，表示未审批状态
4. 添加了相应的索引以提高查询性能
5. 前端使用followAttachmentsArray字段进行数组操作，后端会自动处理转换
*/