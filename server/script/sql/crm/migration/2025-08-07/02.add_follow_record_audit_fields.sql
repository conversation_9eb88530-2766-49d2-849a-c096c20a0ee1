-- =============================================
-- CRM跟进记录审核相关字段添加
-- Author: AI Assistant  
-- Date: 2025-08-07
-- Description: 为跟进记录表添加是否已读、审核时间字段，并添加审核菜单权限
-- =============================================

-- 1. 为 crm_customer_follow_record 表添加审核相关字段
ALTER TABLE `crm_customer_follow_record` 
ADD COLUMN `is_read` CHAR(1) DEFAULT '0' COMMENT '是否已读（0-未读 1-已读）' AFTER `is_approval_required`,
ADD COLUMN `approval_time` DATETIME DEFAULT NULL COMMENT '审核时间' AFTER `is_read`;

-- 2. 添加字段索引以提高查询性能
ALTER TABLE `crm_customer_follow_record` 
ADD INDEX `idx_is_read` (`is_read`),
ADD INDEX `idx_approval_time` (`approval_time`);

-- 3. 添加CRM是否已读字典配置
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(1854078945234567210, 'CRM是否已读', 'crm_is_read', 103, 1, NOW(), 1, NOW(), 'CRM跟进记录是否已读标识');

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(1854078945234567211, 1, '未读', '0', 'crm_is_read', '', 'default', 'Y', 103, 1, NOW(), 1, NOW(), '未读状态'),
(1854078945234567212, 2, '已读', '1', 'crm_is_read', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '已读状态');

-- 4. 添加跟进记录审核菜单权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1854078945234567220, '跟进记录审核', 1859425395123200020, 4, '', '', '', 1, 0, 'F', '1', '1', 'crm:follow:audit', '#', 103, 1, NOW(), 1, NOW(), '跟进记录审核权限');

-- =============================================
-- 验证字段添加结果
-- =============================================

-- 查看表结构
DESCRIBE `crm_customer_follow_record`;

-- 查看索引
SHOW INDEX FROM `crm_customer_follow_record`;

-- 验证字典数据
SELECT * FROM `sys_dict_type` WHERE `dict_type` = 'crm_is_read';
SELECT * FROM `sys_dict_data` WHERE `dict_type` = 'crm_is_read';

-- 验证菜单权限
SELECT * FROM `sys_menu` WHERE `perms` = 'crm:follow:audit';

-- =============================================
-- 字段说明
-- =============================================
/*
新增字段说明：

1. is_read (是否已读)
   - 类型: CHAR(1)
   - 允许空值: NO
   - 默认值: '0'
   - 取值: '0'-未读, '1'-已读
   - 说明: 标识跟进记录是否已被相关人员阅读

2. approval_time (审核时间)
   - 类型: DATETIME
   - 允许空值: YES
   - 默认值: NULL
   - 说明: 记录跟进记录的审核时间，审核时会自动设置

索引说明：
- idx_is_read: 是否已读索引，便于筛选未读的跟进记录
- idx_approval_time: 审核时间索引，便于按审核时间排序和筛选

权限说明：
- crm:follow:audit: 审核跟进记录的权限，只有具备此权限的用户才能进行审核操作

业务逻辑：
1. 新创建的跟进记录默认为未读状态
2. 审核时会同时设置审核时间和审批状态
3. 可以通过是否已读字段筛选需要处理的记录
4. 审核时间可以用于审核记录的追踪和统计
*/