# CRM模块数据库迁移 2025-07-16

## 版本概述

本版本主要新增CRM客户画像相关字段，提升客户管理能力。

## 迁移文件列表

### add_customer_profile_fields.sql
**功能**: 新增CRM客户画像字段
**影响表**: `crm_customer`, `sys_dict_type`, `sys_dict_data`

**主要变更**:
- 添加10个客户画像相关字段到 `crm_customer` 表
- 创建6个数据字典类型及对应数据
- 添加数据库索引优化查询性能

**新增字段**:
1. `single_purchase_amount` - 单次采购金额
2. `purchase_frequency` - 采购频率
3. `is_easy_communicate` - 是否好沟通
4. `contact_preference` - 联系方式偏好
5. `decision_authority` - 决策权大小
6. `price_sensitivity` - 对价格敏感的程度
7. `deal_key_points` - 成交的关键点
8. `competitor_brands` - 已销售的竞品品牌
9. `contact_frequency` - 联系频次
10. `order_frequency` - 下单频率

**新增数据字典**:
- `crm_purchase_frequency` - 采购频率选项
- `crm_contact_preference` - 联系方式偏好选项
- `crm_decision_authority` - 决策权大小选项
- `crm_price_sensitivity` - 价格敏感程度选项
- `crm_contact_frequency` - 联系频次选项
- `crm_order_frequency` - 下单频率选项

## 执行说明

### 执行顺序
按文件名字母顺序执行本版本的迁移文件：
1. `add_customer_profile_fields.sql`

### 执行方式
```bash
# 执行迁移文件
mysql -u username -p database_name < server/script/sql/crm/migration/2025-07-16/add_customer_profile_fields.sql
```

### 验证方式
执行完成后，可通过以下SQL验证：
```sql
-- 验证新字段是否添加成功
DESCRIBE crm_customer;

-- 验证数据字典是否创建成功
SELECT dict_type, dict_name FROM sys_dict_type WHERE dict_type LIKE 'crm_%';

-- 验证索引是否创建成功
SHOW INDEX FROM crm_customer WHERE Key_name LIKE 'idx_%';
```

## 注意事项

1. **备份数据**: 执行前请备份相关数据表
2. **测试环境**: 建议先在测试环境验证
3. **向后兼容**: 所有新增字段都有默认值，不影响现有功能
4. **权限要求**: 需要数据库表结构修改权限

## 回滚方案

如需回滚，可执行以下操作：
```sql
-- 删除新增字段
ALTER TABLE crm_customer
DROP COLUMN single_purchase_amount,
DROP COLUMN purchase_frequency,
DROP COLUMN is_easy_communicate,
DROP COLUMN contact_preference,
DROP COLUMN decision_authority,
DROP COLUMN price_sensitivity,
DROP COLUMN deal_key_points,
DROP COLUMN competitor_brands,
DROP COLUMN contact_frequency,
DROP COLUMN order_frequency;

-- 删除数据字典（谨慎操作）
DELETE FROM sys_dict_data WHERE dict_type LIKE 'crm_%';
DELETE FROM sys_dict_type WHERE dict_type LIKE 'crm_%';
```
