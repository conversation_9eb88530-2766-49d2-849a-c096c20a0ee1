# 修复影响成交因素数组接收问题 - 2025年7月24日

## 🐛 问题描述

在实现影响成交因素多选功能时，发现后端无法正确接收前端发送的`dealInfluenceFactorsArray`数组数据。

### 问题现象
- 前端正常发送多选数组数据：`['price', 'product_quality']`
- 后端Controller接收到的`dealInfluenceFactorsArray`字段为null
- 数据库中`deal_influence_factors`字段没有正确保存

### 问题原因
1. **@JsonIgnore注解问题**：在BO类的`dealInfluenceFactorsArray`字段上使用了`@JsonIgnore`注解，导致Jackson在反序列化时忽略该字段
2. **数据转换时机问题**：没有在保存前正确处理数组到字符串的转换

## 🔧 解决方案

### 1. 移除@JsonIgnore注解

**修改文件**：`CrmCustomerBo.java`

```java
// 修改前
/**
 * 影响成交的主要因素（多选数组，用于前端）
 */
@JsonIgnore
private String[] dealInfluenceFactorsArray;

// 修改后
/**
 * 影响成交的主要因素（多选数组，用于前端）
 */
private String[] dealInfluenceFactorsArray;
```

### 2. 在Service层添加数据转换处理

**修改文件**：`CrmCustomerServiceImpl.java`

#### 2.1 在保存方法中添加转换处理
```java
@Override
public Boolean insertByBo(CrmCustomerBo bo) {
    // 处理影响成交因素数组转换
    processDealInfluenceFactorsArray(bo);
    
    CrmCustomer add = MapstructUtils.convert(bo, CrmCustomer.class);
    // ... 其他逻辑
}

@Override
public Boolean updateByBo(CrmCustomerBo bo) {
    // 处理影响成交因素数组转换
    processDealInfluenceFactorsArray(bo);
    
    CrmCustomer update = MapstructUtils.convert(bo, CrmCustomer.class);
    // ... 其他逻辑
}
```

#### 2.2 添加数据转换方法
```java
/**
 * 处理影响成交因素数组转换
 * 将前端传来的数组转换为逗号分隔的字符串
 */
private void processDealInfluenceFactorsArray(CrmCustomerBo bo) {
    if (bo.getDealInfluenceFactorsArray() != null && bo.getDealInfluenceFactorsArray().length > 0) {
        // 如果前端传了数组，将数组转换为逗号分隔的字符串
        bo.setDealInfluenceFactors(String.join(",", bo.getDealInfluenceFactorsArray()));
    }
}
```

### 3. 添加调试日志

**修改文件**：`CrmCustomerController.java`

```java
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("crm/customer")
public class CrmCustomerController extends BaseController {

    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CrmCustomerBo bo) {
        // 添加日志来调试数据接收
        log.info("接收到客户数据: dealInfluenceFactors={}, dealInfluenceFactorsArray={}", 
                bo.getDealInfluenceFactors(), 
                bo.getDealInfluenceFactorsArray() != null ? Arrays.toString(bo.getDealInfluenceFactorsArray()) : "null");
        return toAjax(crmCustomerService.insertByBo(bo));
    }

    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CrmCustomerBo bo) {
        // 添加日志来调试数据接收
        log.info("接收到客户更新数据: dealInfluenceFactors={}, dealInfluenceFactorsArray={}", 
                bo.getDealInfluenceFactors(), 
                bo.getDealInfluenceFactorsArray() != null ? Arrays.toString(bo.getDealInfluenceFactorsArray()) : "null");
        return toAjax(crmCustomerService.updateByBo(bo));
    }
}
```

## 📊 数据流程

### 修复后的完整数据流程

#### 1. 前端发送数据
```json
{
  "companyName": "测试公司",
  "dealInfluenceFactorsArray": ["price", "product_quality", "after_sales_service"],
  "dealInfluenceOther": ""
}
```

#### 2. 后端接收数据
```java
// Controller接收到的BO对象
CrmCustomerBo bo = {
    companyName: "测试公司",
    dealInfluenceFactors: null,  // 初始为空
    dealInfluenceFactorsArray: ["price", "product_quality", "after_sales_service"],
    dealInfluenceOther: ""
}
```

#### 3. Service层处理
```java
// processDealInfluenceFactorsArray方法执行后
CrmCustomerBo bo = {
    companyName: "测试公司",
    dealInfluenceFactors: "price,product_quality,after_sales_service",  // 转换后的字符串
    dealInfluenceFactorsArray: ["price", "product_quality", "after_sales_service"],
    dealInfluenceOther: ""
}
```

#### 4. 数据库存储
```sql
INSERT INTO crm_customer (
    company_name,
    deal_influence_factors,
    deal_influence_other
) VALUES (
    '测试公司',
    'price,product_quality,after_sales_service',
    ''
);
```

#### 5. 前端查询显示
```java
// VO对象的getDealInfluenceFactorsArray()方法
public String[] getDealInfluenceFactorsArray() {
    if (dealInfluenceFactors != null && !dealInfluenceFactors.isEmpty()) {
        return dealInfluenceFactors.split(",");  // "price,product_quality,after_sales_service" -> ["price", "product_quality", "after_sales_service"]
    }
    return new String[0];
}
```

## 🧪 测试验证

### 1. 后端日志验证
启动应用后，在创建或编辑客户时查看控制台日志：
```
INFO  - 接收到客户数据: dealInfluenceFactors=null, dealInfluenceFactorsArray=[price, product_quality]
INFO  - 处理后的数据: dealInfluenceFactors=price,product_quality
```

### 2. 数据库验证
```sql
SELECT 
    company_name,
    deal_influence_factors
FROM crm_customer 
WHERE customer_id = ?;

-- 期望结果
-- company_name: 测试公司
-- deal_influence_factors: price,product_quality,after_sales_service
```

### 3. 前端验证
- 创建客户时选择多个影响因素
- 保存成功后重新打开编辑，确认多选值正确显示
- 查看客户详情页面，确认多选标签正确显示

## ⚠️ 注意事项

### 1. 数据兼容性
- 现有的单选数据完全兼容
- 新的多选数据向下兼容
- 数据库字段无需修改

### 2. 前端字段使用
- **编辑表单**：使用`dealInfluenceFactorsArray`字段
- **查看页面**：使用`dealInfluenceFactorsArray`字段（通过VO的getter方法获取）
- **后端存储**：使用`dealInfluenceFactors`字段（逗号分隔的字符串）

### 3. 验证规则
- 前端验证：至少选择一个因素
- 后端验证：字符串长度不超过500字符
- 数据转换：自动处理数组与字符串的转换

## 🔄 回滚方案

如果需要回滚到单选模式：

### 1. 前端回滚
```typescript
// 将多选改回单选
{
  fieldName: 'dealInfluenceFactors',  // 改回原字段名
  component: 'Select',
  componentProps: {
    mode: undefined,  // 移除多选模式
    // ... 其他配置
  }
}
```

### 2. 后端回滚
```java
// 移除数组字段和转换方法
// 移除processDealInfluenceFactorsArray调用
// 移除相关日志
```

## ✅ 修复完成

- [x] 移除@JsonIgnore注解
- [x] 添加Service层数据转换处理
- [x] 添加Controller调试日志
- [x] 验证数据接收和转换流程
- [x] 确保前后端数据一致性

影响成交因素数组接收问题已完全修复，多选功能现在可以正常工作！🎉
