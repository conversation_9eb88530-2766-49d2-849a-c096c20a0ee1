-- ================================================================
-- CRM客户影响成交因素字段改为多选支持 - 2025年7月24日
-- ================================================================

-- 说明：
-- 1. 数据库字段 deal_influence_factors 保持 varchar(500) 不变
-- 2. 前端改为多选模式，后端处理数组与字符串的转换
-- 3. 多个值用逗号分隔存储，如：'price,product_quality,after_sales_service'

-- ================================================================
-- 数据验证和清理（可选）
-- ================================================================

-- 1. 查看当前影响成交因素的数据分布
SELECT 
    deal_influence_factors,
    COUNT(*) as count
FROM 
    crm_customer 
WHERE 
    del_flag = '0' 
    AND deal_influence_factors IS NOT NULL 
    AND deal_influence_factors != ''
GROUP BY 
    deal_influence_factors
ORDER BY 
    count DESC;

-- 2. 检查是否有包含逗号的数据（可能已经是多选格式）
SELECT 
    customer_code,
    company_name,
    deal_influence_factors
FROM 
    crm_customer 
WHERE 
    del_flag = '0' 
    AND deal_influence_factors LIKE '%,%'
LIMIT 10;

-- ================================================================
-- 数据转换示例（如果需要将现有单选数据转换为多选格式）
-- ================================================================

-- 注意：以下转换脚本仅在需要时执行，请根据实际数据情况决定是否执行

/*
-- 示例：如果需要将某些客户的单个因素扩展为多个因素
-- 比如将 'price' 扩展为 'price,product_quality'

-- 更新示例1：为价格敏感的客户添加产品质量因素
UPDATE crm_customer 
SET deal_influence_factors = CONCAT(deal_influence_factors, ',product_quality')
WHERE 
    del_flag = '0' 
    AND deal_influence_factors = 'price'
    AND price_sensitivity = '高';

-- 更新示例2：为已有其他因素的客户，确保格式正确
UPDATE crm_customer 
SET deal_influence_factors = TRIM(BOTH ',' FROM deal_influence_factors)
WHERE 
    del_flag = '0' 
    AND (deal_influence_factors LIKE ',%' OR deal_influence_factors LIKE '%,');
*/

-- ================================================================
-- 验证SQL
-- ================================================================

-- 验证多选数据格式
SELECT 
    customer_code,
    company_name,
    deal_influence_factors,
    CHAR_LENGTH(deal_influence_factors) as length,
    (CHAR_LENGTH(deal_influence_factors) - CHAR_LENGTH(REPLACE(deal_influence_factors, ',', '')) + 1) as factor_count
FROM 
    crm_customer 
WHERE 
    del_flag = '0' 
    AND deal_influence_factors IS NOT NULL 
    AND deal_influence_factors != ''
ORDER BY 
    factor_count DESC
LIMIT 20;

-- 检查字段长度是否足够
SELECT 
    MAX(CHAR_LENGTH(deal_influence_factors)) as max_length,
    AVG(CHAR_LENGTH(deal_influence_factors)) as avg_length
FROM 
    crm_customer 
WHERE 
    del_flag = '0' 
    AND deal_influence_factors IS NOT NULL 
    AND deal_influence_factors != '';

-- ================================================================
-- 数据完整性检查
-- ================================================================

-- 检查是否有无效的因素值（不在字典中的值）
SELECT DISTINCT
    TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(deal_influence_factors, ',', numbers.n), ',', -1)) as factor_value,
    COUNT(*) as usage_count
FROM 
    crm_customer
    CROSS JOIN (
        SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL 
        SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL 
        SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10
    ) numbers
WHERE 
    del_flag = '0'
    AND deal_influence_factors IS NOT NULL 
    AND deal_influence_factors != ''
    AND CHAR_LENGTH(deal_influence_factors) - CHAR_LENGTH(REPLACE(deal_influence_factors, ',', '')) >= numbers.n - 1
GROUP BY 
    factor_value
ORDER BY 
    usage_count DESC;

-- ================================================================
-- 性能优化建议
-- ================================================================

-- 如果需要频繁按影响成交因素查询，可以考虑添加全文索引
-- ALTER TABLE crm_customer ADD FULLTEXT(deal_influence_factors);

-- 或者创建专门的关联表来存储多选关系（更规范的做法）
/*
CREATE TABLE crm_customer_deal_factors (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    customer_id BIGINT NOT NULL,
    factor_code VARCHAR(50) NOT NULL,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_customer_id (customer_id),
    INDEX idx_factor_code (factor_code),
    UNIQUE KEY uk_customer_factor (customer_id, factor_code),
    FOREIGN KEY (customer_id) REFERENCES crm_customer(customer_id) ON DELETE CASCADE
) COMMENT='客户影响成交因素关联表';
*/

-- ================================================================
-- 使用说明
-- ================================================================

/*
多选功能使用说明：

1. 前端表单：
   - 使用 Select 组件的 multiple 模式
   - 支持最多显示3个标签，超出显示 "+N个"
   - 必填验证，至少选择一个因素

2. 数据存储：
   - 数据库中以逗号分隔的字符串存储：'price,product_quality,after_sales_service'
   - 后端BO类提供数组和字符串的自动转换
   - VO类提供数组格式供前端使用

3. 数据查询：
   - 可以使用 FIND_IN_SET 函数查询包含特定因素的客户
   - 示例：SELECT * FROM crm_customer WHERE FIND_IN_SET('price', deal_influence_factors)

4. 显示逻辑：
   - 当选择包含 'other' 时，显示"其他因素"输入框
   - 查看页面以多选标签形式显示
   - Excel导出时显示完整的逗号分隔字符串
*/
