-- ================================================================
-- CRM客户表添加联系方式字段 - 2025年7月24日
-- ================================================================

-- 1. 为 crm_customer 表添加客户电话字段
ALTER TABLE `crm_customer` 
ADD COLUMN `customer_phone` varchar(20) DEFAULT NULL COMMENT '客户电话' 
AFTER `contact_email`;

-- 2. 为 crm_customer 表添加WhatsApp号码字段
ALTER TABLE `crm_customer` 
ADD COLUMN `whatsapp_number` varchar(20) DEFAULT NULL COMMENT 'WhatsApp号码' 
AFTER `customer_phone`;

-- 3. 添加字段注释
ALTER TABLE `crm_customer` 
MODIFY COLUMN `customer_phone` varchar(20) DEFAULT NULL COMMENT '客户电话',
MODIFY COLUMN `whatsapp_number` varchar(20) DEFAULT NULL COMMENT 'WhatsApp号码';

-- 4. 添加索引以提高查询性能（可选）
CREATE INDEX `idx_crm_customer_phone` ON `crm_customer` (`customer_phone`);
CREATE INDEX `idx_crm_customer_whatsapp` ON `crm_customer` (`whatsapp_number`);

-- ================================================================
-- 验证SQL
-- ================================================================

-- 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'crm_customer' 
    AND COLUMN_NAME IN ('customer_phone', 'whatsapp_number')
ORDER BY 
    ORDINAL_POSITION;

-- 验证索引是否创建成功
SHOW INDEX FROM `crm_customer` WHERE `Key_name` IN ('idx_crm_customer_phone', 'idx_crm_customer_whatsapp');

-- 查看表结构
DESCRIBE `crm_customer`;

-- ================================================================
-- 回滚SQL（如果需要回滚）
-- ================================================================

/*
-- 删除索引
DROP INDEX `idx_crm_customer_whatsapp` ON `crm_customer`;
DROP INDEX `idx_crm_customer_phone` ON `crm_customer`;

-- 删除字段
ALTER TABLE `crm_customer` DROP COLUMN `whatsapp_number`;
ALTER TABLE `crm_customer` DROP COLUMN `customer_phone`;
*/

-- ================================================================
-- 数据示例（可选）
-- ================================================================

/*
-- 更新示例数据（如果需要为现有客户添加联系方式）
UPDATE `crm_customer` 
SET 
    `customer_phone` = '******-0123',
    `whatsapp_number` = '******-0123'
WHERE 
    `customer_code` = 'LCS#00001';

-- 查询包含新字段的客户信息
SELECT 
    customer_code,
    company_name,
    contact_person,
    contact_email,
    customer_phone,
    whatsapp_number
FROM 
    `crm_customer` 
WHERE 
    del_flag = '0'
LIMIT 10;
*/
