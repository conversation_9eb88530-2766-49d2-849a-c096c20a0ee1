# CRM 订单 Mapper XML 配置优化 - 2025年7月24日

## 📁 文件位置

```
server/lookah-business/lookah-crm/lookah-crm-core/src/main/resources/mapper/
└── CrmOrderMapper.xml          # 订单查询 XML 映射文件（直接在mapper目录下）
```

**注意**：XML文件直接放在mapper目录下，不使用crm子文件夹。

## 🎯 优化目标

将 CrmOrderMapper 中的自定义 SQL 查询从 `@Select` 注解迁移到 MyBatis XML 文件中，解决以下问题：

1. **WHERE 子句冲突**：使用 `<where>` 标签避免与 MyBatis-Plus 自动生成的条件冲突
2. **逻辑删除支持**：确保 `@TableLogic` 注解正确生效
3. **用户信息关联**：保持 `user_id` 关联查询用户名称
4. **代码分离**：将 SQL 逻辑与 Java 代码分离

## 🔧 技术实现

### 1. 解决 WHERE 冲突问题

#### 问题描述
```
Caused by: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "WHERE" "WHERE"
```

#### 原因分析
- MyBatis-Plus 的 `${ew.customSqlSegment}` 可能包含 WHERE 子句
- 手动添加的 `WHERE o.del_flag = '0'` 与自动生成的 WHERE 冲突

#### 解决方案
使用固定WHERE + 条件判断的方式：

```xml
<!-- ❌ 错误写法 - 会导致 WHERE 冲突 -->
<select id="selectVoPageWithUserInfo" resultMap="CrmOrderVoResult">
    <include refid="selectCrmOrderVoWithUserInfo"/>
    WHERE o.del_flag = '0'
    ${ew.customSqlSegment}  <!-- 这里可能包含WHERE，导致重复 -->
</select>

<!-- ❌ 也会冲突 - <where>标签仍然会与customSqlSegment冲突 -->
<select id="selectVoPageWithUserInfo" resultMap="CrmOrderVoResult">
    <include refid="selectCrmOrderVoWithUserInfo"/>
    <where>
        o.del_flag = '0'
        ${ew.customSqlSegment}  <!-- customSqlSegment包含WHERE时仍会冲突 -->
    </where>
</select>

<!-- ✅ 最终正确写法 - 智能处理WHERE和ORDER BY -->
<select id="selectVoPageWithUserInfo" resultMap="CrmOrderVoResult">
    <include refid="selectCrmOrderVoWithUserInfo"/>
    <where>
        o.del_flag = '0'
        <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != '' and !ew.sqlSegment.trim().toUpperCase().startsWith('ORDER')">
            AND (${ew.sqlSegment})  <!-- 只有非ORDER BY条件才用AND连接 -->
        </if>
    </where>
    <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != '' and ew.sqlSegment.trim().toUpperCase().startsWith('ORDER')">
        ${ew.sqlSegment}  <!-- ORDER BY子句直接拼接 -->
    </if>
</select>
```

### 2. XML 配置详解

#### 结果映射 (ResultMap)
```xml
<resultMap id="CrmOrderVoResult" type="com.imhuso.crm.core.domain.vo.CrmOrderVo">
    <id property="orderId" column="order_id"/>
    <result property="orderNo" column="order_no"/>
    <!-- ... 其他字段映射 ... -->
    <result property="createByName" column="createByName"/>
    <result property="updateByName" column="updateByName"/>
</resultMap>
```

#### SQL 片段复用
```xml
<sql id="selectCrmOrderVoWithUserInfo">
    SELECT
        o.*,
        cu.nick_name as createByName,
        uu.nick_name as updateByName
    FROM crm_order o
    LEFT JOIN sys_user cu ON o.create_by = cu.user_id
    LEFT JOIN sys_user uu ON o.update_by = uu.user_id
</sql>
```

#### 智能动态条件处理
```xml
<where>
    o.del_flag = '0'                    <!-- 固定的逻辑删除条件 -->
    <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != '' and !ew.sqlSegment.trim().toUpperCase().startsWith('ORDER')">
        AND (${ew.sqlSegment})          <!-- 非ORDER BY条件用AND连接 -->
    </if>
</where>
<if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != '' and ew.sqlSegment.trim().toUpperCase().startsWith('ORDER')">
    ${ew.sqlSegment}                    <!-- ORDER BY子句直接拼接 -->
</if>
```

### 3. 关键特性

#### 逻辑删除支持 ✅
- **自动过滤**：`o.del_flag = '0'` 确保只查询未删除记录
- **一致性**：所有查询方法都应用相同的逻辑删除条件
- **冲突解决**：使用 `<where>` 标签避免 WHERE 子句冲突

#### 用户信息关联 ✅
- **正确关联**：使用 `o.create_by = cu.user_id` 关联用户表
- **显示名称**：获取 `nick_name` 作为用户显示名称
- **双重关联**：同时关联创建者和更新者信息

#### 字段冲突解决 ✅
- **问题识别**：多表关联时相同字段名导致引用不明确
- **表别名处理**：在Service层使用表别名处理动态条件
- **智能条件**：区分WHERE条件和ORDER BY子句

#### 动态查询支持 ✅
- **条件拼接**：`${ew.sqlSegment}` 支持 MyBatis-Plus 动态条件
- **智能处理**：自动区分WHERE条件和ORDER BY子句
- **分页支持**：与 MyBatis-Plus 分页插件完美集成

## 📋 方法说明

### 1. selectVoPageWithUserInfo
- **用途**：分页查询订单列表，关联用户信息
- **参数**：分页对象 + 查询条件包装器
- **返回**：IPage<CrmOrderVo>
- **特点**：自动应用逻辑删除条件

### 2. selectVoListWithUserInfo
- **用途**：查询订单列表，关联用户信息（不分页）
- **参数**：查询条件包装器
- **返回**：List<CrmOrderVo>
- **特点**：自动应用逻辑删除条件

### 3. selectVoByIdWithUserInfo
- **用途**：根据ID查询单个订单，关联用户信息
- **参数**：订单ID
- **返回**：CrmOrderVo
- **特点**：自动应用逻辑删除条件

## 🔍 使用示例

### Service 层调用
```java
// 分页查询 - 自动处理 WHERE 条件
LambdaQueryWrapper<CrmOrder> lqw = buildQueryWrapper(bo);
IPage<CrmOrderVo> result = baseMapper.selectVoPageWithUserInfo(pageQuery.build(), lqw);

// 列表查询 - 自动处理 WHERE 条件
List<CrmOrderVo> list = baseMapper.selectVoListWithUserInfo(lqw);

// 单个查询 - 自动处理 WHERE 条件
CrmOrderVo order = baseMapper.selectVoByIdWithUserInfo(orderId);
```

### 生成的 SQL 示例
```sql
-- 当 lqw 包含条件时，生成的 SQL：
SELECT o.*, cu.nick_name as createByName, uu.nick_name as updateByName
FROM crm_order o
LEFT JOIN sys_user cu ON o.create_by = cu.user_id
LEFT JOIN sys_user uu ON o.update_by = uu.user_id
WHERE o.del_flag = '0' 
  AND o.order_status = 'pending'
  AND o.order_no LIKE '%TEST%'

-- 当 lqw 为空时，生成的 SQL：
SELECT o.*, cu.nick_name as createByName, uu.nick_name as updateByName
FROM crm_order o
LEFT JOIN sys_user cu ON o.create_by = cu.user_id
LEFT JOIN sys_user uu ON o.update_by = uu.user_id
WHERE o.del_flag = '0'
```

## 🚀 部署说明

1. **确保 XML 文件位置正确**：
   ```
   src/main/resources/mapper/CrmOrderMapper.xml  # 直接在mapper目录下
   ```

2. **MyBatis 配置**：
   ```yaml
   mybatis-plus:
     mapper-locations: classpath*:mapper/**/*.xml
   ```

3. **重启应用**：XML 配置修改需要重启应用生效

## 🔧 故障排除

### 常见问题

1. **WHERE 冲突错误**
   ```
   ParseException: Encountered unexpected token: "WHERE" "WHERE"
   ```
   - **解决方案**：使用智能条件处理，分离WHERE和ORDER BY

2. **字段引用不明确错误**
   ```
   SQLIntegrityConstraintViolationException: Column 'create_time' in where clause is ambiguous
   ```
   - **原因**：多个表都有相同字段名（如create_time）
   - **解决方案**：在Service层使用表别名处理动态条件

3. **XML 文件未找到**
   - 检查文件路径：`src/main/resources/mapper/CrmOrderMapper.xml`
   - 确认 mapper-locations 配置

4. **逻辑删除不生效**
   - 确认 XML 中包含 `o.del_flag = '0'`
   - 检查实体类 `@TableLogic` 注解

5. **用户名称显示为空**
   - 确认关联条件：`o.create_by = cu.user_id`
   - 检查用户表数据完整性

## 📁 最终文件结构

```
server/lookah-business/lookah-crm/lookah-crm-core/
├── src/main/java/com/imhuso/crm/core/mapper/
│   └── CrmOrderMapper.java                    # Mapper接口
└── src/main/resources/mapper/
    └── CrmOrderMapper.xml                     # XML映射文件（直接在mapper下）

server/script/sql/crm/migration/2025-07-24/
├── README.md                                  # 迁移说明
└── 01.mapper_xml_configuration.md            # 本文档
```

## 📝 最佳实践

1. **文件位置**：XML文件直接放在mapper目录下，避免过深的目录结构
2. **使用 `<where>` 标签**：避免 WHERE 子句冲突
3. **SQL 片段复用**：使用 `<sql>` 和 `<include>` 减少重复
4. **结果映射**：使用 ResultMap 明确字段映射关系
5. **逻辑删除**：在所有查询中统一应用逻辑删除条件
6. **用户关联**：使用 user_id 而不是 user_name 进行关联

## 🎉 优化效果

1. ✅ **解决 WHERE 冲突**：使用 `<where>` 标签自动处理条件拼接
2. ✅ **逻辑删除生效**：`@TableLogic` 注解正确工作
3. ✅ **用户信息显示**：正确显示创建者和更新者名称
4. ✅ **代码维护性**：SQL 与 Java 代码分离，更易维护
5. ✅ **动态查询**：完美支持 MyBatis-Plus 动态条件
6. ✅ **文件管理**：简化目录结构，便于维护
