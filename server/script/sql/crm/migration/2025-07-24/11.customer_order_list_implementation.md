# 客户详情订单列表功能实现总结 - 2025年7月24日

## 📋 功能概述

在客户详情页面的"客户订单"Tab中实现了完整的订单列表功能，使用BasicTable组件展示客户的所有订单记录，支持数据加载、刷新等操作。

## 🎯 实现目标

1. **订单列表展示**：在客户详情页面显示该客户的所有订单
2. **使用BasicTable**：采用项目统一的表格组件
3. **配置统一管理**：将表格配置放在data.tsx中
4. **数据自动加载**：切换到订单Tab时自动加载数据
5. **支持刷新**：提供手动刷新功能

## 🔧 技术实现

### 1. 数据配置 (data.tsx)

#### 1.1 订单列表列配置
**文件**：`web/apps/lookah-admin/src/views/crm/customer/data.tsx`

```typescript
// 客户订单列表配置
export const customerOrderColumns: VxeGridProps['columns'] = [
  {
    field: 'orderNo',
    title: '订单号',
    width: 180,
    fixed: 'left',
  },
  {
    field: 'orderDate',
    title: '下单日期',
    width: 120,
    formatter: ({ cellValue }) => {
      return cellValue ? formatDate(cellValue, 'YYYY-MM-DD') : ''
    },
  },
  {
    field: 'orderStatus',
    title: '订单状态',
    width: 100,
    slots: {
      default: ({ row }) => {
        return renderDict(row.orderStatus, CrmDictEnum.CRM_ORDER_STATUS)
      },
    },
  },
  {
    field: 'orderType',
    title: '订单类型',
    width: 120,
    slots: {
      default: ({ row }) => {
        return renderDict(row.orderType, CrmDictEnum.CRM_ORDER_TYPE)
      },
    },
  },
  {
    field: 'orderTotalAmount',
    title: '订单总金额',
    width: 120,
    formatter: ({ cellValue }) => {
      return cellValue ? `$${Number(cellValue).toFixed(2)}` : '$0.00'
    },
  },
  {
    field: 'paymentMethod',
    title: '付款方式',
    width: 120,
    slots: {
      default: ({ row }) => {
        return renderDict(row.paymentMethod, CrmDictEnum.CRM_PAYMENT_METHOD)
      },
    },
  },
  {
    field: 'relatedOrderNo',
    title: '关联订单号',
    width: 180,
  },
  {
    field: 'createByName',
    title: '创建人',
    width: 100,
  },
  {
    field: 'createTime',
    title: '创建时间',
    width: 150,
    formatter: ({ cellValue }) => {
      return cellValue ? formatDate(cellValue) : ''
    },
  },
]
```

#### 1.2 配置特点
- **字典渲染**：订单状态、订单类型、付款方式使用字典渲染
- **金额格式化**：订单总金额自动格式化为美元格式
- **日期格式化**：下单日期和创建时间格式化显示
- **固定列**：订单号固定在左侧，便于查看

### 2. OrderTable组件

#### 2.1 组件文件
**文件**：`web/apps/lookah-admin/src/views/crm/customer/components/order-table.vue`

```vue
<script setup lang="ts">
import type { CrmOrder } from '#/api/crm/order/model'
import { customerOrderColumns } from '../data'
import { useVbenVxeGrid } from '#/adapter/vxe-table'
import { ShoppingCartOutlined } from '@ant-design/icons-vue'

// Props定义
const props = defineProps<{
  data: CrmOrder[]
  loading?: boolean
  height?: number | string
}>()

// 表格配置
const gridOptions = {
  columns: customerOrderColumns,
  height: props.height || 400,
  stripe: true,
  border: true,
  showOverflow: true,
  emptyRender: {
    name: 'AEmpty',
    props: {
      description: '该客户暂无订单记录',
      image: 'simple',
    },
  },
  scrollY: {
    enabled: true,
  },
  pagerConfig: {
    enabled: false, // 由父组件控制分页
  },
}

// 使用VbenVxeGrid
const [OrderTable] = useVbenVxeGrid({
  gridOptions,
})
</script>

<template>
  <div class="order-table-container">
    <OrderTable
      :data="data"
      :loading="loading"
    >
      <template #empty>
        <div class="text-center py-8">
          <ShoppingCartOutlined class="text-4xl text-gray-300 mb-2" />
          <p class="text-gray-500">该客户暂无订单记录</p>
        </div>
      </template>
    </OrderTable>
  </div>
</template>
```

#### 2.2 组件特点
- **使用useVbenVxeGrid**：采用项目标准的表格Hook
- **配置外部化**：表格列配置从data.tsx导入
- **空状态处理**：提供友好的空状态提示
- **响应式高度**：支持自定义表格高度

### 3. 客户详情页面集成

#### 3.1 响应式数据
**文件**：`web/apps/lookah-admin/src/views/crm/customer/components/customer-view.vue`

```typescript
// 订单相关数据
const orderList = ref<CrmOrder[]>([])
const orderLoading = ref(false)
const orderTableRef = ref()
```

#### 3.2 数据加载逻辑
```typescript
/**
 * 加载客户订单列表
 */
async function loadOrderList() {
  if (!props.customer?.customerId) return
  
  orderLoading.value = true
  try {
    const response = await getOrderList({
      customerId: props.customer.customerId,
      current: 1,
      size: 100, // 显示前100条订单
    })
    orderList.value = response.rows || []
  } catch (error) {
    console.error('加载订单列表失败:', error)
    orderList.value = []
  } finally {
    orderLoading.value = false
  }
}

/**
 * 刷新订单列表
 */
function refreshOrderList() {
  loadOrderList()
}
```

#### 3.3 Tab切换监听
```typescript
// 监听Tab切换，延迟加载数据
watch(activeTab, async (newTab) => {
  if (newTab === 'follow' && props.customer) {
    // 加载跟进记录
    await new Promise(resolve => setTimeout(resolve, 100))
    await loadFollowRecords()
  } else if (newTab === 'orders' && props.customer) {
    // 加载订单数据
    await new Promise(resolve => setTimeout(resolve, 100))
    await loadOrderList()
  }
})
```

#### 3.4 模板集成
```vue
<!-- 客户订单Tab -->
<TabPane key="orders" tab="客户订单">
  <template #tab>
    <span>
      <ShoppingCartOutlined />
      客户订单
    </span>
  </template>

  <div class="min-h-[600px]">
    <!-- 订单统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <Card class="text-center">
        <div class="text-2xl font-bold text-blue-600">{{ customer.totalOrderCount || 0 }}</div>
        <div class="text-sm text-gray-600 mt-1">总订单数</div>
      </Card>
      <Card class="text-center">
        <div class="text-2xl font-bold text-green-600">{{ formatAmount(customer.totalOrderAmount) }}</div>
        <div class="text-sm text-gray-600 mt-1">总订单金额</div>
      </Card>
      <Card class="text-center">
        <div class="text-2xl font-bold text-orange-600">{{ customer.firstOrderDate || '-' }}</div>
        <div class="text-sm text-gray-600 mt-1">首次下单</div>
      </Card>
      <Card class="text-center">
        <div class="text-2xl font-bold text-purple-600">{{ customer.lastOrderDate || '-' }}</div>
        <div class="text-sm text-gray-600 mt-1">最近下单</div>
      </Card>
    </div>

    <!-- 订单列表 -->
    <Card class="shadow-sm">
      <template #title>
        <div class="flex items-center justify-between">
          <span>订单列表</span>
          <div class="flex items-center space-x-2">
            <Button 
              type="primary" 
              size="small" 
              @click="refreshOrderList"
              :loading="orderLoading"
            >
              <ReloadOutlined />
              刷新
            </Button>
          </div>
        </div>
      </template>

      <OrderTable
        ref="orderTableRef"
        :data="orderList"
        :loading="orderLoading"
        :height="400"
      />
    </Card>
  </div>
</TabPane>
```

## 📊 数据流程

### 1. 数据加载流程
```
用户切换到订单Tab 
    ↓
触发watch监听 
    ↓
调用loadOrderList() 
    ↓
调用getOrderList API 
    ↓
传入customerId参数 
    ↓
后端返回TableDataInfo<CrmOrderVo> 
    ↓
提取response.rows数据 
    ↓
更新orderList响应式数据 
    ↓
OrderTable组件自动更新显示
```

### 2. API调用参数
```typescript
{
  customerId: props.customer.customerId,  // 客户ID
  current: 1,                            // 当前页
  size: 100,                             // 每页大小
}
```

### 3. 后端API接口
- **接口路径**：`GET /crm/order/list`
- **权限要求**：`crm:order:list`
- **返回格式**：`TableDataInfo<CrmOrderVo>`
- **数据结构**：
  ```typescript
  {
    rows: CrmOrder[],    // 订单列表
    total: number,       // 总数量
    code: number,        // 状态码
    msg: string          // 消息
  }
  ```

## 🎨 界面效果

### 1. 订单统计卡片
- **总订单数**：显示客户的订单总数量
- **总订单金额**：显示客户的订单总金额
- **首次下单**：显示客户第一次下单日期
- **最近下单**：显示客户最后一次下单日期

### 2. 订单列表表格
- **订单号**：固定在左侧，便于查看
- **下单日期**：格式化为YYYY-MM-DD
- **订单状态**：使用字典渲染，带颜色标识
- **订单类型**：使用字典渲染
- **订单总金额**：格式化为美元格式
- **付款方式**：使用字典渲染
- **关联订单号**：显示相关订单
- **创建人**：显示订单创建者
- **创建时间**：格式化显示

### 3. 交互功能
- **自动加载**：切换到订单Tab时自动加载数据
- **手动刷新**：点击刷新按钮重新加载数据
- **加载状态**：显示加载动画
- **空状态**：无订单时显示友好提示

## ⚡ 性能优化

### 1. 延迟加载
- 只有切换到订单Tab时才加载数据
- 避免不必要的API调用

### 2. 数据缓存
- 切换Tab后数据保持在内存中
- 手动刷新时重新加载

### 3. 分页控制
- 默认加载前100条订单
- 可根据需要调整分页大小

## 🔄 扩展功能

### 1. 可扩展的功能点
- **分页支持**：添加分页组件
- **搜索过滤**：按订单状态、日期范围筛选
- **排序功能**：按金额、日期排序
- **导出功能**：导出订单列表
- **订单详情**：点击订单号查看详情

### 2. 配置扩展
- **列显示控制**：可配置显示/隐藏列
- **列宽调整**：支持拖拽调整列宽
- **自定义渲染**：支持自定义列渲染

## ✅ 完成清单

- [x] 创建订单列表配置 (customerOrderColumns)
- [x] 创建OrderTable组件
- [x] 集成到客户详情页面
- [x] 实现数据加载逻辑
- [x] 添加Tab切换监听
- [x] 实现刷新功能
- [x] 处理加载状态
- [x] 处理空状态
- [x] 错误处理
- [x] 类型安全

## 🎉 总结

客户详情订单列表功能已完全实现，提供了：

1. **完整的订单展示**：显示客户所有订单信息
2. **统一的技术栈**：使用BasicTable和useVbenVxeGrid
3. **良好的用户体验**：自动加载、手动刷新、友好提示
4. **可维护的代码**：配置外部化、组件化设计
5. **类型安全**：完整的TypeScript类型定义

功能已经可以正常使用，用户可以在客户详情页面查看该客户的所有订单记录！🎊
