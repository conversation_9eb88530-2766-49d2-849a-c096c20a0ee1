-- ================================================================
-- CRM订单表添加订单类型字段 - 2025年7月24日
-- ================================================================

-- 1. 为 crm_order 表添加 order_type 字段
ALTER TABLE `crm_order`
ADD COLUMN `order_type` varchar(20) NOT NULL DEFAULT 'offline' COMMENT '订单类型（online:线上订单, offline:线下订单）'
AFTER `order_status`;

-- 2. 添加字段注释
ALTER TABLE `crm_order`
MODIFY COLUMN `order_type` varchar(20) NOT NULL DEFAULT 'offline' COMMENT '订单类型（online:线上订单, offline:线下订单）';

-- 3. 创建订单类型字典数据
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1854789632145678336, 'CRM订单类型', 'crm_order_type', 103, 1, NOW(), 1, NOW(), 'CRM订单类型字典');

-- 4. 插入订单类型字典数据
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1854789632145678337, 1, '线上订单', 'online', 'crm_order_type', '', 'primary', 'N', 103, 1, NOW(), 1, NOW(), '通过线上渠道产生的订单'),
(1854789632145678338, 2, '线下订单', 'offline', 'crm_order_type', '', 'success', 'Y', 103, 1, NOW(), 1, NOW(), '通过线下渠道产生的订单');

-- 5. 为现有数据设置默认值（可选，如果需要为现有订单设置特定类型）
-- UPDATE `crm_order` SET `order_type` = 'offline' WHERE `order_type` IS NULL OR `order_type` = '';

-- 6. 添加索引以提高查询性能
CREATE INDEX `idx_crm_order_type` ON `crm_order` (`order_type`);

-- 7. 添加复合索引（订单状态 + 订单类型）
CREATE INDEX `idx_crm_order_status_type` ON `crm_order` (`order_status`, `order_type`);

-- ================================================================
-- 验证SQL
-- ================================================================

-- 验证字段是否添加成功
SELECT
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM
    INFORMATION_SCHEMA.COLUMNS
WHERE
    TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'crm_order'
    AND COLUMN_NAME = 'order_type';

-- 验证字典数据是否插入成功
SELECT
    dt.dict_name,
    dt.dict_type,
    dd.dict_label,
    dd.dict_value,
    dd.dict_sort
FROM
    sys_dict_type dt
    LEFT JOIN sys_dict_data dd ON dt.dict_type = dd.dict_type
WHERE
    dt.dict_type = 'crm_order_type'
ORDER BY
    dd.dict_sort;

-- 验证索引是否创建成功
SHOW INDEX FROM `crm_order` WHERE `Key_name` IN ('idx_crm_order_type', 'idx_crm_order_status_type');

-- ================================================================
-- 回滚SQL（如果需要回滚）
-- ================================================================

/*
-- 删除索引
DROP INDEX `idx_crm_order_status_type` ON `crm_order`;
DROP INDEX `idx_crm_order_type` ON `crm_order`;

-- 删除字典数据
DELETE FROM `sys_dict_data` WHERE `dict_code` IN (1854789632145678337, 1854789632145678338);
DELETE FROM `sys_dict_type` WHERE `dict_id` = 1854789632145678336;

-- 删除字段
ALTER TABLE `crm_order` DROP COLUMN `order_type`;
*/
