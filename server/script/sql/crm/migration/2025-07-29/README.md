# CRM客户表字段扩展 - 2025年7月29日

## 📋 功能概述

本次迁移为CRM客户表新增三个重要字段，提升客户管理功能：

1. **是否为新客户** (`is_new_customer`)
2. **客户说明** (`customer_description`) 
3. **系统星级评分** (`system_star_rating`)

## 🗂️ 文件说明

### SQL执行文件

| 文件名 | 说明 | 推荐使用 |
|--------|------|----------|
| `01.add_customer_new_fields_simple.sql` | **简化版本** - 直接执行ALTER语句 | ✅ **推荐** |
| `01.add_customer_new_fields.sql` | 动态版本 - 检查字段是否存在 | 备选方案 |

### 数据字典文件

| 文件名 | 说明 |
|--------|------|
| `03.add_customer_dict_data.sql` | 数据字典配置（使用雪花ID） |

### 社媒账号扩展

| 文件名 | 说明 |
|--------|------|
| `04.add_customer_social_media_table.sql` | 创建客户社媒账号关联表 |

### 文档文件

| 文件名 | 说明 |
|--------|------|
| `02.customer_new_fields_documentation.md` | 详细功能说明文档 |
| `README.md` | 本文件 - 使用说明 |

## 🚀 执行步骤

### 第一步：执行字段扩展SQL

**推荐使用简化版本：**

```sql
-- 执行客户表字段扩展
source server/script/sql/crm/migration/2025-07-29/01.add_customer_new_fields_simple.sql;
```

**如果遇到字段已存在错误，可以使用动态版本：**

```sql
-- 执行动态检查版本
source server/script/sql/crm/migration/2025-07-29/01.add_customer_new_fields.sql;
```

### 第二步：执行数据字典配置

```sql
-- 执行数据字典配置
source server/script/sql/crm/migration/2025-07-29/03.add_customer_dict_data.sql;
```

### 第三步：创建社媒账号表（可选）

```sql
-- 创建客户社媒账号关联表
source server/script/sql/crm/migration/2025-07-29/04.add_customer_social_media_table.sql;
```

## ⚠️ 注意事项

### 执行前检查

1. **备份数据库**：执行前请务必备份数据库
2. **检查表结构**：确认`crm_customer`表存在且包含`is_public`字段
3. **权限确认**：确保数据库用户有ALTER TABLE权限

### 常见问题

#### 问题1：字段已存在错误

```
ERROR 1060 (42S21): Duplicate column name 'is_new_customer'
```

**解决方案**：
- 字段已存在，可以忽略此错误
- 或者使用动态检查版本的SQL文件

#### 问题2：找不到参考字段

```
ERROR 1054 (42S22): Unknown column 'customer_level' in 'crm_customer'
```

**解决方案**：
- 已修复，现在使用`is_public`字段作为参考位置
- 使用最新版本的SQL文件

#### 问题3：数据类型不匹配

**解决方案**：
- 检查现有表结构是否与预期一致
- 必要时调整SQL中的数据类型定义

## 🔍 验证方法

### 检查表结构

```sql
-- 查看表结构变更
DESCRIBE `crm_customer`;

-- 检查新增字段
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'crm_customer' 
  AND COLUMN_NAME IN ('is_new_customer', 'customer_description', 'system_star_rating');
```

### 检查数据初始化

```sql
-- 查看新客户统计
SELECT 
    is_new_customer,
    CASE is_new_customer 
        WHEN '1' THEN '新客户' 
        WHEN '0' THEN '老客户' 
        ELSE '未知' 
    END as customer_type,
    COUNT(*) as count
FROM `crm_customer` 
WHERE del_flag = '0'
GROUP BY is_new_customer;

-- 查看系统星级评分分布
SELECT 
    system_star_rating,
    CONCAT(system_star_rating, '星') as rating_label,
    COUNT(*) as count
FROM `crm_customer` 
WHERE del_flag = '0'
GROUP BY system_star_rating
ORDER BY system_star_rating;
```

### 检查索引创建

```sql
-- 查看索引
SHOW INDEX FROM `crm_customer` WHERE Key_name IN ('idx_is_new_customer', 'idx_system_star_rating');
```

## 🔄 回滚方案

如需回滚，请按以下顺序执行：

```sql
-- 1. 删除索引
DROP INDEX `idx_is_new_customer` ON `crm_customer`;
DROP INDEX `idx_system_star_rating` ON `crm_customer`;

-- 2. 删除字段（谨慎执行）
ALTER TABLE `crm_customer` DROP COLUMN `customer_description`;
ALTER TABLE `crm_customer` DROP COLUMN `system_star_rating`;
ALTER TABLE `crm_customer` DROP COLUMN `is_new_customer`;

-- 3. 删除数据字典（如果需要）
DELETE FROM `sys_dict_data` WHERE dict_type IN ('crm_is_new_customer', 'crm_system_star_rating');
DELETE FROM `sys_dict_type` WHERE dict_id IN (1854078945234567300, 1854078945234567310);
```

## 📞 技术支持

如遇到问题，请检查：

1. 数据库版本兼容性
2. 表结构是否与预期一致
3. 用户权限是否充足
4. 是否有其他进程锁定表

执行成功后，前端应用将自动支持新增的字段功能。
