-- =============================================
-- CRM客户新增字段数据字典SQL - 2025年7月29日
-- =============================================
-- 功能说明：为CRM客户新增字段添加数据字典配置
-- 字典类型：
-- - CRM_IS_NEW_CUSTOMER: 是否新客户
-- - CRM_SYSTEM_STAR_RATING: 系统星级评分
-- 使用雪花算法ID确保全局唯一性
-- =============================================

-- 1. 是否新客户字典类型
INSERT INTO `sys_dict_type` (
    `dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, 
    `update_by`, `update_time`, `remark`
) VALUES (
    1854078945234567300, 'CRM是否新客户', 'crm_is_new_customer', 103, 1, NOW(), 
    1, NOW(), 'CRM客户是否为新客户字典'
);

-- 是否新客户字典数据
INSERT INTO `sys_dict_data` (
    `dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, 
    `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, 
    `update_by`, `update_time`, `remark`
) VALUES 
(1854078945234567301, 1, '新客户', '1', 'crm_is_new_customer', '', 'success', 'Y', 103, 1, NOW(), 1, NOW(), '新客户'),
(1854078945234567302, 2, '老客户', '0', 'crm_is_new_customer', '', 'primary', 'N', 103, 1, NOW(), 1, NOW(), '老客户');

-- 2. 系统星级评分字典类型
INSERT INTO `sys_dict_type` (
    `dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, 
    `update_by`, `update_time`, `remark`
) VALUES (
    1854078945234567310, 'CRM系统星级评分', 'crm_system_star_rating', 103, 1, NOW(), 
    1, NOW(), 'CRM客户系统星级评分字典'
);

-- 系统星级评分字典数据
INSERT INTO `sys_dict_data` (
    `dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, 
    `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, 
    `update_by`, `update_time`, `remark`
) VALUES 
(1854078945234567311, 1, '1星', '1', 'crm_system_star_rating', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), '1星客户'),
(1854078945234567312, 2, '2星', '2', 'crm_system_star_rating', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '2星客户'),
(1854078945234567313, 3, '3星', '3', 'crm_system_star_rating', '', 'info', 'Y', 103, 1, NOW(), 1, NOW(), '3星客户'),
(1854078945234567314, 4, '4星', '4', 'crm_system_star_rating', '', 'primary', 'N', 103, 1, NOW(), 1, NOW(), '4星客户'),
(1854078945234567315, 5, '5星', '5', 'crm_system_star_rating', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '5星客户');

-- =============================================
-- 验证SQL
-- =============================================
-- 查看字典类型创建情况
-- SELECT dict_id, dict_name, dict_type 
-- FROM `sys_dict_type` 
-- WHERE dict_id IN (1854078945234567300, 1854078945234567310);

-- 查看字典数据创建情况
-- SELECT dict_code, dict_label, dict_value, dict_type 
-- FROM `sys_dict_data` 
-- WHERE dict_type IN ('crm_is_new_customer', 'crm_system_star_rating')
-- ORDER BY dict_type, dict_sort;

-- =============================================
-- 回滚SQL（如需回滚，请谨慎执行）
-- =============================================
-- 删除字典数据
-- DELETE FROM `sys_dict_data` WHERE dict_type IN ('crm_is_new_customer', 'crm_system_star_rating');

-- 删除字典类型
-- DELETE FROM `sys_dict_type` WHERE dict_id IN (1854078945234567300, 1854078945234567310);
