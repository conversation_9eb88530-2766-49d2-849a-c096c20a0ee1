-- =============================================
-- CRM客户表新增字段SQL（简化版本） - 2025年7月29日
-- =============================================
-- 功能说明：为CRM客户表新增"是否为新客户"、"客户说明"和"系统星级评分"字段
-- 注意：如果字段已存在，请忽略相应的错误信息
-- =============================================

-- 1. 新增是否为新客户字段（添加到is_public字段之后）
ALTER TABLE `crm_customer`
ADD COLUMN `is_new_customer` CHAR(1) NOT NULL DEFAULT '1' COMMENT '是否为新客户（1=是, 0=否）' AFTER `is_public`;

-- 2. 新增客户说明字段（添加到is_new_customer字段之后）
ALTER TABLE `crm_customer`
ADD COLUMN `customer_description` VARCHAR(1000) NULL COMMENT '客户说明' AFTER `is_new_customer`;

-- 3. 新增系统星级评分字段（添加到customer_description字段之后）
ALTER TABLE `crm_customer`
ADD COLUMN `system_star_rating` INT(1) DEFAULT 1 COMMENT '系统星级评分（1-5星）' AFTER `customer_description`;

-- =============================================
-- 数据初始化
-- =============================================
-- 将现有客户默认设置为老客户（根据业务需求调整）
UPDATE `crm_customer` 
SET `is_new_customer` = '0' 
WHERE `create_time` < DATE_SUB(NOW(), INTERVAL 30 DAY)
  AND `is_new_customer` = '1';

-- 为系统星级评分设置默认值（如果为NULL）
UPDATE `crm_customer` 
SET `system_star_rating` = 1 
WHERE `system_star_rating` IS NULL;

-- =============================================
-- 索引优化（可选）
-- =============================================
-- 为是否新客户字段添加索引，便于查询统计
CREATE INDEX `idx_is_new_customer` ON `crm_customer` (`is_new_customer`);

-- 为系统星级评分字段添加索引
CREATE INDEX `idx_system_star_rating` ON `crm_customer` (`system_star_rating`);

-- =============================================
-- 验证SQL
-- =============================================
-- 查看表结构变更
-- DESCRIBE `crm_customer`;

-- 查看新客户统计
-- SELECT 
--     is_new_customer,
--     CASE is_new_customer 
--         WHEN '1' THEN '新客户' 
--         WHEN '0' THEN '老客户' 
--         ELSE '未知' 
--     END as customer_type,
--     COUNT(*) as count
-- FROM `crm_customer` 
-- WHERE del_flag = '0'
-- GROUP BY is_new_customer;

-- 查看系统星级评分分布
-- SELECT 
--     system_star_rating,
--     CONCAT(system_star_rating, '星') as rating_label,
--     COUNT(*) as count
-- FROM `crm_customer` 
-- WHERE del_flag = '0'
-- GROUP BY system_star_rating
-- ORDER BY system_star_rating;

-- =============================================
-- 回滚SQL（如需回滚，请谨慎执行）
-- =============================================
-- 删除索引
-- DROP INDEX `idx_is_new_customer` ON `crm_customer`;
-- DROP INDEX `idx_system_star_rating` ON `crm_customer`;

-- 删除字段
-- ALTER TABLE `crm_customer` DROP COLUMN `customer_description`;
-- ALTER TABLE `crm_customer` DROP COLUMN `system_star_rating`;
-- ALTER TABLE `crm_customer` DROP COLUMN `is_new_customer`;
