-- =============================================
-- CRM客户社媒账号表创建SQL - 2025年7月29日
-- =============================================
-- 功能说明：创建客户社媒账号关联表，支持一个客户多个社媒账号
-- 表名：crm_customer_social_media
-- 关联：客户表(crm_customer) 一对多 社媒账号表
-- =============================================

-- 1. 创建客户社媒账号表
CREATE TABLE `crm_customer_social_media` (
    `social_media_id` BIGINT(20) NOT NULL COMMENT '社媒账号ID',
    `customer_id` BIGINT(20) NOT NULL COMMENT '客户ID',
    `social_media_type` VARCHAR(50) NOT NULL COMMENT '社媒类型',
    `social_media_account` VARCHAR(200) NOT NULL COMMENT '社媒账号',
    `is_primary` CHAR(1) DEFAULT '0' COMMENT '是否主要账号（1=是, 0=否）',
    `sort_order` INT(3) DEFAULT 1 COMMENT '排序顺序',
    `status` CHAR(1) DEFAULT '1' COMMENT '状态（1=正常, 0=停用）',
    `remark` VARCHAR(500) DEFAULT NULL COMMENT '备注',
    `create_dept` BIGINT(20) DEFAULT NULL COMMENT '创建部门',
    `create_by` BIGINT(20) DEFAULT NULL COMMENT '创建者',
    `create_time` DATETIME DEFAULT NULL COMMENT '创建时间',
    `update_by` BIGINT(20) DEFAULT NULL COMMENT '更新者',
    `update_time` DATETIME DEFAULT NULL COMMENT '更新时间',
    `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
    PRIMARY KEY (`social_media_id`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_social_media_type` (`social_media_type`),
    KEY `idx_is_primary` (`is_primary`),
    KEY `idx_status` (`status`),
    KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='CRM客户社媒账号表';

-- 2. 迁移现有数据到新表
INSERT INTO `crm_customer_social_media` (
    `social_media_id`, `customer_id`, `social_media_type`, `social_media_account`, 
    `is_primary`, `sort_order`, `status`, `create_dept`, `create_by`, `create_time`, 
    `update_by`, `update_time`, `del_flag`
)
SELECT 
    -- 使用雪花算法ID，这里用customer_id + 1000000000000000000作为临时ID
    `customer_id` + 1000000000000000000,
    `customer_id`,
    COALESCE(`social_media_type`, 'other'),
    `social_media_account`,
    '1', -- 设为主要账号
    1,   -- 排序为1
    '1', -- 状态正常
    `create_dept`,
    `create_by`,
    `create_time`,
    `update_by`,
    `update_time`,
    `del_flag`
FROM `crm_customer` 
WHERE `social_media_account` IS NOT NULL 
  AND `social_media_account` != '' 
  AND `del_flag` = '0';

-- 3. 备份原有字段（可选，建议先备份再删除）
-- ALTER TABLE `crm_customer` ADD COLUMN `social_media_account_backup` VARCHAR(200) COMMENT '社媒账号备份';
-- ALTER TABLE `crm_customer` ADD COLUMN `social_media_type_backup` VARCHAR(50) COMMENT '社媒类型备份';
-- UPDATE `crm_customer` SET `social_media_account_backup` = `social_media_account`, `social_media_type_backup` = `social_media_type`;

-- 4. 删除原有社媒字段（谨慎执行，建议先测试）
-- ALTER TABLE `crm_customer` DROP COLUMN `social_media_account`;
-- ALTER TABLE `crm_customer` DROP COLUMN `social_media_type`;

-- =============================================
-- 索引优化
-- =============================================
-- 复合索引：客户ID + 状态 + 删除标志
CREATE INDEX `idx_customer_status_del` ON `crm_customer_social_media` (`customer_id`, `status`, `del_flag`);

-- 复合索引：客户ID + 是否主要 + 删除标志
CREATE INDEX `idx_customer_primary_del` ON `crm_customer_social_media` (`customer_id`, `is_primary`, `del_flag`);

-- =============================================
-- 验证SQL
-- =============================================
-- 查看表结构
-- DESCRIBE `crm_customer_social_media`;

-- 查看数据迁移情况
-- SELECT 
--     COUNT(*) as total_records,
--     COUNT(DISTINCT customer_id) as unique_customers
-- FROM `crm_customer_social_media` 
-- WHERE del_flag = '0';

-- 查看每个客户的社媒账号数量
-- SELECT 
--     customer_id,
--     COUNT(*) as social_media_count
-- FROM `crm_customer_social_media` 
-- WHERE del_flag = '0'
-- GROUP BY customer_id
-- ORDER BY social_media_count DESC
-- LIMIT 10;

-- =============================================
-- 回滚SQL（如需回滚，请谨慎执行）
-- =============================================
-- 恢复原有字段
-- ALTER TABLE `crm_customer` ADD COLUMN `social_media_account` VARCHAR(200) COMMENT '社媒账号';
-- ALTER TABLE `crm_customer` ADD COLUMN `social_media_type` VARCHAR(50) COMMENT '社媒类型';

-- 恢复数据（如果有备份）
-- UPDATE `crm_customer` SET 
--     `social_media_account` = `social_media_account_backup`, 
--     `social_media_type` = `social_media_type_backup`
-- WHERE `social_media_account_backup` IS NOT NULL;

-- 删除社媒账号表
-- DROP TABLE IF EXISTS `crm_customer_social_media`;
