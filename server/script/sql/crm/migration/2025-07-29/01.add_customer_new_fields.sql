-- =============================================
-- CRM客户表新增字段SQL - 2025年7月29日
-- =============================================
-- 功能说明：为CRM客户表新增"是否为新客户"和"客户说明"字段
-- 字段说明：
-- - is_new_customer: 是否为新客户 CHAR(1) (1=是, 0=否)
-- - customer_description: 客户说明 VARCHAR(1000)
-- =============================================

-- 1. 新增是否为新客户字段（检查字段是否存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'crm_customer'
     AND COLUMN_NAME = 'is_new_customer') = 0,
    'ALTER TABLE `crm_customer` ADD COLUMN `is_new_customer` CHAR(1) NOT NULL DEFAULT ''1'' COMMENT ''是否为新客户（1=是, 0=否）'' AFTER `is_public`',
    'SELECT ''字段 is_new_customer 已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 新增客户说明字段（检查字段是否存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'crm_customer'
     AND COLUMN_NAME = 'customer_description') = 0,
    'ALTER TABLE `crm_customer` ADD COLUMN `customer_description` VARCHAR(1000) NULL COMMENT ''客户说明'' AFTER `is_new_customer`',
    'SELECT ''字段 customer_description 已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 新增系统星级评分字段（检查字段是否存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'crm_customer'
     AND COLUMN_NAME = 'system_star_rating') = 0,
    'ALTER TABLE `crm_customer` ADD COLUMN `system_star_rating` INT(1) DEFAULT 1 COMMENT ''系统星级评分（1-5星）'' AFTER `customer_description`',
    'SELECT ''字段 system_star_rating 已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =============================================
-- 数据初始化
-- =============================================
-- 将现有客户默认设置为老客户（根据业务需求调整）
UPDATE `crm_customer` 
SET `is_new_customer` = '0' 
WHERE `create_time` < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- =============================================
-- 索引优化（可选）
-- =============================================
-- 为是否新客户字段添加索引，便于查询统计
CREATE INDEX `idx_is_new_customer` ON `crm_customer` (`is_new_customer`);

-- =============================================
-- 验证SQL
-- =============================================
-- 查看表结构变更
-- DESCRIBE `crm_customer`;

-- 查看新客户统计
-- SELECT 
--     is_new_customer,
--     CASE is_new_customer 
--         WHEN '1' THEN '新客户' 
--         WHEN '0' THEN '老客户' 
--         ELSE '未知' 
--     END as customer_type,
--     COUNT(*) as count
-- FROM `crm_customer` 
-- WHERE del_flag = '0'
-- GROUP BY is_new_customer;

-- =============================================
-- 回滚SQL（如需回滚，请谨慎执行）
-- =============================================
-- 删除索引
-- DROP INDEX `idx_is_new_customer` ON `crm_customer`;

-- 删除字段
-- ALTER TABLE `crm_customer` DROP COLUMN `customer_description`;
-- ALTER TABLE `crm_customer` DROP COLUMN `is_new_customer`;
