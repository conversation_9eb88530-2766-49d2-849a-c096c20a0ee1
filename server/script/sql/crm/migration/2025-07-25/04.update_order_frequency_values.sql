-- 更新下单频率字段值
-- 执行时间：2025-07-25
-- 说明：将现有的下单频率值从high/medium/low更新为weekly/monthly/quarterly/irregular

-- 备份现有数据（可选）
-- CREATE TABLE crm_customer_order_frequency_backup AS 
-- SELECT customer_id, order_frequency FROM crm_customer WHERE del_flag = '0';

-- 更新现有的下单频率值
UPDATE crm_customer 
SET order_frequency = CASE 
    WHEN order_frequency = 'high' THEN 'weekly'
    WHEN order_frequency = 'medium' THEN 'monthly'
    WHEN order_frequency = 'low' THEN 'irregular'
    ELSE 'irregular'
END
WHERE del_flag = '0';

-- 重新计算所有客户的下单频率（基于新的规则）
UPDATE crm_customer c 
SET order_frequency = CASE 
    WHEN c.total_order_count <= 1 THEN 'irregular'
    WHEN c.first_order_date IS NULL OR c.last_order_date IS NULL THEN 'irregular'
    WHEN DATEDIFF(c.last_order_date, c.first_order_date) = 0 THEN 'irregular'
    WHEN (DATEDIFF(c.last_order_date, c.first_order_date) / (c.total_order_count - 1)) <= 10 THEN 'weekly'
    WHEN (DATEDIFF(c.last_order_date, c.first_order_date) / (c.total_order_count - 1)) <= 35 THEN 'monthly'
    WHEN (DATEDIFF(c.last_order_date, c.first_order_date) / (c.total_order_count - 1)) <= 100 THEN 'quarterly'
    ELSE 'irregular'
END
WHERE c.del_flag = '0' AND c.total_order_count > 1;

-- 验证更新结果
SELECT 
    order_frequency,
    COUNT(*) as customer_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM crm_customer WHERE del_flag = '0'), 2) as percentage
FROM crm_customer 
WHERE del_flag = '0'
GROUP BY order_frequency
ORDER BY 
    CASE order_frequency 
        WHEN 'weekly' THEN 1
        WHEN 'monthly' THEN 2
        WHEN 'quarterly' THEN 3
        WHEN 'irregular' THEN 4
        ELSE 5
    END;

-- 显示各频率客户的平均订单数量和金额
SELECT 
    order_frequency,
    COUNT(*) as customer_count,
    ROUND(AVG(total_order_count), 2) as avg_order_count,
    ROUND(AVG(total_order_amount), 2) as avg_order_amount,
    MIN(total_order_count) as min_order_count,
    MAX(total_order_count) as max_order_count
FROM crm_customer 
WHERE del_flag = '0' AND total_order_count > 0
GROUP BY order_frequency
ORDER BY 
    CASE order_frequency 
        WHEN 'weekly' THEN 1
        WHEN 'monthly' THEN 2
        WHEN 'quarterly' THEN 3
        WHEN 'irregular' THEN 4
        ELSE 5
    END;

-- 显示每个频率的示例客户
SELECT 
    order_frequency,
    customer_name,
    total_order_count,
    total_order_amount,
    DATEDIFF(last_order_date, first_order_date) as days_span,
    CASE 
        WHEN total_order_count > 1 THEN 
            ROUND(DATEDIFF(last_order_date, first_order_date) / (total_order_count - 1), 1)
        ELSE NULL 
    END as avg_days_between_orders
FROM crm_customer 
WHERE del_flag = '0' 
  AND total_order_count > 0
  AND order_frequency IN ('weekly', 'monthly', 'quarterly', 'irregular')
ORDER BY 
    CASE order_frequency 
        WHEN 'weekly' THEN 1
        WHEN 'monthly' THEN 2
        WHEN 'quarterly' THEN 3
        WHEN 'irregular' THEN 4
        ELSE 5
    END,
    total_order_amount DESC
LIMIT 20;
