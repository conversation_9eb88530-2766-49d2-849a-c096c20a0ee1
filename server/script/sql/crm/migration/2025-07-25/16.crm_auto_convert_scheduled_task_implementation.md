# CRM客户自动转换定时任务实现 - 2025年7月25日

## 📋 功能概述

实现了每天晚上2点自动将15天未跟进的客户转为公海客户的定时任务功能。利用项目现有的Spring Scheduling和SnailJob框架，提供了完整的定时任务解决方案。

## 🎯 主要功能

### 1. 定时任务核心功能
- **执行时间**：每天凌晨2点自动执行
- **业务逻辑**：调用现有的`autoConvertToPublicCustomers`方法
- **默认阈值**：15天未跟进的客户
- **可配置性**：支持通过配置文件自定义参数

### 2. 任务管理功能
- **手动触发**：支持手动触发定时任务
- **自定义参数**：支持自定义未跟进天数阈值
- **状态查询**：查看定时任务配置和状态
- **日志记录**：详细的执行日志和异常处理

## 🔧 技术实现

### 1. 定时任务类
**文件**: `CrmCustomerAutoConvertTask.java`

```java
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "crm.task.auto-convert", name = "enabled", havingValue = "true", matchIfMissing = true)
public class CrmCustomerAutoConvertTask {

    private final ICrmCustomerService crmCustomerService;
    private final CrmTaskProperties crmTaskProperties;

    /**
     * 自动将未跟进的客户转为公海客户
     * 默认每天凌晨2点执行，可通过配置文件修改
     */
    @Scheduled(cron = "${crm.task.auto-convert.cron:0 0 2 * * ?}")
    public void autoConvertToPublicCustomers() {
        log.info("开始执行定时任务：自动转换公海客户");
        
        try {
            // 从配置中获取未跟进天数阈值
            int days = crmTaskProperties.getAutoConvert().getDays();
            
            // 调用服务方法，将指定天数未跟进的客户转为公海客户
            int convertedCount = crmCustomerService.autoConvertToPublicCustomers(days);
            
            if (convertedCount > 0) {
                log.info("定时任务执行成功：共转换 {} 个客户为公海客户（未跟进天数阈值：{} 天）", convertedCount, days);
            } else {
                log.info("定时任务执行完成：没有需要转换的客户（未跟进天数阈值：{} 天）", days);
            }
            
        } catch (Exception e) {
            log.error("定时任务执行失败：自动转换公海客户出现异常", e);
        }
    }
}
```

### 2. 定时任务配置类
**文件**: `CrmScheduleConfig.java`

```java
@Slf4j
@Configuration
@EnableScheduling
@ConditionalOnProperty(prefix = "crm.schedule", name = "enabled", havingValue = "true", matchIfMissing = true)
public class CrmScheduleConfig implements SchedulingConfigurer {

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        // 设置定时任务使用的线程池
        taskRegistrar.setScheduler(Executors.newScheduledThreadPool(2, r -> {
            Thread thread = new Thread(r, "crm-schedule-");
            thread.setDaemon(true);
            return thread;
        }));
        
        log.info("CRM定时任务配置初始化完成");
    }
}
```

### 3. 配置属性类
**文件**: `CrmTaskProperties.java`

```java
@Data
@Component
@ConfigurationProperties(prefix = "crm.task")
public class CrmTaskProperties {

    /**
     * 是否启用CRM定时任务
     */
    private boolean enabled = true;

    /**
     * 客户自动转换配置
     */
    private AutoConvert autoConvert = new AutoConvert();

    @Data
    public static class AutoConvert {
        
        /**
         * 是否启用自动转换公海客户任务
         */
        private boolean enabled = true;
        
        /**
         * 未跟进天数阈值，默认15天
         */
        private int days = 15;
        
        /**
         * 定时任务执行时间（cron表达式），默认每天凌晨2点
         */
        private String cron = "0 0 2 * * ?";
        
        /**
         * 任务描述
         */
        private String description = "自动将超过指定天数未跟进的客户转为公海客户";
    }
}
```

### 4. 任务管理控制器
**文件**: `CrmTaskController.java`

```java
@RestController
@RequestMapping("/crm/task")
@Tag(name = "CRM定时任务管理", description = "CRM定时任务管理")
public class CrmTaskController extends BaseController {

    /**
     * 手动触发自动转换公海客户任务
     */
    @SaCheckPermission("crm:task:execute")
    @PostMapping("/auto-convert/trigger")
    public R<String> triggerAutoConvert() {
        try {
            int convertedCount = autoConvertTask.manualTriggerAutoConvert();
            String message = String.format("手动触发成功，共转换 %d 个客户为公海客户", convertedCount);
            return R.ok(message);
        } catch (Exception e) {
            return R.fail("手动触发失败：" + e.getMessage());
        }
    }

    /**
     * 使用自定义天数触发自动转换任务
     */
    @PostMapping("/auto-convert/trigger-with-days")
    public R<String> triggerAutoConvertWithDays(@RequestParam(value = "days", defaultValue = "15") int days) {
        // 实现自定义天数的转换逻辑
    }
}
```

## 📊 配置说明

### 1. 应用配置文件
**文件**: `application.yml`

```yaml
crm:
  task:
    # 是否启用CRM定时任务
    enabled: true
    
    # 客户自动转换配置
    auto-convert:
      # 是否启用自动转换公海客户任务
      enabled: true
      
      # 未跟进天数阈值，默认15天
      days: 15
      
      # 定时任务执行时间（cron表达式）
      # 默认每天凌晨2点执行：0 0 2 * * ?
      cron: "0 0 2 * * ?"
      
      # 任务描述
      description: "自动将超过指定天数未跟进的客户转为公海客户"

# CRM定时任务调度配置
crm:
  schedule:
    # 是否启用定时任务调度
    enabled: true
```

### 2. Cron表达式说明

| 时间 | Cron表达式 | 说明 |
|------|-----------|------|
| 每天凌晨2点 | `0 0 2 * * ?` | 默认配置 |
| 每天凌晨1点 | `0 0 1 * * ?` | 提前1小时 |
| 每天凌晨3点 | `0 0 3 * * ?` | 延后1小时 |
| 每周日凌晨2点 | `0 0 2 ? * SUN` | 每周执行一次 |
| 每月1号凌晨2点 | `0 0 2 1 * ?` | 每月执行一次 |

## 🔄 执行流程

### 1. 自动执行流程
```
定时触发 → 读取配置参数 → 调用业务方法 → 记录执行结果 → 异常处理
```

### 2. 手动执行流程
```
API调用 → 权限验证 → 执行任务 → 返回结果 → 记录日志
```

### 3. 业务逻辑流程
```
查询未跟进客户 → 批量转换为公海客户 → 清空业务员信息 → 更新客户状态 → 统计转换数量
```

## 📁 文件清单

### 新增文件
- `server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/task/CrmCustomerAutoConvertTask.java`
- `server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/config/CrmScheduleConfig.java`
- `server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/config/properties/CrmTaskProperties.java`
- `server/lookah-business/lookah-crm/lookah-crm-api/src/main/java/com/imhuso/crm/controller/admin/CrmTaskController.java`

### 配置文件
- `server/script/sql/crm/migration/2025-07-25/15.crm_task_config_example.yml` - 配置示例

### 文档
- `server/script/sql/crm/migration/2025-07-25/16.crm_auto_convert_scheduled_task_implementation.md` - 实现文档

## 🎯 特性优势

### 1. 高可配置性
- **开关控制**：可以通过配置文件启用/禁用任务
- **参数可调**：未跟进天数阈值可配置
- **时间灵活**：执行时间通过cron表达式配置

### 2. 完善的监控
- **详细日志**：记录任务执行过程和结果
- **异常处理**：捕获并记录异常信息
- **状态查询**：提供API查询任务状态

### 3. 管理便利
- **手动触发**：支持手动执行任务进行测试
- **参数调试**：支持自定义参数执行
- **权限控制**：集成系统权限管理

### 4. 技术先进
- **Spring Scheduling**：使用Spring原生定时任务框架
- **条件装配**：支持条件化装配，避免不必要的资源消耗
- **线程池管理**：独立的线程池，不影响主业务

## ✅ 验证方法

### 1. 功能验证
- 检查定时任务是否按时执行
- 验证客户转换逻辑是否正确
- 确认日志记录是否完整

### 2. 配置验证
- 测试不同的cron表达式
- 验证配置参数的生效
- 检查开关控制是否有效

### 3. API验证
- 测试手动触发接口
- 验证状态查询接口
- 检查权限控制是否正常

## 🎉 总结

成功实现了CRM客户自动转换定时任务功能：

1. **定时执行**：每天凌晨2点自动执行，将15天未跟进客户转为公海客户
2. **高度可配置**：支持通过配置文件自定义执行时间和参数
3. **完善监控**：提供详细的日志记录和状态查询
4. **管理便利**：支持手动触发和参数调试
5. **技术规范**：使用Spring标准定时任务框架，集成现有架构

现在系统可以自动维护客户的公海状态，提高CRM系统的自动化管理水平！🎊
