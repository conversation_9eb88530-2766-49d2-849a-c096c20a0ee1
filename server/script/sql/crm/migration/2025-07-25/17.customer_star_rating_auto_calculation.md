# 客户星级自动计算功能 - 2025年7月25日

## 📋 功能概述

实现了客户每次添加、编辑订单时根据下单总金额、下单数量自动计算客户星级的功能。系统会根据客户的订单统计数据（订单总数、订单总金额、平均订单金额、最大单笔订单金额）综合评分，自动更新客户的星级评定。

## 🎯 主要功能

### 1. 自动触发机制
- **新增订单时**：自动计算并更新客户星级
- **编辑订单时**：重新计算并更新客户星级
- **实时更新**：订单操作完成后立即更新星级

### 2. 星级计算标准
基于四个维度的综合评分系统（总分100分）：

#### 2.1 订单总数评分（最高30分）
| 订单数量 | 得分 | 说明 |
|---------|------|------|
| ≥50单 | 30分 | 超级客户 |
| 20-49单 | 25分 | 重要客户 |
| 10-19单 | 20分 | 优质客户 |
| 5-9单 | 15分 | 稳定客户 |
| 2-4单 | 10分 | 普通客户 |
| 1单 | 5分 | 新客户 |

#### 2.2 订单总金额评分（最高40分）
| 总金额 | 得分 | 说明 |
|--------|------|------|
| ≥10万 | 40分 | 顶级客户 |
| 5-10万 | 35分 | 高价值客户 |
| 2-5万 | 30分 | 中高价值客户 |
| 1-2万 | 25分 | 中等价值客户 |
| 5千-1万 | 20分 | 一般价值客户 |
| 1千-5千 | 15分 | 低价值客户 |
| <1千 | 10分 | 微价值客户 |

#### 2.3 平均订单金额评分（最高20分）
| 平均金额 | 得分 | 说明 |
|---------|------|------|
| ≥5千 | 20分 | 高客单价 |
| 2-5千 | 15分 | 中高客单价 |
| 1-2千 | 12分 | 中等客单价 |
| 500-1千 | 8分 | 低客单价 |
| <500 | 5分 | 微客单价 |

#### 2.4 最大单笔订单金额评分（最高10分）
| 最大单笔 | 得分 | 说明 |
|---------|------|------|
| ≥1万 | 10分 | 大单能力强 |
| 5千-1万 | 8分 | 大单能力中等 |
| 2-5千 | 6分 | 大单能力一般 |
| 1-2千 | 4分 | 大单能力弱 |
| <1千 | 2分 | 无大单能力 |

### 3. 星级分级标准
| 星级 | 总分范围 | 说明 |
|------|---------|------|
| ⭐⭐⭐⭐⭐ (5星) | 90-100分 | 顶级客户，最高优先级 |
| ⭐⭐⭐⭐ (4星) | 75-89分 | 重要客户，高优先级 |
| ⭐⭐⭐ (3星) | 60-74分 | 优质客户，中等优先级 |
| ⭐⭐ (2星) | 40-59分 | 普通客户，一般优先级 |
| ⭐ (1星) | 0-39分 | 新客户或低价值客户 |

## 🔧 技术实现

### 1. 数据访问层

#### 1.1 订单汇总统计VO
**文件**: `CrmCustomerOrderSummaryVo.java`

```java
@Data
public class CrmCustomerOrderSummaryVo implements Serializable {
    /**
     * 订单总数
     */
    private Integer totalOrderCount;

    /**
     * 订单总金额
     */
    private BigDecimal totalOrderAmount;

    /**
     * 平均订单金额
     */
    private BigDecimal avgOrderAmount;

    /**
     * 最大单笔订单金额
     */
    private BigDecimal maxOrderAmount;
}
```

#### 1.2 Mapper查询方法
**文件**: `CrmCustomerMapper.java`

```java
/**
 * 查询客户订单金额和数量统计（用于计算星级）
 */
CrmCustomerOrderSummaryVo selectOrderSummaryByCustomerId(@Param("customerId") Long customerId);
```

#### 1.3 SQL查询语句
**文件**: `CrmCustomerMapper.xml`

```xml
<select id="selectOrderSummaryByCustomerId" parameterType="java.lang.Long" 
        resultType="com.imhuso.crm.core.domain.vo.CrmCustomerOrderSummaryVo">
    SELECT 
        COUNT(*) as totalOrderCount,
        COALESCE(SUM(total_amount), 0) as totalOrderAmount,
        COALESCE(AVG(total_amount), 0) as avgOrderAmount,
        COALESCE(MAX(total_amount), 0) as maxOrderAmount
    FROM crm_order
    WHERE customer_id = #{customerId} 
      AND del_flag = '0'
</select>
```

### 2. 业务逻辑层

#### 2.1 客户服务接口
**文件**: `ICrmCustomerService.java`

```java
/**
 * 根据订单总金额和数量计算并更新客户星级
 */
void updateCustomerStarRating(Long customerId);
```

#### 2.2 星级计算实现
**文件**: `CrmCustomerServiceImpl.java`

```java
@Override
public void updateCustomerStarRating(Long customerId) {
    try {
        // 查询客户订单汇总统计
        CrmCustomerOrderSummaryVo orderSummary = baseMapper.selectOrderSummaryByCustomerId(customerId);
        
        if (orderSummary != null) {
            // 计算客户星级
            String starRating = calculateStarRating(orderSummary);
            
            // 更新客户的星级字段
            CrmCustomer customer = new CrmCustomer();
            customer.setCustomerId(customerId);
            customer.setStarRating(starRating);
            baseMapper.updateById(customer);
            
            log.info("更新客户星级成功，客户ID: {}, 订单总数: {}, 订单总金额: {}, 星级: {}", 
                customerId, orderSummary.getTotalOrderCount(), orderSummary.getTotalOrderAmount(), starRating);
        }
    } catch (Exception e) {
        log.error("更新客户星级失败，客户ID: {}, 错误: {}", customerId, e.getMessage(), e);
    }
}

private String calculateStarRating(CrmCustomerOrderSummaryVo orderSummary) {
    // 综合评分逻辑
    int score = 0;
    
    // 订单总数评分（30分）
    // 订单总金额评分（40分）
    // 平均订单金额评分（20分）
    // 最大单笔订单金额评分（10分）
    
    // 根据总分确定星级
    if (score >= 90) return "5";      // 5星
    else if (score >= 75) return "4"; // 4星
    else if (score >= 60) return "3"; // 3星
    else if (score >= 40) return "2"; // 2星
    else return "1";                  // 1星
}
```

### 3. 订单服务集成

#### 3.1 新增订单时调用
**文件**: `CrmOrderServiceImpl.java`

```java
// 更新客户星级
try {
    customerService.updateCustomerStarRating(add.getCustomerId());
    log.info("更新客户星级成功，客户ID: {}", add.getCustomerId());
} catch (Exception e) {
    log.error("更新客户星级失败，客户ID: {}, 错误: {}", add.getCustomerId(), e.getMessage());
    // 不影响主流程，只记录日志
}
```

#### 3.2 编辑订单时调用
```java
// 更新客户星级
try {
    customerService.updateCustomerStarRating(update.getCustomerId());
    log.info("更新客户星级成功，客户ID: {}", update.getCustomerId());
} catch (Exception e) {
    log.error("更新客户星级失败，客户ID: {}, 错误: {}", update.getCustomerId(), e.getMessage());
    // 不影响主流程，只记录日志
}
```

## 🔄 执行流程

### 1. 订单操作触发流程
```
订单新增/编辑 → 保存订单数据 → 更新客户订单统计 → 查询订单汇总 → 计算星级评分 → 更新客户星级
```

### 2. 星级计算流程
```
获取订单统计 → 订单数量评分 → 总金额评分 → 平均金额评分 → 最大单笔评分 → 综合评分 → 确定星级
```

## 📊 业务价值

### 1. 客户分级管理
- **自动分级**：根据实际交易数据自动评定客户等级
- **动态调整**：随着订单变化实时调整客户星级
- **精准识别**：准确识别高价值客户和潜力客户

### 2. 营销策略支持
- **差异化服务**：为不同星级客户提供差异化服务
- **资源配置**：优先为高星级客户配置资源
- **促销策略**：针对不同星级制定促销策略

### 3. 销售管理优化
- **客户优先级**：明确客户跟进优先级
- **业绩评估**：基于客户星级评估销售业绩
- **目标设定**：为不同星级客户设定不同目标

## 📁 文件清单

### 新增文件
- `server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/domain/vo/CrmCustomerOrderSummaryVo.java`

### 修改文件
- `server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/service/ICrmCustomerService.java`
- `server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/mapper/CrmCustomerMapper.java`
- `server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/service/impl/CrmCustomerServiceImpl.java`
- `server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/service/impl/CrmOrderServiceImpl.java`
- `server/lookah-business/lookah-crm/lookah-crm-core/src/main/resources/mapper/crm/CrmCustomerMapper.xml`

## ✅ 验证方法

### 1. 功能验证
- 新增订单后检查客户星级是否更新
- 编辑订单后验证星级重新计算
- 测试不同金额和数量的星级计算结果

### 2. 边界测试
- 测试无订单客户的星级（应为1星）
- 测试单笔大额订单的星级计算
- 测试多笔小额订单的星级计算

### 3. 性能验证
- 验证星级计算不影响订单操作性能
- 检查异常处理是否正常工作
- 确认日志记录是否完整

## 🎉 总结

成功实现了客户星级自动计算功能：

1. **智能评分**：基于四个维度的综合评分系统
2. **实时更新**：订单操作时自动触发星级计算
3. **业务导向**：评分标准贴近实际业务需求
4. **异常安全**：星级计算失败不影响主业务流程
5. **日志完善**：详细记录计算过程和结果

现在系统能够根据客户的实际交易情况自动评定和更新客户星级，为客户分级管理和精准营销提供数据支持！🎊
