# 自动更新客户感兴趣产品和联系频次 - 2025年7月25日

## 📋 功能概述

实现两个自动统计更新功能：
1. 客户订单新增、编辑时统计感兴趣产品名称，以逗号分隔由多到少列出，修改到客户列表里
2. 新增跟进记录时统计联系频次，修改到客户列表里

## 🎯 主要功能

### 1. 感兴趣产品自动统计

#### 1.1 功能描述
- 当客户订单新增或编辑时，自动统计该客户所有订单中的产品名称
- 按产品的订单数量从多到少排序
- 将产品名称用逗号连接，更新到客户的`interested_products`字段

#### 1.2 统计逻辑
```sql
SELECT od.product_name, SUM(od.quantity) as total_quantity
FROM crm_order o
JOIN crm_order_detail od ON o.order_id = od.order_id
WHERE o.customer_id = ? AND o.del_flag = '0' AND od.del_flag = '0'
GROUP BY od.product_name
ORDER BY total_quantity DESC
```

#### 1.3 更新时机
- 新增客户订单时
- 编辑客户订单时
- 订单详情发生变化时

### 2. 联系频次自动统计

#### 2.1 功能描述
- 当新增跟进记录时，自动统计该客户的跟进记录总数
- 根据跟进次数计算联系频次等级
- 更新到客户的`contact_frequency`字段

#### 2.2 统计逻辑
```sql
SELECT COUNT(*) 
FROM crm_customer_follow_record 
WHERE customer_id = ? AND del_flag = '0'
```

#### 2.3 频次分级
| 跟进次数 | 联系频次 | 说明 |
|---------|---------|------|
| ≥20次 | daily | 每天联系 |
| 10-19次 | weekly | 每周联系 |
| 3-9次 | monthly | 每月联系 |
| <3次 | irregular | 不定期联系 |

#### 2.4 更新时机
- 新增跟进记录时

## 🔧 技术实现

### 1. 客户服务接口扩展
**文件**: `ICrmCustomerService.java`

```java
/**
 * 统计并更新客户感兴趣的产品名称
 */
void updateInterestedProducts(Long customerId);

/**
 * 统计并更新客户联系频次
 */
void updateContactFrequency(Long customerId);
```

### 2. 客户服务实现
**文件**: `CrmCustomerServiceImpl.java`

#### 2.1 感兴趣产品更新
```java
@Override
public void updateInterestedProducts(Long customerId) {
    try {
        // 使用Mapper直接查询统计数据
        List<Map<String, Object>> results = baseMapper.selectInterestedProductsByCustomerId(customerId);
        
        if (CollUtil.isNotEmpty(results)) {
            // 将产品名称按数量从多到少用逗号连接
            String interestedProducts = results.stream()
                .map(map -> (String) map.get("product_name"))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(","));
            
            // 更新客户的感兴趣产品字段
            CrmCustomer customer = new CrmCustomer();
            customer.setCustomerId(customerId);
            customer.setInterestedProducts(interestedProducts);
            baseMapper.updateById(customer);
        }
    } catch (Exception e) {
        log.error("更新客户感兴趣产品失败，客户ID: {}, 错误: {}", customerId, e.getMessage(), e);
    }
}
```

#### 2.2 联系频次更新
```java
@Override
public void updateContactFrequency(Long customerId) {
    try {
        // 使用Mapper直接查询跟进记录统计
        Integer totalFollowCount = baseMapper.selectFollowCountByCustomerId(customerId);
        
        if (totalFollowCount != null && totalFollowCount > 0) {
            // 根据跟进次数确定联系频次
            String contactFrequency = calculateContactFrequencyByCount(totalFollowCount);
            
            // 更新客户的联系频次字段
            CrmCustomer customer = new CrmCustomer();
            customer.setCustomerId(customerId);
            customer.setContactFrequency(contactFrequency);
            baseMapper.updateById(customer);
        }
    } catch (Exception e) {
        log.error("更新客户联系频次失败，客户ID: {}, 错误: {}", customerId, e.getMessage(), e);
    }
}

private String calculateContactFrequencyByCount(int followCount) {
    if (followCount >= 20) {
        return "daily"; // 每天
    } else if (followCount >= 10) {
        return "weekly"; // 每周
    } else if (followCount >= 3) {
        return "monthly"; // 每月
    } else {
        return "irregular"; // 不定期
    }
}
```

### 3. 数据访问层扩展
**文件**: `CrmCustomerMapper.java`

```java
/**
 * 查询客户感兴趣的产品名称（按订单数量排序）
 */
@Select("""
    SELECT od.product_name, SUM(od.quantity) as total_quantity
    FROM crm_order o
    JOIN crm_order_detail od ON o.order_id = od.order_id
    WHERE o.customer_id = #{customerId} AND o.del_flag = '0' AND od.del_flag = '0'
    GROUP BY od.product_name
    ORDER BY total_quantity DESC
    """)
List<Map<String, Object>> selectInterestedProductsByCustomerId(@Param("customerId") Long customerId);

/**
 * 查询客户跟进记录总数
 */
@Select("""
    SELECT COUNT(*) 
    FROM crm_customer_follow_record 
    WHERE customer_id = #{customerId} AND del_flag = '0'
    """)
Integer selectFollowCountByCustomerId(@Param("customerId") Long customerId);
```

### 4. 订单服务集成
**文件**: `CrmOrderServiceImpl.java`

#### 4.1 新增订单时调用
```java
// 更新客户感兴趣的产品名称
try {
    customerService.updateInterestedProducts(add.getCustomerId());
    log.info("更新客户感兴趣产品成功，客户ID: {}", add.getCustomerId());
} catch (Exception e) {
    log.error("更新客户感兴趣产品失败，客户ID: {}, 错误: {}", add.getCustomerId(), e.getMessage());
    // 不影响主流程，只记录日志
}
```

#### 4.2 编辑订单时调用
```java
// 更新客户感兴趣的产品名称
try {
    customerService.updateInterestedProducts(update.getCustomerId());
    log.info("更新客户感兴趣产品成功，客户ID: {}", update.getCustomerId());
} catch (Exception e) {
    log.error("更新客户感兴趣产品失败，客户ID: {}, 错误: {}", update.getCustomerId(), e.getMessage());
    // 不影响主流程，只记录日志
}
```

### 5. 跟进记录服务集成
**文件**: `CrmCustomerFollowRecordServiceImpl.java`

#### 5.1 新增跟进记录时调用
```java
// 更新客户联系频次
try {
    crmCustomerService.updateContactFrequency(add.getCustomerId());
    log.info("更新客户联系频次成功，客户ID: {}", add.getCustomerId());
} catch (Exception e) {
    log.error("更新客户联系频次失败，客户ID: {}, 错误: {}", add.getCustomerId(), e.getMessage());
    // 不影响主流程，只记录日志
}
```

## 📊 业务价值

### 1. 感兴趣产品统计
- **精准营销**: 了解客户最感兴趣的产品类型
- **库存管理**: 为重要客户预留热门产品
- **产品推荐**: 基于历史订单推荐相关产品
- **销售策略**: 制定针对性的产品销售策略

### 2. 联系频次统计
- **客户分级**: 根据联系频次对客户进行分级管理
- **资源分配**: 为高频联系客户分配更多资源
- **跟进策略**: 制定差异化的客户跟进策略
- **关系维护**: 识别需要加强联系的客户

## 🔄 数据流程

### 1. 感兴趣产品更新流程
```
订单新增/编辑 → 保存订单详情 → 统计产品数量 → 按数量排序 → 逗号连接 → 更新客户表
```

### 2. 联系频次更新流程
```
新增跟进记录 → 统计跟进总数 → 计算频次等级 → 更新客户表
```

## 📁 修改的文件清单

### 后端文件
- `server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/service/ICrmCustomerService.java`
  - 新增updateInterestedProducts和updateContactFrequency方法

- `server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/service/impl/CrmCustomerServiceImpl.java`
  - 实现感兴趣产品和联系频次的统计更新逻辑

- `server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/mapper/CrmCustomerMapper.java`
  - 新增查询感兴趣产品和跟进记录统计的方法

- `server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/service/impl/CrmOrderServiceImpl.java`
  - 在订单新增和编辑时调用感兴趣产品更新

- `server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/service/impl/CrmCustomerFollowRecordServiceImpl.java`
  - 在跟进记录新增时调用联系频次更新

## ✅ 功能验证

### 1. 感兴趣产品验证
- 新增订单后检查客户的感兴趣产品字段
- 编辑订单后验证产品排序是否正确
- 多个产品的客户验证逗号分隔格式

### 2. 联系频次验证
- 新增跟进记录后检查客户的联系频次字段
- 验证不同跟进次数对应的频次等级
- 确认频次计算逻辑的准确性

## 🎉 总结

成功实现了客户数据的自动统计更新功能：

1. **感兴趣产品自动统计**: 基于订单数据智能分析客户偏好
2. **联系频次自动计算**: 基于跟进记录评估客户关系密切程度
3. **实时数据更新**: 在业务操作时自动触发统计更新
4. **异常处理完善**: 统计失败不影响主业务流程
5. **日志记录详细**: 便于问题排查和数据追踪

现在客户列表中的感兴趣产品和联系频次字段会根据实际业务数据自动更新，为客户管理和营销决策提供更准确的数据支持！🎊
