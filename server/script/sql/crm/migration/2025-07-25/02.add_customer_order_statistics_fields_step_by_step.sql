-- 客户订单统计字段添加脚本（分步执行版本）
-- 执行时间：2025-07-25
-- 说明：分步执行，每个步骤可以单独运行，避免语法兼容性问题

-- ========================================
-- 第一步：添加字段
-- ========================================

-- 1. 添加累计下单金额字段
-- 如果字段已存在会报错，可以忽略
ALTER TABLE crm_customer 
ADD COLUMN total_order_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '累计下单金额';

-- 2. 添加累计下单次数字段
ALTER TABLE crm_customer 
ADD COLUMN total_order_count INT DEFAULT 0 COMMENT '累计下单次数';

-- 3. 添加下单频率字段
ALTER TABLE crm_customer
ADD COLUMN order_frequency VARCHAR(20) DEFAULT 'irregular' COMMENT '下单频率(weekly:每周,monthly:每月,quarterly:每季度,irregular:不规律)';

-- 4. 添加第一次下单日期字段
ALTER TABLE crm_customer 
ADD COLUMN first_order_date DATETIME COMMENT '第一次下单日期';

-- 5. 添加最后一次下单日期字段
ALTER TABLE crm_customer 
ADD COLUMN last_order_date DATETIME COMMENT '最后一次下单日期';

-- ========================================
-- 第二步：添加索引
-- ========================================

-- 1. 创建复合索引
CREATE INDEX idx_customer_order_stats ON crm_customer(total_order_count, total_order_amount);

-- 2. 创建下单频率索引
CREATE INDEX idx_customer_order_frequency ON crm_customer(order_frequency);

-- 3. 创建第一次下单日期索引
CREATE INDEX idx_customer_first_order_date ON crm_customer(first_order_date);

-- 4. 创建最后一次下单日期索引
CREATE INDEX idx_customer_last_order_date ON crm_customer(last_order_date);

-- ========================================
-- 第三步：初始化数据（可选，建议在业务低峰期执行）
-- ========================================

-- 1. 更新累计下单次数和金额
UPDATE crm_customer c 
SET 
    total_order_count = (
        SELECT COUNT(*) 
        FROM crm_order o 
        WHERE o.customer_id = c.customer_id 
          AND o.del_flag = '0' 
          AND o.order_status != 'cancelled'
    ),
    total_order_amount = (
        SELECT COALESCE(SUM(o.order_total_amount), 0) 
        FROM crm_order o 
        WHERE o.customer_id = c.customer_id 
          AND o.del_flag = '0' 
          AND o.order_status != 'cancelled'
    )
WHERE c.del_flag = '0';

-- 2. 更新第一次和最后一次下单日期
UPDATE crm_customer c 
SET 
    first_order_date = (
        SELECT MIN(o.order_date) 
        FROM crm_order o 
        WHERE o.customer_id = c.customer_id 
          AND o.del_flag = '0' 
          AND o.order_status != 'cancelled'
    ),
    last_order_date = (
        SELECT MAX(o.order_date) 
        FROM crm_order o 
        WHERE o.customer_id = c.customer_id 
          AND o.del_flag = '0' 
          AND o.order_status != 'cancelled'
    )
WHERE c.del_flag = '0';

-- 3. 更新下单频率
UPDATE crm_customer c
SET order_frequency = CASE
    WHEN c.total_order_count <= 1 THEN 'irregular'
    WHEN c.first_order_date IS NULL OR c.last_order_date IS NULL THEN 'irregular'
    WHEN DATEDIFF(c.last_order_date, c.first_order_date) = 0 THEN 'irregular'
    WHEN (DATEDIFF(c.last_order_date, c.first_order_date) / (c.total_order_count - 1)) <= 10 THEN 'weekly'
    WHEN (DATEDIFF(c.last_order_date, c.first_order_date) / (c.total_order_count - 1)) <= 35 THEN 'monthly'
    WHEN (DATEDIFF(c.last_order_date, c.first_order_date) / (c.total_order_count - 1)) <= 100 THEN 'quarterly'
    ELSE 'irregular'
END
WHERE c.del_flag = '0' AND c.total_order_count > 1;

-- ========================================
-- 第四步：验证结果
-- ========================================

-- 1. 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'crm_customer' 
  AND COLUMN_NAME IN ('total_order_amount', 'total_order_count', 'order_frequency', 'first_order_date', 'last_order_date')
ORDER BY COLUMN_NAME;

-- 2. 验证索引是否创建成功
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'crm_customer' 
  AND INDEX_NAME LIKE 'idx_customer_%'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 3. 验证数据统计结果
SELECT 
    COUNT(*) as total_customers,
    COUNT(CASE WHEN total_order_count > 0 THEN 1 END) as customers_with_orders,
    ROUND(AVG(total_order_count), 2) as avg_order_count,
    ROUND(AVG(total_order_amount), 2) as avg_order_amount,
    COUNT(CASE WHEN order_frequency = 'weekly' THEN 1 END) as weekly_frequency_customers,
    COUNT(CASE WHEN order_frequency = 'monthly' THEN 1 END) as monthly_frequency_customers,
    COUNT(CASE WHEN order_frequency = 'quarterly' THEN 1 END) as quarterly_frequency_customers,
    COUNT(CASE WHEN order_frequency = 'irregular' THEN 1 END) as irregular_frequency_customers
FROM crm_customer 
WHERE del_flag = '0';

-- 4. 显示前5个有订单的客户的统计信息
SELECT 
    customer_id,
    customer_name,
    total_order_count,
    total_order_amount,
    order_frequency,
    DATE_FORMAT(first_order_date, '%Y-%m-%d %H:%i:%s') as first_order_date,
    DATE_FORMAT(last_order_date, '%Y-%m-%d %H:%i:%s') as last_order_date
FROM crm_customer 
WHERE del_flag = '0' 
  AND total_order_count > 0 
ORDER BY total_order_amount DESC 
LIMIT 5;
