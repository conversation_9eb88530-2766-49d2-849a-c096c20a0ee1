-- =============================================
-- CRM是否沟通好字典数据
-- 创建日期: 2025-07-25
-- 描述: 添加"是否沟通好"字典类型和数据
-- 字典值: 是=1, 否=0
-- =============================================

-- 1. 添加字典类型
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (1859425395123200036, 'CRM是否沟通好', 'crm_is_easy_communicate', 103, 1, NOW(), 1, NOW(), 'CRM客户是否容易沟通的选项')
ON DUPLICATE KEY UPDATE
    `dict_name` = VALUES(`dict_name`),
    `update_time` = NOW(),
    `remark` = VALUES(`remark`);

-- 2. 添加字典数据
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1859425395123200037, 1, '是', '1', 'crm_is_easy_communicate', '', 'success', 'Y', 103, 1, NOW(), 1, NOW(), '容易沟通'),
(1859425395123200038, 2, '否', '0', 'crm_is_easy_communicate', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), '不容易沟通');

-- =============================================
-- 验证插入的字典数据
-- =============================================

-- 查看是否沟通好字典
SELECT
    dt.dict_name,
    dd.dict_label,
    dd.dict_value,
    dd.list_class,
    dd.dict_sort
FROM sys_dict_type dt
JOIN sys_dict_data dd ON dt.dict_type = dd.dict_type
WHERE dt.dict_type = 'crm_is_easy_communicate'
ORDER BY dd.dict_sort;

-- =============================================
-- 说明
-- =============================================
/*
字典类型: crm_is_easy_communicate
字典名称: CRM是否沟通好

字典数据:
- 是: 值为 1, 样式为 success (绿色)
- 否: 值为 0, 样式为 danger (红色)

使用的雪花ID:
- dict_type ID: 1859425395123200036
- 字典数据 "是" ID: 1859425395123200037  
- 字典数据 "否" ID: 1859425395123200038

在前端使用时:
- 字典类型: crm_is_easy_communicate
- 可以通过 getDictOptions('crm_is_easy_communicate') 获取选项
- 显示时会根据值 1/0 显示对应的标签 是/否
*/
