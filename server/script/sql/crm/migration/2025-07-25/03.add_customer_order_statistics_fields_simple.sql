-- 添加客户订单统计字段（简化版本）
-- 执行时间：2025-07-25
-- 说明：为客户表添加订单统计相关字段，支持自动计算和更新
-- 注意：如果字段已存在，会报错但不影响后续操作

-- 添加累计下单金额字段
ALTER TABLE crm_customer 
ADD COLUMN total_order_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '累计下单金额';

-- 添加累计下单次数字段
ALTER TABLE crm_customer 
ADD COLUMN total_order_count INT DEFAULT 0 COMMENT '累计下单次数';

-- 添加下单频率字段
ALTER TABLE crm_customer
ADD COLUMN order_frequency VARCHAR(20) DEFAULT 'irregular' COMMENT '下单频率(weekly:每周,monthly:每月,quarterly:每季度,irregular:不规律)';

-- 添加第一次下单日期字段
ALTER TABLE crm_customer 
ADD COLUMN first_order_date DATETIME COMMENT '第一次下单日期';

-- 添加最后一次下单日期字段
ALTER TABLE crm_customer 
ADD COLUMN last_order_date DATETIME COMMENT '最后一次下单日期';

-- 为新增字段添加索引以提高查询性能
CREATE INDEX idx_customer_order_stats ON crm_customer(total_order_count, total_order_amount);
CREATE INDEX idx_customer_order_frequency ON crm_customer(order_frequency);
CREATE INDEX idx_customer_first_order_date ON crm_customer(first_order_date);
CREATE INDEX idx_customer_last_order_date ON crm_customer(last_order_date);

-- 初始化现有客户的订单统计数据
-- 注意：这个操作可能需要较长时间，建议在业务低峰期执行
UPDATE crm_customer c 
SET 
    total_order_count = (
        SELECT COUNT(*) 
        FROM crm_order o 
        WHERE o.customer_id = c.customer_id 
          AND o.del_flag = '0' 
          AND o.order_status != 'cancelled'
    ),
    total_order_amount = (
        SELECT COALESCE(SUM(o.order_total_amount), 0) 
        FROM crm_order o 
        WHERE o.customer_id = c.customer_id 
          AND o.del_flag = '0' 
          AND o.order_status != 'cancelled'
    ),
    first_order_date = (
        SELECT MIN(o.order_date) 
        FROM crm_order o 
        WHERE o.customer_id = c.customer_id 
          AND o.del_flag = '0' 
          AND o.order_status != 'cancelled'
    ),
    last_order_date = (
        SELECT MAX(o.order_date) 
        FROM crm_order o 
        WHERE o.customer_id = c.customer_id 
          AND o.del_flag = '0' 
          AND o.order_status != 'cancelled'
    )
WHERE c.del_flag = '0';

-- 更新下单频率
UPDATE crm_customer c
SET order_frequency = CASE
    WHEN c.total_order_count <= 1 THEN 'irregular'
    WHEN c.first_order_date IS NULL OR c.last_order_date IS NULL THEN 'irregular'
    WHEN DATEDIFF(c.last_order_date, c.first_order_date) = 0 THEN 'irregular'
    WHEN (DATEDIFF(c.last_order_date, c.first_order_date) / (c.total_order_count - 1)) <= 10 THEN 'weekly'
    WHEN (DATEDIFF(c.last_order_date, c.first_order_date) / (c.total_order_count - 1)) <= 35 THEN 'monthly'
    WHEN (DATEDIFF(c.last_order_date, c.first_order_date) / (c.total_order_count - 1)) <= 100 THEN 'quarterly'
    ELSE 'irregular'
END
WHERE c.del_flag = '0' AND c.total_order_count > 1;

-- 验证数据更新结果
SELECT 
    COUNT(*) as total_customers,
    COUNT(CASE WHEN total_order_count > 0 THEN 1 END) as customers_with_orders,
    AVG(total_order_count) as avg_order_count,
    AVG(total_order_amount) as avg_order_amount,
    COUNT(CASE WHEN order_frequency = 'weekly' THEN 1 END) as weekly_frequency_customers,
    COUNT(CASE WHEN order_frequency = 'monthly' THEN 1 END) as monthly_frequency_customers,
    COUNT(CASE WHEN order_frequency = 'quarterly' THEN 1 END) as quarterly_frequency_customers,
    COUNT(CASE WHEN order_frequency = 'irregular' THEN 1 END) as irregular_frequency_customers
FROM crm_customer 
WHERE del_flag = '0';

-- 显示前10个有订单的客户的统计信息
SELECT 
    customer_id,
    customer_name,
    total_order_count,
    total_order_amount,
    order_frequency,
    first_order_date,
    last_order_date
FROM crm_customer 
WHERE del_flag = '0' 
  AND total_order_count > 0 
ORDER BY total_order_amount DESC 
LIMIT 10;
