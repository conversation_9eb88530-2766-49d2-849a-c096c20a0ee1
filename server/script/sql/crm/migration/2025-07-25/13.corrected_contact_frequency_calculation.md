# 修正联系频次计算逻辑 - 2025年7月25日

## 📋 修正说明

根据用户反馈，对联系频次计算逻辑进行了两个重要修正：
1. 将SQL查询从注解方式改为XML配置文件
2. 联系频次计算改为基于时间间隔，而不是简单的跟进次数

## 🔧 主要修正

### 1. SQL查询方式修正

#### 1.1 修正前（注解方式）
```java
@Select("""
    SELECT COUNT(*) 
    FROM crm_customer_follow_record 
    WHERE customer_id = #{customerId} AND del_flag = '0'
    """)
Integer selectFollowCountByCustomerId(@Param("customerId") Long customerId);
```

#### 1.2 修正后（XML配置）
**文件**: `CrmCustomerMapper.xml`
```xml
<select id="selectFollowStatisticsByCustomerId" parameterType="java.lang.Long" resultType="java.util.Map">
    SELECT 
        COUNT(*) as total_count,
        MIN(follow_date) as first_follow_date,
        MAX(follow_date) as last_follow_date,
        DATEDIFF(MAX(follow_date), MIN(follow_date)) as date_span_days
    FROM crm_customer_follow_record
    WHERE customer_id = #{customerId} 
      AND del_flag = '0'
      AND follow_date IS NOT NULL
</select>
```

### 2. 联系频次计算逻辑修正

#### 2.1 修正前（基于跟进次数）
```java
private String calculateContactFrequencyByCount(int followCount) {
    if (followCount >= 20) {
        return "daily"; // 每天
    } else if (followCount >= 10) {
        return "weekly"; // 每周
    } else if (followCount >= 3) {
        return "monthly"; // 每月
    } else {
        return "irregular"; // 不定期
    }
}
```

#### 2.2 修正后（基于时间间隔）
```java
private String calculateContactFrequencyByTimeInterval(int totalCount, int dateSpanDays) {
    if (dateSpanDays <= 0 || totalCount <= 1) {
        return "irregular"; // 不定期
    }
    
    // 计算平均间隔天数
    double avgIntervalDays = (double) dateSpanDays / (totalCount - 1);
    
    if (avgIntervalDays <= 2) {
        return "daily"; // 每天：平均2天内联系一次
    } else if (avgIntervalDays <= 10) {
        return "weekly"; // 每周：平均10天内联系一次
    } else if (avgIntervalDays <= 35) {
        return "monthly"; // 每月：平均35天内联系一次
    } else if (avgIntervalDays <= 100) {
        return "quarterly"; // 每季度：平均100天内联系一次
    } else {
        return "irregular"; // 不定期：超过100天才联系一次
    }
}
```

## 📊 新的联系频次计算标准

### 1. 计算公式
```
平均间隔天数 = 总时间跨度天数 / (跟进次数 - 1)
```

### 2. 频次分级标准

| 联系频次 | 平均间隔天数 | 说明 | 示例 |
|---------|-------------|------|------|
| daily | ≤2天 | 几乎每天联系 | 10次跟进，跨度15天 |
| weekly | 3-10天 | 大约每周联系 | 8次跟进，跨度50天 |
| monthly | 11-35天 | 大约每月联系 | 6次跟进，跨度150天 |
| quarterly | 36-100天 | 大约每季度联系 | 4次跟进，跨度300天 |
| irregular | >100天或≤1次 | 不定期联系 | 2次跟进，跨度300天 |

### 3. 特殊情况处理
- **只有1次跟进**：直接设为 `irregular`
- **时间跨度为0**：直接设为 `irregular`
- **跟进日期为空**：不参与计算

## 🔧 技术实现

### 1. XML映射文件
**文件**: `CrmCustomerMapper.xml`

```xml
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imhuso.crm.core.mapper.CrmCustomerMapper">

    <!-- 查询客户感兴趣的产品名称（按订单数量排序） -->
    <select id="selectInterestedProductsByCustomerId" parameterType="java.lang.Long" resultType="java.util.Map">
        SELECT 
            od.product_name, 
            SUM(od.quantity) as total_quantity
        FROM crm_order o
        JOIN crm_order_detail od ON o.order_id = od.order_id
        WHERE o.customer_id = #{customerId} 
          AND o.del_flag = '0' 
          AND od.del_flag = '0'
        GROUP BY od.product_name
        ORDER BY total_quantity DESC
    </select>

    <!-- 查询客户跟进记录统计信息（用于计算联系频次） -->
    <select id="selectFollowStatisticsByCustomerId" parameterType="java.lang.Long" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_count,
            MIN(follow_date) as first_follow_date,
            MAX(follow_date) as last_follow_date,
            DATEDIFF(MAX(follow_date), MIN(follow_date)) as date_span_days
        FROM crm_customer_follow_record
        WHERE customer_id = #{customerId} 
          AND del_flag = '0'
          AND follow_date IS NOT NULL
    </select>

</mapper>
```

### 2. Mapper接口修正
**文件**: `CrmCustomerMapper.java`

```java
/**
 * 查询客户感兴趣的产品名称（按订单数量排序）
 */
List<Map<String, Object>> selectInterestedProductsByCustomerId(@Param("customerId") Long customerId);

/**
 * 查询客户跟进记录统计信息（用于计算联系频次）
 */
Map<String, Object> selectFollowStatisticsByCustomerId(@Param("customerId") Long customerId);
```

### 3. 服务实现修正
**文件**: `CrmCustomerServiceImpl.java`

```java
@Override
public void updateContactFrequency(Long customerId) {
    try {
        // 查询客户跟进记录统计信息
        Map<String, Object> statistics = baseMapper.selectFollowStatisticsByCustomerId(customerId);
        
        if (statistics != null && statistics.get("total_count") != null) {
            Integer totalCount = ((Number) statistics.get("total_count")).intValue();
            
            if (totalCount > 1) {
                // 获取时间跨度（天数）
                Integer dateSpanDays = statistics.get("date_span_days") != null ? 
                    ((Number) statistics.get("date_span_days")).intValue() : 0;
                
                // 根据时间间隔计算联系频次
                String contactFrequency = calculateContactFrequencyByTimeInterval(totalCount, dateSpanDays);
                
                // 更新客户的联系频次字段
                CrmCustomer customer = new CrmCustomer();
                customer.setCustomerId(customerId);
                customer.setContactFrequency(contactFrequency);
                baseMapper.updateById(customer);
                
                log.info("更新客户联系频次成功，客户ID: {}, 跟进次数: {}, 时间跨度: {}天, 联系频次: {}", 
                    customerId, totalCount, dateSpanDays, contactFrequency);
            } else if (totalCount == 1) {
                // 只有一次跟进记录，设为不定期
                CrmCustomer customer = new CrmCustomer();
                customer.setCustomerId(customerId);
                customer.setContactFrequency("irregular");
                baseMapper.updateById(customer);
            }
        }
    } catch (Exception e) {
        log.error("更新客户联系频次失败，客户ID: {}, 错误: {}", customerId, e.getMessage(), e);
    }
}
```

## 📈 计算示例

### 示例1：高频客户
- **跟进记录**：15次
- **时间跨度**：20天
- **平均间隔**：20 ÷ (15-1) = 1.43天
- **联系频次**：daily

### 示例2：中频客户
- **跟进记录**：8次
- **时间跨度**：60天
- **平均间隔**：60 ÷ (8-1) = 8.57天
- **联系频次**：weekly

### 示例3：低频客户
- **跟进记录**：4次
- **时间跨度**：200天
- **平均间隔**：200 ÷ (4-1) = 66.67天
- **联系频次**：quarterly

### 示例4：不定期客户
- **跟进记录**：3次
- **时间跨度**：400天
- **平均间隔**：400 ÷ (3-1) = 200天
- **联系频次**：irregular

## 📁 修改的文件清单

### 新增文件
- `server/lookah-business/lookah-crm/lookah-crm-core/src/main/resources/mapper/crm/CrmCustomerMapper.xml` - XML映射文件

### 修改文件
- `server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/mapper/CrmCustomerMapper.java`
  - 移除@Select注解
  - 修改方法签名

- `server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/service/impl/CrmCustomerServiceImpl.java`
  - 修改联系频次计算逻辑
  - 改为基于时间间隔计算

## 🎯 业务价值

### 1. 更准确的频次评估
- 基于实际时间间隔，而不是简单的次数统计
- 能够区分短期密集跟进和长期稀疏跟进
- 更符合实际业务场景

### 2. 更合理的客户分类
- 考虑了跟进的时间分布
- 避免了因历史累积导致的分类偏差
- 提供更精准的客户关系评估

### 3. 更好的营销指导
- 识别真正的高频互动客户
- 发现需要加强联系的客户
- 制定更有针对性的跟进策略

## ✅ 验证方法

### 1. 数据验证
- 检查XML映射文件是否正确加载
- 验证SQL查询结果的准确性
- 确认计算逻辑的正确性

### 2. 功能验证
- 新增跟进记录后检查联系频次更新
- 验证不同时间间隔的计算结果
- 确认特殊情况的处理逻辑

## 🎉 总结

通过本次修正，联系频次计算功能更加科学和准确：

1. **SQL配置规范化**：使用XML配置替代注解，便于维护和优化
2. **计算逻辑优化**：基于时间间隔计算，更符合业务实际
3. **分级标准合理**：考虑了真实的联系时间分布
4. **异常处理完善**：处理各种边界情况

现在联系频次能够更准确地反映客户的真实联系情况，为客户管理提供更有价值的数据支持！🎊
