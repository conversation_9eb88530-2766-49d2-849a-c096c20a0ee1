-- ----------------------------
-- 添加客户图片字段
-- 创建时间: 2025-08-06
-- 作者: jf
-- 描述: 为CRM客户表添加图片字段，用于存储客户相关图片
-- ----------------------------

-- 1. 添加客户图片字段
ALTER TABLE `crm_customer`
ADD COLUMN `customer_images` TEXT NULL COMMENT '客户图片（逗号分隔存储多张图片URL）' AFTER `social_media_type`;

-- 2. 添加索引（如果需要按图片字段查询）
-- ALTER TABLE `crm_customer` ADD INDEX `idx_customer_images` (`customer_images`(255));

-- 3. 更新表注释
ALTER TABLE `crm_customer` COMMENT = 'CRM客户信息表（包含客户图片字段）';

-- 4. 示例数据格式说明
-- customer_images 字段存储格式示例：
-- 单张图片: "https://example.com/image1.jpg"
-- 多张图片: "https://example.com/image1.jpg,https://example.com/image2.jpg,https://example.com/image3.jpg"
-- 空值: NULL 或 ""

-- 5. 验证字段添加
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'crm_customer' 
    AND COLUMN_NAME = 'customer_images';

-- 6. 回滚脚本（如需要）
-- ALTER TABLE `crm_customer` DROP COLUMN `customer_images`;
