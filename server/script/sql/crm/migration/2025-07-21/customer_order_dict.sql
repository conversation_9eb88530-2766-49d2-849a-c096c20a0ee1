-- ----------------------------
-- 客户订单字典数据
-- Author: AI Assistant
-- Date: 2025-07-21
-- Description: 客户订单相关的字典数据配置
-- ----------------------------

-- =============================================
-- 订单状态字典数据
-- =============================================

-- 删除已存在的字典类型
DELETE FROM sys_dict_data WHERE dict_type = 'crm_order_status';
DELETE FROM sys_dict_type WHERE dict_type = 'crm_order_status';

-- 插入订单状态字典类型
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, create_dept, create_by, create_time, update_by, update_time, remark)
VALUES (1859425395123200020, '订单状态', 'crm_order_status', 103, 1, NOW(), 1, NOW(), 'CRM订单状态列表');

-- 插入订单状态字典数据
INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, create_dept, create_by, create_time, update_by, update_time, remark) VALUES
(1859425395123200021, 1, '待处理', 'pending', 'crm_order_status', '', 'warning', 'Y', 103, 1, NOW(), 1, NOW(), '订单待处理状态'),
(1859425395123200022, 2, '已确认', 'confirmed', 'crm_order_status', '', 'primary', 'N', 103, 1, NOW(), 1, NOW(), '订单已确认状态'),
(1859425395123200023, 3, '处理中', 'processing', 'crm_order_status', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), '订单处理中状态'),
(1859425395123200024, 4, '已发货', 'shipped', 'crm_order_status', '', 'primary', 'N', 103, 1, NOW(), 1, NOW(), '订单已发货状态'),
(1859425395123200025, 5, '已送达', 'delivered', 'crm_order_status', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '订单已送达状态'),
(1859425395123200026, 6, '已完成', 'completed', 'crm_order_status', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '订单已完成状态'),
(1859425395123200027, 7, '已取消', 'cancelled', 'crm_order_status', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), '订单已取消状态'),
(1859425395123200028, 8, '已退款', 'refunded', 'crm_order_status', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), '订单已退款状态');

-- =============================================
-- 支付方式字典数据
-- =============================================

-- 删除已存在的字典类型
DELETE FROM sys_dict_data WHERE dict_type = 'crm_payment_method';
DELETE FROM sys_dict_type WHERE dict_type = 'crm_payment_method';

-- 插入支付方式字典类型
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, create_dept, create_by, create_time, update_by, update_time, remark)
VALUES (1859425395123200030, '支付方式', 'crm_payment_method', 103, 1, NOW(), 1, NOW(), 'CRM订单支付方式列表');

-- 插入支付方式字典数据
INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, create_dept, create_by, create_time, update_by, update_time, remark) VALUES
(1859425395123200031, 1, '银行转账', 'bank_transfer', 'crm_payment_method', '', 'primary', 'Y', 103, 1, NOW(), 1, NOW(), '银行转账支付'),
(1859425395123200032, 2, 'ACH', 'ach', 'crm_payment_method', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), 'ACH支付'),
(1859425395123200033, 3, '线上信用卡', 'online_credit_card', 'crm_payment_method', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '线上信用卡支付'),
(1859425395123200034, 4, '线下信用卡', 'offline_credit_card', 'crm_payment_method', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '线下信用卡支付'),
(1859425395123200035, 5, 'Zelle', 'zelle', 'crm_payment_method', '', 'primary', 'N', 103, 1, NOW(), 1, NOW(), 'Zelle支付');

-- =============================================
-- 验证插入的字典数据
-- =============================================

-- 查看订单状态字典
SELECT
    dt.dict_name,
    dd.dict_label,
    dd.dict_value,
    dd.list_class,
    dd.dict_sort
FROM sys_dict_type dt
JOIN sys_dict_data dd ON dt.dict_type = dd.dict_type
WHERE dt.dict_type = 'crm_order_status'
ORDER BY dd.dict_sort;

-- 查看支付方式字典
SELECT
    dt.dict_name,
    dd.dict_label,
    dd.dict_value,
    dd.list_class,
    dd.dict_sort
FROM sys_dict_type dt
JOIN sys_dict_data dd ON dt.dict_type = dd.dict_type
WHERE dt.dict_type = 'crm_payment_method'
ORDER BY dd.dict_sort;
