# CRM客户订单功能实现详细说明

## 概述

本文档详细说明了CRM系统中客户订单功能的完整实现，包括数据库设计、后端API、前端界面等所有相关组件。

## 功能特性

### 核心功能
- ✅ 订单信息管理（增删改查）
- ✅ 订单详情管理（支持多产品）
- ✅ 订单状态跟踪
- ✅ 支付方式管理
- ✅ 订单关联管理
- ✅ 数据权限控制
- ✅ 订单导出功能

### 业务规则
- 订单号自动生成（格式：ORD{YYYYMMDD}{时间戳}）
- 支持多种订单状态流转
- 支持多种支付方式
- 自动计算订单总金额
- 软删除机制

## 数据库设计

### 主要表结构

#### 1. crm_order（订单主表）
```sql
-- 位置：server/script/sql/crm/crm_order.sql
-- 主要字段：
- order_id: 订单ID（主键）
- order_no: 订单号（唯一）
- order_date: 下单日期
- customer_id: 客户ID
- customer_code: 客户编号
- company_name: 公司名称
- order_status: 订单状态
- order_total_amount: 订单总金额
- payment_method: 付款方式
- related_order_no: 关联订单号
```

#### 2. crm_order_detail（订单详情表）
```sql
-- 位置：server/script/sql/crm/crm_order_detail.sql
-- 主要字段：
- detail_id: 订单详情ID（主键）
- order_id: 订单ID（外键）
- order_no: 订单号
- product_id: 产品ID
- product_name: 产品名称
- product_color: 颜色
- unit_price: 单价
- quantity: 数量
- product_total_amount: 单款产品总金额
```

### 字典数据

#### 订单状态（crm_order_status）
- `pending` - 待处理
- `confirmed` - 已确认
- `processing` - 处理中
- `shipped` - 已发货
- `delivered` - 已送达
- `completed` - 已完成
- `cancelled` - 已取消
- `refunded` - 已退款

#### 支付方式（crm_payment_method）
- `bank_transfer` - 银行转账
- `ach` - ACH
- `online_credit_card` - 线上信用卡
- `offline_credit_card` - 线下信用卡
- `zelle` - Zelle

## 后端实现

### 实体类
```
server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/domain/
├── CrmOrder.java                    # 订单实体类
└── CrmOrderDetail.java              # 订单详情实体类
```

### 业务对象和视图对象
```
server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/domain/
├── bo/
│   ├── CrmOrderBo.java              # 订单业务对象
│   └── CrmOrderDetailBo.java        # 订单详情业务对象
└── vo/
    ├── CrmOrderVo.java              # 订单视图对象
    └── CrmOrderDetailVo.java        # 订单详情视图对象
```

### Service层
```
server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/service/
├── ICrmOrderService.java            # 订单服务接口
├── ICrmOrderDetailService.java      # 订单详情服务接口
└── impl/
    ├── CrmOrderServiceImpl.java     # 订单服务实现
    └── CrmOrderDetailServiceImpl.java # 订单详情服务实现
```

### Controller层
```
server/lookah-business/lookah-crm/lookah-crm-api/src/main/java/com/imhuso/crm/controller/admin/
└── CrmOrderController.java          # 订单控制器
```

### Mapper层
```
server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/mapper/
├── CrmOrderMapper.java              # 订单Mapper接口
└── CrmOrderDetailMapper.java        # 订单详情Mapper接口
```

## 前端实现

### 页面结构
```
web/apps/lookah-admin/src/views/crm/customer-order/
├── index.vue                        # 订单列表主页面
├── order-drawer.vue                 # 订单抽屉组件
├── data.tsx                         # 数据配置文件
└── components/
    ├── order-form.vue               # 订单表单组件
    └── order-view.vue               # 订单查看组件
```

### API接口
```
web/apps/lookah-admin/src/api/crm/order/
├── index.ts                         # API接口定义
└── model.d.ts                       # 类型定义
```

## API接口说明

### 主要接口

#### 1. 查询订单列表
```
GET /crm/order/list
权限：crm:order:list
参数：分页参数 + 查询条件
返回：PageResult<CrmOrderVo>
```

#### 2. 查询我的客户订单列表
```
GET /crm/order/my-list
权限：crm:order:list
说明：只查询当前用户负责客户的订单
```

#### 3. 获取订单详情
```
GET /crm/order/{orderId}
权限：crm:order:query
返回：CrmOrderVo（包含订单详情列表）
```

#### 4. 新增订单
```
POST /crm/order
权限：crm:order:add
参数：CrmOrderBo（包含订单详情列表）
```

#### 5. 修改订单
```
PUT /crm/order
权限：crm:order:edit
参数：CrmOrderBo（包含订单详情列表）
```

#### 6. 删除订单
```
DELETE /crm/order/{orderIds}
权限：crm:order:remove
支持：批量删除
```

#### 7. 更新订单状态
```
PUT /crm/order/{orderId}/status
权限：crm:order:edit
参数：orderStatus, remark（可选）
```

#### 8. 生成订单号
```
GET /crm/order/generate-order-no
权限：crm:order:add
返回：自动生成的订单号
```

#### 9. 导出订单
```
POST /crm/order/export
权限：crm:order:export
参数：查询条件
返回：Excel文件
```

## 权限配置

### 菜单权限
- `crm:order:list` - 订单列表查看
- `crm:order:query` - 订单详情查询
- `crm:order:add` - 订单新增
- `crm:order:edit` - 订单修改
- `crm:order:remove` - 订单删除
- `crm:order:export` - 订单导出

### 数据权限
- 业务员只能查看自己负责客户的订单
- 管理员可以查看所有订单
- 部门主管可以查看本部门的订单

## 部署说明

### 1. 执行数据库脚本
```bash
# 创建订单表
mysql -u username -p database_name < server/script/sql/crm/crm_order.sql
mysql -u username -p database_name < server/script/sql/crm/crm_order_detail.sql

# 创建菜单和权限
mysql -u username -p database_name < server/script/sql/crm/migration/2025-07-21/customer_order_menu.sql
```

### 2. 重启后端服务
重启Spring Boot应用以加载新的Controller和Service

### 3. 前端部署
前端代码会自动通过动态路由加载，无需额外配置

## 技术特点

### 后端特点
- 使用MyBatis-Plus进行数据访问
- 支持软删除和逻辑删除
- 事务管理确保数据一致性
- 数据权限控制
- 参数验证和异常处理

### 前端特点
- 使用VXE Table进行数据展示
- 响应式设计，支持移动端
- 组件化开发，易于维护
- 支持数据导出和打印
- 实时数据更新

### 安全特性
- 接口权限验证
- 数据权限控制
- 操作日志记录
- 防重复提交
- 数据脱敏处理

## 扩展性

### 未来扩展方向
1. 订单工作流引擎
2. 订单状态自动化流转
3. 与财务系统集成
4. 与物流系统集成
5. 订单统计分析
6. 移动端支持

### 配置化支持
- 订单状态可配置
- 支付方式可扩展
- 字段显示可定制
- 权限规则可配置

## 注意事项

1. **数据一致性**：订单和订单详情使用事务确保数据一致性
2. **性能优化**：大数据量查询使用分页和索引优化
3. **权限控制**：严格按照数据权限控制访问范围
4. **错误处理**：完善的异常处理和用户提示
5. **日志记录**：重要操作记录操作日志

## 联系方式

如有问题，请联系开发团队或查看相关技术文档。
