-- ----------------------------
-- 清理客户查重菜单配置
-- Author: AI Assistant
-- Date: 2025-07-21
-- Description: 清理客户查重相关的菜单和权限配置
-- ----------------------------

-- =============================================
-- 清理客户查重菜单配置
-- =============================================

-- 删除角色菜单关联
DELETE FROM `sys_role_menu` WHERE `menu_id` IN (1859425395123200006, 1859425395123200007, 1859425395123200008);

-- 删除菜单项
DELETE FROM `sys_menu` WHERE `menu_id` IN (1859425395123200006, 1859425395123200007, 1859425395123200008);

-- 验证清理结果
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 客户查重菜单已清理完成'
        ELSE CONCAT('❌ 还有 ', COUNT(*), ' 个菜单项未清理')
    END AS cleanup_status
FROM sys_menu 
WHERE menu_id IN (1859425395123200006, 1859425395123200007, 1859425395123200008);

SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 客户查重权限已清理完成'
        ELSE CONCAT('❌ 还有 ', COUNT(*), ' 个权限关联未清理')
    END AS permission_cleanup_status
FROM sys_role_menu 
WHERE menu_id IN (1859425395123200006, 1859425395123200007, 1859425395123200008);

SELECT '🧹 客户查重菜单清理完成！' AS message;
