-- ----------------------------
-- Migration: 添加客户查重管理菜单
-- Author: AI Assistant
-- Date: 2025-07-21
-- Description: 为CRM模块添加客户查重管理相关菜单和权限
-- ----------------------------

-- =============================================
-- 清理已存在的菜单（如果需要重新执行）
-- =============================================

-- 删除角色菜单关联
DELETE FROM `sys_role_menu` WHERE `menu_id` IN (1859425395123200006, 1859425395123200007, 1859425395123200008);

-- 删除菜单项
DELETE FROM `sys_menu` WHERE `menu_id` IN (1859425395123200006, 1859425395123200007, 1859425395123200008);

-- =============================================
-- 插入客户查重管理菜单
-- =============================================

-- 1. 插入客户查重列表菜单（页面）- 作为客户管理的子菜单
INSERT IGNORE INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1859425395123200006, '客户查重', 8100, 4, 'customer-duplicate', 'crm/customer-duplicate/index', '',
    1, 1, 'C', '1', '1', 'crm:customer:duplicate', 'lucide:search',
    103, 1, NOW(), 1, NOW(), '客户查重管理页面'
);

-- =============================================
-- 插入客户查重管理相关的功能按钮权限
-- =============================================

-- 客户查重查询权限
INSERT IGNORE INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1859425395123200007, '客户查重查询', 1859425395123200006, 1, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:customer:duplicate:query', '#',
    103, 1, NOW(), 1, NOW(), '客户查重查询权限'
);

-- 客户查重查看权限
INSERT IGNORE INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1859425395123200008, '客户查重查看', 1859425395123200006, 2, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:customer:duplicate:view', '#',
    103, 1, NOW(), 1, NOW(), '客户查重查看详情权限'
);

-- =============================================
-- 为超级管理员角色分配客户查重菜单权限
-- =============================================

-- 为角色ID=1（超级管理员）分配所有客户查重菜单权限
INSERT IGNORE INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
(1, 1859425395123200006),  -- 客户查重
(1, 1859425395123200007),  -- 客户查重查询
(1, 1859425395123200008);  -- 客户查重查看

-- =============================================
-- 验证插入的菜单数据
-- =============================================

-- 查看客户查重菜单树结构
SELECT
    m1.menu_id,
    m1.menu_name,
    m1.parent_id,
    m2.menu_name as parent_name,
    m1.order_num,
    m1.path,
    m1.component,
    m1.menu_type,
    m1.perms,
    m1.icon
FROM sys_menu m1
LEFT JOIN sys_menu m2 ON m1.parent_id = m2.menu_id
WHERE m1.menu_id >= 1859425395123200006 AND m1.menu_id <= 1859425395123200008
ORDER BY m1.parent_id, m1.order_num;

-- 查看角色菜单分配情况
SELECT
    rm.role_id,
    r.role_name,
    m.menu_id,
    m.menu_name,
    m.perms
FROM sys_role_menu rm
JOIN sys_role r ON rm.role_id = r.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE rm.menu_id >= 1859425395123200006 AND rm.menu_id <= 1859425395123200008
ORDER BY rm.role_id, m.menu_id;
