# CRM客户查重功能实现详细说明

## 概述

客户查重功能用于帮助用户查询和识别系统中的重复客户信息，提供统一的客户数据查看界面，便于数据清理和客户信息管理。

## 功能特性

### 1. 核心功能
- **全量客户查询**：查询系统中所有客户信息，不受数据权限限制
- **高级搜索**：支持按公司名称、客户编号、联系人等多维度搜索
- **客户详情查看**：提供完整的客户信息查看功能
- **分页展示**：支持大数据量的分页查询和展示

### 2. 查重特性
- **跨权限查询**：突破普通客户列表的数据权限限制
- **统一视图**：将所有客户（私有、公海）统一展示
- **详细信息**：显示客户的完整信息便于识别重复

## 技术实现

### 后端实现

#### 1. Controller接口
```java
/**
 * 查询所有客户信息列表（用于客户查重）
 */
@SaCheckPermission("crm:customer:duplicate")
@GetMapping("/duplicate-list")
public TableDataInfo<CrmCustomerVo> duplicateList(CrmCustomerBo bo, PageQuery pageQuery) {
    return crmCustomerService.queryAllCustomerPageList(bo, pageQuery);
}
```

#### 2. Service方法
```java
/**
 * 查询所有客户信息分页列表（用于客户查重）
 */
@Override
public TableDataInfo<CrmCustomerVo> queryAllCustomerPageList(CrmCustomerBo bo, PageQuery pageQuery) {
    LambdaQueryWrapper<CrmCustomer> lqw = buildQueryWrapper(bo);
    
    // 客户查重不需要数据权限过滤，查询所有客户
    Page<CrmCustomerVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
    
    log.info("查询客户查重列表，查询条件: {}, 结果数量: {}", bo, result.getRecords().size());
    
    return TableDataInfo.build(result);
}
```

#### 3. 权限配置
- `crm:customer:duplicate` - 客户查重基础权限
- `crm:customer:duplicate:query` - 客户查重查询权限
- `crm:customer:duplicate:view` - 客户查重查看权限

### 前端实现

#### 1. 页面结构
```
web/apps/lookah-admin/src/views/crm/customer-duplicate/
├── index.vue                    # 主页面
├── customer-duplicate-drawer.vue # 客户详情抽屉
└── data.tsx                     # 表格配置和搜索表单
```

#### 2. 核心功能
- **表格展示**：使用VxeGrid展示客户列表
- **搜索功能**：支持多条件搜索和筛选
- **详情查看**：点击查看按钮显示客户详细信息
- **响应式设计**：适配不同屏幕尺寸

#### 3. 路由配置
- **动态路由**：通过SQL菜单配置自动生成路由
- **组件路径**：`crm/customer-duplicate/index`
- **访问路径**：`/customer-duplicate`

## 菜单配置

### 菜单层级

**现有菜单结构**（推荐使用）：
```
CRM管理 (7000)
└── 客户池 (7100)
    ├── 客户列表 (7101)
    ├── 公海客户 (7102)
    ├── 客户查重 (7103)  # 更新现有菜单
    │   ├── 客户查重查询 (7130)
    │   └── 客户查重查看 (7131)
    └── 客户画像 (7104)
```

**备用菜单结构**（如果需要新建）：
```
客户管理 (8100)
└── 客户查重 (1859425395123200006)  # 新建菜单
    ├── 客户查重查询 (1859425395123200007)
    └── 客户查重查看 (1859425395123200008)
```

### 菜单ID说明
- **现有菜单更新**：使用 7103, 7130, 7131
- **新建菜单**：使用雪花算法ID 1859425395123200006-008

## 使用说明

### 1. 访问入口
- 导航路径：CRM管理 → 客户管理 → 客户查重
- 权限要求：需要 `crm:customer:duplicate` 权限

### 2. 功能操作
1. **查询客户**：
   - 使用搜索表单输入查询条件
   - 支持公司名称、客户编号、联系人等搜索
   - 点击查询按钮执行搜索

2. **查看详情**：
   - 点击表格中的"查看"按钮
   - 在抽屉中查看客户完整信息
   - 支持查看客户的所有详细字段

3. **数据导出**：
   - 支持将查询结果导出为Excel文件
   - 便于线下数据分析和处理

### 3. 查重识别
- **相同公司名称**：重点关注公司名称相同的记录
- **相似联系信息**：检查邮箱、电话等联系方式
- **地址信息**：比较客户地址信息的相似性
- **业务员信息**：查看是否有不同业务员管理相同客户

## 注意事项

### 1. 权限说明
- 客户查重功能不受数据权限限制
- 可以查看所有用户创建的客户信息
- 仅用于查看，不提供编辑和删除功能

### 2. 性能考虑
- 大数据量查询时建议使用搜索条件
- 分页查询避免一次性加载过多数据
- 导出功能限制在合理的数据量范围内

### 3. 数据安全
- 查重功能仅供内部管理使用
- 不对外暴露客户敏感信息
- 操作日志记录便于审计

## 扩展功能

### 未来可扩展的功能
1. **智能查重算法**：基于相似度算法自动识别重复客户
2. **合并建议**：提供客户信息合并的建议和工具
3. **查重报告**：生成客户查重分析报告
4. **批量处理**：支持批量标记和处理重复客户

## 技术架构

### 后端技术栈
- Spring Boot 3.x
- MyBatis Plus
- Sa-Token (权限控制)
- MySQL 8.0

### 前端技术栈
- Vue 3
- TypeScript
- Ant Design Vue
- VxeTable
- Vben Admin

## 部署说明

### 1. 一键部署
```bash
# 执行SQL脚本，自动完成所有菜单和权限配置
mysql -u username -p database_name < customer_duplicate_menu_final.sql
```

### 2. 自动化特性
- **智能检测**：自动检测现有菜单配置
- **动态路由**：系统通过SQL菜单配置自动生成动态路由
- **组件加载**：前端组件路径 `crm/customer-duplicate/index` 自动识别
- **权限分配**：自动为超级管理员分配所有权限
- **状态验证**：执行后自动显示配置结果

### 3. 功能测试
- 测试查询功能是否正常
- 验证权限控制是否生效
- 检查数据展示是否完整
- 验证动态路由是否正确加载
