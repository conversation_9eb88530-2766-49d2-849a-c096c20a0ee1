# CRM模块数据库迁移 2025-07-18

## 版本概述

本版本主要添加时间戳字段和唯一索引，支持软删除场景下的数据唯一性约束。

## 迁移文件列表

### 01.add_timestamp_and_unique_index.sql
**功能**: 添加时间戳字段和唯一索引
**影响表**: `crm_customer`

**主要变更**:
- 添加 `deleted_at` 时间戳字段（毫秒级）
- 修改唯一索引支持软删除场景
- 创建复合索引优化查询性能
- 添加触发器自动管理删除时间戳
- 创建视图和存储过程

## 详细变更说明

### 1. 新增字段
- `deleted_at` BIGINT - 删除时间戳（毫秒，NULL表示未删除）

### 2. 索引变更

#### 删除的索引
- `uk_account_email` - 原账户邮箱唯一索引
- `uk_customer_code` - 原客户编号唯一索引

#### 新增的索引
- `uk_account_email_deleted_at` - 账户邮箱+删除时间戳唯一索引
- `uk_customer_code_deleted_at` - 客户编号+删除时间戳唯一索引
- `idx_deleted_at` - 删除时间戳索引
- `idx_del_flag_deleted_at` - 删除标志+删除时间戳复合索引
- `idx_sales_user_del_flag` - 业务员+删除标志复合索引
- `idx_customer_status_del_flag` - 客户状态+删除标志复合索引

### 3. 唯一性约束逻辑

#### 修改前
```sql
-- 账户邮箱全局唯一（包括已删除数据）
UNIQUE KEY uk_account_email (account_email)

-- 客户编号全局唯一（包括已删除数据）
UNIQUE KEY uk_customer_code (customer_code)
```

#### 修改后
```sql
-- 账户邮箱在未删除数据中唯一，已删除数据可重复
UNIQUE KEY uk_account_email_deleted_at (account_email, deleted_at)

-- 客户编号在未删除数据中唯一，已删除数据可重复
UNIQUE KEY uk_customer_code_deleted_at (customer_code, deleted_at)
```

### 4. 业务逻辑说明

#### 软删除逻辑
1. **删除时**: `del_flag = '1'`, `deleted_at = 当前时间戳(毫秒)`
2. **恢复时**: `del_flag = '0'`, `deleted_at = NULL`
3. **查询活跃数据**: `WHERE del_flag = '0' AND deleted_at IS NULL`

#### 唯一性检查
1. **新增客户**: 检查 `account_email` 在 `deleted_at IS NULL` 的记录中是否唯一
2. **修改邮箱**: 检查新邮箱在 `deleted_at IS NULL` 的记录中是否唯一
3. **恢复客户**: 检查邮箱在当前活跃记录中是否唯一

### 5. 自动化功能

#### 触发器
- `tr_crm_customer_soft_delete`: 自动设置和清除删除时间戳

#### 视图
- `v_crm_customer_active`: 活跃客户视图
- `v_crm_customer_deleted`: 已删除客户视图

#### 存储过程
- `sp_cleanup_deleted_customers(days)`: 物理删除指定天数前的软删除数据

## 执行步骤

### 1. 备份数据
```bash
mysqldump -u username -p database_name crm_customer > crm_customer_backup_20250718.sql
```

### 2. 执行迁移
```bash
mysql -u username -p database_name < 01.add_timestamp_and_unique_index.sql
```

### 3. 验证结果
```sql
-- 检查表结构
SHOW CREATE TABLE crm_customer;

-- 检查索引
SHOW INDEX FROM crm_customer;

-- 检查数据完整性
SELECT COUNT(*) FROM crm_customer WHERE del_flag = '0';
SELECT COUNT(*) FROM crm_customer WHERE del_flag = '1' AND deleted_at IS NOT NULL;
```

## 注意事项

### 1. 数据一致性
- 迁移前确保没有重复的账户邮箱和客户编号
- 已删除的数据会自动设置删除时间戳

### 2. 应用程序适配
- 需要修改后端代码支持新的唯一性检查逻辑
- 软删除时需要设置 `deleted_at` 字段
- 查询时需要考虑 `deleted_at IS NULL` 条件

### 3. 性能影响
- 新增的复合索引会占用额外存储空间
- 查询性能会有所提升，特别是按删除状态过滤的查询

### 4. 回滚方案
如需回滚，可执行以下SQL：
```sql
-- 删除新增的索引
ALTER TABLE crm_customer DROP INDEX uk_account_email_deleted_at;
ALTER TABLE crm_customer DROP INDEX uk_customer_code_deleted_at;
ALTER TABLE crm_customer DROP INDEX idx_deleted_at;
ALTER TABLE crm_customer DROP INDEX idx_del_flag_deleted_at;
ALTER TABLE crm_customer DROP INDEX idx_sales_user_del_flag;
ALTER TABLE crm_customer DROP INDEX idx_customer_status_del_flag;

-- 恢复原有唯一索引
ALTER TABLE crm_customer ADD UNIQUE KEY uk_account_email (account_email);
ALTER TABLE crm_customer ADD UNIQUE KEY uk_customer_code (customer_code);

-- 删除新增字段
ALTER TABLE crm_customer DROP COLUMN deleted_at;

-- 删除触发器
DROP TRIGGER IF EXISTS tr_crm_customer_soft_delete;

-- 删除视图
DROP VIEW IF EXISTS v_crm_customer_active;
DROP VIEW IF EXISTS v_crm_customer_deleted;

-- 删除存储过程
DROP PROCEDURE IF EXISTS sp_cleanup_deleted_customers;
```

## 测试用例

### 1. 唯一性测试
```sql
-- 测试1: 新增相同邮箱的客户（应该失败）
INSERT INTO crm_customer (customer_code, company_name, account_email, ...) 
VALUES ('TEST001', 'Test Company', '<EMAIL>', ...);

-- 测试2: 删除客户后新增相同邮箱（应该成功）
UPDATE crm_customer SET del_flag = '1' WHERE account_email = '<EMAIL>';
INSERT INTO crm_customer (customer_code, company_name, account_email, ...) 
VALUES ('TEST002', 'Test Company 2', '<EMAIL>', ...);
```

### 2. 软删除测试
```sql
-- 测试删除时间戳自动设置
UPDATE crm_customer SET del_flag = '1' WHERE customer_id = 1;
SELECT deleted_at FROM crm_customer WHERE customer_id = 1;

-- 测试恢复时时间戳清除
UPDATE crm_customer SET del_flag = '0' WHERE customer_id = 1;
SELECT deleted_at FROM crm_customer WHERE customer_id = 1;
```

## 维护建议

### 1. 定期清理
建议每月执行一次物理删除：
```sql
CALL sp_cleanup_deleted_customers(90); -- 删除90天前的软删除数据
```

### 2. 监控索引使用情况
```sql
-- 查看索引使用统计
SELECT * FROM information_schema.INDEX_STATISTICS 
WHERE TABLE_SCHEMA = 'your_database' AND TABLE_NAME = 'crm_customer';
```

### 3. 性能监控
定期检查查询性能，特别是涉及软删除条件的查询。
