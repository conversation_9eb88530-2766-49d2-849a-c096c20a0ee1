-- ----------------------------
-- Migration: 为CRM客户表添加时间戳字段和唯一索引
-- Author: jf
-- Date: 2025-07-18
-- Description: 添加删除时间戳字段，创建客户邮箱和删除时间戳的唯一索引，支持软删除后的数据唯一性
-- ----------------------------

-- =============================================
-- 1. 添加删除时间戳字段
-- =============================================

-- 添加删除时间戳字段（用于软删除时记录删除时间）
ALTER TABLE `crm_customer` ADD COLUMN `deleted_at` bigint DEFAULT NULL COMMENT '删除时间戳（毫秒，软删除时记录，NULL表示未删除）' AFTER `del_flag`;

-- 创建删除时间戳索引
ALTER TABLE `crm_customer` ADD KEY `idx_deleted_at` (`deleted_at`);

-- =============================================
-- 2. 修改现有唯一索引，支持软删除
-- =============================================

-- 删除原有的账户邮箱唯一索引
ALTER TABLE `crm_customer` DROP INDEX `uk_account_email`;

-- 创建新的复合唯一索引：账户邮箱 + 删除时间戳
-- 这样可以确保：
-- 1. 未删除的记录（deleted_at = NULL）中，account_email 唯一
-- 2. 已删除的记录可以有相同的 account_email，但 deleted_at 不同
ALTER TABLE `crm_customer` ADD UNIQUE KEY `uk_account_email_deleted_at` (`account_email`, `deleted_at`);

-- =============================================
-- 3. 添加客户编号的软删除支持
-- =============================================

-- 删除原有的客户编号唯一索引
ALTER TABLE `crm_customer` DROP INDEX `uk_customer_code`;

-- 创建新的复合唯一索引：客户编号 + 删除时间戳
ALTER TABLE `crm_customer` ADD UNIQUE KEY `uk_customer_code_deleted_at` (`customer_code`, `deleted_at`);

-- =============================================
-- 4. 创建复合索引优化查询性能
-- =============================================

-- 创建删除标志和删除时间戳的复合索引
ALTER TABLE `crm_customer` ADD KEY `idx_del_flag_deleted_at` (`del_flag`, `deleted_at`);

-- 创建业务员和删除状态的复合索引
ALTER TABLE `crm_customer` ADD KEY `idx_sales_user_del_flag` (`sales_user_id`, `del_flag`);

-- 创建客户状态和删除状态的复合索引
ALTER TABLE `crm_customer` ADD KEY `idx_customer_status_del_flag` (`customer_status`, `del_flag`);

-- =============================================
-- 5. 数据迁移：为现有数据设置删除时间戳
-- =============================================

-- 为已删除的数据设置删除时间戳（使用更新时间作为删除时间）
UPDATE `crm_customer` 
SET `deleted_at` = UNIX_TIMESTAMP(IFNULL(`update_time`, `create_time`)) * 1000
WHERE `del_flag` = '1' AND `deleted_at` IS NULL;

-- =============================================
-- 6. 验证数据完整性
-- =============================================

-- 检查是否有重复的账户邮箱（在未删除的记录中）
SELECT 
    `account_email`, 
    COUNT(*) as count,
    GROUP_CONCAT(`customer_id`) as customer_ids
FROM `crm_customer` 
WHERE `del_flag` = '0' 
GROUP BY `account_email` 
HAVING COUNT(*) > 1;

-- 检查是否有重复的客户编号（在未删除的记录中）
SELECT 
    `customer_code`, 
    COUNT(*) as count,
    GROUP_CONCAT(`customer_id`) as customer_ids
FROM `crm_customer` 
WHERE `del_flag` = '0' 
GROUP BY `customer_code` 
HAVING COUNT(*) > 1;

-- =============================================
-- 7. 创建视图简化查询
-- =============================================

-- 创建活跃客户视图（未删除的客户）
CREATE OR REPLACE VIEW `v_crm_customer_active` AS
SELECT *
FROM `crm_customer`
WHERE `del_flag` = '0' AND `deleted_at` IS NULL;

-- 创建已删除客户视图
CREATE OR REPLACE VIEW `v_crm_customer_deleted` AS
SELECT *
FROM `crm_customer`
WHERE `del_flag` = '1' AND `deleted_at` IS NOT NULL;

-- =============================================
-- 8. 添加触发器自动设置删除时间戳
-- =============================================

DELIMITER $$

-- 创建更新触发器：当del_flag变为'1'时自动设置deleted_at
CREATE TRIGGER `tr_crm_customer_soft_delete` 
BEFORE UPDATE ON `crm_customer`
FOR EACH ROW
BEGIN
    -- 如果删除标志从'0'变为'1'，设置删除时间戳
    IF OLD.del_flag = '0' AND NEW.del_flag = '1' AND NEW.deleted_at IS NULL THEN
        SET NEW.deleted_at = UNIX_TIMESTAMP(NOW(3)) * 1000;
    END IF;
    
    -- 如果删除标志从'1'变为'0'，清除删除时间戳（恢复数据）
    IF OLD.del_flag = '1' AND NEW.del_flag = '0' THEN
        SET NEW.deleted_at = NULL;
    END IF;
END$$

DELIMITER ;

-- =============================================
-- 9. 创建存储过程用于数据清理
-- =============================================

DELIMITER $$

-- 创建存储过程：物理删除指定天数前的软删除数据
CREATE PROCEDURE `sp_cleanup_deleted_customers`(IN days_ago INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE customer_count INT DEFAULT 0;
    DECLARE cutoff_timestamp BIGINT;
    
    -- 计算截止时间戳（指定天数前）
    SET cutoff_timestamp = UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL days_ago DAY)) * 1000;
    
    -- 统计要删除的记录数
    SELECT COUNT(*) INTO customer_count
    FROM `crm_customer`
    WHERE `del_flag` = '1' 
      AND `deleted_at` IS NOT NULL 
      AND `deleted_at` < cutoff_timestamp;
    
    -- 物理删除数据
    DELETE FROM `crm_customer`
    WHERE `del_flag` = '1' 
      AND `deleted_at` IS NOT NULL 
      AND `deleted_at` < cutoff_timestamp;
    
    -- 返回删除的记录数
    SELECT CONCAT('已物理删除 ', customer_count, ' 条客户记录') as result;
END$$

DELIMITER ;

-- =============================================
-- 10. 验证迁移结果
-- =============================================

-- 显示表结构变更
SHOW CREATE TABLE `crm_customer`;

-- 显示索引信息
SHOW INDEX FROM `crm_customer`;

-- 统计数据
SELECT 
    '总客户数' as type,
    COUNT(*) as count
FROM `crm_customer`
UNION ALL
SELECT 
    '活跃客户数' as type,
    COUNT(*) as count
FROM `crm_customer`
WHERE `del_flag` = '0'
UNION ALL
SELECT 
    '已删除客户数' as type,
    COUNT(*) as count
FROM `crm_customer`
WHERE `del_flag` = '1';

-- =============================================
-- 迁移完成提示
-- =============================================
SELECT '=== CRM客户表时间戳字段和唯一索引迁移完成 ===' as message;
SELECT '1. 已添加 deleted_at 时间戳字段' as step1;
SELECT '2. 已创建 account_email + deleted_at 唯一索引' as step2;
SELECT '3. 已创建 customer_code + deleted_at 唯一索引' as step3;
SELECT '4. 已创建相关复合索引优化查询性能' as step4;
SELECT '5. 已创建触发器自动管理删除时间戳' as step5;
SELECT '6. 已创建视图和存储过程' as step6;
