# CRM系统功能增强 - 部署指南

## 📋 部署概述

本次更新包含两个主要功能：
1. **客户星级规则优化**：完善客户星级计算规则，提供批量更新功能
2. **客户跟进记录增强**：新增跟进主题和跟进客户状态字段

需要按顺序执行数据库脚本和代码部署。

## 🔄 部署步骤

### 第一步：数据库更新

#### 1.1 执行客户星级规则优化
```bash
# 执行客户星级规则优化脚本
mysql -u [username] -p [database_name] < 01.update_customer_star_rating_rules.sql
```

**脚本内容**:
- 创建优化的客户星级计算存储过程
- 创建批量更新存储过程
- 创建星级统计视图
- 提供批量更新功能（可选执行）

#### 1.2 执行跟进记录表结构修改
```bash
# 执行跟进记录表结构修改脚本
mysql -u [username] -p [database_name] < 02.add_follow_record_fields.sql
```

**脚本内容**:
- 为 `crm_customer_follow_record` 表添加 `follow_theme` 和 `follow_customer_status` 字段
- 添加相应的索引以提高查询性能

#### 1.3 执行跟进主题字典数据插入
```bash
# 执行字典数据插入脚本
mysql -u [username] -p [database_name] < 03.add_follow_theme_dict.sql
```

**脚本内容**:
- 添加 `crm_follow_theme` 字典类型
- 插入跟进主题字典数据（新客首次触达、日常跟进、新品推广、回答疑问）

#### 1.4 验证数据库更新
```sql
-- 验证客户星级存储过程
SHOW PROCEDURE STATUS WHERE Name IN ('UpdateCustomerStarRating', 'BatchUpdateCustomerStarRating');

-- 验证星级统计视图
SELECT * FROM v_customer_star_rating_statistics;

-- 验证跟进记录表结构
DESCRIBE crm_customer_follow_record;

-- 验证跟进主题字典数据
SELECT dt.dict_name, dd.dict_label, dd.dict_value 
FROM sys_dict_type dt
JOIN sys_dict_data dd ON dt.dict_type = dd.dict_type
WHERE dt.dict_type = 'crm_follow_theme'
ORDER BY dd.dict_sort;
```

### 第二步：后端代码部署

#### 2.1 更新的文件列表
```
server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/domain/
├── CrmCustomerFollowRecord.java          # 实体类 - 添加新字段
├── bo/CrmCustomerFollowRecordBo.java     # 业务对象 - 添加验证注解
└── vo/CrmCustomerFollowRecordVo.java     # 视图对象 - 添加Excel支持
```

#### 2.2 编译和打包
```bash
# 进入项目根目录
cd server/

# 清理并编译
mvn clean compile

# 打包
mvn package -DskipTests
```

#### 2.3 部署后端服务
```bash
# 停止现有服务
systemctl stop lookah-crm

# 备份现有jar包
cp lookah-crm.jar lookah-crm.jar.backup.$(date +%Y%m%d_%H%M%S)

# 部署新jar包
cp target/lookah-crm.jar /path/to/deployment/

# 启动服务
systemctl start lookah-crm

# 检查服务状态
systemctl status lookah-crm
```

### 第三步：前端代码部署

#### 3.1 更新的文件列表
```
web/apps/lookah-admin/src/
├── constants/index.ts                    # 添加字典枚举常量
├── views/crm/customer/data.tsx          # 更新表单和列表配置
└── utils/dict-form-helper.tsx           # 字典表单助手工具
```

#### 3.2 构建和部署
```bash
# 进入前端项目目录
cd web/

# 安装依赖（如有新增）
npm install

# 构建生产版本
npm run build

# 部署到Web服务器
cp -r dist/* /path/to/web/server/
```

### 第四步：可选的批量更新操作

#### 4.1 客户星级批量更新（可选）
```sql
-- 注意：此操作会更新所有客户的星级，建议在业务低峰期执行
-- 执行前请确认并备份数据
CALL BatchUpdateCustomerStarRating();
```

## 🧪 功能测试

### 测试用例1：客户星级功能测试
1. 验证存储过程是否正常创建
2. 测试单个客户星级更新功能
3. 查看星级统计视图数据
4. 验证星级计算规则是否正确

### 测试用例2：跟进记录新增测试
1. 登录CRM系统
2. 进入客户管理 -> 客户列表
3. 选择一个客户，点击"跟进记录"
4. 点击"新增跟进记录"
5. 验证表单中是否显示"跟进主题"和"跟进客户状态"字段
6. 选择跟进主题和客户状态，填写其他必填字段
7. 保存记录，验证是否成功
8. **重要**：验证客户的状态是否自动更新为跟进记录中设置的客户状态

### 测试用例3：跟进记录列表显示测试
1. 在跟进记录列表中验证是否显示新增的两个字段列
2. 验证字典值是否正确显示为对应的标签
3. 测试按跟进主题和客户状态进行筛选

### 测试用例4：客户状态自动同步测试
1. 选择一个状态为"潜在客户"的客户
2. 新增跟进记录，设置跟进客户状态为"有兴趣"
3. 保存跟进记录后，返回客户列表
4. 验证该客户的状态是否已自动更新为"有兴趣"
5. 查看系统日志，确认状态更新操作已记录

### 测试用例5：数据导出测试
1. 导出跟进记录Excel文件
2. 验证新字段是否包含在导出文件中
3. 验证字典值是否正确转换为标签显示

## 🔍 问题排查

### 常见问题1：存储过程创建失败
**症状**: 执行SQL脚本时报存储过程语法错误
**排查步骤**:
1. 检查MySQL版本是否支持存储过程
2. 确认用户是否有创建存储过程的权限
3. 检查SQL脚本中的分隔符设置

### 常见问题2：字典数据不显示
**症状**: 下拉选项为空或显示异常
**排查步骤**:
1. 检查数据库中字典数据是否正确插入
2. 检查前端常量 `CrmDictEnum.CRM_FOLLOW_THEME` 是否正确定义
3. 检查浏览器控制台是否有JavaScript错误

### 常见问题3：新字段不显示
**症状**: 表单中看不到新增字段
**排查步骤**:
1. 检查前端代码是否正确部署
2. 清除浏览器缓存
3. 检查表单配置是否正确

### 常见问题4：批量更新执行缓慢
**症状**: 批量更新客户星级执行时间过长
**排查步骤**:
1. 检查数据库服务器资源使用情况
2. 确认是否在业务低峰期执行
3. 考虑分批次执行更新

## 📊 性能影响评估

### 数据库性能
- **新增字段**: 对现有查询性能无影响
- **新增索引**: 提高按主题和状态查询的性能
- **存储过程**: 提高星级计算效率
- **存储空间**: 每条跟进记录增加约100字节存储空间

### 应用性能
- **内存使用**: 增加微量内存使用（字典缓存）
- **响应时间**: 对现有功能响应时间无明显影响
- **并发性能**: 无影响

## 🔒 安全考虑

### 数据验证
- 后端添加了字段长度验证
- 前端使用字典选项，防止非法值输入
- 存储过程包含异常处理机制

### 权限控制
- 复用现有的跟进记录权限控制
- 存储过程执行需要相应的数据库权限
- 无需额外的权限配置

## 📈 监控建议

### 业务监控
- 监控新字段的使用率
- 统计各跟进主题的分布情况
- 分析客户状态变化趋势
- 监控客户星级分布变化

### 技术监控
- 监控相关API的响应时间
- 监控数据库查询性能
- 监控前端页面加载时间
- 监控存储过程执行性能

## 🔄 回滚方案

### 紧急回滚
如果发现严重问题需要紧急回滚：

1. **回滚后端服务**:
```bash
systemctl stop lookah-crm
cp lookah-crm.jar.backup.* lookah-crm.jar
systemctl start lookah-crm
```

2. **回滚前端代码**:
```bash
# 恢复之前的前端代码版本
git checkout [previous_commit_hash]
npm run build
cp -r dist/* /path/to/web/server/
```

3. **数据库回滚**（谨慎操作）:
```sql
-- 删除存储过程
DROP PROCEDURE IF EXISTS UpdateCustomerStarRating;
DROP PROCEDURE IF EXISTS BatchUpdateCustomerStarRating;

-- 删除视图
DROP VIEW IF EXISTS v_customer_star_rating_statistics;

-- 删除新增字段（会丢失数据）
ALTER TABLE crm_customer_follow_record 
DROP COLUMN follow_theme,
DROP COLUMN follow_customer_status;

-- 删除字典数据
DELETE FROM sys_dict_data WHERE dict_type = 'crm_follow_theme';
DELETE FROM sys_dict_type WHERE dict_type = 'crm_follow_theme';
```

**注意**: 数据库回滚会导致新字段数据丢失，请谨慎操作。

## ✅ 部署检查清单

### 数据库部分
- [ ] 客户星级存储过程创建成功
- [ ] 星级统计视图创建成功
- [ ] 跟进记录表字段添加成功
- [ ] 跟进主题字典数据插入成功
- [ ] 相关索引创建成功

### 后端部分
- [ ] 后端代码编译无错误
- [ ] 后端服务正常启动
- [ ] 实体类字段映射正确
- [ ] API接口正常响应

### 前端部分
- [ ] 前端代码构建成功
- [ ] 前端页面正常访问
- [ ] 新增字段正常显示
- [ ] 字典选项正确加载
- [ ] 表单提交功能正常
- [ ] 列表筛选功能正常

### 功能验证
- [ ] 客户星级计算功能正常
- [ ] 跟进记录新增功能正常
- [ ] 数据导出功能正常
- [ ] 权限控制正常
- [ ] 性能无明显下降

## 📞 技术支持

如在部署过程中遇到问题，请联系技术支持团队，并提供：
1. 具体的错误信息
2. 相关的日志文件
3. 部署环境信息
4. 问题复现步骤
