-- =============================================
-- 客户跟进记录新增字段
-- Author: AI Assistant  
-- Date: 2025-07-31
-- Description: 为客户跟进记录表添加跟进主题和跟进客户状态字段
-- =============================================

-- 1. 为 crm_customer_follow_record 表添加新字段
ALTER TABLE `crm_customer_follow_record` 
ADD COLUMN `follow_theme` VARCHAR(50) DEFAULT NULL COMMENT '跟进主题（新客首次触达、日常跟进、新品推广、回答疑问）' AFTER `follow_type`,
ADD COLUMN `follow_customer_status` VARCHAR(50) DEFAULT NULL COMMENT '跟进客户状态（与客户状态字典一致）' AFTER `follow_theme`;

-- 2. 添加字段索引以提高查询性能
ALTER TABLE `crm_customer_follow_record` 
ADD INDEX `idx_follow_theme` (`follow_theme`),
ADD INDEX `idx_follow_customer_status` (`follow_customer_status`);

-- =============================================
-- 验证字段添加结果
-- =============================================

-- 查看表结构
DESCRIBE `crm_customer_follow_record`;

-- 查看索引
SHOW INDEX FROM `crm_customer_follow_record`;

-- =============================================
-- 说明
-- =============================================
/*
新增字段说明：

1. follow_theme (跟进主题)
   - 类型: VARCHAR(50)
   - 允许空值: YES
   - 默认值: NULL
   - 说明: 记录本次跟进的主题类型，使用字典管理
   - 字典类型: crm_follow_theme

2. follow_customer_status (跟进客户状态)
   - 类型: VARCHAR(50) 
   - 允许空值: YES
   - 默认值: NULL
   - 说明: 记录跟进时客户的状态，与客户表的客户状态字典一致
   - 字典类型: crm_customer_status (复用现有字典)

索引说明：
- idx_follow_theme: 跟进主题索引，便于按主题统计和查询
- idx_follow_customer_status: 跟进客户状态索引，便于按状态统计和查询

注意事项：
1. 新字段允许为空，不影响现有数据
2. 跟进客户状态复用现有的客户状态字典
3. 跟进主题使用新的字典类型
4. 添加了相应的索引以提高查询性能
*/
