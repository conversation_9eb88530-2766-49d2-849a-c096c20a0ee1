-- =============================================
-- 跟进主题字典数据
-- Author: AI Assistant
-- Date: 2025-07-31
-- Description: 添加客户跟进记录的跟进主题字典数据
-- =============================================

-- 使用雪花算法生成的ID（确保唯一性）
SET @dict_type_id = 1859425395123200050;
SET @dict_data_id_start = 1859425395123200051;

-- =============================================
-- 1. 跟进主题字典类型
-- =============================================

-- 删除已存在的字典类型（如果存在）
DELETE FROM sys_dict_data WHERE dict_type = 'crm_follow_theme';
DELETE FROM sys_dict_type WHERE dict_type = 'crm_follow_theme';

-- 插入跟进主题字典类型
INSERT INTO sys_dict_type (
    dict_id, dict_name, dict_type, create_dept, create_by, create_time, 
    update_by, update_time, remark
) VALUES (
    @dict_type_id, 'CRM跟进主题', 'crm_follow_theme', 103, 1, NOW(), 
    1, NOW(), 'CRM客户跟进记录的跟进主题分类'
);

-- =============================================
-- 2. 跟进主题字典数据
-- =============================================

-- 插入跟进主题字典数据
INSERT INTO sys_dict_data (
    dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, 
    list_class, is_default, create_dept, create_by, create_time, 
    update_by, update_time, remark
) VALUES 
-- 新客首次触达
(@dict_data_id_start, 1, '新客首次触达', 'first_contact', 'crm_follow_theme', '', 'primary', 'Y', 103, 1, NOW(), 1, NOW(), '新客户的首次接触和触达'),

-- 日常跟进
(@dict_data_id_start + 1, 2, '日常跟进', 'routine_follow', 'crm_follow_theme', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), '日常的客户跟进和维护'),

-- 新品推广
(@dict_data_id_start + 2, 3, '新品推广', 'product_promotion', 'crm_follow_theme', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '新产品的推广和介绍'),

-- 回答疑问
(@dict_data_id_start + 3, 4, '回答疑问', 'answer_questions', 'crm_follow_theme', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '回答客户的疑问和咨询');

-- =============================================
-- 验证插入的字典数据
-- =============================================

-- 查看跟进主题字典
SELECT
    dt.dict_name,
    dd.dict_label,
    dd.dict_value,
    dd.list_class,
    dd.dict_sort
FROM sys_dict_type dt
JOIN sys_dict_data dd ON dt.dict_type = dd.dict_type
WHERE dt.dict_type = 'crm_follow_theme'
ORDER BY dd.dict_sort;

-- =============================================
-- 说明
-- =============================================
/*
字典类型: crm_follow_theme
字典名称: CRM跟进主题

字典数据:
1. 新客首次触达 (first_contact) - 蓝色主要样式，默认选项
2. 日常跟进 (routine_follow) - 蓝色信息样式
3. 新品推广 (product_promotion) - 绿色成功样式  
4. 回答疑问 (answer_questions) - 橙色警告样式

使用的雪花ID:
- dict_type ID: 1859425395123200050
- 字典数据起始ID: 1859425395123200051

在前端使用时:
- 字典类型: crm_follow_theme
- 可以通过 getDictOptions('crm_follow_theme') 获取选项
- 显示时会根据值显示对应的标签和样式

业务说明:
- 新客首次触达: 用于记录对新客户的首次接触
- 日常跟进: 用于记录常规的客户维护跟进
- 新品推广: 用于记录向客户推广新产品的跟进
- 回答疑问: 用于记录回答客户问题的跟进
*/
