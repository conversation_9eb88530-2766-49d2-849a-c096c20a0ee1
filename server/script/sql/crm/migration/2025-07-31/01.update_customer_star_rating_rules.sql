-- =============================================
-- 客户星级规则优化和老客户自动更新
-- Author: AI Assistant
-- Date: 2025-07-31
-- Description: 优化客户星级计算规则，并为现有老客户批量更新星级
-- =============================================

-- =============================================
-- 1. 优化星级计算规则的存储过程
-- =============================================

DELIMITER $$

-- 删除已存在的存储过程
DROP PROCEDURE IF EXISTS `UpdateCustomerStarRating`$$

-- 创建优化的客户星级计算存储过程
CREATE PROCEDURE `UpdateCustomerStarRating`(IN customer_id BIGINT)
BEGIN
    DECLARE total_count INT DEFAULT 0;
    DECLARE total_amount DECIMAL(15,2) DEFAULT 0.00;
    DECLARE avg_amount DECIMAL(15,2) DEFAULT 0.00;
    DECLARE max_amount DECIMAL(15,2) DEFAULT 0.00;
    DECLARE score INT DEFAULT 0;
    DECLARE new_star_rating INT DEFAULT 1;
    DECLARE order_frequency VARCHAR(20) DEFAULT 'irregular';

    -- 声明异常处理
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- 查询客户订单统计数据
    SELECT
        COALESCE(COUNT(*), 0),
        COALESCE(SUM(total_amount), 0.00),
        COALESCE(AVG(total_amount), 0.00),
        COALESCE(MAX(total_amount), 0.00)
    INTO total_count, total_amount, avg_amount, max_amount
    FROM crm_order
    WHERE customer_id = customer_id AND del_flag = '0';

    -- 计算综合评分（总分100分）
    SET score = 0;

    -- 1. 订单总数评分（最高25分）- 根据实际Java代码
    IF total_count >= 50 THEN
        SET score = score + 25;
    ELSEIF total_count >= 20 THEN
        SET score = score + 20;
    ELSEIF total_count >= 10 THEN
        SET score = score + 16;
    ELSEIF total_count >= 5 THEN
        SET score = score + 12;
    ELSEIF total_count >= 2 THEN
        SET score = score + 8;
    ELSEIF total_count >= 1 THEN
        SET score = score + 4;
    END IF;

    -- 2. 订单总金额评分（最高20分）- 根据实际Java代码
    IF total_amount >= 100000 THEN
        SET score = score + 20;
    ELSEIF total_amount >= 50000 THEN
        SET score = score + 17;
    ELSEIF total_amount >= 20000 THEN
        SET score = score + 14;
    ELSEIF total_amount >= 10000 THEN
        SET score = score + 11;
    ELSEIF total_amount >= 5000 THEN
        SET score = score + 8;
    ELSEIF total_amount >= 1000 THEN
        SET score = score + 5;
    ELSEIF total_amount > 0 THEN
        SET score = score + 2;
    END IF;

    -- 3. 平均订单金额评分（最高20分）- 根据实际Java代码
    IF avg_amount >= 5000 THEN
        SET score = score + 20;
    ELSEIF avg_amount >= 2000 THEN
        SET score = score + 16;
    ELSEIF avg_amount >= 1000 THEN
        SET score = score + 12;
    ELSEIF avg_amount >= 500 THEN
        SET score = score + 8;
    ELSEIF avg_amount >= 200 THEN
        SET score = score + 4;
    ELSEIF avg_amount > 0 THEN
        SET score = score + 2;
    END IF;

    -- 4. 下单频率评分（最高35分）- 根据实际Java代码
    -- 获取客户的下单频率
    SELECT COALESCE(c.order_frequency, 'irregular') INTO order_frequency
    FROM crm_customer c
    WHERE c.customer_id = customer_id;

    IF order_frequency = 'weekly' OR order_frequency = 'monthly' THEN
        SET score = score + 35;
    ELSEIF order_frequency = 'quarterly' THEN
        SET score = score + 20;
    ELSE
        SET score = score + 5;
    END IF;

    -- 根据总分确定星级
    IF score >= 90 THEN
        SET new_star_rating = 5;      -- 5星：超级客户
    ELSEIF score >= 75 THEN
        SET new_star_rating = 4;      -- 4星：重要客户
    ELSEIF score >= 60 THEN
        SET new_star_rating = 3;      -- 3星：优质客户
    ELSEIF score >= 40 THEN
        SET new_star_rating = 2;      -- 2星：普通客户
    ELSE
        SET new_star_rating = 1;      -- 1星：新客户/低价值客户
    END IF;

    -- 更新客户星级
    UPDATE crm_customer
    SET system_star_rating = new_star_rating,
        update_time = NOW()
    WHERE customer_id = customer_id;

    COMMIT;

END$$

DELIMITER ;

-- =============================================
-- 2. 批量更新现有客户星级的存储过程
-- =============================================

DELIMITER $$

-- 删除已存在的批量更新存储过程
DROP PROCEDURE IF EXISTS `BatchUpdateCustomerStarRating`$$

-- 创建批量更新客户星级的存储过程
CREATE PROCEDURE `BatchUpdateCustomerStarRating`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE current_customer_id BIGINT;
    DECLARE update_count INT DEFAULT 0;
    DECLARE error_count INT DEFAULT 0;

    -- 声明游标
    DECLARE customer_cursor CURSOR FOR
        SELECT customer_id
        FROM crm_customer
        WHERE del_flag = '0';

    -- 声明异常处理
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        SET error_count = error_count + 1;
    END;

    -- 开始处理
    OPEN customer_cursor;

    read_loop: LOOP
        FETCH customer_cursor INTO current_customer_id;
        IF done THEN
            LEAVE read_loop;
        END IF;

        -- 调用单个客户星级更新存储过程
        CALL UpdateCustomerStarRating(current_customer_id);
        SET update_count = update_count + 1;

        -- 每处理100个客户输出一次进度
        IF update_count % 100 = 0 THEN
            SELECT CONCAT('已处理 ', update_count, ' 个客户') AS progress;
        END IF;

    END LOOP;

    CLOSE customer_cursor;

    -- 输出最终结果
    SELECT
        update_count AS '更新客户数量',
        error_count AS '错误数量',
        CONCAT('批量更新完成，共处理 ', update_count, ' 个客户，错误 ', error_count, ' 个') AS '执行结果';

END$$

DELIMITER ;

-- =============================================
-- 3. 创建星级统计视图
-- =============================================

-- 删除已存在的视图
DROP VIEW IF EXISTS `v_customer_star_rating_statistics`;

-- 创建客户星级统计视图
CREATE VIEW `v_customer_star_rating_statistics` AS
SELECT
    system_star_rating as star_rating,
    CONCAT(system_star_rating, '星') as star_label,
    COUNT(*) as customer_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM crm_customer WHERE del_flag = '0'), 2) as percentage,
    CASE system_star_rating
        WHEN 5 THEN '超级客户'
        WHEN 4 THEN '重要客户'
        WHEN 3 THEN '优质客户'
        WHEN 2 THEN '普通客户'
        WHEN 1 THEN '新客户/低价值客户'
        ELSE '未知'
    END as customer_type_desc
FROM crm_customer
WHERE del_flag = '0'
GROUP BY system_star_rating
ORDER BY system_star_rating DESC;

-- =============================================
-- 4. 执行批量更新（可选执行）
-- =============================================

-- 注意：以下语句会更新所有客户的星级，执行前请确认
-- 如需执行，请取消注释以下语句

-- CALL BatchUpdateCustomerStarRating();

-- =============================================
-- 5. 验证更新结果
-- =============================================

-- 查看星级分布统计
SELECT * FROM v_customer_star_rating_statistics;

-- 查看具体客户星级情况（前20个）
SELECT
    customer_id,
    company_name,
    system_star_rating,
    total_order_count,
    total_order_amount,
    CASE system_star_rating
        WHEN 5 THEN '超级客户'
        WHEN 4 THEN '重要客户'
        WHEN 3 THEN '优质客户'
        WHEN 2 THEN '普通客户'
        WHEN 1 THEN '新客户/低价值客户'
        ELSE '未知'
    END as customer_type
FROM crm_customer
WHERE del_flag = '0'
ORDER BY system_star_rating DESC, total_order_amount DESC
LIMIT 20;

-- =============================================
-- 说明
-- =============================================
/*
星级计算规则优化说明（基于实际Java代码calculateStarRating函数）：

1. 评分维度（总分100分）：
   - 订单总数（25分）：体现客户忠诚度和活跃度
     ≥50单:25分, 20-49单:20分, 10-19单:16分, 5-9单:12分, 2-4单:8分, 1单:4分
   - 订单总金额（20分）：体现客户价值贡献
     ≥10万:20分, 5-10万:17分, 2-5万:14分, 1-2万:11分, 5千-1万:8分, 1千-5千:5分, >0:2分
   - 平均订单金额（20分）：体现客户消费能力
     ≥5千:20分, 2-5千:16分, 1-2千:12分, 500-1千:8分, 200-500:4分, >0:2分
   - 下单频率（35分）：体现客户活跃度
     每周/每月:35分, 每季度:20分, 不规律:5分

2. 星级划分：
   - 5星（90-100分）：超级客户，最高价值客户
   - 4星（75-89分）：重要客户，高价值客户
   - 3星（60-74分）：优质客户，中等价值客户
   - 2星（40-59分）：普通客户，一般价值客户
   - 1星（0-39分）：新客户/低价值客户

3. 使用方法：
   - 单个客户更新：CALL UpdateCustomerStarRating(客户ID);
   - 批量更新所有客户：CALL BatchUpdateCustomerStarRating();
   - 查看统计：SELECT * FROM v_customer_star_rating_statistics;

4. 性能优化：
   - 使用存储过程减少网络传输
   - 批量处理提高效率
   - 异常处理确保数据一致性
   - 进度显示便于监控

5. 注意事项：
   - 批量更新会锁定表，建议在业务低峰期执行
   - 建议先在测试环境验证
   - 执行前请备份相关数据
*/
