# CRM Migration目录结构调整说明

## 调整日期
2025-07-17

## 调整内容

### 1. 目录结构标准化

#### 调整前
```
server/script/sql/crm/migration/
├── README.md
├── 2025-07-15/
│   └── (多个SQL文件)
└── v1.0.1/
    └── README.md
```

#### 调整后
```
server/script/sql/crm/migration/
├── README.md
├── 2025-07-15/
│   └── (多个SQL文件)
├── 2025-07-16/
│   └── README.md
└── 2025-07-17/
    ├── README.md
    └── 01.add_customer_status_description.sql
```

### 2. 具体变更

#### 2.1 版本号目录改为日期目录
- **原目录**: `v1.0.1/`
- **新目录**: `2025-07-16/`
- **原因**: 统一使用日期格式，便于按时间顺序管理迁移文件

#### 2.2 新增2025-07-17迁移
- **目录**: `2025-07-17/`
- **文件**: `01.add_customer_status_description.sql`
- **功能**: 添加客户状态描述字段和新的客户状态

#### 2.3 移动SQL文件位置
- **原位置**: `server/script/sql/crm/2025-07-17/`
- **新位置**: `server/script/sql/crm/migration/2025-07-17/`
- **原因**: 统一迁移文件管理，所有迁移文件都放在migration目录下

### 3. 命名规范统一

#### 3.1 目录命名
- **格式**: `YYYY-MM-DD`
- **示例**: `2025-07-15`, `2025-07-16`, `2025-07-17`

#### 3.2 SQL文件命名
- **格式**: `序号.功能描述.sql`
- **示例**: `01.add_customer_status_description.sql`

#### 3.3 文档文件
- 每个日期目录必须包含`README.md`
- 详细说明该日期的所有变更内容

### 4. 执行顺序

按日期顺序执行迁移文件：
1. `2025-07-15/` - 客户画像字段等基础功能
2. `2025-07-16/` - 客户画像字段迁移（原v1.0.1内容）
3. `2025-07-17/` - 客户状态描述字段和新状态

### 5. 影响范围

#### 5.1 文件移动
- ✅ `v1.0.1/README.md` → `2025-07-16/README.md`
- ✅ `server/script/sql/crm/2025-07-17/` → `server/script/sql/crm/migration/2025-07-17/`

#### 5.2 文档更新
- ✅ 更新主`README.md`文件
- ✅ 调整目录结构说明
- ✅ 更新执行顺序示例

#### 5.3 清理工作
- ✅ 删除空的`v1.0.1`目录
- ✅ 删除临时创建的`server/script/sql/crm/2025-07-17/`目录

### 6. 验证方法

#### 6.1 目录结构验证
```bash
# 检查migration目录结构
ls -la server/script/sql/crm/migration/

# 应该看到以下目录：
# 2025-07-15/
# 2025-07-16/
# 2025-07-17/
# README.md
```

#### 6.2 文件完整性验证
```bash
# 检查每个日期目录的文件
find server/script/sql/crm/migration/ -name "*.sql" -o -name "README.md" | sort
```

### 7. 后续规范

#### 7.1 新增迁移文件
- 在`server/script/sql/crm/migration/`下创建新的日期目录
- 格式：`YYYY-MM-DD`
- 必须包含`README.md`说明文件

#### 7.2 SQL文件命名
- 使用两位数序号：`01`, `02`, `03`...
- 功能描述要清晰明确
- 示例：`01.add_new_field.sql`, `02.update_dict_data.sql`

#### 7.3 文档要求
- 每个迁移都要有详细的README说明
- 包含变更内容、执行方法、回滚方案
- 更新主README文件的目录结构

### 8. 注意事项

1. **向后兼容**: 目录结构调整不影响现有迁移文件的功能
2. **执行顺序**: 严格按照日期顺序执行迁移
3. **文档维护**: 每次新增迁移都要更新相关文档
4. **版本管理**: 不再使用版本号目录，统一使用日期格式

## 总结

本次调整将CRM迁移文件的目录结构标准化为日期格式，提高了文件管理的一致性和可维护性。所有迁移文件现在都按照统一的命名规范组织，便于团队协作和版本管理。
