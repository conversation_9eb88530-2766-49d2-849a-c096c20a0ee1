# CRM常量和枚举更新说明

## 更新日期
2025-07-17

## 更新内容

### 1. SQL文件dict_code调整

#### 1.1 修改前
```sql
-- dict_code从2090开始
INSERT INTO sys_dict_data (...) VALUES (2090, 6, '已停业', 'closed', ...);
INSERT INTO sys_dict_data (...) VALUES (2091, 7, '半活跃', 'semi_active', ...);
```

#### 1.2 修改后
```sql
-- dict_code从2144开始
INSERT INTO sys_dict_data (...) VALUES (2144, 6, '已停业', 'closed', ...);
INSERT INTO sys_dict_data (...) VALUES (2145, 7, '半活跃', 'semi_active', ...);
```

### 2. CrmConstants类更新

#### 2.1 文件位置
`server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/constant/CrmConstants.java`

#### 2.2 新增常量
```java
/**
 * 客户状态
 */
// 原有状态...
public static final String CUSTOMER_STATUS_CLOSED = "closed";
public static final String CUSTOMER_STATUS_SEMI_ACTIVE = "semi_active";
```

#### 2.3 常量说明
- `CUSTOMER_STATUS_CLOSED` - 已停业状态，对应数据库值 "closed"
- `CUSTOMER_STATUS_SEMI_ACTIVE` - 半活跃状态，对应数据库值 "semi_active"

### 3. CustomerStatusEnum枚举更新

#### 3.1 文件位置
`server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/enums/CustomerStatusEnum.java`

#### 3.2 新增枚举值
```java
public enum CustomerStatusEnum {
    // 原有枚举值...
    CLOSED("closed", "Closed Business"),
    SEMI_ACTIVE("semi_active", "Semi Active Customer");
}
```

#### 3.3 枚举说明
- `CLOSED` - 已停业，英文描述 "Closed Business"
- `SEMI_ACTIVE` - 半活跃客户，英文描述 "Semi Active Customer"

### 4. 数据字典对应关系

| 字典编号 | 字典值 | 中文标签 | 英文描述 | 常量名 | 枚举值 | 样式 |
|---------|--------|----------|----------|--------|--------|------|
| 2144 | closed | 已停业 | Closed Business | CUSTOMER_STATUS_CLOSED | CLOSED | danger |
| 2145 | semi_active | 半活跃 | Semi Active Customer | CUSTOMER_STATUS_SEMI_ACTIVE | SEMI_ACTIVE | warning |

### 5. 使用示例

#### 5.1 在业务代码中使用常量
```java
// 使用常量
if (CrmConstants.CUSTOMER_STATUS_CLOSED.equals(customer.getCustomerStatus())) {
    // 处理已停业客户逻辑
}

if (CrmConstants.CUSTOMER_STATUS_SEMI_ACTIVE.equals(customer.getCustomerStatus())) {
    // 处理半活跃客户逻辑
}
```

#### 5.2 在业务代码中使用枚举
```java
// 使用枚举
CustomerStatusEnum status = CustomerStatusEnum.getByCode(customer.getCustomerStatus());
if (status == CustomerStatusEnum.CLOSED) {
    // 处理已停业客户逻辑
}

if (status == CustomerStatusEnum.SEMI_ACTIVE) {
    // 处理半活跃客户逻辑
}
```

### 6. 前端对应更新

#### 6.1 字典枚举常量
前端的`CrmDictEnum.CRM_CUSTOMER_STATUS`会自动包含新的状态选项，无需额外修改。

#### 6.2 状态显示
新状态会在客户列表和表单中自动显示：
- 已停业 - 红色危险样式
- 半活跃 - 橙色警告样式

### 7. 影响范围

#### 7.1 数据库
- sys_dict_data表新增2条记录（dict_code: 2144, 2145）

#### 7.2 后端代码
- CrmConstants类新增2个常量
- CustomerStatusEnum枚举新增2个值
- 不影响现有业务逻辑

#### 7.3 前端代码
- 自动获取新的字典数据
- 客户状态下拉选项自动更新
- 状态显示样式自动应用

### 8. 测试要点

#### 8.1 常量验证
- [ ] CrmConstants中新常量值正确
- [ ] CustomerStatusEnum中新枚举值正确
- [ ] 枚举的getByCode方法能正确返回新状态

#### 8.2 数据库验证
- [ ] 字典数据插入成功
- [ ] dict_code使用2144、2145
- [ ] 样式类正确设置

#### 8.3 功能验证
- [ ] 前端下拉选项包含新状态
- [ ] 新状态保存和显示正常
- [ ] 状态样式正确显示

### 9. 注意事项

1. **常量命名**: 遵循现有命名规范，使用CUSTOMER_STATUS_前缀
2. **枚举描述**: 使用英文描述，保持与现有枚举一致
3. **字典编号**: 使用2144、2145，避免与现有数据冲突
4. **向后兼容**: 新增状态不影响现有客户数据和业务逻辑

## 总结

本次更新在CRM模块中新增了两个客户状态（已停业、半活跃），涉及数据库字典数据、后端常量类、枚举类的同步更新。所有修改都保持向后兼容，不影响现有功能。
