-- 添加客户状态描述字段和新的客户状态
-- 作者: jf
-- 日期: 2025-07-17

-- 1. 为crm_customer表添加状态描述字段
ALTER TABLE crm_customer ADD COLUMN status_description VARCHAR(500) COMMENT '状态描述';

-- 2. 更新客户状态字典数据，添加新状态
-- dict_code从2144开始

-- 添加"已停业"状态
INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, create_dept, create_by, create_time, update_by, update_time, remark)
VALUES (2144, 6, '已停业', 'closed', 'crm_customer_status', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), '客户已停业');

-- 添加"半活跃"状态
INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, create_dept, create_by, create_time, update_by, update_time, remark)
VALUES (2145, 7, '半活跃', 'semi_active', 'crm_customer_status', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '客户半活跃状态');
