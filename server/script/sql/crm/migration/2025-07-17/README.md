# CRM客户管理功能优化

## 修改日期
2025-07-17

## 修改内容

### 1. 数据库结构调整

#### 1.1 添加状态描述字段
- **表名**: `crm_customer`
- **字段**: `status_description`
- **类型**: `VARCHAR(500)`
- **说明**: 用于描述客户状态的详细信息

#### 1.2 新增客户状态字典数据
- **已停业** (closed) - 样式: danger
- **半活跃** (semi_active) - 样式: warning

### 2. 后端代码调整

#### 2.1 实体类更新
- `CrmCustomer.java` - 添加 `statusDescription` 字段
- `CrmCustomerBo.java` - 添加 `statusDescription` 字段
- `CrmCustomerVo.java` - 添加 `statusDescription` 字段，支持Excel导出

#### 2.2 字段说明
```java
/**
 * 状态描述
 */
private String statusDescription;
```

### 3. 前端代码调整

#### 3.1 表单结构优化
- **移除业务特征分组**: 将所有业务特征字段移至客户画像分组
- **客户编号管理**: 新增时隐藏客户编号字段，由后端生成
- **状态描述字段**: 在客户状态字段后添加状态描述多行文本框

#### 3.2 字段分组调整
**客户画像分组现包含**:
- 客户画像描述（必填）
- 感兴趣的产品（必填）
- 已销售的竞品品牌
- 单次采购金额
- 采购频率
- 是否好沟通
- 联系方式偏好
- 决策权大小
- 价格敏感程度
- 联系频次
- 下单频率

#### 3.3 客户状态扩展
新增客户状态选项：
- 已停业 (closed)
- 半活跃 (semi_active)

### 4. 业务逻辑调整

#### 4.1 客户编号管理
- **新增模式**: 隐藏客户编号字段，由后端自动生成
- **编辑模式**: 显示客户编号字段，支持修改和唯一性验证

#### 4.2 表单验证
- 感兴趣的产品改为必填字段
- 客户编号在编辑时验证唯一性
- 状态描述为可选字段

### 5. 数据迁移说明

#### 5.1 执行顺序
1. 执行 `01.add_customer_status_description.sql`
2. 重启应用服务
3. 验证新功能

#### 5.2 回滚方案
如需回滚，执行以下SQL：
```sql
-- 删除新增的字典数据
DELETE FROM sys_dict_data WHERE dict_code IN (2144, 2145);

-- 删除状态描述字段
ALTER TABLE crm_customer DROP COLUMN status_description;
```

### 6. 测试要点

#### 6.1 功能测试
- [ ] 新增客户时客户编号自动生成
- [ ] 编辑客户时客户编号可修改且验证唯一性
- [ ] 状态描述字段正常保存和显示
- [ ] 新增的客户状态选项正常使用
- [ ] 感兴趣的产品必填验证生效

#### 6.2 界面测试
- [ ] 表单字段分组正确显示
- [ ] 业务特征字段已移至客户画像
- [ ] 状态描述支持多行输入
- [ ] 客户状态下拉包含新选项

### 7. 影响范围

#### 7.1 数据库
- crm_customer表结构变更
- sys_dict_data字典数据新增

#### 7.2 后端代码
- CRM客户相关实体类、BO、VO
- 无需修改业务逻辑代码

#### 7.3 前端代码
- 客户新增/编辑表单
- 客户查看抽屉
- 客户列表显示

### 8. 注意事项

1. **数据兼容性**: 现有客户数据的status_description字段为NULL，不影响业务
2. **字典编号**: 新增字典数据使用编号2144、2145，请确保不与现有数据冲突
3. **表单验证**: 客户编号唯一性验证仅在编辑模式下生效
4. **业务连续性**: 修改不影响现有客户数据和业务流程
