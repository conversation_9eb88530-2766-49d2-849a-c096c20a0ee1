-- ----------------------------
-- CRM跟进记录管理菜单配置SQL
-- Author: jf
-- Date: 2025-08-02
-- Description: 为前端侧边栏添加CRM跟进记录管理菜单项
-- ----------------------------

-- =============================================
-- 插入CRM跟进记录管理菜单
-- =============================================

-- 1. 插入跟进记录列表菜单（在客户管理下）
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1859425395123200020, '跟进记录', 8100, 2, 'follow-record', 'crm/follow-record/index', '',
    1, 0, 'C', '1', '1', 'crm:follow:list', 'lucide:clipboard-list',
    103, 1, NOW(), 1, NOW(), 'CRM跟进记录列表页面'
);

-- 2. 插入跟进记录查询权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1859425395123200021, '跟进记录查询', 1859425395123200020, 1, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:follow:query', '#',
    103, 1, NOW(), 1, NOW(), '跟进记录查询权限'
);

-- 3. 插入跟进记录新增权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1859425395123200022, '跟进记录新增', 1859425395123200020, 2, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:follow:add', '#',
    103, 1, NOW(), 1, NOW(), '跟进记录新增权限'
);

-- 4. 插入跟进记录修改权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1859425395123200023, '跟进记录修改', 1859425395123200020, 3, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:follow:edit', '#',
    103, 1, NOW(), 1, NOW(), '跟进记录修改权限'
);

-- 5. 插入跟进记录删除权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1859425395123200024, '跟进记录删除', 1859425395123200020, 4, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:follow:remove', '#',
    103, 1, NOW(), 1, NOW(), '跟进记录删除权限'
);

-- 6. 插入跟进记录导出权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1859425395123200025, '跟进记录导出', 1859425395123200020, 5, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:follow:export', '#',
    103, 1, NOW(), 1, NOW(), '跟进记录导出权限'
);

-- =============================================
-- 为超级管理员角色分配跟进记录菜单权限
-- =============================================

-- 为角色ID=1（超级管理员）分配所有跟进记录菜单权限
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
(1, 1859425395123200020),  -- 跟进记录
(1, 1859425395123200021),  -- 跟进记录查询
(1, 1859425395123200022),  -- 跟进记录新增
(1, 1859425395123200023),  -- 跟进记录修改
(1, 1859425395123200024),  -- 跟进记录删除
(1, 1859425395123200025);  -- 跟进记录导出

-- =============================================
-- 验证插入的菜单数据
-- =============================================

-- 查看跟进记录菜单树结构
SELECT
    m1.menu_id,
    m1.menu_name,
    m1.parent_id,
    m2.menu_name as parent_name,
    m1.order_num,
    m1.path,
    m1.component,
    m1.menu_type,
    m1.perms,
    m1.icon
FROM sys_menu m1
LEFT JOIN sys_menu m2 ON m1.parent_id = m2.menu_id
WHERE m1.menu_id >= 1859425395123200020 AND m1.menu_id <= 1859425395123200025
ORDER BY m1.parent_id, m1.order_num;

-- 查看角色菜单分配情况
SELECT
    rm.role_id,
    r.role_name,
    m.menu_id,
    m.menu_name,
    m.perms
FROM sys_role_menu rm
JOIN sys_role r ON rm.role_id = r.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE rm.menu_id >= 1859425395123200020 AND rm.menu_id <= 1859425395123200025
ORDER BY rm.role_id, m.menu_id;

-- =============================================
-- 清理脚本（如需删除菜单时使用）
-- =============================================

/*
-- 删除角色菜单关联
DELETE FROM sys_role_menu WHERE menu_id >= 1859425395123200020 AND menu_id <= 1859425395123200025;

-- 删除菜单项
DELETE FROM sys_menu WHERE menu_id >= 1859425395123200020 AND menu_id <= 1859425395123200025;
*/
