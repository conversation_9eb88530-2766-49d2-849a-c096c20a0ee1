# CRM 跟进记录管理模块迁移脚本

**迁移日期**: 2025-08-02  
**模块**: CRM 跟进记录管理  
**版本**: v1.0.0  

## 📋 迁移概述

本次迁移为 CRM 系统添加跟进记录管理功能，包括菜单配置和相关权限设置。

## 📁 文件说明

### 01.follow_record_menu.sql
- **功能**: 跟进记录管理菜单配置
- **内容**:
  - 跟进记录页面菜单（在客户管理下）
  - 相关操作权限按钮
  - 超级管理员权限分配

### 菜单结构
```
CRM管理 (8000)
└── 客户管理 (8100)
    ├── 客户列表 (8101)
    └── 跟进记录 (1859425395123200020)
        ├── 跟进记录查询 (1859425395123200021)
        ├── 跟进记录新增 (1859425395123200022)
        ├── 跟进记录修改 (1859425395123200023)
        ├── 跟进记录删除 (1859425395123200024)
        └── 跟进记录导出 (1859425395123200025)
```

## 🔑 权限配置

### 菜单权限
| 菜单ID | 菜单名称 | 权限标识 | 说明 |
|--------|----------|----------|------|
| 1859425395123200020 | 跟进记录 | crm:follow:list | 页面菜单 |
| 1859425395123200021 | 跟进记录查询 | crm:follow:query | 功能按钮 |
| 1859425395123200022 | 跟进记录新增 | crm:follow:add | 功能按钮 |
| 1859425395123200023 | 跟进记录修改 | crm:follow:edit | 功能按钮 |
| 1859425395123200024 | 跟进记录删除 | crm:follow:remove | 功能按钮 |
| 1859425395123200025 | 跟进记录导出 | crm:follow:export | 功能按钮 |

### 角色分配
- **超级管理员 (role_id=1)**: 自动分配所有跟进记录管理权限

## 🎯 前端对应关系

### 路由配置
- **路径**: `/crm/customer/follow-record`
- **组件**: `crm/follow-record/index`
- **名称**: 跟进记录

### 组件文件
- `web/apps/lookah-admin/src/views/crm/follow-record/index.vue`
- `web/apps/lookah-admin/src/views/crm/follow-record/data.tsx`
- `web/apps/lookah-admin/src/views/crm/follow-record/components/`

## 🚀 执行步骤

### 1. 执行迁移脚本
```bash
# 进入项目根目录
cd /path/to/lookah

# 执行菜单配置脚本
mysql -u username -p database_name < server/script/sql/crm/migration/2025-08-02/01.follow_record_menu.sql
```

### 2. 验证迁移结果
```sql
-- 查看菜单是否正确插入
SELECT 
    m1.menu_id,
    m1.menu_name,
    m1.parent_id,
    m2.menu_name as parent_name,
    m1.path,
    m1.component,
    m1.perms
FROM sys_menu m1
LEFT JOIN sys_menu m2 ON m1.parent_id = m2.menu_id
WHERE m1.menu_id >= 1859425395123200020 AND m1.menu_id <= 1859425395123200025
ORDER BY m1.parent_id, m1.order_num;

-- 查看权限分配是否正确
SELECT 
    rm.role_id,
    r.role_name,
    m.menu_id,
    m.menu_name,
    m.perms
FROM sys_role_menu rm
JOIN sys_role r ON rm.role_id = r.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE rm.menu_id >= 1859425395123200020 AND rm.menu_id <= 1859425395123200025
ORDER BY rm.role_id, m.menu_id;
```

### 3. 前端验证
1. 重启前端应用
2. 使用超级管理员账号登录
3. 检查左侧菜单是否出现"跟进记录管理"
4. 访问跟进记录列表页面
5. 验证各项功能权限

## ⚠️ 注意事项

### 依赖检查
- 确保 CRM 管理父菜单 (menu_id=8000) 已存在
- 确保超级管理员角色 (role_id=1) 已存在
- 确保相关字典数据已配置 (crm_follow_type, crm_follow_theme)

### 回滚方案
如需回滚此次迁移，可执行以下 SQL：
```sql
-- 删除角色菜单关联
DELETE FROM sys_role_menu WHERE menu_id >= 1859425395123200020 AND menu_id <= 1859425395123200025;

-- 删除菜单项
DELETE FROM sys_menu WHERE menu_id >= 1859425395123200020 AND menu_id <= 1859425395123200025;
```

### 环境要求
- MySQL 5.7+
- 确保数据库连接正常
- 建议在测试环境先执行验证

## 📊 影响评估

### 数据库影响
- **新增表**: 无
- **新增菜单**: 6 个
- **新增权限**: 5 个
- **影响角色**: 超级管理员

### 系统影响
- **前端**: 新增跟进记录管理页面
- **后端**: 无影响（接口已存在）
- **权限**: 新增跟进记录相关权限

## ✅ 验收标准

1. **菜单显示**: 左侧导航栏正确显示跟进记录管理菜单
2. **页面访问**: 能够正常访问跟进记录列表页面
3. **权限控制**: 各操作按钮根据权限正确显示/隐藏
4. **功能完整**: 查询、新增、编辑、删除、导出功能正常
5. **数据完整**: 数据库中菜单和权限数据完整无误

## 📞 联系方式

如有问题，请联系：
- **开发者**: AI Assistant
- **创建日期**: 2025-08-02
- **文档版本**: v1.0.0
