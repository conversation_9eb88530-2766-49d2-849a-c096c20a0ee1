-- ----------------------------
-- Migration: 添加公海客户管理菜单
-- Author: AI Assistant
-- Date: 2025-07-19
-- Description: 为CRM模块添加公海客户管理相关菜单和权限
-- ----------------------------

-- =============================================
-- 插入公海客户管理菜单
-- =============================================

-- 1. 插入公海客户列表菜单（页面）- 作为客户管理的子菜单
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1859425395123200001, '公海客户', 8100, 2, 'public-customer', 'crm/public-customer/index', '',
    1, 1, 'C', '1', '1', 'crm:customer:public', 'lucide:users-round',
    103, 1, NOW(), 1, NOW(), '公海客户管理页面'
);

-- =============================================
-- 插入公海客户管理相关的功能按钮权限
-- =============================================

-- 公海客户查询权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, 
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, 
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1859425395123200002, '公海客户查询', 1859425395123200001, 1, '#', '', '', 
    1, 0, 'F', '1', '1', 'crm:customer:public:query', '#', 
    103, 1, NOW(), 1, NOW(), '公海客户查询权限'
);

-- 公海客户分配权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, 
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, 
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1859425395123200003, '公海客户分配', 1859425395123200001, 2, '#', '', '', 
    1, 0, 'F', '1', '1', 'crm:customer:public:assign', '#', 
    103, 1, NOW(), 1, NOW(), '公海客户分配权限'
);

-- 转换公海客户权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, 
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, 
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1859425395123200004, '转换公海客户', 1859425395123200001, 3, '#', '', '', 
    1, 0, 'F', '1', '1', 'crm:customer:public:convert', '#', 
    103, 1, NOW(), 1, NOW(), '转换公海客户权限'
);

-- 自动转换公海客户权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, 
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, 
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1859425395123200005, '自动转换公海客户', 1859425395123200001, 4, '#', '', '', 
    1, 0, 'F', '1', '1', 'crm:customer:public:auto-convert', '#', 
    103, 1, NOW(), 1, NOW(), '自动转换公海客户权限'
);

-- =============================================
-- 为超级管理员角色分配公海客户菜单权限
-- =============================================

-- 为角色ID=1（超级管理员）分配所有公海客户菜单权限
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES 
(1, 1859425395123200001),  -- 公海客户
(1, 1859425395123200002),  -- 公海客户查询
(1, 1859425395123200003),  -- 公海客户分配
(1, 1859425395123200004),  -- 转换公海客户
(1, 1859425395123200005);  -- 自动转换公海客户

-- =============================================
-- 验证插入的菜单数据
-- =============================================

-- 查看公海客户菜单树结构
SELECT 
    m1.menu_id,
    m1.menu_name,
    m1.parent_id,
    m2.menu_name as parent_name,
    m1.order_num,
    m1.path,
    m1.component,
    m1.menu_type,
    m1.perms,
    m1.icon
FROM sys_menu m1
LEFT JOIN sys_menu m2 ON m1.parent_id = m2.menu_id
WHERE m1.menu_id >= 1859425395123200001 AND m1.menu_id <= 1859425395123200005
ORDER BY m1.parent_id, m1.order_num;

-- 查看角色菜单分配情况
SELECT 
    rm.role_id,
    r.role_name,
    m.menu_id,
    m.menu_name,
    m.perms
FROM sys_role_menu rm
JOIN sys_role r ON rm.role_id = r.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE rm.menu_id >= 1859425395123200001 AND rm.menu_id <= 1859425395123200005
ORDER BY rm.role_id, m.menu_id;
