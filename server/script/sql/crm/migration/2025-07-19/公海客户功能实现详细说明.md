# CRM公海客户管理功能实现详细说明

## 概述

根据需求实现了CRM公海客户管理功能，包括：
1. 自动将超过15天未跟进的客户转为公海客户
2. 公海客户菜单和权限配置
3. 公海客户列表查询和分配功能

## 后端实现

### 1. Service接口扩展

在 `ICrmCustomerService` 中新增方法：

- `autoConvertToPublicCustomers(int days)` - 自动转换公海客户
- `convertToPublicCustomer(Long customerId)` - 手动转换公海客户
- `assignPublicCustomerToSales(Long customerId, Long salesUserId, String salesUserName)` - 分配公海客户

### 2. Service实现

在 `CrmCustomerServiceImpl` 中实现：

- **自动转换逻辑**：查询超过指定天数未跟进的私有客户，批量转换为公海客户
- **转换验证**：检查客户状态，防止重复转换
- **分配逻辑**：将公海客户分配给指定业务员，同时转为私有客户

### 3. Controller接口

在 `CrmCustomerController` 中新增接口：

- `GET /crm/customer/public-list` - 查询公海客户列表
- `POST /crm/customer/auto-convert-public` - 手动执行自动转换
- `PUT /crm/customer/{customerId}/convert-to-public` - 转换为公海客户
- `PUT /crm/customer/{customerId}/assign-to-sales` - 分配公海客户

### 4. 权限配置

- `crm:customer:public` - 公海客户基础权限
- `crm:customer:public:query` - 公海客户查询权限
- `crm:customer:public:assign` - 公海客户分配权限
- `crm:customer:public:convert` - 转换公海客户权限
- `crm:customer:public:auto-convert` - 自动转换权限

## 数据库变更

### 菜单配置SQL

文件：`server/script/sql/crm/migration/2025-07-19/01.add_public_customer_menu.sql`

使用雪花ID生成的菜单ID：
- 1859425395123200001 - 公海客户主菜单
- 1859425395123200002 - 公海客户查询权限
- 1859425395123200003 - 公海客户分配权限
- 1859425395123200004 - 转换公海客户权限
- 1859425395123200005 - 自动转换权限

## 前端实现

### 1. 公海客户页面

文件：`web/apps/lookah-admin/src/views/crm/public-customer/index.vue`

功能特性：
- 复用客户列表的表格配置和搜索表单
- 移除"是否公海客户"列（因为都是公海客户）
- 添加"分配"操作按钮
- 支持自动转换公海客户功能
- 支持导出功能

### 2. 分配业务员模态框

文件：`web/apps/lookah-admin/src/views/crm/public-customer/components/assign-sales-modal.vue`

功能特性：
- 显示客户基本信息
- 业务员选择下拉框
- 表单验证
- 分配确认操作

### 3. API接口扩展

文件：`web/apps/lookah-admin/src/api/crm/customer/index.ts`

新增接口：
- `publicCustomerList()` - 公海客户列表
- `autoConvertToPublicCustomers()` - 自动转换
- `convertToPublicCustomer()` - 手动转换
- `assignPublicCustomerToSales()` - 分配客户

## 核心逻辑

### 1. 自动转换逻辑

```sql
-- 查询条件
WHERE is_public = '0'  -- 私有客户
AND (
  last_follow_time IS NULL  -- 从未跟进
  OR last_follow_time < DATE_SUB(NOW(), INTERVAL 15 DAY)  -- 超过15天未跟进
)
AND del_flag = '0'  -- 未删除
AND deleted_at IS NULL  -- 删除时间戳为空
```

### 2. 转换操作

```java
// 转换为公海客户
customer.setIsPublic("1");  // 设为公海客户
customer.setSalesUserId(null);  // 清空业务员ID
customer.setSalesUserName(null);  // 清空业务员姓名
```

### 3. 分配操作

```java
// 分配给业务员
customer.setIsPublic("0");  // 设为私有客户
customer.setSalesUserId(salesUserId);  // 设置业务员ID
customer.setSalesUserName(salesUserName);  // 设置业务员姓名
```

## 使用流程

### 1. 查看公海客户

1. 访问"CRM管理 > 客户管理 > 公海客户"菜单
2. 显示公海客户列表（不再自动转换）
3. 支持多条件搜索和分页

### 2. 分配公海客户

1. 在公海客户列表中点击"分配"按钮
2. 在弹出的模态框中选择业务员
3. 点击"确认分配"完成分配
4. 客户自动转为私有客户

### 3. 手动转换

1. 点击"自动转换公海客户"按钮
2. 确认转换操作
3. 系统执行转换逻辑
4. 显示转换结果

## 权限控制

- 需要 `crm:customer:public` 权限才能访问公海客户功能
- 分配操作需要额外的分配权限
- 转换操作需要相应的转换权限

## 注意事项

1. **手动转换时机**：移除了查询时自动转换，改为手动触发
2. **数据安全**：转换前会验证客户状态，防止误操作
3. **日志记录**：所有转换和分配操作都有详细的日志记录
4. **权限验证**：所有操作都有相应的权限验证
5. **事务处理**：批量转换使用事务确保数据一致性

## 测试要点

1. **自动转换测试**：创建超过15天未跟进的客户，验证手动转换
2. **分配功能测试**：验证公海客户分配给业务员的完整流程
3. **权限测试**：验证不同角色的权限控制
4. **界面测试**：验证前端页面的交互和显示
5. **数据一致性测试**：验证转换和分配操作的数据一致性

## 问题修复

### 列表变长Bug修复

**问题原因**：原来每次查询公海客户列表时都会自动触发转换逻辑，导致列表不断增加新的公海客户。

**解决方案**：
- 移除了查询接口中的自动转换逻辑
- 将自动转换改为手动触发
- 添加确认提示防止误操作

**修改文件**：
```java
// CrmCustomerController.java
@GetMapping("/public-list")
public TableDataInfo<CrmCustomerVo> publicList(CrmCustomerBo bo, PageQuery pageQuery) {
    // 移除自动转换逻辑，避免每次查询都触发转换导致列表变长
    return crmCustomerService.queryPublicCustomerPageList(bo, pageQuery);
}
```

## 独立模块设计

### 文件夹结构

创建了独立的公海客户管理模块：

```
web/apps/lookah-admin/src/views/crm/public-customer/
├── index.vue                           # 公海客户主页面
├── data.tsx                           # 数据配置（表格列、搜索表单）
├── public-customer-drawer.vue         # 公海客户详情抽屉
└── components/
    └── assign-sales-modal.vue         # 分配业务员模态框
```

### 核心特性

1. **独立模块设计**：完全与客户管理模块分离，便于维护
2. **专业化界面**：针对公海客户的特殊需求定制
3. **可视化状态**：直观显示跟进状态和成为公海客户的原因
4. **操作便捷**：简化的分配流程，提高工作效率
5. **数据安全**：手动触发转换，避免意外数据变更
