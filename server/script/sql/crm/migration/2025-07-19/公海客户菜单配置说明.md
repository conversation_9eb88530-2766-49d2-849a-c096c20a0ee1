# 公海客户菜单配置说明

## 概述

根据需求，将公海客户作为客户管理的子菜单，而不是独立的一级菜单。这样可以保持菜单结构的层次性和逻辑性。

## 菜单结构

```
CRM管理 (8000)
└── 客户管理 (8100)
    ├── 客户列表 (8101)
    └── 公海客户 (1859425395123200001)  ← 新增
        ├── 公海客户查询 (1859425395123200002)
        ├── 公海客户分配 (1859425395123200003)
        ├── 转换公海客户 (1859425395123200004)
        └── 自动转换公海客户 (1859425395123200005)
```

## SQL脚本详情

### 文件位置
`server/script/sql/crm/migration/2025-07-19/01.add_public_customer_menu.sql`

### 主要配置

#### 1. 公海客户主菜单
```sql
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, 
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, 
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    1859425395123200001, '公海客户', 8100, 2, 'public-customer', 'crm/public-customer/index', '', 
    1, 1, 'C', '1', '1', 'crm:customer:public', 'lucide:users-round', 
    103, 1, NOW(), 1, NOW(), '公海客户管理页面'
);
```

**关键配置说明**：
- `menu_id`: 1859425395123200001 (雪花ID)
- `parent_id`: 8100 (客户管理菜单ID)
- `order_num`: 2 (排在客户列表之后)
- `path`: 'public-customer' (路由路径)
- `component`: 'crm/public-customer/index' (组件路径)
- `menu_type`: 'C' (菜单类型：页面)
- `perms`: 'crm:customer:public' (权限标识)
- `icon`: 'lucide:users-round' (图标)

#### 2. 功能权限按钮

**公海客户查询权限**：
```sql
INSERT INTO `sys_menu` VALUES (
    1859425395123200002, '公海客户查询', 1859425395123200001, 1, '#', '', '', 
    1, 0, 'F', '1', '1', 'crm:customer:public:query', '#', 
    103, 1, NOW(), 1, NOW(), '公海客户查询权限'
);
```

**公海客户分配权限**：
```sql
INSERT INTO `sys_menu` VALUES (
    1859425395123200003, '公海客户分配', 1859425395123200001, 2, '#', '', '', 
    1, 0, 'F', '1', '1', 'crm:customer:public:assign', '#', 
    103, 1, NOW(), 1, NOW(), '公海客户分配权限'
);
```

**转换公海客户权限**：
```sql
INSERT INTO `sys_menu` VALUES (
    1859425395123200004, '转换公海客户', 1859425395123200001, 3, '#', '', '', 
    1, 0, 'F', '1', '1', 'crm:customer:public:convert', '#', 
    103, 1, NOW(), 1, NOW(), '转换公海客户权限'
);
```

**自动转换公海客户权限**：
```sql
INSERT INTO `sys_menu` VALUES (
    1859425395123200005, '自动转换公海客户', 1859425395123200001, 4, '#', '', '', 
    1, 0, 'F', '1', '1', 'crm:customer:public:auto-convert', '#', 
    103, 1, NOW(), 1, NOW(), '自动转换公海客户权限'
);
```

#### 3. 角色权限分配

为超级管理员角色(ID=1)分配所有公海客户相关权限：
```sql
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES 
(1, 1859425395123200001),  -- 公海客户
(1, 1859425395123200002),  -- 公海客户查询
(1, 1859425395123200003),  -- 公海客户分配
(1, 1859425395123200004),  -- 转换公海客户
(1, 1859425395123200005);  -- 自动转换公海客户
```

## 前端路由配置

### 路由结构
```typescript
// web/apps/lookah-admin/src/router/routes/modules/crm.ts
{
  name: 'CrmCustomer',
  path: '/customer',
  children: [
    {
      name: 'CrmCustomerList',
      path: '/list',
      component: () => import('#/views/crm/customer/index.vue'),
    },
    {
      name: 'CrmPublicCustomerList',
      path: '/public-customer',
      component: () => import('#/views/crm/public-customer/index.vue'),
    },
  ],
}
```

### 访问路径
- 客户列表：`/crm/customer/list`
- 公海客户：`/crm/customer/public-customer`

## 权限体系

### 权限标识说明

| 权限标识 | 说明 | 用途 |
|---------|------|------|
| `crm:customer:public` | 公海客户基础权限 | 访问公海客户页面 |
| `crm:customer:public:query` | 公海客户查询权限 | 查询公海客户列表 |
| `crm:customer:public:assign` | 公海客户分配权限 | 分配公海客户给业务员 |
| `crm:customer:public:convert` | 转换公海客户权限 | 手动转换客户为公海客户 |
| `crm:customer:public:auto-convert` | 自动转换权限 | 执行自动转换任务 |

### 权限使用示例

**后端Controller中的权限验证**：
```java
@SaCheckPermission("crm:customer:public")
@GetMapping("/public-list")
public TableDataInfo<CrmCustomerVo> publicList(CrmCustomerBo bo, PageQuery pageQuery) {
    return crmCustomerService.queryPublicCustomerPageList(bo, pageQuery);
}

@SaCheckPermission("crm:customer:public:assign")
@PutMapping("/{customerId}/assign-to-sales")
public R<Void> assignToSales(@PathVariable Long customerId, @RequestParam Long salesUserId, @RequestParam String salesUserName) {
    return toAjax(crmCustomerService.assignPublicCustomerToSales(customerId, salesUserId, salesUserName));
}
```

## 部署说明

### 1. 执行SQL脚本
```bash
# 在数据库中执行菜单配置脚本
mysql -u username -p database_name < server/script/sql/crm/migration/2025-07-19/01.add_public_customer_menu.sql
```

### 2. 重启应用
重启后端应用以加载新的权限配置。

### 3. 验证菜单
1. 登录系统
2. 检查左侧菜单是否显示"CRM管理 > 客户管理 > 公海客户"
3. 验证页面访问和功能权限

## 注意事项

1. **雪花ID唯一性**：确保使用的雪花ID在系统中唯一
2. **父级菜单依赖**：确保客户管理菜单(8100)已存在
3. **权限继承**：子权限依赖于父级权限
4. **组件路径**：确保前端组件路径正确
5. **图标资源**：确保使用的图标在系统中可用

## 验证清单

- [ ] SQL脚本执行成功
- [ ] 菜单在后台管理中正确显示
- [ ] 前端路由可以正常访问
- [ ] 权限验证正常工作
- [ ] 页面功能完整可用
- [ ] 角色权限分配正确

## 后续维护

1. **权限细化**：根据实际需求可以进一步细化权限粒度
2. **菜单排序**：可以调整`order_num`来改变菜单显示顺序
3. **图标更新**：可以根据UI设计更换菜单图标
4. **多语言支持**：添加菜单名称的多语言配置
