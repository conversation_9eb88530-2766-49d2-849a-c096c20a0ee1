-- ----------------------------
-- Rollback Migration: 恢复CRM表中的租户相关字段
-- Author: jf
-- Date: 2025-07-15
-- Description: 回滚05.remove_tenant_fields.sql的操作，恢复crm_customer表中的tenant_id字段
-- ----------------------------

-- 开始事务
START TRANSACTION;

-- 添加租户编号字段回crm_customer表
-- 注意：这里使用合理的默认值和约束
ALTER TABLE `crm_customer` 
ADD COLUMN `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号' AFTER `id`;

-- 创建索引以提高查询性能
CREATE INDEX `idx_crm_customer_tenant_id` ON `crm_customer` (`tenant_id`);

-- 提交事务
COMMIT;

-- 验证回滚是否成功
-- SELECT COUNT(*) as tenant_field_exists 
-- FROM information_schema.COLUMNS 
-- WHERE TABLE_SCHEMA = DATABASE() 
--   AND TABLE_NAME = 'crm_customer' 
--   AND COLUMN_NAME = 'tenant_id';
