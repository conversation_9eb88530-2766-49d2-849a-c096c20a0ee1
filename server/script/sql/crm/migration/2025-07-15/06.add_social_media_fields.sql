-- ----------------------------
-- Migration: 为CRM客户表添加社媒相关字段
-- Author: jf
-- Date: 2025-07-15
-- Description: 在crm_customer表中添加社媒账号和社媒类型字段
-- ----------------------------

-- 添加社媒账号字段
ALTER TABLE `crm_customer` ADD COLUMN `social_media_account` varchar(200) NOT NULL DEFAULT '' COMMENT '社媒账号' AFTER `deal_influence_other`;

-- 添加社媒类型字段
ALTER TABLE `crm_customer` ADD COLUMN `social_media_type` varchar(50) NOT NULL DEFAULT '' COMMENT '社媒类型（Facebook、Ins、领英、TikTok、Youtube）' AFTER `social_media_account`;

-- 创建索引以优化查询性能
ALTER TABLE `crm_customer` ADD KEY `idx_social_media_type` (`social_media_type`);

-- =============================================
-- 添加社媒类型相关数据字典
-- =============================================

-- 1. 添加社媒类型字典类型
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (208, '社媒类型', 'crm_social_media_type', 103, 1, NOW(), 1, NOW(), 'CRM客户社媒类型选项')
ON DUPLICATE KEY UPDATE
    `dict_name` = VALUES(`dict_name`),
    `update_time` = NOW(),
    `remark` = VALUES(`remark`);

-- 2. 添加社媒类型字典数据
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2081, 1, 'Facebook', 'facebook', 'crm_social_media_type', '', 'primary', 'N', 103, 1, NOW(), 1, NOW(), 'Facebook社交媒体'),
(2082, 2, 'Instagram', 'instagram', 'crm_social_media_type', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), 'Instagram社交媒体'),
(2083, 3, '领英', 'linkedin', 'crm_social_media_type', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), 'LinkedIn领英'),
(2084, 4, 'TikTok', 'tiktok', 'crm_social_media_type', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), 'TikTok短视频'),
(2085, 5, 'YouTube', 'youtube', 'crm_social_media_type', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), 'YouTube视频平台'),
(2086, 6, '其他', 'other', 'crm_social_media_type', '', 'default', 'N', 103, 1, NOW(), 1, NOW(), '其他社交媒体平台');
