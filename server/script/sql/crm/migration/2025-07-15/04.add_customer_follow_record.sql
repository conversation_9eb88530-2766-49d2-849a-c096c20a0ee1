-- ----------------------------
-- Migration: 创建CRM客户跟进记录表
-- Author: jf
-- Date: 2025-07-15
-- Description: 创建客户跟进记录表，用于记录跟进日期、跟进内容、跟进难点等信息
-- ----------------------------

-- 创建CRM客户跟进记录表
CREATE TABLE `crm_customer_follow_record`
(
    `follow_id`               bigint       NOT NULL COMMENT '跟进记录ID',
    `customer_id`             bigint       NOT NULL COMMENT '客户ID',
    `follow_date`             datetime     NOT NULL COMMENT '跟进日期',
    `follow_content`          text         NOT NULL COMMENT '跟进内容',
    `follow_difficulties`     text         DEFAULT NULL COMMENT '跟进过程存在的难点',
    `follow_type`             varchar(50)  NOT NULL DEFAULT 'phone' COMMENT '跟进方式（电话、邮件、面谈、微信等）',
    `follow_result`           varchar(100) DEFAULT NULL COMMENT '跟进结果',
    `next_follow_date`        datetime     DEFAULT NULL COMMENT '下次跟进日期',
    `follow_user_id`          bigint       NOT NULL COMMENT '跟进人员ID',
    `follow_user_name`        varchar(100) NOT NULL COMMENT '跟进人员姓名',
    `create_dept`             bigint       DEFAULT NULL COMMENT '创建部门',
    `create_by`               bigint       DEFAULT NULL COMMENT '创建者',
    `create_time`             datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`               bigint       DEFAULT NULL COMMENT '更新者',
    `update_time`             datetime     DEFAULT NULL COMMENT '更新时间',
    `remark`                  varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`follow_id`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_follow_date` (`follow_date`),
    KEY `idx_follow_user_id` (`follow_user_id`),
    KEY `idx_next_follow_date` (`next_follow_date`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = 'CRM客户跟进记录表';

-- =============================================
-- 添加跟进方式相关数据字典
-- =============================================

-- 1. 添加跟进方式字典类型
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (207, '跟进方式', 'crm_follow_type', 103, 1, NOW(), 1, NOW(), 'CRM客户跟进方式选项')
ON DUPLICATE KEY UPDATE
    `dict_name` = VALUES(`dict_name`),
    `update_time` = NOW(),
    `remark` = VALUES(`remark`);

-- 2. 添加跟进方式字典数据
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2071, 1, '电话', 'phone', 'crm_follow_type', '', 'primary', 'Y', 103, 1, NOW(), 1, NOW(), '电话跟进'),
(2072, 2, '邮件', 'email', 'crm_follow_type', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), '邮件跟进'),
(2073, 3, '面谈', 'meeting', 'crm_follow_type', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '面对面会谈'),
(2074, 4, '微信', 'wechat', 'crm_follow_type', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '微信跟进'),
(2075, 5, 'WhatsApp', 'whatsapp', 'crm_follow_type', '', 'cyan', 'N', 103, 1, NOW(), 1, NOW(), 'WhatsApp跟进'),
(2076, 6, 'Telegram', 'telegram', 'crm_follow_type', '', 'purple', 'N', 103, 1, NOW(), 1, NOW(), 'Telegram跟进'),
(2077, 7, '视频会议', 'video_call', 'crm_follow_type', '', 'orange', 'N', 103, 1, NOW(), 1, NOW(), '视频会议跟进'),
(2078, 8, '其他', 'other', 'crm_follow_type', '', 'default', 'N', 103, 1, NOW(), 1, NOW(), '其他跟进方式');
