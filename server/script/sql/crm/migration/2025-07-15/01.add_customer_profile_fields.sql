-- ----------------------------
-- Migration: 为CRM客户表添加客户画像相关字段
-- Author: jf
-- Date: 2025-07-15
-- Description: 在crm_customer表中添加客户画像相关字段，包括采购信息、沟通偏好、决策权等
-- ----------------------------

-- 添加单次采购金额字段
ALTER TABLE `crm_customer` ADD COLUMN `single_purchase_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '单次采购金额' AFTER `total_order_amount`;

-- 添加采购频率字段
ALTER TABLE `crm_customer` ADD COLUMN `purchase_frequency` varchar(50) NOT NULL DEFAULT '' COMMENT '采购频率（每周、每月、每季度、不定期）' AFTER `single_purchase_amount`;

-- 添加是否好沟通字段
ALTER TABLE `crm_customer` ADD COLUMN `is_easy_communicate` char(1) NOT NULL DEFAULT '1' COMMENT '是否好沟通（0否 1是）' AFTER `purchase_frequency`;

-- 添加联系方式偏好字段
ALTER TABLE `crm_customer` ADD COLUMN `contact_preference` varchar(100) NOT NULL DEFAULT '' COMMENT '联系方式偏好（电话、邮件、WhatsApp、微信等）' AFTER `is_easy_communicate`;

-- 添加决策权大小字段
ALTER TABLE `crm_customer` ADD COLUMN `decision_authority` varchar(50) NOT NULL DEFAULT '' COMMENT '决策权大小（完全决策权、部分决策权、无决策权、需要上级审批）' AFTER `contact_preference`;

-- 添加对价格敏感的程度字段
ALTER TABLE `crm_customer` ADD COLUMN `price_sensitivity` varchar(50) NOT NULL DEFAULT '' COMMENT '对价格敏感的程度（高、中、低）' AFTER `decision_authority`;

-- 添加成交的关键点字段
ALTER TABLE `crm_customer` ADD COLUMN `deal_key_points` text NOT NULL COMMENT '成交的关键点' AFTER `price_sensitivity`;

-- 添加已销售的竞品品牌字段
ALTER TABLE `crm_customer` ADD COLUMN `competitor_brands` varchar(500) NOT NULL DEFAULT '' COMMENT '已销售的竞品品牌' AFTER `deal_key_points`;

-- 添加联系频次字段
ALTER TABLE `crm_customer` ADD COLUMN `contact_frequency` varchar(50) NOT NULL DEFAULT '' COMMENT '联系频次（每天、每周、每月、不定期）' AFTER `competitor_brands`;

-- 添加下单频率字段
ALTER TABLE `crm_customer` ADD COLUMN `order_frequency` varchar(50) NOT NULL DEFAULT '' COMMENT '下单频率（每周、每月、每季度、不定期）' AFTER `contact_frequency`;

-- 创建索引以优化查询性能
ALTER TABLE `crm_customer` ADD KEY `idx_purchase_frequency` (`purchase_frequency`);
ALTER TABLE `crm_customer` ADD KEY `idx_is_easy_communicate` (`is_easy_communicate`);
ALTER TABLE `crm_customer` ADD KEY `idx_decision_authority` (`decision_authority`);
ALTER TABLE `crm_customer` ADD KEY `idx_price_sensitivity` (`price_sensitivity`);
ALTER TABLE `crm_customer` ADD KEY `idx_contact_frequency` (`contact_frequency`);
ALTER TABLE `crm_customer` ADD KEY `idx_order_frequency` (`order_frequency`);

-- =============================================
-- 添加CRM客户画像相关数据字典
-- =============================================

-- 1. 添加采购频率字典类型
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (200, '采购频率', 'crm_purchase_frequency', 103, 1, NOW(), 1, NOW(), 'CRM客户采购频率选项')
ON DUPLICATE KEY UPDATE
    `dict_name` = VALUES(`dict_name`),
    `update_time` = NOW(),
    `remark` = VALUES(`remark`);

-- 2. 添加采购频率字典数据
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2001, 1, '每周', 'weekly', 'crm_purchase_frequency', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '每周采购一次'),
(2002, 2, '每月', 'monthly', 'crm_purchase_frequency', '', 'primary', 'Y', 103, 1, NOW(), 1, NOW(), '每月采购一次'),
(2003, 3, '每季度', 'quarterly', 'crm_purchase_frequency', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), '每季度采购一次'),
(2004, 4, '不定期', 'irregular', 'crm_purchase_frequency', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '不定期采购');

-- 3. 添加联系方式偏好字典类型
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (201, '联系方式偏好', 'crm_contact_preference', 103, 1, NOW(), 1, NOW(), 'CRM客户联系方式偏好选项')
ON DUPLICATE KEY UPDATE
    `dict_name` = VALUES(`dict_name`),
    `update_time` = NOW(),
    `remark` = VALUES(`remark`);

-- 4. 添加联系方式偏好字典数据
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2011, 1, '电话', 'phone', 'crm_contact_preference', '', 'primary', 'Y', 103, 1, NOW(), 1, NOW(), '电话联系'),
(2012, 2, '邮件', 'email', 'crm_contact_preference', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), '邮件联系'),
(2013, 3, 'WhatsApp', 'whatsapp', 'crm_contact_preference', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), 'WhatsApp联系'),
(2014, 4, '微信', 'wechat', 'crm_contact_preference', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '微信联系'),
(2015, 5, 'Telegram', 'telegram', 'crm_contact_preference', '', 'cyan', 'N', 103, 1, NOW(), 1, NOW(), 'Telegram联系'),
(2016, 6, '其他', 'other', 'crm_contact_preference', '', 'default', 'N', 103, 1, NOW(), 1, NOW(), '其他联系方式');

-- 5. 添加决策权大小字典类型
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (202, '决策权大小', 'crm_decision_authority', 103, 1, NOW(), 1, NOW(), 'CRM客户决策权大小选项')
ON DUPLICATE KEY UPDATE
    `dict_name` = VALUES(`dict_name`),
    `update_time` = NOW(),
    `remark` = VALUES(`remark`);

-- 6. 添加决策权大小字典数据
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2021, 1, '完全决策权', 'full', 'crm_decision_authority', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '拥有完全决策权'),
(2022, 2, '部分决策权', 'partial', 'crm_decision_authority', '', 'primary', 'Y', 103, 1, NOW(), 1, NOW(), '拥有部分决策权'),
(2023, 3, '无决策权', 'none', 'crm_decision_authority', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), '无决策权'),
(2024, 4, '需要上级审批', 'approval', 'crm_decision_authority', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '需要上级审批');

-- 7. 添加价格敏感程度字典类型
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (203, '价格敏感程度', 'crm_price_sensitivity', 103, 1, NOW(), 1, NOW(), 'CRM客户价格敏感程度选项')
ON DUPLICATE KEY UPDATE
    `dict_name` = VALUES(`dict_name`),
    `update_time` = NOW(),
    `remark` = VALUES(`remark`);

-- 8. 添加价格敏感程度字典数据
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2031, 1, '高', 'high', 'crm_price_sensitivity', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), '对价格高度敏感'),
(2032, 2, '中', 'medium', 'crm_price_sensitivity', '', 'warning', 'Y', 103, 1, NOW(), 1, NOW(), '对价格中等敏感'),
(2033, 3, '低', 'low', 'crm_price_sensitivity', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '对价格不敏感');

-- 9. 添加联系频次字典类型
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (204, '联系频次', 'crm_contact_frequency', 103, 1, NOW(), 1, NOW(), 'CRM客户联系频次选项')
ON DUPLICATE KEY UPDATE
    `dict_name` = VALUES(`dict_name`),
    `update_time` = NOW(),
    `remark` = VALUES(`remark`);

-- 10. 添加联系频次字典数据
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2041, 1, '每天', 'daily', 'crm_contact_frequency', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '每天联系'),
(2042, 2, '每周', 'weekly', 'crm_contact_frequency', '', 'primary', 'Y', 103, 1, NOW(), 1, NOW(), '每周联系'),
(2043, 3, '每月', 'monthly', 'crm_contact_frequency', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), '每月联系'),
(2044, 4, '不定期', 'irregular', 'crm_contact_frequency', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '不定期联系');

-- 11. 添加下单频率字典类型
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (205, '下单频率', 'crm_order_frequency', 103, 1, NOW(), 1, NOW(), 'CRM客户下单频率选项')
ON DUPLICATE KEY UPDATE
    `dict_name` = VALUES(`dict_name`),
    `update_time` = NOW(),
    `remark` = VALUES(`remark`);

-- 12. 添加下单频率字典数据
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2051, 1, '每周', 'weekly', 'crm_order_frequency', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '每周下单'),
(2052, 2, '每月', 'monthly', 'crm_order_frequency', '', 'primary', 'Y', 103, 1, NOW(), 1, NOW(), '每月下单'),
(2053, 3, '每季度', 'quarterly', 'crm_order_frequency', '', 'info', 'N', 103, 1, NOW(), 1, NOW(), '每季度下单'),
(2054, 4, '不定期', 'irregular', 'crm_order_frequency', '', 'warning', 'N', 103, 1, NOW(), 1, NOW(), '不定期下单');
