-- ----------------------------
-- Migration: 移除CRM表中的租户相关字段
-- Author: jf
-- Date: 2025-07-15
-- Description: 移除crm_customer表中的tenant_id字段，因为BaseEntity已包含相关功能
-- Rollback: 使用 05.remove_tenant_fields_rollback.sql 进行回滚
-- ----------------------------

-- 开始事务
START TRANSACTION;

-- 验证字段存在性
SELECT COUNT(*) as field_exists_before
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'crm_customer'
  AND COLUMN_NAME = 'tenant_id';

-- 移除客户表中的租户编号字段
ALTER TABLE `crm_customer` DROP COLUMN `tenant_id`;

-- 验证字段已被移除
SELECT COUNT(*) as field_exists_after
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'crm_customer'
  AND COLUMN_NAME = 'tenant_id';

-- 提交事务
COMMIT;
