# CRM模块数据库迁移 2025-07-15

## 迁移概述

本次迁移主要新增CRM客户画像相关字段，提升客户管理能力。

## 迁移文件列表

### 01.add_customer_profile_fields.sql
**功能**: 新增CRM客户画像字段
**影响表**: `crm_customer`, `sys_dict_type`, `sys_dict_data`

**主要变更**:
- 添加10个客户画像相关字段到 `crm_customer` 表
- 创建6个数据字典类型及对应数据
- 添加数据库索引优化查询性能

### 02.add_order_date_fields.sql
**功能**: 新增CRM客户下单日期字段
**影响表**: `crm_customer`

**主要变更**:
- 添加 `first_order_date` 字段（第一次下单日期）
- 添加 `last_order_date` 字段（最后一次下单日期）
- 创建相应的数据库索引优化查询性能

### 03.add_deal_influence_factors.sql
**功能**: 新增CRM客户影响成交因素字段
**影响表**: `crm_customer`, `sys_dict_type`, `sys_dict_data`

**主要变更**:
- 添加 `deal_influence_factors` 字段（影响成交的主要因素）
- 添加 `deal_influence_other` 字段（影响成交的其他因素，自定义填写）
- 创建影响成交因素数据字典类型及选项
- 添加数据库索引优化查询性能

### 04.add_customer_follow_record.sql
**功能**: 创建CRM客户跟进记录表
**影响表**: `crm_customer_follow_record`, `sys_dict_type`, `sys_dict_data`

**主要变更**:
- 创建 `crm_customer_follow_record` 表（客户跟进记录表）
- 包含跟进日期、跟进内容、跟进难点等字段
- 创建跟进方式数据字典类型及选项
- 添加相关索引优化查询性能

### 05.remove_tenant_fields.sql
**功能**: 移除CRM表中的租户相关字段
**影响表**: `crm_customer`

**主要变更**:
- 移除 `crm_customer` 表中的 `tenant_id` 字段
- 保留 `create_dept` 字段（数据库需要，实体类通过BaseEntity继承）

### 06.add_social_media_fields.sql
**功能**: 新增CRM客户社媒相关字段
**影响表**: `crm_customer`, `sys_dict_type`, `sys_dict_data`

**主要变更**:
- 添加 `social_media_account` 字段（社媒账号）
- 添加 `social_media_type` 字段（社媒类型）
- 创建社媒类型数据字典类型及选项
- 添加数据库索引优化查询性能

### 07.fix_text_default_values.sql
**功能**: 修复TEXT字段的默认值问题
**影响表**: `crm_customer`

**主要变更**:
- 修复 `customer_profile` 字段，移除默认值
- 修复 `deal_key_points` 字段，移除默认值
- 解决MySQL不支持TEXT字段设置默认值的问题

**新增字段**:
1. `single_purchase_amount` - 单次采购金额
2. `purchase_frequency` - 采购频率
3. `is_easy_communicate` - 是否好沟通
4. `contact_preference` - 联系方式偏好
5. `decision_authority` - 决策权大小
6. `price_sensitivity` - 对价格敏感的程度
7. `deal_key_points` - 成交的关键点
8. `competitor_brands` - 已销售的竞品品牌
9. `contact_frequency` - 联系频次
10. `order_frequency` - 下单频率
11. `first_order_date` - 第一次下单日期
12. `last_order_date` - 最后一次下单日期
13. `deal_influence_factors` - 影响成交的主要因素
14. `deal_influence_other` - 影响成交的其他因素（自定义填写）
15. `social_media_account` - 社媒账号
16. `social_media_type` - 社媒类型

**新增数据字典**:
- `crm_purchase_frequency` - 采购频率选项
- `crm_contact_preference` - 联系方式偏好选项
- `crm_decision_authority` - 决策权大小选项
- `crm_price_sensitivity` - 价格敏感程度选项
- `crm_contact_frequency` - 联系频次选项
- `crm_order_frequency` - 下单频率选项
- `crm_deal_influence_factors` - 影响成交因素选项
- `crm_follow_type` - 跟进方式选项
- `crm_social_media_type` - 社媒类型选项

## 执行说明

### 执行顺序
按文件编号顺序执行本次迁移的文件：
1. `01.add_customer_profile_fields.sql`
2. `02.add_order_date_fields.sql`
3. `03.add_deal_influence_factors.sql`
4. `04.add_customer_follow_record.sql`
5. `05.remove_tenant_fields.sql`
6. `06.add_social_media_fields.sql`
7. `07.fix_text_default_values.sql`

### 执行方式
```bash
# 执行迁移文件（按顺序）
mysql -u username -p database_name < server/script/sql/crm/migration/2025-07-15/01.add_customer_profile_fields.sql
mysql -u username -p database_name < server/script/sql/crm/migration/2025-07-15/02.add_order_date_fields.sql
mysql -u username -p database_name < server/script/sql/crm/migration/2025-07-15/03.add_deal_influence_factors.sql
mysql -u username -p database_name < server/script/sql/crm/migration/2025-07-15/04.add_customer_follow_record.sql
mysql -u username -p database_name < server/script/sql/crm/migration/2025-07-15/05.remove_tenant_fields.sql
mysql -u username -p database_name < server/script/sql/crm/migration/2025-07-15/06.add_social_media_fields.sql
mysql -u username -p database_name < server/script/sql/crm/migration/2025-07-15/07.fix_text_default_values.sql

# 或者批量执行
for file in server/script/sql/crm/migration/2025-07-15/*.sql; do
    mysql -u username -p database_name < "$file"
done
```

### 验证方式
执行完成后，可通过以下SQL验证：
```sql
-- 验证新字段是否添加成功
DESCRIBE crm_customer;

-- 验证数据字典是否创建成功
SELECT dict_type, dict_name FROM sys_dict_type WHERE dict_type LIKE 'crm_%';

-- 验证索引是否创建成功
SHOW INDEX FROM crm_customer WHERE Key_name LIKE 'idx_%';

-- 验证新增的下单日期字段
SELECT first_order_date, last_order_date FROM crm_customer LIMIT 5;

-- 验证影响成交因素字段
SELECT deal_influence_factors, deal_influence_other FROM crm_customer LIMIT 5;

-- 验证影响成交因素数据字典
SELECT dict_label, dict_value FROM sys_dict_data WHERE dict_type = 'crm_deal_influence_factors' ORDER BY dict_sort;

-- 验证跟进记录表是否创建成功
DESCRIBE crm_customer_follow_record;

-- 验证跟进方式数据字典
SELECT dict_label, dict_value FROM sys_dict_data WHERE dict_type = 'crm_follow_type' ORDER BY dict_sort;

-- 验证社媒相关字段
SELECT social_media_account, social_media_type FROM crm_customer LIMIT 5;

-- 验证社媒类型数据字典
SELECT dict_label, dict_value FROM sys_dict_data WHERE dict_type = 'crm_social_media_type' ORDER BY dict_sort;
```

## 注意事项

1. **备份数据**: 执行前请备份相关数据表
2. **测试环境**: 建议先在测试环境验证
3. **向后兼容**: 所有新增字段都有默认值，不影响现有功能
4. **权限要求**: 需要数据库表结构修改权限

## 回滚方案

如需回滚，可执行以下操作：
```sql
-- 删除新增字段
ALTER TABLE crm_customer
DROP COLUMN single_purchase_amount,
DROP COLUMN purchase_frequency,
DROP COLUMN is_easy_communicate,
DROP COLUMN contact_preference,
DROP COLUMN decision_authority,
DROP COLUMN price_sensitivity,
DROP COLUMN deal_key_points,
DROP COLUMN competitor_brands,
DROP COLUMN contact_frequency,
DROP COLUMN order_frequency,
DROP COLUMN first_order_date,
DROP COLUMN last_order_date,
DROP COLUMN deal_influence_factors,
DROP COLUMN deal_influence_other;

-- 删除数据字典（谨慎操作）
DELETE FROM sys_dict_data WHERE dict_type LIKE 'crm_%';
DELETE FROM sys_dict_type WHERE dict_type LIKE 'crm_%';
```
