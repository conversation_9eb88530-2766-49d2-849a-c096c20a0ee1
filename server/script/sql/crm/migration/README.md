# CRM模块数据库迁移文件

## 目录结构

```
server/script/sql/crm/migration/
├── README.md                           # 本说明文件
├── 2025-07-15/                        # 2025年7月15日迁移文件
│   ├── README.md                       # 该日期的迁移说明
│   └── 01.add_customer_profile_fields.sql
├── 2025-07-16/                        # 2025年7月16日迁移文件
│   └── README.md                       # 客户画像字段迁移
├── 2025-07-17/                        # 2025年7月17日迁移文件
│   ├── README.md                       # 该日期的迁移说明
│   └── 01.add_customer_status_description.sql
└── YYYY-MM-DD/                        # 未来日期迁移文件
    ├── README.md
    ├── 01.xxx.sql
    └── 02.xxx.sql
```

## 命名规范

### 1. 文件夹命名
- 格式：`YYYY-MM-DD`（年-月-日）
- 示例：`2025-07-15`、`2025-08-01`

### 2. SQL文件命名
- 格式：`序号.功能描述.sql`
- 序号：两位数字，从01开始
- 示例：
  - `01.add_customer_profile_fields.sql`
  - `02.update_customer_status_enum.sql`
  - `03.remove_deprecated_columns.sql`

### 3. README文件
- 每个日期文件夹内必须包含`README.md`
- 说明该日期的所有迁移变更内容

## 执行顺序

1. **按日期顺序**：先执行较早日期的文件夹
2. **按序号顺序**：同一日期内按文件序号顺序执行

示例执行顺序：
```
2025-07-15/01.add_customer_profile_fields.sql
2025-07-15/02.xxx.sql
2025-07-16/（客户画像字段迁移）
2025-07-17/01.add_customer_status_description.sql
2025-08-01/01.xxx.sql
2025-08-01/02.xxx.sql
```

## 使用示例

```bash
# 执行特定日期的迁移
for file in server/script/sql/crm/migration/2025-07-15/*.sql; do
    mysql -u username -p database_name < "$file"
done

# 执行所有迁移（按日期顺序）
for date_dir in server/script/sql/crm/migration/20*/; do
    for file in "$date_dir"*.sql; do
        mysql -u username -p database_name < "$file"
    done
done
```

## 注意事项

1. **向后兼容**：所有迁移都应保持向后兼容
2. **数据安全**：涉及数据删除的操作需要特别谨慎
3. **测试验证**：每个迁移文件都应在测试环境验证
4. **回滚计划**：重要变更应准备回滚脚本
5. **文档完整**：每个日期文件夹都要有详细的README说明
