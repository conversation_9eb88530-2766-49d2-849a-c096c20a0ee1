# CRM社媒字典数据功能说明

## 📋 功能概述

为CRM系统的社交媒体账号管理功能添加标准化的字典数据支持，包括社媒状态和是否主要账号的字典配置。

## 🎯 主要目标

1. **标准化字典管理**: 将硬编码的状态值改为字典配置
2. **统一常量定义**: 在后端constant包下统一管理字典类型
3. **前端动态获取**: 前端通过字典API动态获取选项数据
4. **提高可维护性**: 便于后续扩展和修改状态选项

## 📊 新增字典数据

### 1. 社媒状态字典 (crm_social_media_status)

| 字典值 | 字典标签 | 样式类 | 说明 |
|--------|----------|--------|------|
| 1 | 正常 | success | 社媒账号正常使用状态 |
| 0 | 停用 | danger | 社媒账号停用状态 |

### 2. 是否主要账号字典 (crm_is_primary_account)

| 字典值 | 字典标签 | 样式类 | 说明 |
|--------|----------|--------|------|
| 1 | 是 | primary | 主要联系的社媒账号 |
| 0 | 否 | default | 非主要社媒账号 |

## 🔧 技术实现

### 1. 数据库层面
- 使用雪花算法生成唯一的字典ID
- 遵循现有字典数据结构规范
- 支持样式类配置用于前端显示

### 2. 后端实现
- 在 `CrmDictEnum` 中添加新的字典类型常量
- 统一管理字典类型，避免硬编码
- 提供字典查询接口

### 3. 前端实现
- 使用 `getDictOptions` 动态获取字典数据
- 在表单组件中使用字典选项
- 在列表显示中使用字典标签

## 📁 涉及文件

### SQL文件
- `server/script/sql/crm/migration/2025-07-30/01.add_social_media_dict_data.sql` - 字典数据SQL

### 后端文件
- `web/apps/lookah-admin/src/constants/index.ts` - 添加字典枚举常量

### 前端文件
- `web/apps/lookah-admin/src/views/crm/customer/components/social-media-modal.vue` - 社媒编辑模态框
- `web/apps/lookah-admin/src/views/crm/customer/components/social-media-list.vue` - 社媒列表组件

## 🚀 使用方式

### 后端常量定义
```typescript
export enum CrmDictEnum {
  // 现有字典...
  CRM_SOCIAL_MEDIA_STATUS = 'crm_social_media_status',
  CRM_IS_PRIMARY_ACCOUNT = 'crm_is_primary_account',
}
```

### 前端使用示例
```typescript
// 获取社媒状态选项
const statusOptions = getDictOptions(CrmDictEnum.CRM_SOCIAL_MEDIA_STATUS)

// 获取是否主要账号选项
const primaryOptions = getDictOptions(CrmDictEnum.CRM_IS_PRIMARY_ACCOUNT)
```

## ✅ 预期效果

1. **配置化管理**: 状态选项可通过字典管理界面动态配置
2. **国际化支持**: 便于后续添加多语言支持
3. **样式统一**: 通过字典样式类统一前端显示效果
4. **扩展性强**: 便于添加新的状态类型或选项

## 🔄 实际修改内容

### 1. 数据库字典数据
- 添加了 `crm_social_media_status` 字典类型和数据
- 添加了 `crm_is_primary_account` 字典类型和数据
- 使用雪花算法生成唯一ID

### 2. 后端常量定义
```typescript
export enum CrmDictEnum {
  // 现有字典...
  CRM_SOCIAL_MEDIA_STATUS = 'crm_social_media_status',
  CRM_IS_PRIMARY_ACCOUNT = 'crm_is_primary_account',
}
```

### 3. 前端组件更新
- **社媒模态框**:
  - 将硬编码的Switch改为使用字典的Select
  - 添加状态字段选择
  - 动态获取字典选项
- **社媒列表**:
  - 添加状态显示标识
  - 使用字典标签显示状态和主要账号信息
  - 添加相应的CSS样式

### 4. 用户界面改进
- 状态标识使用不同颜色区分（正常=绿色，停用=红色）
- 主要账号标识保持原有的星标样式
- 表单中使用下拉选择替代开关组件，提供更好的用户体验

## 📝 注意事项

1. 执行SQL前请确保数据库连接正常
2. 雪花算法ID需要确保在系统中唯一
3. 字典类型名称需要与常量定义保持一致
4. 前端组件已同步更新使用新的字典配置
5. 现有数据的状态字段默认值为'1'（正常状态）
