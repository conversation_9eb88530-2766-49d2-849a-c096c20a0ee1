# 客户导入导出功能优化说明

## 📋 优化概述

对CRM客户列表的导入导出功能进行优化，新增新老客户字段支持，并将社交媒体账号从单组改为支持多组数据（最多3组）。

## 🎯 优化内容

### 1. 新增字段支持

#### 1.1 新老客户字段
- **字段名**: `isNewCustomer`
- **字典类型**: `CRM_IS_NEW_CUSTOMER`
- **选项**: 
  - `1` - 新客户（默认）
  - `0` - 老客户
- **Excel列名**: "新老客户"

#### 1.2 客户说明字段
- **字段名**: `customerDescription`
- **类型**: 文本
- **Excel列名**: "客户说明"

### 2. 社交媒体多组支持

#### 2.1 导入模板字段
原来的单组字段：
- ~~社媒账号~~
- ~~社媒类型~~

新的多组字段：
- **社交媒体账号1** / **社交媒体类型1**
- **社交媒体账号2** / **社交媒体类型2**  
- **社交媒体账号3** / **社交媒体类型3**

#### 2.2 数据处理逻辑
- 第一组自动设为主要账号（`isPrimary = "1"`）
- 其他组设为非主要账号（`isPrimary = "0"`）
- 按顺序设置排序号（`sortOrder = 1,2,3`）
- 所有账号状态默认为正常（`status = "1"`）

## 🔧 技术实现

### 1. 修改的文件

#### 1.1 CrmCustomerImportVo.java
**路径**: `server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/domain/vo/CrmCustomerImportVo.java`

**新增字段**:
```java
/**
 * 是否为新客户
 */
@ExcelProperty(value = "新老客户", converter = ExcelDictConvert.class)
@ExcelDictFormat(dictType = "CRM_IS_NEW_CUSTOMER")
private String isNewCustomer;

/**
 * 客户说明
 */
@ExcelProperty(value = "客户说明")
private String customerDescription;

/**
 * 社交媒体账号1-3
 */
@ExcelProperty(value = "社交媒体账号1")
private String socialMediaAccount1;

@ExcelProperty(value = "社交媒体类型1", converter = ExcelDictConvert.class)
@ExcelDictFormat(dictType = "crm_social_media_type")
private String socialMediaType1;

// ... 类似的字段2和3
```

#### 1.2 CrmCustomerVo.java
**路径**: `server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/domain/vo/CrmCustomerVo.java`

**新增导出字段**:
```java
/**
 * 社交媒体账号1-3（用于导出）
 */
@ExcelProperty(value = "社交媒体账号1")
private String socialMediaAccount1;

@ExcelProperty(value = "社交媒体类型1", converter = ExcelDictConvert.class)
@ExcelDictFormat(dictType = "crm_social_media_type")
private String socialMediaType1;

// ... 类似的字段2和3
```

#### 1.3 CrmCustomerImportListener.java
**路径**: `server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/listener/CrmCustomerImportListener.java`

**新增处理逻辑**:
```java
// 设置新老客户默认值
if (StringUtils.isBlank(customerBo.getIsNewCustomer())) {
    customerBo.setIsNewCustomer("1"); // 默认为新客户
}

// 处理多组社交媒体数据
List<CrmCustomerSocialMediaBo> socialMediaList = new ArrayList<>();

// 处理第一组社交媒体
if (StringUtils.isNotEmpty(importVo.getSocialMediaAccount1()) && 
    StringUtils.isNotEmpty(importVo.getSocialMediaType1())) {
    CrmCustomerSocialMediaBo socialMedia1 = new CrmCustomerSocialMediaBo();
    socialMedia1.setSocialMediaAccount(importVo.getSocialMediaAccount1());
    socialMedia1.setSocialMediaType(importVo.getSocialMediaType1());
    socialMedia1.setIsPrimary("1"); // 第一个设为主要账号
    socialMedia1.setSortOrder(1);
    socialMedia1.setStatus("1");
    socialMediaList.add(socialMedia1);
}

// ... 类似处理第二组和第三组

customerBo.setSocialMediaList(socialMediaList);
```

#### 1.4 CrmCustomerServiceImpl.java
**路径**: `server/lookah-business/lookah-crm/lookah-crm-core/src/main/java/com/imhuso/crm/core/service/impl/CrmCustomerServiceImpl.java`

**新增方法**:
```java
/**
 * 填充社交媒体数据用于导出
 */
private void fillSocialMediaForExport(CrmCustomerVo customerVo) {
    if (customerVo.getCustomerId() != null) {
        List<CrmCustomerSocialMediaVo> socialMediaList = socialMediaService.queryByCustomerId(customerVo.getCustomerId());
        customerVo.setSocialMediaList(socialMediaList);
        
        // 将社交媒体列表转换为导出用的单独字段
        if (socialMediaList != null && !socialMediaList.isEmpty()) {
            for (int i = 0; i < socialMediaList.size() && i < 3; i++) {
                CrmCustomerSocialMediaVo socialMedia = socialMediaList.get(i);
                switch (i) {
                    case 0:
                        customerVo.setSocialMediaAccount1(socialMedia.getSocialMediaAccount());
                        customerVo.setSocialMediaType1(socialMedia.getSocialMediaType());
                        break;
                    case 1:
                        customerVo.setSocialMediaAccount2(socialMedia.getSocialMediaAccount());
                        customerVo.setSocialMediaType2(socialMedia.getSocialMediaType());
                        break;
                    case 2:
                        customerVo.setSocialMediaAccount3(socialMedia.getSocialMediaAccount());
                        customerVo.setSocialMediaType3(socialMedia.getSocialMediaType());
                        break;
                }
            }
        }
    }
}
```

### 2. 数据库更新

#### 2.1 字典数据
**文件**: `03.update_customer_import_export_fields.sql`

```sql
-- 新老客户字典类型
INSERT IGNORE INTO sys_dict_type (...) VALUES (..., 'CRM_IS_NEW_CUSTOMER', ...);

-- 新老客户字典数据
INSERT IGNORE INTO sys_dict_data (...) VALUES 
(..., '新客户', '1', 'CRM_IS_NEW_CUSTOMER', ...),
(..., '老客户', '0', 'CRM_IS_NEW_CUSTOMER', ...);
```

## 📊 功能效果

### 1. 导入模板变化

#### 原模板列
- 客户编号、公司名称、客户类型...
- 社媒账号、社媒类型
- ...其他字段

#### 新模板列
- 客户编号、公司名称、客户类型...
- **新老客户**、**客户说明**
- **社交媒体账号1**、**社交媒体类型1**
- **社交媒体账号2**、**社交媒体类型2**
- **社交媒体账号3**、**社交媒体类型3**
- ...其他字段

### 2. 导入处理流程

1. **Excel解析**: 读取多组社交媒体数据
2. **数据验证**: 验证字典值和必填字段
3. **数据转换**: 将多组数据转换为socialMediaList
4. **数据保存**: 
   - 保存客户基本信息
   - 批量保存社交媒体账号列表

### 3. 导出处理流程

1. **数据查询**: 查询客户基本信息
2. **社交媒体填充**: 查询并填充socialMediaList
3. **格式转换**: 将socialMediaList转换为导出用的单独字段
4. **Excel生成**: 生成包含多组社交媒体数据的Excel

## ✅ 验证方法

### 1. 导入测试
1. 下载新的导入模板
2. 填写测试数据（包含多组社交媒体）
3. 上传导入，检查数据是否正确保存
4. 在客户详情页面验证社交媒体账号显示

### 2. 导出测试
1. 导出客户数据
2. 检查Excel中是否包含新老客户字段
3. 检查社交媒体数据是否正确分组显示
4. 验证字典值是否正确转换为标签

### 3. 数据一致性测试
1. 导入数据后立即导出
2. 对比导入和导出的数据是否一致
3. 验证社交媒体数据的完整性

## 🔄 升级步骤

1. **执行SQL脚本**: 运行字典数据更新脚本
2. **重新编译**: 编译后端代码
3. **重启服务**: 重启应用服务
4. **测试验证**: 执行导入导出功能测试
5. **用户培训**: 通知用户新模板格式变化

## 📝 注意事项

1. **向后兼容**: 新字段都是可选的，不影响现有数据
2. **数据迁移**: 现有客户默认为新客户，可后续手动调整
3. **模板更新**: 用户需要下载新的导入模板
4. **字段限制**: 最多支持3组社交媒体账号
5. **性能影响**: 导出时会额外查询社交媒体数据，可能略微影响性能

现在客户导入导出功能已完全支持新老客户标识和多组社交媒体账号！

## 🔔 客户已存在检查功能

### 功能概述
在新增客户时，系统会自动检查客户编号和账户邮箱是否已存在，如果存在则弹出客户说明确认对话框，允许用户更新已存在客户的说明信息。

### 检查逻辑
1. **客户编号检查**: 使用 `checkCustomerCodeUniqueInActive()` 方法
2. **账户邮箱检查**: 使用 `checkAccountEmailUniqueInActive()` 方法
3. **获取已存在客户**: 使用 `getExistingCustomer()` 方法

### 用户体验流程
1. 用户填写新客户信息并提交
2. 系统检查客户编号和邮箱是否已存在
3. 如果存在，弹出客户说明确认对话框
4. 显示已存在客户的详细信息
5. 用户填写或更新客户说明
6. 确认后更新已存在客户的说明信息
7. 刷新客户列表，显示更新结果

### 涉及组件
- **CustomerDescriptionModal.vue**: 客户说明确认对话框
- **CustomerDrawer.vue**: 客户抽屉（集成存在检查）
- **客户列表页面**: 处理客户已存在事件

### API接口
- `GET /check-code-unique`: 检查客户编号唯一性
- `GET /check-email-unique`: 检查账户邮箱唯一性
- `GET /get-existing-customer`: 获取已存在的客户信息
- `PUT /{customerId}/description`: 更新客户说明

这样既保证了数据的唯一性，又提供了友好的用户体验，避免了重复数据的产生！
