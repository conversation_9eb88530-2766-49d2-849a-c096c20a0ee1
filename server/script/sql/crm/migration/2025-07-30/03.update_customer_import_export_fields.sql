-- ----------------------------
-- Migration: 更新客户导入导出功能，添加新老客户和多组社交媒体支持
-- Author: AI Assistant
-- Date: 2025-07-30
-- Description: 
-- 1. 确保新老客户字典数据存在
-- 2. 更新导入导出模板支持多组社交媒体账号
-- 3. 添加客户说明字段支持
-- ----------------------------

-- 检查并插入新老客户字典类型
INSERT IGNORE INTO sys_dict_type (dict_id, tenant_id, dict_name, dict_type, create_dept, create_by, create_time, update_by, update_time, remark) 
VALUES (
    NEXT_VALUE(sys_dict_type_seq), 
    '000000', 
    '新老客户', 
    'CRM_IS_NEW_CUSTOMER', 
    103, 
    1, 
    NOW(), 
    1, 
    NOW(), 
    'CRM客户新老客户标识'
);

-- 检查并插入新老客户字典数据
INSERT IGNORE INTO sys_dict_data (dict_code, tenant_id, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, create_dept, create_by, create_time, update_by, update_time, remark) 
VALUES 
(NEXT_VALUE(sys_dict_data_seq), '000000', 1, '新客户', '1', 'CRM_IS_NEW_CUSTOMER', '', 'primary', 'Y', 103, 1, NOW(), 1, NOW(), '新客户'),
(NEXT_VALUE(sys_dict_data_seq), '000000', 2, '老客户', '0', 'CRM_IS_NEW_CUSTOMER', '', 'success', 'N', 103, 1, NOW(), 1, NOW(), '老客户');

-- 验证字典数据
SELECT 
    dt.dict_name,
    dt.dict_type,
    dd.dict_label,
    dd.dict_value,
    dd.dict_sort
FROM sys_dict_type dt
LEFT JOIN sys_dict_data dd ON dt.dict_type = dd.dict_type
WHERE dt.dict_type = 'CRM_IS_NEW_CUSTOMER'
ORDER BY dd.dict_sort;

-- 验证社交媒体类型字典（应该已存在）
SELECT 
    dt.dict_name,
    dt.dict_type,
    dd.dict_label,
    dd.dict_value,
    dd.dict_sort
FROM sys_dict_type dt
LEFT JOIN sys_dict_data dd ON dt.dict_type = dd.dict_type
WHERE dt.dict_type = 'crm_social_media_type'
ORDER BY dd.dict_sort;

-- 注意：以下功能已通过Java代码实现，无需SQL修改：
-- 1. CrmCustomerImportVo 新增字段：
--    - isNewCustomer (新老客户)
--    - customerDescription (客户说明)
--    - socialMediaAccount1/2/3 (多组社交媒体账号)
--    - socialMediaType1/2/3 (多组社交媒体类型)
--
-- 2. CrmCustomerVo 新增导出字段：
--    - socialMediaAccount1/2/3 (用于导出的社交媒体账号)
--    - socialMediaType1/2/3 (用于导出的社交媒体类型)
--
-- 3. CrmCustomerImportListener 新增处理逻辑：
--    - 处理新老客户默认值设置
--    - 处理多组社交媒体数据转换为socialMediaList
--
-- 4. CrmCustomerServiceImpl 新增方法：
--    - fillSocialMediaForExport() 填充导出用的社交媒体字段
