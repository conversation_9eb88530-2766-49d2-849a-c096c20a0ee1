-- ----------------------------
-- Migration: 添加社媒相关字典数据
-- Author: AI Assistant
-- Date: 2025-07-30
-- Description: 为CRM系统添加社媒状态和是否主要账号字典数据
-- ----------------------------

-- 1. 社媒状态字典类型
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1753857701949440001, 'CRM社媒状态', 'crm_social_media_status', 103, 1, NOW(), 1, NOW(), 'CRM社媒账号状态');

-- 社媒状态字典数据
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1753857701949440002, 1, '正常', '1', 'crm_social_media_status', '', 'success', 'Y', 103, 1, NOW(), 1, NOW(), '社媒账号正常状态'),
(1753857701949440003, 2, '停用', '0', 'crm_social_media_status', '', 'danger', 'N', 103, 1, NOW(), 1, NOW(), '社媒账号停用状态');

-- 2. 是否主要账号字典类型
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1753857701949440004, 'CRM是否主要账号', 'crm_is_primary_account', 103, 1, NOW(), 1, NOW(), 'CRM社媒是否主要账号');

-- 是否主要账号字典数据
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1753857701949440005, 1, '是', '1', 'crm_is_primary_account', '', 'primary', 'Y', 103, 1, NOW(), 1, NOW(), '主要社媒账号'),
(1753857701949440006, 2, '否', '0', 'crm_is_primary_account', '', 'default', 'N', 103, 1, NOW(), 1, NOW(), '非主要社媒账号');
