-- ----------------------------
-- Migration: 客户已存在检查功能测试数据
-- Author: AI Assistant
-- Date: 2025-07-30
-- Description: 
-- 为客户已存在检查功能创建测试数据
-- 用于验证新增客户时的重复检查和客户说明更新功能
-- ----------------------------

-- 插入测试客户数据（用于测试客户已存在的情况）
INSERT IGNORE INTO crm_customer (
    customer_id,
    tenant_id,
    customer_code,
    company_name,
    account_email,
    contact_email,
    contact_person,
    customer_phone,
    whatsapp_number,
    country,
    state,
    zip_code,
    customer_type,
    customer_status,
    customer_source,
    star_rating,
    business_location_count,
    is_public,
    is_new_customer,
    customer_description,
    customer_profile,
    interested_products,
    competitor_brands,
    single_purchase_amount,
    purchase_frequency,
    deal_influence_factors,
    deal_influence_other,
    sales_user_id,
    sales_user_name,
    last_follow_time,
    status_description,
    create_dept,
    create_by,
    create_time,
    update_by,
    update_time,
    del_flag
) VALUES (
    NEXT_VALUE(crm_customer_seq),
    '000000',
    'TEST001',
    '测试重复公司A',
    '<EMAIL>',
    '<EMAIL>',
    '张三',
    '******-0001',
    '******-0001',
    'United States',
    'California',
    '90210',
    'distributor',
    'potential',
    'website',
    4,
    5,
    '0',
    '1',
    '这是一个测试客户，用于验证客户已存在检查功能',
    '大型分销商，主要经营电子烟产品',
    'VAPE设备,电子烟油',
    'JUUL,IQOS',
    50000.00,
    'monthly',
    'price,quality',
    '注重产品质量和价格竞争力',
    1,
    'admin',
    NOW(),
    '潜在客户，正在洽谈中',
    103,
    1,
    NOW(),
    1,
    NOW(),
    '0'
), (
    NEXT_VALUE(crm_customer_seq),
    '000000',
    'TEST002',
    '测试重复公司B',
    '<EMAIL>',
    '<EMAIL>',
    '李四',
    '******-0002',
    '******-0002',
    'United States',
    'New York',
    '10001',
    'cash_carry',
    'contacted',
    'referral',
    3,
    2,
    '1',
    '0',
    '另一个测试客户，用于验证邮箱重复检查',
    '中小型零售商，主要服务本地市场',
    'CBD产品,电子烟',
    'PAX,Storz & Bickel',
    15000.00,
    'quarterly',
    'service,delivery',
    '重视售后服务和配送速度',
    1,
    'admin',
    DATE_SUB(NOW(), INTERVAL 7 DAY),
    '已联系，等待报价',
    103,
    1,
    NOW(),
    1,
    NOW(),
    '0'
);

-- 为测试客户添加社交媒体账号
INSERT IGNORE INTO crm_customer_social_media (
    social_media_id,
    tenant_id,
    customer_id,
    social_media_type,
    social_media_account,
    is_primary,
    sort_order,
    status,
    create_dept,
    create_by,
    create_time,
    update_by,
    update_time,
    del_flag
) VALUES (
    NEXT_VALUE(crm_customer_social_media_seq),
    '000000',
    (SELECT customer_id FROM crm_customer WHERE company_name = '测试重复公司A' LIMIT 1),
    'facebook',
    'test-company-a-facebook',
    '1',
    1,
    '1',
    103,
    1,
    NOW(),
    1,
    NOW(),
    '0'
), (
    NEXT_VALUE(crm_customer_social_media_seq),
    '000000',
    (SELECT customer_id FROM crm_customer WHERE company_name = '测试重复公司A' LIMIT 1),
    'instagram',
    'test-company-a-instagram',
    '0',
    2,
    '1',
    103,
    1,
    NOW(),
    1,
    NOW(),
    '0'
), (
    NEXT_VALUE(crm_customer_social_media_seq),
    '000000',
    (SELECT customer_id FROM crm_customer WHERE company_name = '测试重复公司B' LIMIT 1),
    'twitter',
    'test-company-b-twitter',
    '1',
    1,
    '1',
    103,
    1,
    NOW(),
    1,
    NOW(),
    '0'
);

-- 验证测试数据
SELECT 
    c.customer_code,
    c.company_name,
    c.account_email,
    c.customer_description,
    c.is_public,
    c.is_new_customer,
    COUNT(sm.social_media_id) as social_media_count
FROM crm_customer c
LEFT JOIN crm_customer_social_media sm ON c.customer_id = sm.customer_id AND sm.del_flag = '0'
WHERE c.company_name LIKE '测试重复公司%'
  AND c.del_flag = '0'
GROUP BY c.customer_id, c.customer_code, c.company_name, c.account_email, c.customer_description, c.is_public, c.is_new_customer
ORDER BY c.customer_code;

-- 测试客户存在检查查询
-- 测试1：通过公司名称检查
SELECT 
    customer_id,
    company_name,
    account_email,
    customer_description
FROM crm_customer 
WHERE company_name = '测试重复公司A' 
  AND del_flag = '0'
LIMIT 1;

-- 测试2：通过邮箱检查
SELECT 
    customer_id,
    company_name,
    account_email,
    customer_description
FROM crm_customer 
WHERE account_email = '<EMAIL>' 
  AND del_flag = '0'
LIMIT 1;

-- 测试3：通过公司名称或邮箱检查（模拟后端查询逻辑）
SELECT 
    customer_id,
    company_name,
    account_email,
    customer_description
FROM crm_customer 
WHERE (company_name = '测试重复公司A' OR account_email = '<EMAIL>')
  AND del_flag = '0'
LIMIT 1;

-- 注意：
-- 1. 这些测试数据仅用于开发和测试环境
-- 2. 生产环境部署时应该删除这些测试数据
-- 3. 测试完成后可以通过以下SQL清理测试数据：
-- 
-- DELETE FROM crm_customer_social_media WHERE customer_id IN (
--     SELECT customer_id FROM crm_customer WHERE company_name LIKE '测试重复公司%'
-- );
-- DELETE FROM crm_customer WHERE company_name LIKE '测试重复公司%';
