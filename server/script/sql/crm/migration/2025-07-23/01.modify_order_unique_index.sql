-- =====================================================
-- CRM订单表唯一索引优化
-- 日期: 2025-01-23
-- 目的: 修改订单号唯一索引，支持软删除时的订单号重用
-- =====================================================

-- 开始事务
START TRANSACTION;

-- 1. 检查当前索引状态
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'crm_order' 
  AND INDEX_NAME LIKE '%order_no%';

-- 2. 删除原有的订单号唯一索引（如果存在）
-- 注意：根据实际情况调整索引名称
SET @sql = (
    SELECT CONCAT('ALTER TABLE crm_order DROP INDEX ', INDEX_NAME, ';')
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'crm_order' 
      AND INDEX_NAME LIKE '%order_no%'
      AND NON_UNIQUE = 0
    LIMIT 1
);

-- 执行删除索引（如果存在）
SET @sql = IFNULL(@sql, 'SELECT "No unique index on order_no found" as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 创建新的复合唯一索引：订单号 + 删除时间戳
-- 这样可以保证：
-- - 未删除的记录（deleted_at = NULL）：订单号唯一
-- - 已删除的记录：相同订单号可以有不同的删除时间戳
ALTER TABLE crm_order 
ADD CONSTRAINT uk_order_no_deleted_at 
UNIQUE (order_no, deleted_at);

-- 4. 验证新索引创建成功
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE,
    SEQ_IN_INDEX
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'crm_order' 
  AND INDEX_NAME = 'uk_order_no_deleted_at'
ORDER BY SEQ_IN_INDEX;

-- 5. 检查现有数据的完整性
-- 确保没有重复的未删除订单号
SELECT 
    order_no,
    COUNT(*) as count,
    GROUP_CONCAT(order_id) as order_ids
FROM crm_order 
WHERE del_flag = '0' OR del_flag IS NULL
GROUP BY order_no 
HAVING COUNT(*) > 1;

-- 6. 显示优化结果统计
SELECT 
    '订单总数' as metric,
    COUNT(*) as value
FROM crm_order
UNION ALL
SELECT 
    '未删除订单数' as metric,
    COUNT(*) as value
FROM crm_order 
WHERE del_flag = '0' OR del_flag IS NULL
UNION ALL
SELECT 
    '已删除订单数' as metric,
    COUNT(*) as value
FROM crm_order 
WHERE del_flag = '1'
UNION ALL
SELECT 
    '有删除时间戳的订单数' as metric,
    COUNT(*) as value
FROM crm_order 
WHERE deleted_at IS NOT NULL;

-- 提交事务
COMMIT;

-- =====================================================
-- 迁移完成说明
-- =====================================================
-- 1. 已删除原有的订单号唯一索引
-- 2. 已创建新的复合唯一索引 (order_no, deleted_at)
-- 3. 未删除记录的 deleted_at 为 NULL，保证订单号唯一
-- 4. 已删除记录的 deleted_at 为时间戳，允许相同订单号
-- 5. 软删除功能现在可以正常工作，支持订单号重用
-- =====================================================

-- 回滚脚本（如需要）：
-- ALTER TABLE crm_order DROP INDEX uk_order_no_deleted_at;
-- ALTER TABLE crm_order ADD UNIQUE INDEX uk_order_no (order_no);
