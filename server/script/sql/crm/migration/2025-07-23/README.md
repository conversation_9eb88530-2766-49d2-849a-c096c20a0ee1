# CRM订单模块优化 - 2025年1月23日

## 迁移内容

### 1. 修改订单表唯一索引
- **文件**: `01.modify_order_unique_index.sql`
- **目的**: 修改订单号唯一索引，支持软删除
- **变更**: 将唯一索引从 `order_no` 改为 `order_no + deleted_at`

### 2. 优化说明

#### 问题背景
- 原有订单表使用订单号作为唯一索引
- 软删除时，删除的订单号无法重复使用
- 需要支持相同订单号在不同删除时间戳下的唯一性

#### 解决方案
- 删除原有的订单号唯一索引
- 创建新的复合唯一索引：`order_no + deleted_at`
- 未删除的记录 `deleted_at` 为 NULL，保证订单号唯一
- 已删除的记录 `deleted_at` 为删除时间戳，允许相同订单号存在

#### 技术实现
1. **后端代码优化**:
   - 查询时关联系统用户表，显示创建者和更新者名称
   - 删除时设置删除时间戳，实现软删除
   - 修改 Mapper 查询方法，支持用户信息关联

2. **数据库结构优化**:
   - 修改唯一索引结构
   - 保持数据完整性和一致性

## 执行顺序

1. `01.modify_order_unique_index.sql` - 修改订单表唯一索引

## 注意事项

1. **数据安全**: 执行前请备份相关数据表
2. **业务影响**: 迁移过程中可能短暂影响订单相关功能
3. **测试验证**: 执行后需要验证订单创建、查询、删除功能
4. **回滚准备**: 如有问题，可通过重建原索引回滚

## 验证方法

```sql
-- 验证新索引是否创建成功
SHOW INDEX FROM crm_order WHERE Key_name = 'uk_order_no_deleted_at';

-- 验证软删除功能
SELECT order_no, deleted_at, del_flag FROM crm_order WHERE del_flag = '1';

-- 验证订单号唯一性（未删除记录）
SELECT order_no, COUNT(*) FROM crm_order WHERE del_flag = '0' GROUP BY order_no HAVING COUNT(*) > 1;
```
