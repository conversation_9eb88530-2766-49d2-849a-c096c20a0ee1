# 产品选择弹窗功能增强

## 🎯 解决的问题

### 1. 产品颜色下拉框宽度变小问题
- **问题描述**: 选择产品后，颜色下拉框宽度会变小，影响用户体验
- **解决方案**: 在所有颜色选择相关的配置中添加 `style: { width: '100%' }`

### 2. 自动计算产品总金额功能
- **问题描述**: 选择产品、填写数量时需要手动计算总金额
- **解决方案**: 实现自动监听单价和数量变化，实时计算并更新总金额

## 🔧 实现详情

### 1. 修复颜色下拉框宽度问题

#### data.tsx 配置修改
```typescript
// 在颜色选择字段配置中添加样式
componentProps: {
  placeholder: '请选择颜色',
  options: [],
  getPopupContainer,
  style: { width: '100%' }, // 🔧 新增：确保宽度100%
  onchange: (value: string) => {
    console.log('Color selected:', value)
  },
},
```

#### simple-add-product-modal.vue 修改
```typescript
// 在产品选择时更新颜色选项配置
formApi.updateSchema([
  {
    fieldName: 'productColor',
    componentProps: {
      options: product.colors.map(color => ({ label: color, value: color })),
      disabled: false,
      style: { width: '100%' }, // 🔧 新增：保持宽度
      onChange: handleColorChange,
    },
  },
])

// 在弹窗打开时重新设置样式
formApi.updateSchema([
  {
    fieldName: 'productColor',
    componentProps: {
      onChange: handleColorChange,
      style: { width: '100%' }, // 🔧 新增：确保样式一致
    },
  },
])
```

### 2. 自动计算产品总金额功能

#### 新增处理函数
```typescript
// 数量变化处理
function handleQuantityChange(quantity: number) {
  currentQuantity.value = quantity || 1
  calculateAndUpdateTotalAmount()
}

// 单价变化处理
function handleUnitPriceChange(unitPrice: number) {
  currentUnitPrice.value = unitPrice || 0
  calculateAndUpdateTotalAmount()
}

// 计算并更新总金额
function calculateAndUpdateTotalAmount() {
  const totalAmount = currentUnitPrice.value * currentQuantity.value
  formApi.setValues({ productTotalAmount: totalAmount })
}
```

#### 事件绑定
```typescript
// 在弹窗打开时绑定数量和单价变化事件
formApi.updateSchema([
  {
    fieldName: 'quantity',
    componentProps: {
      onChange: handleQuantityChange,
    },
  },
  {
    fieldName: 'unitPrice',
    componentProps: {
      onChange: handleUnitPriceChange,
    },
  },
])
```

#### 产品选择时自动计算
```typescript
// 在产品选择后自动计算总金额
function handleProductChange(productId: string) {
  const product = getProductById(productId)
  if (product) {
    // ... 其他逻辑
    
    // 设置单价
    formApi.setValues({ unitPrice: product.unitPrice })
    
    // 🔧 新增：自动计算总金额
    calculateAndUpdateTotalAmount()
  }
}
```

#### 优化表单值监听
```typescript
// 监听表单值变化，避免递归调用
watch(() => formApi.getValues(), (values: any) => {
  if (values) {
    currentColor.value = values.productColor || ''
    const newUnitPrice = Number(values.unitPrice) || 0
    const newQuantity = Number(values.quantity) || 1
    
    // 只有当值真正改变时才更新
    if (currentUnitPrice.value !== newUnitPrice || currentQuantity.value !== newQuantity) {
      currentUnitPrice.value = newUnitPrice
      currentQuantity.value = newQuantity
      
      // 自动计算并更新总金额（避免递归调用）
      const totalAmount = currentUnitPrice.value * currentQuantity.value
      if (values.productTotalAmount !== totalAmount) {
        formApi.setValues({ productTotalAmount: totalAmount })
      }
    }
  }
}, { deep: true })
```

## 🎨 用户体验改进

### 1. 颜色选择体验
- ✅ **一致的宽度**: 颜色下拉框始终保持100%宽度
- ✅ **流畅交互**: 选择产品后颜色选项平滑更新
- ✅ **智能选择**: 单色产品自动选择，多色产品清空选择

### 2. 总金额计算体验
- ✅ **实时计算**: 修改单价或数量时立即更新总金额
- ✅ **自动填充**: 选择产品后自动计算初始总金额
- ✅ **格式化显示**: 总金额以货币格式显示（¥1,234.56）
- ✅ **预览同步**: 产品信息预览区域实时显示计算结果

## 🔄 计算逻辑

### 触发计算的场景
1. **选择产品**: 自动使用产品单价 × 默认数量(1)
2. **修改数量**: 当前单价 × 新数量
3. **修改单价**: 新单价 × 当前数量
4. **表单值变化**: 监听所有相关字段变化

### 计算公式
```typescript
产品总金额 = 单价 × 数量
```

### 防止递归调用
- 在 watch 中检查值是否真正改变
- 在设置 productTotalAmount 前检查是否与计算结果相同
- 使用独立的计算函数避免重复逻辑

## 🧪 测试场景

### 颜色选择测试
1. **选择单色产品**: 颜色自动选择，下拉框保持正常宽度
2. **选择多色产品**: 颜色清空，下拉框显示所有选项，宽度正常
3. **切换产品**: 颜色选项正确更新，宽度保持一致

### 总金额计算测试
1. **选择产品**: 总金额 = 产品单价 × 1
2. **修改数量**: 总金额实时更新
3. **修改单价**: 总金额实时更新
4. **组合操作**: 先选产品，再改数量，再改单价，总金额始终正确

### 预览同步测试
1. **产品信息**: 预览区域显示正确的产品信息
2. **实时更新**: 修改任何字段后预览立即更新
3. **格式化**: 金额以正确格式显示

## ✅ 实现效果

### 修复前的问题
- ❌ 颜色下拉框宽度不一致
- ❌ 需要手动计算总金额
- ❌ 用户体验不够流畅

### 修复后的效果
- ✅ 颜色下拉框始终保持100%宽度
- ✅ 总金额自动实时计算
- ✅ 用户操作更加流畅直观
- ✅ 预览信息实时同步更新

## 🚀 后续优化建议

1. **数据验证**: 添加单价和数量的合理性验证
2. **精度控制**: 确保金额计算的精度处理
3. **国际化**: 支持不同货币格式
4. **性能优化**: 对频繁计算进行防抖处理

现在的产品选择弹窗提供了更好的用户体验，颜色选择更加一致，总金额计算更加智能！
