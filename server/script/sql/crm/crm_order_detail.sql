-- ----------------------------
-- Table structure for crm_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `crm_order_detail`;
CREATE TABLE `crm_order_detail`
(
    `detail_id`             bigint         NOT NULL COMMENT '订单详情ID',
    `order_id`              bigint         NOT NULL COMMENT '订单ID',
    `order_no`              varchar(50)    NOT NULL COMMENT '订单号',
    `product_id`            bigint         DEFAULT NULL COMMENT '产品ID',
    `product_name`          varchar(200)   NOT NULL COMMENT '产品名称',
    `product_color`         varchar(100)   DEFAULT NULL COMMENT '颜色',
    `unit_price`            decimal(10, 2) NOT NULL COMMENT '单价',
    `quantity`              int            NOT NULL COMMENT '数量',
    `product_total_amount`  decimal(12, 2) NOT NULL COMMENT '单款产品总金额',
    `create_dept`           bigint         DEFAULT NULL COMMENT '创建部门',
    `create_by`             varchar(64)    DEFAULT '' COMMENT '创建者',
    `create_time`           datetime       DEFAULT NULL COMMENT '创建时间',
    `update_by`             varchar(64)    DEFAULT '' COMMENT '更新者',
    `update_time`           datetime       DEFAULT NULL COMMENT '更新时间',
    `remark`                varchar(500)   DEFAULT NULL COMMENT '备注',
    `del_flag`              char(1)        DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
    `deleted_at`            bigint         DEFAULT NULL COMMENT '删除时间戳（毫秒，软删除时记录，NULL表示未删除）',
    PRIMARY KEY (`detail_id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_del_flag` (`del_flag`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='CRM客户订单详情表';

-- ----------------------------
-- Records of crm_order_detail
-- ----------------------------
