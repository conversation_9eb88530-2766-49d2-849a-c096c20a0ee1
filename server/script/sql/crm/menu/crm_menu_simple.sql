-- ----------------------------
-- CRM客户管理菜单配置SQL（简化版）
-- Author: jf
-- Date: 2025-07-15
-- Description: 为前端侧边栏添加CRM客户管理菜单项（仅包含必要菜单）
-- ----------------------------

-- =============================================
-- 插入CRM客户管理菜单（简化版）
-- =============================================

-- 1. 插入CRM管理父级菜单（目录）
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    8000, 'CRM管理', 0, 8, 'crm', '', '',
    0, 0, 'M', '1', '1', '', 'lucide:users',
    103, 1, NOW(), 1, NOW(), 'CRM客户关系管理系统'
);

-- 2. 插入客户管理子菜单（目录）
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    8100, '客户管理', 8000, 1, 'customer', '', '',
    1, 0, 'M', '1', '1', '', 'lucide:user-check',
    103, 1, NOW(), 1, NOW(), '客户信息管理'
);

-- 3. 插入客户列表菜单（页面）
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    8101, '客户列表', 8100, 1, 'list', 'crm/customer/index', '',
    1, 1, 'C', '1', '1', 'crm:customer:list', 'lucide:list',
    103, 1, NOW(), 1, NOW(), '客户列表页面'
);

-- =============================================
-- 为超级管理员角色分配CRM菜单权限
-- =============================================

-- 为角色ID=1（超级管理员）分配CRM菜单权限
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
(1, 8000),  -- CRM管理
(1, 8100),  -- 客户管理
(1, 8101);  -- 客户列表

-- =============================================
-- 验证插入的菜单数据
-- =============================================

-- 查看CRM菜单树结构
SELECT
    m1.menu_id,
    m1.menu_name,
    m1.parent_id,
    m2.menu_name as parent_name,
    m1.order_num,
    m1.path,
    m1.component,
    m1.menu_type,
    m1.perms,
    m1.icon
FROM sys_menu m1
LEFT JOIN sys_menu m2 ON m1.parent_id = m2.menu_id
WHERE m1.menu_id IN (8000, 8100, 8101)
ORDER BY m1.parent_id, m1.order_num;
