# CRM客户管理菜单配置说明

本目录包含CRM客户管理模块的前端菜单配置SQL脚本。

## 文件说明

### 1. crm_menu.sql
**完整菜单配置**
- 包含CRM管理的完整菜单树结构
- 包含所有功能按钮权限配置
- 适用于生产环境的完整部署

### 2. crm_menu_simple.sql
**简化菜单配置**
- 仅包含基本的菜单结构
- 适用于快速测试和开发环境

## 菜单结构

### 完整菜单树结构
```
CRM管理 (8000)
└── 客户管理 (8100)
    └── 客户列表 (8101)
        ├── 客户查询 (8102)
        ├── 客户新增 (8103)
        ├── 客户修改 (8104)
        ├── 客户删除 (8105)
        ├── 客户导出 (8106)
        ├── 客户导入 (8107)
        ├── 跟进记录查询 (8108)
        ├── 跟进记录新增 (8109)
        ├── 跟进记录修改 (8110)
        └── 跟进记录删除 (8111)
```

### 简化菜单树结构
```
CRM管理 (8000)
└── 客户管理 (8100)
    └── 客户列表 (8101)
```

## 菜单字段说明

| 字段 | 说明 | 示例值 |
|------|------|--------|
| menu_id | 菜单ID（唯一标识） | 8000 |
| menu_name | 菜单名称 | CRM管理 |
| parent_id | 父菜单ID（0表示顶级菜单） | 0 |
| order_num | 显示顺序 | 8 |
| path | 路由地址 | crm |
| component | 组件路径 | crm/customer/index |
| menu_type | 菜单类型（M=目录，C=菜单，F=按钮） | M |
| visible | 是否显示（1=显示，0=隐藏） | 1 |
| status | 菜单状态（1=正常，0=停用） | 1 |
| perms | 权限标识 | crm:customer:list |
| icon | 菜单图标 | lucide:users |

## 权限标识说明

### 客户管理权限
- `crm:customer:list` - 客户列表查看
- `crm:customer:query` - 客户查询
- `crm:customer:add` - 客户新增
- `crm:customer:edit` - 客户修改
- `crm:customer:remove` - 客户删除
- `crm:customer:export` - 客户导出
- `crm:customer:import` - 客户导入

### 跟进记录权限
- `crm:follow:query` - 跟进记录查询
- `crm:follow:add` - 跟进记录新增
- `crm:follow:edit` - 跟进记录修改
- `crm:follow:remove` - 跟进记录删除

## 使用方法

### 执行完整菜单配置
```bash
mysql -u username -p database_name < server/script/sql/crm/menu/crm_menu.sql
```

### 执行简化菜单配置
```bash
mysql -u username -p database_name < server/script/sql/crm/menu/crm_menu_simple.sql
```

## 前端路由对应关系

| 菜单路径 | 前端路由 | 组件文件 |
|----------|----------|----------|
| /crm | /crm | - |
| /crm/customer | /crm/customer | - |
| /crm/customer/list | /crm/customer/list | crm/customer/index.vue |

## 注意事项

1. **菜单ID范围**：使用8000-8999范围，避免与其他模块冲突
2. **权限分配**：脚本自动为超级管理员（role_id=1）分配所有权限
3. **图标使用**：使用lucide图标库，确保前端支持
4. **路由匹配**：确保菜单路径与前端路由配置一致

## 验证菜单配置

执行以下SQL验证菜单是否正确插入：

```sql
-- 查看CRM菜单树结构
SELECT 
    m1.menu_id,
    m1.menu_name,
    m1.parent_id,
    m2.menu_name as parent_name,
    m1.order_num,
    m1.path,
    m1.component,
    m1.menu_type,
    m1.perms,
    m1.icon
FROM sys_menu m1
LEFT JOIN sys_menu m2 ON m1.parent_id = m2.menu_id
WHERE m1.menu_id >= 8000 AND m1.menu_id <= 8111
ORDER BY m1.parent_id, m1.order_num;

-- 查看角色菜单分配
SELECT 
    rm.role_id,
    r.role_name,
    m.menu_id,
    m.menu_name,
    m.perms
FROM sys_role_menu rm
JOIN sys_role r ON rm.role_id = r.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.menu_id >= 8000 AND m.menu_id <= 8111
ORDER BY rm.role_id, m.menu_id;
```

## 清理菜单配置

如需删除CRM菜单配置，可执行以下SQL：

```sql
-- 删除角色菜单关联
DELETE FROM sys_role_menu WHERE menu_id >= 8000 AND menu_id <= 8111;

-- 删除菜单项（按层级从下往上删除）
DELETE FROM sys_menu WHERE menu_id >= 8102 AND menu_id <= 8111;  -- 删除按钮权限
DELETE FROM sys_menu WHERE menu_id = 8101;  -- 删除客户列表
DELETE FROM sys_menu WHERE menu_id = 8100;  -- 删除客户管理
DELETE FROM sys_menu WHERE menu_id = 8000;  -- 删除CRM管理
```
