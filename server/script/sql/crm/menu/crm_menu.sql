-- ----------------------------
-- CRM客户管理菜单配置SQL
-- Author: jf
-- Date: 2025-07-15
-- Description: 为前端侧边栏添加CRM客户管理菜单项
-- ----------------------------

-- =============================================
-- 插入CRM客户管理菜单
-- =============================================

-- 1. 插入CRM客户管理父级菜单（目录）
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    8000, 'CRM管理', 0, 8, 'crm', '', '',
    0, 0, 'M', '1', '1', '', 'lucide:users',
    103, 1, NOW(), 1, NOW(), 'CRM客户关系管理系统'
);

-- 2. 插入客户管理子菜单（目录）
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    8100, '客户管理', 8000, 1, 'customer', '', '',
    1, 0, 'M', '1', '1', '', 'lucide:user-check',
    103, 1, NOW(), 1, NOW(), '客户信息管理'
);

-- 3. 插入客户列表菜单（页面）
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    8101, '客户列表', 8100, 1, 'list', 'crm/customer/index', '',
    1, 1, 'C', '1', '1', 'crm:customer:list', 'lucide:list',
    103, 1, NOW(), 1, NOW(), '客户列表页面'
);

-- =============================================
-- 插入客户管理相关的功能按钮权限
-- =============================================

-- 客户查询权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    8102, '客户查询', 8101, 1, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:customer:query', '#',
    103, 1, NOW(), 1, NOW(), '客户查询权限'
);

-- 客户新增权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    8103, '客户新增', 8101, 2, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:customer:add', '#',
    103, 1, NOW(), 1, NOW(), '客户新增权限'
);

-- 客户修改权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    8104, '客户修改', 8101, 3, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:customer:edit', '#',
    103, 1, NOW(), 1, NOW(), '客户修改权限'
);

-- 客户删除权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    8105, '客户删除', 8101, 4, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:customer:remove', '#',
    103, 1, NOW(), 1, NOW(), '客户删除权限'
);

-- 客户导出权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    8106, '客户导出', 8101, 5, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:customer:export', '#',
    103, 1, NOW(), 1, NOW(), '客户导出权限'
);

-- 客户导入权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    8107, '客户导入', 8101, 6, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:customer:import', '#',
    103, 1, NOW(), 1, NOW(), '客户导入权限'
);

-- =============================================
-- 插入跟进记录管理相关权限
-- =============================================

-- 跟进记录查询权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    8108, '跟进记录查询', 8101, 7, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:follow:query', '#',
    103, 1, NOW(), 1, NOW(), '跟进记录查询权限'
);

-- 跟进记录新增权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    8109, '跟进记录新增', 8101, 8, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:follow:add', '#',
    103, 1, NOW(), 1, NOW(), '跟进记录新增权限'
);

-- 跟进记录修改权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    8110, '跟进记录修改', 8101, 9, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:follow:edit', '#',
    103, 1, NOW(), 1, NOW(), '跟进记录修改权限'
);

-- 跟进记录删除权限
INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    8111, '跟进记录删除', 8101, 10, '#', '', '',
    1, 0, 'F', '1', '1', 'crm:follow:remove', '#',
    103, 1, NOW(), 1, NOW(), '跟进记录删除权限'
);

INSERT INTO `sys_menu` (
    `menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
    `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
 8112, '跟进记录列表', 8101, 11, '#', '', '',
 1, 0, 'F', '1', '1', 'crm:follow:list', '#',
 103, 1, NOW(), 1, NOW(), '跟进记录查询权限'
);

-- =============================================
-- 为超级管理员角色分配CRM菜单权限
-- =============================================

-- 为角色ID=1（超级管理员）分配所有CRM菜单权限
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
(1, 8000),  -- CRM管理
(1, 8100),  -- 客户管理
(1, 8101),  -- 客户列表
(1, 8102),  -- 客户查询
(1, 8103),  -- 客户新增
(1, 8104),  -- 客户修改
(1, 8105),  -- 客户删除
(1, 8106),  -- 客户导出
(1, 8107),  -- 客户导入
(1, 8108),  -- 跟进记录查询
(1, 8109),  -- 跟进记录新增
(1, 8110),  -- 跟进记录修改
(1, 8111),  -- 跟进记录删除
(1, 8112);  -- 跟进记录列表

-- =============================================
-- 验证插入的菜单数据
-- =============================================

-- 查看CRM菜单树结构
SELECT
    m1.menu_id,
    m1.menu_name,
    m1.parent_id,
    m2.menu_name as parent_name,
    m1.order_num,
    m1.path,
    m1.component,
    m1.menu_type,
    m1.perms,
    m1.icon
FROM sys_menu m1
LEFT JOIN sys_menu m2 ON m1.parent_id = m2.menu_id
WHERE m1.menu_id >= 8000 AND m1.menu_id <= 8112
ORDER BY m1.parent_id, m1.order_num;
