-- ----------------------------
-- CRM简化测试数据插入脚本
-- Author: jf
-- Date: 2025-07-15
-- Description: 插入少量CRM测试数据，用于快速功能验证
-- ----------------------------

-- 插入3个简单的客户数据
INSERT INTO `crm_customer` (
    `customer_id`, `customer_code`, `company_name`, `customer_type`, `business_location_count`,
    `country`, `account_email`, `contact_person`, `position`,
    `customer_profile`, `customer_status`, `star_rating`,
    `sales_user_id`, `sales_user_name`, `is_public`,
    `deal_key_points`, `create_dept`, `create_by`, `create_time`
) VALUES
(2001, 'DEMO001', 'Demo Company A', 'retailer', 1, '中国', '<EMAIL>',
 'Zhang San', 'boss', '测试客户A，用于功能验证', 'potential', 3,
 1, 'admin', '0', '测试客户，价格敏感', 103, 1, NOW()),

(2002, 'DEMO002', 'Demo Company B', 'wholesaler', 2, '美国', '<EMAIL>',
 'John Doe', 'purchase_manager', '测试客户B，用于功能验证', 'signed', 4,
 1, 'admin', '0', '测试客户，注重质量', 103, 1, NOW()),

(2003, 'DEMO003', 'Demo Company C', 'distributor', 5, '英国', '<EMAIL>',
 'Jane Smith', 'store_manager', '测试客户C，用于功能验证', 'negotiating', 5,
 1, 'admin', '0', '测试客户，合作意向强', 103, 1, NOW());

-- 插入跟进记录
INSERT INTO `crm_customer_follow_record` (
    `follow_id`, `customer_id`, `follow_date`, `follow_content`, `follow_type`,
    `follow_result`, `follow_user_id`, `follow_user_name`,
    `create_dept`, `create_by`, `create_time`
) VALUES
(20001, 2001, NOW(), '初次联系，了解客户需求', 'phone', '客户有兴趣', 1, 'admin', 103, 1, NOW()),

(20002, 2002, NOW(), '发送产品资料', 'email', '等待反馈', 1, 'admin', 103, 1, NOW()),

(20003, 2003, NOW(), '面谈讨论合作', 'meeting', '进展顺利', 1, 'admin', 103, 1, NOW());

-- 验证数据
SELECT '=== 客户数据 ===' as info;
SELECT customer_code, company_name, customer_type, country, contact_person, customer_status 
FROM crm_customer WHERE customer_code LIKE 'DEMO%';

SELECT '=== 跟进记录 ===' as info;
SELECT cc.customer_code, cfr.follow_date, cfr.follow_type, cfr.follow_result
FROM crm_customer_follow_record cfr
JOIN crm_customer cc ON cfr.customer_id = cc.customer_id
WHERE cc.customer_code LIKE 'DEMO%';
