-- ----------------------------
-- CRM测试数据插入脚本
-- Author: jf
-- Date: 2025-07-15
-- Description: 插入CRM客户信息和跟进记录的测试数据
-- ----------------------------

-- 清空现有测试数据（可选）
-- DELETE FROM crm_customer_follow_record WHERE customer_id IN (SELECT customer_id FROM crm_customer WHERE customer_code LIKE 'TEST%');
-- DELETE FROM crm_customer WHERE customer_code LIKE 'TEST%';

-- =============================================
-- 插入CRM客户测试数据
-- =============================================

INSERT INTO `crm_customer` (
    `customer_id`, `customer_code`, `company_name`, `customer_type`, `business_location_count`,
    `country`, `state`, `zip_code`, `account_email`, `contact_email`,
    `contact_person`, `position`, `customer_profile`, `interested_products`,
    `customer_source`, `customer_status`, `star_rating`, `total_order_count`,
    `total_order_amount`, `first_order_date`, `last_order_date`, `last_follow_time`,
    `sales_user_id`, `sales_user_name`, `is_public`, `single_purchase_amount`,
    `purchase_frequency`, `is_easy_communicate`, `contact_preference`,
    `decision_authority`, `price_sensitivity`, `deal_key_points`,
    `competitor_brands`, `contact_frequency`, `order_frequency`,
    `deal_influence_factors`, `deal_influence_other`, `social_media_account`,
    `social_media_type`, `remark`, `create_dept`, `create_by`, `create_time`
) VALUES
-- 客户1：美国大型分销商
(
    1001, 'TEST001', 'American Retail Solutions Inc.', 'Distributor', 15,
    '美国', '加利福尼亚州', '90210', '<EMAIL>', '<EMAIL>',
    'John Smith', 'PurchaseManager', '大型零售连锁企业，在美国西海岸有15家门店，主要销售消费电子产品，年营业额约500万美元，对产品质量要求很高，价格敏感度中等。',
    '智能手机配件,充电器,数据线,蓝牙耳机', 'TPE', 'potential', 4, 8, 125000.00,
    '2024-03-15 10:30:00', '2024-12-10 14:20:00', '2025-01-10 09:15:00',
    1, 'admin', '0', 15000.00, '每月', '1', 'email,phone',
    '部分决策权', '中等', '产品质量稳定，价格有竞争力，能够提供完善的售后服务，交货期准时。需要提供产品认证证书和质量保证书。',
    'Anker,Belkin,Aukey', '每周', '每月',
    'price,product_quality,after_sales_service', '', '@johnsmith_retail',
    'linkedin', '重要客户，决策周期较长，需要耐心跟进', 103, 1, NOW()
),

-- 客户2：英国现购自运商
(
    1002, 'TEST002', 'UK Electronics Wholesale Ltd.', 'Cash&Carry', 3,
    '英国', '伦敦', 'SW1A 1AA', '<EMAIL>', '<EMAIL>',
    'Emma Wilson', 'Boss', '英国中型电子产品批发商，主要服务欧洲市场，有3个仓库，注重产品创新和时尚设计，对价格比较敏感。',
    '无线充电器,手机壳,平板配件', 'Customer Referral', 'closed_won', 5, 12, 89500.00,
    '2024-01-20 11:45:00', '2024-12-28 16:30:00', '2025-01-08 15:20:00',
    1, 'admin', '0', 7500.00, '每两周', '1', 'whatsapp,email',
    '完全决策权', '高', '价格优势明显，产品款式新颖，包装精美，能够快速响应市场需求。',
    'Spigen,OtterBox,UAG', '每两周', '每两周',
    'price,product_popularity,shipping_time', '包装设计要符合欧洲审美', 'emma.wilson.uk',
    'facebook', '合作愉快的老客户，信任度高', 103, 1, NOW()
),

-- 客户3：德国烟草店
(
    1003, 'TEST003', 'Deutsche Online Handel GmbH', 'SmokeShop', 1,
    '德国', '巴伐利亚州', '80331', '<EMAIL>', '<EMAIL>',
    'Hans Mueller', 'PurchaseManager', '德国专业的在线电子产品零售商，主要通过亚马逊和自有网站销售，对产品认证要求严格，注重环保。',
    '环保充电器,可持续材料手机壳', 'Google Development', 'negotiating', 3, 5, 45000.00,
    '2024-06-10 09:20:00', '2024-11-15 13:45:00', '2024-12-20 10:30:00',
    1, 'admin', '0', 9000.00, '每月', '0', 'email,video_call',
    '部分决策权', '中等', '产品必须符合欧盟CE认证，环保材料优先，包装要求可回收，交货期稳定。',
    'Native Union,Peak Design', '每月', '每季度',
    'product_quality,after_sales_service,inventory_pressure', 'CE认证和环保认证', 'hans.mueller.tech',
    'linkedin', '对认证要求严格，沟通需要更多耐心', 103, 1, NOW()
),

-- 客户4：澳大利亚药房
(
    1004, 'TEST004', 'Aussie Tech Store Pty Ltd', 'Dispensary', 2,
    '澳大利亚', '新南威尔士州', '2000', '<EMAIL>', '<EMAIL>',
    'Sarah Johnson', 'StoreManager', '澳大利亚悉尼的小型科技产品零售店，有2家实体店，主要面向年轻消费者，喜欢时尚潮流产品。',
    '潮流手机壳,创意充电器,游戏配件', 'Social Media', 'potential', 2, 3, 18000.00,
    '2024-08-05 14:15:00', '2024-10-22 11:20:00', '2024-12-15 16:45:00',
    1, 'admin', '0', 6000.00, '不定期', '1', 'instagram,whatsapp',
    '完全决策权', '高', '产品要有创意和时尚感，适合年轻人使用，价格要有竞争力，社交媒体营销支持。',
    'PopSockets,Casetify', '不定期', '不定期',
    'price,product_popularity', '希望有独家款式', '@sarahtechstore',
    'instagram', '年轻老板，喜欢通过社交媒体沟通', 103, 1, NOW()
),

-- 客户5：日本电子烟店
(
    1005, 'TEST005', 'Japan Electronics Distribution Co.', 'VapeShop', 25,
    '日本', '东京都', '100-0001', '<EMAIL>', '<EMAIL>',
    'Takeshi Yamamoto', 'PurchaseManager', '日本最大的电子产品分销商之一，在全国有25个分销点，对产品质量要求极高，注重细节和服务。',
    '高端手机配件,专业充电设备,精密数据线', 'MJBIZ', 'closed_won', 5, 20, 350000.00,
    '2024-02-01 08:30:00', '2025-01-05 10:15:00', '2025-01-12 14:00:00',
    1, 'admin', '0', 17500.00, '每周', '0', 'email,meeting',
    '部分决策权', '低', '产品质量必须完美，包装精美，提供详细的技术文档，售后服务要及时专业。',
    'Elecom,Buffalo,Sanwa', '每周', '每周',
    'product_quality,after_sales_service,shipping_time', '需要日语说明书', 'yamamoto.takeshi.biz',
    'linkedin', '要求很高但很专业的客户，值得长期合作', 103, 1, NOW()
),

-- 客户6：美国连锁烟草店
(
    1006, 'TEST006', 'American Chain Smoke Shops LLC', 'ChainSmokeShops', 50,
    '美国', '德克萨斯州', '75201', '<EMAIL>', '<EMAIL>',
    'Robert Davis', 'Boss', '美国大型连锁烟草店，在全美有50家门店，主要销售烟草和相关配件产品。',
    '电子烟配件,充电设备,展示用品', 'Email Development', 'interested', 4, 15, 180000.00,
    '2024-04-10 12:00:00', '2024-12-20 15:30:00', '2025-01-08 10:20:00',
    1, 'admin', '0', 12000.00, '每月', '1', 'phone,email',
    '完全决策权', '中等', '产品要符合美国FDA标准，包装要专业，需要提供合规证明文件。',
    'JUUL,Vuse,NJOY', '每月', '每月',
    'product_quality,local_purchase,financial_pressure', '需要FDA合规证明', '@robertdavis_smoke',
    'facebook', '连锁店老板，决策快速，注重合规', 103, 1, NOW()
),

-- 客户7：加拿大连锁药房
(
    1007, 'TEST007', 'Canadian Chain Dispensaries Corp', 'ChainDispensaries', 30,
    '加拿大', '不列颠哥伦比亚省', 'V6B 1A1', '<EMAIL>', '<EMAIL>',
    'Jennifer Wilson', 'Co-partner', '加拿大连锁药房，在西部省份有30家门店，主要销售医疗相关产品。',
    '医疗级充电设备,健康监测配件', 'Other Exhibition', 'negotiating', 3, 8, 95000.00,
    '2024-07-15 14:30:00', '2024-11-25 16:45:00', '2024-12-28 11:15:00',
    1, 'admin', '0', 11875.00, '每季度', '1', 'email,whatsapp',
    '部分决策权', '低', '产品要有医疗级认证，质量要求极高，需要提供详细的技术规格。',
    'Philips Healthcare,GE Healthcare', '每月', '每季度',
    'product_quality,after_sales_service,inventory_pressure', '需要Health Canada认证', 'jennifer.wilson.health',
    'linkedin', '医疗行业背景，对产品质量要求很高', 103, 1, NOW()
),

-- 客户8：欧洲电商平台
(
    1008, 'TEST008', 'European E-commerce Solutions SL', 'E-commerce', 1,
    '西班牙', '马德里', '28001', '<EMAIL>', '<EMAIL>',
    'Carlos Rodriguez', 'SalesManager', '欧洲大型电商平台，主要服务欧盟市场，通过多个在线渠道销售电子产品。',
    '时尚手机配件,创新充电产品,智能穿戴配件', 'Acquisition Platform', 'contacted', 2, 0, 0.00,
    NULL, NULL, '2024-12-30 09:45:00',
    1, 'admin', '1', 0.00, '', '1', 'email,video_call',
    '需要上级审批', '高', '产品要有CE认证，包装要多语言，价格要有竞争力，需要支持多币种结算。',
    '', '不定期', '',
    'price,product_popularity,shipping_time', '需要多语言包装', 'carlos.rodriguez.ecommerce',
    'linkedin', '公海客户，刚开始接触，有潜力', 103, 1, NOW()
);

-- =============================================
-- 插入CRM客户跟进记录测试数据
-- =============================================

-- 使用固定的客户ID
SET @customer1_id = 1001;
SET @customer2_id = 1002;
SET @customer3_id = 1003;
SET @customer4_id = 1004;
SET @customer5_id = 1005;

-- 客户1的跟进记录
INSERT INTO `crm_customer_follow_record` (
    `follow_id`, `customer_id`, `follow_date`, `follow_content`, `follow_difficulties`,
    `follow_type`, `follow_result`, `next_follow_date`, `follow_user_id`,
    `follow_user_name`, `remark`, `create_dept`, `create_by`, `create_time`
) VALUES
(10001, @customer1_id, '2025-01-10 09:15:00',
'与John Smith通话，了解了他们Q1的采购计划。他们计划采购智能手机配件约2万美元，主要是无线充电器和蓝牙耳机。客户对我们的产品质量比较满意，但希望价格能够再优惠一些。',
'客户对价格比较敏感，需要与供应商协商更好的价格。同时客户要求提供更多的产品认证文件。',
'phone', '客户有采购意向，需要报价', '2025-01-17 10:00:00', 1, 'admin',
'重要跟进，准备详细报价单', 103, 1, NOW()),

(10002, @customer1_id, '2025-01-05 14:30:00',
'发送了最新的产品目录和价格表给客户，包括了他们感兴趣的无线充电器系列。客户表示会在一周内给出反馈。',
'客户决策周期较长，需要内部讨论。',
'email', '等待客户反馈', '2025-01-12 09:00:00', 1, 'admin',
'已发送产品资料', 103, 1, NOW()),

-- 客户2的跟进记录
(10003, @customer2_id, '2025-01-08 15:20:00',
'通过WhatsApp与Emma联系，确认了上个月的订单已经顺利到货。客户对产品质量很满意，特别是包装设计。讨论了2月份的新品推广计划。',
'客户希望能够获得新品的独家销售权，需要与公司商讨政策。',
'whatsapp', '合作顺利，讨论新品合作', '2025-01-15 14:00:00', 1, 'admin',
'老客户维护，关系良好', 103, 1, NOW()),

(10004, @customer2_id, '2024-12-28 16:30:00',
'年终总结会议，回顾了2024年的合作情况。客户对我们的服务非常满意，2024年总采购额达到8.95万美元，超出预期。',
'无特殊困难',
'video_call', '年度合作总结，客户满意', '2025-01-08 15:00:00', 1, 'admin',
'年终总结，合作愉快', 103, 1, NOW()),

-- 客户3的跟进记录
(10005, @customer3_id, '2024-12-20 10:30:00',
'与Hans进行视频会议，讨论CE认证的相关要求。客户强调所有产品必须符合欧盟标准，并提供了详细的认证清单。',
'CE认证流程复杂，需要额外的时间和成本。客户对环保要求很高，需要寻找符合标准的供应商。',
'video_call', '了解认证要求，准备相关文件', '2025-01-03 09:00:00', 1, 'admin',
'认证要求严格，需要仔细准备', 103, 1, NOW()),

-- 客户4的跟进记录
(10006, @customer4_id, '2024-12-15 16:45:00',
'通过Instagram私信联系Sarah，分享了一些新的潮流产品图片。客户对几款创意手机壳很感兴趣，询问了价格和起订量。',
'客户资金有限，希望起订量能够灵活一些。需要提供更多的产品图片和视频。',
'instagram', '客户有兴趣，需要灵活的订购政策', '2024-12-22 10:00:00', 1, 'admin',
'年轻客户，喜欢社交媒体沟通', 103, 1, NOW()),

-- 客户5的跟进记录
(10007, @customer5_id, '2025-01-12 14:00:00',
'与Yamamoto先生面谈，讨论了2025年的合作计划。客户计划增加高端产品线的采购，预计全年采购额可达50万美元。',
'客户对产品质量要求极高，需要提供更详细的质量保证书和技术规格书。日语文档的准备需要专业翻译。',
'meeting', '大客户，合作前景良好', '2025-01-19 09:00:00', 1, 'admin',
'重要客户，需要高度重视', 103, 1, NOW()),

(10008, @customer5_id, '2025-01-05 10:15:00',
'发送了2025年新品预览和价格表，客户对几款高端充电设备很感兴趣。安排了下周的详细产品演示。',
'需要准备日语版本的产品说明书和技术文档。',
'email', '安排产品演示', '2025-01-12 14:00:00', 1, 'admin',
'准备产品演示材料', 103, 1, NOW());

-- =============================================
-- 验证插入的数据
-- =============================================

-- 查看插入的客户数据
SELECT
    customer_code, company_name, customer_type, country,
    contact_person, customer_status, star_rating,
    social_media_type, social_media_account
FROM crm_customer
WHERE customer_code LIKE 'TEST%'
ORDER BY customer_code;

-- 查看插入的跟进记录数据
SELECT
    cfr.follow_date, cc.customer_code, cc.company_name,
    cfr.follow_type, cfr.follow_result, cfr.next_follow_date
FROM crm_customer_follow_record cfr
JOIN crm_customer cc ON cfr.customer_id = cc.customer_id
WHERE cc.customer_code LIKE 'TEST%'
ORDER BY cfr.follow_date DESC;

-- 统计各客户的跟进次数
SELECT
    cc.customer_code, cc.company_name,
    COUNT(cfr.follow_id) as follow_count,
    MAX(cfr.follow_date) as last_follow_date
FROM crm_customer cc
LEFT JOIN crm_customer_follow_record cfr ON cc.customer_id = cfr.customer_id
WHERE cc.customer_code LIKE 'TEST%'
GROUP BY cc.customer_id, cc.customer_code, cc.company_name
ORDER BY follow_count DESC;
