# CRM测试数据说明

本目录包含CRM系统的测试数据脚本，用于功能验证和演示。

## 文件说明

### 1. insert_test_data.sql
**完整测试数据集**
- 包含5个详细的客户记录
- 包含10条跟进记录
- 涵盖所有客户画像字段
- 包含多种跟进方式和场景

**客户数据包括**：
- TEST001 (ID: 1001): 美国大型零售商（潜在客户）
- TEST002 (ID: 1002): 英国中型批发商（已签约客户）
- TEST003 (ID: 1003): 德国在线零售商（谈判中）
- TEST004 (ID: 1004): 澳大利亚小型零售商（潜在客户）
- TEST005 (ID: 1005): 日本大型分销商（已签约客户）

### 2. simple_test_data.sql
**简化测试数据集**
- 包含3个基本的客户记录
- 包含3条简单跟进记录
- 用于快速功能验证

**客户数据包括**：
- DEMO001 (ID: 2001): 中国零售商（潜在客户）
- DEMO002 (ID: 2002): 美国批发商（已签约客户）
- DEMO003 (ID: 2003): 英国分销商（谈判中）

## 使用方法

### 执行完整测试数据
```bash
mysql -u username -p database_name < server/script/sql/crm/test_data/insert_test_data.sql
```

### 执行简化测试数据
```bash
mysql -u username -p database_name < server/script/sql/crm/test_data/simple_test_data.sql
```

## 测试数据特点

### 客户画像字段覆盖
- ✅ 基本信息：公司名称、联系人、邮箱等
- ✅ 地理信息：国家、州、邮编
- ✅ 业务信息：客户类型、经营地点数量
- ✅ 采购信息：采购频率、单次采购金额
- ✅ 沟通偏好：联系方式偏好、是否好沟通
- ✅ 决策信息：决策权大小、价格敏感度
- ✅ 竞争信息：竞品品牌、成交关键点
- ✅ 订单信息：下单次数、金额、日期
- ✅ 影响因素：成交影响因素
- ✅ 社媒信息：社媒账号、社媒类型

### 跟进记录场景
- 📞 电话跟进：了解采购计划、价格谈判
- 📧 邮件跟进：发送资料、等待反馈
- 🤝 面谈跟进：重要客户会议、合作讨论
- 📱 社交媒体：WhatsApp、Instagram联系
- 🎥 视频会议：年终总结、技术讨论

### 业务场景模拟
- 🎯 不同客户状态：潜在、谈判中、已签约
- 🌍 多国客户：美国、英国、德国、澳大利亚、日本、中国
- 💰 不同规模：大型分销商、中型批发商、小型零售商
- 🔄 完整流程：初次接触→发送资料→谈判→签约→维护

## 清理测试数据

### 清理完整测试数据
```sql
DELETE FROM crm_customer_follow_record WHERE customer_id IN 
(SELECT customer_id FROM crm_customer WHERE customer_code LIKE 'TEST%');
DELETE FROM crm_customer WHERE customer_code LIKE 'TEST%';
```

### 清理简化测试数据
```sql
DELETE FROM crm_customer_follow_record WHERE customer_id IN 
(SELECT customer_id FROM crm_customer WHERE customer_code LIKE 'DEMO%');
DELETE FROM crm_customer WHERE customer_code LIKE 'DEMO%';
```

## 注意事项

1. **执行顺序**：请确保先执行所有迁移脚本，再执行测试数据脚本
2. **数据冲突**：如果客户ID、跟进记录ID或客户编号已存在，会导致插入失败
3. **客户ID**：测试数据使用固定ID（1001-1005, 2001-2003），请确保这些ID未被使用
4. **跟进记录ID**：测试数据使用固定ID（10001-10008, 20001-20003），请确保这些ID未被使用
5. **用户ID**：测试数据使用user_id=1，请确保该用户存在
6. **部门ID**：测试数据使用dept_id=103，请根据实际情况调整

## 验证测试数据

### 查看客户数据
```sql
SELECT customer_code, company_name, customer_type, country, customer_status 
FROM crm_customer 
WHERE customer_code LIKE 'TEST%' OR customer_code LIKE 'DEMO%';
```

### 查看跟进记录
```sql
SELECT cc.customer_code, cfr.follow_date, cfr.follow_type, cfr.follow_result
FROM crm_customer_follow_record cfr
JOIN crm_customer cc ON cfr.customer_id = cc.customer_id
WHERE cc.customer_code LIKE 'TEST%' OR cc.customer_code LIKE 'DEMO%'
ORDER BY cfr.follow_date DESC;
```

### 统计数据
```sql
-- 客户状态分布
SELECT customer_status, COUNT(*) as count 
FROM crm_customer 
WHERE customer_code LIKE 'TEST%' OR customer_code LIKE 'DEMO%'
GROUP BY customer_status;

-- 跟进方式分布
SELECT cfr.follow_type, COUNT(*) as count
FROM crm_customer_follow_record cfr
JOIN crm_customer cc ON cfr.customer_id = cc.customer_id
WHERE cc.customer_code LIKE 'TEST%' OR cc.customer_code LIKE 'DEMO%'
GROUP BY cfr.follow_type;
```
