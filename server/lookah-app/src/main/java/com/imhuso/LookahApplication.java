package com.imhuso;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * Lookah 统一应用启动程序
 * 包含管理后台和商城API
 *
 * <AUTHOR>
 */
@Slf4j
@SpringBootApplication(scanBasePackages = "com.imhuso")
public class LookahApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(LookahApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        log.info("(♥◠‿◠)ﾉﾞ  项目启动成功   ლ(´ڡ`ლ)ﾞ");
    }

}
