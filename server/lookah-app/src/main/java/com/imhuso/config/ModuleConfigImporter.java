package com.imhuso.config;

import com.imhuso.common.core.factory.YmlPropertySourceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * 模块配置导入器
 * <p>
 * 简单使用 @PropertySource 注解自动导入模块配置文件
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@PropertySource(value = "classpath:config/modules.yml", factory = YmlPropertySourceFactory.class, ignoreResourceNotFound = true)
public class ModuleConfigImporter {

    public ModuleConfigImporter() {
        log.info("📁 模块配置已自动加载: config/modules.yml");
    }
}
