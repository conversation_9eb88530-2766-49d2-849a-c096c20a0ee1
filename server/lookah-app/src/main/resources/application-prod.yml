--- # 临时文件存储位置 避免临时文件被系统清理报错
spring.servlet.multipart.location: /lookah/server/temp

--- # 监控中心配置
spring.boot.admin.client:
  # 增加客户端开关
  enabled: true
  url: http://localhost:9090
  instance:
    service-host-type: IP
    metadata:
      username: ${spring.boot.admin.client.username}
      userpassword: ${spring.boot.admin.client.password}
  username: @monitor.username@
  password: @monitor.password@

--- # snail-job 配置
snail-job:
  enabled: true
  # 需要在 SnailJob 后台组管理创建对应名称的组,然后创建任务的时候选择对应的组,才能正确分派任务
  group: "lookah_group"
  # SnailJob 接入验证令牌 详见 script/sql/ry_job.sql `sj_group_config`表
  token: "LK_meyREsenc3hR7ERQCSXMRFZzWEeZydmM"
  server:
    host: snailjob
    port: 17888
  # 命名空间UUID 详见 script/sql/ry_job.sql `sj_namespace`表`unique_id`字段
  namespace: ${spring.profiles.active}
  # 随主应用端口漂移
  port: 2${server.port}
  # 客户端ip指定
  host:
  # RPC类型: netty, grpc
  rpc-type: grpc

--- # 数据源配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: false
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driverClassName: com.mysql.cj.jdbc.Driver
          # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562
          # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能(对数据库有性能损耗 使用批量操作应考虑性能问题)
          url: ******************************************************************************************************************************************************************************************************************************************************
          username: lookah
          password: XPC!eak3tnq8pef@wjt
      #        # 从库数据源
      #        slave:
      #          lazy: true
      #          type: ${spring.datasource.type}
      #          driverClassName: com.mysql.cj.jdbc.Driver
      #          url: **********************************************************************************************************************************************************************************************************************************************************
      #          username:
      #          password:
      #        oracle:
      #          type: ${spring.datasource.type}
      #          driverClassName: oracle.jdbc.OracleDriver
      #          url: *************************************
      #          username: ROOT
      #          password: root
      #        postgres:
      #          type: ${spring.datasource.type}
      #          driverClassName: org.postgresql.Driver
      #          url: ******************************************************************************************************************************************
      #          username: root
      #          password: root
      #        sqlserver:
      #          type: ${spring.datasource.type}
      #          driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
      #          url: *******************************************************************************************************************
      #          username: SA
      #          password: root
      hikari:
        # 最大连接池数量
        maxPoolSize: 20
        # 最小空闲线程数量
        minIdle: 10
        # 配置获取连接等待超时的时间
        connectionTimeout: 30000
        # 校验超时时间
        validationTimeout: 5000
        # 空闲连接存活最大时间，默认10分钟
        idleTimeout: 600000
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
        maxLifetime: 1800000
        # 多久检查一次连接的活性
        keepaliveTime: 30000

--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring.data:
  redis:
    # 地址
    host: redis
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # redis 密码必须配置
    password: XPC!eak3tnq8pef@wjt
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl.enabled: false

# redisson 配置
redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 16
  # Netty线程池数量
  nettyThreads: 32
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${lookah.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 32
    # 连接池大小
    connectionPoolSize: 64
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

--- # mail 邮件发送
mail:
  enabled: true
  host: smtp.mandrillapp.com
  port: 587
  # 是否需要用户名密码验证
  auth: true
  # 发送方，遵循RFC-822标准
  from: Lookah Distribution<<EMAIL>>
  # 用户名（注意：如果使用foxmail邮箱，此处user为qq号）
  user: <EMAIL>
  # 密码（注意，某些邮箱需要为SMTP服务单独设置密码，详情查看相关帮助）
  pass: mUFXVrZMpCH6362MTO4S3A
  # 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。
  starttlsEnable: true
  # 使用SSL安全连接
  sslEnable: false
  # SMTP超时时长，单位毫秒，缺省值不超时
  timeout: 0
  # Socket连接超时值，单位毫秒，缺省值不超时
  connectionTimeout: 0


# 企业微信配置 - 生产环境（基础技术配置）
wecom:
  enabled: true
  connect-timeout: 10000  # 连接超时时间（毫秒）
  read-timeout: 10000     # 读取超时时间（毫秒）
  # 注意：webhook密钥现在通过数据库sys_config表管理，提供更好的安全性和灵活性

# Wholesale模块业务配置 - 生产环境
wholesale:
  # 🧾 发票配置
  invoice:
    initial-id: 30000  # 发票号初始值
  # 📦 库存配置
  stock:
    notify:
      threshold: 10  # 库存到货通知阈值

--- # ERP系统集成配置 - 生产环境
lookah:
  erp:
    # 是否启用ERP集成
    enabled: true

    # ERP系统类型
    type: LOOKAH

    # API基础地址 (生产环境)
    base-url: https://erp-api.lookah.com

    # 应用标识符
    app-key: lookah_app_7f3e9d2a8b5c1k4m

    # 应用密钥 (请妥善保管)
    app-secret: sk_9x8w7v6u5t4s3r2q1p0o9n8m7l6k5j4i3h2g1f0e9d8c7b6a5z4y3x2w1v0u

    # 是否为内部系统 (内部系统享有更高权限和免流量限制)
    internal: true

    # 连接超时时间(毫秒)
    connect-timeout: 30000

    # 读取超时时间(毫秒)
    read-timeout: 60000

    # 重试次数
    retry-count: 3

    # 重试间隔(毫秒)
    retry-interval: 1000

    # 批量查询最大数量 (与开发环境保持一致)
    batch-size: 500

    # Webhook配置
    webhook:
      # 是否启用Webhook
      enabled: true

      # 签名验证是否启用
      signature-verification: true
