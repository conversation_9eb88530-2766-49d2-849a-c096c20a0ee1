# 🏗️ 模块化配置文件
#
# 📖 配置说明：
# - prefix: API前缀路径（如 /api/admin）
# - security-module: 安全模块名（admin=管理员权限, store=用户权限）
# - auto-prefix: 自动为控制器添加API前缀的智能配置
#   - packages: 精确包名映射（优先级高）
#   - rules: 包名关键字规则（优先级低）
# - exclude-paths: 无需身份验证的公开接口列表
# - description: 模块功能描述
#
# 📚 详细文档: docs/模块化配置详细说明.md
# 💡 注意：JWT配置使用传统方式，在application.yml的sa-token.jwt-secret-keys中配置

modules:
  # 动态模块配置 - 支持任意数量的模块
  configs:
    # 📋 通用排除路径（所有安全模块共享的公开路径）
    common:
      exclude-paths:
        # 静态资源文件
        - /*.html # 根目录HTML文件
        - /**/*.html # 所有子目录HTML文件
        - /**/*.css # CSS样式文件
        - /**/*.js # JavaScript文件
        - /favicon.ico # 网站图标
        # 系统接口
        - /error # 错误页面
        - /*/api-docs # API文档
        - /*/api-docs/** # API文档子路径
        - /actuator/** # Spring Boot监控端点
        - /static/** # 静态资源目录
        # 基础设施服务（SSE、WebSocket、文件服务等）
        - /resource/** # 所有基础设施服务路径
      description: 所有模块共享的公开路径，无需身份验证

    # 🔐 Admin系统管理模块 - 后台管理功能
    admin:
      # API前缀：所有admin控制器的URL都会自动添加此前缀
      prefix: /admin
      # 安全模块：指定使用哪个安全配置（admin/store）
      security-module: admin
      # 🤖 自动API前缀配置 - 智能为控制器添加前缀
      auto-prefix:
        # 是否启用自动前缀功能
        enabled: true
        # 📦 精确包名映射：指定包名对应的API前缀
        # 格式：完整包名: API前缀
        packages:
          com.imhuso.system.controller: /admin # 系统管理控制器
          com.imhuso.admin.controller: /admin # 管理员控制器
          com.imhuso.crm.controller.admin: /admin # CRM管理控制器
        # 🏷️ 包名关键字规则：根据包名中的关键字自动匹配前缀
        # 格式：关键字: API前缀
        # 匹配规则：*.controller.{关键字}.* 或 *.controller.{关键字}
        rules:
          admin: /admin # 包含admin的控制器包
          system: /admin # 包含system的控制器包
          crm: /admin # 包含crm的控制器包
      # 🚫 排除路径：此模块中无需身份验证的公开接口
      exclude-paths: [ ] # Admin模块暂无公开接口，所有接口都需要管理员权限
      description: 系统管理后台API - 需要管理员权限访问

    # 🔧 系统模块（可选）
    system:
      prefix: /system
      security-module: admin
      exclude-paths: [ ]
      description: 系统功能模块

    # 🏪 Wholesale批发业务前端模块 - 面向批发商的购买功能
    wholesale_front:
      # API前缀：批发前端接口使用/api/wholesale前缀
      prefix: /api/wholesale
      # 安全模块：使用wholesale自定义安全配置
      security-module: wholesale
      # 🤖 自动API前缀配置
      auto-prefix:
        enabled: true
        # 📦 精确包名映射
        packages:
          com.imhuso.wholesale.controller.front: /api/wholesale # 批发前端控制器
        # 🏷️ 包名关键字规则
        rules:
          wholesale.front: /api/wholesale # 包含wholesale.front的控制器包
      # 🌐 排除路径：无需登录即可访问的公开接口
      exclude-paths:
        # 🔐 认证相关接口（批发商注册登录）
        - /api/wholesale/auth/** # 所有认证接口
        - /api/wholesale/member/register # 批发商注册
        - /api/wholesale/member/login # 批发商登录
      description: 批发业务前端API - 面向批发商，支持批量采购和订单管理

    # 🏪 Wholesale批发业务后端管理模块 - 批发业务管理员功能
    wholesale_admin:
      # API前缀：批发管理接口使用/admin/wholesale前缀
      prefix: /admin/wholesale
      # 安全模块：使用admin安全配置（管理员权限验证）
      security-module: admin
      # 🤖 自动API前缀配置
      auto-prefix:
        enabled: true
        # 📦 精确包名映射
        packages:
          com.imhuso.wholesale.controller.admin: /admin/wholesale # 批发后台管理控制器
        # 🏷️ 包名关键字规则
        rules:
          wholesale.admin: /admin/wholesale # 包含wholesale.admin的控制器包
      # 🚫 排除路径：批发管理功能都需要管理员权限
      exclude-paths: [ ] # 无公开接口，所有功能都需要管理员登录
      description: 批发后台管理API - 批发商管理、订单管理、库存管理等功能

    # 🛒 Mall商城业务前端模块 - 面向普通消费者的购物功能（默认API前端）
    mall_front:
      # API前缀：商城前端接口使用/api前缀（作为默认API前端）
      prefix: /api
      # 安全模块：使用mall自定义安全配置
      security-module: mall
      # 🤖 自动API前缀配置
      auto-prefix:
        enabled: true
        # 📦 精确包名映射
        packages:
          com.imhuso.mall.controller.front: /api # 商城前端控制器
        # 🏷️ 包名关键字规则
        rules:
          mall.front: /api # 包含mall.front的控制器包
      # 🌐 排除路径：无需登录即可访问的公开接口
      exclude-paths:
        # 🔐 认证相关接口（会员注册登录）
        - /api/auth/** # 所有认证接口
        - /api/member/register # 会员注册
        - /api/member/login # 会员登录
        # 🛍️ 公开商品浏览接口（游客可访问）
        - /api/product/** # 商品列表、详情、搜索
        - /api/category/** # 分类浏览
        - /api/collection/** # 商品集合浏览
      description: 商城业务前端API - 面向普通消费者，支持商品浏览、购物车、订单管理（默认API前端）

    # 🛒 Mall商城业务后端管理模块 - 商城业务管理员功能
    mall_admin:
      # API前缀：商城管理接口使用/admin/mall前缀
      prefix: /admin/mall
      # 安全模块：使用admin安全配置（管理员权限验证）
      security-module: admin
      # 🤖 自动API前缀配置
      auto-prefix:
        enabled: true
        # 📦 精确包名映射
        packages:
          com.imhuso.mall.controller.admin: /admin/mall # 商城后台管理控制器
        # 🏷️ 包名关键字规则
        rules:
          mall.admin: /admin/mall # 包含mall.admin的控制器包
      # 🚫 排除路径：商城管理功能都需要管理员权限
      exclude-paths: [ ] # 无公开接口，所有功能都需要管理员登录
      description: 商城后台管理API - 商品管理、订单管理、会员管理、促销管理等功能
