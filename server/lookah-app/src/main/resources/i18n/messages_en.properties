# 新增字段的英文国际化消息

# ERP System Integration Messages
erp.integration.disabled=ERP integration is disabled
erp.provider.not.found=No available ERP provider found
erp.provider.none.available=No provider available
erp.connection.failed=ERP connection failed
erp.stock.sync.invalid.data=Invalid ERP stock data
erp.stock.fetch.failed=Failed to fetch ERP stock data
erp.provider.type.empty=ERP provider type is empty
erp.provider.duplicate.type=Duplicate ERP provider type found
lookah.erp.unavailable=Lookah ERP system is unavailable

# 订单状态
wholesale.order.status.draft=Draft
wholesale.order.status.pending=Pending
wholesale.order.status.processing=Processing
wholesale.order.status.completed=Completed
wholesale.order.status.canceled=Canceled

# 付款状态
wholesale.order.payment.status.pending=Unpaid
wholesale.order.payment.status.partial_paid=Partially Paid
wholesale.order.payment.status.paid=Paid

# 发货状态
wholesale.order.shipment.status.pending=Pending Shipment
wholesale.order.shipment.status.preparing=Preparing
wholesale.order.shipment.status.partial_shipped=Partially Shipped
wholesale.order.shipment.status.shipped=Shipped
wholesale.order.shipment.status.delivered=Delivered

# 发票状态
wholesale.order.invoice.status.pending=Pending
wholesale.order.invoice.status.sent=Sent

# 发货审批状态
wholesale.shipment.approval.status.no_approval=No Approval Required
wholesale.shipment.approval.status.no_approval_enabled=Pending Request
wholesale.shipment.approval.status.pending=Pending Approval
wholesale.shipment.approval.status.approved=Approved
wholesale.shipment.approval.status.rejected=Rejected
wholesale.shipment.approval.status.cancelled=Cancelled

# 会员管理新增字段
wholesale.admin.member.company.type.not.blank=Company type is required
wholesale.admin.member.store.count.not.null=Store count is required
wholesale.admin.member.store.count.min=Store count must be greater than 0
wholesale.admin.member.customer.source.length=Customer source cannot exceed {max} characters
wholesale.admin.member.company.name.length=Company name cannot exceed {max} characters

# 会员公司类型
wholesale.member.company.type.smoke_shop=Smoke Shop
wholesale.member.company.type.chain_stores=Chain Stores
wholesale.member.company.type.cash_carry=Cash & Carry
wholesale.member.company.type.distributor=Distributor
wholesale.member.company.type.vip=VIP
wholesale.member.company.type.dispensary=Dispensary
wholesale.member.company.type.chain_dispensaries=Chain Dispensaries
wholesale.member.company.type.others=Others

# 客户来源
wholesale.member.source.champs_show=Champs Show
wholesale.member.source.tpe=TPE
wholesale.member.source.mjbiz=MJBIZ
wholesale.member.source.website=Website
wholesale.member.source.others=Others

# 订单明细新增字段
wholesale.admin.order.item.purchase.price.not.null=Purchase price is required
wholesale.admin.order.item.purchase.price.min=Purchase price must be greater than 0
wholesale.admin.order.item.sales.price.not.null=Sales price is required
wholesale.admin.order.item.sales.price.min=Sales price must be greater than 0

# 运费信息管理
wholesale.admin.freight.id.not.null=Freight ID is required
wholesale.admin.freight.order.id.not.null=Order ID is required
wholesale.admin.freight.estimated.freight.min=Estimated freight must be greater than or equal to 0
wholesale.admin.freight.actual.freight.min=Actual freight must be greater than or equal to 0
wholesale.admin.freight.currency.not.blank=Freight currency is required
wholesale.admin.freight.sync.success=Freight information synchronized successfully
wholesale.admin.freight.sync.failed=Failed to synchronize freight information
wholesale.admin.freight.not.found=Freight information not found

# 运费同步状态
wholesale.freight.sync.status.pending=Not Synchronized
wholesale.freight.sync.status.synced=Synchronized

# 昊通运费查询
wholesale.haotong.freight.query.success=Freight query successful
wholesale.haotong.freight.query.failed=Failed to query freight information
wholesale.haotong.freight.order.tracking.empty=Order number and tracking number cannot both be empty
wholesale.haotong.freight.api.error=API error occurred while querying freight information
wholesale.haotong.freight.response.invalid=Invalid response format from freight query API

# 权限相关
wholesale.permission.international.sales=International Sales Team
wholesale.permission.member.extended.fields=Member Extended Fields Management
wholesale.permission.order.freight=Order Freight Management
wholesale.permission.order.extended.fields=Order Extended Fields Management

# 订单折扣验证
wholesale.admin.order.discount.amount.min=Discount amount cannot be less than 0
wholesale.admin.order.discount.amount.exceeds.total=Discount amount cannot exceed order total
wholesale.order.discount.exceeds.total=Discount amount cannot exceed order total. Order total: ${total}, Discount: ${discount}
wholesale.order.discount.min.zero=Discount amount cannot be less than 0
wholesale.order.discount.exceeds.subtotal=Discount amount cannot exceed order subtotal ${subtotal}
wholesale.order.discount.format.error=Invalid discount amount format, reset to 0

# Custom field validation messages
wholesale.member.custom.company.type.required=Custom business type is required when selecting "Others"
wholesale.member.custom.company.type.too.long=Custom business type cannot exceed 100 characters
wholesale.member.custom.source.required=Custom source is required when selecting "Others"
wholesale.member.custom.source.too.long=Custom source cannot exceed 100 characters
wholesale.member.email.already.exists=This email address is already in use by another customer
