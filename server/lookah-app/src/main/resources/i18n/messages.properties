# Default messages (English)
not.null=* Required field
length.not.valid=Length must be between {min} and {max} characters
repeat.submit.message=Duplicate submission not allowed. Please try again later
rate.limiter.message=Too many requests. Please try again later

# CRM related messages
crm.customer.code.not.unique=Customer code already exists
crm.customer.account.email.not.unique=Account email already exists
# User related messages
## Captcha
user.captcha.error=Invalid captcha code
user.captcha.expire=Captcha has expired
## SMS and Email verification
sms.code.not.blank=SMS verification code is required
sms.code.retry.limit.count=Incorrect SMS code. {0} failed attempts
sms.code.retry.limit.exceed=Too many failed SMS verification attempts. Account locked for {1} minutes
email.code.not.blank=Email verification code is required
email.code.retry.limit.count=Incorrect email code. {0} failed attempts
email.code.retry.limit.exceed=Too many failed email verification attempts. Account locked for {1} minutes
## User accounts
user.not.exists=Invalid username or password
user.password.not.match=Invalid username or password
user.password.retry.limit.count=Invalid username or password
user.password.retry.limit.exceed=Too many failed login attempts. Account locked for {1} minutes
user.password.delete=Invalid username or password
user.blocked=Invalid username or password
user.logout.success=Successfully logged out
user.login.success=Login successful
user.notfound=Session expired. Please log in again
user.forcelogout=You have been logged out by an administrator. Please log in again
user.unknown.error=An unexpected error occurred. Please log in again
## User field validation
user.username.not.blank=Username is required
user.username.not.valid=Username must be 2-20 characters (letters, numbers, underscores) and cannot start with a number
user.username.length.valid=Username must be between {min} and {max} characters
user.password.not.blank=Password is required
user.password.length.valid=Password must be between {min} and {max} characters
user.password.not.valid=Password must be 5-50 characters
user.email.not.valid=Invalid email format
user.email.not.blank=Email address is required
user.phonenumber.not.blank=Phone number is required
user.mobile.phone.number.not.valid=Invalid phone number format
## User registration
user.register.success=Registration successful
user.register.save.error=Registration failed. User {0} already exists
user.register.error=Registration failed. Please contact system administrator
# Role messages
role.blocked=Role has been disabled. Please contact your administrator
# Authentication messages
auth.grant.type.error=Invalid authentication type
auth.grant.type.blocked=Authentication type is disabled
auth.grant.type.not.blank=Authentication type is required
auth.clientid.not.blank=Client ID is required
# Third-party login
xcx.code.not.blank=Mini program code is required
social.source.not.blank=Social login platform is required
social.code.not.blank=Social login code is required
social.state.not.blank=Social login state is required
# Store module messages
wholesale.member.invalid.credentials=Invalid username or password
wholesale.member.confirm.password.not.blank=Password confirmation is required
wholesale.member.nick.name.size=Nickname cannot exceed 50 characters
wholesale.member.info.not.found=User information not found. Please login again
wholesale.member.info.get.failed=Failed to retrieve user information. Please try again later
wholesale.member.register.success=Registration successful
wholesale.member.register.failed=Registration failed. Please try again
wholesale.member.create.failed=Failed to create user account
wholesale.member.logout.success=Successfully logged out
# Store social authentication messages
wholesale.auth.social.provider.required=Social provider is required
wholesale.auth.social.provider.not.supported=Social provider {0} is not supported
wholesale.auth.social.credential.required=Social credential or authorization code is required
wholesale.auth.social.jwt.not.supported=JWT authentication is not supported for provider {0}
wholesale.auth.social.jwt.invalid=Invalid JWT token
wholesale.auth.social.jwt.expired=JWT token has expired
wholesale.auth.social.code.invalid=Invalid authorization code for provider {0}
wholesale.auth.social.email.required=Email address is required for social login
wholesale.auth.social.login.failed=Social login failed for provider {0}
wholesale.auth.social.auth.url.generation.failed=Failed to generate authorization URL for provider {0}
wholesale.auth.social.request.required=Social authentication request is required
wholesale.auth.social.redirect.uri.required=Redirect URI is required
# File upload messages
upload.exceed.maxSize=File size exceeds limit. Maximum allowed size: {0}MB
upload.filename.exceed.length=Filename cannot exceed {0} characters
# Permission messages
no.permission=Access denied. Please contact administrator for permissions [{0}]
no.create.permission=Create permission denied. Please contact administrator for permissions [{0}]
no.update.permission=Update permission denied. Please contact administrator for permissions [{0}]
no.delete.permission=Delete permission denied. Please contact administrator for permissions [{0}]
no.export.permission=Export permission denied. Please contact administrator for permissions [{0}]
no.view.permission=View permission denied. Please contact administrator for permissions [{0}]
# Global request messages
request.validation.failed=Parameter validation failed
# Response messages
response.success=Success
response.failed=Failed
response.warn=Warning
# Sa-Token authentication messages
auth.permission.denied=Access denied. Please contact administrator for authorization
auth.role.denied=Access denied. Please contact administrator for role permission
auth.not.login=Authentication failed. Please login to access system resources
auth.client.id.required=Client ID is required
# ===== Mall Module Messages =====
# Mall authentication messages
mall.auth.client.id.invalid=Invalid client ID
mall.auth.grant.type.invalid=Invalid authentication type
mall.auth.email.already.exists=Email address already exists
mall.auth.phone.already.exists=Phone number already exists
mall.auth.email.already.registered=Email address already registered
mall.auth.phone.already.registered=Phone number already registered
mall.auth.registration.failed=Registration failed
mall.auth.invalid.email.or.password=Invalid email or password
mall.auth.account.disabled=Account has been disabled
mall.auth.account.locked=Account is locked, please try again later
mall.auth.login.successful=Login successful
mall.auth.logout.successful=Logout successful
mall.auth.registration.successful=Registration successful

# Mall validation messages
mall.validation.email.required=Email address cannot be empty
mall.validation.email.invalid=Invalid email format
mall.validation.email.max.length=Email address length cannot exceed {0} characters
mall.validation.password.required=Password cannot be empty
mall.validation.password.length=Password length must be between {0}-{1} characters
mall.validation.password.pattern=Password must contain uppercase, lowercase letters and numbers
mall.validation.confirm.password.required=Confirm password cannot be empty
mall.validation.passwords.not.match=Passwords do not match
mall.validation.client.id.required=Client ID cannot be empty
mall.validation.terms.required=Must agree to terms of service
mall.validation.privacy.required=Must agree to privacy policy
mall.validation.full.name.max.length=Full name length cannot exceed {0} characters
mall.validation.first.name.max.length=First name length cannot exceed {0} characters
mall.validation.last.name.max.length=Last name length cannot exceed {0} characters
mall.validation.gender.invalid=Gender value must be 0, 1 or 2
mall.validation.member.id.required=Member ID cannot be empty
mall.validation.avatar.max.length=Avatar URL length cannot exceed {0} characters
mall.validation.birthday.past=Birthday must be a past date
mall.validation.status.invalid=Status value must be 0, 1 or 2
mall.validation.email.verified.invalid=Email verification status must be 0 or 1
mall.validation.register.source.invalid=Invalid registration source
mall.validation.login.count.min=Login count cannot be negative
mall.validation.failed.login.count.min=Failed login count cannot be negative
mall.validation.old.password.required=Old password cannot be empty
mall.validation.new.password.required=New password cannot be empty

# Mall social login messages
mall.social.login.failed=Social login failed: {0}
mall.social.user.info.failed=Failed to get social user information
mall.auth.client.token.mismatch=Client ID does not match token
mall.security.too.many.failures=Too many login failures

# Mall captcha messages
mall.captcha.invalid.or.expired=Invalid or expired captcha
mall.captcha.generation.failed=Failed to generate captcha
mall.captcha.too.frequent=Too frequent, please try again later
mall.captcha.verification.code.sent=Verification code sent
mall.captcha.send.failed=Failed to send
mall.captcha.required=Captcha required
mall.captcha.not.required=No captcha required
mall.captcha.config.error=Captcha configuration error

# Mall security messages
mall.security.too.many.attempts=Too many login attempts, please try again later
mall.security.account.locked=Account locked
mall.security.invalid.password=Invalid password

# Mall member messages
mall.member.email.available=Email available
mall.member.email.already.registered=Email already registered
mall.member.logged.in=Logged in
mall.member.not.logged.in=Not logged in
mall.member.create.failed=Failed to create member
mall.member.info.not.exists=Member information does not exist
mall.member.account.disabled=Account has been disabled

# Mall social login messages
mall.social.account.already.bound=This social account is already bound to another user
mall.social.auth.url.failed=Failed to get social login authorization URL
mall.social.login.failed=Social login failed: {0}
mall.social.user.info.failed=Failed to get social user information
mall.member.info.not.found=User information not found
mall.member.update.success=Profile updated successfully
mall.member.update.failed=Failed to update profile
mall.member.password.not.match=Incorrect password
mall.member.password.confirm.not.match=Password confirmation does not match
mall.member.password.change.success=Password changed successfully
mall.member.password.change.failed=Failed to change password
mall.member.email.verify.success=Email verified successfully
mall.member.avatar.upload.success=Avatar uploaded successfully
mall.member.avatar.upload.failed=Failed to upload avatar
mall.member.avatar.url.empty=Avatar URL cannot be empty
mall.member.deactivate.success=Account deactivated successfully
mall.member.deactivate.failed=Failed to deactivate account
# Mall captcha and verification messages
mall.captcha.email.code.sent=Email verification code sent
mall.captcha.email.code.send.failed=Failed to send email verification code

# Mall social login messages
mall.social.invalid.client.id=Invalid client ID
mall.social.auth.url.failed=Failed to get authorization URL
mall.social.auth.url.success=Authorization URL retrieved successfully
mall.social.login.failed=Social login failed
mall.social.login.success=Social login successful
mall.social.account.already.bound=Social account already bound
mall.social.account.bind.success=Social account bound successfully
mall.social.account.unbind.success=Social account unbound successfully
mall.social.account.not.bound=Social account not bound
mall.social.set.primary.success=Primary account set successfully
mall.social.bind.success=Social account bound successfully
mall.social.bind.failed=Failed to bind social account
mall.social.unbind.success=Social account unbound successfully
mall.social.unbind.failed=Failed to unbind social account
mall.social.set.primary.failed=Failed to set primary account
mall.social.account.bound=Account is bound
mall.social.account.not.bound=Account is not bound

# Mall captcha messages
mall.captcha.required=Captcha verification required
mall.captcha.not.required=Captcha verification not required

# ===== Wholesale Module Messages =====
# \u679A\u4E3E\u503C
## \u5305\u88C5\u7C7B\u578B
# \u7CFB\u7EDF\u7EA7\u9519\u8BEF\u6D88\u606F
system.error=Sorry, an unexpected error occurred. Please try again later.
request.path.variable.missing=Missing required path variable: {0}
request.argument.type.mismatch=Invalid parameter
request.body.missing=Request body is missing
request.method.not.supported=Request method not supported
request.uri.not.found=The requested resource was not found
request.runtime.error=An unexpected error occurred
request.server.error=Internal server error

# Wholesale order force complete messages
wholesale.order.force.complete.reason.required=Force completion reason is required
wholesale.order.force.complete.canceled.not.allowed=Canceled orders cannot be force completed
wholesale.order.force.complete.not.processing=Only processing orders can be force completed
wholesale.order.force.complete.not.shipped=Only orders that have been shipped can be force completed
wholesale.order.force.complete.success=Order force completed successfully

# \u4F1A\u5458\u57FA\u7840\u6D88\u606F
wholesale.member.not.exists=Sorry, this account does not exist
wholesale.member.password.not.match=Incorrect password, please try again
wholesale.member.blocked=Your account has been suspended, please contact customer service
wholesale.member.register.exist=The email {0} has already been registered
wholesale.member.register.error=Registration failed, please try again
wholesale.member.password.change.error=Failed to change password, please try again
wholesale.member.old.password.not.blank=Current password is required
wholesale.member.new.password.not.blank=New password is required
wholesale.member.profile.update.error=Unable to update account information
wholesale.member.first.name.size=First name cannot exceed {max} characters
wholesale.member.last.name.size=Last name cannot exceed {max} characters
wholesale.member.first.name.not.blank=First name is required
wholesale.member.last.name.not.blank=Last name is required
# \u6570\u636E\u9A8C\u8BC1\u6D88\u606F
wholesale.member.email.not.blank=Please enter your email address
wholesale.member.email.format=Please enter a valid email address
wholesale.member.password.size=Password must be between {min} and {max} characters
wholesale.member.password.pattern=Password must contain both letters and numbers
wholesale.member.nickname.size=Nickname cannot exceed {max} characters
# \u4F1A\u5458\u767B\u5F55\u6D88\u606F
wholesale.member.username.not.blank=Email address cannot be empty
wholesale.member.email.not.valid=Please enter a valid email address
wholesale.member.password.not.blank=Password cannot be empty
wholesale.member.password.length=Password length must be between 6 and 20 characters
wholesale.member.client.id.not.blank=Client ID cannot be empty
# \u4F1A\u5458\u4FE1\u606F\u6D88\u606F
wholesale.member.info.get.error=Failed to get member information
wholesale.member.info.update.error=Failed to update member information
wholesale.member.not.login=Please sign in to access this feature
wholesale.member.username.empty=Username is empty
# \u5BA2\u6237\u7AEF\u6D88\u606F
wholesale.member.client.not.exists=Invalid client ID
wholesale.member.client.blocked=Client has been disabled
wholesale.member.client.not.match=Client ID does not match with token
# \u5BA2\u6237\u6743\u9650\u6D88\u606F
wholesale.member.permission.no.order=You do not have permission to place orders. Please contact customer service.
wholesale.member.permission.no.price=Price information is not available for your account.
wholesale.member.permission.no.stock=Stock information is not available for your account.
# \u4EA7\u54C1\u5206\u7C7B\u6D88\u606F
wholesale.product.category.not.exists=Category does not exist
wholesale.product.category.blocked=Category has been disabled
wholesale.product.category.list.error=Failed to get category list
wholesale.product.category.name.not.blank=Category name cannot be empty
wholesale.product.category.parent.not.exists=Parent category does not exist
wholesale.product.category.parent.error=Cannot set itself or child as parent category
wholesale.product.category.delete.has.child=Cannot delete category with sub-categories
wholesale.product.category.delete.has.product=Cannot delete category with products
# \u4EA7\u54C1\u9A8C\u8BC1\u6D88\u606F
wholesale.product.id.not.null=Product ID is required
wholesale.variant.id.not.null=Product variant ID is required
wholesale.product.ids.not.empty=Please select at least one product
wholesale.product.quantity.not.null=Please enter quantity
wholesale.product.quantity.min=Quantity must be greater than 0
# \u4EA7\u54C1\u72B6\u6001\u6D88\u606F
wholesale.product.not.exists=Sorry, this product does not exist
wholesale.product.not.available=Sorry, this product is currently unavailable
wholesale.product.stock.insufficient=Insufficient stock for {0}
wholesale.product.stock.insufficient.no.permission=Insufficient stock for {0}
wholesale.product.stock.insufficient.multiple=Some products have insufficient stock: {0}
wholesale.product.not.exists.with.name=Sorry, product "{0}" does not exist
wholesale.product.not.available.with.name=Sorry, product "{0}" is currently unavailable
wholesale.product.variant.not.exists.with.name=Sorry, the selected option for "{0}" is not available
wholesale.product.variant.not.available.with.name=Sorry, the selected option for "{0}" is currently unavailable
wholesale.product.stock.not.enough.with.name=Insufficient stock for product "{0}"
wholesale.product.stock.not.enough=Sorry, the requested quantity is not available in stock
wholesale.product.variant.not.exists.batch=Sorry, one or more selected products are not available
wholesale.product.not.exists.batch=Sorry, one or more products in your cart are no longer available
# SKU\u76F8\u5173\u6D88\u606F
wholesale.sku.not.exists=Sorry, this product option is not available
wholesale.sku.not.available=Sorry, this product option is currently unavailable
wholesale.sku.product.mismatch=The selected option does not belong to this product
wholesale.sku.code.duplicated=This product code already exists
wholesale.sku.id.not.null=Product variant ID is required
wholesale.sku.product.id.not.null=Product ID is required for this variant
wholesale.sku.code.not.blank=SKU code is required
wholesale.sku.price.not.null=Price is required for this variant
wholesale.sku.price.min=Price must be greater than 0
wholesale.sku.stock.not.null=Stock quantity is required for this variant
wholesale.sku.stock.min=Stock quantity must be greater than or equal to 0
# \u5230\u8D27\u901A\u77E5\u9A8C\u8BC1\u6D88\u606F
wholesale.notify.email.not.blank=Email address cannot be empty
wholesale.notify.email.not.valid=Please enter a valid email address
wholesale.notify.already.subscribed=You have already subscribed to this product variant's stock notification
wholesale.notify.not.exists=Stock notification does not exist
# \u5230\u8D27\u901A\u77E5\u90AE\u4EF6\u6D88\u606F
wholesale.notify.email.subject=Stock Notification Subscription Confirmation
wholesale.notify.email.content=Thank you for subscribing to stock notifications for {0}. We will notify you as soon as the product is back in stock.
wholesale.notify.admin.email.subject=New Stock Notification Subscription
wholesale.notify.admin.email.content=A customer has subscribed to stock notifications for {0}.\n\nCustomer Email: {1}\nWhatsApp: {2}
# \u8D2D\u7269\u8F66\u9A8C\u8BC1\u6D88\u606F
wholesale.cart.items.not.empty=Please add at least one item to cart
wholesale.cart.item.quantity.exceed=The quantity exceeds the maximum limit of 999 per item
wholesale.cart.max.items.exceeded=Your cart has reached the maximum limit of 500 items
wholesale.cart.item.not.available=Some items in your cart are no longer available
wholesale.cart.not.exists=The item does not exist in your cart
wholesale.cart.quantity.not.null=Please enter quantity
wholesale.cart.quantity.min=Quantity must be greater than 0
wholesale.cart.product.id.not.null=Please select a product
wholesale.cart.variant.id.not.null=Please select product options
wholesale.cart.empty=Your cart is empty
wholesale.cart.batch.operation.failed=Sorry, we couldn't update your cart. Please try again
# \u8BA2\u5355\u76F8\u5173\u6D88\u606F
wholesale.order.create.error=Failed to create order, please try again later.
wholesale.order.items.not.empty=Please select at least one product
wholesale.order.product.id.not.null=Please select a product
wholesale.order.quantity.not.null=Please enter quantity
wholesale.order.quantity.min=Quantity must be greater than 0
wholesale.order.not.exists=Order does not exist
wholesale.order.status.error=Invalid order status
wholesale.order.create.success=Order created successfully
wholesale.order.cancel.success=Order cancelled successfully
wholesale.order.remark.size=Remark cannot exceed {max} characters
# \u7ED3\u8D26\u76F8\u5173\u6D88\u606F
wholesale.address.default.not.exists=Please add a default shipping address before checkout
# \u5730\u5740\u76F8\u5173\u6D88\u606F
wholesale.address.not.exists=The shipping address does not exist
wholesale.address.first.name.not.blank=First name is required
wholesale.address.last.name.not.blank=Last name is required
wholesale.address.phone.not.blank=Phone number is required
wholesale.address.phone.not.valid=Invalid phone number format
wholesale.address.email.not.blank=Email address is required
wholesale.address.email.not.valid=Invalid email address format
wholesale.address.country.not.blank=Country is required
wholesale.address.state.not.blank=State is required
wholesale.address.state.not.valid=Invalid state code (2 letters required)
wholesale.address.city.not.blank=City is required
wholesale.address.line1.not.blank=Address line 1 is required
wholesale.address.zip.code.not.blank=ZIP code is required
wholesale.address.zip.code.not.valid=Invalid ZIP code format
wholesale.address.id.not.null=Address ID is required
wholesale.address.add.error=Failed to add shipping address
wholesale.address.update.error=Failed to update shipping address
wholesale.address.delete.error=Failed to delete shipping address
wholesale.address.country.not.valid=Invalid country code
wholesale.address.postal.code.not.valid=Invalid postal code format
# \u679A\u4E3E\u503C
## \u5305\u88C5\u7C7B\u578B
wholesale.packaging.type.individual=Indv.
wholesale.packaging.type.display=Display
wholesale.packaging.type.case=Case
## \u901A\u77E5\u72B6\u6001
wholesale.notify.status.pending=Pending
wholesale.notify.status.notified=Notified
## \u64CD\u4F5C\u7C7B\u578B
wholesale.operator.type.member=Member
wholesale.operator.type.admin=Admin
## \u8BA2\u5355\u72B6\u6001
wholesale.order.status.draft=Draft
wholesale.order.status.pending=Pending
wholesale.order.status.processing=Processing
wholesale.order.status.completed=Completed
wholesale.order.status.canceled=Canceled
## \u53D1\u8D27\u72B6\u6001
wholesale.shipment.status.pending=Pending
wholesale.shipment.status.preparing=Preparing
wholesale.shipment.status.partial_shipped=Partially Shipped
wholesale.shipment.status.shipped=Shipped
wholesale.shipment.status.delivered=Delivered
## \u652F\u4ED8\u72B6\u6001
wholesale.payment.status.pending=Pending
wholesale.payment.status.paid=Paid
wholesale.payment.status.partial_paid=Partially Paid
## \u5E93\u5B58\u72B6\u6001
wholesale.stock.status.sufficient=Sufficient
wholesale.stock.status.insufficient=Insufficient
wholesale.stock.status.out_of_stock=Out of Stock
wholesale.stock.status.partial_stock=Partial Stock
# Product Series
wholesale.product.series.name.exists=Series name already exists
wholesale.product.series.not.exists=Series does not exist
wholesale.product.series.in.use=Series is in use by products and cannot be deleted
# \u53D1\u7968\u72B6\u6001
wholesale.invoice.status.pending=Not Sent
wholesale.invoice.status.sent=Sent
wholesale.invoice.status.confirmed=Confirmed
# Region messages
wholesale.region.country.code.not.blank=Country code cannot be empty
wholesale.region.country.not.found=Country not found
# \u5E93\u5B58\u64CD\u4F5C\u7C7B\u578B
wholesale.stock.operation.type.create=Initialize
wholesale.stock.operation.type.adjust=Adjust
wholesale.stock.operation.type.lock=Lock
wholesale.stock.operation.type.release=Release
wholesale.stock.operation.type.sync=Sync
wholesale.stock.operation.type.deduct=Deduct
wholesale.stock.operation.type.outbound=Outbound
# \u53D1\u8D27\u65B9\u6848\u72B6\u6001
wholesale.shipment.plan.status.pending=Pending
wholesale.shipment.plan.status.confirmed=Confirmed
wholesale.shipment.plan.status.executed=Executed
wholesale.shipment.plan.status.unavailable=Unavailable
# \u5E93\u5B58\u5206\u914D\u72B6\u6001
wholesale.stock.allocation.status.pending=Pending
wholesale.stock.allocation.status.allocated=Allocated
wholesale.stock.allocation.status.released=Released
wholesale.stock.allocation.status.shipped=Shipped
# \u5305\u88C5\u8F6C\u6362\u7C7B\u578B
wholesale.conversion.type.no_change=No Change
wholesale.conversion.type.package_optimization=Package Optimization
wholesale.conversion.type.item_splitting=Item Splitting
wholesale.conversion.type.custom=Custom
# Strategy Conversion Types
wholesale.conversion.type.box_optimization=Box Optimization
wholesale.conversion.type.display_box_optimization=Display Box Optimization
# \u5305\u88C5\u8F6C\u6362\u72B6\u6001
wholesale.package.conversion.status.suggested=Suggested
wholesale.package.conversion.status.applied=Applied
wholesale.package.conversion.status.rejected=Rejected
# \u5305\u88F9\u72B6\u6001
wholesale.package.status.pending=Pending
wholesale.package.status.shipped=Shipped
wholesale.package.status.in_transit=In Transit
wholesale.package.status.delivered=Delivered
wholesale.package.status.exception=Exception
# China Post Service Types
wholesale.service.type.china.post.registered=China Post Registered Mail
wholesale.service.type.china.post.ems=China Post EMS
wholesale.service.type.china.post.ems.express=China Post EMS Express
wholesale.service.type.china.post.epacket=China Post ePacket
wholesale.service.type.china.post.air=China Post Air Mail

# ===== Mall Product Module Messages =====
# Product errors
mall.product.not.found=Product not found
mall.product.code.duplicate=Product code already exists
mall.product.name.required=Product name is required
mall.product.name.length=Product name cannot exceed {max} characters
mall.product.code.required=Product code is required
mall.product.code.length=Product code cannot exceed {max} characters
mall.product.description.length=Product description cannot exceed {max} characters
mall.product.short.description.length=Product short description cannot exceed {max} characters
mall.product.price.invalid=Product price must be greater than 0
mall.product.price.required=Product price is required
mall.product.original.price.invalid=Product original price must be greater than or equal to 0
mall.product.stock.insufficient=Insufficient stock
mall.product.stock.invalid=Stock quantity must be greater than or equal to 0
mall.product.min.stock.invalid=Minimum stock level must be greater than or equal to 0
mall.product.status.invalid=Invalid product status
mall.product.type.invalid=Invalid product type
mall.product.brand.not.found=Brand not found
mall.product.weight.invalid=Product weight must be greater than or equal to 0
mall.product.dimensions.length=Product dimensions cannot exceed {max} characters
mall.product.tags.length=Product tags cannot exceed {max} characters
mall.product.meta.title.length=SEO title cannot exceed {max} characters
mall.product.meta.description.length=SEO description cannot exceed {max} characters
mall.product.meta.keywords.length=SEO keywords cannot exceed {max} characters
mall.product.featured.image.length=Featured image URL cannot exceed {max} characters
mall.product.gallery.invalid=Invalid product gallery format
mall.product.sort.order.invalid=Sort order must be greater than or equal to 0
mall.product.create.success=Product created successfully
mall.product.update.success=Product updated successfully
mall.product.delete.success=Product deleted successfully
mall.product.batch.delete.success=Products deleted successfully
mall.product.status.update.success=Product status updated successfully

# Category errors
mall.category.not.found=Category not found
mall.category.name.required=Category name is required
mall.category.name.length=Category name cannot exceed {max} characters
mall.category.code.required=Category code is required
mall.category.code.length=Category code cannot exceed {max} characters
mall.category.code.duplicate=Category code already exists
mall.category.description.length=Category description cannot exceed {max} characters
mall.category.parent.invalid=Invalid parent category
mall.category.parent.circular=Cannot set self or child category as parent
mall.category.has.products=Cannot delete category with products
mall.category.has.children=Cannot delete category with subcategories
mall.category.status.invalid=Invalid category status
mall.category.sort.order.invalid=Sort order must be greater than or equal to 0
mall.category.image.length=Category image URL cannot exceed {max} characters
mall.category.icon.length=Category icon URL cannot exceed {max} characters
mall.category.meta.title.length=SEO title cannot exceed {max} characters
mall.category.meta.description.length=SEO description cannot exceed {max} characters
mall.category.meta.keywords.length=SEO keywords cannot exceed {max} characters
mall.category.create.success=Category created successfully
mall.category.update.success=Category updated successfully
mall.category.delete.success=Category deleted successfully
mall.category.batch.delete.success=Categories deleted successfully

# Brand errors
mall.brand.not.found=Brand not found
mall.brand.id.invalid=Invalid brand ID
mall.brand.name.required=Brand name is required
mall.brand.name.length=Brand name cannot exceed {max} characters
mall.brand.code.required=Brand code is required
mall.brand.code.length=Brand code cannot exceed {max} characters
mall.brand.code.duplicate=Brand code already exists
mall.brand.description.length=Brand description cannot exceed {max} characters
mall.brand.logo.length=Brand logo URL cannot exceed {max} characters
mall.brand.website.length=Brand website URL cannot exceed {max} characters
mall.brand.website.invalid=Invalid brand website URL format
mall.brand.status.invalid=Invalid brand status
mall.brand.sort.order.invalid=Sort order must be greater than or equal to 0
mall.brand.meta.title.length=SEO title cannot exceed {max} characters
mall.brand.meta.description.length=SEO description cannot exceed {max} characters
mall.brand.meta.keywords.length=SEO keywords cannot exceed {max} characters
mall.brand.create.success=Brand created successfully
mall.brand.update.success=Brand updated successfully
mall.brand.delete.success=Brand deleted successfully
mall.brand.batch.delete.success=Brands deleted successfully

# Product SKU errors
mall.product.sku.not.found=Product SKU not found
mall.product.sku.code.required=SKU code is required
mall.product.sku.code.length=SKU code cannot exceed {max} characters
mall.product.sku.code.duplicate=SKU code already exists
mall.product.sku.name.required=SKU name is required
mall.product.sku.name.length=SKU name cannot exceed {max} characters
mall.product.sku.price.invalid=SKU price must be greater than 0
mall.product.sku.price.required=SKU price is required
mall.product.sku.original.price.invalid=SKU original price must be greater than or equal to 0
mall.product.sku.stock.invalid=SKU stock quantity must be greater than or equal to 0
mall.product.sku.min.stock.invalid=SKU minimum stock level must be greater than or equal to 0
mall.product.sku.status.invalid=Invalid SKU status
mall.product.sku.attributes.invalid=Invalid SKU attributes format
mall.product.sku.image.length=SKU image URL cannot exceed {max} characters
mall.product.sku.weight.invalid=SKU weight must be greater than or equal to 0
mall.product.sku.dimensions.length=SKU dimensions cannot exceed {max} characters
mall.product.sku.create.success=SKU created successfully
mall.product.sku.update.success=SKU updated successfully
mall.product.sku.delete.success=SKU deleted successfully

# Product Image errors
mall.product.image.not.found=Product image not found
mall.product.image.url.required=Image URL is required
mall.product.image.url.length=Image URL cannot exceed {max} characters
mall.product.image.url.invalid=Invalid image URL format
mall.product.image.alt.length=Image alt text cannot exceed {max} characters
mall.product.image.sort.order.invalid=Image sort order must be greater than or equal to 0
mall.product.image.upload.success=Image uploaded successfully
mall.product.image.delete.success=Image deleted successfully

# Product Attribute errors
mall.product.attribute.not.found=Product attribute not found
mall.product.attribute.name.required=Attribute name is required
mall.product.attribute.name.length=Attribute name cannot exceed {max} characters
mall.product.attribute.code.required=Attribute code is required
mall.product.attribute.code.length=Attribute code cannot exceed {max} characters
mall.product.attribute.code.duplicate=Attribute code already exists
mall.product.attribute.type.invalid=Invalid attribute type
mall.product.attribute.sort.order.invalid=Attribute sort order must be greater than or equal to 0
mall.product.attribute.create.success=Attribute created successfully
mall.product.attribute.update.success=Attribute updated successfully
mall.product.attribute.delete.success=Attribute deleted successfully

# Product Attribute Value errors
mall.product.attribute.value.not.found=Attribute value not found
mall.product.attribute.value.name.required=Attribute value name is required
mall.product.attribute.value.name.length=Attribute value name cannot exceed {max} characters
mall.product.attribute.value.code.required=Attribute value code is required
mall.product.attribute.value.code.length=Attribute value code cannot exceed {max} characters
mall.product.attribute.value.code.duplicate=Attribute value code already exists
mall.product.attribute.value.sort.order.invalid=Attribute value sort order must be greater than or equal to 0
mall.product.attribute.value.create.success=Attribute value created successfully
mall.product.attribute.value.update.success=Attribute value updated successfully
mall.product.attribute.value.delete.success=Attribute value deleted successfully

# Product Status enum
mall.product.status.disabled=Disabled
mall.product.status.enabled=Enabled
mall.product.status.draft=Draft

# Product Type enum
mall.product.type.simple=Simple Product
mall.product.type.variable=Variable Product

# Category Status enum
mall.category.status.disabled=Disabled
mall.category.status.enabled=Enabled

# Search and filter messages
mall.product.search.keyword.required=Search keyword is required
mall.product.search.keyword.length=Search keyword cannot exceed {max} characters
mall.product.search.no.results=No products found
mall.product.filter.price.invalid=Invalid price range
mall.product.filter.category.invalid=Invalid category filter
mall.product.filter.brand.invalid=Invalid brand filter

# Business operation messages
mall.product.stock.update.success=Stock updated successfully
mall.product.stock.check.failed=Stock check failed
mall.product.related.not.found=No related products found
mall.product.featured.not.found=No featured products found

# Validation messages
mall.product.id.required=Product ID is required
mall.category.id.required=Category ID is required
mall.brand.id.required=Brand ID is required
mall.product.sku.id.required=SKU ID is required
mall.product.image.id.required=Image ID is required
mall.product.attribute.id.required=Attribute ID is required
mall.product.attribute.value.id.required=Attribute value ID is required

# Product validation
mall.validation.product.short.description.max.length=Short description cannot exceed 500 characters
mall.validation.product.price.required=Product price is required
mall.validation.product.price.min=Product price must be greater than 0
mall.validation.product.price.format=Invalid price format
mall.validation.product.original.price.min=Original price must be greater than or equal to 0
mall.validation.product.original.price.format=Invalid original price format
mall.validation.product.stock.quantity.required=Stock quantity is required
mall.validation.product.stock.quantity.min=Stock quantity must be greater than or equal to 0
mall.validation.product.min.stock.level.min=Minimum stock level must be greater than or equal to 0
mall.validation.product.status.required=Product status is required
mall.validation.product.type.required=Product type is required
mall.validation.product.seo.title.max.length=SEO title cannot exceed 200 characters
mall.validation.product.seo.description.max.length=SEO description cannot exceed 500 characters
mall.validation.product.seo.keywords.max.length=SEO keywords cannot exceed 200 characters
mall.validation.product.sort.order.min=Sort order must be greater than or equal to 0
mall.validation.product.featured.image.max.length=Featured image URL cannot exceed 500 characters
mall.validation.product.weight.min=Product weight must be greater than or equal to 0
mall.validation.product.weight.format=Invalid weight format
mall.validation.product.dimensions.max.length=Product dimensions cannot exceed 100 characters
mall.validation.product.tags.max.length=Product tags cannot exceed 500 characters

# Product image validation
mall.validation.product.image.id.required=Image ID is required
mall.validation.product.image.url.required=Image URL is required
mall.validation.product.image.url.max.length=Image URL cannot exceed 500 characters
mall.validation.product.image.alt.max.length=Image alt text cannot exceed 200 characters
mall.validation.product.image.title.max.length=Image title cannot exceed 200 characters
mall.validation.product.image.sort.order.min=Sort order must be greater than or equal to 0
mall.validation.product.image.featured.invalid=Invalid featured image flag

# Product SKU validation
mall.validation.product.sku.id.required=SKU ID is required
mall.validation.product.sku.code.required=SKU code is required
mall.validation.product.sku.code.max.length=SKU code cannot exceed 50 characters
mall.validation.product.sku.name.required=SKU name is required
mall.validation.product.sku.name.max.length=SKU name cannot exceed 200 characters
mall.validation.product.sku.price.required=SKU price is required
mall.validation.product.sku.price.min=SKU price must be greater than 0
mall.validation.product.sku.price.format=Invalid SKU price format
mall.validation.product.sku.original.price.min=SKU original price must be greater than or equal to 0
mall.validation.product.sku.original.price.format=Invalid SKU original price format
mall.validation.product.sku.stock.quantity.required=SKU stock quantity is required
mall.validation.product.sku.stock.quantity.min=SKU stock quantity must be greater than or equal to 0
mall.validation.product.sku.min.stock.level.min=SKU minimum stock level must be greater than or equal to 0
mall.validation.product.sku.status.required=SKU status is required
mall.validation.product.sku.status.invalid=Invalid SKU status
mall.validation.product.sku.image.max.length=SKU image URL cannot exceed 500 characters
mall.validation.product.sku.weight.min=SKU weight must be greater than or equal to 0
mall.validation.product.sku.weight.format=Invalid SKU weight format
mall.validation.product.sku.dimensions.max.length=SKU dimensions cannot exceed 100 characters

# ===== Wholesale Admin Module Messages =====
# Product messages
wholesale.admin.product.id.not.null=Product ID is required
wholesale.admin.product.name.not.blank=Product name is required
wholesale.admin.product.name.length=Product name cannot exceed {max} characters
wholesale.admin.product.category.not.null=Product category is required

# Member messages
wholesale.admin.member.id.not.null=Member ID is required
wholesale.admin.member.email.not.blank=Email address is required
wholesale.admin.member.email.format=Invalid email format
wholesale.admin.member.password.not.blank=Password is required
wholesale.admin.member.phone.format=Invalid phone number format
wholesale.admin.member.first.name.not.blank=First name is required
wholesale.admin.member.last.name.not.blank=Last name is required

# Warehouse messages
wholesale.admin.warehouse.id.not.null=Warehouse ID is required
wholesale.admin.warehouse.name.not.blank=Warehouse name is required
wholesale.admin.warehouse.name.length=Warehouse name cannot exceed {max} characters
wholesale.admin.warehouse.code.not.blank=Warehouse code is required
wholesale.admin.warehouse.code.length=Warehouse code cannot exceed {max} characters
wholesale.admin.warehouse.address.length=Warehouse address cannot exceed {max} characters
wholesale.admin.warehouse.contact.length=Contact person cannot exceed {max} characters
wholesale.admin.warehouse.phone.length=Phone number cannot exceed {max} characters

# Order messages
wholesale.admin.order.no.not.blank=Order number is required
wholesale.admin.order.customer.id.not.null=Customer must be specified
wholesale.admin.order.customer.address.not.null=Customer address must be specified
wholesale.admin.order.items.not.empty=Order items must be selected
wholesale.admin.order.discount.amount.min=Discount amount cannot be less than 0
wholesale.admin.order.discount.amount.exceeds.total=Discount amount cannot exceed order total
wholesale.order.discount.exceeds.total=Discount amount cannot exceed order total. Order total: ${total}, Discount: ${discount}
wholesale.order.discount.min.zero=Discount amount cannot be less than 0
wholesale.order.discount.exceeds.subtotal=Discount amount cannot exceed order subtotal ${subtotal}
wholesale.order.discount.format.error=Invalid discount amount format, reset to 0

# Common validation messages
wholesale.admin.common.id.not.null=ID is required
wholesale.admin.common.name.not.blank=Name is required
wholesale.admin.common.code.not.blank=Code is required
wholesale.admin.common.length.exceed=Length cannot exceed {max} characters

# ===== Mall Validation Messages =====
# Product validation
mall.validation.product.id.required=Product ID is required
mall.validation.product.name.required=Product name is required
mall.validation.product.name.max.length=Product name cannot exceed 200 characters
mall.validation.product.code.required=Product code is required
mall.validation.product.code.max.length=Product code cannot exceed 50 characters

# Brand validation
mall.validation.brand.id.required=Brand ID is required
mall.validation.brand.name.required=Brand name is required
mall.validation.brand.name.max.length=Brand name cannot exceed 100 characters
mall.validation.brand.code.required=Brand code is required
mall.validation.brand.code.max.length=Brand code cannot exceed 50 characters
mall.validation.brand.description.max.length=Brand description cannot exceed 500 characters
mall.validation.brand.logo.max.length=Brand logo URL cannot exceed 500 characters
mall.validation.brand.website.max.length=Brand website URL cannot exceed 200 characters
mall.validation.brand.website.format=Invalid brand website URL format
mall.validation.brand.status.required=Brand status is required
mall.validation.brand.status.invalid=Invalid brand status value
mall.validation.brand.sort.min=Sort order must be greater than or equal to 0
mall.validation.brand.seo.title.max.length=SEO title cannot exceed 200 characters
mall.validation.brand.seo.description.max.length=SEO description cannot exceed 500 characters
mall.validation.brand.seo.keywords.max.length=SEO keywords cannot exceed 200 characters

# Category validation
mall.validation.category.id.required=Category ID is required
mall.validation.category.name.required=Category name is required
mall.validation.category.name.max.length=Category name cannot exceed 100 characters
mall.validation.category.code.required=Category code is required
mall.validation.category.code.max.length=Category code cannot exceed 50 characters
mall.validation.category.description.max.length=Category description cannot exceed 500 characters
mall.validation.category.parent.id.required=Parent category ID is required
mall.validation.category.parent.id.min=Parent category ID must be greater than or equal to 0
mall.validation.category.status.required=Category status is required
mall.validation.category.sort.min=Sort order must be greater than or equal to 0
mall.validation.category.image.max.length=Category image URL cannot exceed 500 characters
mall.validation.category.icon.max.length=Category icon URL cannot exceed 200 characters
mall.validation.category.seo.title.max.length=SEO title cannot exceed 200 characters
mall.validation.category.seo.description.max.length=SEO description cannot exceed 500 characters
mall.validation.category.seo.keywords.max.length=SEO keywords cannot exceed 200 characters

# Customer name validation messages
wholesale.admin.member.first.name.not.blank=First name is required
wholesale.admin.member.last.name.not.blank=Last name is required
wholesale.admin.member.customer.name.too.long=Customer name cannot exceed {0} characters, current: {1} characters (first name:{2} + space:1 + last name:{3})
