#\u9519\u8BEF\u6D88\u606F
not.null=* \u5FC5\u987B\u586B\u5199

# ERP\u7CFB\u7EDF\u96C6\u6210\u76F8\u5173\u6D88\u606F
erp.integration.disabled=ERP\u96C6\u6210\u529F\u80FD\u5DF2\u7981\u7528
erp.provider.not.found=\u672A\u627E\u5230\u53EF\u7528\u7684ERP\u63D0\u4F9B\u5546
erp.provider.none.available=\u65E0\u53EF\u7528\u63D0\u4F9B\u5546
erp.connection.failed=ERP\u8FDE\u63A5\u5931\u8D25
erp.stock.sync.invalid.data=ERP\u5E93\u5B58\u6570\u636E\u65E0\u6548
erp.stock.fetch.failed=\u83B7\u53D6ERP\u5E93\u5B58\u6570\u636E\u5931\u8D25
erp.provider.type.empty=ERP\u63D0\u4F9B\u5546\u7C7B\u578B\u4E3A\u7A7A
erp.provider.duplicate.type=\u53D1\u73B0\u91CD\u590D\u7684ERP\u63D0\u4F9B\u5546\u7C7B\u578B
lookah.erp.unavailable=Lookah ERP\u7CFB\u7EDF\u4E0D\u53EF\u7528
# \u5168\u5C40\u9519\u8BEF
system.error=\u7CFB\u7EDF\u53D1\u751F\u4E86\u9519\u8BEF\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5
# \u540E\u53F0\u7528\u6237
user.captcha.error=\u9A8C\u8BC1\u7801\u9519\u8BEF
user.captcha.expire=\u9A8C\u8BC1\u7801\u5DF2\u5931\u6548
user.not.exists=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
user.password.not.match=\u7528\u6237\u4E0D\u5B58\u5728/\u5BC6\u7801\u9519\u8BEF
user.password.retry.limit.count=\u8D26\u53F7\u6216\u5BC6\u7801\u9519\u8BEF
user.password.retry.limit.exceed=\u5BC6\u7801\u8F93\u5165\u9519\u8BEF{0}\u6B21\uFF0C\u5E10\u6237\u9501\u5B9A{1}\u5206\u949F
user.password.delete=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
user.blocked=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
role.blocked=\u89D2\u8272\u5DF2\u5C01\u7981\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
user.logout.success=\u9000\u51FA\u6210\u529F
length.not.valid=\u957F\u5EA6\u5FC5\u987B\u5728{min}\u5230{max}\u4E2A\u5B57\u7B26\u4E4B\u95F4
user.username.not.blank=\u7528\u6237\u540D\u4E0D\u80FD\u4E3A\u7A7A
user.username.not.valid=* 2\u523020\u4E2A\u6C49\u5B57\u3001\u5B57\u6BCD\u3001\u6570\u5B57\u6216\u4E0B\u5212\u7EBF\u7EC4\u6210\uFF0C\u4E14\u5FC5\u987B\u4EE5\u975E\u6570\u5B57\u5F00\u5934
user.username.length.valid=\u8D26\u6237\u957F\u5EA6\u5FC5\u987B\u5728{min}\u5230{max}\u4E2A\u5B57\u7B26\u4E4B\u95F4
user.password.not.blank=\u7528\u6237\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A
user.password.length.valid=\u7528\u6237\u5BC6\u7801\u957F\u5EA6\u5FC5\u987B\u5728{min}\u5230{max}\u4E2A\u5B57\u7B26\u4E4B\u95F4
user.password.not.valid=* 5-50\u4E2A\u5B57\u7B26
user.email.not.valid=\u90AE\u7BB1\u683C\u5F0F\u9519\u8BEF
user.email.not.blank=\u90AE\u7BB1\u4E0D\u80FD\u4E3A\u7A7A
user.phonenumber.not.blank=\u7528\u6237\u624B\u673A\u53F7\u4E0D\u80FD\u4E3A\u7A7A
user.mobile.phone.number.not.valid=\u624B\u673A\u53F7\u683C\u5F0F\u9519\u8BEF
user.login.success=\u767B\u5F55\u6210\u529F
user.register.success=\u6CE8\u518C\u6210\u529F
user.register.save.error=\u4FDD\u5B58\u7528\u6237 {0} \u5931\u8D25\uFF0C\u6CE8\u518C\u8D26\u53F7\u5DF2\u5B58\u5728
user.register.error=\u6CE8\u518C\u5931\u8D25\uFF0C\u8BF7\u8054\u7CFB\u7CFB\u7EDF\u7BA1\u7406\u4EBA\u5458
user.notfound=\u8BF7\u91CD\u65B0\u767B\u5F55
user.forcelogout=\u7BA1\u7406\u5458\u5F3A\u5236\u9000\u51FA\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55
user.unknown.error=\u672A\u77E5\u9519\u8BEF\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55
auth.grant.type.error=\u8BA4\u8BC1\u6743\u9650\u7C7B\u578B\u9519\u8BEF
auth.grant.type.blocked=\u8BA4\u8BC1\u6743\u9650\u7C7B\u578B\u5DF2\u7981\u7528
auth.grant.type.not.blank=\u8BA4\u8BC1\u6743\u9650\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
auth.clientid.not.blank=\u8BA4\u8BC1\u5BA2\u6237\u7AEFid\u4E0D\u80FD\u4E3A\u7A7A
##\u6587\u4EF6\u4E0A\u4F20\u6D88\u606F
upload.exceed.maxSize=\u4E0A\u4F20\u7684\u6587\u4EF6\u5927\u5C0F\u8D85\u51FA\u9650\u5236\u7684\u6587\u4EF6\u5927\u5C0F\uFF01<br/>\u5141\u8BB8\u7684\u6587\u4EF6\u6700\u5927\u5927\u5C0F\u662F\uFF1A{0}MB\uFF01
upload.filename.exceed.length=\u4E0A\u4F20\u7684\u6587\u4EF6\u540D\u6700\u957F{0}\u4E2A\u5B57\u7B26
##\u6743\u9650
no.permission=\u60A8\u6CA1\u6709\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.create.permission=\u60A8\u6CA1\u6709\u521B\u5EFA\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.update.permission=\u60A8\u6CA1\u6709\u4FEE\u6539\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.delete.permission=\u60A8\u6CA1\u6709\u5220\u9664\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.export.permission=\u60A8\u6CA1\u6709\u5BFC\u51FA\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.view.permission=\u60A8\u6CA1\u6709\u67E5\u770B\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
repeat.submit.message=\u4E0D\u5141\u8BB8\u91CD\u590D\u63D0\u4EA4\uFF0C\u8BF7\u7A0D\u5019\u518D\u8BD5
rate.limiter.message=\u8BBF\u95EE\u8FC7\u4E8E\u9891\u7E41\uFF0C\u8BF7\u7A0D\u5019\u518D\u8BD5
sms.code.not.blank=\u77ED\u4FE1\u9A8C\u8BC1\u7801\u4E0D\u80FD\u4E3A\u7A7A
sms.code.retry.limit.count=\u77ED\u4FE1\u9A8C\u8BC1\u7801\u8F93\u5165\u9519\u8BEF{0}\u6B21
sms.code.retry.limit.exceed=\u77ED\u4FE1\u9A8C\u8BC1\u7801\u8F93\u5165\u9519\u8BEF{0}\u6B21\uFF0C\u5E10\u6237\u9501\u5B9A{1}\u5206\u949F
email.code.not.blank=\u90AE\u7BB1\u9A8C\u8BC1\u7801\u4E0D\u80FD\u4E3A\u7A7A
email.code.retry.limit.count=\u90AE\u7BB1\u9A8C\u8BC1\u7801\u8F93\u5165\u9519\u8BEF{0}\u6B21
email.code.retry.limit.exceed=\u90AE\u7BB1\u9A8C\u8BC1\u7801\u8F93\u5165\u9519\u8BEF{0}\u6B21\uFF0C\u5E10\u6237\u9501\u5B9A{1}\u5206\u949F
xcx.code.not.blank=\u5C0F\u7A0B\u5E8F[code]\u4E0D\u80FD\u4E3A\u7A7A
social.source.not.blank=\u7B2C\u4E09\u65B9\u767B\u5F55\u5E73\u53F0[source]\u4E0D\u80FD\u4E3A\u7A7A
social.code.not.blank=\u7B2C\u4E09\u65B9\u767B\u5F55\u5E73\u53F0[code]\u4E0D\u80FD\u4E3A\u7A7A
social.state.not.blank=\u7B2C\u4E09\u65B9\u767B\u5F55\u5E73\u53F0[state]\u4E0D\u80FD\u4E3A\u7A7A
# \u5168\u5C40\u8BF7\u6C42\u6D88\u606F
request.body.missing=\u8BF7\u6C42\u6570\u636E\u6709\u8BEF
request.method.not.supported=\u4E0D\u652F\u6301{0}\u8BF7\u6C42\u65B9\u6CD5
request.path.variable.missing=\u7F3A\u5C11\u5FC5\u9700\u7684\u8DEF\u5F84\u53D8\u91CF[{0}]
request.uri.not.found=\u8BF7\u6C42\u7684URI\u4E0D\u5B58\u5728
request.server.error=\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF
request.runtime.error=\u8FD0\u884C\u65F6\u9519\u8BEF
request.argument.type.mismatch=\u53C2\u6570[{0}]\u5E94\u4E3A'{1}'\u4F46\u63A5\u6536\u5230'{2}'
request.validation.failed=\u53C2\u6570\u6821\u9A8C\u5931\u8D25
# \u54CD\u5E94\u6D88\u606F
response.success=\u64CD\u4F5C\u6210\u529F
response.failed=\u64CD\u4F5C\u5931\u8D25
response.warn=\u8B66\u544A
# Sa-Token\u8BA4\u8BC1\u6D88\u606F
auth.permission.denied=\u60A8\u6CA1\u6709\u8BBF\u95EE\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6388\u6743
auth.role.denied=\u60A8\u6CA1\u6709\u89D2\u8272\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6388\u6743
auth.not.login=\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u767B\u5F55\u540E\u518D\u8BBF\u95EE\u7CFB\u7EDF\u8D44\u6E90
auth.client.id.required=\u5BA2\u6237\u7AEFID\u4E0D\u80FD\u4E3A\u7A7A

# ===== Wholesale Module Messages =====
# 正确的枚举键名
wholesale.packaging.type.individual=单品
wholesale.packaging.type.display=展示盒
wholesale.packaging.type.case=箱装
# 通知状态
wholesale.notify.status.pending=待通知
wholesale.notify.status.notified=已通知
# 操作类型
wholesale.operator.type.member=会员
wholesale.operator.type.admin=管理员
# 订单状态
wholesale.order.status.draft=草稿
wholesale.order.status.pending=待处理
wholesale.order.status.processing=处理中
wholesale.order.status.completed=已完成
wholesale.order.status.canceled=已取消
wholesale.order.not.exists=订单不存在
# 发货状态
wholesale.shipment.status.pending=待发货
wholesale.shipment.status.preparing=备货中
wholesale.shipment.status.partial_shipped=部分发货
wholesale.shipment.status.shipped=已发货
wholesale.shipment.status.delivered=已派送
# 发货审批状态
wholesale.shipment.approval.status.no_approval=无需审批
wholesale.shipment.approval.status.no_approval_enabled=待申请
wholesale.shipment.approval.status.pending=待审批
wholesale.shipment.approval.status.approved=审批通过
wholesale.shipment.approval.status.rejected=审批拒绝
wholesale.shipment.approval.status.cancelled=已撤销
# 支付状态
wholesale.payment.status.pending=待付款
wholesale.payment.status.partial_paid=部分付款
wholesale.payment.status.paid=已付款
# 库存状态
wholesale.stock.status.sufficient=库存充足
wholesale.stock.status.insufficient=库存不足
wholesale.stock.status.out_of_stock=无库存
wholesale.stock.status.partial_stock=部分库存
# 库存操作类型
wholesale.stock.operation.type.create=初始化库存
wholesale.stock.operation.type.adjust=调整库存
wholesale.stock.operation.type.lock=锁定库存
wholesale.stock.operation.type.release=释放库存
wholesale.stock.operation.type.sync=同步库存
wholesale.stock.operation.type.deduct=扣减库存
wholesale.stock.operation.type.outbound=发货出库
# 产品分类相关
wholesale.product.category.not.exists=产品分类不存在
wholesale.product.category.name.duplicate=产品分类名称已存在
wholesale.product.category.delete.has.child=存在子分类，不允许删除
wholesale.product.category.delete.has.product=分类下存在产品，不允许删除
# 通用错误
common.parameter.not.null=参数不能为空
common.convert.error=数据转换错误
# 产品系列
wholesale.product.series.name.exists=系列名称已存在
wholesale.product.series.not.exists=系列不存在
wholesale.product.series.in.use=系列正在被产品使用，无法删除
# 发票状态
wholesale.invoice.status.pending=未发送
wholesale.invoice.status.sent=已发送
# 地址相关消息
wholesale.address.not.exists=收货地址不存在
wholesale.address.country.not.blank=国家不能为空
wholesale.address.state.not.blank=州/省不能为空
wholesale.address.state.not.valid=无效的州/省代码
wholesale.address.country.not.valid=无效的国家代码
wholesale.address.postal.code.not.valid=邮政编码格式不正确
wholesale.address.add.error=添加收货地址失败
wholesale.address.update.error=更新收货地址失败
wholesale.address.delete.error=删除收货地址失败
# 区域相关消息
wholesale.region.country.code.not.blank=国家代码不能为空
wholesale.region.country.not.found=未找到国家
# 商品相关错误
wholesale.product.stock.insufficient={0}库存不足
wholesale.product.stock.insufficient.no.permission={0}库存不足
# 发货方案状态
wholesale.shipment.plan.status.pending=待发货
wholesale.shipment.plan.status.executed=执行中
wholesale.shipment.plan.status.unavailable=失效
# 库存分配状态
wholesale.stock.allocation.status.pending=待处理
wholesale.stock.allocation.status.allocated=已分配
wholesale.stock.allocation.status.released=已释放
wholesale.stock.allocation.status.shipped=已发货
# 包装转换类型
wholesale.conversion.type.no_change=无变化
wholesale.conversion.type.package_optimization=包装优化
wholesale.conversion.type.item_splitting=拆分单品
wholesale.conversion.type.custom=自定义
# 包装转换状态
wholesale.package.conversion.status.suggested=建议
wholesale.package.conversion.status.applied=已应用
wholesale.package.conversion.status.rejected=已拒绝
# 策略转换类型名称
wholesale.conversion.type.box_optimization=整箱优化
wholesale.conversion.type.display_box_optimization=展示盒优化
# 包裹状态
wholesale.package.status.pending=待处理
wholesale.package.status.shipped=已发货
wholesale.package.status.in_transit=运输中
wholesale.package.status.delivered=已送达
wholesale.package.status.exception=异常
# 中国邮政服务类型
wholesale.service.type.china.post.registered=中国邮政挂号信
wholesale.service.type.china.post.ems=中国邮政特快专递
wholesale.service.type.china.post.ems.express=中国邮政特快专递快递
wholesale.service.type.china.post.epacket=中国邮政电子包裹
wholesale.service.type.china.post.air=中国邮政航空邮件

# ===== Mall Product Module Messages =====
# Product errors
mall.product.not.found=商品不存在
mall.product.code.duplicate=商品编码已存在
mall.product.name.required=商品名称不能为空
mall.product.name.length=商品名称长度不能超过{max}个字符
mall.product.code.required=商品编码不能为空
mall.product.code.length=商品编码长度不能超过{max}个字符
mall.product.description.length=商品描述长度不能超过{max}个字符
mall.product.short.description.length=商品简介长度不能超过{max}个字符
mall.product.price.invalid=商品价格必须大于0
mall.product.price.required=商品价格不能为空
mall.product.original.price.invalid=商品原价必须大于等于0
mall.product.stock.insufficient=库存不足
mall.product.stock.invalid=库存数量必须大于等于0
mall.product.min.stock.invalid=最低库存必须大于等于0
mall.product.status.invalid=商品状态无效
mall.product.type.invalid=商品类型无效
mall.product.brand.not.found=品牌不存在
mall.product.weight.invalid=商品重量必须大于等于0
mall.product.dimensions.length=商品尺寸长度不能超过{max}个字符
mall.product.tags.length=商品标签长度不能超过{max}个字符
mall.product.meta.title.length=SEO标题长度不能超过{max}个字符
mall.product.meta.description.length=SEO描述长度不能超过{max}个字符
mall.product.meta.keywords.length=SEO关键词长度不能超过{max}个字符
mall.product.featured.image.length=主图URL长度不能超过{max}个字符
mall.product.gallery.invalid=商品图册格式无效
mall.product.sort.order.invalid=排序值必须大于等于0
mall.product.create.success=商品创建成功
mall.product.update.success=商品更新成功
mall.product.delete.success=商品删除成功
mall.product.batch.delete.success=批量删除商品成功
mall.product.status.update.success=商品状态更新成功

# Category errors
mall.category.not.found=分类不存在
mall.category.name.required=分类名称不能为空
mall.category.name.length=分类名称长度不能超过{max}个字符
mall.category.code.required=分类编码不能为空
mall.category.code.length=分类编码长度不能超过{max}个字符
mall.category.code.duplicate=分类编码已存在
mall.category.description.length=分类描述长度不能超过{max}个字符
mall.category.parent.invalid=无效的父分类
mall.category.parent.circular=不能将自己或子分类设为父分类
mall.category.has.products=分类下存在商品，无法删除
mall.category.has.children=分类下存在子分类，无法删除
mall.category.status.invalid=分类状态无效
mall.category.sort.order.invalid=排序值必须大于等于0
mall.category.image.length=分类图片URL长度不能超过{max}个字符
mall.category.icon.length=分类图标URL长度不能超过{max}个字符
mall.category.meta.title.length=SEO标题长度不能超过{max}个字符
mall.category.meta.description.length=SEO描述长度不能超过{max}个字符
mall.category.meta.keywords.length=SEO关键词长度不能超过{max}个字符
mall.category.create.success=分类创建成功
mall.category.update.success=分类更新成功
mall.category.delete.success=分类删除成功
mall.category.batch.delete.success=批量删除分类成功

# Brand errors
mall.brand.not.found=品牌不存在
mall.brand.id.invalid=无效的品牌ID
mall.brand.name.required=品牌名称不能为空
mall.brand.name.length=品牌名称长度不能超过{max}个字符
mall.brand.code.required=品牌编码不能为空
mall.brand.code.length=品牌编码长度不能超过{max}个字符
mall.brand.code.duplicate=品牌编码已存在
mall.brand.description.length=品牌描述长度不能超过{max}个字符
mall.brand.logo.length=品牌Logo URL长度不能超过{max}个字符
mall.brand.website.length=品牌官网URL长度不能超过{max}个字符
mall.brand.website.invalid=品牌官网URL格式无效
mall.brand.status.invalid=品牌状态无效
mall.brand.sort.order.invalid=排序值必须大于等于0
mall.brand.meta.title.length=SEO标题长度不能超过{max}个字符
mall.brand.meta.description.length=SEO描述长度不能超过{max}个字符
mall.brand.meta.keywords.length=SEO关键词长度不能超过{max}个字符
mall.brand.create.success=品牌创建成功
mall.brand.update.success=品牌更新成功
mall.brand.delete.success=品牌删除成功
mall.brand.batch.delete.success=批量删除品牌成功

# Product SKU errors
mall.product.sku.not.found=商品SKU不存在
mall.product.sku.code.required=SKU编码不能为空
mall.product.sku.code.length=SKU编码长度不能超过{max}个字符
mall.product.sku.code.duplicate=SKU编码已存在
mall.product.sku.name.required=SKU名称不能为空
mall.product.sku.name.length=SKU名称长度不能超过{max}个字符
mall.product.sku.price.invalid=SKU价格必须大于0
mall.product.sku.price.required=SKU价格不能为空
mall.product.sku.original.price.invalid=SKU原价必须大于等于0
mall.product.sku.stock.invalid=SKU库存数量必须大于等于0
mall.product.sku.min.stock.invalid=SKU最低库存必须大于等于0
mall.product.sku.status.invalid=SKU状态无效
mall.product.sku.attributes.invalid=SKU属性格式无效
mall.product.sku.image.length=SKU图片URL长度不能超过{max}个字符
mall.product.sku.weight.invalid=SKU重量必须大于等于0
mall.product.sku.dimensions.length=SKU尺寸长度不能超过{max}个字符
mall.product.sku.create.success=SKU创建成功
mall.product.sku.update.success=SKU更新成功
mall.product.sku.delete.success=SKU删除成功

# Product Image errors
mall.product.image.not.found=商品图片不存在
mall.product.image.url.required=图片URL不能为空
mall.product.image.url.length=图片URL长度不能超过{max}个字符
mall.product.image.url.invalid=图片URL格式无效
mall.product.image.alt.length=图片Alt文本长度不能超过{max}个字符
mall.product.image.sort.order.invalid=图片排序值必须大于等于0
mall.product.image.upload.success=图片上传成功
mall.product.image.delete.success=图片删除成功

# Product Attribute errors
mall.product.attribute.not.found=商品属性不存在
mall.product.attribute.name.required=属性名称不能为空
mall.product.attribute.name.length=属性名称长度不能超过{max}个字符
mall.product.attribute.code.required=属性编码不能为空
mall.product.attribute.code.length=属性编码长度不能超过{max}个字符
mall.product.attribute.code.duplicate=属性编码已存在
mall.product.attribute.type.invalid=属性类型无效
mall.product.attribute.sort.order.invalid=属性排序值必须大于等于0
mall.product.attribute.create.success=属性创建成功
mall.product.attribute.update.success=属性更新成功
mall.product.attribute.delete.success=属性删除成功

# Product Attribute Value errors
mall.product.attribute.value.not.found=属性值不存在
mall.product.attribute.value.name.required=属性值名称不能为空
mall.product.attribute.value.name.length=属性值名称长度不能超过{max}个字符
mall.product.attribute.value.code.required=属性值编码不能为空
mall.product.attribute.value.code.length=属性值编码长度不能超过{max}个字符
mall.product.attribute.value.code.duplicate=属性值编码已存在
mall.product.attribute.value.sort.order.invalid=属性值排序值必须大于等于0
mall.product.attribute.value.create.success=属性值创建成功
mall.product.attribute.value.update.success=属性值更新成功
mall.product.attribute.value.delete.success=属性值删除成功

# Product Status enum
mall.product.status.disabled=已禁用
mall.product.status.enabled=已启用
mall.product.status.draft=草稿

# Product Type enum
mall.product.type.simple=简单商品
mall.product.type.variable=可变商品

# Category Status enum
mall.category.status.disabled=已禁用
mall.category.status.enabled=已启用

# Search and filter messages
mall.product.search.keyword.required=搜索关键词不能为空
mall.product.search.keyword.length=搜索关键词长度不能超过{max}个字符
mall.product.search.no.results=未找到相关商品
mall.product.filter.price.invalid=价格范围无效
mall.product.filter.category.invalid=分类筛选无效
mall.product.filter.brand.invalid=品牌筛选无效

# Business operation messages
mall.product.stock.update.success=库存更新成功
mall.product.stock.check.failed=库存检查失败
mall.product.related.not.found=未找到相关商品
mall.product.featured.not.found=未找到推荐商品

# Validation messages
mall.product.id.required=商品ID不能为空
mall.category.id.required=分类ID不能为空
mall.brand.id.required=品牌ID不能为空
mall.product.sku.id.required=SKU ID不能为空
mall.product.image.id.required=图片ID不能为空
mall.product.attribute.id.required=属性ID不能为空
mall.product.attribute.value.id.required=属性值ID不能为空

# Product validation
mall.validation.product.short.description.max.length=简短描述长度不能超过500个字符
mall.validation.product.price.required=产品价格不能为空
mall.validation.product.price.min=产品价格不能小于0
mall.validation.product.price.format=价格格式不正确
mall.validation.product.original.price.min=原价不能小于0
mall.validation.product.original.price.format=原价格式不正确
mall.validation.product.stock.quantity.required=库存数量不能为空
mall.validation.product.stock.quantity.min=库存数量不能小于0
mall.validation.product.min.stock.level.min=最低库存水平不能小于0
mall.validation.product.status.required=产品状态不能为空
mall.validation.product.type.required=产品类型不能为空
mall.validation.product.seo.title.max.length=SEO标题长度不能超过200个字符
mall.validation.product.seo.description.max.length=SEO描述长度不能超过500个字符
mall.validation.product.seo.keywords.max.length=SEO关键词长度不能超过200个字符
mall.validation.product.sort.order.min=排序顺序不能小于0
mall.validation.product.featured.image.max.length=特色图片URL长度不能超过500个字符
mall.validation.product.weight.min=产品重量不能小于0
mall.validation.product.weight.format=重量格式不正确
mall.validation.product.dimensions.max.length=产品尺寸长度不能超过100个字符
mall.validation.product.tags.max.length=产品标签长度不能超过500个字符

# Product image validation
mall.validation.product.image.id.required=图片ID不能为空
mall.validation.product.image.url.required=图片URL不能为空
mall.validation.product.image.url.max.length=图片URL长度不能超过500个字符
mall.validation.product.image.alt.max.length=图片Alt文本长度不能超过200个字符
mall.validation.product.image.title.max.length=图片标题长度不能超过200个字符
mall.validation.product.image.sort.order.min=排序顺序不能小于0
mall.validation.product.image.featured.invalid=特色图片标识值不正确

# Product SKU validation
mall.validation.product.sku.id.required=SKU ID不能为空
mall.validation.product.sku.code.required=SKU编码不能为空
mall.validation.product.sku.code.max.length=SKU编码长度不能超过50个字符
mall.validation.product.sku.name.required=SKU名称不能为空
mall.validation.product.sku.name.max.length=SKU名称长度不能超过200个字符
mall.validation.product.sku.price.required=SKU价格不能为空
mall.validation.product.sku.price.min=SKU价格不能小于0
mall.validation.product.sku.price.format=价格格式不正确
mall.validation.product.sku.original.price.min=SKU原价不能小于0
mall.validation.product.sku.original.price.format=原价格式不正确
mall.validation.product.sku.stock.quantity.required=库存数量不能为空
mall.validation.product.sku.stock.quantity.min=库存数量不能小于0
mall.validation.product.sku.min.stock.level.min=最低库存水平不能小于0
mall.validation.product.sku.status.required=SKU状态不能为空
mall.validation.product.sku.status.invalid=SKU状态值不正确
mall.validation.product.sku.image.max.length=SKU图片URL长度不能超过500个字符
mall.validation.product.sku.weight.min=SKU重量不能小于0
mall.validation.product.sku.weight.format=重量格式不正确
mall.validation.product.sku.dimensions.max.length=SKU尺寸长度不能超过100个字符

# ===== 批发后台模块消息 =====
# 产品消息
wholesale.admin.product.id.not.null=产品ID不能为空
wholesale.admin.product.name.not.blank=产品名称不能为空
wholesale.admin.product.name.length=产品名称不能超过{max}个字符
wholesale.admin.product.category.not.null=产品分类不能为空

# 会员消息
wholesale.admin.member.id.not.null=会员ID不能为空
wholesale.admin.member.email.not.blank=邮箱不能为空
wholesale.admin.member.email.format=邮箱格式不正确
wholesale.admin.member.password.not.blank=密码不能为空
wholesale.admin.member.phone.format=电话号码格式不正确
wholesale.admin.member.first.name.not.blank=名字不能为空
wholesale.admin.member.last.name.not.blank=姓氏不能为空

# 仓库消息
wholesale.admin.warehouse.id.not.null=仓库ID不能为空
wholesale.admin.warehouse.name.not.blank=仓库名称不能为空
wholesale.admin.warehouse.name.length=仓库名称不能超过{max}个字符
wholesale.admin.warehouse.code.not.blank=仓库编码不能为空
wholesale.admin.warehouse.code.length=仓库编码不能超过{max}个字符
wholesale.admin.warehouse.address.length=仓库地址不能超过{max}个字符
wholesale.admin.warehouse.contact.length=联系人不能超过{max}个字符
wholesale.admin.warehouse.phone.length=联系电话不能超过{max}个字符

# 订单消息
wholesale.admin.order.no.not.blank=订单号不能为空
wholesale.admin.order.customer.id.not.null=必须指定客户
wholesale.admin.order.customer.address.not.null=必须指定客户地址
wholesale.admin.order.items.not.empty=必须选择订单项
wholesale.admin.order.discount.amount.min=折扣金额不能小于0
wholesale.admin.order.discount.amount.exceeds.total=折扣金额不能大于订单总金额
wholesale.order.discount.exceeds.total=折扣金额不能大于订单总金额。订单总金额：${total}，折扣金额：${discount}
wholesale.order.discount.min.zero=折扣金额不能小于0
wholesale.order.discount.exceeds.subtotal=折扣金额不能超过订单小计 ${subtotal}
wholesale.order.discount.format.error=折扣金额格式错误，已重置为0

# 订单强制完成消息
wholesale.order.force.complete.reason.required=必须提供强制完成的原因
wholesale.order.force.complete.canceled.not.allowed=已取消的订单不能强制完成
wholesale.order.force.complete.not.processing=只有处理中的订单才能强制完成
wholesale.order.force.complete.not.shipped=只有已发货的订单才能强制完成
wholesale.order.force.complete.success=订单强制完成成功

# 通用验证消息
wholesale.admin.common.id.not.null=ID不能为空
wholesale.admin.common.name.not.blank=名称不能为空
wholesale.admin.common.code.not.blank=编码不能为空
wholesale.admin.common.length.exceed=长度不能超过{max}个字符

# ===== Mall Validation Messages =====
# Product validation
mall.validation.product.id.required=产品ID不能为空
mall.validation.product.name.required=产品名称不能为空
mall.validation.product.name.max.length=产品名称长度不能超过200个字符
mall.validation.product.code.required=产品编码不能为空
mall.validation.product.code.max.length=产品编码长度不能超过50个字符

# Brand validation
mall.validation.brand.id.required=品牌ID不能为空
mall.validation.brand.name.required=品牌名称不能为空
mall.validation.brand.name.max.length=品牌名称长度不能超过100个字符
mall.validation.brand.code.required=品牌编码不能为空
mall.validation.brand.code.max.length=品牌编码长度不能超过50个字符
mall.validation.brand.description.max.length=品牌描述长度不能超过500个字符
mall.validation.brand.logo.max.length=品牌Logo URL长度不能超过500个字符
mall.validation.brand.website.max.length=品牌官网长度不能超过200个字符
mall.validation.brand.website.format=品牌官网格式不正确
mall.validation.brand.status.required=品牌状态不能为空
mall.validation.brand.status.invalid=品牌状态值不正确
mall.validation.brand.sort.min=排序顺序不能小于0
mall.validation.brand.seo.title.max.length=SEO标题长度不能超过200个字符
mall.validation.brand.seo.description.max.length=SEO描述长度不能超过500个字符
mall.validation.brand.seo.keywords.max.length=SEO关键词长度不能超过200个字符

# Category validation
mall.validation.category.id.required=分类ID不能为空
mall.validation.category.name.required=分类名称不能为空
mall.validation.category.name.max.length=分类名称长度不能超过100个字符
mall.validation.category.code.required=分类编码不能为空
mall.validation.category.code.max.length=分类编码长度不能超过50个字符
mall.validation.category.description.max.length=分类描述长度不能超过500个字符
mall.validation.category.parent.id.required=父分类ID不能为空
mall.validation.category.parent.id.min=父分类ID不能小于0
mall.validation.category.status.required=分类状态不能为空
mall.validation.category.sort.min=排序顺序不能小于0
mall.validation.category.image.max.length=分类图片URL长度不能超过500个字符
mall.validation.category.icon.max.length=分类图标长度不能超过200个字符
mall.validation.category.seo.title.max.length=SEO标题长度不能超过200个字符
mall.validation.category.seo.description.max.length=SEO描述长度不能超过500个字符
mall.validation.category.seo.keywords.max.length=SEO关键词长度不能超过200个字符

# ========== 新增字段的中文国际化消息 ==========

# 会员管理新增字段
wholesale.admin.member.company.type.not.blank=公司类型不能为空
wholesale.admin.member.store.count.not.null=店面数量不能为空
wholesale.admin.member.store.count.min=店面数量必须大于0
wholesale.admin.member.customer.source.length=客户来源不能超过{max}个字符
wholesale.admin.member.company.name.length=公司名称不能超过{max}个字符

# 会员公司类型
wholesale.member.company.type.smoke_shop=烟草店
wholesale.member.company.type.chain_stores=连锁店
wholesale.member.company.type.cash_carry=现购自运
wholesale.member.company.type.distributor=分销商
wholesale.member.company.type.vip=VIP客户
wholesale.member.company.type.dispensary=药房
wholesale.member.company.type.chain_dispensaries=连锁药房
wholesale.member.company.type.others=其他

# 客户来源
wholesale.member.source.champs_show=Champs展会
wholesale.member.source.tpe=TPE展会
wholesale.member.source.mjbiz=MJBIZ展会
wholesale.member.source.website=官网
wholesale.member.source.others=其他

# 订单明细新增字段
wholesale.admin.order.item.purchase.price.not.null=采购单价不能为空
wholesale.admin.order.item.purchase.price.min=采购单价必须大于0
wholesale.admin.order.item.sales.price.not.null=销售单价不能为空
wholesale.admin.order.item.sales.price.min=销售单价必须大于0

# 运费信息管理
wholesale.admin.freight.id.not.null=运费信息ID不能为空
wholesale.admin.freight.order.id.not.null=订单ID不能为空
wholesale.admin.freight.estimated.freight.min=预估运费必须大于或等于0
wholesale.admin.freight.actual.freight.min=实际运费必须大于或等于0
wholesale.admin.freight.currency.not.blank=运费货币不能为空
wholesale.admin.freight.sync.success=运费信息同步成功
wholesale.admin.freight.sync.failed=运费信息同步失败
wholesale.admin.freight.not.found=未找到运费信息

# 运费同步状态
wholesale.freight.sync.status.pending=未同步
wholesale.freight.sync.status.synced=已同步

# 昊通运费查询
wholesale.haotong.freight.query.success=运费查询成功
wholesale.haotong.freight.query.failed=运费查询失败
wholesale.haotong.freight.order.tracking.empty=订单号和跟踪号不能同时为空
wholesale.haotong.freight.api.error=查询运费信息时发生API错误
wholesale.haotong.freight.response.invalid=运费查询API响应格式无效

# 权限相关
wholesale.permission.international.sales=国际销售团队
wholesale.permission.member.extended.fields=会员扩展字段管理
wholesale.permission.order.freight=订单运费管理
wholesale.permission.order.extended.fields=订单扩展字段管理

# 自定义字段验证消息
wholesale.member.custom.company.type.required=选择其他公司类型时，必须提供自定义内容
wholesale.member.custom.company.type.too.long=自定义公司类型长度不能超过100个字符
wholesale.member.custom.source.required=选择其他客户来源时，必须提供自定义内容
wholesale.member.custom.source.too.long=自定义客户来源长度不能超过100个字符
wholesale.member.email.already.exists=该邮箱地址已被其他客户使用

# 客户姓名验证消息
wholesale.admin.member.first.name.not.blank=名字不能为空
wholesale.admin.member.last.name.not.blank=姓氏不能为空
wholesale.admin.member.customer.name.too.long=客户姓名总长度不能超过{0}个字符，当前长度为{1}个字符（名字:{2} + 空格:1 + 姓氏:{3}）
