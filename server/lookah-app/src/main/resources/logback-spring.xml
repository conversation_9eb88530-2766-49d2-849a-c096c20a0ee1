<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="log.path" value="./logs"/>
    <property name="console.log.pattern"
              value="%cyan(%d{yyyy-MM-dd HH:mm:ss}) %green([%thread]) %highlight(%-5level) %boldMagenta(%logger{36}%n) - %msg%n"/>
    <property name="log.pattern" value="%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"/>

    <!-- 控制台输出 - 开发环境 -->
    <springProfile name="dev,local">
        <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>${console.log.pattern}</pattern>
                <charset>utf-8</charset>
            </encoder>
        </appender>
    </springProfile>

    <!-- 控制台输出 - 生产环境（显示INFO及以上级别，但过滤框架日志） -->
    <springProfile name="prod">
        <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>${console.log.pattern}</pattern>
                <charset>utf-8</charset>
            </encoder>
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>INFO</level>
            </filter>
        </appender>
    </springProfile>

    <!-- 控制台输出 -->
    <appender name="file_console" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/sys-console.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${log.path}/sys-console.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大 1天 -->
            <maxHistory>1</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <!-- 过滤的级别 -->
            <level>INFO</level>
        </filter>
    </appender>

    <!-- 系统日志输出 -->
    <appender name="file_info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/sys-info.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${log.path}/sys-info.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>60</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>INFO</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="file_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/sys-error.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${log.path}/sys-error.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>60</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>ERROR</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- info异步输出 -->
    <appender name="async_info" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="file_info"/>
    </appender>

    <!-- error异步输出 -->
    <appender name="async_error" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="file_error"/>
    </appender>

    <!-- 整合 skywalking 控制台输出 tid -->
<!--    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">-->
<!--        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">-->
<!--            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">-->
<!--                <pattern>[%tid] ${console.log.pattern}</pattern>-->
<!--            </layout>-->
<!--            <charset>utf-8</charset>-->
<!--        </encoder>-->
<!--    </appender>-->

    <!-- 整合 skywalking 推送采集日志 -->
<!--    <appender name="sky_log" class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.log.GRPCLogClientAppender">-->
<!--        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">-->
<!--            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">-->
<!--                <pattern>[%tid] ${console.log.pattern}</pattern>-->
<!--            </layout>-->
<!--            <charset>utf-8</charset>-->
<!--        </encoder>-->
<!--    </appender>-->

    <!-- 项目包日志级别 - 开发环境 -->
    <springProfile name="dev,local">
        <logger name="com.imhuso" level="DEBUG" />
    </springProfile>

    <!-- 项目包日志级别 - 生产环境 -->
    <springProfile name="prod">
        <logger name="com.imhuso" level="INFO" />
    </springProfile>

    <!-- 框架日志级别控制 - 生产环境过滤不必要的框架日志 -->
    <springProfile name="prod">
        <!-- Spring框架日志 -->
        <logger name="org.springframework" level="WARN" />
        <logger name="org.springframework.web" level="WARN" />
        <logger name="org.springframework.security" level="WARN" />
        <logger name="org.springframework.boot" level="WARN" />

        <!-- MyBatis日志 -->
        <logger name="org.mybatis" level="WARN" />
        <logger name="com.baomidou.mybatisplus" level="WARN" />

        <!-- 数据库连接池日志 -->
        <logger name="com.zaxxer.hikari" level="WARN" />
        <logger name="com.alibaba.druid" level="WARN" />

        <!-- Redis日志 -->
        <logger name="org.redisson" level="WARN" />
        <logger name="io.lettuce" level="WARN" />

        <!-- HTTP客户端日志 -->
        <logger name="org.apache.http" level="WARN" />
        <logger name="okhttp3" level="WARN" />

        <!-- 其他第三方框架 -->
        <logger name="org.apache.fury" level="WARN" />
        <logger name="io.undertow" level="WARN" />
    </springProfile>

    <!-- Spring Boot Admin 客户端日志级别调整 -->
    <logger name="de.codecentric.boot.admin.client.registration" level="ERROR" additivity="false">
        <appender-ref ref="console" />
        <appender-ref ref="async_error" />
    </logger>

    <!-- Spring Web Client 相关日志级别调整 -->
    <logger name="org.springframework.web.client" level="ERROR" additivity="false">
        <appender-ref ref="console" />
        <appender-ref ref="async_error" />
    </logger>

    <!--系统操作日志-->
    <root level="info">
        <appender-ref ref="console" />
        <appender-ref ref="async_info" />
        <appender-ref ref="async_error" />
        <appender-ref ref="file_console" />
<!--        <appender-ref ref="sky_log"/>-->
    </root>

</configuration>
