# 企业微信配置 - 本地开发环境（基础技术配置）
wecom:
  enabled: false  # 本地环境关闭企业微信
  connect-timeout: 3000  # 连接超时时间（毫秒）
  read-timeout: 3000     # 读取超时时间（毫秒）
  # 注意：webhook密钥现在通过数据库sys_config表管理，本地环境已禁用企业微信服务

# Wholesale模块业务配置 - 本地开发环境
wholesale:
  # 🧾 发票配置
  invoice:
    initial-id: 10000  # 本地环境发票号初始值
  # 📦 库存配置
  stock:
    notify:
      threshold: 5  # 本地环境库存通知阈值

--- # mail 邮件发送 - 本地开发环境
mail:
  enabled: false  # 本地环境关闭邮件发送
  host: localhost
  port: 1025
  # 是否需要用户名密码验证
  auth: false
  # 发送方，遵循RFC-822标准
  from: dev@localhost
  # 用户名
  user: dev
  # 密码
  pass: dev123
  # 使用 STARTTLS安全连接
  starttlsEnable: false
  # 使用SSL安全连接
  sslEnable: false
  # SMTP超时时长，单位毫秒，缺省值不超时
  timeout: 5000
  # Socket连接超时值，单位毫秒，缺省值不超时
  connectionTimeout: 5000

# 日志配置 - 本地开发环境
logging:
  level:
    root: info
    # 特定类的详细日志
    com.imhuso.wholesale.core.service.impl.WecomServiceImpl: debug
    com.imhuso.wholesale.core.strategy: debug
