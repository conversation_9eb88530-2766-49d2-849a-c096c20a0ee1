# 项目相关配置
lookah:
  # 名称
  name: Lookah
  # 版本
  version: ${revision}
  # 版权年份
  copyrightYear: 2025

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 9900
  servlet:
    # 应用的访问路径
    context-path: /
  # undertow 配置
  undertow:
    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的
    max-http-post-size: -1
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分
    buffer-size: 512
    # 是否分配的直接内存
    direct-buffers: true
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 8
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 256

# 🏗️ 模块化配置
#
# 📁 配置文件已提取到独立文件: config/modules.yml
# 🤖 自动加载: 通过 ModuleConfigImporter 自动导入
# 📚 详细文档: docs/模块化配置详细说明.md
#
# 注意: modules 配置现在从 config/modules.yml 文件中加载

captcha:
  enable: false
  # 页面 <参数设置> 可开启关闭 验证码校验
  # 验证码类型 math 数组计算 char 字符验证
  type: MATH
  # line 线段干扰 circle 圆圈干扰 shear 扭曲干扰
  category: CIRCLE
  # 数字验证码位数
  numberLength: 1
  # 字符验证码长度
  charLength: 4

# 日志配置
logging:
  level:
    com.imhuso: "@logging.level@"
    org.springframework: warn
    org.mybatis.spring.mapper: error
    org.apache.fury: warn
  config: classpath:logback-spring.xml

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  application:
    name: ${lookah.name}
  threads:
    # 开启虚拟线程 仅jdk21可用
    virtual:
      enabled: false
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
    # 编码
    encoding: UTF-8
    # 国际化消息缓存有效期，单位秒
    cache-duration: 3600
    # 如果没有找到特定的语言，使用默认的
    fallback-to-system-locale: false
  profiles:
    active: "@profiles.active@"
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  mvc:
    # 设置静态资源路径 防止所有请求都去查静态资源
    static-path-pattern: /static/**
    format:
      date-time: yyyy-MM-dd HH:mm:ss
  jackson:
    # 日期格式化
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      # 格式化输出
      indent_output: false
      # 忽略无法转换的对象
      fail_on_empty_beans: false
    deserialization:
      # 允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false

# Sa-Token统一配置 - 支持多模块
sa-token:
  # token名称 (Header名称)
  token-name: Authorization
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # 多模块JWT密钥统一配置（临时恢复传统配置以确保系统正常运行）
  jwt-secret-keys:
    default: yfYMuqLSNS7hqWMgLdGqSXJy5xfqtrq8
    # Mall商城模块 (loginType="mall")
    mall: mKLp9QwE7rTyFxN3vBzH8sJ6uC2nA5gY
    # Wholesale批发模块 (loginType="wholesale")
    wholesale: hthH_gNTjbqei9wMe9YuFxPKXq2L7sTY

# 注意：security.excludes 配置已整合到 modules.configs 中
# 每个模块的排除路径现在在对应的 excludePaths 中配置

# MyBatisPlus配置
# https://baomidou.com/config/
mybatis-plus:
  # 自定义配置 是否全局开启逻辑删除 关闭后 所有逻辑删除功能将失效
  enableLogicDelete: true
  # 多包名使用 例如 com.imhuso.**.mapper,org.xxx.**.mapper
  mapperPackage: com.imhuso.**.mapper
  # 对应的 XML 文件位置
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.imhuso.**.domain
  global-config:
    dbConfig:
      # 主键类型
      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID
      # 如需改为自增 需要将数据库表全部设置为自增
      idType: ASSIGN_ID

# 数据加密
mybatis-encryptor:
  # 是否开启加密
  enable: false
  # 默认加密算法
  algorithm: BASE64
  # 编码方式 BASE64/HEX。默认BASE64
  encode: BASE64
  # 安全秘钥 对称算法的秘钥 如：AES，SM4
  password:
  # 公私钥 非对称算法的公私钥 如：SM2，RSA
  publicKey:
  privateKey:

# api接口加密
api-decrypt:
  # 是否开启全局接口加密
  enabled: true
  # AES 加密头标识
  headerFlag: encrypt-key
  # 响应加密公钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换
  # 对应前端解密私钥 MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAmc3CuPiGL/LcIIm7zryCEIbl1SPzBkr75E2VMtxegyZ1lYRD+7TZGAPkvIsBcaMs6Nsy0L78n2qh+lIZMpLH8wIDAQABAkEAk82Mhz0tlv6IVCyIcw/s3f0E+WLmtPFyR9/WtV3Y5aaejUkU60JpX4m5xNR2VaqOLTZAYjW8Wy0aXr3zYIhhQQIhAMfqR9oFdYw1J9SsNc+CrhugAvKTi0+BF6VoL6psWhvbAiEAxPPNTmrkmrXwdm/pQQu3UOQmc2vCZ5tiKpW10CgJi8kCIFGkL6utxw93Ncj4exE/gPLvKcT+1Emnoox+O9kRXss5AiAMtYLJDaLEzPrAWcZeeSgSIzbL+ecokmFKSDDcRske6QIgSMkHedwND1olF8vlKsJUGK3BcdtM8w4Xq7BpSBwsloE=
  publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJnNwrj4hi/y3CCJu868ghCG5dUj8wZK++RNlTLcXoMmdZWEQ/u02RgD5LyLAXGjLOjbMtC+/J9qofpSGTKSx/MCAwEAAQ==
  # 请求解密私钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换
  # 对应前端加密公钥 MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==
  privateKey: MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKNPuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gAkM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWowcSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99EcvDQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthhYhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3UP8iWi1Qw0Y=

springdoc:
  api-docs:
    # 是否开启接口文档
    enabled: true
  #  swagger-ui:
  #    # 持久化认证数据
  #    persistAuthorization: true
  info:
    # 标题
    title: "标题：${lookah.name}多租户管理系统_接口文档"
    # 描述
    description: "管理系统接口文档"
    # 版本
    version: "版本号: ${lookah.version}"
  components:
    # 鉴权方式配置
    security-schemes:
      apiKey:
        type: APIKEY
        in: HEADER
        name: ${sa-token.token-name}
  #API文档分组配置 - 按模块组织
  group-configs:
    - group: 1.系统管理模块
      packages-to-scan: com.imhuso.system.controller,com.imhuso.admin.controller
    - group: 2.CRM客户管理模块
      packages-to-scan: com.imhuso.crm.controller.admin
    - group: 3.商城前端模块
      packages-to-scan: com.imhuso.mall.controller.front
    - group: 4.商城管理模块
      packages-to-scan: com.imhuso.mall.controller.admin
    - group: 5.批发前端模块
      packages-to-scan: com.imhuso.wholesale.controller.front
    - group: 6.批发管理模块
      packages-to-scan: com.imhuso.wholesale.controller.admin

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludeUrls:
    - /system/notice
    - /warm-flow/save-xml

# 全局线程池相关配置
# 如使用JDK21请直接使用虚拟线程 不要开启此配置
thread-pool:
  # 是否开启线程池
  enabled: false
  # 队列最大长度
  queueCapacity: 128
  # 线程池维护线程所允许的空闲时间
  keepAliveSeconds: 300

--- # 分布式锁 lock4j 全局配置
lock4j:
  # 获取分布式锁超时时间，默认为 3000 毫秒
  acquire-timeout: 3000
  # 分布式锁的超时时间，默认为 30 秒
  expire: 30000
--- # Actuator 监控端点的配置项
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: ALWAYS
    logfile:
      external-file: ./logs/sys-console.log
--- # 默认/推荐使用sse推送
sse:
  enabled: true
  path: /resource/sse
--- # websocket
websocket:
  # 如果关闭 需要和前端开关一起关闭
  enabled: false
  # 路径
  path: /resource/websocket
  # 设置访问源地址
  allowedOrigins: "*"
--- # Mall模块验证码配置
mall:
  captcha:
    turnstile:
      # 是否启用 Cloudflare Turnstile 验证码
      enabled: ${TURNSTILE_ENABLED:true}
      # 站点密钥（前端使用）- 从环境变量获取，避免硬编码
      site-key: ${TURNSTILE_SITE_KEY:0x4AAAAAAAC1DgiNptvhEpEd}
      # 密钥（后端验证使用）- 从环境变量获取，避免硬编码
      secret-key: ${TURNSTILE_SECRET_KEY:0x4AAAAAAAC1DglUq4903J-kDzf--AL56a4}
      # 是否在开发环境跳过验证（开发环境建议跳过）
      skip-in-dev: ${TURNSTILE_SKIP_IN_DEV:true}
      # 验证API地址
      verify-url: ${TURNSTILE_VERIFY_URL:https://challenges.cloudflare.com/turnstile/v0/siteverify}
      # 连接超时时间（毫秒）
      connect-timeout: ${TURNSTILE_CONNECT_TIMEOUT:5000}
      # 读取超时时间（毫秒）
      read-timeout: ${TURNSTILE_READ_TIMEOUT:10000}
--- # 三方授权
justauth:
  # 前端外网访问地址
  address: http://localhost:9900
  type:
    #maxkey:
    #  # maxkey 服务器地址
    #  # 注意 如下均配置均不需要修改 maxkey 已经内置好了数据
    #  server-url: http://sso.maxkey.top
    #  client-id: 876892492581044224
    #  client-secret: x1Y5MTMwNzIwMjMxNTM4NDc3Mzche8
    #  redirect-uri: ${justauth.address}/api/auth/social-callback?source=maxkey

    # Google OAuth 2.0
    google:
      client-id: 845485272963-6gv0gm15sjj5kaqhe8oauf8qigh05efj.apps.googleusercontent.com
      client-secret: 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
      redirect-uri: ${justauth.address}/api/auth/social-callback?source=google
      scopes:
        - openid
        - profile
        - email

    # GitHub OAuth 2.0
    github:
      client-id: 3c3d7cd8c5433a4ea8ec
      client-secret: df815c14bc31c06b3a41ed03a249a587e19a737a
      redirect-uri: ${justauth.address}/api/auth/social-callback?source=github
      scopes:
        - user:email
        - read:user
